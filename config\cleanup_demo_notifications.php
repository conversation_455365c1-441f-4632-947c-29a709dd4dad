<?php
/**
 * Script untuk membersihkan notifikasi demo
 */

// Disable session untuk testing
define('NO_SESSION', true);

require_once __DIR__ . '/config.php';

echo "🧹 MEMBERSIHKAN NOTIFIKASI DEMO\n";
echo "===============================\n\n";

// Daftar judul notifikasi demo yang akan dihapus
$demo_titles = [
    '🎓 Training Baru: Digital Marketing',
    '✅ Pengajuan Training Disetujui',
    '⚠️ Deadline Assignment Mendekati',
    '❌ Training Dibatalkan',
    '📅 Reminder: Training Besok',
    'Training Baru Tersedia',
    'Pengajuan Training Disetujui',
    'Deadline Assignment Mendekati',
    'Training Dibatalkan',
    'Final Test Notification',
    'Test General Notification',
    'Test Class Notification'
];

echo "🔍 Mencari notifikasi demo...\n";

// Cek berapa notifikasi demo yang ada
$check_query = "SELECT COUNT(*) as count FROM training_notifications WHERE title IN (" . 
               str_repeat('?,', count($demo_titles) - 1) . "?)";
$check_stmt = $conn->prepare($check_query);
$check_stmt->bind_param(str_repeat('s', count($demo_titles)), ...$demo_titles);
$check_stmt->execute();
$check_result = $check_stmt->get_result();
$demo_count = $check_result->fetch_assoc()['count'];

echo "   📊 Ditemukan: $demo_count notifikasi demo\n";

if ($demo_count > 0) {
    echo "\n🗑️ Menghapus notifikasi demo...\n";
    
    // Hapus notifikasi demo
    $delete_query = "DELETE FROM training_notifications WHERE title IN (" . 
                   str_repeat('?,', count($demo_titles) - 1) . "?)";
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bind_param(str_repeat('s', count($demo_titles)), ...$demo_titles);
    
    if ($delete_stmt->execute()) {
        $deleted_count = $delete_stmt->affected_rows;
        echo "   ✅ Berhasil menghapus: $deleted_count notifikasi demo\n";
    } else {
        echo "   ❌ Gagal menghapus notifikasi demo: " . $conn->error . "\n";
    }
} else {
    echo "   ℹ️  Tidak ada notifikasi demo yang perlu dihapus\n";
}

// Tampilkan statistik setelah cleanup
echo "\n📈 Statistik setelah cleanup:\n";
$stats_query = "SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread,
                    COUNT(DISTINCT user_id) as users_with_notifications
                FROM training_notifications";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

echo "   📊 Total notifikasi: {$stats['total']}\n";
echo "   📬 Belum dibaca: {$stats['unread']}\n";
echo "   👥 User dengan notifikasi: {$stats['users_with_notifications']}\n";

echo "\n✅ CLEANUP SELESAI!\n";

$conn->close();
?>
