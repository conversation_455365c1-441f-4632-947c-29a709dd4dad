<?php
session_start();
include '../config/config.php';

if (isset($_GET['token'])) {
    $token = $_GET['token'];

    // Verify token and check if it's not expired
    $query = "SELECT id, email FROM users
              WHERE reset_token = ?
              AND reset_token_expires > NOW()";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $error_message = "Token tidak valid atau sudah kadaluarsa.";
    }
} else {
    header('Location: login.php');
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    if ($password !== $confirm_password) {
        $error_message = "Password tidak cocok.";
    } else {
        // Update password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        $updateQuery = "UPDATE users
                       SET password = ?,
                           reset_token = NULL,
                           reset_token_expires = NULL
                       WHERE reset_token = ?";
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param("ss", $hashed_password, $token);

        if ($updateStmt->execute()) {
            $success_message = "Password berhasil diubah. Silakan login dengan password baru Anda.";
        } else {
            $error_message = "Gagal mengubah password.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<body>
    <?php include '../config/navbarb.php'; ?>

    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <img src="../asset/picture/logo-pas-with-text-removebg-preview.png" alt="Logo" class="login-logo">
                <h2>Reset Password</h2>
            </div>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <?php if (isset($success_message)): ?>
                <div class="alert alert-success">
                    <?php echo $success_message; ?>
                    <br>
                    <a href="login.php" class="btn btn-primary">Kembali ke halaman login</a>
                </div>
            <?php else: ?>
                <form method="POST" class="reset-password-form">
                    <div class="form-group">
                        <label for="password">
                            <i class="fas fa-lock"></i> Password Baru
                        </label>
                        <div class="password-input">
                            <input type="password"
                                   name="password"
                                   id="password"
                                   required
                                   placeholder="Masukkan password baru"
                                   class="form-control">
                            <span class="toggle-password" onclick="togglePassword('password')">
                                <i class="far fa-eye"></i>
                            </span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">
                            <i class="fas fa-lock"></i> Konfirmasi Password
                        </label>
                        <div class="password-input">
                            <input type="password"
                                   name="confirm_password"
                                   id="confirm_password"
                                   required
                                   placeholder="Konfirmasi password baru"
                                   class="form-control">
                            <span class="toggle-password" onclick="togglePassword('confirm_password')">
                                <i class="far fa-eye"></i>
                            </span>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-block" style="width: 100%; max-width: 300px; margin: 0 auto; display: block;">
                        <i class="fas fa-key"></i> Reset Password
                    </button>
                </form>
            <?php endif; ?>
        </div>
    </div>
    <?php include '../config/footer.php'; ?>

    <script>
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const toggle = field.nextElementSibling.querySelector('i');

        if (field.type === "password") {
            field.type = "text";
            toggle.classList.remove('fa-eye');
            toggle.classList.add('fa-eye-slash');
        } else {
            field.type = "password";
            toggle.classList.remove('fa-eye-slash');
            toggle.classList.add('fa-eye');
        }
    }
    </script>

    <style>
    /* Base styles */
    body {
        font-family: 'Roboto', Arial, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 0;
    }

    /* Login container */
    .login-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 120px); /* Adjust for navbar and footer */
        padding: 20px;
        background: linear-gradient(135deg, #c40000 0%, #8b0000 100%);
        position: relative;
        z-index: 1; /* Ensure it's above the navbar */
    }

    .login-box {
        width: 100%;
        max-width: 400px;
        background: rgba(255, 255, 255, 0.98);
        border-radius: 15px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        padding: 30px;
        transition: all 0.3s ease;
    }

    /* Header styles */
    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .login-logo {
        width: 120px;
        height: auto;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }

    .login-header h2 {
        color: #c40000;
        font-size: 28px;
        margin: 0;
        font-weight: 600;
    }

    /* Form styles */
    .form-group {
        margin-bottom: 22px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #444;
        font-weight: 500;
        font-size: 15px;
    }

    .form-control {
        width: 100%;
        padding: 14px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }

    .form-control:focus {
        border-color: #c40000;
        outline: none;
        box-shadow: 0 0 0 3px rgba(196, 0, 0, 0.1);
    }

    .password-input {
        position: relative;
    }

    .toggle-password {
        position: absolute;
        right: 14px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #666;
        font-size: 16px;
        padding: 5px;
        z-index: 10;
    }

    .toggle-password:hover {
        color: #c40000;
    }

    /* Button styles */
    .btn {
        width: 100%;
        padding: 14px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }

    .btn-primary {
        background: #c40000;
        color: white;
    }

    .btn-primary:hover {
        background: #a00000;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn-primary:active {
        transform: translateY(0);
    }

    .btn-block {
        display: block;
        width: 100%;
    }

    /* Alert styles */
    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 25px;
        text-align: center;
        font-size: 15px;
        font-weight: 500;
    }

    .alert-danger {
        background: #fff2f2;
        color: #c40000;
        border: 1px solid #ffcfcf;
    }

    .alert-success {
        background: #f0fff4;
        color: #0d6832;
        border: 1px solid #c3e6cb;
    }

    /* Responsive styles */
    @media (max-width: 768px) {
        .login-container {
            padding: 15px;
            padding-top: 70px; /* Add top padding to prevent navbar overlap */
        }

        .login-box {
            padding: 25px 20px;
        }

        .login-logo {
            width: 100px;
        }

        .login-header h2 {
            font-size: 24px;
        }

        .form-control {
            padding: 12px;
            font-size: 14px;
        }

        .btn {
            padding: 12px;
            font-size: 15px;
        }
    }

    @media (max-width: 480px) {
        .login-box {
            padding: 20px 15px;
        }

        .login-logo {
            width: 90px;
        }

        .login-header h2 {
            font-size: 22px;
        }

        .form-group label {
            font-size: 14px;
        }

        .form-control {
            padding: 10px;
            font-size: 14px;
        }

        .btn {
            padding: 12px;
            font-size: 14px;
        }

        .alert {
            padding: 12px;
            font-size: 14px;
        }
    }
    </style>
</body>
</html>
