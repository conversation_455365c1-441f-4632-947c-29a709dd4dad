<?php
/**
 * Assignment Submissions Page for Admin
 * This page allows admins to view and grade assignment submissions
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if assignment ID is provided
$assignment_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($assignment_id <= 0) {
    $_SESSION['error_message'] = "ID tugas tidak valid.";
    header('Location: manage_classes.php');
    exit();
}

// Get assignment information
$assignment_query = "SELECT a.*, c.id as class_id, c.title as class_title, t.training_topic
                    FROM training_assignments a
                    JOIN training_classes c ON a.class_id = c.id
                    JOIN training_submissions t ON c.training_id = t.id
                    WHERE a.id = ?";
$stmt = $conn->prepare($assignment_query);
$stmt->bind_param("i", $assignment_id);
$stmt->execute();
$result = $stmt->get_result();
$assignment = $result->fetch_assoc();
$stmt->close();

// Check if the original_filename column exists in training_assignment_submissions
$check_column_query = "SHOW COLUMNS FROM training_assignment_submissions LIKE 'original_filename'";
$column_result = $conn->query($check_column_query);
$original_filename_exists = ($column_result && $column_result->num_rows > 0);

if (!$original_filename_exists) {
    // Add the column if it doesn't exist
    $add_column_query = "ALTER TABLE training_assignment_submissions
                       ADD COLUMN original_filename varchar(255) DEFAULT NULL AFTER file_path";
    $conn->query($add_column_query);
}

if (!$assignment) {
    $_SESSION['error_message'] = "Tugas tidak ditemukan.";
    header('Location: manage_classes.php');
    exit();
}

// Extract class ID from assignment
$class_id = $assignment['class_id'];

// First, let's check the structure of the users table to find the name column
$user_table_query = "DESCRIBE users";
$user_table_result = $conn->query($user_table_query);
$name_column = 'name'; // Default to name

if ($user_table_result) {
    while ($column = $user_table_result->fetch_assoc()) {
        // Look for common name column patterns
        if (in_array(strtolower($column['Field']), ['full_name', 'name', 'user_name', 'username', 'nama'])) {
            $name_column = $column['Field'];
            // Prefer full_name or name if available
            if (in_array(strtolower($column['Field']), ['full_name', 'name', 'nama'])) {
                break;
            }
        }
    }
}

// Get submissions with grader information
$submissions_query = "SELECT s.*, u.id as user_id, u.$name_column as user_name, u.email as user_email,
                             g.name as grader_name
                     FROM training_assignment_submissions s
                     JOIN users u ON s.user_id = u.id
                     LEFT JOIN users g ON s.graded_by = g.id
                     WHERE s.assignment_id = ?
                     ORDER BY s.submitted_at DESC";
$stmt = $conn->prepare($submissions_query);
$stmt->bind_param("i", $assignment_id);
$stmt->execute();
$result = $stmt->get_result();
$submissions = [];

while ($row = $result->fetch_assoc()) {
    $submissions[] = $row;
}
$stmt->close();

// Get class participants who haven't submitted
$participants_query = "SELECT p.user_id, u.$name_column as user_name, u.email as user_email
                      FROM training_participants p
                      JOIN users u ON p.user_id = u.id
                      WHERE p.class_id = ? AND p.role = 'student'
                      AND p.user_id NOT IN (
                          SELECT user_id FROM training_assignment_submissions WHERE assignment_id = ?
                      )";
$stmt = $conn->prepare($participants_query);
$stmt->bind_param("ii", $class_id, $assignment_id);
$stmt->execute();
$result = $stmt->get_result();
$not_submitted = [];

while ($row = $result->fetch_assoc()) {
    $not_submitted[] = $row;
}
$stmt->close();

// Handle grade submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['grade_submission'])) {
    $submission_id = intval($_POST['submission_id']);
    $grade = intval($_POST['grade']);
    $feedback = trim($_POST['feedback']);
    $status = 'graded';

    // Validate input
    if ($grade < 0 || $grade > ($assignment['points'] ?? 100)) {
        $error_message = "Nilai tidak valid. Nilai harus antara 0 dan " . ($assignment['points'] ?? 100) . ".";
    } else {
        // Update submission
        $update_query = "UPDATE training_assignment_submissions
                       SET grade = ?, feedback = ?, status = ?, graded_at = NOW(), graded_by = ?
                       WHERE id = ?";

        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("issii", $grade, $feedback, $status, $user_id, $submission_id);

        if ($stmt->execute()) {
            $success_message = "Pengumpulan berhasil dinilai.";

            // Update submission in the array
            foreach ($submissions as &$submission) {
                if ($submission['id'] == $submission_id) {
                    $submission['grade'] = $grade;
                    $submission['feedback'] = $feedback;
                    $submission['status'] = $status;
                    $submission['graded_at'] = date('Y-m-d H:i:s');
                    $submission['graded_by'] = $user_id;
                    break;
                }
            }
        } else {
            $error_message = "Gagal menilai pengumpulan: " . $conn->error;
        }

        $stmt->close();
    }
}

// Check for session messages
if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

if (isset($_SESSION['error_message'])) {
    $error_message = $_SESSION['error_message'];
    unset($_SESSION['error_message']);
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .assignment-header {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .assignment-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .assignment-meta {
        color: #6c757d;
        margin-bottom: 10px;
    }

    .submission-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .submission-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .submission-user {
        font-weight: 600;
    }

    .submission-date {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .submission-content {
        margin-bottom: 15px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 4px;
    }

    .submission-file {
        margin-bottom: 15px;
    }

    .submission-status {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-right: 10px;
    }

    .submission-status.submitted {
        background-color: #e3f2fd;
        color: #2196F3;
    }

    .submission-status.graded {
        background-color: #e8f5e9;
        color: #4CAF50;
    }

    .submission-status.returned {
        background-color: #fff3e0;
        color: #ff9800;
    }

    .grade-form {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
    }

    .feedback-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
    }

    .feedback-header {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .feedback-content {
        white-space: pre-line;
    }

    .nav-pills .nav-link.active {
        background-color: #BF0000;
    }

    .nav-pills .nav-link :hover {
        color: white !important;
        background-color: #BF0000;
    }

    .nav-pills .nav-link {
        background-color: #fff;
        color: #333;
        border: 1px solid #BF0000 !important;
    }

    .nav-pills .nav-link.active {
        color: white !important;
    }

    .grader-info {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 6px;
        border-left: 4px solid #BF0000;
    }

    .grader-info .text-muted {
        color: #6c757d !important;
    }

    .grader-info i {
        color: #BF0000;
        margin-right: 5px;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="welcome-section">
                <h1><i class="fas fa-clipboard-check"></i> Pengumpulan Tugas</h1>
                <a href="manage_class.php?id=<?= $class_id ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Kelas
                </a>
            </div>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="assignment-header">
                <div class="assignment-title"><?= htmlspecialchars($assignment['title']) ?></div>
                <div class="assignment-meta">
                    <div><strong>Kelas:</strong> <?= htmlspecialchars($assignment['class_title']) ?></div>
                    <div><strong>Tenggat:</strong> <?= date('d M Y H:i', strtotime($assignment['due_date'])) ?></div>
                    <?php if ($assignment['points']): ?>
                        <div><strong>Poin:</strong> <?= $assignment['points'] ?></div>
                    <?php endif; ?>
                </div>
                <div class="assignment-description">
                    <?= !empty($assignment['description']) ? nl2br(htmlspecialchars($assignment['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                </div>
            </div>

            <ul class="nav nav-pills mb-4" id="submissionTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="submitted-tab" data-bs-toggle="tab" data-bs-target="#submitted" type="button" role="tab" aria-controls="submitted" aria-selected="true">
                        Dikumpulkan (<?= count($submissions) ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="not-submitted-tab" data-bs-toggle="tab" data-bs-target="#not-submitted" type="button" role="tab" aria-controls="not-submitted" aria-selected="false">
                        Belum Dikumpulkan (<?= count($not_submitted) ?>)
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="submissionTabsContent">
                <div class="tab-pane fade show active" id="submitted" role="tabpanel" aria-labelledby="submitted-tab">
                    <?php if (empty($submissions)): ?>
                        <div class="alert alert-info">
                            Belum ada pengumpulan untuk tugas ini.
                        </div>
                    <?php else: ?>
                        <?php foreach ($submissions as $submission): ?>
                            <div class="submission-card">
                                <div class="submission-header">
                                    <div>
                                        <div class="submission-user"><?= htmlspecialchars($submission['user_name']) ?></div>
                                        <div class="submission-email"><?= htmlspecialchars($submission['user_email']) ?></div>
                                    </div>
                                    <div>
                                        <div class="submission-date">
                                            <i class="fas fa-clock"></i> <?= date('d M Y H:i', strtotime($submission['submitted_at'])) ?>
                                        </div>
                                        <div class="text-end mt-1">
                                            <span class="submission-status <?= $submission['status'] ?>">
                                                <?php
                                                    $status_labels = [
                                                        'submitted' => 'Dikumpulkan',
                                                        'graded' => 'Dinilai',
                                                        'returned' => 'Dikembalikan'
                                                    ];
                                                    echo $status_labels[$submission['status']] ?? $submission['status'];
                                                ?>
                                            </span>
                                            <?php if ($submission['status'] == 'graded' && $submission['grade'] !== null): ?>
                                                <span class="badge bg-success">
                                                    <?= $submission['grade'] ?>/<?= $assignment['points'] ?? 100 ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <?php if (!empty($submission['content'])): ?>
                                    <div class="submission-content">
                                        <?= nl2br(htmlspecialchars($submission['content'])) ?>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($submission['file_path']) && !empty($submission['original_filename'])): ?>
                                    <div class="submission-file">
                                        <strong>File:</strong>
                                        <a href="../download.php?type=assignment&id=<?= $submission['id'] ?>" target="_blank">
                                            <?= htmlspecialchars($submission['original_filename']) ?>
                                        </a>
                                    </div>
                                <?php elseif (!empty($submission['file_path'])): ?>
                                    <div class="submission-file">
                                        <strong>File:</strong>
                                        <a href="../download.php?type=assignment&id=<?= $submission['id'] ?>" target="_blank">
                                            File Lampiran
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <?php if ($submission['status'] == 'graded'): ?>
                                    <div class="feedback-section">
                                        <!-- Informasi Penilai -->
                                        <div class="grader-info mb-3">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <small class="text-muted">
                                                        <i class="fas fa-user-check"></i>
                                                        <strong>Dinilai oleh:</strong>
                                                        <?= !empty($submission['grader_name']) ? htmlspecialchars($submission['grader_name']) : 'Sistem' ?>
                                                    </small>
                                                </div>
                                                <?php if (!empty($submission['graded_at'])): ?>
                                                <div class="col-md-6">
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock"></i>
                                                        <strong>Tanggal penilaian:</strong>
                                                        <?= date('d M Y H:i', strtotime($submission['graded_at'])) ?>
                                                    </small>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <?php if (!empty($submission['feedback'])): ?>
                                            <div class="feedback-header">Umpan Balik:</div>
                                            <div class="feedback-content">
                                                <?= nl2br(htmlspecialchars($submission['feedback'])) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <?php if ($submission['status'] != 'graded'): ?>
                                    <div class="grade-form">
                                        <h5>Berikan Nilai</h5>
                                        <form method="post" action="">
                                            <input type="hidden" name="submission_id" value="<?= $submission['id'] ?>">

                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="mb-3">
                                                        <label for="grade-<?= $submission['id'] ?>" class="form-label">Nilai</label>
                                                        <input type="number" class="form-control" id="grade-<?= $submission['id'] ?>" name="grade" min="0" max="<?= $assignment['points'] ?? 100 ?>" required>
                                                        <div class="form-text">Maksimal: <?= $assignment['points'] ?? 100 ?></div>
                                                    </div>
                                                </div>
                                                <div class="col-md-9">
                                                    <div class="mb-3">
                                                        <label for="feedback-<?= $submission['id'] ?>" class="form-label">Umpan Balik</label>
                                                        <textarea class="form-control" id="feedback-<?= $submission['id'] ?>" name="feedback" rows="3"></textarea>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="text-end">
                                                <button type="submit" name="grade_submission" class="btn btn-primary">
                                                    <i class="fas fa-check"></i> Simpan Nilai
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="tab-pane fade" id="not-submitted" role="tabpanel" aria-labelledby="not-submitted-tab">
                    <?php if (empty($not_submitted)): ?>
                        <div class="alert alert-success">
                            Semua peserta telah mengumpulkan tugas ini.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nama</th>
                                        <th>Email</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($not_submitted as $participant): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($participant['user_name']) ?></td>
                                            <td><?= htmlspecialchars($participant['user_email']) ?></td>
                                            <td><span class="badge bg-danger">Belum Mengumpulkan</span></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                   bsAlert.close();
            });
        }, 100000);
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
