/* Reset and Base */
body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0!important;
    margin: 0;
    padding: 0;
}

/* Container Styles */
.container-form {
    margin-top: 100px;
    padding: 20px;
}

.form-container {
    max-width: 1100px;
    margin: 0 auto;
    background-color: #FFFFFF;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 25px rgba(0,0,0,0.2);
}

/* Login and Activation Styles */
.login-container {
    max-width: 400px;
    margin: 20px auto;
    background-color: #FFFFFF;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo {
    height: 80px;
    margin-bottom: 20px;
}

.login-header h2 {
    color: #BF0000;
    margin: 0;
    font-size: 24px;
}

.login-footer {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.login-footer p {
    color: #666;
    margin: 0;
}

.login-footer a {
    color: #BF0000;
    text-decoration: none;
}

.login-footer a:hover {
    text-decoration: underline;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: bold;
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"],
select,
textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-sizing: border-box;
    font-size: 14px;
    transition: border-color 0.3s, box-shadow 0.3s;
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: #BF0000;
    box-shadow: 0 0 5px rgba(191, 0, 0, 0.2);
}

/* Buttons */
button,
.btn, .badge{
    margin: 0 !important;
    margin-top: 10px !important;
    margin-bottom: 10px !important;
    background-color: #A50000!important;
    color: white!important;
    padding: 12px 30px;
    border: none;
    border-radius: 5px !important;
    border-color: #A50000!important;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    display: inline-block;
    text-decoration: none;
    transition: background-color 0.3s, transform 0.3s;
    min-width: 120px !important; /* Minimum width untuk button */;
}

.btn-detail,
.btn-approve,
.btn-delete {
    padding: 12px 24px;
    margin: 5px;
    font-size: 16px;
    min-width: 100px;
    text-align: center;
}

/* Responsive Design untuk Button */
@media screen and (max-width: 768px) {
    button,
    .btn-detail,
    .btn-approve,
    .btn-delete,
    .action-button {
        padding: 14px 28px; /* Padding lebih besar untuk area tap yang lebih besar */
        font-size: 16px; /* Font size yang readable */
        margin: 8px 4px; /* Margin yang lebih besar antara button */
        min-width: 100px; /* Minimum width lebih besar untuk mobile */
        min-height: 48px; /* Minimum height untuk touchscreen */
        display: inline-flex; /* Untuk alignment yang lebih baik */
        align-items: center;
        justify-content: center;
        touch-action: manipulation; /* Optimasi untuk touch devices */
    }

    /* Responsive container */
    .container-form {
        margin-top: 60px;
        padding: 10px;
    }

    .form-container {
        padding: 15px;
        margin: 0 auto;
    }

    /* Responsive form elements */
    input, select, textarea {
        padding: 10px;
        font-size: 16px; /* Optimal size for mobile inputs */
        margin-bottom: 12px;
    }

    /* Responsive tables */
    table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    /* Responsive login */
    .login-container {
        width: 90%;
        max-width: 350px;
        padding: 20px;
    }


    /* Quick actions buttons */
    .quick-actions .btn {
        width: 100%;
        max-width: 300px; /* Maximum width untuk readability */
        margin: 8px auto;
    }

    /* Form submit buttons */
    button[type="submit"],
    .action-button {
        width: 100%;
        max-width: 320px;
        margin: 10px auto;
    }
}

/* Extra small devices */
@media screen and (max-width: 480px) {
    button,
    .btn-detail,
    .btn-approve,
    .btn-delete,
    .action-button {
        padding: 12px 24px; /* Adjusted for better fit */
        font-size: 16px; /* Adjusted for better readability */
        min-width: 120px; /* Adjusted for better fit */
    }

    /* Stack buttons vertically on very small screens */
    .form-actions,
    .quick-actions {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .form-actions button,
    .quick-actions .btn {
        width: 100%;
        max-width: 100%;
    }

    /* Improved table responsiveness */
    table {
        font-size: 14px;
    }

    th, td {
        padding: 8px 5px;
    }

    /* Improved form container */
    .form-container {
        padding: 10px;
        border-radius: 8px;
    }

    /* Improved login container */
    .login-container {
        width: 95%;
        padding: 15px;
    }

    .login-header h2 {
        font-size: 20px;
    }

    /* Improved navigation */
    .navbar-brand {
        font-size: 18px;
    }
}
/* CSS untuk responsive logo */
.logo {
    max-width: 100px;
    height: auto;
    margin: 0 auto;
    display: block;
}

/* Atur navbar supaya tidak numpuk di tengah */
.navbar-toggler {
    display: block;
    margin: 0 auto;
}

/* Active state untuk touch feedback */
@media (hover: none) {
    button:active,
    .btn-detail:active,
    .btn-approve:active,
    .btn-delete:active {
        transform: scale(0.98);
        opacity: 0.9;
    }
}

button:hover,
.btn:hover, .badge {
    color: white;
    background-color: #A50000;
    transform: translateY(-2px);
}

/* Alert Messages */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

/* Header and Navigation */
.header {
    background-color: #FFFFFF;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 100;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: #BF0000;
    text-decoration: none;
}
.logo:hover{
    color: #900000;
}
.logo-image {
    height: 40px;
    width: 170px;
    object-fit: cover;
}

.nav-menu {
    display: flex;
    gap: 30px;
    align-items: center;
}

.nav-menu a {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 4px;
    position: relative;
}

.nav-menu a.active {
    color: #BF0000;
    background-color: rgba(191, 0, 0, 0.1);
    font-weight: bold;
}

.nav-menu a:hover {
    color: #BF0000;
    background-color: rgba(191, 0, 0, 0.05);
}

.nav-menu a.logout {
    font-weight: bold;
    color: #BF0000;
    background-color: white;
    border-radius: 5px;
    padding: 5px 5px;
    text-decoration: none;
    width: 100px;
    justify-content: center;
    display: flex;
    align-items: center;
}

/* Mobile Styles */
@media screen and (max-width: 768px) {
    .nav-menu a {
        color: white;
        padding: 12px 16px;
        width: 100%;
        text-align: left;
    }

    .nav-menu a.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .nav-menu a:hover {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .nav-menu .logout {
        margin-top: 10px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 15px;
    }
}

/* Footer */
.footer {
    background-color: #BF0000;
    color: white;
    padding: 40px 0 20px;
    margin-top: 50px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
}

.footer-section {
    flex: 1;
    margin-right: 30px;
}

.footer-section h3 {
    border-bottom: 2px solid white;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: white;
    text-decoration: none;
}

.footer-bottom {
    text-align: center;
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid rgba(255,255,255,0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-form {
        margin-top: 80px;
        padding: 10px;
    }

    .login-container {
        margin: 10px;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .nav-menu {
        margin-top: 15px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .footer-content {
        flex-direction: column;
    }

    .footer-section {
        margin-right: 0;
        margin-bottom: 30px;
    }
}


.page-title {
    color: #333;
    font-size: 24px;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #bf0000;
}

/* Table Styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}
table th, table td {
    overflow: hidden;
    white-space: nowrap;
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

table th {
    background-color: #a50000 !important;
    font-weight: bold;
    color: white !important;
    border-bottom: 1px solid #ddd;
}
table tr{
    transition: background-color 0.3s;
    -webkit-transition: background-color 0.3s;
    -moz-transition: background-color 0.3s;
    -ms-transition: background-color 0.3s;
    -o-transition: background-color 0.3s;
}
tr:hover {
    color: black !important;
    background-color: #e8e6e6 !important;
}



/* Required Field Indicator */
.required::after {
    content: "*";
    color: #BF0000;
    margin-left: 3px;
}

/* Small Tag Styling */
small {
    font-size: 80%;
    font-weight: 400;
    color: #666;
    display: inline-block;
    margin-top: 5px;
    line-height: 1.4;
    opacity: 0.9;
}

/* Small tag inside form groups */
.form-group small {
    display: block;
    margin-top: 5px;
    color: #757575;
    font-style: italic;
}

/* Small tag for validation messages */
small.validation-message {
    color: #BF0000;
    font-weight: 500;
    margin-top: 5px;
}

/* Small tag for success messages */
small.success-message {
    color: #4CAF50;
    font-weight: 500;
}

/* Small tag inside alerts */
.alert small {
    display: block;
    margin-top: 5px;
    opacity: 0.8;
}

/* Small tag inside tables */
table small {
    display: block;
    font-size: 75%;
    color: #757575;
    margin-top: 3px;
}

/* Responsive small tag */
@media screen and (max-width: 768px) {
    small {
        font-size: 75%;
    }
}

/* Error and Success Messages */
.error-message, .success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.error-message {
    background-color: #ffecec;
    color: #BF0000;
    border: 1px solid #BF0000;
}

.success-message {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #2e7d32;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
        -webkit-transform: translateX(100%);
        -moz-transform: translateX(100%);
        -ms-transform: translateX(100%);
        -o-transform: translateX(100%);
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
/* File CSS untuk tabel */
table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
}

th, td {
    padding: 8px;
    text-align: left;
    word-wrap: break-word;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

td {
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}


        .quick-actions .btn {
            background: #BF0000;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 1.1rem;
            transition: background 0.3s;
        }

        .quick-actions .btn:hover {
            color: white;
            background: #A50000;
        }

        .container-form {
            margin-top: 100px;
        }

        .btn-detail {
            background-color: #2196F3;
            color: white;
        }

        .btn-detail:hover {
            color: white;
            background-color: #03a9f4;
        }

        .btn-approve {
            background-color: #4CAF50;
            color: white;
        }

        .btn-approve:hover {
            color: white;
            background-color: #45a049;
        }

        .btn-delete {
            background-color: #cc0000;
            color: white;
        }

        .btn-delete:hover {
            color: white;
            background-color: #a50000;
        }

        .error-message, .success-message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 16px;
            z-index: 1000;
        }
        .full-width {
    width: 97%;
    height: 30px;
    margin-bottom: 20px;

}
input[type="date"] {
 /* Agar selebar input lain */
    padding: 10px; /* Menyamakan padding dengan input lain */
    font-size: 12px; /* Menyamakan ukuran font */
    font-weight: bold;
    border: 1px solid #ccc; /* Menyamakan border */
    border-radius: 5px; /* Membuat sudut melengkung */
    background-color: white;
    color: black;
}
        .mobile-menu-toggle {
            display: none;
        }

        @media screen and (max-width: 768px) {
            .mobile-menu-toggle {
                display: block;
                position: absolute;
                right: 15px;
                top: 50%;
                transform: translateY(-50%);
                background: transparent;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                z-index: 1001;
                padding: 10px;
            }

            .nav-menu {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: rgb(157, 0, 0);
                flex-direction: column;
                padding: 20px;
                z-index: 1000;
            }

            .nav-menu.active {
                display: flex;
            }

            .nav-menu a {
                padding: 10px;
                color: white;
                text-decoration: none;
                width: 100%;
            }
        }
    .username {

        padding: 5px;
    }



/* Dashboard Responsive Styles */
@media screen and (max-width: 768px) {
    .container-form {
        padding: 10px;
        margin-top: 70px;
    }

    .welcome-section {
        margin: 10px;
        padding: 15px;
    }

    .stats-section {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        margin: 10px;
    }

    .stat-card {
        padding: 15px;
    }

    .submissions-table {
        margin: 10px;
        overflow-x: auto;
    }

    .submissions-table table {
        min-width: content-width;
    }

    .action-button {
        width: auto;
        padding: 8px 16px;
    }
}

/* Small Device Optimization */
@media screen and (max-width: 480px) {
    .stats-section {
        grid-template-columns: 1fr;
    }

    .welcome-section h1 {
        font-size: 20px;
    }

    .stat-card h3 {
        font-size: 14px;
    }

    .stat-card p {
        font-size: 20px;
    }

    .submissions-table th,
    .submissions-table td {
        padding: 8px;
        font-size: 13px;
    }
}

/* General Improvements */
.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    border-left: 4px solid var(--primary-color);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color-light) 0%, rgba(255,255,255,0) 60%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card h3 {
    margin: 0;
    color: var(--text-dark);
    font-size: var(--font-size-md);
    font-weight: 600;
    position: relative;
}

.stat-card p {
    margin: var(--spacing-sm) 0 0;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    font-weight: bold;
    position: relative;
}

.stat-card:active {
    transform: scale(0.98);
}

.action-button {
    transition: background 0.2s ease;
}

.action-button:active {
    transform: scale(0.98);
}

/* Loading State */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid rgb(157, 0, 0);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
}

.pagination-info {
    font-size: 14px;
    color: #666;
}

.pagination-controls {
    display: flex;
    gap: 5px;
    align-items: center;
    justify-content: center;
}

.pagination-button {
    min-width: unset !important; /* Override default button min-width */
    padding: 6px 12px !important; /* Kecilkan padding */
    font-size: 14px !important; /* Kecilkan font size */
    margin: 0 2px !important; /* Kecilkan margin */
    background-color: #fff !important;
    color: #333 !important;
    border: 1px solid #ddd !important;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.pagination-button:hover {
    color: #333 !important;
    background-color: #f5f5f5 !important;
}

.pagination-button.active {
    background-color: #BF0000 !important;
    color: white !important;
    border-color: #BF0000 !important;
}

.pagination-button:disabled {
    background-color: #f5f5f5 !important;
    color: #999 !important;
    cursor: not-allowed;
}

.pagination-ellipsis {
    padding: 0 5px;
    color: #666;
}

/* Responsive pagination */
@media screen and (max-width: 768px) {
    .pagination-button {
        padding: 4px 8px !important;
        font-size: 12px !important;
    }

    .pagination-info {
        font-size: 12px;
    }
}

/* Table Cell Scrollable Style */
.scrollable-cell {
    max-width: 150px;
    overflow-x: auto;
    white-space: nowrap;
    padding: 2px 4px;
    /* Sembunyikan scrollbar tapi tetap bisa scroll */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.scrollable-cell::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}

/* Khusus untuk kolom actions, tidak perlu scroll */
td:last-child .scrollable-cell {
    overflow: visible;
    white-space: normal;
}

/* Khusus untuk kolom status, tidak perlu scroll */
td:nth-last-child(2) .scrollable-cell {
    overflow: visible;
    white-space: normal;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: black;
}

.column-selections {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.column-selections label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.column-selections input[type="checkbox"] {
    margin: 0;
}

.modal-footer {
    text-align: right;
    margin-top: 20px;
}

.btn-copy {
    background-color: #45a049;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-copy:hover {
    background-color: #3d8b3d;
}

.welcome-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    transform: rotate(30deg);
    pointer-events: none;
}

.welcome-section h1 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.welcome-section p {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-md);
    opacity: 0.9;
}


.searchable-select {
    position: relative;
    width: 100%;
    margin-bottom: 20px;
}

.searchable-select input {
    width: 100%;
    padding: 12px;
    box-sizing: border-box;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 15px;
    transition: all 0.3s ease;
}

.searchable-select input:focus {
    border-color: #c40000;
    outline: none;
    box-shadow: 0 0 0 3px rgba(196, 0, 0, 0.1);
}

.searchable-select ul {
    position: absolute;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 0 0 8px 8px;
    margin: 0;
    padding: 0;
    background: white;
    list-style-type: none;
    display: none;
    z-index: 1000;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.searchable-select ul li {
    padding: 12px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.searchable-select ul li:hover {
    background-color: #f5f5f5;
}

.searchable-select ul li:active {
    background-color: #e0e0e0;
}


.open-email-btn {
    color: white!important;
    margin: 10px auto; display: inline-flex; align-items: center; padding: 6px 12px; background-color: #BF0000; border-radius: 4px; text-decoration: none; color: white; font-size: 14px; font-weight: bold;
}
.open-email-btn:hover {
    background-color: #a00000;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(191, 0, 0, 0.15);
}


.btn {
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: -100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    transition: all 0.6s ease;
}

.btn:hover::after {
    left: 100%;
}


.announcement-card {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.announcement-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.announcement-header {
    padding: 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.announcement-title {
    font-weight: 600;
    margin: 0;
    color: #333;
}

.announcement-meta {
    font-size: 0.85rem;
    color: #6c757d;
}

.announcement-content {
    padding: 15px;
    background-color: #fff;
}

.announcement-footer {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.status-badge {
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.form-section {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.form-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.announcement-list {
    margin-top: 30px;
}

.btn-icon {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.target-role {
    display: inline-block;
    padding: 2px 6px;
    background-color: #e9ecef;
    border-radius: 4px;
    font-size: 0.75rem;
    margin-left: 5px;
}

.expiry-date {
    color: #dc3545;
    font-size: 0.85rem;
}

.expired {
    text-decoration: line-through;
    opacity: 0.7;
}

.avatar {
    width: 40px !important;
    height: 40px !important;
    min-width: 40px !important;      /* mencegah ukuran berubah */
    min-height: 40px !important;     /* mencegah ukuran berubah */
    border-radius: 50% !important;
    background-color: #FCF2F2 !important;
    display: inline-flex !important; /* lebih stabil dalam layout */
    align-items: center !important;
    justify-content: center !important;
    font-weight: 600 !important;
    color: #BF0000 !important;
    font-size: 0.8rem !important;     /* ukuran font tetap agar tidak overflow */
    text-align: center !important;   /* untuk keamanan jika ada teks */
    overflow: hidden !important;     /* mencegah teks keluar dari lingkaran */
    flex-shrink: 0 !important;       /* mencegah kompresi di dalam flexbox */
    position: relative !important;   /* jika ingin menambahkan tooltip/overlay */
    box-sizing: border-box !important; /* termasuk border tanpa merusak ukuran */
    line-height: 1 !important;       /* hindari distorsi vertikal dari font */
}

.avatar:hover {
    transform: scale(1.1)!important;
    transition: transform 0.3s ease !important;
    background-color: white!important;
    color: black!important;
}

.jarak {
    height: 80px;
}

@media screen and (max-width: 768px) {
    .jarak { height: 1000px; }
}

@media screen and (max-width: 576px) {
    .jarak { height: 1000px; }
}

@media screen and (max-width: 480px) {
    .jarak { height: 1000px; }
}


.card {
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.card-header {
    background-color: #BF0000 !important;
    color: white;
    padding: 15px 20px;
}

.card-header h3 {
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
}

.card-header h3 i {
    margin-right: 10px;
}

.card-body {
    padding: 20px;
    background-color: white;
}

.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--white);
    padding: var(--spacing-xl) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    text-align: center;
    margin-top: 3vh!important;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    transform: rotate(30deg);
    pointer-events: none;
}

.hero-section h1 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.hero-section p {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-lg);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-lg);
}
