<?php
/**
 * Schedule Backup Script
 *
 * This script sets up a scheduled task for automatic database backups
 * based on the settings in the database.
 */

session_start();
include '../config/config.php';

// Validate admin access
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Get backup settings
$query = "SELECT backup_schedule, backup_time FROM settings WHERE id = 1";
$result = $conn->query($query);

if (!$result) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Failed to retrieve backup settings']);
    exit();
}

$settings = $result->fetch_assoc();
$backupSchedule = $settings['backup_schedule'] ?? 'disabled';
$backupTime = $settings['backup_time'] ?? '01:00';

// Parse backup time
list($hour, $minute) = explode(':', $backupTime);

// Get script path
$scriptPath = realpath(__DIR__ . '/auto_backup.php');

// Determine OS and create appropriate scheduled task
$isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';

if ($isWindows) {
    // Windows Task Scheduler
    scheduleWindowsTask($backupSchedule, $hour, $minute, $scriptPath);
} else {
    // Linux/Unix Cron
    scheduleLinuxCron($backupSchedule, $hour, $minute, $scriptPath);
}

/**
 * Schedule a task in Windows Task Scheduler
 */
function scheduleWindowsTask($schedule, $hour, $minute, $scriptPath) {
    // Determine frequency based on schedule
    switch ($schedule) {
        case 'daily':
            $frequency = '/SC DAILY';
            break;
        case 'weekly':
            $frequency = '/SC WEEKLY /D SUN';
            break;
        case 'monthly':
            $frequency = '/SC MONTHLY /D 1';
            break;
        case 'disabled':
        default:
            // Remove existing task if disabled
            $deleteCommand = 'schtasks /Delete /TN "TrainingSystemBackup" /F';
            exec($deleteCommand, $output, $returnVar);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'Automatic backup has been disabled',
                'details' => 'Scheduled task removed'
            ]);
            exit();
    }

    // Get PHP executable path
    $phpPath = PHP_BINARY;

    // Create task command
    $taskName = 'TrainingSystemBackup';

    // Log the current directory and script path for debugging
    error_log("Current directory: " . getcwd());
    error_log("Script path: " . $scriptPath);
    error_log("PHP path: " . $phpPath);

    // Create a batch file to run the backup script
    $batchDir = dirname(dirname(__FILE__)) . '/backups';
    if (!file_exists($batchDir)) {
        mkdir($batchDir, 0755, true);
    }

    $batchFile = $batchDir . '/run_backup.bat';
    $batchContent = "@echo off\r\n";
    $batchContent .= "cd /d " . dirname(dirname(__FILE__)) . "\r\n";
    $batchContent .= "\"$phpPath\" \"$scriptPath\"\r\n";
    $batchContent .= "echo Backup completed at %date% %time% >> \"$batchDir/backup_log.txt\"\r\n";

    file_put_contents($batchFile, $batchContent);

    // Make sure the batch file is executable
    chmod($batchFile, 0755);

    // Use the batch file in the scheduled task
    $command = "schtasks /Create /TN \"$taskName\" $frequency /ST $hour:$minute /TR \"$batchFile\" /F";

    // Log the command for debugging
    error_log("Scheduled task command: " . $command);

    // Execute command
    exec($command, $output, $returnVar);

    // Check result
    if ($returnVar === 0) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Backup schedule has been set up successfully',
            'details' => "Scheduled to run $schedule at $hour:$minute"
        ]);
    } else {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Failed to set up backup schedule',
            'error' => implode("\n", $output),
            'command' => $command
        ]);
    }
}

/**
 * Schedule a task in Linux/Unix Cron
 */
function scheduleLinuxCron($schedule, $hour, $minute, $scriptPath) {
    // Get PHP executable path
    $phpPath = PHP_BINARY;

    // Create temporary file for crontab
    $tempFile = tempnam(sys_get_temp_dir(), 'cron');

    // Get current crontab
    exec('crontab -l 2>/dev/null', $currentCrontab, $returnVar);

    // Remove any existing backup tasks
    $filteredCrontab = [];
    foreach ($currentCrontab as $line) {
        if (strpos($line, 'auto_backup.php') === false) {
            $filteredCrontab[] = $line;
        }
    }

    // Add new task if not disabled
    if ($schedule !== 'disabled') {
        // Determine cron expression based on schedule
        switch ($schedule) {
            case 'daily':
                $cronExpr = "$minute $hour * * *";
                break;
            case 'weekly':
                $cronExpr = "$minute $hour * * 0";
                break;
            case 'monthly':
                $cronExpr = "$minute $hour 1 * *";
                break;
            default:
                $cronExpr = "$minute $hour * * *"; // Default to daily
        }

        // Add new cron job
        $filteredCrontab[] = "$cronExpr $phpPath $scriptPath > /dev/null 2>&1";
    }

    // Write to temporary file
    file_put_contents($tempFile, implode("\n", $filteredCrontab) . "\n");

    // Install new crontab
    exec("crontab $tempFile", $output, $returnVar);
    unlink($tempFile);

    // Check result
    if ($returnVar === 0) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => ($schedule === 'disabled')
                ? 'Automatic backup has been disabled'
                : 'Backup schedule has been set up successfully',
            'details' => ($schedule === 'disabled')
                ? 'Cron job removed'
                : "Scheduled to run $schedule at $hour:$minute"
        ]);
    } else {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Failed to set up backup schedule',
            'error' => implode("\n", $output)
        ]);
    }
}
