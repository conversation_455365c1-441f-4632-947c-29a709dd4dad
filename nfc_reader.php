<?php
// File: nfc_reader.php
// Deskripsi: Halaman untuk membaca tag NFC dari smartphone

// Ambil daftar training aktif
$api_url = "http://" . $_SERVER['HTTP_HOST'] . "/Training/api/nfc_attendance.php?action=get_trainings";
$trainings = [];

try {
    $response = file_get_contents($api_url);
    $data = json_decode($response, true);

    if ($data && $data['success'] && !empty($data['data'])) {
        $trainings = $data['data'];
    }
} catch (Exception $e) {
    // Error handling
}

// Cek apakah ada parameter card_id dan training_id di URL
$url_card_id = isset($_GET['card_id']) ? $_GET['card_id'] : '';
$url_training_id = isset($_GET['training_id']) ? $_GET['training_id'] : '';
$auto_submit = false;

// Jika kedua parameter ada, siapkan untuk auto-submit
if (!empty($url_card_id) && !empty($url_training_id)) {
    $auto_submit = true;
}

// Fungsi untuk memvalidasi ID kartu (hanya alfanumerik dan beberapa karakter khusus)
function validateCardId($card_id) {
    return preg_match('/^[a-zA-Z0-9:_-]+$/', $card_id);
}

// Validasi parameter URL
if ($auto_submit) {
    if (!validateCardId($url_card_id)) {
        $url_card_id = '';
        $auto_submit = false;
    }

    // Pastikan training_id ada dalam daftar training yang tersedia
    $training_valid = false;
    foreach ($trainings as $training) {
        if ($training['id'] == $url_training_id) {
            $training_valid = true;
            break;
        }
    }

    if (!$training_valid) {
        $url_training_id = '';
        $auto_submit = false;
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NFC Attendance - Training System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #4e73df;
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
        }
        .btn-primary {
            background-color: #4e73df;
            border-color: #4e73df;
        }
        .btn-success {
            background-color: #1cc88a;
            border-color: #1cc88a;
        }
        .btn-warning {
            background-color: #f6c23e;
            border-color: #f6c23e;
        }
        .nfc-icon {
            font-size: 80px;
            color: #4e73df;
            margin: 20px 0;
        }
        .nfc-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                transform: scale(0.95);
                opacity: 0.7;
            }
            50% {
                transform: scale(1.05);
                opacity: 1;
            }
            100% {
                transform: scale(0.95);
                opacity: 0.7;
            }
        }
        .status-box {
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }
        .status-success {
            background-color: #e6f7ef;
            border-left: 5px solid #1cc88a;
        }
        .status-error {
            background-color: #feecec;
            border-left: 5px solid #e74a3b;
        }
        .status-info {
            background-color: #e8f0fe;
            border-left: 5px solid #4e73df;
        }
        .employee-card {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="text-center mb-4">
                    <h1 class="h3">NFC Attendance</h1>
                    <p class="text-muted">Training System</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-id-card me-2"></i>Absensi dengan NFC</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-3">
                            <label for="training_select" class="form-label">Pilih Training:</label>
                            <select class="form-select" id="training_select">
                                <option value="">-- Pilih Training --</option>
                                <?php foreach ($trainings as $training): ?>
                                <option value="<?php echo $training['id']; ?>" <?php echo ($url_training_id == $training['id']) ? 'selected' : ''; ?>>
                                    <?php echo $training['topic'] . ' (' . $training['date'] . ')'; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <?php if ($auto_submit): ?>
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <span>ID Kartu terdeteksi dari URL. Memproses absensi...</span>
                        </div>
                        <?php endif; ?>

                        <div class="text-center">
                            <div id="nfc_icon" class="nfc-icon">
                                <i class="fas fa-wifi"></i>
                            </div>
                            <p id="nfc_status" class="mb-3">Klik tombol di bawah untuk mulai membaca NFC</p>
                            <button id="scan_button" class="btn btn-primary btn-lg mb-3">
                                <i class="fas fa-id-card me-2"></i>Scan NFC
                            </button>
                            <button id="check_nfc_button" class="btn btn-outline-info btn-sm mb-3 ms-2">
                                <i class="fas fa-sync-alt me-1"></i>Periksa Status NFC
                            </button>
                        </div>

                        <div id="status_box" class="status-box status-info d-none">
                            <p id="status_message" class="mb-0"></p>
                        </div>

                        <div id="employee_info" class="employee-card d-none">
                            <h5 id="employee_name" class="mb-2">-</h5>
                            <div class="row">
                                <div class="col-6">
                                    <p class="mb-1"><strong>NIK:</strong> <span id="employee_nik">-</span></p>
                                    <p class="mb-0"><strong>Jabatan:</strong> <span id="employee_position">-</span></p>
                                </div>
                                <div class="col-6">
                                    <p class="mb-1"><strong>Departemen:</strong> <span id="employee_dept">-</span></p>
                                    <p class="mb-0"><strong>Status:</strong> <span id="employee_status">-</span></p>
                                </div>
                            </div>
                        </div>

                        <!-- Manual Alternative (shown when NFC is not supported) -->
                        <div id="manual_alternative" class="mt-4 d-none">
                            <hr>
                            <h5 class="mb-3">Alternatif: Input Manual</h5>
                            <form id="manual_form" class="mb-3">
                                <div class="mb-3">
                                    <label for="card_number_input" class="form-label">Nomor Kartu RFID:</label>
                                    <input type="text" class="form-control" id="card_number_input" placeholder="Masukkan nomor kartu RFID" value="<?php echo htmlspecialchars($url_card_id); ?>">
                                    <div class="form-text">Masukkan nomor kartu RFID secara manual</div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>Kirim
                                </button>
                            </form>

                            <div class="mt-3">
                                <h6>URL untuk Integrasi:</h6>
                                <div class="input-group">
                                    <input type="text" id="integration_url" class="form-control form-control-sm" readonly>
                                    <button class="btn btn-outline-secondary btn-sm" type="button" id="copy_url_btn">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                <div class="form-text">URL ini dapat digunakan untuk integrasi dengan aplikasi NFC pihak ketiga</div>
                            </div>
                        </div>

                        <!-- iOS Alternative (shown only on iOS devices) -->
                        <div id="ios_alternative" class="mt-4 d-none">
                            <hr>
                            <h5 class="mb-3">Alternatif untuk Pengguna iOS</h5>
                            <p>Untuk pengguna iPhone, ikuti langkah-langkah berikut:</p>
                            <ol>
                                <li>Unduh aplikasi "NFC Tools" atau "NFC Reader" dari App Store</li>
                                <li>Buka aplikasi dan scan kartu RFID Anda</li>
                                <li>Salin nomor kartu yang muncul</li>
                                <li>Kembali ke halaman ini dan masukkan nomor tersebut di form input manual di atas</li>
                            </ol>
                        </div>

                        <!-- NFC Hardware Help (shown when NFC hardware might be disabled) -->
                        <div id="nfc_hardware_help" class="mt-4 d-none">
                            <hr>
                            <h5 class="mb-3">Cara Mengaktifkan NFC di Android</h5>
                            <p>Jika perangkat Anda memiliki NFC tetapi tidak terdeteksi, coba langkah-langkah berikut:</p>
                            <ol>
                                <li>Buka <strong>Pengaturan</strong> di smartphone Anda</li>
                                <li>Ketuk <strong>Koneksi</strong> atau <strong>Konektivitas</strong></li>
                                <li>Cari dan aktifkan <strong>NFC</strong> (geser tombol ke posisi aktif)</li>
                                <li>Pada beberapa perangkat, Anda juga perlu mengaktifkan <strong>Android Beam</strong></li>
                                <li>Kembali ke halaman ini dan muat ulang</li>
                            </ol>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Catatan untuk Infinix:</strong> Pada perangkat Infinix, NFC biasanya dapat ditemukan di:
                                <ul class="mb-0 mt-2">
                                    <li>Pengaturan > Koneksi > NFC</li>
                                    <li>Atau dengan menggeser ke bawah panel notifikasi dan mencari tombol NFC</li>
                                </ul>
                            </div>

                            <p class="mt-3"><strong>Periksa apakah perangkat Anda mendukung NFC:</strong></p>
                            <ol>
                                <li>Buka <strong>Pengaturan</strong> di smartphone Anda</li>
                                <li>Ketuk <strong>Tentang Ponsel</strong> atau <strong>Tentang Perangkat</strong></li>
                                <li>Ketuk <strong>Spesifikasi Hardware</strong> atau <strong>Semua Spesifikasi</strong></li>
                                <li>Cari "NFC" dalam daftar. Jika tidak ada, perangkat Anda mungkin tidak mendukung NFC</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informasi</h5>
                    </div>
                    <div class="card-body">
                        <p>Untuk menggunakan fitur ini:</p>
                        <ol>
                            <li>Pastikan smartphone Anda mendukung NFC dan fitur NFC diaktifkan</li>
                            <li>Pilih training yang akan dihadiri</li>
                            <li>Klik tombol "Scan NFC"</li>
                            <li>Tempelkan ID card RFID ke bagian belakang smartphone</li>
                            <li>Tunggu hingga proses selesai</li>
                        </ol>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Catatan:</strong> Fitur NFC hanya berfungsi pada browser Chrome versi 89+ di Android
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Alternatif:</strong> Jika NFC tidak berfungsi, Anda dapat menggunakan metode input manual:
                            <ul class="mb-0 mt-2">
                                <li><strong>Android:</strong> Gunakan aplikasi "NFC Tools" dari Play Store untuk membaca kartu, lalu salin nomor ke form manual</li>
                                <li><strong>iPhone:</strong> Gunakan aplikasi "NFC Tools" atau "NFC Reader" dari App Store, lalu salin nomor ke form manual</li>
                            </ul>
                        </div>

                        <div class="mt-3">
                            <h6>Posisi Sensor NFC pada Smartphone:</h6>
                            <div class="row">
                                <div class="col-6">
                                    <ul class="mb-0">
                                        <li>Samsung: Bagian tengah belakang</li>
                                        <li>Google Pixel: Bagian atas belakang</li>
                                        <li>Xiaomi: Tengah atau atas belakang</li>
                                    </ul>
                                </div>
                                <div class="col-6">
                                    <ul class="mb-0">
                                        <li>iPhone: Bagian atas belakang</li>
                                        <li>Oppo/Vivo: Bagian tengah belakang</li>
                                        <li>Realme: Bagian atas belakang</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-3">
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Kembali ke Beranda
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const scanButton = document.getElementById('scan_button');
            const nfcIcon = document.getElementById('nfc_icon');
            const nfcStatus = document.getElementById('nfc_status');
            const statusBox = document.getElementById('status_box');
            const statusMessage = document.getElementById('status_message');
            const employeeInfo = document.getElementById('employee_info');
            const employeeName = document.getElementById('employee_name');
            const employeeNik = document.getElementById('employee_nik');
            const employeePosition = document.getElementById('employee_position');
            const employeeDept = document.getElementById('employee_dept');
            const employeeStatus = document.getElementById('employee_status');
            const trainingSelect = document.getElementById('training_select');
            const integrationUrlInput = document.getElementById('integration_url');
            const copyUrlBtn = document.getElementById('copy_url_btn');

            // URL parameters for auto-submit
            const urlParams = new URLSearchParams(window.location.search);
            const urlCardId = urlParams.get('card_id');
            const urlTrainingId = urlParams.get('training_id');
            const autoSubmit = urlCardId && urlTrainingId;

            // Check if NFC is available
            const checkNfcAvailability = async () => {
                try {
                    // First check if NDEFReader exists in window
                    if ('NDEFReader' in window) {
                        try {
                            // Try to create an instance to verify it's really available
                            const ndef = new NDEFReader();
                            nfcStatus.textContent = 'NFC tersedia. Pilih training dan klik tombol Scan NFC.';
                            return true;
                        } catch (error) {
                            console.log("Error creating NDEFReader:", error);
                            // NDEFReader exists but can't be instantiated
                            return false;
                        }
                    } else {
                        return false;
                    }
                } catch (error) {
                    console.log("Error checking NFC:", error);
                    return false;
                }
            };

            // Check if device has NFC hardware
            const checkNfcHardware = async () => {
                // For Android, we can try to detect if NFC is enabled in settings
                const isAndroid = /Android/.test(navigator.userAgent);
                if (isAndroid) {
                    try {
                        // This is a simple check that might work on some Android devices
                        if ('permissions' in navigator && 'query' in navigator.permissions) {
                            const result = await navigator.permissions.query({ name: 'nfc' });
                            if (result.state === 'granted' || result.state === 'prompt') {
                                return true;
                            }
                        }
                    } catch (error) {
                        console.log("Error checking NFC permissions:", error);
                    }
                }

                // Default to unknown
                return null; // null means "unknown" rather than "not available"
            };

            // Main function to check NFC and update UI
            const updateNfcStatus = async () => {
                const hasWebNfcApi = await checkNfcAvailability();
                const hasNfcHardware = await checkNfcHardware();

                // Detect device type for better error message
                const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
                const isAndroid = /Android/.test(navigator.userAgent);
                const chromeVersion = /Chrome\/([0-9.]+)/.exec(navigator.userAgent);
                const chromeVersionNumber = chromeVersion ? parseInt(chromeVersion[1]) : 0;

                if (hasWebNfcApi) {
                    nfcStatus.textContent = 'NFC tersedia. Pilih training dan klik tombol Scan NFC.';
                    return;
                }

                // If Web NFC API is not available, show appropriate message
                if (isIOS) {
                    nfcStatus.textContent = 'NFC tidak didukung di Safari. Gunakan aplikasi NFC Reader sebagai alternatif.';
                    // Show alternative method for iOS
                    document.getElementById('ios_alternative').classList.remove('d-none');
                } else if (isAndroid) {
                    if (hasNfcHardware === false) {
                        nfcStatus.textContent = 'Perangkat Anda tidak memiliki hardware NFC atau NFC dinonaktifkan di pengaturan.';
                        document.getElementById('nfc_hardware_help').classList.remove('d-none');
                    } else if (chromeVersionNumber < 89) {
                        nfcStatus.textContent = `NFC tidak didukung di Chrome versi ${chromeVersionNumber}. Gunakan Chrome versi 89 atau lebih baru.`;
                    } else {
                        nfcStatus.textContent = 'NFC tidak didukung di browser ini. Pastikan Anda menggunakan Chrome dan NFC diaktifkan di pengaturan.';
                        document.getElementById('nfc_hardware_help').classList.remove('d-none');
                    }
                } else {
                    nfcStatus.textContent = 'NFC tidak didukung di perangkat desktop. Gunakan smartphone Android dengan Chrome.';
                }

                scanButton.disabled = true;
                scanButton.classList.add('btn-secondary');
                scanButton.classList.remove('btn-primary');

                // Show manual input alternative
                document.getElementById('manual_alternative').classList.remove('d-none');
            };

            // Run the check
            updateNfcStatus();

            // Update integration URL when training is selected
            trainingSelect.addEventListener('change', updateIntegrationUrl);

            function updateIntegrationUrl() {
                const trainingId = trainingSelect.value;
                if (trainingId) {
                    const baseUrl = window.location.href.split('?')[0];
                    const url = `${baseUrl}?training_id=${trainingId}&card_id=CARD_ID_HERE`;
                    integrationUrlInput.value = url;
                } else {
                    integrationUrlInput.value = '';
                }
            }

            // Initialize integration URL
            updateIntegrationUrl();

            // Copy URL button
            copyUrlBtn?.addEventListener('click', function() {
                if (integrationUrlInput.value) {
                    integrationUrlInput.select();
                    document.execCommand('copy');

                    // Show feedback
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check"></i>';
                    setTimeout(() => {
                        this.innerHTML = originalText;
                    }, 2000);
                }
            });

            // Auto-submit if URL parameters are present
            if (autoSubmit) {
                // Show manual input form
                document.getElementById('manual_alternative').classList.remove('d-none');

                // Process the card ID from URL
                processCardManually(urlCardId, urlTrainingId);
            }

            // Function to process card ID manually
            function processCardManually(cardNumber, trainingId) {
                showStatus('info', 'Memproses kartu dari URL...');

                fetch('api/nfc_attendance.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        card_number: cardNumber,
                        training_id: trainingId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showStatus('success', data.message);

                        if (data.data) {
                            employeeName.textContent = data.data.nama;
                            employeeNik.textContent = data.data.nik;
                            employeePosition.textContent = data.data.jabatan;
                            employeeDept.textContent = data.data.dept;

                            let statusText = '';
                            switch (data.data.action) {
                                case 'check_in':
                                    statusText = 'Check-in berhasil';
                                    employeeStatus.className = 'text-success';
                                    break;
                                case 'check_out':
                                    statusText = 'Check-out berhasil';
                                    employeeStatus.className = 'text-primary';
                                    break;
                                case 'new_attendance':
                                    statusText = 'Absensi baru';
                                    employeeStatus.className = 'text-success';
                                    break;
                                case 'already_complete':
                                    statusText = 'Sudah lengkap';
                                    employeeStatus.className = 'text-secondary';
                                    break;
                                default:
                                    statusText = 'Berhasil';
                                    employeeStatus.className = 'text-success';
                            }

                            employeeStatus.textContent = statusText;
                            employeeInfo.classList.remove('d-none');
                        }
                    } else {
                        showStatus('error', data.message || 'Terjadi kesalahan saat memproses kartu');
                    }
                })
                .catch(error => {
                    showStatus('error', 'Terjadi kesalahan saat menghubungi server');
                    console.error('Error:', error);
                });
            }

            // Handle check NFC button click
            document.getElementById('check_nfc_button').addEventListener('click', async () => {
                showStatus('info', 'Memeriksa status NFC...');

                // Check if NFC is enabled in Android settings
                if ('NDEFReader' in window) {
                    try {
                        const ndef = new NDEFReader();
                        await ndef.scan();
                        showStatus('success', 'NFC tersedia dan aktif! Anda dapat menggunakan fitur Scan NFC.');
                        nfcStatus.textContent = 'NFC tersedia. Pilih training dan klik tombol Scan NFC.';
                        scanButton.disabled = false;
                        scanButton.classList.remove('btn-secondary');
                        scanButton.classList.add('btn-primary');
                    } catch (error) {
                        console.log("Error scanning:", error);

                        if (error.name === 'NotAllowedError') {
                            showStatus('error', 'NFC tidak diizinkan. Pastikan NFC diaktifkan di pengaturan perangkat Anda.');
                        } else if (error.name === 'NotSupportedError') {
                            showStatus('error', 'NFC tidak didukung oleh perangkat ini.');
                        } else if (error.name === 'NotReadableError') {
                            showStatus('error', 'NFC tidak dapat dibaca. Pastikan NFC diaktifkan di pengaturan.');
                        } else if (error.name === 'AbortError') {
                            showStatus('error', 'Pembacaan NFC dibatalkan.');
                        } else {
                            showStatus('error', `Error: ${error.message || 'Tidak dapat mengakses NFC'}`);
                        }

                        // Show detailed device info for troubleshooting
                        const deviceInfo = `
                            <div class="mt-3 p-3 bg-light rounded">
                                <h6>Informasi Perangkat:</h6>
                                <ul class="mb-0">
                                    <li>Browser: ${navigator.userAgent}</li>
                                    <li>Platform: ${navigator.platform}</li>
                                    <li>Vendor: ${navigator.vendor}</li>
                                </ul>
                            </div>
                        `;
                        document.getElementById('nfc_hardware_help').innerHTML += deviceInfo;
                        document.getElementById('nfc_hardware_help').classList.remove('d-none');
                    }
                } else {
                    showStatus('error', 'Web NFC API tidak didukung di browser ini.');

                    // Check if it's Chrome on Android but old version
                    const isAndroid = /Android/.test(navigator.userAgent);
                    const chromeVersion = /Chrome\/([0-9.]+)/.exec(navigator.userAgent);
                    const chromeVersionNumber = chromeVersion ? parseInt(chromeVersion[1]) : 0;

                    if (isAndroid && chromeVersionNumber > 0) {
                        if (chromeVersionNumber < 89) {
                            showStatus('error', `Chrome versi ${chromeVersionNumber} tidak mendukung Web NFC API. Perbarui ke versi 89+.`);
                        } else {
                            showStatus('error', `Chrome versi ${chromeVersionNumber} seharusnya mendukung Web NFC API. Pastikan NFC diaktifkan di pengaturan.`);
                        }
                    }

                    document.getElementById('nfc_hardware_help').classList.remove('d-none');
                }
            });

            // Handle scan button click
            scanButton.addEventListener('click', async () => {
                const trainingId = trainingSelect.value;

                if (!trainingId) {
                    showStatus('error', 'Silakan pilih training terlebih dahulu');
                    return;
                }

                if (!('NDEFReader' in window)) {
                    showStatus('error', 'NFC tidak didukung di browser ini');
                    return;
                }

                try {
                    nfcIcon.classList.add('nfc-animation');
                    nfcStatus.textContent = 'Menunggu kartu NFC...';
                    scanButton.disabled = true;

                    const ndef = new NDEFReader();
                    await ndef.scan();

                    showStatus('info', 'Mencari kartu NFC...');

                    ndef.addEventListener("reading", ({ serialNumber }) => {
                        if (serialNumber) {
                            // Convert serial number to format compatible with RFID system
                            const formattedSerial = formatNfcSerial(serialNumber);
                            processNfcTag(formattedSerial, trainingId);
                        } else {
                            showStatus('error', 'Tidak dapat membaca serial number kartu');
                            resetScanState();
                        }
                    });

                    ndef.addEventListener("error", (error) => {
                        showStatus('error', `Error: ${error.message}`);
                        resetScanState();
                    });

                } catch (error) {
                    showStatus('error', `Error: ${error.message}`);
                    resetScanState();
                }
            });

            // Format NFC serial number to match RFID format
            function formatNfcSerial(serial) {
                // Convert to uppercase and remove colons if present
                let formatted = serial.toUpperCase().replace(/:/g, '');

                // Reverse byte order if needed (depends on your RFID system)
                // This is an example and might need adjustment
                const bytes = [];
                for (let i = 0; i < formatted.length; i += 2) {
                    bytes.push(formatted.substr(i, 2));
                }

                // Return the formatted serial
                return formatted;
            }

            // Process NFC tag data
            function processNfcTag(cardNumber, trainingId) {
                showStatus('info', 'Memproses kartu...');

                fetch('api/nfc_attendance.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        card_number: cardNumber,
                        training_id: trainingId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showStatus('success', data.message);

                        if (data.data) {
                            employeeName.textContent = data.data.nama;
                            employeeNik.textContent = data.data.nik;
                            employeePosition.textContent = data.data.jabatan;
                            employeeDept.textContent = data.data.dept;

                            let statusText = '';
                            switch (data.data.action) {
                                case 'check_in':
                                    statusText = 'Check-in berhasil';
                                    employeeStatus.className = 'text-success';
                                    break;
                                case 'check_out':
                                    statusText = 'Check-out berhasil';
                                    employeeStatus.className = 'text-primary';
                                    break;
                                case 'new_attendance':
                                    statusText = 'Absensi baru';
                                    employeeStatus.className = 'text-success';
                                    break;
                                case 'already_complete':
                                    statusText = 'Sudah lengkap';
                                    employeeStatus.className = 'text-secondary';
                                    break;
                                default:
                                    statusText = 'Berhasil';
                                    employeeStatus.className = 'text-success';
                            }

                            employeeStatus.textContent = statusText;
                            employeeInfo.classList.remove('d-none');
                        }
                    } else {
                        showStatus('error', data.message || 'Terjadi kesalahan saat memproses kartu');
                    }

                    resetScanState();
                })
                .catch(error => {
                    showStatus('error', 'Terjadi kesalahan saat menghubungi server');
                    console.error('Error:', error);
                    resetScanState();
                });
            }

            // Show status message
            function showStatus(type, message) {
                statusBox.classList.remove('d-none', 'status-success', 'status-error', 'status-info');

                switch (type) {
                    case 'success':
                        statusBox.classList.add('status-success');
                        break;
                    case 'error':
                        statusBox.classList.add('status-error');
                        break;
                    default:
                        statusBox.classList.add('status-info');
                }

                statusMessage.textContent = message;
                statusBox.classList.remove('d-none');
            }

            // Reset scan state
            function resetScanState() {
                nfcIcon.classList.remove('nfc-animation');
                nfcStatus.textContent = 'Klik tombol di bawah untuk mulai membaca NFC';
                scanButton.disabled = false;
            }

            // Handle manual form submission
            document.getElementById('manual_form')?.addEventListener('submit', function(e) {
                e.preventDefault();

                const trainingId = trainingSelect.value;
                const cardNumber = document.getElementById('card_number_input').value.trim();

                if (!trainingId) {
                    showStatus('error', 'Silakan pilih training terlebih dahulu');
                    return;
                }

                if (!cardNumber) {
                    showStatus('error', 'Silakan masukkan nomor kartu RFID');
                    return;
                }

                // Process the card using the shared function
                processCardManually(cardNumber, trainingId);

                // Clear input
                document.getElementById('card_number_input').value = '';

                // Update URL with the card ID and training ID (without reloading the page)
                const url = new URL(window.location);
                url.searchParams.set('card_id', cardNumber);
                url.searchParams.set('training_id', trainingId);
                window.history.pushState({}, '', url);
            });
        });
    </script>
</body>
</html>
