<?php
/**
 * Training Summary Data Handler
 * Provides summary statistics for both external and internal training
 */

session_start();
include '../config/config.php';

// Check if user is logged in and has admin access
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

$training_type = isset($_GET['training_type']) ? $_GET['training_type'] : 'external';

try {
    $summary = [];
    
    if ($training_type === 'internal') {
        // Get internal training summary
        $query = "SELECT
            COUNT(*) as total,
            SUM(CASE WHEN status = 'Cancelled' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN status = 'Active' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN status = 'Completed' THEN 1 ELSE 0 END) as completed
            FROM offline_training";
        
        $result = mysqli_query($conn, $query);
        if (!$result) {
            throw new Exception('Database query failed: ' . mysqli_error($conn));
        }
        
        $summary = mysqli_fetch_assoc($result);
        $summary['pending'] = 0; // Internal training doesn't have pending status
        
    } else {
        // Get external training summary
        $total_query = "SELECT
            COUNT(*) as total,
            SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
            FROM training_submissions";
        
        $result = mysqli_query($conn, $total_query);
        if (!$result) {
            throw new Exception('Database query failed: ' . mysqli_error($conn));
        }
        
        $summary = mysqli_fetch_assoc($result);
        
        // Get pending count separately
        $pending_query = "SELECT COUNT(*) as pending FROM training_submissions WHERE status = 'pending'";
        $pending_result = mysqli_query($conn, $pending_query);
        if (!$pending_result) {
            throw new Exception('Pending query failed: ' . mysqli_error($conn));
        }
        
        $pending = mysqli_fetch_assoc($pending_result);
        $summary['pending'] = $pending['pending'];
    }
    
    // Get totals for both types for tab counts
    $external_total_query = "SELECT COUNT(*) as count FROM training_submissions";
    $external_result = mysqli_query($conn, $external_total_query);
    $external_total = mysqli_fetch_assoc($external_result);
    
    $internal_total_query = "SELECT COUNT(*) as count FROM offline_training";
    $internal_result = mysqli_query($conn, $internal_total_query);
    $internal_total = mysqli_fetch_assoc($internal_result);
    
    // Ensure all values are set
    $summary['total'] = $summary['total'] ?? 0;
    $summary['pending'] = $summary['pending'] ?? 0;
    $summary['rejected'] = $summary['rejected'] ?? 0;
    $summary['approved'] = $summary['approved'] ?? 0;
    $summary['completed'] = $summary['completed'] ?? 0;
    $summary['external_total'] = $external_total['count'] ?? 0;
    $summary['internal_total'] = $internal_total['count'] ?? 0;
    
    header('Content-Type: application/json');
    echo json_encode($summary);
    
} catch (Exception $e) {
    error_log("Error in get_training_summary.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
