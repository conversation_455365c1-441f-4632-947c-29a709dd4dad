<?php
// File: admin/get_card_data.php
// Deskripsi: Script untuk mendapatkan data kartu RFID secara asinkron

// Aktifkan output buffering
ob_start();

// Nonaktifkan error reporting yang bisa merusak JSON
error_reporting(0);
ini_set('display_errors', 0);

// Set header JSON di awal
header('Content-Type: application/json');

// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Include security.php untuk pengecekan akses
include 'security.php';

// Ambil parameter dari request
$search = isset($_GET['search']) ? $_GET['search'] : '';
$dept = isset($_GET['dept']) ? $_GET['dept'] : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

// Hitung offset
$offset = ($page - 1) * $limit;

try {
    // Buat query dasar
    $query = "SELECT k.id, k.nik, k.nama, k.dept, k.bagian, k.jabatan, k.card_number 
              FROM karyawan k 
              WHERE 1=1";
    $count_query = "SELECT COUNT(*) as total FROM karyawan k WHERE 1=1";
    
    $params = [];
    $types = "";
    
    // Tambahkan filter pencarian jika ada
    if (!empty($search)) {
        $query .= " AND (k.nik LIKE ? OR k.nama LIKE ? OR k.card_number LIKE ?)";
        $count_query .= " AND (k.nik LIKE ? OR k.nama LIKE ? OR k.card_number LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
        $types .= "sss";
    }
    
    // Tambahkan filter departemen jika ada
    if (!empty($dept)) {
        $query .= " AND k.dept = ?";
        $count_query .= " AND k.dept = ?";
        $params[] = $dept;
        $types .= "s";
    }
    
    // Tambahkan sorting dan limit
    $query .= " ORDER BY k.nama ASC LIMIT ?, ?";
    $params[] = $offset;
    $params[] = $limit;
    $types .= "ii";
    
    // Jalankan query untuk menghitung total data
    $stmt_count = $conn->prepare($count_query);
    if (!empty($params) && count($params) > 0 && !empty($types)) {
        // Hapus parameter limit dan offset untuk query count
        $count_params = array_slice($params, 0, -2);
        $count_types = substr($types, 0, -2);
        if (!empty($count_params)) {
            $stmt_count->bind_param($count_types, ...$count_params);
        }
    }
    $stmt_count->execute();
    $result_count = $stmt_count->get_result();
    $row_count = $result_count->fetch_assoc();
    $total_records = $row_count['total'];
    $total_pages = ceil($total_records / $limit);
    
    // Jalankan query untuk mendapatkan data
    $stmt = $conn->prepare($query);
    if (!empty($params) && count($params) > 0) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    // Siapkan data untuk response
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    
    // Siapkan data departemen untuk filter
    $dept_query = "SELECT DISTINCT dept FROM karyawan ORDER BY dept ASC";
    $dept_result = $conn->query($dept_query);
    $departments = [];
    while ($dept_row = $dept_result->fetch_assoc()) {
        $departments[] = $dept_row['dept'];
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'data' => $data,
        'departments' => $departments,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_records' => $total_records,
            'limit' => $limit
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($stmt)) {
        $stmt->close();
    }
    if (isset($stmt_count)) {
        $stmt_count->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
    
    // Flush output buffer and end script
    ob_end_flush();
}
?>
