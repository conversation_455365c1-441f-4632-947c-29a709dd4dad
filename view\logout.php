<?php
session_start();

// Log aktivitas logout jika user sudah login
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
    $user_name = $_SESSION['name'] ?? 'Unknown User';

    // Include database connection
    include_once '../config/config.php';

    // Log ke security_logs table untuk monitoring keamanan
    try {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

        // Insert ke security_logs table
        $stmt = $conn->prepare("INSERT INTO security_logs (user_id, event_type, description, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
        $event_type = 'LOGOUT';
        $description = "User logout dari sistem - " . $user_name;
        $stmt->bind_param("issss", $user_id, $event_type, $description, $ip_address, $user_agent);
        $stmt->execute();
        $stmt->close();

        // Log ke activity_logs table juga (jika ada)
        if (file_exists('../config/activity_logger.php')) {
            include_once '../config/activity_logger.php';

            if (function_exists('log_activity')) {
                log_activity($user_id, "Logout dari sistem", "login", [
                    'ip_address' => $ip_address,
                    'user_agent' => $user_agent,
                    'logout_time' => date('Y-m-d H:i:s')
                ]);
            }
        }

    } catch (Exception $e) {
        // Jika gagal log, tetap lanjutkan logout
        error_log("Error logging logout: " . $e->getMessage());
    }
}

// Hapus semua data session
session_unset(); // Menghapus semua session
session_destroy(); // Menghancurkan session

// Redirect ke halaman login
header('Location: login.php'); // Arahkan kembali ke halaman login
exit();
?>
