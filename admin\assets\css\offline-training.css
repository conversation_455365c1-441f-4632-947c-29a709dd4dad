 /* Card styling */
 .card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.card-header h6 {
    font-weight: 700;
    margin: 0;
    color: white;
}

/* Table styling */
.table {
    table-layout: fixed;
    width: 100%;
}

.table td, .table th {
    white-space: normal;
    word-wrap: break-word;
}
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    table-layout: fixed;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #e3e6f0;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #e3e6f0;
    background-color: #f8f9fc;
    color: #4e73df;
    font-weight: 700;
}

.table-bordered {
    border: 1px solid #e3e6f0;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #e3e6f0;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

/* Badge styling */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-success {
    color: #fff;
    background-color: #1cc88a;
}

.badge-primary {
    color: #fff;
    background-color: #4e73df;
}

.badge-danger {
    color: #fff;
    background-color: #e74a3b;
}

/* Button styling */
.btn-group {
    display: flex;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

.btn-primary {
    color: #fff;
    background-color: #BF0000;
    border-color: #BF0000;
}

.btn-primary:hover {
    background-color: #a30000;
    border-color: #a30000;
}

.btn-info {
    color: #fff;
    background-color: #36b9cc;
    border-color: #36b9cc;
}

.btn-info:hover {
    background-color: #2c9faf;
    border-color: #2c9faf;
}

.btn-success {
    color: #fff;
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-success:hover {
    background-color: #17a673;
    border-color: #17a673;
}

.btn-danger {
    color: #fff;
    background-color: #e74a3b;
    border-color: #e74a3b;
}

.btn-danger:hover {
    background-color: #e02d1b;
    border-color: #e02d1b;
}

/* Form styling */
.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #6e707e;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: #6e707e;
    background-color: #fff;
    border-color: #bac8f3;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

textarea.form-control {
    height: auto;
}

/* Modal styling */
.modal-header {
    border-bottom: 1px solid #e3e6f0;
    background-color: #f8f9fc;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
}

.modal-title {
    color: #BF0000;
    font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table th,
    .table td {
        padding: 0.5rem;
    }

    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }

    .btn-group {
        flex-wrap: wrap;
    }
}

.nav-link {
    color: gray;
}

/* Custom untuk kolom aksi agar bisa muat 4 tombol */
.aksi-column {
    min-width: 200px;
    white-space: nowrap;
}

.aksi-column .btn-group {
    justify-content: flex-start;
}
