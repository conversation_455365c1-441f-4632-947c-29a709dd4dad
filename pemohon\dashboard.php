<?php
include '../config/config.php';
include 'security.php';
include '../includes/functions.php';

// Initialize TrainingHelper
$trainingHelper = new TrainingHelper($conn);

// Move expired trainings to history
$trainingHelper->moveExpiredTrainingsToHistory();

$user_id = $_SESSION['user_id'] ?? null;
if (!$user_id) {
    die("Anda harus login terlebih dahulu.");
}

// Query untuk data training
$query = "SELECT
    ts.id,
    ts.training_topic,
    ts.start_date,
    ts.end_date,
    ts.is_confirmed,
    ts.approved_dept_head,
    ts.approved_hrd,
    ts.approved_fm,
    ts.approved_dir,
    ts.approved_ga,
    ts.approved_by,
    ts.status,
    ts.rejected_by,
    ts.rejected_at,
    u.dept AS departemen
FROM training_submissions ts
LEFT JOIN users u ON ts.user_id = u.id
WHERE ts.user_id = ?
ORDER BY ts.id DESC";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

// Fetch approver names
$approvers = [
    'dept_head' => $conn->query("
        SELECT u.name
        FROM users u
        INNER JOIN user_departments ud ON u.id = ud.user_id
        WHERE u.role_id = 2
        AND ud.dept = (SELECT dept FROM users WHERE id = $user_id)
        LIMIT 1")->fetch_assoc()['name'] ?? 'Tidak Ditemukan',
    'lnd' => $conn->query("SELECT name FROM users WHERE jabatan = 'Chief Supervisor Learning & Development' LIMIT 1")->fetch_assoc()['name'] ?? 'Tidak Ditemukan',
    'hrga' => $conn->query("SELECT name FROM users WHERE jabatan = 'Manager HRGA' LIMIT 1")->fetch_assoc()['name'] ?? 'Tidak Ditemukan',
    'direktur' => $conn->query("SELECT name FROM users WHERE jabatan = 'Direktur' LIMIT 1")->fetch_assoc()['name'] ?? 'Tidak Ditemukan',
    'factory' => $conn->query("SELECT name FROM users WHERE jabatan = 'Factory Manager' LIMIT 1")->fetch_assoc()['name'] ?? 'Tidak Ditemukan'
];
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
:root {
    --primary-color: rgb(157, 0, 0);
    --primary-color-dark: rgb(127, 0, 0);
    --primary-color-light: rgba(157, 0, 0, 0.1);
    --success-color: #4CAF50;
    --success-color-light: #e8f5e9;
    --warning-color: #ffc107;
    --danger-color: #f44336;
    --text-dark: #333;
    --text-light: #757575;
    --white: #ffffff;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
    --shadow-lg: 0 6px 15px rgba(0,0,0,0.1);
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 10px;
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
    --font-size-xs: 11px;
    --font-size-sm: 13px;
    --font-size-md: 16px;
    --font-size-lg: 20px;
    --font-size-xl: 24px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
}

body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
}

.container-form {
    padding: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
    animation: fadeIn 0.5s ease-in-out;
}

.welcome-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    transform: rotate(30deg);
    pointer-events: none;
}

.welcome-section h1 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.welcome-section p {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-md);
    opacity: 0.9;
}

.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    border-left: 4px solid var(--primary-color);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color-light) 0%, rgba(255,255,255,0) 60%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card h3 {
    margin: 0;
    color: var(--text-dark);
    font-size: var(--font-size-md);
    font-weight: 600;
    position: relative;
}

.stat-card p {
    margin: var(--spacing-sm) 0 0;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    font-weight: bold;
    position: relative;
}

.submissions-table {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
    transition: box-shadow var(--transition-normal);
}

.submissions-table:hover {
    box-shadow: var(--shadow-md);
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) #f1f1f1;
}

.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

.submissions-table table {
    width: 100%;
    border-collapse: collapse;
}

.submissions-table th {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

.submissions-table td {
    background: var(--white);
    padding: var(--spacing-md);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    vertical-align: middle;
}

.submissions-table tr:hover td {
    background-color: rgba(0,0,0,0.02);
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all var(--transition-fast);
}

.status-pending {
    background-color: rgba(255, 193, 7, 0.2);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.4);
}

.status-approved {
    background-color: rgba(76, 175, 80, 0.2);
    color: #155724;
    border: 1px solid rgba(76, 175, 80, 0.4);
}

.status-rejected {
    background-color: rgba(244, 67, 54, 0.2);
    color: #721c24;
    border: 1px solid rgba(244, 67, 54, 0.4);
}

.status-revise {
    background-color: rgba(255, 152, 0, 0.2);
    color: #e65100;
    border: 1px solid rgba(255, 152, 0, 0.4);
}

.action-button {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-button:hover {
    background: var(--primary-color-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: var(--white);
    text-decoration: none;
}

.action-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.new-submission-button {
    text-align: center;
    margin-top: var(--spacing-lg);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.date-confirmed {
    color: var(--success-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    background-color: rgba(76, 175, 80, 0.1);
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    border-left: 4px solid var(--success-color);
}

.date-confirmed i {
    color: var(--success-color);
}

.date-pending {
    color: var(--warning-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    background-color: rgba(255, 193, 7, 0.1);
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    border-left: 4px solid var(--warning-color);
}

.date-pending i {
    color: var(--warning-color);
}

.date-fixed {
    color: var(--success-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.date-fixed i {
    color: var(--success-color);
}

.confirmed {
    background-color: rgba(76, 175, 80, 0.2);
    color: #155724;
    border: 1px solid rgba(76, 175, 80, 0.4);
}

.pending-badge {
    background-color: rgba(255, 193, 7, 0.2);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.4);
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 8px;
}

.confirmed-badge {
    background-color: rgba(76, 175, 80, 0.2);
    color: #155724;
    border: 1px solid rgba(76, 175, 80, 0.4);
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 8px;
}

.approved-status {
    text-align: center;
}

.status-badge.status-rejected {
    color: #721c24;
}

.approved-status .status-badge i {
    margin-right: 8px;
    color: #2e7d32;
}

.jarak {
    height: 80px;
}

/* Card layout for mobile */
.mobile-card {
    display: none;
}

/* Empty state styling */
.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.empty-state i {
    font-size: 3rem;
    color: var(--primary-color-light);
    margin-bottom: var(--spacing-md);
    display: block;
}

.empty-state h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--text-dark);
}

.empty-state p {
    color: var(--text-light);
    max-width: 400px;
    margin: 0 auto;
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 90%;
    width: 350px;
}

.error-message,
.success-message {
    padding: 15px;
    border-radius: var(--border-radius-md);
    margin-bottom: 10px;
    font-size: var(--font-size-sm);
    animation: slideIn 0.3s ease-out;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: 10px;
}

.error-message {
    background: var(--danger-color);
    color: white;
    border-left: 5px solid #b71c1c;
}

.success-message {
    background: var(--success-color);
    color: white;
    border-left: 5px solid #1b5e20;
}

.notification-icon {
    font-size: 20px;
}

.notification-content {
    flex: 1;
}

.notification-close {
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.notification-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Responsive styles */
@media screen and (max-width: 992px) {
    .stats-section {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media screen and (max-width: 768px) {
    .container-form {
        padding: var(--spacing-sm);
    }

    .welcome-section {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .welcome-section h1 {
        font-size: var(--font-size-lg);
    }

    .stats-section {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--spacing-sm);
    }

    .stat-card {
        padding: var(--spacing-md);
    }

    /* Hide regular table on mobile */
    .desktop-table {
        display: none;
    }

    /* Show card layout on mobile */
    .mobile-card {
        display: block;
        margin-bottom: var(--spacing-lg);
    }

    .mobile-submission-card {
        background: var(--white);
        border-radius: var(--border-radius-md);
        box-shadow: var(--shadow-sm);
        margin-bottom: var(--spacing-md);
        overflow: hidden;
        transition: transform var(--transition-fast), box-shadow var(--transition-fast);
    }

    .mobile-submission-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .mobile-card-header {
        background: var(--primary-color);
        color: var(--white);
        padding: var(--spacing-sm) var(--spacing-md);
        font-weight: 600;
    }

    .mobile-card-body {
        padding: var(--spacing-md);
    }

    .mobile-card-row {
        display: flex;
        justify-content: space-between;
        padding: var(--spacing-xs) 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .mobile-card-row:last-child {
        border-bottom: none;
    }

    .mobile-card-label {
        font-weight: 600;
        color: var(--text-dark);
        font-size: var(--font-size-sm);
    }

    .mobile-card-value {
        text-align: right;
        font-size: var(--font-size-sm);
    }

    .mobile-card-footer {
        padding: var(--spacing-sm) var(--spacing-md);
        background: rgba(0,0,0,0.02);
        text-align: center;
    }

    .notifications-container {
        width: 90%;
        max-width: 400px;
    }
}

@media screen and (max-width: 480px) {
    .welcome-section h1 {
        font-size: var(--font-size-md);
    }

    .welcome-section p {
        font-size: var(--font-size-sm);
    }

    .stat-card h3 {
        font-size: var(--font-size-sm);
    }

    .stat-card p {
        font-size: var(--font-size-lg);
    }

    .jarak {
        height: 60px;
    }

    .action-button {
        padding: 6px 12px;
        font-size: var(--font-size-xs);
    }
}
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container-form">
    <div class="notifications-container">
        <?php if (isset($_SESSION['errors'])): ?>
            <div class="error-message">
                <div class="notification-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="notification-content">
                    <?php foreach ($_SESSION['errors'] as $error): ?>
                        <p><?php echo htmlspecialchars($error); ?></p>
                    <?php endforeach; ?>
                </div>
                <button class="notification-close" onclick="this.parentElement.style.animation='slideOut 0.5s forwards';setTimeout(() => this.parentElement.remove(), 500)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <?php unset($_SESSION['errors']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['success'])): ?>
            <div class="success-message">
                <div class="notification-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="notification-content">
                    <p><?php echo htmlspecialchars($_SESSION['success']); ?></p>
                </div>
                <button class="notification-close" onclick="this.parentElement.style.animation='slideOut 0.5s forwards';setTimeout(() => this.parentElement.remove(), 500)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>
    </div>
    <div class="welcome-section">
        <h1>Dashboard Pengajuan Training</h1>
        <p>Selamat datang, <?= htmlspecialchars($_SESSION['user_name'] ?? 'Pengguna') ?></p>
    </div>

    <?php
    // Calculate counts using same logic as index.php for consistency
    $pending_count = 0;
    $approved_count = 0;
    $rejected_count = 0;

    // Store data in array to avoid consuming the result set
    $training_data = [];
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $training_data[] = $row;

            // Count while we're at it - using EXACT same logic as index.php
            if ($row['status'] == 'Rejected') {
                $rejected_count++;
            } elseif ($row['approved_dir'] == 'Approved') {
                $approved_count++;
            } elseif ($row['approved_dir'] == 'Pending') {
                $pending_count++;
            }
            // Note: This should match index.php logic exactly
        }
    }

    $total_count = $pending_count + $approved_count + $rejected_count;


    ?>

    <div class="stats-section">
        <div class="stat-card">
            <h3>Total Pengajuan</h3>
            <p><?= $total_count ?></p>
        </div>
        <div class="stat-card">
            <h3>Pending</h3>
            <p><?= $pending_count ?></p>
        </div>
        <div class="stat-card">
            <h3>Disetujui</h3>
            <p><?= $approved_count ?></p>
        </div>
        <div class="stat-card">
            <h3>Ditolak</h3>
            <p><?= $rejected_count ?></p>
        </div>
    </div>

    <!-- Desktop Table View -->
    <div class="submissions-table desktop-table">
        <div class="table-responsive">
            <table>
                <thead>
                    <tr>
                        <th style="width: 200px;">Training</th>
                        <th style="width: 180px;">Tanggal</th>
                        <th>Dept Head<br/>(<?= $approvers['dept_head'] ?>)</th>
                        <th>LnD<br/>(<?= $approvers['lnd'] ?>)</th>
                        <th>HRGA<br/>(<?= $approvers['hrga'] ?>)</th>
                        <th>Factory<br/>(<?= $approvers['factory'] ?>)</th>
                        <th>Direktur<br/>(<?= $approvers['direktur'] ?>)</th>
                        <th style="width: 100px;">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    foreach ($training_data as $row) :
                        // Ambil nama dept head dari database jika ada
                        $dept_head = 'Tidak Ditemukan';
                        if (!empty($row['approved_by'])) {
                            $dept_head_query = "SELECT name FROM users WHERE id = ? AND role_id = 2 AND dept = ?";
                            $dept_stmt = $conn->prepare($dept_head_query);
                            $dept_stmt->bind_param("is", $row['approved_by'], $row['departemen']);
                            $dept_stmt->execute();
                            $dept_result = $dept_stmt->get_result();
                            if ($dept_row = $dept_result->fetch_assoc()) {
                                $dept_head = $dept_row['name'];
                            }
                            $dept_stmt->close();
                        }
                    ?>
                        <tr>
                            <td style="width: 200px;"><?= htmlspecialchars($row['training_topic']) ?></td>
                            <td style="width: 180px;">
                                <?php
                                if (!empty($row['start_date'])) {
                                    // Ada tanggal training
                                    if ($row['is_confirmed'] == 1) {
                                        // Tanggal sudah dikonfirmasi/fixed
                                        if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                                            // Multi-day training yang sudah dikonfirmasi
                                            echo '<span class="date-confirmed">';
                                            echo '<i class="fas fa-calendar-check"></i> ';
                                            echo htmlspecialchars($row['start_date']) . ' s/d ' . htmlspecialchars($row['end_date']);
                                            echo '<span class="confirmed-badge">Dikonfirmasi</span>';
                                            echo '</span>';
                                        } else {
                                            // Single day training yang sudah dikonfirmasi
                                            echo '<span class="date-confirmed">';
                                            echo '<i class="fas fa-calendar-check"></i> ';
                                            echo htmlspecialchars($row['start_date']);
                                            echo '<span class="confirmed-badge">Dikonfirmasi</span>';
                                            echo '</span>';
                                        }
                                    } else {
                                        // Tanggal belum dikonfirmasi (pending)
                                        if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                                            // Multi-day training yang masih pending
                                            echo '<span class="date-pending">';
                                            echo '<i class="fas fa-calendar-alt"></i> ';
                                            echo htmlspecialchars($row['start_date']) . ' s/d ' . htmlspecialchars($row['end_date']);
                                            echo '<span class="pending-badge">Pending</span>';
                                            echo '</span>';
                                        } else {
                                            // Single day training yang masih pending
                                            echo '<span class="date-pending">';
                                            echo '<i class="fas fa-calendar-alt"></i> ';
                                            echo htmlspecialchars($row['start_date']);
                                            echo '<span class="pending-badge">Pending</span>';
                                            echo '</span>';
                                        }
                                    }
                                } else {
                                    // Belum ada tanggal sama sekali
                                    echo '<span class="date-pending">';
                                    echo '<i class="fas fa-calendar-times"></i> ';
                                    echo 'Belum Ditentukan';
                                    echo '<span class="pending-badge">Menunggu</span>';
                                    echo '</span>';
                                }
                                ?>
                            </td>
                            <?php if ($row['status'] == 'Rejected'): ?>
                                <td colspan="5" class="approved-status">
                                    <span class="status-badge status-rejected">
                                        <i class="fas fa-times-circle" style="color: #d32f2f;"></i>
                                        <?php
                                        if (!empty($row['rejected_by'])) {
                                            // Ambil nama user yang membatalkan
                                            $rejecter_query = "SELECT name, jabatan FROM users WHERE id = ?";
                                            $rejecter_stmt = $conn->prepare($rejecter_query);
                                            $rejecter_stmt->bind_param("i", $row['rejected_by']);
                                            $rejecter_stmt->execute();
                                            $rejecter_result = $rejecter_stmt->get_result();

                                            if ($rejecter_row = $rejecter_result->fetch_assoc()) {
                                                $rejecter_name = $rejecter_row['name'];
                                                $rejecter_position = $rejecter_row['jabatan'];
                                                echo "Training telah dibatalkan oleh " . htmlspecialchars($rejecter_name) . " (" . htmlspecialchars($rejecter_position) . ")";

                                                if (!empty($row['rejected_at'])) {
                                                    $rejected_date = new DateTime($row['rejected_at']);
                                                    echo " pada " . $rejected_date->format('d-m-Y H:i');
                                                }
                                            } else {
                                                echo "Training telah dibatalkan";
                                            }
                                            $rejecter_stmt->close();
                                        } else {
                                            echo "Training telah dibatalkan";
                                        }
                                        ?>
                                    </span>
                                </td>
                            <?php elseif ($row['status'] == 'Revise'): ?>
                                <td colspan="5" class="approved-status">
                                    <span class="status-badge status-revise">
                                        <i class="fas fa-edit" style="color: #e65100;"></i>
                                        Training Memerlukan Revisi
                                    </span>
                                </td>
                            <?php elseif ($row['approved_dept_head'] == 'Approved' &&
                                  $row['approved_hrd'] == 'Approved' &&
                                  $row['approved_ga'] == 'Approved' &&
                                  $row['approved_fm'] == 'Approved' &&
                                  $row['approved_dir'] == 'Approved'): ?>
                                <td colspan="5" class="approved-status">
                                    <span class="status-badge status-approved">
                                        <i class="fas fa-check-circle"></i> Training Telah Disetujui
                                    </span>
                                </td>
                            <?php else: ?>
                                <td>
                                    <span class="status-badge <?= getStatusClass($row['approved_dept_head']) ?>">
                                    <?php if ($row['approved_dept_head'] == 'Approved'): ?>
                                        <i class="fas fa-check-circle" style="color: #2e7d32;"></i>
                                    <?php else: ?>
                                        <i class="fas fa-clock" style="color: #f57c00;"></i>
                                    <?php endif; ?>
                                        <?= htmlspecialchars($row['approved_dept_head'] ?? 'Pending') ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge <?= getStatusClass($row['approved_hrd']) ?>">
                                    <?php if ($row['approved_hrd'] == 'Approved'): ?>
                                        <i class="fas fa-check-circle" style="color: #2e7d32;"></i>
                                    <?php else: ?>
                                        <i class="fas fa-clock" style="color: #f57c00;"></i>
                                    <?php endif; ?>
                                        <?= htmlspecialchars($row['approved_hrd'] ?? 'Pending') ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge <?= getStatusClass($row['approved_ga']) ?>">
                                    <?php if ($row['approved_ga'] == 'Approved'): ?>
                                        <i class="fas fa-check-circle" style="color: #2e7d32;"></i>
                                    <?php else: ?>
                                        <i class="fas fa-clock" style="color: #f57c00;"></i>
                                    <?php endif; ?>
                                        <?= htmlspecialchars($row['approved_ga'] ?? 'Pending') ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge <?= getStatusClass($row['approved_fm']) ?>">
                                    <?php if ($row['approved_fm'] == 'Approved'): ?>
                                        <i class="fas fa-check-circle" style="color: #2e7d32;"></i>
                                    <?php else: ?>
                                        <i class="fas fa-clock" style="color: #f57c00;"></i>
                                    <?php endif; ?>
                                        <?= htmlspecialchars($row['approved_fm'] ?? 'Pending') ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge <?= getStatusClass($row['approved_dir']) ?>">
                                    <?php if ($row['approved_dir'] == 'Approved'): ?>
                                        <i class="fas fa-check-circle" style="color: #2e7d32;"></i>
                                    <?php else: ?>
                                        <i class="fas fa-clock" style="color: #f57c00;"></i>
                                    <?php endif; ?>
                                        <?= htmlspecialchars($row['approved_dir'] ?? 'Pending') ?>
                                    </span>
                                </td>
                            <?php endif; ?>
                            <td style="width: 150px;">
                                <a href="detail_training.php?id=<?= $row['id'] ?>" class="action-button">
                                <i class="fas fa-info-circle"></i> Detail</a>
                                <?php if ($row['status'] == 'Revise'): ?>
                                    <a href="edit_training.php?id=<?= $row['id'] ?>" class="action-button" style="background-color: #28a745; margin-left: 5px; font-size: 12px; padding: 8px 12px;">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Mobile Card View -->
    <div class="mobile-card">
        <?php
        if (empty($training_data)): ?>
            <div class="empty-state">
                <i class="fas fa-clipboard-list"></i>
                <h3>Tidak Ada Pengajuan</h3>
                <p>Anda belum memiliki pengajuan training. Klik tombol di bawah untuk membuat pengajuan baru.</p>
            </div>
        <?php else:
            foreach ($training_data as $row): ?>
                <div class="mobile-submission-card">
                    <div class="mobile-card-header">
                        <?= htmlspecialchars($row['training_topic']) ?>
                    </div>
                    <div class="mobile-card-body">
                        <div class="mobile-card-row">
                            <div class="mobile-card-label">Tanggal:</div>
                            <div class="mobile-card-value">
                                <?php
                                if (!empty($row['start_date'])) {
                                    // Ada tanggal training
                                    if ($row['is_confirmed'] == 1) {
                                        // Tanggal sudah dikonfirmasi/fixed
                                        if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                                            // Multi-day training yang sudah dikonfirmasi
                                            echo '<span class="date-confirmed">';
                                            echo '<i class="fas fa-calendar-check"></i> ';
                                            echo htmlspecialchars($row['start_date']) . ' s/d ' . htmlspecialchars($row['end_date']);
                                            echo '<span class="confirmed-badge">Dikonfirmasi</span>';
                                            echo '</span>';
                                        } else {
                                            // Single day training yang sudah dikonfirmasi
                                            echo '<span class="date-confirmed">';
                                            echo '<i class="fas fa-calendar-check"></i> ';
                                            echo htmlspecialchars($row['start_date']);
                                            echo '<span class="confirmed-badge">Dikonfirmasi</span>';
                                            echo '</span>';
                                        }
                                    } else {
                                        // Tanggal belum dikonfirmasi (pending)
                                        if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                                            // Multi-day training yang masih pending
                                            echo '<span class="date-pending">';
                                            echo '<i class="fas fa-calendar-alt"></i> ';
                                            echo htmlspecialchars($row['start_date']) . ' s/d ' . htmlspecialchars($row['end_date']);
                                            echo '<span class="pending-badge">Pending</span>';
                                            echo '</span>';
                                        } else {
                                            // Single day training yang masih pending
                                            echo '<span class="date-pending">';
                                            echo '<i class="fas fa-calendar-alt"></i> ';
                                            echo htmlspecialchars($row['start_date']);
                                            echo '<span class="pending-badge">Pending</span>';
                                            echo '</span>';
                                        }
                                    }
                                } else {
                                    // Belum ada tanggal sama sekali
                                    echo '<span class="date-pending">';
                                    echo '<i class="fas fa-calendar-times"></i> ';
                                    echo 'Belum Ditentukan';
                                    echo '<span class="pending-badge">Menunggu</span>';
                                    echo '</span>';
                                }
                                ?>
                            </div>
                        </div>

                        <?php if ($row['status'] == 'Rejected'): ?>
                            <div class="mobile-card-row">
                                <div class="mobile-card-label">Status:</div>
                                <div class="mobile-card-value">
                                    <span class="status-badge status-rejected">
                                        <i class="fas fa-times-circle"></i>
                                        <?php
                                        if (!empty($row['rejected_by'])) {
                                            // Ambil nama user yang membatalkan
                                            $rejecter_query = "SELECT name, jabatan FROM users WHERE id = ?";
                                            $rejecter_stmt = $conn->prepare($rejecter_query);
                                            $rejecter_stmt->bind_param("i", $row['rejected_by']);
                                            $rejecter_stmt->execute();
                                            $rejecter_result = $rejecter_stmt->get_result();

                                            if ($rejecter_row = $rejecter_result->fetch_assoc()) {
                                                $rejecter_name = $rejecter_row['name'];
                                                $rejecter_position = $rejecter_row['jabatan'];
                                                echo "Training telah dibatalkan oleh " . htmlspecialchars($rejecter_name) . " (" . htmlspecialchars($rejecter_position) . ")";

                                                if (!empty($row['rejected_at'])) {
                                                    $rejected_date = new DateTime($row['rejected_at']);
                                                    echo " pada " . $rejected_date->format('d-m-Y H:i');
                                                }
                                            } else {
                                                echo "Training telah dibatalkan";
                                            }
                                            $rejecter_stmt->close();
                                        } else {
                                            echo "Training telah dibatalkan";
                                        }
                                        ?>
                                    </span>
                                </div>
                            </div>
                        <?php elseif ($row['status'] == 'Revise'): ?>
                            <div class="mobile-card-row">
                                <div class="mobile-card-label">Status:</div>
                                <div class="mobile-card-value">
                                    <span class="status-badge status-revise">
                                        <i class="fas fa-edit"></i> Training Perlu Direvisi
                                    </span>
                                </div>
                            </div>
                        <?php elseif ($row['approved_dept_head'] == 'Approved' &&
                              $row['approved_hrd'] == 'Approved' &&
                              $row['approved_ga'] == 'Approved' &&
                              $row['approved_fm'] == 'Approved' &&
                              $row['approved_dir'] == 'Approved'): ?>
                            <div class="mobile-card-row">
                                <div class="mobile-card-label">Status:</div>
                                <div class="mobile-card-value">
                                    <span class="status-badge status-approved">
                                        <i class="fas fa-check-circle"></i> Training Telah Disetujui
                                    </span>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="mobile-card-row">
                                <div class="mobile-card-label">Dept Head:</div>
                                <div class="mobile-card-value">
                                    <span class="status-badge <?= getStatusClass($row['approved_dept_head']) ?>">
                                        <?php if ($row['approved_dept_head'] == 'Approved'): ?>
                                            <i class="fas fa-check-circle"></i>
                                        <?php else: ?>
                                            <i class="fas fa-clock"></i>
                                        <?php endif; ?>
                                        <?= htmlspecialchars($row['approved_dept_head'] ?? 'Pending') ?>
                                    </span>
                                </div>
                            </div>
                            <div class="mobile-card-row">
                                <div class="mobile-card-label">LnD:</div>
                                <div class="mobile-card-value">
                                    <span class="status-badge <?= getStatusClass($row['approved_hrd']) ?>">
                                        <?php if ($row['approved_hrd'] == 'Approved'): ?>
                                            <i class="fas fa-check-circle"></i>
                                        <?php else: ?>
                                            <i class="fas fa-clock"></i>
                                        <?php endif; ?>
                                        <?= htmlspecialchars($row['approved_hrd'] ?? 'Pending') ?>
                                    </span>
                                </div>
                            </div>
                            <div class="mobile-card-row">
                                <div class="mobile-card-label">HRGA:</div>
                                <div class="mobile-card-value">
                                    <span class="status-badge <?= getStatusClass($row['approved_ga']) ?>">
                                        <?php if ($row['approved_ga'] == 'Approved'): ?>
                                            <i class="fas fa-check-circle"></i>
                                        <?php else: ?>
                                            <i class="fas fa-clock"></i>
                                        <?php endif; ?>
                                        <?= htmlspecialchars($row['approved_ga'] ?? 'Pending') ?>
                                    </span>
                                </div>
                            </div>
                            <div class="mobile-card-row">
                                <div class="mobile-card-label">Factory:</div>
                                <div class="mobile-card-value">
                                    <span class="status-badge <?= getStatusClass($row['approved_fm']) ?>">
                                        <?php if ($row['approved_fm'] == 'Approved'): ?>
                                            <i class="fas fa-check-circle"></i>
                                        <?php else: ?>
                                            <i class="fas fa-clock"></i>
                                        <?php endif; ?>
                                        <?= htmlspecialchars($row['approved_fm'] ?? 'Pending') ?>
                                    </span>
                                </div>
                            </div>
                            <div class="mobile-card-row">
                                <div class="mobile-card-label">Direktur:</div>
                                <div class="mobile-card-value">
                                    <span class="status-badge <?= getStatusClass($row['approved_dir']) ?>">
                                        <?php if ($row['approved_dir'] == 'Approved'): ?>
                                            <i class="fas fa-check-circle"></i>
                                        <?php else: ?>
                                            <i class="fas fa-clock"></i>
                                        <?php endif; ?>
                                        <?= htmlspecialchars($row['approved_dir'] ?? 'Pending') ?>
                                    </span>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="mobile-card-footer">
                        <a href="detail_training.php?id=<?= $row['id'] ?>" class="action-button">
                            <i class="fas fa-info-circle"></i> Detail
                        </a>
                        <?php if ($row['status'] == 'Revise'): ?>
                            <a href="edit_training.php?id=<?= $row['id'] ?>" class="action-button" style="background-color: #28a745; margin-left: 10px;">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach;
        endif; ?>
    </div>

    <div class="new-submission-button">
        <a href="form.php" class="action-button">
        <i class="fas fa-plus-circle"></i>  Ajukan Training Baru</a>
    </div>
    <button>
        <a style="color: white; text-decoration: none; margin:auto;" href="index.php">
            <i class="fas fa-arrow-circle-left"></i> Kembali ke Halaman Utama</a>
    </button>
</div>

<?php include '../config/footer.php'; ?>

<?php
function getStatusClass($status) {
    switch (strtolower($status)) {
        case 'approved':
            return 'status-approved';
        case 'rejected':
            return 'status-rejected';
        case 'revise':
            return 'status-revise';
        default:
            return 'status-pending';
    }
}
?>

<script>
    // Auto-hide notifications after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const notifications = document.querySelectorAll('.success-message, .error-message');

        notifications.forEach(function(notification) {
            // Set timeout to hide notification
            setTimeout(function() {
                notification.style.animation = 'slideOut 0.5s ease-in forwards';

                // Remove from DOM after animation completes
                setTimeout(function() {
                    notification.remove();
                }, 500);
            }, 5000); // 5 seconds
        });

        // Add click event to close buttons
        const closeButtons = document.querySelectorAll('.notification-close');
        closeButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const notification = this.closest('.success-message, .error-message');
                if (notification) {
                    notification.style.animation = 'slideOut 0.5s ease-in forwards';
                    setTimeout(function() {
                        notification.remove();
                    }, 500);
                }
            });
        });
    });
</script>

</body>
</html>

<?php
$stmt->close();
$conn->close();
?>
