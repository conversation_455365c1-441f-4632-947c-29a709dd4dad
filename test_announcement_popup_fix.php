<?php
/**
 * Test script untuk memverifikasi perbaikan popup announcement di semua dashboard
 */

echo "🔧 TESTING ANNOUNCEMENT POPUP FIXES\n";
echo "===================================\n\n";

$dashboard_files = [
    'pemohon/index.php',
    'LnD/index.php',
    'Dir/index.php',
    'dept_head/index.php'
];

echo "1️⃣ Checking modal HTML structure...\n";
echo "-----------------------------------\n";

foreach ($dashboard_files as $file) {
    echo "📄 Checking: $file\n";
    
    if (!file_exists($file)) {
        echo "   ❌ File not found\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Check for announcement modal
    $has_announcement_modal = strpos($content, 'id="announcementModal"') !== false;
    $has_modal_title = strpos($content, 'id="announcementModalTitle"') !== false;
    $has_modal_body = strpos($content, 'id="announcementModalBody"') !== false;
    $has_modal_close = strpos($content, 'id="announcementModalClose"') !== false;
    
    echo "   - Announcement Modal: " . ($has_announcement_modal ? "✅ Found" : "❌ Missing") . "\n";
    echo "   - Modal Title: " . ($has_modal_title ? "✅ Found" : "❌ Missing") . "\n";
    echo "   - Modal Body: " . ($has_modal_body ? "✅ Found" : "❌ Missing") . "\n";
    echo "   - Modal Close: " . ($has_modal_close ? "✅ Found" : "❌ Missing") . "\n";
    
    // Check for proper modal structure (no extra closing divs)
    $modal_start = strpos($content, '<div id="announcementModal"');
    if ($modal_start !== false) {
        $modal_section = substr($content, $modal_start, 1000); // Get 1000 chars after modal start
        $extra_closing_divs = substr_count($modal_section, '</div>') - 3; // Should have exactly 3 closing divs
        
        if ($extra_closing_divs <= 0) {
            echo "   - Modal Structure: ✅ Correct\n";
        } else {
            echo "   - Modal Structure: ❌ Extra closing divs detected\n";
        }
    }
    
    echo "\n";
}

echo "2️⃣ Checking CSS classes...\n";
echo "--------------------------\n";

foreach ($dashboard_files as $file) {
    echo "📄 Checking: $file\n";
    
    if (!file_exists($file)) {
        echo "   ❌ File not found\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Check for CSS classes
    $has_hover_class = strpos($content, '.announcement-img-hover') !== false;
    $has_modal_css = strpos($content, '.announcement-modal-content') !== false;
    $has_image_css = strpos($content, '.announcement-modal-image') !== false;
    
    echo "   - Hover CSS: " . ($has_hover_class ? "✅ Found" : "❌ Missing") . "\n";
    echo "   - Modal CSS: " . ($has_modal_css ? "✅ Found" : "❌ Missing") . "\n";
    echo "   - Image CSS: " . ($has_image_css ? "✅ Found" : "❌ Missing") . "\n";
    
    echo "\n";
}

echo "3️⃣ Checking JavaScript functions...\n";
echo "------------------------------------\n";

foreach ($dashboard_files as $file) {
    echo "📄 Checking: $file\n";
    
    if (!file_exists($file)) {
        echo "   ❌ File not found\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Check for JavaScript functions
    $has_init_function = strpos($content, 'function initializeAnnouncementPopup()') !== false;
    $has_show_function = strpos($content, 'function showAnnouncementModal(') !== false;
    $has_close_function = strpos($content, 'function closeAnnouncementModal()') !== false;
    $has_init_call = strpos($content, 'initializeAnnouncementPopup()') !== false;
    
    echo "   - Init Function: " . ($has_init_function ? "✅ Found" : "❌ Missing") . "\n";
    echo "   - Show Function: " . ($has_show_function ? "✅ Found" : "❌ Missing") . "\n";
    echo "   - Close Function: " . ($has_close_function ? "✅ Found" : "❌ Missing") . "\n";
    echo "   - Init Call: " . ($has_init_call ? "✅ Found" : "❌ Missing") . "\n";
    
    echo "\n";
}

echo "4️⃣ Checking image data attributes...\n";
echo "------------------------------------\n";

foreach ($dashboard_files as $file) {
    echo "📄 Checking: $file\n";
    
    if (!file_exists($file)) {
        echo "   ❌ File not found\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Check for data attributes
    $has_hover_class_img = strpos($content, 'class="img-fluid rounded announcement-img-hover"') !== false;
    $has_data_title = strpos($content, 'data-title=') !== false;
    $has_data_content = strpos($content, 'data-content=') !== false;
    $has_data_image = strpos($content, 'data-image=') !== false;
    
    echo "   - Hover Class on Image: " . ($has_hover_class_img ? "✅ Found" : "❌ Missing") . "\n";
    echo "   - Data Title: " . ($has_data_title ? "✅ Found" : "❌ Missing") . "\n";
    echo "   - Data Content: " . ($has_data_content ? "✅ Found" : "❌ Missing") . "\n";
    echo "   - Data Image: " . ($has_data_image ? "✅ Found" : "❌ Missing") . "\n";
    
    echo "\n";
}

echo "5️⃣ Checking for common issues...\n";
echo "--------------------------------\n";

foreach ($dashboard_files as $file) {
    echo "📄 Checking: $file\n";
    
    if (!file_exists($file)) {
        echo "   ❌ File not found\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Check for common issues
    $has_duplicate_modal = substr_count($content, 'id="announcementModal"') > 1;
    $has_missing_quotes = strpos($content, 'data-title=<?=') !== false; // Should have quotes
    $has_console_warn = strpos($content, 'console.warn(\'Announcement modal elements not found\')') !== false;
    
    echo "   - Duplicate Modal: " . ($has_duplicate_modal ? "❌ Found duplicates" : "✅ No duplicates") . "\n";
    echo "   - Missing Quotes: " . ($has_missing_quotes ? "❌ Found missing quotes" : "✅ Quotes OK") . "\n";
    echo "   - Error Handling: " . ($has_console_warn ? "✅ Has error handling" : "❌ No error handling") . "\n";
    
    echo "\n";
}

echo "📊 SUMMARY:\n";
echo "===========\n";

$all_files_ok = true;

foreach ($dashboard_files as $file) {
    if (!file_exists($file)) {
        echo "❌ $file - File not found\n";
        $all_files_ok = false;
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Check essential components
    $has_modal = strpos($content, 'id="announcementModal"') !== false;
    $has_css = strpos($content, '.announcement-img-hover') !== false;
    $has_js = strpos($content, 'function initializeAnnouncementPopup()') !== false;
    $has_init = strpos($content, 'initializeAnnouncementPopup()') !== false;
    $has_image_class = strpos($content, 'announcement-img-hover') !== false;
    
    $file_ok = $has_modal && $has_css && $has_js && $has_init && $has_image_class;
    
    echo ($file_ok ? "✅" : "❌") . " $file - " . ($file_ok ? "All components present" : "Missing components") . "\n";
    
    if (!$file_ok) {
        $all_files_ok = false;
        echo "   Missing: ";
        if (!$has_modal) echo "Modal ";
        if (!$has_css) echo "CSS ";
        if (!$has_js) echo "JavaScript ";
        if (!$has_init) echo "Initialization ";
        if (!$has_image_class) echo "Image-Class ";
        echo "\n";
    }
}

echo "\n🎯 OVERALL STATUS:\n";
echo "==================\n";

if ($all_files_ok) {
    echo "✅ ALL DASHBOARD FILES ARE READY!\n";
    echo "✅ Announcement popup should work on all dashboards\n";
    echo "✅ Modal structure is correct\n";
    echo "✅ CSS and JavaScript are properly implemented\n";
} else {
    echo "❌ SOME ISSUES FOUND!\n";
    echo "❌ Please check the files marked with ❌ above\n";
    echo "❌ Fix missing components before testing\n";
}

echo "\n🧪 TESTING INSTRUCTIONS:\n";
echo "========================\n";
echo "1. Create an announcement with image via admin/manage_announcements.php\n";
echo "2. Login as different user roles (pemohon, LnD, Dir, dept_head)\n";
echo "3. Go to their respective dashboard index.php\n";
echo "4. Look for announcements with images\n";
echo "5. Hover over image - should see scale effect and border\n";
echo "6. Click on image - should open popup modal with larger image\n";
echo "7. Test modal close with X button, ESC key, or click outside\n";

echo "\n🚀 ANNOUNCEMENT POPUP FIXES VERIFICATION COMPLETE!\n";
?>
