<?php
session_start();
include '../config/config.php';

// Cek apakah user adalah admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nik = $_POST['nik'];
    $nama = $_POST['nama'];
    $tgl_masuk = $_POST['tgl_masuk'];
    $jk = $_POST['jk'];
    $level_karyawan = $_POST['level_karyawan'];
    $tgl_lahir = $_POST['tgl_lahir'];
    $agama = $_POST['agama'];
    $pendidikan_akhir = $_POST['pendidikan_akhir'];
    $no_telp = $_POST['no_telp'];
    $dept = $_POST['dept'];
    $bagian = $_POST['bagian'];
    $jabatan = $_POST['jabatan'];
    $group = $_POST['group'];
    $status = $_POST['status'];
    $pt = $_POST['pt'];

    // Cek apakah NIK sudah ada
    $check_query = "SELECT nik FROM karyawan WHERE nik = ?";
    $stmt = $conn->prepare($check_query);
    $stmt->bind_param("s", $nik);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $error = "NIK sudah terdaftar!";
    } else {
        $query = "INSERT INTO karyawan (nik, nama, tgl_masuk, jk, level_karyawan, tgl_lahir,
                                      agama, pendidikan_akhir, no_telp, dept, bagian, jabatan,
                                      `group`, status, pt)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("sssssssssssssss",
            $nik, $nama, $tgl_masuk, $jk, $level_karyawan, $tgl_lahir,
            $agama, $pendidikan_akhir, $no_telp, $dept, $bagian, $jabatan,
            $group, $status, $pt);

        if ($stmt->execute()) {
            $success = "Data karyawan berhasil ditambahkan!";

            // Log aktivitas
            if (file_exists('../config/activity_logger.php')) {
                include_once '../config/activity_logger.php';
                if (function_exists('log_activity')) {
                    log_activity($_SESSION['user_id'], "Menambahkan karyawan baru: {$nama} (NIK: {$nik})", "employee", [
                        'nik' => $nik,
                        'nama' => $nama,
                        'dept' => $dept,
                        'jabatan' => $jabatan,
                        'status' => $status
                    ]);
                }
            }

            // Reset form
            $_POST = array();
        } else {
            $error = "Gagal menambahkan data karyawan: " . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
.create-employee-container {
    max-width: 1200px;
    margin: 30px auto;
    padding: 20px;
}

.page-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 20px;
    background: linear-gradient(135deg, #BF0000, #800000);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.page-header h1 {
    margin: 0;
    font-size: 2.5em;
    font-weight: 700;
}

.form-container {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.btn-container {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #BF0000;
    color: white;
    border: none;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: none;
}

.required-field::after {
    content: " *";
    color: #BF0000;
}
</style>

<body>
    <?php include '../config/navbar.php'; ?>
    <div class="container-form">
        <div class="create-employee-container">
            <div class="page-header">
                <h1>Tambah Karyawan Baru</h1>
                <p>Isi semua field yang diperlukan untuk menambahkan karyawan baru</p>
            </div>

            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?= htmlspecialchars($success) ?></div>
            <?php endif; ?>

            <div class="form-container">
                <form method="POST" onsubmit="return validateForm()">
                    <div class="form-section">
                        <h2>Informasi Pribadi</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="required-field">NIK</label>
                                <input type="text" class="form-control" name="nik" value="<?= htmlspecialchars($_POST['nik'] ?? '') ?>" required>
                            </div>
                            <div class="form-group">
                                <label class="required-field">Nama Lengkap</label>
                                <input type="text" class="form-control" name="nama" value="<?= htmlspecialchars($_POST['nama'] ?? '') ?>" required>
                            </div>
                            <div class="form-group">
                                <label class="required-field">Tanggal Masuk</label>
                                <input type="date" class="form-control" name="tgl_masuk" value="<?= htmlspecialchars($_POST['tgl_masuk'] ?? '') ?>" required>
                            </div>
                            <div class="form-group">
                                <label class="required-field">Jenis Kelamin</label>
                                <select class="form-control" name="jk" required>
                                    <option value="">Pilih Jenis Kelamin</option>
                                    <option value="L" <?= isset($_POST['jk']) && $_POST['jk'] == 'L' ? 'selected' : '' ?>>Laki-laki</option>
                                    <option value="P" <?= isset($_POST['jk']) && $_POST['jk'] == 'P' ? 'selected' : '' ?>>Perempuan</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Informasi Tambahan</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="required-field">Tanggal Lahir</label>
                                <input type="date" class="form-control" name="tgl_lahir" value="<?= htmlspecialchars($_POST['tgl_lahir'] ?? '') ?>" required>
                            </div>
                            <div class="form-group">
                                <label class="required-field">Agama</label>
                                <select class="form-control" name="agama" required>
                                    <?php
                                    $agama_query = "SELECT DISTINCT agama FROM karyawan";
                                    $agama_result = $conn->query($agama_query);
                                    while ($agama_row = $agama_result->fetch_assoc()) {
                                        $agama_value = $agama_row['agama'];
                                        echo "<option value='" . htmlspecialchars($agama_value) . "'>" . htmlspecialchars($agama_value) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="required-field">Pendidikan Terakhir</label>
                                <select class="form-control" name="pendidikan_akhir" required>
                                    <?php
                            $pendidikan_akhir_query = "SELECT DISTINCT pendidikan_akhir FROM karyawan";
                            $pendidikan_akhir_result = $conn->query($pendidikan_akhir_query);
                            while ($pendidikan_akhir_row = $pendidikan_akhir_result->fetch_assoc()) {
                                $pendidikan_akhir_value = $pendidikan_akhir_row['pendidikan_akhir'];
                                echo "<option value='" . htmlspecialchars($pendidikan_akhir_value) . "'>" . htmlspecialchars($pendidikan_akhir_value) . "</option>";
                            }
                            ?>
                    </div>
                    <div class="form-group">
                        </select>
                        <label class="required-field">No. Telepon</label>
                        <input type="tel" class="form-control" name="no_telp" value="<?= htmlspecialchars($_POST['no_telp'] ?? '') ?>" required>
                    </div>
                </div>
            </div>


                    <div class="form-section">
                        <h2>Informasi Pekerjaan</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="required-field">Level Karyawan</label>
                                <select class="form-control" name="level_karyawan" required>
                                    <?php
                                    $level_karyawan_query = "SELECT DISTINCT level_karyawan FROM karyawan";
                                    $level_karyawan_result = $conn->query($level_karyawan_query);
                                    while ($level_karyawan_row = $level_karyawan_result->fetch_assoc()) {
                                        $level_karyawan_value = $level_karyawan_row['level_karyawan'];
                                        echo "<option value='" . htmlspecialchars($level_karyawan_value) . "'>" . htmlspecialchars($level_karyawan_value) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="required-field">Departemen</label>
                                <select class="form-control" name="dept" id="dept" required onchange="updateBagian()"></select>
                            </div>
                        </div>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="required-field">Bagian</label>
                                <select class="form-control" name="bagian" id="bagian" required onchange="updateJabatan()"></select>
                            </div>
                            <div class="form-group">
                                <label class="required-field">Jabatan</label>
                                <select class="form-control" name="jabatan" id="jabatan" required></select>
                            </div>
                            <div class="form-group">
                                <label class="required-field">Group</label>
                                <input type="text" class="form-control" name="group" value="<?= htmlspecialchars($_POST['group'] ?? '') ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Status & Perusahaan</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="required-field">Status</label>
                                <select class="form-control" name="status" required>
                                    <?php
                                    $status_query = "SELECT DISTINCT status FROM karyawan";
                                    $status_result = $conn->query($status_query);
                                    while ($status_row = $status_result->fetch_assoc()) {
                                        $status_value = $status_row['status'];
                                        echo "<option value='" . htmlspecialchars($status_value) . "'>" . htmlspecialchars($status_value) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="required-field">Perusahan/PT</label>
                                <select class="form-control" name="pt" required>
                                    <?php
                                    $pt_query = "SELECT DISTINCT pt FROM karyawan";
                                    $pt_result = $conn->query($pt_query);
                                    while ($pt_row = $pt_result->fetch_assoc()) {
                                        $pt_values = $pt_row['pt'];
                                        echo "<option value='" . htmlspecialchars($pt_values) . "'>" . htmlspecialchars($pt_values) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>


                    <div class="btn-container">
                        <button type="submit" class="btn btn-primary">Tambahkan</button>
                    </div>
                </form>

                <div class="btn-container">
                        <a href="employee_management.php" class="btn"  >Kembali</a>
                    </div>
            </div>
        </div>
    </div>
    <?php
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($success)) {
        echo "<script>window.location.href = 'employee_management.php';</script>";
    }
    ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php include '../config/footer.php'; ?>
    <script src="js/employee_form.js"></script>
    <script>
    function validateForm() {

        // Validasi nomor telepon (hanya angka dan -)
        const noTelp = document.getElementsByName('no_telp')[0].value;
        if (!/^[\d-]+$/.test(noTelp)) {
            alert('Nomor telepon hanya boleh berisi angka dan tanda hubung (-)!');
            return false;
        }


        // Validasi tanggal lahir
        const tglLahir = new Date(document.getElementsByName('tgl_lahir')[0].value);
        const minAge = new Date();
        minAge.setFullYear(minAge.getFullYear() - 15); // Minimal usia 15 tahun
        if (tglLahir > minAge) {
            alert('Karyawan harus berusia minimal 15 tahun!');
            return false;
        }

        return true;
    }
    </script>
</body>
</html>

