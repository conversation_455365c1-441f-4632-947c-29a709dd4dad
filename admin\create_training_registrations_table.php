<?php
// File: admin/create_training_registrations_table.php
// Script untuk membuat tabel pendaftaran training

include '../config/config.php';

// Create training registrations table
$create_table_query = "
Cx  xREATE TABLE IF NOT EXISTS `training_registrations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `training_id` int NOT NULL,
  `training_type` enum('internal','external') NOT NULL DEFAULT 'internal',
  `participant_id` int NOT NULL COMMENT 'ID karyawan yang didaftarkan',
  `participant_nik` varchar(20) NOT NULL,
  `participant_nama` varchar(100) NOT NULL,
  `participant_dept` varchar(50) DEFAULT NULL,
  `participant_jabatan` varchar(50) DEFAULT NULL,
  `registered_by` int NOT NULL COMMENT 'ID user yang mendaftarkan',
  `registered_by_name` varchar(100) NOT NULL,
  `registered_by_dept` varchar(50) DEFAULT NULL,
  `registration_type` enum('self','supervisor') NOT NULL DEFAULT 'self',
  `status` enum('pending','approved','rejected','cancelled') NOT NULL DEFAULT 'pending',
  `admin_notes` text DEFAULT NULL,
  `approved_by` int DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_registration` (`training_id`,`training_type`,`participant_id`),
  KEY `idx_training_id` (`training_id`),
  KEY `idx_participant_id` (`participant_id`),
  KEY `idx_registered_by` (`registered_by`),
  KEY `idx_status` (`status`),
  KEY `idx_training_type` (`training_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
";

if ($conn->query($create_table_query) === TRUE) {
    echo "✅ Tabel training_registrations berhasil dibuat atau sudah ada.<br>";
} else {
    echo "❌ Error membuat tabel: " . $conn->error . "<br>";
}

// Create notification table for admin";

if ($conn->query($create_notification_query) === TRUE) {
    echo "✅ Tabel training_registration_notifications berhasil dibuat atau sudah ada.<br>";
} else {
    echo "❌ Error membuat tabel notifikasi: " . $conn->error . "<br>";
}

echo "<br>🎉 Setup database untuk sistem pendaftaran training selesai!";
echo "<br><a href='../pemohon/index.php'>← Kembali ke Dashboard</a>";

$conn->close();
?>
