<?php
/**
 * Simple script untuk membuat notifikasi demo
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/../includes/notification_helper.php';

// Cari user pertama yang aktif
$user_query = "SELECT id, name FROM users WHERE is_active = 1 LIMIT 1";
$user_result = $conn->query($user_query);

if ($user_result && $user_result->num_rows > 0) {
    $user = $user_result->fetch_assoc();
    
    echo "Creating demo notification for: {$user['name']}\n";
    
    // Buat notifikasi demo
    $created = createNotification(
        $user['id'],
        null,
        '🎓 Demo: Training Baru Tersedia',
        'Ini adalah notifikasi demo. Training "Digital Marketing" telah dibuka untuk pendaftaran!',
        'info'
    );
    
    if ($created) {
        echo "✅ Demo notification created successfully!\n";
        echo "👆 Login sebagai '{$user['name']}' dan lihat icon bell di header\n";
        echo "🔔 Seharusnya ada badge merah dengan angka 1\n";
    } else {
        echo "❌ Failed to create demo notification\n";
    }
} else {
    echo "❌ No active users found\n";
}

$conn->close();
?>
