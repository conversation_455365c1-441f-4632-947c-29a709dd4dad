<?php
/**
 * Fungsi-fungsi untuk normalisasi dan validasi NIK
 */

/**
 * Normalisasi NIK dengan menghapus semua karakter non-angka
 * Fungsi ini hanya digunakan untuk pencarian dan perbandingan NIK,
 * bukan untuk mengubah format asli NIK yang disimpan di database
 *
 * @param string $nik NIK yang akan dinormalisasi
 * @return string NIK yang sudah dinormalisasi (hanya angka)
 */
function normalizeNIK($nik) {
    // Jika NIK dalam format notasi ilmiah (contoh: 1.23457E+15), konversi ke string
    if (strpos($nik, 'E+') !== false || strpos($nik, 'e+') !== false) {
        $nik = sprintf('%.0f', floatval($nik));
    }

    // Hapus semua karakter non-angka
    return preg_replace('/[^0-9]/', '', $nik);
}

/**
 * Format NIK dengan mempertahankan format asli
 * Fungsi ini digunakan untuk mempertahankan format asli NIK saat disimpan ke database
 *
 * @param string $nik NIK yang akan diformat
 * @return string NIK yang sudah diformat (dengan mempertahankan format asli)
 */
function formatNIK($nik) {
    // Kembalikan NIK dengan format asli (termasuk tanda strip jika ada)
    return $nik;
}

/**
 * Cek apakah NIK sudah ada di database, baik dalam format asli maupun format yang dinormalisasi
 *
 * @param mysqli $conn Koneksi database
 * @param string $nik NIK yang akan dicek
 * @return array|false Array dengan data karyawan jika ditemukan, false jika tidak ditemukan
 */
function checkNIKExists($conn, $nik) {
    // Normalisasi NIK
    $normalized_nik = normalizeNIK($nik);

    // Cek apakah kolom normalized_nik sudah ada
    $check_column_query = "SHOW COLUMNS FROM karyawan LIKE 'normalized_nik'";
    $check_column_result = $conn->query($check_column_query);

    if ($check_column_result->num_rows > 0) {
        // Gunakan kolom normalized_nik untuk pencarian
        $query = "SELECT * FROM karyawan WHERE normalized_nik = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $normalized_nik);
    } else {
        // Fallback ke metode lama jika kolom normalized_nik belum ada
        $query = "SELECT * FROM karyawan WHERE REPLACE(REPLACE(REPLACE(nik, '-', ''), ' ', ''), '.', '') = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $normalized_nik);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}

/**
 * Cek kemungkinan duplikasi karyawan berdasarkan nama dan data lainnya
 *
 * @param mysqli $conn Koneksi database
 * @param array $employee_data Data karyawan yang akan dicek
 * @return array Array dengan data karyawan yang kemungkinan duplikat
 */
function checkPossibleDuplicates($conn, $employee_data) {
    $possible_duplicates = [];

    // Ambil data yang diperlukan
    $nama = $employee_data['nama'] ?? '';
    $dept = $employee_data['dept'] ?? '';
    $no_telp = $employee_data['no_telp'] ?? '';

    if (empty($nama)) {
        return $possible_duplicates;
    }

    // Query untuk mencari kemungkinan duplikat berdasarkan nama
    $query = "SELECT * FROM karyawan WHERE nama = ?";

    // Buat array parameter
    $types = "s"; // Tipe parameter pertama (string untuk nama)
    $params = [$nama]; // Parameter pertama (nama)

    // Tambahkan filter departemen jika ada
    if (!empty($dept)) {
        $query .= " AND dept = ?";
        $types .= "s"; // Tambahkan tipe string
        $params[] = $dept; // Tambahkan parameter dept
    }

    // Tambahkan filter nomor telepon jika ada
    if (!empty($no_telp)) {
        $query .= " AND no_telp = ?";
        $types .= "s"; // Tambahkan tipe string
        $params[] = $no_telp; // Tambahkan parameter no_telp
    }

    $stmt = $conn->prepare($query);

    if ($stmt) {
        // Buat array parameter dengan referensi
        $bind_params = [$types]; // Parameter pertama adalah tipe data

        // Tambahkan parameter lainnya sebagai referensi
        for ($i = 0; $i < count($params); $i++) {
            $bind_params[] = &$params[$i];
        }

        // Bind parameter
        call_user_func_array([$stmt, 'bind_param'], $bind_params);

        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $possible_duplicates[] = $row;
        }
    }

    return $possible_duplicates;
}

/**
 * Cek apakah dua karyawan kemungkinan sama berdasarkan kesamaan data
 *
 * @param array $employee1 Data karyawan pertama
 * @param array $employee2 Data karyawan kedua
 * @return bool True jika kemungkinan sama, false jika tidak
 */
function isPossibleSamePerson($employee1, $employee2) {
    // Jika NIK yang dinormalisasi sama, kemungkinan orang yang sama
    $nik1 = normalizeNIK($employee1['nik'] ?? '');
    $nik2 = normalizeNIK($employee2['nik'] ?? '');

    if (!empty($nik1) && !empty($nik2) && $nik1 === $nik2) {
        return true;
    }

    // Hitung skor kesamaan
    $similarity_score = 0;
    $total_fields = 0;

    // Cek kesamaan nama (bobot tinggi)
    if (!empty($employee1['nama']) && !empty($employee2['nama'])) {
        $total_fields += 3;
        if (strtolower(trim($employee1['nama'])) === strtolower(trim($employee2['nama']))) {
            $similarity_score += 3;
        }
    }

    // Cek kesamaan departemen
    if (!empty($employee1['dept']) && !empty($employee2['dept'])) {
        $total_fields++;
        if (strtolower(trim($employee1['dept'])) === strtolower(trim($employee2['dept']))) {
            $similarity_score++;
        }
    }

    // Cek kesamaan bagian
    if (!empty($employee1['bagian']) && !empty($employee2['bagian'])) {
        $total_fields++;
        if (strtolower(trim($employee1['bagian'])) === strtolower(trim($employee2['bagian']))) {
            $similarity_score++;
        }
    }

    // Cek kesamaan jabatan
    if (!empty($employee1['jabatan']) && !empty($employee2['jabatan'])) {
        $total_fields++;
        if (strtolower(trim($employee1['jabatan'])) === strtolower(trim($employee2['jabatan']))) {
            $similarity_score++;
        }
    }

    // Cek kesamaan nomor telepon (bobot tinggi)
    if (!empty($employee1['no_telp']) && !empty($employee2['no_telp'])) {
        $total_fields += 2;
        $phone1 = preg_replace('/[^0-9]/', '', $employee1['no_telp']);
        $phone2 = preg_replace('/[^0-9]/', '', $employee2['no_telp']);
        if ($phone1 === $phone2) {
            $similarity_score += 2;
        }
    }

    // Cek kesamaan tanggal lahir (bobot tinggi)
    if (!empty($employee1['tgl_lahir']) && !empty($employee2['tgl_lahir'])) {
        $total_fields += 2;
        if ($employee1['tgl_lahir'] === $employee2['tgl_lahir']) {
            $similarity_score += 2;
        }
    }

    // Hitung persentase kesamaan
    if ($total_fields > 0) {
        $similarity_percentage = ($similarity_score / $total_fields) * 100;

        // Jika kesamaan lebih dari 70%, kemungkinan orang yang sama
        return $similarity_percentage >= 70;
    }

    return false;
}
