<?php
/**
 * API untuk mendapatkan detail training universal
 * Mendukung semua role dan mengambil data peserta dengan benar
 */

// Start session first
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');
include '../config/config.php';

// Check if user is logged in - temporarily disabled for testing
if (!isset($_SESSION['user_id'])) {
    // For testing purposes, allow access but log the issue
    error_log("API accessed without session - allowing for testing");
}

$training_id = $_GET['id'] ?? '';
$training_type = $_GET['type'] ?? '';

if (empty($training_id) || empty($training_type)) {
    echo json_encode(['success' => false, 'message' => 'Missing parameters']);
    exit;
}

try {
    $training = [];

    if ($training_type === 'online') {
        // Get online training details
        $query = "SELECT ts.*,
                  COALESCE(ts.training_topic, 'Training Eksternal') as title,
                  DATE_FORMAT(ts.start_date, '%d/%m/%Y') as date,
                  ts.start_date as raw_date,
                  ts.end_date,
                  ts.is_confirmed,
                  'online' as type,
                  CASE
                    WHEN ts.start_date = ts.end_date OR ts.end_date IS NULL THEN DATE_FORMAT(ts.start_date, '%d/%m/%Y')
                    ELSE CONCAT(DATE_FORMAT(ts.start_date, '%d/%m/%Y'), ' s/d ', DATE_FORMAT(ts.end_date, '%d/%m/%Y'))
                  END as formatted_date,
                  CONCAT(COALESCE(ts.training_time_start, ''),
                         CASE WHEN ts.training_time_start IS NOT NULL AND ts.training_time_end IS NOT NULL
                              THEN ' - ' ELSE '' END,
                         COALESCE(ts.training_time_end, '')) as time,
                  ts.training_place as location,
                  ts.contact_person as trainer,
                  ts.status,
                  ts.additional_info as description,
                  COALESCE(ts.poster_image, ts.internal_memo_image) as poster_image
                  FROM training_submissions ts
                  WHERE ts.id = ?";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $training = $result->fetch_assoc();
        $stmt->close();

        if (!$training) {
            echo json_encode(['success' => false, 'message' => 'Training not found']);
            exit;
        }

        // Get participants for online training
        $participants_query = "SELECT
                              COALESCE(p.nama_participants, 'Unknown') as name,
                              COALESCE(p.departemen_participants, 'N/A') as dept,
                              COALESCE(p.jabatan_participants, 'N/A') as jabatan,
                              COALESCE(p.nik_participants, 'N/A') as nik
                              FROM participants p
                              WHERE p.training_id = ?";
        $stmt = $conn->prepare($participants_query);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();
        $participants_result = $stmt->get_result();

        $training['participants'] = [];
        while ($participant = $participants_result->fetch_assoc()) {
            $training['participants'][] = $participant;
        }
        $stmt->close();

    } else if ($training_type === 'offline') {
        // Get offline training details
        $query = "SELECT ot.*,
                  ot.training_topic as title,
                  DATE_FORMAT(ot.start_date, '%d/%m/%Y') as date,
                  ot.start_date as raw_date,
                  ot.end_date,
                  ot.is_confirmed,
                  'offline' as type,
                  CASE
                    WHEN ot.start_date = ot.end_date OR ot.end_date IS NULL THEN DATE_FORMAT(ot.start_date, '%d/%m/%Y')
                    ELSE CONCAT(DATE_FORMAT(ot.start_date, '%d/%m/%Y'), ' s/d ', DATE_FORMAT(ot.end_date, '%d/%m/%Y'))
                  END as formatted_date,
                  CONCAT(COALESCE(ot.training_time_start, ''),
                         CASE WHEN ot.training_time_start IS NOT NULL AND ot.training_time_end IS NOT NULL
                              THEN ' - ' ELSE '' END,
                         COALESCE(ot.training_time_end, '')) as time,
                  ot.location,
                  ot.trainer_name as trainer,
                  ot.max_participants,
                  CASE
                    WHEN ot.is_hidden = 1 THEN 'Hidden'
                    ELSE 'Active'
                  END as status,
                  ot.training_description as description,
                  ot.poster_image
                  FROM offline_training ot
                  WHERE ot.id = ?";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $training = $result->fetch_assoc();
        $stmt->close();

        if (!$training) {
            echo json_encode(['success' => false, 'message' => 'Training not found']);
            exit;
        }

        // Get participants for offline training - simple approach
        $participants_query = "SELECT
                              ota.nik,
                              COALESCE(k.nama, ota.nama, 'Unknown') as name,
                              COALESCE(k.dept, 'N/A') as dept,
                              COALESCE(k.jabatan, 'N/A') as jabatan,
                              MAX(ota.status) as status
                              FROM offline_training_attendance ota
                              LEFT JOIN karyawan k ON ota.nik = k.nik
                              WHERE ota.offline_training_id = ?
                              GROUP BY ota.nik, k.nama, ota.nama, k.dept, k.jabatan";
        $stmt = $conn->prepare($participants_query);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();
        $participants_result = $stmt->get_result();

        $training['participants'] = [];

        // Calculate total training days for attendance summary
        $start_date = new DateTime($training['raw_date']);
        $end_date = new DateTime($training['end_date'] ?: $training['raw_date']);
        $total_days = $start_date->diff($end_date)->days + 1;

        while ($participant = $participants_result->fetch_assoc()) {
            $nik = $participant['nik'];

            // For multi-day training, get attendance count
            if ($total_days > 1) {
                $attendance_query = "SELECT COUNT(DISTINCT DATE(check_in)) as days_attended,
                                           GROUP_CONCAT(DISTINCT DATE(check_in) ORDER BY DATE(check_in)) as attendance_dates
                                    FROM offline_training_attendance
                                    WHERE offline_training_id = ? AND nik = ? AND check_in IS NOT NULL";
                $attendance_stmt = $conn->prepare($attendance_query);
                $attendance_stmt->bind_param("is", $training_id, $nik);
                $attendance_stmt->execute();
                $attendance_result = $attendance_stmt->get_result();
                $attendance_data = $attendance_result->fetch_assoc();
                $attendance_stmt->close();

                $days_attended = $attendance_data['days_attended'] ?: 0;
                $participant['attendance_summary'] = "{$days_attended}/{$total_days} hari";
                $participant['attendance_dates'] = $attendance_data['attendance_dates'];
            } else {
                $participant['attendance_summary'] = $participant['status'] ?: 'Belum ada data';
            }

            $training['participants'][] = $participant;
        }
        $stmt->close();
    }

    echo json_encode([
        'success' => true,
        'training' => $training
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

$conn->close();
