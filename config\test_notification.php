<?php
/**
 * Script untuk test sistem notifikasi email
 */

// Disable session untuk testing
define('NO_SESSION', true);

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/notification_helper.php';

echo "🧪 Testing Notification System\n";
echo "==============================\n\n";

// Test 1: Cek konfigurasi email
echo "1. Checking email configuration...\n";
$settings_query = "SELECT smtp_server, smtp_port, sender_email, sender_name, smtp_encryption FROM settings WHERE id = 1";
$settings_result = $conn->query($settings_query);

if ($settings_result && $settings_result->num_rows > 0) {
    $settings = $settings_result->fetch_assoc();
    echo "   ✅ Email settings found\n";
    echo "   📧 SMTP: {$settings['smtp_server']}:{$settings['smtp_port']}\n";
    echo "   👤 From: {$settings['sender_name']} <{$settings['sender_email']}>\n";
    echo "   🔒 Encryption: {$settings['smtp_encryption']}\n\n";
} else {
    echo "   ❌ No email settings found\n";
    exit(1);
}

// Test 2: Cek training data untuk test
echo "2. Finding test training data...\n";
$training_query = "SELECT ts.*, u.name as requester_name, u.email as requester_email 
                   FROM training_submissions ts 
                   LEFT JOIN users u ON ts.user_id = u.id 
                   WHERE ts.id IS NOT NULL 
                   ORDER BY ts.id DESC 
                   LIMIT 1";
$training_result = $conn->query($training_query);

if ($training_result && $training_result->num_rows > 0) {
    $training = $training_result->fetch_assoc();
    echo "   ✅ Found training ID: {$training['id']}\n";
    echo "   📚 Topic: {$training['training_topic']}\n";
    echo "   👤 Requester: {$training['requester_name']} ({$training['requester_email']})\n\n";
} else {
    echo "   ❌ No training data found for testing\n";
    exit(1);
}

// Test 3: Cek approver data
echo "3. Finding test approver...\n";
$approver_query = "SELECT id, name, email FROM users WHERE role_id = 2 AND email IS NOT NULL AND email != '' LIMIT 1";
$approver_result = $conn->query($approver_query);

if ($approver_result && $approver_result->num_rows > 0) {
    $approver = $approver_result->fetch_assoc();
    echo "   ✅ Found approver: {$approver['name']} ({$approver['email']})\n\n";
} else {
    echo "   ❌ No approver with email found\n";
    exit(1);
}

// Test 4: Test notification function
echo "4. Testing notification function...\n";
echo "   📤 Attempting to send test notification...\n";

try {
    $result = send_status_update_notification(
        $training['id'],           // training_id
        'Pending',                 // status
        'Test notification',       // comments
        '',                        // assignment
        2,                         // next_approver_role_id
        $conn,                     // database connection
        $approver['id']           // specific approver
    );
    
    if ($result['success']) {
        echo "   ✅ Notification function executed successfully\n";
        echo "   📧 Message: {$result['message']}\n";
        
        if (isset($result['errors']) && !empty($result['errors'])) {
            echo "   ⚠️  Errors encountered:\n";
            foreach ($result['errors'] as $error) {
                echo "      - $error\n";
            }
        }
    } else {
        echo "   ❌ Notification function failed\n";
        echo "   💥 Error: {$result['message']}\n";
        
        if (isset($result['errors']) && !empty($result['errors'])) {
            echo "   📝 Error details:\n";
            foreach ($result['errors'] as $error) {
                echo "      - $error\n";
            }
        }
    }
} catch (Exception $e) {
    echo "   ❌ Exception occurred: " . $e->getMessage() . "\n";
}

echo "\n🏁 Test completed!\n";
echo "📋 Check the error logs for detailed SMTP communication\n";

$conn->close();
?>
