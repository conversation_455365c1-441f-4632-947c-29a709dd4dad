<?php
/**
 * Quiz Result Page for Pemohon (Applicant)
 * This page displays the results of a quiz attempt
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';

// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['full_name'] ?? 'Pengguna';

// Check if attempt ID is provided
$attempt_id = isset($_GET['attempt']) ? intval($_GET['attempt']) : 0;

if ($attempt_id <= 0) {
    header('Location: classroom.php');
    exit();
}

// Get attempt information
$attempt_query = "SELECT a.*, q.title as quiz_title, q.passing_score, q.show_correct_answers, q.hide_all_answers, q.hide_correct_answers,
                 c.id as class_id, c.title as class_title, t.training_topic
                 FROM training_quiz_attempts a
                 JOIN training_quizzes q ON a.quiz_id = q.id
                 JOIN training_classes c ON q.class_id = c.id
                 JOIN training_submissions t ON c.training_id = t.id
                 WHERE a.id = ? AND a.user_id = ?";
$stmt = $conn->prepare($attempt_query);
$stmt->bind_param("ii", $attempt_id, $user_id);
$stmt->execute();
$result = $stmt->get_result();
$attempt = $result->fetch_assoc();
$stmt->close();

if (!$attempt) {
    header('Location: classroom.php');
    exit();
}

// Get answers for this attempt
$answers_query = "SELECT a.*, q.question_text, q.question_type, q.points
                 FROM training_quiz_answers a
                 JOIN training_questions q ON a.question_id = q.id
                 WHERE a.attempt_id = ?
                 ORDER BY q.order_number ASC";
$stmt = $conn->prepare($answers_query);
$stmt->bind_param("i", $attempt_id);
$stmt->execute();
$answers_result = $stmt->get_result();
$answers = [];
while ($row = $answers_result->fetch_assoc()) {
    // For multiple choice questions, get selected option
    if ($row['question_type'] == 'multiple_choice' || $row['question_type'] == 'true_false') {
        $option_query = "SELECT * FROM training_question_options WHERE id = ?";
        $option_stmt = $conn->prepare($option_query);
        $option_stmt->bind_param("i", $row['selected_option_id']);
        $option_stmt->execute();
        $option_result = $option_stmt->get_result();
        $row['selected_option'] = $option_result->fetch_assoc();
        $option_stmt->close();

        // If show correct answers is enabled, get correct option
        if ($attempt['show_correct_answers']) {
            // Check if we should hide correct answers for questions the user has already answered
            $showCorrectOption = true;

            // Only hide correct answers for correct responses if hide_correct_answers is enabled
            if (isset($attempt['hide_correct_answers']) && $attempt['hide_correct_answers'] && isset($row['is_correct']) && $row['is_correct']) {
                $showCorrectOption = false;
            }

            if ($showCorrectOption) {
                $correct_option_query = "SELECT * FROM training_question_options WHERE question_id = ? AND is_correct = 1";
                $correct_option_stmt = $conn->prepare($correct_option_query);
                $correct_option_stmt->bind_param("i", $row['question_id']);
                $correct_option_stmt->execute();
                $correct_option_result = $correct_option_stmt->get_result();
                $row['correct_option'] = $correct_option_result->fetch_assoc();
                $correct_option_stmt->close();
            }
        }
    }

    $answers[] = $row;
}
$stmt->close();

// Calculate statistics
$total_questions = count($answers);
$correct_answers = 0;
$total_points = 0;
$earned_points = 0;

foreach ($answers as $answer) {
    $total_points += $answer['points'];
    if (isset($answer['is_correct']) && $answer['is_correct']) {
        $correct_answers++;
    }
    if (isset($answer['points_earned'])) {
        $earned_points += $answer['points_earned'];
    }
}

$score_percentage = ($total_points > 0) ? round(($earned_points / $total_points) * 100) : 0;
$passed = ($attempt['passing_score'] && $score_percentage >= $attempt['passing_score']);
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .result-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .result-header {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .result-title {
        font-size: 1.8rem;
        margin-bottom: 10px;
        color: #333;
    }

    .result-meta {
        color: #6c757d;
        margin-bottom: 15px;
    }

    .score-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }

    .score-value {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .score-high {
        color: #28a745;
    }

    .score-medium {
        color: #ffc107;
    }

    .score-low {
        color: #dc3545;
    }

    .score-label {
        font-size: 1.2rem;
        color: #6c757d;
        margin-bottom: 15px;
    }

    .score-details {
        display: flex;
        justify-content: space-around;
        margin-top: 20px;
    }

    .score-detail {
        text-align: center;
    }

    .detail-value {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .detail-label {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .answer-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .answer-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
    }

    .question-number {
        font-size: 0.9rem;
        color: #6c757d;
        flex-grow: 1; /* Ensure question number takes full width when points are hidden */
    }

    .answer-points {
        font-weight: 600;
    }

    .points-earned {
        color: #28a745;
    }

    .points-lost {
        color: #dc3545;
    }

    .question-text {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 15px;
    }

    .answer-content {
        margin-bottom: 15px;
    }

    .selected-answer {
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 10px;
    }

    .correct-answer {
        background-color: rgba(40, 167, 69, 0.1);
        border: 1px solid rgba(40, 167, 69, 0.2);
    }

    .incorrect-answer {
        background-color: rgba(220, 53, 69, 0.1);
        border: 1px solid rgba(220, 53, 69, 0.2);
    }

    .neutral-answer {
        background-color: rgba(108, 117, 125, 0.1);
        border: 1px solid rgba(108, 117, 125, 0.2);
    }

    .correct-option {
        padding: 10px;
        border-radius: 8px;
        background-color: rgba(40, 167, 69, 0.1);
        border: 1px solid rgba(40, 167, 69, 0.2);
    }

    .feedback {
        font-style: italic;
        color: #6c757d;
        margin-top: 10px;
    }
    @media (max-width: 768px) {
    .score-details {
        flex-direction: column;
        gap: 10px;
    }

    .score-detail {
        width: 100%;
    }

    .answer-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .result-header,
    .score-card,
    .answer-card {
        padding: 15px;
    }

    .score-value {
        font-size: 2.5rem;
    }

    .question-text {
        font-size: 1rem;
    }

    .selected-answer,
    .correct-option {
        font-size: 0.95rem;
    }

    .result-meta > div {
        font-size: 0.95rem;
    }
}

</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="result-container">
    <div class="result-header">
        <h1 class="result-title">Hasil Kuis: <?= htmlspecialchars($attempt['quiz_title']) ?></h1>
        <div class="result-meta">
            <div><strong>Kelas:</strong> <?= htmlspecialchars($attempt['class_title']) ?></div>
            <div><strong>Topik:</strong> <?= htmlspecialchars($attempt['training_topic']) ?></div>
            <div><strong>Waktu Pengerjaan:</strong> <?= date('d M Y H:i', strtotime($attempt['start_time'])) ?></div>
            <div><strong>Waktu Selesai:</strong> <?= date('d M Y H:i', strtotime($attempt['end_time'])) ?></div>
            <div><strong>Durasi:</strong>
                <?php
                    $start = new DateTime($attempt['start_time']);
                    $end = new DateTime($attempt['end_time']);
                    $interval = $start->diff($end);

                    if ($interval->h > 0) {
                        echo $interval->format('%h jam %i menit %s detik');
                    } else {
                        echo $interval->format('%i menit %s detik');
                    }
                ?>
            </div>
        </div>
    </div>

    <div class="score-card">
        <div class="score-value <?= $score_percentage >= 80 ? 'score-high' : ($score_percentage >= 60 ? 'score-medium' : 'score-low') ?>">
            <?= $score_percentage ?>%
        </div>
        <div class="progress my-3" style="height: 25px; background-color: #e9ecef; border-radius: 20px; overflow: hidden;">
    <div class="progress-bar <?= $score_percentage >= 80 ? 'bg-success' : ($score_percentage >= 60 ? 'bg-warning' : 'bg-danger') ?>"
        role="progressbar"
        style="width: <?= $score_percentage ?>%; transition: width 0.6s ease;"
        aria-valuenow="<?= $score_percentage ?>" aria-valuemin="0" aria-valuemax="100">
        <?= $score_percentage ?>%
    </div>
</div>

        <div class="score-label">
            <?php if ($attempt['passing_score']): ?>
                <?php if ($passed): ?>
                    <span class="badge bg-success">Lulus</span> Nilai kelulusan: <?= $attempt['passing_score'] ?>%
                <?php else: ?>
                    <span class="badge bg-danger">Tidak Lulus</span> Nilai kelulusan: <?= $attempt['passing_score'] ?>%
                <?php endif; ?>
            <?php else: ?>
                Nilai Anda
            <?php endif; ?>
        </div>

        <div class="score-details">
            <?php if (!$attempt['hide_all_answers']): ?>
                <div class="score-detail">
                    <div class="detail-value"><?= $correct_answers ?>/<?= $total_questions ?></div>
                    <div class="detail-label">Jawaban Benar</div>
                </div>
            <?php endif; ?>
            <div class="score-detail">
                <div class="detail-value"><?= $earned_points ?>/<?= $total_points ?></div>
                <div class="detail-label">Poin</div>
            </div>
        </div>

    </div>

    <h2>Detail Jawaban</h2>

    <?php foreach ($answers as $index => $answer): ?>
        <div class="answer-card">
            <div class="answer-header">
                <div class="question-number">Pertanyaan <?= $index + 1 ?></div>
                <div class="answer-points">
                    <?php if (!$attempt['hide_all_answers']): ?>
                        <?php if (isset($answer['points_earned'])): ?>
                            <span class="<?= $answer['points_earned'] > 0 ? 'points-earned' : 'points-lost' ?>">
                                <?= $answer['points_earned'] ?>/<?= $answer['points'] ?> poin
                            </span>
                        <?php else: ?>
                            <?= $answer['points'] ?> poin
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="question-text"><?= htmlspecialchars($answer['question_text']) ?></div>

            <div class="answer-content">
                <?php if ($answer['question_type'] == 'multiple_choice' || $answer['question_type'] == 'true_false'): ?>
                    <?php if (isset($answer['selected_option'])): ?>
                        <?php
                        // Determine if we should show correct/incorrect indicators
                        $showAnswerStatus = $attempt['show_correct_answers'] && !$attempt['hide_all_answers'];

                        // Determine answer class
                        $answerClass = 'neutral-answer';
                        if ($showAnswerStatus) {
                            $answerClass = $answer['is_correct'] ? 'correct-answer' : 'incorrect-answer';
                        }
                        ?>
                        <div class="selected-answer <?= $answerClass ?>">
                            <strong>Jawaban Anda:</strong> <?= htmlspecialchars($answer['selected_option']['option_text']) ?>
                            <?php if ($showAnswerStatus): ?>
                                <?php if ($answer['is_correct']): ?>
                                    <i class="fas fa-check-circle text-success ms-2"></i>
                                <?php else: ?>
                                    <i class="fas fa-times-circle text-danger ms-2"></i>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>

                        <?php
                        // Show correct answer if:
                        // 1. show_correct_answers is enabled
                        // 2. hide_all_answers is disabled
                        // 3. correct_option exists
                        $showCorrectAnswer = $attempt['show_correct_answers'] &&
                                           !$attempt['hide_all_answers'] &&
                                           isset($answer['correct_option']);

                        if ($showCorrectAnswer):
                        ?>
                            <div class="correct-option">
                                <strong>Jawaban Benar:</strong> <?= htmlspecialchars($answer['correct_option']['option_text']) ?>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="selected-answer neutral-answer">
                            <em>Tidak ada jawaban yang dipilih</em>
                        </div>
                    <?php endif; ?>
                <?php elseif ($answer['question_type'] == 'short_answer' || $answer['question_type'] == 'essay'): ?>
                    <?php
                    // Determine if we should show correct/incorrect indicators
                    $showAnswerStatus = $attempt['show_correct_answers'] && !$attempt['hide_all_answers'];

                    // Determine answer class
                    $answerClass = 'neutral-answer';
                    if ($showAnswerStatus && isset($answer['is_correct'])) {
                        $answerClass = $answer['is_correct'] ? 'correct-answer' : 'incorrect-answer';
                    }
                    ?>
                    <div class="selected-answer <?= $answerClass ?>">
                        <strong>Jawaban Anda:</strong>
                        <div class="mt-2"><?= nl2br(htmlspecialchars($answer['text_answer'])) ?></div>
                    </div>

                    <?php if (isset($answer['feedback']) && !empty($answer['feedback'])): ?>
                        <div class="feedback">
                            <strong>Umpan Balik:</strong> <?= nl2br(htmlspecialchars($answer['feedback'])) ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>

    <?php endforeach; ?>

    <div class="text-center mt-4 mb-5">
        <a href="take_quiz.php?id=<?= $attempt['quiz_id'] ?>" class="btn btn-primary me-2">
            <i class="fas fa-redo"></i> Coba Lagi
        </a>
        <a href="classroom_detail.php?id=<?= $attempt['class_id'] ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Kelas
        </a>
    </div>
</div>

<?php include '../config/footer.php'; ?>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>