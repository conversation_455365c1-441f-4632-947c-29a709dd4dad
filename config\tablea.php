<style>
    .no-column {
        width: 30px !important;
        text-align: left !important;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }
    .keputusan table th, .keputusan table td, .keputusan table tr {
        padding: 8px;
        border: 1px solid #ddd;
        padding: 8px;
        vertical-align: top;
        word-break: break-word; /* untuk potong kata jika panjang */
        white-space: pre-wrap;  /* agar newline tetap terlihat jika ada */
    }

    /* Button styling */
    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .btn-info {
        color: #fff;
        background-color: #17a2b8;
        border-color: #17a2b8;
    }

    .btn-info:hover {
        color: #fff;
        background-color: #138496;
        border-color: #117a8b;
    }

    .btn-secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-secondary:hover {
        color: #fff;
        background-color: #5a6268;
        border-color: #545b62;
    }

    .view-memo-btn {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        padding: 5px 10px;
        font-size: 0.875rem;
        border-radius: 0.25rem;
        cursor: pointer;
        transition: all 0.2s;
        border: 1px solid transparent;
    }

    /* Modal styling */
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1050;
        display: none;
        width: 100%;
        height: 100%;
        overflow: hidden;
        outline: 0;
    }

    .modal.fade .modal-dialog {
        transition: transform 0.3s ease-out;
        transform: translate(0, -50px);
    }

    .modal.show .modal-dialog {
        transform: none;
    }

    .modal-dialog {
        position: relative;
        width: auto;
        margin: 0.5rem;
        pointer-events: none;
    }

    .modal-lg {
        max-width: 800px;
        margin: 1.75rem auto;
    }

    .modal-content {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        pointer-events: auto;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: 0.3rem;
        outline: 0;
    }

    .modal-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
        border-top-left-radius: 0.3rem;
        border-top-right-radius: 0.3rem;
    }

    .modal-title {
        margin-bottom: 0;
        line-height: 1.5;
    }

    .modal-body {
        position: relative;
        flex: 1 1 auto;
        padding: 1rem;
    }

    .modal-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 1rem;
        border-top: 1px solid #dee2e6;
        border-bottom-right-radius: 0.3rem;
        border-bottom-left-radius: 0.3rem;
    }

    .close {
        float: right;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: 0.5;
        background: none;
        border: none;
        padding: 0;
        cursor: pointer;
    }

    .close:hover {
        color: #000;
        text-decoration: none;
        opacity: 0.75;
    }

    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1040;
        width: 100vw;
        height: 100vh;
        background-color: #000;
    }

    .modal-backdrop.fade {
        opacity: 0;
    }

    .modal-backdrop.show {
        opacity: 0.5;
    }

    .modal-open {
        overflow: hidden;
    }

    .modal-open .modal {
        overflow-x: hidden;
        overflow-y: auto;
    }

    /* Tambahan untuk memastikan modal terlihat */
    #memoModal {
        z-index: 1050;
        background-color: rgba(0, 0, 0, 0.5);
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
    }

    #memoModal.show {
        display: block;
    }

    #memoModal .modal-dialog {
        position: relative;
        width: auto;
        margin: 1.75rem auto;
        max-width: 800px;
        pointer-events: auto;
    }

    #memoModal .modal-content {
        background-color: #fff;
        border-radius: 0.3rem;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    }

    .text-center {
        text-align: center;
    }

    .date-fixed, .date-pending {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    margin-bottom: 4px;
    flex-wrap: wrap;
}

.date-fixed {
    color: #2e7d32;
}

.date-fixed i {
    color: #2e7d32;
    font-size: 16px;
}

.date-pending {
    color: #757575;
}

.date-pending i {
    color: #f57c00;
    font-size: 16px;
}

.date-proposal {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #757575;
    margin-top: 4px;
    padding-left: 4px;
}

.date-proposal i {
    color: #757575;
    font-size: 14px;
}

.proposal-text {
    font-style: italic;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.confirmed {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

.status-badge.pending {
    background-color: #fff3e0;
    color: #f57c00;
    border: 1px solid #ffcc80;
}

/* Styling untuk status yang sudah ditetapkan */
.time-fixed, .place-fixed {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2e7d32;
    font-size: 14px;
}

.time-fixed i, .place-fixed i {
    color: #2e7d32;
    font-size: 16px;
}

/* Styling untuk status yang belum ditentukan */
.time-pending, .place-pending {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #757575;
    font-size: 14px;
}

.time-pending i, .place-pending i {
    color: #f57c00;
    font-size: 16px;
}

/* Badge status */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-left: 8px;
}

.status-badge.confirmed {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

.status-badge.pending {
    background-color: #fff3e0;
    color: #f57c00;
    border: 1px solid #ffcc80;
}

/* Responsive styling */
@media screen and (max-width: 768px) {
    .time-fixed, .place-fixed,
    .time-pending, .place-pending {
        flex-direction: column;
        align-items: flex-start;
    }

    .status-badge {
        margin-left: 24px;
        margin-top: 4px;
    }
}
</style>


<table border="1">
        <!-- <tr><th colspan="5" style="text-align: center;">Informasi Training</th></tr>
        <tr><th>ID Training</th><td colspan="4"><?= htmlspecialchars($row['id'] ?? 'N/A') ?></td></tr> -->
    <table border="1">
        <tr><th colspan="5" style="text-align: center;">Informasi Pemohon Training</th></tr>
        <tr>
            <th>Nama</th>
            <th>NIK</th>
            <th>Jabatan</th>
            <th>Bagian</th>
            <th>Departemen</th>
        </tr>
        <tr>
            <td><?= htmlspecialchars($row['full_name'] ?? 'N/A') ?></td>
            <td><?= htmlspecialchars($row['nik'] ?? 'N/A') ?></td>
            <td><?= htmlspecialchars($row['jabatan'] ?? 'N/A') ?></td>
            <td><?= htmlspecialchars($row['bagian'] ?? 'N/A') ?></td>
            <td><?= htmlspecialchars($row['departemen'] ?? 'N/A') ?></td>
        </tr>
        <tr>
            <th colspan="3">Email</th>
            <th colspan="2">Telepon</th>
        </tr>
        <tr>
            <td colspan="3"><?= htmlspecialchars($row['email'] ?? 'N/A') ?></td>
            <td colspan="2"><?= htmlspecialchars($row['phone'] ?? 'N/A') ?></td>
        </tr>
    </table>
    <table border="1">
        <tr><th colspan="5" style="text-align: center;">Informasi Training</th></tr>
        <tr><th>Nama Training</th>
            <td colspan="4"><?= htmlspecialchars($row['training_topic'] ?? 'N/A') ?></td></tr>
        <tr>
            <th>Tanggal Training</th>
            <td colspan="4">
                <?php if (!empty($row['start_date'])): ?>
                    <?php if ($row['is_confirmed'] == 1): ?>
                        <!-- Training sudah ditetapkan/dikonfirmasi -->
                        <?php if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']): ?>
                            <!-- Multi-day training yang sudah dikonfirmasi -->
                            <span class="date-fixed">
                                <i class="fas fa-calendar-week" style="color: #2e7d32;"></i>
                                <span style="white-space: nowrap; display: inline-block;">
                                    <?= htmlspecialchars($row['start_date']) ?> s/d <?= htmlspecialchars($row['end_date']) ?>
                                </span>
                                <span class="status-badge confirmed" style="margin-left: 5px;">Sudah Ditetapkan (Multi Hari)</span>
                            </span>
                        <?php else: ?>
                            <!-- Single day training yang sudah dikonfirmasi -->
                            <span class="date-fixed">
                                <i class="fas fa-calendar-check" style="color: #2e7d32;"></i>
                                <?= htmlspecialchars($row['start_date']) ?>
                                <span class="status-badge confirmed">Sudah Ditetapkan (1 Hari)</span>
                            </span>
                        <?php endif; ?>
                    <?php else: ?>
                        <!-- Training belum ditetapkan/dikonfirmasi (pending) -->
                        <?php if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']): ?>
                            <!-- Multi-day training yang masih pending -->
                            <span class="date-pending">
                                <i class="fas fa-calendar-alt" style="color: #f57c00;"></i>
                                <span style="white-space: nowrap; display: inline-block;">
                                    <?= htmlspecialchars($row['start_date']) ?> s/d <?= htmlspecialchars($row['end_date']) ?>
                                </span>
                                <span class="status-badge pending" style="margin-left: 5px;">Belum Ditetapkan LnD (Multi Hari)</span>
                            </span>
                        <?php else: ?>
                            <!-- Single day training yang masih pending -->
                            <span class="date-pending">
                                <i class="fas fa-clock" style="color: #f57c00;"></i>
                                <?= htmlspecialchars($row['start_date']) ?>
                                <span class="status-badge pending">Belum Ditetapkan LnD</span>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php else: ?>
                    <span class="date-pending">
                        <i class="fas fa-question-circle" style="color: #f57c00;"></i>
                        <span class="status-badge pending">Tanggal Belum Ditentukan</span>
                    </span>
                <?php endif; ?>
            </td>
        </tr>
        <tr>
            <th>Waktu Training</th>
            <td colspan="4">
                <?php if (!empty($row['training_time_start']) && !empty($row['training_time_end'])): ?>
                    <span class="time-fixed">
                        <i class="fas fa-clock" style="color: #2e7d32;"></i>
                        <?= htmlspecialchars($row['training_time_start']) ?> - <?= htmlspecialchars($row['training_time_end']) ?>
                        <span class="status-badge confirmed">Sudah Ditetapkan</span>
                    </span>
                <?php else: ?>
                    <span class="time-pending">
                        <i class="fas fa-hourglass-half" style="color: #f57c00;"></i>
                        <span class="status-badge pending">Belum Ditentukan LnD</span>
                    </span>
                <?php endif; ?>
            </td>
        </tr>
        <tr><th>Jenis Training</th><td colspan="4"><?= htmlspecialchars($row['training_type'] ?? 'Tidak ada jenis training') ?></td></tr>
        <tr><th>Jenis Skill Training</th><td colspan="4"><?= htmlspecialchars($row['training_skill_type'] ?? 'Tidak ada jenis skill training') ?></td></tr>
        <tr><th>Vendor Training</th><td colspan="4"><?= htmlspecialchars($row['provider_type'] ?? 'Tidak ada provider training') ?></td></tr>
        <tr>
            <th>Tempat Training</th>
            <td colspan="4">
                <?php if (!empty($row['training_place'])): ?>
                    <span class="place-fixed">
                        <i class="fas fa-map-marker-alt"   style="color: #2e7d32;"></i>
                        <?= htmlspecialchars($row['training_place']) ?>
                        <span class="status-badge confirmed">Sudah Ditetapkan</span>
                    </span>
                <?php else: ?>
                    <span class="place-pending">
                        <i class="fas fa-search-location" style="color: #f57c00;"></i>
                        <span class="status-badge pending">Belum Ditentukan LnD</span>
                    </span>
                <?php endif; ?>
            </td>
        </tr>
        <tr>
            <th>Biaya Training</th>
            <td colspan="4">
                <?php if (!empty($row['training_cost'])): ?>
                    <span class="cost-fixed">
                        <i class="fas fa-money-bill-wave" style="color: #2e7d32;"></i>
                        Rp <?= is_numeric($row['training_cost']) ? number_format((float)$row['training_cost'], 0, ',', '.') : htmlspecialchars($row['training_cost']) ?>
                        <span class="status-badge confirmed">Sudah Ditetapkan</span>
                    </span>
                <?php else: ?>
                    <span class="cost-pending">
                        <i class="fas fa-search-dollar" style="color: #f57c00;"></i>
                        <span class="status-badge pending">Belum Ditentukan LnD</span>
                    </span>
                <?php endif; ?>
            </td>
        </tr>
        <tr><th>Urgensi Training</th><td colspan="4"><?= htmlspecialchars($row['additional_info'] ?? 'Tidak ada informasi tambahan') ?></td></tr>
        <tr><th>Contact Person</th><td colspan="4"><?= htmlspecialchars($row['contact_person'] ?? 'Tidak ada informasi contact person') ?></td></tr>
        <tr><th>Contact Number</th><td colspan="4"><?= htmlspecialchars($row['contact_number'] ?? 'Tidak ada informasi nomor kontak') ?></td></tr>
        <tr><th>Sharing Knowledge</th><td colspan="4"><?= htmlspecialchars($row['sharing_knowledge'] ?? 'Belum ditentukan') ?></td></tr>


        <?php if (isset($row['provider_type']) && $row['provider_type'] === 'Internal'):
            // Handle JSON encoded trainer fields
            // Debug: Log raw values
            error_log("Raw trainer_name_internal: " . ($row['trainer_name_internal'] ?? 'NULL'));
            error_log("Raw trainer_nik_internal: " . ($row['trainer_nik_internal'] ?? 'NULL'));

            // Decode JSON data with proper error handling
            $trainerNames = [];
            $trainerNiks = [];
            $trainerDepartments = [];
            $trainerSubDepartments = [];
            $trainerPositions = [];

            // Check if the values are already arrays (not JSON strings)
            if (is_array($row['trainer_name_internal'] ?? null)) {
                $trainerNames = $row['trainer_name_internal'];
            } else {
                $decoded = json_decode($row['trainer_name_internal'] ?? '[]', true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    $trainerNames = $decoded;
                }
            }

            if (is_array($row['trainer_nik_internal'] ?? null)) {
                $trainerNiks = $row['trainer_nik_internal'];
            } else {
                $decoded = json_decode($row['trainer_nik_internal'] ?? '[]', true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    $trainerNiks = $decoded;
                }
            }

            if (is_array($row['trainer_department_internal'] ?? null)) {
                $trainerDepartments = $row['trainer_department_internal'];
            } else {
                $decoded = json_decode($row['trainer_department_internal'] ?? '[]', true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    $trainerDepartments = $decoded;
                }
            }

            if (is_array($row['trainer_sub_department_internal'] ?? null)) {
                $trainerSubDepartments = $row['trainer_sub_department_internal'];
            } else {
                $decoded = json_decode($row['trainer_sub_department_internal'] ?? '[]', true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    $trainerSubDepartments = $decoded;
                }
            }

            if (is_array($row['trainer_position_internal'] ?? null)) {
                $trainerPositions = $row['trainer_position_internal'];
            } else {
                $decoded = json_decode($row['trainer_position_internal'] ?? '[]', true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    $trainerPositions = $decoded;
                }
            }

            // Debug: Log decoded values
            error_log("Decoded trainerNames: " . print_r($trainerNames, true));
            error_log("Decoded trainerNiks: " . print_r($trainerNiks, true));

            if (!empty($trainerNames)) {
                echo '<table>';
                echo '<tr><th colspan="6" style="text-align: center;">Trainer</th></tr>';
                echo '<tr>
                        <th class="no-column">No</th>
                        <th>Nama</th>
                        <th>NIK</th>
                        <th>Jabatan</th>
                        <th>Bagian</th>
                        <th>Departemen</th>
                      </tr>';

                for ($i = 0; $i < count($trainerNames); $i++) {
                    echo '<tr>';
                    echo '<td class="no-column">' . ($i + 1) . '</td>';
                    echo '<td>' . htmlspecialchars($trainerNames[$i] ?? '') . '</td>';
                    echo '<td>' . htmlspecialchars($trainerNiks[$i] ?? '') . '</td>';
                    echo '<td>' . htmlspecialchars($trainerPositions[$i] ?? '') . '</td>';
                    echo '<td>' . htmlspecialchars($trainerSubDepartments[$i] ?? '') . '</td>';
                    echo '<td>' . htmlspecialchars($trainerDepartments[$i] ?? '') . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            } else {
                echo '<table border="1">';
                echo '<tr><th colspan="6" style="text-align: center;">Trainer</th></tr>';
                echo '<tr><td colspan="6" style="text-align: center;">Tidak ada trainer.</td></tr>';
                echo '</table>';
            }
        ?>
        <?php elseif (isset($row['provider_type']) && ($row['provider_type'] === 'Eksternal' || $row['provider_type'] === 'External')): ?>
            <tr><th>Nama Vendor</th><td colspan="4"><?= htmlspecialchars($row['provider_name'] ?? 'Tidak ada nama provider') ?></td></tr>
            <tr><th>Alamat Vendor</th><td colspan="4"><?= htmlspecialchars($row['provider_address'] ?? 'Tidak ada alamat provider') ?></td></tr>
            <tr><th>Nama Trainer Eksternal</th><td colspan="4"><?= htmlspecialchars($row['trainer_name_external'] ?? 'Tidak ada nama trainer eksternal') ?></td></tr>
            <tr><th>Informasi Vendor</th><td colspan="4"><?= htmlspecialchars($row['additional_info_provider'] ?? 'Tidak ada informasi provider') ?></td></tr>
        <?php endif; ?>
    </table>
    <?php
    if (isset($row['participant_names']) && $row['participant_names']) {
        $participants = explode(', ', $row['participant_names']);
        $niks = explode(', ', $row['participant_niks']);
        $jabatans = explode(', ', $row['participant_jabatans']);
        $bagians = explode(', ', $row['participant_bagians']);
        $departemens = explode(', ', $row['participant_departemens']);
        echo '<table>';
        echo '<tr><th colspan="9" style="text-align: center;">Peserta</th></tr>';
        echo '<tr>
                <th class="no-column">No</th>
                <th colspan="2">Nama</th>
                <th colspan="2">NIK</th>
                <th colspan="2">Jabatan</th>
                <th>Bagian</th>
                <th>Departemen</th>
              </tr>';

        for ($i = 0; $i < count($participants); $i++) {
            echo '<tr>';
            echo '<td class="no-column">' . ($i + 1) . '</td>';
            echo '<td colspan="2">' . htmlspecialchars($participants[$i]) . '</td>';
            echo '<td colspan="2">' . htmlspecialchars($niks[$i] ?? 'N/A') . '</td>';
            echo '<td colspan="2">' . htmlspecialchars($jabatans[$i] ?? 'N/A') . '</td>';
            echo '<td>' . htmlspecialchars($bagians[$i] ?? 'N/A') . '</td>';
            echo '<td>' . htmlspecialchars($departemens[$i] ?? 'N/A') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<table>';
        echo '<tr><th colspan="6" style="text-align: center;">Peserta</th></tr>';
        echo '<tr><td colspan="6" style="text-align: center;">Tidak ada peserta.</td></tr>';
        echo '</table>';
    }
    ?>
        <div class="keputusan">
    <table border="1">
        <tr><th colspan="5" style="text-align: center;">Status Approval</th></tr>
        <tr><th>Sedang Ditinjau Oleh</th><td colspan="4"><?= htmlspecialchars($row['current_approver'] ?? 'Selesai') ?></td></tr>
        <tr><th>Komentar Dept Head</th><td colspan="4"><?= htmlspecialchars($row['comments_dept_head'] ?? 'Belum ada komentar.') ?></td></tr>
        <tr><th>Penugasan Dept Head</th><td colspan="4"><?= htmlspecialchars($row['assignment'] ?? 'Belum ada penugasan.') ?></td></tr>
        <tr><th>Komentar Chief Spv LnD</th><td colspan="4"><?= htmlspecialchars($row['comments_hrd'] ?? 'Belum ada komentar.') ?></td></tr>
        <tr><th>Internal Memo</th>
            <td colspan="4">
                <?php if (!empty($row['internal_memo_image'])): ?>
                    <?php
                    // Determine file type for button icon
                    $memo_extension = strtolower(pathinfo($row['internal_memo_image'], PATHINFO_EXTENSION));
                    $is_memo_pdf = ($memo_extension === 'pdf');
                    $button_icon = $is_memo_pdf ? 'fas fa-file-pdf' : 'fas fa-image';
                    $button_text = $is_memo_pdf ? 'Lihat Internal Memo (PDF)' : 'Lihat Internal Memo (Gambar)';
                    ?>
                    <button type="button" class="btn btn-info view-memo-btn" style="padding: 6px 12px; white-space: nowrap; width: auto; font-size: 14px; font-weight: 500; border-radius: 4px; cursor: pointer;" onclick="showMemoModal(); return false;">
                        <i class="<?= $button_icon ?>" style="margin-right: 5px;"></i> <?= $button_text ?>
                    </button>
                    <!-- Debug info - only visible to admins -->
                    <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin'): ?>
                    <div style="font-size: 11px; color: #999; margin-top: 5px;">
                        <strong>Debug:</strong> <?= htmlspecialchars($row['internal_memo_image']) ?> (<?= strtoupper($memo_extension) ?>)
                    </div>
                    <?php endif; ?>
                <?php else: ?>
                    <span class="text-muted">Tidak ada internal memo</span>
                <?php endif; ?>
            </td>
        </tr>
        <tr><th>Sharing Knowledge?</th><td colspan="4"><?= htmlspecialchars($row['sharing_knowledge'] ?? 'Belum ditentukan.') ?></td></tr>
        <tr><th>Komentar Manager HRGA</th><td colspan="4"><?= htmlspecialchars($row['comments_ga'] ?? 'Belum ada komentar.') ?></td></tr>
        <tr><th>Komentar Factory Manager</th><td colspan="4"><?= htmlspecialchars($row['comments_fm'] ?? 'Belum ada komentar.') ?></td></tr>
        <tr><th>Komentar Direktur</th><td colspan="4"><?= htmlspecialchars($row['comments_dir'] ?? 'Belum ada komentar.') ?></td></tr>
    </table>
        </div>

</table>

<!-- Modal for Internal Memo -->
<?php if (!empty($row['internal_memo_image'])): ?>
<div class="modal fade" id="memoModal" tabindex="-1" role="dialog" aria-labelledby="memoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="memoModalLabel">Internal Memo</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <?php
                // Determine the correct path based on current directory
                $image_path = $row['internal_memo_image'];

                // If path doesn't start with uploads/, add it (PHP 7.4 compatible)
                if (strpos($image_path, 'uploads/') !== 0) {
                    $image_path = 'uploads/' . ltrim($image_path, '/');
                }

                // Add ../ prefix for files called from subdirectories
                $full_image_path = '../' . $image_path;

                // Check file extension to determine how to display
                $file_extension = strtolower(pathinfo($image_path, PATHINFO_EXTENSION));
                $is_pdf = ($file_extension === 'pdf');
                $is_image = in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']);
                ?>

                <?php if ($is_pdf): ?>
                    <!-- PDF Display - Using object tag instead of iframe -->
                    <div style="padding: 20px; text-align: center;">
                        <i class="fas fa-file-pdf" style="font-size: 64px; color: #dc3545; margin-bottom: 15px;"></i>
                        <h4>File PDF Internal Memo</h4>
                        <p>File PDF tidak dapat ditampilkan langsung di browser untuk mencegah error.</p>
                        <div style="margin: 20px 0;">
                            <a href="../config/download_memo.php?file=<?= urlencode($row['internal_memo_image']) ?>&action=download" class="btn btn-danger" style="margin-right: 10px;">
                                <i class="fas fa-download"></i> Download PDF
                            </a>
                            <a href="../config/download_memo.php?file=<?= urlencode($row['internal_memo_image']) ?>&action=view" class="btn btn-primary" target="_blank">
                                <i class="fas fa-external-link-alt"></i> Buka di Tab Baru
                            </a>
                        </div>
                        <div style="font-size: 12px; color: #666; margin-top: 10px;">
                            Nama File: <?= basename($row['internal_memo_image']) ?>
                        </div>
                    </div>
                <?php elseif ($is_image): ?>
                    <!-- Image Display -->
                    <img id="memoImage" src="<?= htmlspecialchars($full_image_path) ?>" alt="Internal Memo" style="max-width: 100%; max-height: 80vh;"
                         onerror="handleImageError(this);">
                    <div id="imageErrorMessage" style="display: none; padding: 20px; color: #666;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #f39c12; margin-bottom: 10px;"></i><br>
                        <strong>Gambar tidak dapat dimuat</strong><br>
                        <small>Path: <?= htmlspecialchars($full_image_path) ?></small><br>
                        <small>Periksa apakah file masih ada di server</small><br>
                        <button onclick="tryAlternatePath()" class="btn btn-sm btn-warning" style="margin-top: 10px;">
                            <i class="fas fa-redo"></i> Coba Path Alternatif
                        </button>
                    </div>
                <?php else: ?>
                    <!-- Unknown file type -->
                    <div style="padding: 20px; color: #666;">
                        <i class="fas fa-file" style="font-size: 48px; color: #6c757d; margin-bottom: 10px;"></i><br>
                        <strong>File Type: <?= strtoupper($file_extension) ?></strong><br>
                        <small>File ini tidak dapat ditampilkan di browser</small><br>
                        <small>Gunakan tombol Download untuk mengunduh file</small>
                    </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                <a href="../config/download_memo.php?file=<?= urlencode($row['internal_memo_image']) ?>&action=download" class="btn btn-primary">
                    <i class="fas fa-download"></i> Download
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // Completely new approach - use direct DOM manipulation without any dependencies

    // Global variables to track modal state
    var modalIsOpen = false;
    var modalBackdrop = null;

    // Function to show the modal
    function showMemoModal() {
        // If modal is already open, do nothing
        if (modalIsOpen) return;

        var modal = document.getElementById('memoModal');
        if (!modal) {
            console.error("Modal element not found!");
            return;
        }

        // Clean up any existing modal state
        cleanupModal();

        // Show the modal
        modal.style.display = 'block';
        modal.classList.add('show');

        // Create backdrop
        modalBackdrop = document.createElement('div');
        modalBackdrop.className = 'modal-backdrop fade show';
        document.body.appendChild(modalBackdrop);

        // Prevent body scrolling
        document.body.style.overflow = 'hidden';
        document.body.classList.add('modal-open');

        // Add event listeners
        document.addEventListener('keydown', handleEscapeKey);
        modalBackdrop.addEventListener('click', hideMemoModal);

        // Set modal state
        modalIsOpen = true;

        // Add click handlers to close buttons
        var closeButtons = modal.querySelectorAll('[data-dismiss="modal"]');
        closeButtons.forEach(function(button) {
            button.onclick = hideMemoModal;
        });

        // Force the modal to be visible with proper z-index
        setTimeout(function() {
            modal.style.zIndex = '1050';
            modal.style.display = 'block';

            // Make sure the modal dialog is visible
            var modalDialog = modal.querySelector('.modal-dialog');
            if (modalDialog) {
                modalDialog.style.display = 'block';
                modalDialog.style.margin = '1.75rem auto';
            }

            // Make sure the modal content is visible
            var modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.display = 'block';
            }

            // Make sure the image is visible
            var modalImage = modal.querySelector('.modal-body img');
            if (modalImage) {
                modalImage.style.display = 'block';
                modalImage.style.margin = '0 auto';

                // Debug: Log image information
                console.log('Image src:', modalImage.src);
                console.log('Image complete:', modalImage.complete);
                console.log('Image naturalWidth:', modalImage.naturalWidth);
                console.log('Image naturalHeight:', modalImage.naturalHeight);

                // Check if image loads successfully
                modalImage.onload = function() {
                    console.log('Image loaded successfully');
                };

                modalImage.onerror = function() {
                    console.error('Image failed to load:', this.src);
                };
            }
        }, 50);
    }

    // Function to hide the modal
    function hideMemoModal() {
        // If modal is not open, do nothing
        if (!modalIsOpen) return;

        var modal = document.getElementById('memoModal');
        if (!modal) return;

        // Hide the modal
        modal.style.display = 'none';

        // Clean up
        cleanupModal();

        // Set modal state
        modalIsOpen = false;
    }

    // Function to clean up modal resources
    function cleanupModal() {
        // Remove backdrop
        if (modalBackdrop && modalBackdrop.parentNode) {
            modalBackdrop.parentNode.removeChild(modalBackdrop);
        }

        // Remove any other backdrops that might exist
        var backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(function(backdrop) {
            if (backdrop.parentNode) {
                backdrop.parentNode.removeChild(backdrop);
            }
        });

        // Reset body styles
        document.body.style.overflow = '';
        document.body.classList.remove('modal-open');
        document.body.style.paddingRight = '';

        // Remove event listeners
        document.removeEventListener('keydown', handleEscapeKey);
    }

    // Function to handle escape key
    function handleEscapeKey(e) {
        if (e.key === 'Escape') {
            hideMemoModal();
        }
    }

    // Initialize when document is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Add click handlers to all view memo buttons
        var viewButtons = document.querySelectorAll('.view-memo-btn');
        viewButtons.forEach(function(button) {
            button.onclick = function(e) {
                e.preventDefault();
                showMemoModal();
                return false;
            };
        });
    });

    // Function to handle image loading errors
    function handleImageError(img) {
        console.error('Image failed to load:', img.src);
        img.style.display = 'none';
        document.getElementById('imageErrorMessage').style.display = 'block';
    }



    // Function to try alternate path
    function tryAlternatePath() {
        var img = document.getElementById('memoImage');
        var currentSrc = img.src;

        // Try different path variations
        var originalPath = '<?= htmlspecialchars($row['internal_memo_image']) ?>';
        var alternatePaths = [
            originalPath, // Direct path without ../
            '../' + originalPath, // Current path
            '../../' + originalPath, // One more level up
            '../uploads/internal_memo/' + originalPath.split('/').pop(), // Just filename
            'uploads/internal_memo/' + originalPath.split('/').pop() // Without ../
        ];

        // Find current path index and try next one
        var currentIndex = -1;
        for (var i = 0; i < alternatePaths.length; i++) {
            if (currentSrc.includes(alternatePaths[i])) {
                currentIndex = i;
                break;
            }
        }

        var nextIndex = (currentIndex + 1) % alternatePaths.length;
        img.src = alternatePaths[nextIndex];
        img.style.display = 'block';
        document.getElementById('imageErrorMessage').style.display = 'none';

        console.log('Trying alternate path:', alternatePaths[nextIndex]);
    }

    // Make functions globally available
    window.showMemoModal = showMemoModal;
    window.hideMemoModal = hideMemoModal;
    window.handleImageError = handleImageError;
    window.tryAlternatePath = tryAlternatePath;
</script>
<?php endif; ?>


