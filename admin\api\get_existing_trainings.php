<?php
/**
 * API untuk mengambil daftar training existing berdasarkan tipe
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Include necessary files
require_once '../../config/config.php';
require_once '../security.php';

// Check if user is admin (same validation as security.php)
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role_id'], [2, 3, 4, 5, 99])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized - Admin access required']);
    exit;
}

try {
    // Get parameters
    $training_type = $_GET['type'] ?? '';
    $search = $_GET['search'] ?? '';
    $exclude_displayed = $_GET['exclude_displayed'] ?? 'false'; // Default to false untuk menampilkan semua

    if (empty($training_type)) {
        throw new Exception('Training type is required');
    }

    $trainings = [];

    // Get list of training IDs that are already displayed in calendar (not hidden)
    $displayed_training_ids = [];
    if ($exclude_displayed === 'true') {
        if ($training_type === 'offline') {
            $displayed_query = "SELECT id FROM offline_training WHERE (is_hidden = 0 OR is_hidden IS NULL) AND status IN ('Active', 'Completed')";
        } else {
            $displayed_query = "SELECT id FROM training_submissions WHERE (is_hidden = 0 OR is_hidden IS NULL) AND status = 'Approved'";
        }

        $displayed_result = $conn->query($displayed_query);
        if ($displayed_result) {
            while ($row = $displayed_result->fetch_assoc()) {
                $displayed_training_ids[] = $row['id'];
            }
        }
    }

    if ($training_type === 'offline') {
        // Get offline trainings from offline_training table
        $query = "SELECT
                    id,
                    training_topic as title,
                    training_description as description,
                    start_date as date,
                    CONCAT(COALESCE(training_time_start, ''),
                           CASE WHEN training_time_start IS NOT NULL AND training_time_end IS NOT NULL
                                THEN ' - ' ELSE '' END,
                           COALESCE(training_time_end, '')) as time,
                    location,
                    trainer_name as trainer,
                    max_participants,
                    COALESCE(status, 'Active') as status,
                    created_at
                  FROM offline_training";

        $params = [];
        $types = "";

        // Add status filter
        $where_conditions = ["status IN ('Active', 'Completed')"];

        if (!empty($search)) {
            $where_conditions[] = "(training_topic LIKE ? OR location LIKE ? OR trainer_name LIKE ?)";
            $search_param = "%$search%";
            $params = [$search_param, $search_param, $search_param];
            $types = "sss";
        }

        if (!empty($where_conditions)) {
            $query .= " WHERE " . implode(" AND ", $where_conditions);
        }

        $query .= " ORDER BY start_date DESC, created_at DESC";

        $stmt = $conn->prepare($query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            // Skip if this training is already displayed in calendar
            if ($exclude_displayed === 'true' && in_array($row['id'], $displayed_training_ids)) {
                continue;
            }

            $trainings[] = [
                'id' => $row['id'],
                'title' => $row['title'],
                'description' => $row['description'],
                'date' => $row['date'],
                'time' => trim($row['time']),
                'location' => $row['location'],
                'trainer' => $row['trainer'],
                'max_participants' => $row['max_participants'],
                'status' => $row['status'],
                'type' => 'offline',
                'is_displayed' => in_array($row['id'], $displayed_training_ids)
            ];
        }
        $stmt->close();

    } elseif ($training_type === 'online') {
        // Get online trainings from training_submissions table
        $query = "SELECT
                    id,
                    training_topic as title,
                    additional_info as description,
                    start_date as date,
                    CONCAT(COALESCE(training_time_start, ''),
                           CASE WHEN training_time_start IS NOT NULL AND training_time_end IS NOT NULL
                                THEN ' - ' ELSE '' END,
                           COALESCE(training_time_end, '')) as time,
                    COALESCE(training_place, 'Online') as location,
                    contact_person as trainer,
                    NULL as max_participants,
                    status,
                    created_at
                  FROM training_submissions";

        $params = [];
        $types = "";

        if (!empty($search)) {
            $query .= " WHERE training_topic LIKE ? OR training_place LIKE ? OR contact_person LIKE ?";
            $search_param = "%$search%";
            $params = [$search_param, $search_param, $search_param];
            $types = "sss";
        }

        $query .= " ORDER BY start_date DESC, created_at DESC";

        $stmt = $conn->prepare($query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            // Skip if this training is already displayed in calendar
            if ($exclude_displayed === 'true' && in_array($row['id'], $displayed_training_ids)) {
                continue;
            }

            $trainings[] = [
                'id' => $row['id'],
                'title' => $row['title'],
                'description' => $row['description'],
                'date' => $row['date'],
                'time' => trim($row['time']),
                'location' => $row['location'],
                'trainer' => $row['trainer'],
                'max_participants' => $row['max_participants'],
                'status' => $row['status'],
                'type' => 'online',
                'is_displayed' => in_array($row['id'], $displayed_training_ids)
            ];
        }
        $stmt->close();

    } else {
        throw new Exception('Invalid training type');
    }

    echo json_encode([
        'success' => true,
        'trainings' => $trainings,
        'count' => count($trainings),
        'type' => $training_type,
        'exclude_displayed' => $exclude_displayed,
        'displayed_count' => count($displayed_training_ids),
        'message' => $exclude_displayed === 'true' ?
            'Training yang sudah ditampilkan di kalender disembunyikan' :
            'Menampilkan semua training'
    ]);

} catch (Exception $e) {
    error_log("Error in get_existing_trainings.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error loading trainings: ' . $e->getMessage()
    ]);
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
