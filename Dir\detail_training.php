<?php
include '../config/config.php';
include 'security.php';  // Ensure security.php is included

$id = $_GET['id'];

$query = "SELECT ts.id, ts.full_name, ts.assignment, ts.status, ts.training_topic, ts.nik, ts.departemen, ts.bagian, ts.jabatan, ts.internal_memo_image,
                 ts.training_type, ts.training_skill_type, ts.start_date, ts.end_date, ts.is_confirmed, ts.training_place, ts.training_cost,
                 ts.contact_person, ts.contact_number, ts.sharing_knowledge, ts.additional_info, ts.approved_hrd,
                 ts.email, ts.phone, ts.comments_dept_head, ts.comments_hrd, ts.comments_ga, ts.comments_fm, ts.comments_dir,
                 ts.training_time_start, ts.training_time_end, ts.provider_name, ts.provider_address,
                 ts.provider_type, ts.trainer_name_external, ts.additional_info_provider,
                 ts.trainer_name_internal, ts.trainer_nik_internal, ts.trainer_department_internal, ts.trainer_sub_department_internal, ts.trainer_position_internal,
                 ts.approved_fm, ts.approved_dir,
                 r.role_name AS current_approver,
                 GROUP_CONCAT(p.nama_participants SEPARATOR ', ') AS participant_names,
                 GROUP_CONCAT(p.nik_participants SEPARATOR ', ') AS participant_niks,
                 GROUP_CONCAT(p.jabatan_participants SEPARATOR ', ') AS participant_jabatans,
                 GROUP_CONCAT(p.bagian_participants SEPARATOR ', ') AS participant_bagians,
                 GROUP_CONCAT(p.departemen_participants SEPARATOR ', ') AS participant_departemens
          FROM training_submissions ts
          LEFT JOIN roles r ON ts.current_approver_role_id = r.id
          LEFT JOIN participants p ON ts.id = p.training_id
          WHERE ts.id = ?
          GROUP BY ts.id";
$stmt = $conn->prepare($query);
if ($stmt === false) {
    die("Error preparing query.");
}

$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();

// Cek apakah data pengajuan ditemukan
if (!$row) {
    die("Training submission not found.");
}
// Process form submission if any
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $status = $_POST['status'];
    $comments = $_POST['comments'];

    // Validate status
    if (!in_array($status, ['Approved', 'Rejected', 'Revise'])) {
        die("Invalid status.");
    }

    // Update status and comments
    if ($status == 'Approved') {
        $updateQuery = "UPDATE training_submissions
                        SET status = 'Approved',
                            comments_dir = ?,
                            approved_dir = 'Approved',
                            approved_by = ?
                        WHERE id = ?";

        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param("sii", $comments, $_SESSION['user_id'], $id);
    } else if ($status == 'Rejected') {
        $updateQuery = "UPDATE training_submissions
                        SET status = 'Rejected',
                            comments_dir = ?,
                            approved_dir = 'Rejected',
                            rejected_at = CURRENT_TIMESTAMP,
                            rejected_by = ?
                        WHERE id = ?";

        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param("sii", $comments, $_SESSION['user_id'], $id);
    } else if ($status == 'Revise') {
        // Reset ke pemohon untuk revisi
        $updateQuery = "UPDATE training_submissions
                        SET status = 'Revise',
                            comments_dir = ?,
                            approved_dir = 'Revise',
                            current_approver_role_id = NULL,
                            next_approver_id = NULL
                        WHERE id = ?";

        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param("si", $comments, $id);
    }

    if ($updateStmt->execute()) {
        if ($status == 'Approved') {
            // Send email notification for Director approval
            require_once '../config/director_approval_email_helper.php';
            $assignment = $row['assignment'] ?? '';
            $email_result = sendDirectorApprovalNotification($id, $conn, $comments, $assignment);

            if ($email_result['success']) {
                $_SESSION['success_message'] = "Training telah disetujui dan siap dilaksanakan. Email notifikasi telah dikirim ke pemohon dan department head.";
            } else {
                $_SESSION['success_message'] = "Training telah disetujui dan siap dilaksanakan. (Notifikasi email gagal dikirim)";
            }
        } elseif ($status == 'Rejected') {
            // Send email notification for Director rejection
            require_once '../config/director_approval_email_helper.php';
            $email_result = sendDirectorRejectionNotification($id, $conn, $comments);

            if ($email_result['success']) {
                $_SESSION['error_message'] = "Training telah ditolak. Email notifikasi telah dikirim ke pemohon.";
            } else {
                $_SESSION['error_message'] = "Training telah ditolak. (Notifikasi email gagal dikirim)";
            }
        } else {
            $_SESSION['error_message'] = "Training telah direvisi.";
        }

        header("Location: ../Dir/dashboard.php");
        exit;
    } else {
        $_SESSION['error_message'] = "Terjadi kesalahan dalam memproses persetujuan.";
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<head>
    <script>
        function showAlert(message) {
            let alertBox = document.createElement("div");
            alertBox.innerText = message;
            alertBox.style.position = "fixed";
            alertBox.style.top = "20px";
            alertBox.style.left = "50%";
            alertBox.style.transform = "translateX(-50%)";
            alertBox.style.backgroundColor = "#28a745";
            alertBox.style.color = "white";
            alertBox.style.padding = "10px 20px";
            alertBox.style.borderRadius = "5px";
            alertBox.style.boxShadow = "0px 4px 6px rgba(0,0,0,0.1)";
            document.body.appendChild(alertBox);
            setTimeout(() => { alertBox.remove(); }, 3000);
        }
    </script>
</head>
<style>
    .alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success">
        <?= htmlspecialchars($_SESSION['success_message']) ?>
    </div>
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger">
        <?= htmlspecialchars($_SESSION['error_message']) ?>
    </div>
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

<div class="container-form">
    <div class="form-container">
        <div class="latest-submissions">
            <h1>Detail Pengajuan Training</h1>
            <?php include '../config/tablea.php'; ?>
            <h3>Berikan Keputusan</h3>
            <form method="POST">
                <label for="status">Status:</label>
                <select name="status" id="status" onchange="toggleCommentField()">
                    <option value="Approved" <?= $row['status'] == 'Approved' ? 'selected' : '' ?>>Approved</option>
                    <option value="Rejected" <?= $row['status'] == 'Rejected' ? 'selected' : '' ?>>Rejected</option>
                    <option value="Revise" <?= $row['status'] == 'Revise' ? 'selected' : '' ?>>Revise</option>
                </select><br><br>

                <div id="comments_section">
                    <label for="comments" id="comments_label">Komentar:</label><br>
                    <small id="comments_help" class="form-text text-muted">
                        <?php if ($row['status'] == 'Revise'): ?>
                            Berikan alasan dan petunjuk yang jelas untuk revisi yang diperlukan
                        <?php elseif ($row['status'] == 'Rejected'): ?>
                            Berikan alasan yang jelas mengapa pengajuan ini ditolak
                        <?php else: ?>
                            Berikan alasan dan petunjuk yang jelas (opsional)
                        <?php endif; ?>
                    </small>
                    <textarea name="comments" id="comments" rows="4" cols="50" <?= ($row['status'] == 'Revise' || $row['status'] == 'Rejected') ? 'required' : '' ?>><?= htmlspecialchars($row['comments_dir']??'') ?></textarea><br><br>
                </div>
                <button type="submit">Submit</button>
            </form>
            <button><a style="color: white; text-decoration: none;" href="../Dir/dashboard.php">Kembali ke Daftar Pengajuan</a></button>
        </div>
    </div>
</div>
<?php include '../config/footer.php'; ?>

<script>
    // Fungsi untuk menampilkan/menyembunyikan kolom komentar berdasarkan status
    function toggleCommentField() {
        const statusSelect = document.getElementById('status');
        const commentsSection = document.getElementById('comments_section');
        const commentsTextarea = document.getElementById('comments');
        const commentsLabel = document.getElementById('comments_label');
        const commentsHelp = document.getElementById('comments_help');

        // Cek apakah status adalah Revise atau Rejected
        const isReviseOrRejected = statusSelect.value === 'Revise' || statusSelect.value === 'Rejected';

        // Selalu tampilkan kolom komentar
        commentsSection.style.display = 'block';

        // Hanya jadikan required jika status Revise atau Rejected
        if (isReviseOrRejected) {
            commentsTextarea.setAttribute('required', 'required');
        } else {
            commentsTextarea.removeAttribute('required');
        }

        // Sesuaikan teks berdasarkan status
        if (statusSelect.value === 'Revise') {
            commentsLabel.textContent = 'Komentar Revisi:';
            commentsHelp.textContent = 'Berikan alasan dan petunjuk yang jelas untuk revisi yang diperlukan';
        } else if (statusSelect.value === 'Rejected') {
            commentsLabel.textContent = 'Alasan Penolakan:';
            commentsHelp.textContent = 'Berikan alasan yang jelas mengapa pengajuan ini ditolak';
        } else {
            commentsLabel.textContent = 'Komentar (Opsional):';
            commentsHelp.textContent = 'Berikan komentar tambahan jika diperlukan';
        }
    }

    // Panggil fungsi saat halaman dimuat untuk mengatur tampilan awal
    document.addEventListener('DOMContentLoaded', function() {
        toggleCommentField();
    });
</script>
</body>
</html>

<?php $conn->close(); ?>
