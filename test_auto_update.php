<?php
/**
 * Test Script untuk Auto-Update Training Status System
 * 
 * Script ini untuk testing dan verifi<PERSON>i bahwa sistem auto-update berfungsi dengan baik
 */

require_once 'config/config.php';
require_once 'config/auto_update_helper.php';

echo "<h2>🧪 Test Auto-Update Training Status System</h2>";

// 1. Test Database Connection
echo "<h3>1. Database Connection Test</h3>";
try {
    // Test connection dengan query sederhana
    $test_result = $conn->query("SELECT 1");
    if ($test_result) {
        echo "<p style='color: green;'>✅ Database connection: OK</p>";
    } else {
        echo "<p style='color: red;'>❌ Database connection: FAILED</p>";
        exit(1);
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
    exit(1);
}

// 2. Test Auto-Update Helper Functions
echo "<h3>2. Auto-Update Helper Functions Test</h3>";

// Test isAutoUpdateRunToday function
try {
    $is_run_today = isAutoUpdateRunToday($conn);
    echo "<p>✅ isAutoUpdateRunToday(): " . ($is_run_today ? 'true' : 'false') . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ isAutoUpdateRunToday() error: " . $e->getMessage() . "</p>";
}

// Test getAutoUpdateStats function
try {
    $stats = getAutoUpdateStats($conn, 30);
    echo "<p>✅ getAutoUpdateStats(): " . json_encode($stats) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ getAutoUpdateStats() error: " . $e->getMessage() . "</p>";
}

// 3. Check Training Data
echo "<h3>3. Training Data Analysis</h3>";

$today = date('Y-m-d');

// Check for training that should be updated
$check_queries = [
    'Approved training yang sudah lewat tanggal' => "
        SELECT COUNT(*) as count 
        FROM training_submissions 
        WHERE status = 'Approved' 
        AND (start_date < '$today' OR (end_date IS NOT NULL AND end_date < '$today'))
    ",
    'Pending training yang expired' => "
        SELECT COUNT(*) as count 
        FROM training_submissions 
        WHERE status = 'Pending' 
        AND start_date < DATE_SUB('$today', INTERVAL 1 DAY)
    ",
    'Total training aktif' => "
        SELECT COUNT(*) as count 
        FROM training_submissions 
        WHERE status IN ('Approved', 'Pending', 'Completed')
    "
];

foreach ($check_queries as $description => $query) {
    try {
        $result = $conn->query($query);
        $count = $result->fetch_assoc()['count'];
        echo "<p>📊 {$description}: <strong>{$count}</strong></p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error checking {$description}: " . $e->getMessage() . "</p>";
    }
}

// 4. Test Auto-Update Function (Dry Run)
echo "<h3>4. Auto-Update Function Test (Dry Run)</h3>";

// Backup current auto-update status
$original_last_update = $_SESSION['last_auto_update'] ?? null;

// Reset untuk testing
if (isset($_SESSION['last_auto_update'])) {
    unset($_SESSION['last_auto_update']);
}

try {
    echo "<p>🔄 Running auto-update test...</p>";
    $results = autoUpdateTrainingStatus($conn);
    
    if ($results['success']) {
        echo "<p style='color: green;'>✅ Auto-update test: SUCCESS</p>";
        echo "<ul>";
        echo "<li>Approved → Completed: {$results['approved_to_completed']}</li>";
        echo "<li>Pending → Canceled: {$results['pending_to_canceled']}</li>";
        echo "<li>Total Updated: {$results['total_updated']}</li>";
        echo "</ul>";
        
        if (!empty($results['details'])) {
            echo "<p><strong>Details:</strong></p>";
            echo "<ul>";
            foreach ($results['details'] as $detail) {
                echo "<li>" . htmlspecialchars($detail) . "</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p style='color: red;'>❌ Auto-update test: FAILED</p>";
        echo "<p>Error: " . htmlspecialchars($results['message']) . "</p>";
        
        if (!empty($results['errors'])) {
            echo "<ul>";
            foreach ($results['errors'] as $error) {
                echo "<li style='color: red;'>" . htmlspecialchars($error) . "</li>";
            }
            echo "</ul>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Auto-update test exception: " . $e->getMessage() . "</p>";
}

// Restore original status
if ($original_last_update) {
    $_SESSION['last_auto_update'] = $original_last_update;
}

// 5. Test Cron Script (Syntax Check)
echo "<h3>5. Cron Script Test</h3>";

$cron_script = __DIR__ . '/cron/auto_update_training.php';

if (file_exists($cron_script)) {
    echo "<p>✅ Cron script exists: {$cron_script}</p>";
    
    // Test PHP syntax
    $output = [];
    $return_var = 0;
    exec("php -l \"$cron_script\" 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "<p style='color: green;'>✅ Cron script syntax: OK</p>";
    } else {
        echo "<p style='color: red;'>❌ Cron script syntax error:</p>";
        echo "<pre>" . implode("\n", $output) . "</pre>";
    }
} else {
    echo "<p style='color: red;'>❌ Cron script not found: {$cron_script}</p>";
}

// 6. Test Log Directory
echo "<h3>6. Log Directory Test</h3>";

$log_dir = __DIR__ . '/logs';
$log_file = $log_dir . '/auto_update_cron.log';

if (!is_dir($log_dir)) {
    echo "<p style='color: orange;'>⚠️ Log directory doesn't exist, creating: {$log_dir}</p>";
    if (mkdir($log_dir, 0755, true)) {
        echo "<p style='color: green;'>✅ Log directory created successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create log directory</p>";
    }
} else {
    echo "<p>✅ Log directory exists: {$log_dir}</p>";
}

if (is_writable($log_dir)) {
    echo "<p style='color: green;'>✅ Log directory is writable</p>";
    
    // Test log writing
    $test_message = "[" . date('Y-m-d H:i:s') . "] [TEST] Auto-update system test\n";
    if (file_put_contents($log_file, $test_message, FILE_APPEND | LOCK_EX)) {
        echo "<p style='color: green;'>✅ Log writing test: SUCCESS</p>";
    } else {
        echo "<p style='color: red;'>❌ Log writing test: FAILED</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Log directory is not writable</p>";
}

// 7. System Requirements Check
echo "<h3>7. System Requirements Check</h3>";

$requirements = [
    'PHP Version' => version_compare(PHP_VERSION, '7.4.0', '>=') ? 'OK (' . PHP_VERSION . ')' : 'FAILED (need 7.4+)',
    'MySQLi Extension' => extension_loaded('mysqli') ? 'OK' : 'FAILED',
    'JSON Extension' => extension_loaded('json') ? 'OK' : 'FAILED',
    'Date Extension' => extension_loaded('date') ? 'OK' : 'FAILED'
];

foreach ($requirements as $req => $status) {
    $color = strpos($status, 'OK') !== false ? 'green' : 'red';
    $icon = strpos($status, 'OK') !== false ? '✅' : '❌';
    echo "<p style='color: {$color};'>{$icon} {$req}: {$status}</p>";
}

// 8. Configuration Summary
echo "<h3>8. Configuration Summary</h3>";

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>Database Host</td><td>" . ($db_host ?? 'Not set') . "</td></tr>";
echo "<tr><td>Database Name</td><td>" . ($db_name ?? 'Not set') . "</td></tr>";
echo "<tr><td>Timezone</td><td>" . date_default_timezone_get() . "</td></tr>";
echo "<tr><td>Current Date</td><td>" . date('Y-m-d H:i:s') . "</td></tr>";
echo "<tr><td>Auto-Update Helper</td><td>" . (file_exists(__DIR__ . '/config/auto_update_helper.php') ? 'Found' : 'Missing') . "</td></tr>";
echo "<tr><td>Cron Script</td><td>" . (file_exists(__DIR__ . '/cron/auto_update_training.php') ? 'Found' : 'Missing') . "</td></tr>";
echo "<tr><td>Monitor Dashboard</td><td>" . (file_exists(__DIR__ . '/admin/auto_update_monitor.php') ? 'Found' : 'Missing') . "</td></tr>";
echo "</table>";

// 9. Next Steps
echo "<h3>9. Next Steps</h3>";

echo "<div style='background-color: #e7f3ff; padding: 15px; border-left: 4px solid #2196F3; margin: 10px 0;'>";
echo "<h4>🚀 Setup Instructions:</h4>";
echo "<ol>";
echo "<li><strong>Setup Cron Job:</strong><br>";
echo "<code>1 0 * * * /usr/bin/php " . __DIR__ . "/cron/auto_update_training.php</code></li>";
echo "<li><strong>Monitor System:</strong><br>";
echo "<a href='admin/auto_update_monitor.php' target='_blank'>Auto-Update Monitor Dashboard</a></li>";
echo "<li><strong>Test Manual Run:</strong><br>";
echo "<code>php " . __DIR__ . "/cron/auto_update_training.php</code></li>";
echo "<li><strong>Check Logs:</strong><br>";
echo "<code>tail -f " . __DIR__ . "/logs/auto_update_cron.log</code></li>";
echo "</ol>";
echo "</div>";

echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0;'>";
echo "<h4>⚠️ Important Notes:</h4>";
echo "<ul>";
echo "<li>Sistem auto-update akan berjalan sekali per hari</li>";
echo "<li>Backup mechanism tersedia via application hook</li>";
echo "<li>Monitor dashboard hanya bisa diakses oleh admin</li>";
echo "<li>Log files akan di-rotate otomatis setiap 30 hari</li>";
echo "</ul>";
echo "</div>";

// 10. Cleanup
echo "<h3>10. Cleanup</h3>";
echo "<p>Setelah testing selesai, Anda bisa menghapus file ini:</p>";
echo "<code>rm " . __FILE__ . "</code>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h2 {
    color: #333;
    border-bottom: 2px solid #0066cc;
    padding-bottom: 10px;
}

h3 {
    color: #0066cc;
    margin-top: 30px;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

code {
    background-color: #f4f4f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

pre {
    background-color: #f4f4f4;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}
</style>
