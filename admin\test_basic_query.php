<?php
// Disable error display to prevent HTML output
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Set content type early
header('Content-Type: application/json');

// Start session
session_start();

// Include database connection
include '../config/config.php';

// Check authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Check database connection
if (!$conn) {
    echo json_encode(['error' => 'Database connection failed']);
    exit();
}

try {
    // Test 1: Check if table exists
    $table_check = "SHOW TABLES LIKE 'training_submissions'";
    $table_result = $conn->query($table_check);
    
    if ($table_result->num_rows == 0) {
        throw new Exception('Table training_submissions does not exist');
    }
    
    // Test 2: Get table structure
    $structure_query = "DESCRIBE training_submissions";
    $structure_result = $conn->query($structure_query);
    
    $columns = [];
    while ($col = $structure_result->fetch_assoc()) {
        $columns[] = $col['Field'] . ' (' . $col['Type'] . ')';
    }
    
    error_log("Table structure: " . implode(', ', $columns));
    
    // Test 3: Count total records
    $count_query = "SELECT COUNT(*) as total FROM training_submissions";
    $count_result = $conn->query($count_query);
    $total_count = $count_result->fetch_assoc()['total'];
    
    error_log("Total records in table: " . $total_count);
    
    if ($total_count == 0) {
        echo json_encode(['error' => 'No data found in training_submissions table', 'total_records' => 0]);
        exit();
    }
    
    // Test 4: Very basic query - just get first few records
    $basic_query = "SELECT id, full_name, training_topic, status FROM training_submissions ORDER BY id DESC LIMIT 5";
    $basic_result = $conn->query($basic_query);
    
    if (!$basic_result) {
        throw new Exception('Basic query failed: ' . $conn->error);
    }
    
    // Fetch data
    $trainings = [];
    while ($row = $basic_result->fetch_assoc()) {
        $trainings[] = [
            'id' => (int)$row['id'],
            'full_name' => $row['full_name'] ?? '',
            'training_topic' => $row['training_topic'] ?? '',
            'training_date' => null,
            'training_date_fixed' => null,
            'training_date_start' => null,
            'training_date_end' => null,
            'status' => $row['status'] ?? 'pending',
            'email' => '',
            'submission_date' => null,
            'requester_name' => ''
        ];
    }

    error_log("Basic query successful - Records found: " . count($trainings));

    // Return JSON response
    echo json_encode($trainings);
    
} catch (Exception $e) {
    // Log error and return error response
    error_log("test_basic_query.php error: " . $e->getMessage());
    echo json_encode(['error' => $e->getMessage()]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
