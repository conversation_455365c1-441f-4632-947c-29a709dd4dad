-- MySQL dump 10.13  Distrib 8.0.30, for Win64 (x86_64)
--
-- Host: localhost    Database: db_training
-- ------------------------------------------------------
-- Server version	8.0.30

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `training_submissions`
--

DROP TABLE IF EXISTS `training_submissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `training_submissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `training_topic` varchar(200) NOT NULL,
  `training_type` varchar(255) DEFAULT NULL,
  `provider_type` varchar(255) DEFAULT NULL,
  `trainer_name_internal` text,
  `trainer_nik_internal` text,
  `trainer_position_internal` text,
  `trainer_department_internal` text,
  `trainer_sub_department_internal` text,
  `provider_name` varchar(255) DEFAULT NULL,
  `provider_address` text,
  `trainer_name_external` varchar(100) DEFAULT NULL,
  `additional_info_provider` text,
  `training_date` date NOT NULL,
  `training_skill_type` varchar(50) DEFAULT 'Soft Skill',
  `training_date_fixed` date DEFAULT NULL,
  `training_place` varchar(255) DEFAULT NULL,
  `training_cost` varchar(255) DEFAULT NULL,
  `contact_person` varchar(100) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `sharing_knowledge` enum('Ya','Tidak') DEFAULT NULL,
  `additional_info` text,
  `submission_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('Pending','Approved','Rejected','Revise','Completed') DEFAULT 'Pending',
  `approved_by` int DEFAULT NULL,
  `canceled_by` int DEFAULT NULL,
  `canceled_at` datetime DEFAULT NULL,
  `current_approver_role_id` int DEFAULT '2',
  `next_approver_id` int DEFAULT NULL,
  `comments` text,
  `comments_dept_head` text,
  `approved_dept_head` enum('Pending','Approved','Rejected','Revise') DEFAULT 'Pending',
  `approved_hrd` enum('Pending','Approved','Rejected','Revise') DEFAULT 'Pending',
  `approved_ga` enum('Pending','Approved','Rejected','Revise') DEFAULT 'Pending',
  `approved_fm` enum('Pending','Approved','Rejected','Revise') DEFAULT 'Pending',
  `approved_dir` enum('Pending','Approved','Rejected','Revise') DEFAULT 'Pending',
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `comments_hrd` text COMMENT 'Komentar dari HRD terkait pengajuan pelatihan',
  `comments_ga` text COMMENT 'Komentar dari General Affairs (GA) terkait pengajuan pelatihan',
  `comments_fm` text,
  `comments_dir` text,
  `internal_memo_image` varchar(255) DEFAULT NULL,
  `training_time_start` time DEFAULT NULL,
  `training_time_end` time DEFAULT NULL,
  `nik` varchar(50) DEFAULT NULL,
  `jabatan` varchar(100) DEFAULT NULL,
  `bagian` varchar(100) DEFAULT NULL,
  `departemen` varchar(100) DEFAULT NULL,
  `training_date_start` date DEFAULT NULL,
  `training_date_end` date DEFAULT NULL,
  `rejected_at` datetime DEFAULT NULL COMMENT 'Waktu training ditolak',
  `rejected_by` int DEFAULT NULL COMMENT 'User ID yang menolak',
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int DEFAULT NULL,
  `assignment` text,
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_date` (`training_date`),
  KEY `fk_user_training` (`user_id`),
  KEY `idx_training_status` (`status`),
  KEY `fk_rejected_by` (`rejected_by`),
  KEY `fk_approved_by` (`approved_by`),
  KEY `idx_training_rejected_at` (`rejected_at`),
  KEY `idx_soft_delete` (`deleted_at`),
  KEY `idx_next_approver_id` (`next_approver_id`),
  CONSTRAINT `fk_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_rejected_by` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_user_training` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `training_submissions_ibfk_1` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`),
  CONSTRAINT `training_submissions_ibfk_2` FOREIGN KEY (`next_approver_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=115 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `training_submissions`
--

LOCK TABLES `training_submissions` WRITE;
/*!40000 ALTER TABLE `training_submissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `training_submissions` ENABLE KEYS */;
UNLOCK TABLES;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `before_insert_training_submissions` BEFORE INSERT ON `training_submissions` FOR EACH ROW BEGIN
    IF NEW.additional_info IS NULL OR TRIM(NEW.additional_info) = '' THEN
        SET NEW.additional_info = 'Tidak ada informasi tambahan';
    END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-23 22:13:52
