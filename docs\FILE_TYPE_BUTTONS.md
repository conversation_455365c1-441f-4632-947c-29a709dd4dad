# File Type Buttons Documentation

## Overview
Sistem training sekarang memiliki tombol yang berbeda untuk setiap jenis file berdasarkan ekstensi file. Setiap jenis file memiliki ikon, warna, dan aksi yang sesuai.

## Jenis File yang Didukung

### 📄 Documents (Dokumen)
| Ekstensi | Ikon | Warna | Label | Aksi | Keterangan |
|----------|------|-------|-------|------|------------|
| `.pdf` | 📄 `fa-file-pdf` | 🔴 Merah | PDF | View | Dapat dilihat langsung di browser |
| `.doc` | 📘 `fa-file-word` | 🔵 Biru | Word | Download | Microsoft Word (lama) |
| `.docx` | 📘 `fa-file-word` | 🔵 Biru | Word | Download | Microsoft Word (baru) |
| `.ppt` | 📙 `fa-file-powerpoint` | 🟡 Kuning | PowerPoint | Download | Microsoft PowerPoint (lama) |
| `.pptx` | 📙 `fa-file-powerpoint` | 🟡 Kuning | PowerPoint | Download | Microsoft PowerPoint (baru) |
| `.xls` | 📗 `fa-file-excel` | 🟢 Hijau | Excel | Download | Microsoft Excel (lama) |
| `.xlsx` | 📗 `fa-file-excel` | 🟢 Hijau | Excel | Download | Microsoft Excel (baru) |
| `.txt` | 📝 `fa-file-alt` | ⚫ Abu-abu | Text | View | File teks biasa |
| `.rtf` | 📝 `fa-file-alt` | ⚫ Abu-abu | RTF | Download | Rich Text Format |

### 🖼️ Images (Gambar)
| Ekstensi | Ikon | Warna | Label | Aksi | Keterangan |
|----------|------|-------|-------|------|------------|
| `.jpg` | 🖼️ `fa-file-image` | 🔵 Info | Gambar | View | JPEG Image |
| `.jpeg` | 🖼️ `fa-file-image` | 🔵 Info | Gambar | View | JPEG Image |
| `.png` | 🖼️ `fa-file-image` | 🔵 Info | Gambar | View | PNG Image |
| `.gif` | 🖼️ `fa-file-image` | 🔵 Info | Gambar | View | GIF Image |
| `.bmp` | 🖼️ `fa-file-image` | 🔵 Info | Gambar | View | Bitmap Image |
| `.webp` | 🖼️ `fa-file-image` | 🔵 Info | Gambar | View | WebP Image |
| `.svg` | 🖼️ `fa-file-image` | 🔵 Info | SVG | View | Scalable Vector Graphics |

### 🎥 Videos (Video)
| Ekstensi | Ikon | Warna | Label | Aksi | Keterangan |
|----------|------|-------|-------|------|------------|
| `.mp4` | 🎥 `fa-file-video` | ⚫ Hitam | Video | View | Dapat diputar di browser |
| `.webm` | 🎥 `fa-file-video` | ⚫ Hitam | Video | View | Dapat diputar di browser |
| `.avi` | 🎥 `fa-file-video` | ⚫ Hitam | Video | Download | Perlu aplikasi eksternal |
| `.mov` | 🎥 `fa-file-video` | ⚫ Hitam | Video | Download | QuickTime format |
| `.wmv` | 🎥 `fa-file-video` | ⚫ Hitam | Video | Download | Windows Media Video |
| `.mkv` | 🎥 `fa-file-video` | ⚫ Hitam | Video | Download | Matroska Video |
| `.flv` | 🎥 `fa-file-video` | ⚫ Hitam | Video | Download | Flash Video |

### 🎵 Audio (Audio)
| Ekstensi | Ikon | Warna | Label | Aksi | Keterangan |
|----------|------|-------|-------|------|------------|
| `.mp3` | 🎵 `fa-file-audio` | 🟣 Ungu | Audio | View | Dapat diputar di browser |
| `.wav` | 🎵 `fa-file-audio` | 🟣 Ungu | Audio | View | Dapat diputar di browser |
| `.ogg` | 🎵 `fa-file-audio` | 🟣 Ungu | Audio | View | Dapat diputar di browser |

### 📦 Archives (Arsip)
| Ekstensi | Ikon | Warna | Label | Aksi | Keterangan |
|----------|------|-------|-------|------|------------|
| `.zip` | 📦 `fa-file-archive` | ⚫ Abu-abu | ZIP | Download | File terkompresi ZIP |
| `.rar` | 📦 `fa-file-archive` | ⚫ Abu-abu | RAR | Download | File terkompresi RAR |
| `.7z` | 📦 `fa-file-archive` | ⚫ Abu-abu | 7Z | Download | File terkompresi 7-Zip |
| `.tar` | 📦 `fa-file-archive` | ⚫ Abu-abu | TAR | Download | File terkompresi TAR |
| `.gz` | 📦 `fa-file-archive` | ⚫ Abu-abu | GZ | Download | File terkompresi GZIP |

### ❓ Other (Lainnya)
| Ekstensi | Ikon | Warna | Label | Aksi | Keterangan |
|----------|------|-------|-------|------|------------|
| Lainnya | 📄 `fa-file` | ⚪ Outline | EKSTENSI | Download | File tidak dikenal |

## Jenis Aksi

### 👁️ View (Lihat)
- File dapat dilihat langsung di browser
- Tombol utama: "Lihat [Jenis File]"
- Tombol tambahan: Ikon download kecil untuk mengunduh file
- Contoh: PDF, gambar, video MP4, audio MP3

### ⬇️ Download (Unduh)
- File harus diunduh untuk dibuka
- Tombol utama: "Download [Jenis File]"
- Tidak ada tombol tambahan
- Contoh: Word, Excel, PowerPoint, arsip

## Implementasi

### File Helper
```php
// Include helper
include '../includes/file_type_helper.php';

// Generate buttons
echo generateFileButtons($material_id, $file_path);
```

### Fungsi Utama
- `getFileTypeInfo($file_path)` - Mendapatkan informasi jenis file
- `generateFileButtons($material_id, $file_path)` - Membuat HTML tombol
- `isFileViewable($file_path)` - Cek apakah file dapat dilihat
- `getFileCategory($file_path)` - Mendapatkan kategori file

### CSS Classes
- `.file-type-buttons` - Container untuk tombol
- `.btn-purple` - Warna ungu untuk audio
- Responsive design untuk mobile

## Contoh Penggunaan

### Dalam Template PHP
```php
<div class="d-flex justify-content-end file-type-buttons">
    <?php if ($material['type'] == 'link'): ?>
        <a href="<?= $material['external_url'] ?>" class="btn btn-primary" target="_blank">
            <i class="fas fa-external-link-alt"></i> Buka Tautan
        </a>
    <?php elseif (!empty($material['file_path'])): ?>
        <?= generateFileButtons($material['id'], $material['file_path']) ?>
    <?php endif; ?>
</div>
```

### Hasil HTML untuk PDF
```html
<a href="../download_material.php?id=123" class="btn btn-danger" target="_blank" title="Lihat PDF">
    <i class="fas fa-file-pdf"></i> Lihat PDF
</a>
<a href="../download_material.php?id=123&download=1" class="btn btn-outline-secondary btn-sm" title="Download File">
    <i class="fas fa-download"></i>
</a>
```

### Hasil HTML untuk Word
```html
<a href="../download_material.php?id=123" class="btn btn-primary" target="_blank" title="Download Word">
    <i class="fas fa-file-word"></i> Download Word
</a>
```

## Keunggulan

1. **User-Friendly**: Pengguna langsung tahu jenis file dan cara mengaksesnya
2. **Visual Clarity**: Ikon dan warna yang konsisten untuk setiap jenis file
3. **Responsive**: Tombol menyesuaikan dengan ukuran layar
4. **Extensible**: Mudah menambah jenis file baru
5. **Consistent**: Tampilan yang sama di seluruh aplikasi
6. **Accessible**: Tooltip dan label yang jelas

## File yang Terpengaruh

- `dept_head/classroom_detail.php` - Implementasi utama
- `includes/file_type_helper.php` - Helper functions
- `download_material.php` - Handler download dengan parameter
- `docs/FILE_TYPE_BUTTONS.md` - Dokumentasi ini

## Update Selanjutnya

Untuk menerapkan tombol file type ini ke halaman lain:

1. Include helper: `include '../includes/file_type_helper.php';`
2. Ganti kode tombol lama dengan: `<?= generateFileButtons($id, $file_path) ?>`
3. Tambahkan CSS class: `file-type-buttons`
4. Tambahkan CSS untuk `.btn-purple` jika diperlukan
