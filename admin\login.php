<?php
session_start();

// Cek apakah pengguna sudah login
if (isset($_SESSION['user_id'])) {
    if ($_SESSION['role_id'] == 99) { // Cek jika role_id adalah 99 (admin)
        header('Location: ../admin/index.php');
        exit();
    } else {
        header('Location: dashboard.php');
        exit();
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    include '../config/config.php';

    $nik = $_POST['nik'];
    $name = $_POST['name'];

    // Prepared statement untuk menghindari SQL injection
    $query = "SELECT * FROM users WHERE nik = ? AND name = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $nik, $name);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        
        // Simpan informasi pengguna ke dalam session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['name']; // Simpan nama pengguna
        $_SESSION['role_id'] = $user['role_id']; // Simpan role pengguna

        // Arahkan berdasarkan role_id
        if ($_SESSION['role_id'] == 99) { // Cek jika role_id adalah 99 (admin)
            header('Location: ../admin/index.php');
            exit();
        } else {
            header('Location: dashboard.php');
            exit();
        }
    } else {
        $error_message = "NIK atau nama salah.";
    }

    $stmt->close();
}
?>
<style>
    .login-container {
        width: 100%;
        max-width: 400px;
        margin: 50px auto;
        padding: 20px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .error-message {
        color: red;
        font-size: 14px;
    }
</style>
<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<body>
    <?php include '../config/navbarb.php';?>
    <div class="container-form">
        <div class="login-container">
            <h2>Login</h2>
            <?php if (isset($error_message)) : ?>
                <p class="error-message"><?php echo $error_message; ?></p>
            <?php endif; ?>
            <form method="POST">
                <label for="name">Nama Lengkap:</label>
                <input type="text" name="name" required>
                
                <label for="nik">NIK:</label>
                <input type="text" name="nik" required>

                <button type="submit">Login</button>
            </form>
            <p>Belum punya akun? <a href="signup.php">Daftar disini</a></p>
        </div>
    <?php include '../config/footer.php'; ?>
</body>
</html>
