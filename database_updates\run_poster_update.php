<?php
/**
 * <PERSON><PERSON><PERSON> to add poster fields to training tables
 * Run this once to update the database schema
 */

require_once '../config/config.php';

try {
    echo "Starting database update for poster fields...\n";
    
    // Check if offline_training.poster_image already exists
    $check_offline = $conn->query("SHOW COLUMNS FROM offline_training LIKE 'poster_image'");
    if ($check_offline->num_rows == 0) {
        echo "Adding poster_image field to offline_training table...\n";
        $sql1 = "ALTER TABLE `offline_training` 
                 ADD COLUMN `poster_image` VARCHAR(255) DEFAULT NULL COMMENT 'Path to training poster image' 
                 AFTER `training_description`";
        
        if ($conn->query($sql1) === TRUE) {
            echo "✓ Successfully added poster_image to offline_training\n";
        } else {
            echo "✗ Error adding poster_image to offline_training: " . $conn->error . "\n";
        }
    } else {
        echo "✓ poster_image field already exists in offline_training\n";
    }
    
    // Check if training_submissions.poster_image already exists
    $check_online = $conn->query("SHOW COLUMNS FROM training_submissions LIKE 'poster_image'");
    if ($check_online->num_rows == 0) {
        echo "Adding poster_image field to training_submissions table...\n";
        $sql2 = "ALTER TABLE `training_submissions` 
                 ADD COLUMN `poster_image` VARCHAR(255) DEFAULT NULL COMMENT 'Path to training poster image' 
                 AFTER `internal_memo_image`";
        
        if ($conn->query($sql2) === TRUE) {
            echo "✓ Successfully added poster_image to training_submissions\n";
        } else {
            echo "✗ Error adding poster_image to training_submissions: " . $conn->error . "\n";
        }
    } else {
        echo "✓ poster_image field already exists in training_submissions\n";
    }
    
    // Create upload directories
    $upload_dirs = [
        '../uploads/training_posters',
        '../uploads/training_posters/offline',
        '../uploads/training_posters/online'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (!file_exists($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "✓ Created directory: $dir\n";
            } else {
                echo "✗ Failed to create directory: $dir\n";
            }
        } else {
            echo "✓ Directory already exists: $dir\n";
        }
    }
    
    echo "\nDatabase update completed successfully!\n";
    echo "You can now upload posters for training events.\n";
    
} catch (Exception $e) {
    echo "✗ Error during database update: " . $e->getMessage() . "\n";
}

$conn->close();
?>
