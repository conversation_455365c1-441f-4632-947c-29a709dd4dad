/**
 * CSS untuk Search Training Dropdown dengan Suggestions
 */

/* Search Container */
.search-training-container {
    position: relative;
    width: 100%;
    max-width: 500px;
}

/* Search Input */
.search-training-input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-training-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
}

.search-training-input::placeholder {
    color: #999;
    font-style: italic;
}

/* Search Icon */
.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    pointer-events: none;
    z-index: 2;
}

/* Loading Spinner */
.search-loading {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
}

.search-loading .spinner-border {
    width: 20px;
    height: 20px;
    border-width: 2px;
}

/* Suggestions Dropdown */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    margin-top: 5px;
    display: none;
}

.search-suggestions.show {
    display: block;
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Suggestion Items */
.suggestion-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.active {
    background-color: #f8f9fa;
}

.suggestion-item.selected {
    background-color: #e3f2fd;
}

/* Suggestion Icon */
.suggestion-icon {
    flex-shrink: 0;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    margin-top: 2px;
}

.suggestion-icon.offline {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.suggestion-icon.online {
    background: linear-gradient(135deg, #007bff, #17a2b8);
}

/* Suggestion Content */
.suggestion-content {
    flex: 1;
    min-width: 0;
}

.suggestion-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
    line-height: 1.3;
}

.suggestion-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 4px;
}

.suggestion-meta-item {
    font-size: 12px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 4px;
}

.suggestion-meta-item i {
    font-size: 11px;
    opacity: 0.7;
}

.suggestion-description {
    font-size: 12px;
    color: #888;
    line-height: 1.4;
    margin-top: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Search Highlight */
.search-highlight {
    background-color: #fff3cd;
    color: #856404;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 600;
}

/* Type Badge */
.suggestion-type-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.suggestion-type-badge.offline {
    background-color: #d4edda;
    color: #155724;
}

.suggestion-type-badge.online {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* No Results */
.no-suggestions {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

.no-suggestions i {
    font-size: 24px;
    margin-bottom: 8px;
    opacity: 0.5;
}

/* Search Info */
.search-info {
    padding: 8px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    font-size: 12px;
    color: #666;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Filter Buttons */
.search-filters {
    display: flex;
    gap: 5px;
    margin-top: 10px;
}

.filter-btn {
    padding: 4px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-btn:hover {
    background-color: #f8f9fa;
}

.filter-btn.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

/* Responsive */
@media (max-width: 768px) {
    .search-training-container {
        max-width: 100%;
    }
    
    .suggestion-item {
        padding: 10px 12px;
        gap: 10px;
    }
    
    .suggestion-icon {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }
    
    .suggestion-title {
        font-size: 13px;
    }
    
    .suggestion-meta-item {
        font-size: 11px;
    }
}

/* Scrollbar Styling */
.search-suggestions::-webkit-scrollbar {
    width: 6px;
}

.search-suggestions::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.search-suggestions::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.search-suggestions::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
