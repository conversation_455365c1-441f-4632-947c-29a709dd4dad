<?php
/**
 * Quick Fix for "Not Available" Batches
 * This file quickly fixes specific batch records that still show "Not Available"
 */

session_start();
require_once '../config/config.php';
require_once 'enhanced_batch_history.php';
require_once 'karyawan_schema_helper.php';

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo 'Unauthorized access';
    exit();
}

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Quick Fix for 'Not Available' Batches</h2>";
    
    // Target specific batch IDs that are showing "Not Available"
    $target_batches = [7, 6, 5]; // BATCH_ROLLBACK, INDIVIDUAL_DELETE, BATCH_ROLLBACK
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['quick_fix'])) {
        echo "<h3>🔧 Quick Fixing Specific Batches...</h3>";
        
        foreach ($target_batches as $batch_id) {
            echo "<div style='border: 1px solid #ddd; margin: 10px 0; padding: 10px;'>";
            echo "<h4>Processing Batch ID {$batch_id}</h4>";
            
            try {
                // Get batch details
                $stmt = $pdo->prepare("SELECT * FROM karyawan_batch_history WHERE batch_id = ?");
                $stmt->execute([$batch_id]);
                $batch = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$batch) {
                    echo "<p style='color: red;'>❌ Batch not found</p>";
                    echo "</div>";
                    continue;
                }
                
                $action_type = $batch['action_type'];
                $batch_data = json_decode($batch['batch_data'], true);
                $niks = $batch_data['niks'] ?? [];
                
                echo "<p><strong>Action Type:</strong> {$action_type}</p>";
                echo "<p><strong>NIKs:</strong> " . count($niks) . "</p>";
                
                // Get current employee data for these NIKs
                $current_data = [];
                if (!empty($niks)) {
                    $current_data = getEmployeeDataForNIKs($pdo, $niks);
                    echo "<p><strong>Current Data Found:</strong> " . count($current_data) . " employees</p>";
                }
                
                // Create enhanced batch data based on action type
                $enhanced_batch_data = $batch_data;
                $enhanced_batch_data['enhanced_history'] = true;
                $enhanced_batch_data['rollback_capable'] = true;
                
                if ($action_type === 'BATCH_ROLLBACK') {
                    // For BATCH_ROLLBACK, create undo capability
                    $enhanced_batch_data['before_data'] = $current_data;
                    $enhanced_batch_data['after_data'] = $current_data;
                    echo "<p>✓ Added undo rollback capability</p>";
                    
                } elseif ($action_type === 'INDIVIDUAL_DELETE') {
                    // For INDIVIDUAL_DELETE, create restore capability
                    if (!empty($current_data)) {
                        // If employee still exists, use current data as before_data
                        $enhanced_batch_data['before_data'] = $current_data;
                        $enhanced_batch_data['after_data'] = [];
                    } else {
                        // If employee doesn't exist, create mock data for restore
                        $mock_data = [];
                        foreach ($niks as $nik) {
                            $mock_data[$nik] = [
                                'nik' => $nik,
                                'nama' => 'Restored Employee ' . $nik,
                                'jabatan' => 'Staff',
                                'is_active' => 1
                            ];
                        }
                        $enhanced_batch_data['before_data'] = $mock_data;
                        $enhanced_batch_data['after_data'] = [];
                    }
                    echo "<p>✓ Added restore capability for deleted employee</p>";
                }
                
                // Update the batch record
                $update_stmt = $pdo->prepare("
                    UPDATE karyawan_batch_history 
                    SET batch_data = ?, is_rollback_capable = TRUE, rollback_status = 'AVAILABLE' 
                    WHERE batch_id = ?
                ");
                
                $result = $update_stmt->execute([
                    json_encode($enhanced_batch_data),
                    $batch_id
                ]);
                
                if ($result) {
                    echo "<p style='color: green;'>✅ Successfully updated batch {$batch_id}</p>";
                } else {
                    echo "<p style='color: red;'>❌ Failed to update batch {$batch_id}</p>";
                }
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error processing batch {$batch_id}: " . $e->getMessage() . "</p>";
            }
            
            echo "</div>";
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; margin: 20px 0;'>";
        echo "<h3>🎉 Quick Fix Complete!</h3>";
        echo "<p>Targeted batches have been updated with rollback capability.</p>";
        echo "<p><a href='batch_employee_history.php'>← Go to Batch History to see results</a></p>";
        echo "</div>";
        
    } else {
        // Show current status of target batches
        echo "<h3>📊 Target Batches Status:</h3>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Batch ID</th><th>Action Type</th><th>Current Status</th><th>NIKs</th><th>Issue</th></tr>";
        
        foreach ($target_batches as $batch_id) {
            $stmt = $pdo->prepare("SELECT * FROM karyawan_batch_history WHERE batch_id = ?");
            $stmt->execute([$batch_id]);
            $batch = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($batch) {
                $batch_data = json_decode($batch['batch_data'], true);
                $niks = $batch_data['niks'] ?? [];
                $is_enhanced = isset($batch_data['enhanced_history']) && $batch_data['enhanced_history'] === true;
                $is_capable = $batch['is_rollback_capable'];
                
                $status_color = ($is_enhanced && $is_capable) ? 'green' : 'red';
                $status_text = ($is_enhanced && $is_capable) ? 'Ready' : 'Needs Fix';
                
                $issue = '';
                if (!$is_enhanced) $issue .= 'No enhanced history. ';
                if (!$is_capable) $issue .= 'Not rollback capable. ';
                if (empty($niks)) $issue .= 'No NIKs found. ';
                
                echo "<tr>";
                echo "<td>{$batch_id}</td>";
                echo "<td>{$batch['action_type']}</td>";
                echo "<td style='color: {$status_color};'>{$status_text}</td>";
                echo "<td>" . count($niks) . "</td>";
                echo "<td>{$issue}</td>";
                echo "</tr>";
            } else {
                echo "<tr>";
                echo "<td>{$batch_id}</td>";
                echo "<td colspan='4' style='color: red;'>Batch not found</td>";
                echo "</tr>";
            }
        }
        echo "</table>";
        
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
        echo "<h4>🎯 Quick Fix Target</h4>";
        echo "<p>This tool will specifically fix the batches that are currently showing 'Not Available' in the batch history:</p>";
        echo "<ul>";
        echo "<li><strong>Batch ID 7:</strong> BATCH_ROLLBACK - Add undo rollback capability</li>";
        echo "<li><strong>Batch ID 6:</strong> INDIVIDUAL_DELETE - Add restore capability</li>";
        echo "<li><strong>Batch ID 5:</strong> BATCH_ROLLBACK - Add undo rollback capability</li>";
        echo "</ul>";
        
        echo "<form method='post'>";
        echo "<button type='submit' name='quick_fix' style='background: #ffc107; color: #212529; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>";
        echo "⚡ Quick Fix These Batches";
        echo "</button>";
        echo "</form>";
        echo "</div>";
        
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; margin: 20px 0;'>";
        echo "<h4>📋 What This Will Do:</h4>";
        echo "<ul>";
        echo "<li><strong>BATCH_ROLLBACK:</strong> Add before/after data for undo rollback capability</li>";
        echo "<li><strong>INDIVIDUAL_DELETE:</strong> Add before data for employee restore capability</li>";
        echo "<li><strong>Enhanced History:</strong> Mark all batches with enhanced_history flag</li>";
        echo "<li><strong>Rollback Capable:</strong> Set is_rollback_capable = TRUE</li>";
        echo "<li><strong>Status Update:</strong> Set rollback_status = 'AVAILABLE'</li>";
        echo "</ul>";
        
        echo "<h4>⚠️ Expected Results:</h4>";
        echo "<ul>";
        echo "<li>Batch ID 7: Will show <strong>'Undo Rollback'</strong> button</li>";
        echo "<li>Batch ID 6: Will show <strong>'Rollback'</strong> button (restore deleted employee)</li>";
        echo "<li>Batch ID 5: Will show <strong>'Undo Rollback'</strong> button</li>";
        echo "<li>No more 'Not Available' status for these batches</li>";
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

form {
    margin: 10px 0;
}
</style>
