<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Tanggal dan waktu saat ini
$current_date = date("Y-m-d");
$current_datetime = date("Y-m-d H:i:s");
$formatted_datetime = date("d F Y, H:i:s");

// Sebelum mengupdate, periksa panjang kolom status di database
$check_length_query = "SELECT CHARACTER_MAXIMUM_LENGTH
                      FROM INFORMATION_SCHEMA.COLUMNS
                      WHERE TABLE_NAME = 'training_submissions'
                      AND COLUMN_NAME = 'status'";
$length_result = mysqli_query($conn, $check_length_query);
$column_length = mysqli_fetch_assoc($length_result)['CHARACTER_MAXIMUM_LENGTH'];

// Pastikan status yang akan diupdate sesuai dengan panjang kolom
$new_status = 'completed';
if (strlen($new_status) > $column_length) {
    // Log error atau tangani sesuai kebutuhan
    error_log("Status length exceeds column maximum length");
    // Gunakan nilai yang sesuai dengan panjang kolom
    $new_status = substr($new_status, 0, $column_length);
}

// Update query dengan prepared statement
$update_query = "UPDATE training_submissions
                SET status = ?
                WHERE start_date < ?
                AND status NOT IN ('completed', 'rejected')";
$stmt_update = $conn->prepare($update_query);
$stmt_update->bind_param("ss", $new_status, $current_date);

try {
    $stmt_update->execute();
} catch (mysqli_sql_exception $e) {
    error_log("Error updating status: " . $e->getMessage());
    // Handle error sesuai kebutuhan
}
$stmt_update->close();

// Ambil statistik pengajuan
$query_stats = "SELECT
                    COUNT(*) AS total_submissions,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) AS pending,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) AS approved,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) AS completed
                FROM training_submissions";
$result_stats = mysqli_query($conn, $query_stats);
$stats = $result_stats ? mysqli_fetch_assoc($result_stats) : [
    'total_submissions' => 0,
    'pending' => 0,
    'approved' => 0,
    'completed' => 0
];

// Inisialisasi variabel pencarian
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Ambil pengajuan terbaru dengan filter pencarian
$query_latest = "SELECT full_name, training_topic, start_date, status
                 FROM training_submissions
                 WHERE start_date >= ?
                 AND (full_name LIKE ? OR training_topic LIKE ? OR status LIKE ?)
                 ORDER BY start_date DESC";
$stmt = $conn->prepare($query_latest);
$search_param = "%" . $search . "%";
$stmt->bind_param("ssss", $current_date, $search_param, $search_param, $search_param);
$stmt->execute();
$result_latest = $stmt->get_result();
$latest_submissions = $result_latest->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Ambil data pengguna dari database
$user_id = $_SESSION['user_id'];
$query_user = "SELECT name FROM users WHERE id = ?";
$stmt = $conn->prepare($query_user);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result_user = $stmt->get_result();
$user = $result_user->fetch_assoc();
$stmt->close();
$username = $user['name'] ?? 'Pengguna';

// Query untuk statistik pengguna
$query_user_stats = "SELECT
    COUNT(DISTINCT id) as total_users,
    COUNT(DISTINCT CASE WHEN role_id = 1 THEN id END) as total_employees,
    COUNT(DISTINCT CASE WHEN role_id = 99 THEN id END) as total_admins
FROM users";
$result_user_stats = mysqli_query($conn, $query_user_stats);
$user_stats = $result_user_stats ? mysqli_fetch_assoc($result_user_stats) : [
    'total_users' => 0,
    'total_employees' => 0,
    'total_admins' => 0
];

// Query untuk statistik karyawan
$query_karyawan_stats = "SELECT
    COUNT(*) as total_karyawan,
    SUM(CASE WHEN jk = 'L' THEN 1 ELSE 0 END) as total_laki,
    SUM(CASE WHEN jk = 'P' THEN 1 ELSE 0 END) as total_perempuan,
    COUNT(DISTINCT dept) as total_departemen,
    COUNT(DISTINCT bagian) as total_bagian,
    COUNT(DISTINCT jabatan) as total_jabatan,
    COUNT(DISTINCT pt) as total_pt
FROM karyawan";
$result_karyawan_stats = mysqli_query($conn, $query_karyawan_stats);
$karyawan_stats = $result_karyawan_stats ? mysqli_fetch_assoc($result_karyawan_stats) : [
    'total_karyawan' => 0,
    'total_laki' => 0,
    'total_perempuan' => 0,
    'total_departemen' => 0,
    'total_bagian' => 0,
    'total_jabatan' => 0,
    'total_pt' => 0
];

// Query untuk statistik training
$query_training_stats = "SELECT
    COUNT(*) as total_trainings,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_trainings,
    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_trainings,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_trainings,
    SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_trainings,
    COUNT(DISTINCT training_topic) as unique_topics
FROM training_submissions";
$result_training_stats = mysqli_query($conn, $query_training_stats);
$training_stats = $result_training_stats ? mysqli_fetch_assoc($result_training_stats) : [
    'total_trainings' => 0,
    'pending_trainings' => 0,
    'approved_trainings' => 0,
    'completed_trainings' => 0,
    'rejected_trainings' => 0,
    'unique_topics' => 0
];

// Query untuk statistik aktivitas
$query_activity_stats = "SELECT
    COUNT(*) as total_activities,
    COUNT(DISTINCT user_id) as active_users,
    COUNT(DISTINCT DATE(timestamp)) as active_days,
    COUNT(DISTINCT CASE WHEN category = 'employee' THEN id END) as employee_activities,
    COUNT(DISTINCT CASE WHEN category = 'training' THEN id END) as training_activities
FROM activity_logs";
$result_activity_stats = mysqli_query($conn, $query_activity_stats);
$activity_stats = $result_activity_stats ? mysqli_fetch_assoc($result_activity_stats) : [
    'total_activities' => 0,
    'active_users' => 0,
    'active_days' => 0,
    'employee_activities' => 0,
    'training_activities' => 0
];

mysqli_close($conn); // Tutup koneksi setelah semua query selesai
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
/* Container styles */
.container-form {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    margin-top: 70px; /* Add top margin to prevent navbar overlap */
    position: relative;
    z-index: 1; /* Ensure it's above the navbar */
}

@media (max-width: 768px) {
    .container-form {
        padding: 15px;
        margin-top: 60px; /* Adjust for smaller screens */
    }
}

@media (max-width: 480px) {
    .container-form {
        padding: 10px;
        margin-top: 55px; /* Adjust for mobile screens */
    }
}

.admin-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.control-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s;
}

.control-card:hover {
    transform: translateY(-5px);
}

.control-card i {
    font-size: 2rem;
    color: #BF0000;
    margin-bottom: 15px;
}

.control-card h3 {
    margin-bottom: 10px;
    color: #333;
}

.control-card p {
    color: #666;
    margin-bottom: 15px;
}

.control-card .btn {
    background: #BF0000;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    transition: background 0.3s;
}

.control-card .btn:hover {
    background: #990000;
}

.stats-detail {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-detail h2 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
    color: #333;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.stats-detail h2::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 20px;
    background-color: #BF0000;
    margin-right: 10px;
    border-radius: 2px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 20px 15px;
    background: linear-gradient(135deg, #BF0000, #a50000);
    color: white;
    border-radius: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-item:hover::before {
    opacity: 1;
}

.stat-item i {
    margin-top: 10px;
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.stat-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 15px rgba(0,0,0,0.2);
}

.stat-item p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    margin-top: 5px;
}

.stat-item h4 {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin: 10px 0 5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.last-updated {
    color: #666;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    margin: 20px 0;
}

.last-updated i {
    margin-right: 5px;
    color: #BF0000;
}

.welcome-container {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #BF0000, #800000);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.welcome-container h1 {
    margin: 0;
    font-size: 2.5em;
    font-weight: 700;
}

.welcome-container p {
    margin: 10px 0 0;
    font-size: 1.2em;
    opacity: 0.9;
}

</style>
<body>
    <?php include '../config/navbar.php'; ?>
    <div class="container-form">
        <!-- Tampilkan pesan sukses jika ada -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success" style="background-color: #d4edda; color: #155724; padding: 15px; margin-bottom: 20px; border: 1px solid #c3e6cb; border-radius: 4px;">
                <?= htmlspecialchars($_SESSION['success']) ?>
                <?php unset($_SESSION['success']); // Hapus pesan setelah ditampilkan ?>
            </div>
        <?php endif; ?>

        <!-- Welcome section tetap sama -->
        <div class="welcome-section">
            <h1>👋 Selamat Datang, <span><?= htmlspecialchars($username ?? 'Admin') ?></span>!</h1>
            <p>Panel Kontrol Administrator</p>
        </div>

        <!-- Detailed Statistics -->
        <div class="stats-detail">
            <h2>Statistik Pengguna & Karyawan</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <h4><?= $user_stats['total_users'] ?></h4>
                    <p>Total Pengguna</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-user-tie"></i>
                    <h4><?= $karyawan_stats['total_karyawan'] ?></h4>
                    <p>Total Karyawan</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-male"></i>
                    <h4><?= $karyawan_stats['total_laki'] ?></h4>
                    <p>Karyawan Laki-laki</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-female"></i>
                    <h4><?= $karyawan_stats['total_perempuan'] ?></h4>
                    <p>Karyawan Perempuan</p>
                </div>
            </div>
        </div>

        <!-- Detailed Organization Statistics -->
        <div class="stats-detail">
            <h2>Statistik Organisasi</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <i class="fas fa-building"></i>
                    <h4><?= $karyawan_stats['total_pt'] ?></h4>
                    <p>Total Perusahaan</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-sitemap"></i>
                    <h4><?= $karyawan_stats['total_departemen'] ?></h4>
                    <p>Total Departemen</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-project-diagram"></i>
                    <h4><?= $karyawan_stats['total_bagian'] ?></h4>
                    <p>Total Bagian</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-id-badge"></i>
                    <h4><?= $karyawan_stats['total_jabatan'] ?></h4>
                    <p>Total Jabatan</p>
                </div>
            </div>
        </div>

        <!-- Training Statistics -->
        <div class="stats-detail">
            <h2>Statistik Training</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <i class="fas fa-file-alt"></i>
                    <h4><?= $training_stats['total_trainings'] ?></h4>
                    <p>Total Pengajuan</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <h4><?= $training_stats['pending_trainings'] ?></h4>
                    <p>Training Pending</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-check-circle"></i>
                    <h4><?= $training_stats['completed_trainings'] ?></h4>
                    <p>Training Selesai</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-book"></i>
                    <h4><?= $training_stats['unique_topics'] ?></h4>
                    <p>Topik Training Unik</p>
                </div>
            </div>
        </div>

        <!-- Activity Statistics -->
        <div class="stats-detail">
            <h2>Statistik Aktivitas</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <i class="fas fa-history"></i>
                    <h4><?= $activity_stats['total_activities'] ?></h4>
                    <p>Total Aktivitas</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-user-clock"></i>
                    <h4><?= $activity_stats['active_users'] ?></h4>
                    <p>Pengguna Aktif</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-calendar-check"></i>
                    <h4><?= $activity_stats['active_days'] ?></h4>
                    <p>Hari Aktif</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-user-edit"></i>
                    <h4><?= $activity_stats['employee_activities'] ?></h4>
                    <p>Aktivitas Karyawan</p>
                </div>
            </div>
        </div>

        <!-- Admin Control Cards -->
        <div class="admin-controls">

        <div class="control-card">
                <i class="fas fa-building"></i>
                <h3>Manajemen Organisasi</h3>
                <p>Kelola Organisasi</p>
                <a href="manage_organization.php" class="btn">Kelola</a>
            </div>

            <div class="control-card">
                <i class="fas fa-users"></i>
                <h3>Database Karyawan</h3>
                <p>Kelola data karyawan</p>
                <a href="employee_management.php" class="btn">Kelola</a>
            </div>

            <div class="control-card">
                <i class="fas fa-user-shield"></i>
                <h3>User Access</h3>
                <p>Kelola akun pengguna dan hak akses</p>
                <a href="manage_user.php" class="btn">Kelola</a>
            </div>

            <div class="control-card">
                <i class="fas fa-book"></i>
                <h3>Training Management</h3>
                <p>Kelola daftar dan kategori training</p>
                <a href="training_management.php" class="btn">Kelola</a>
            </div>

            <div class="control-card">
                <i class="fas fa-calendar-alt"></i>
                <h3>Training Calendar</h3>
                <p>Kelola kalender training</p>
                <a href="calendar_management.php" class="btn">Kelola</a>
            </div>

            <div class="control-card">
                <i class="fas fa-chart-bar"></i>
                <h3>Laporan Training</h3>
                <p>Lihat laporan dan statistik detail</p>
                <a href="reports_enhanced.php" class="btn">Lihat</a>
            </div>

            <div class="control-card">
                <i class="fas fa-cog"></i>
                <h3>Pengaturan Sistem</h3>
                <p>Konfigurasi sistem dan parameter</p>
                <a href="settings.php" class="btn">Atur</a>
            </div>

            <div class="control-card">
                <i class="fas fa-history"></i>
                <h3>Log Aktivitas</h3>
                <p>Pantau semua aktivitas pengguna</p>
                <a href="activity_logs.php" class="btn">Lihat</a>
            </div>

                <div class="control-card">
                        <i class="fas fa-bullhorn"></i>
                        <h3>Manajemen Pengumuman</h3>
                        <p>Kelola pengumuman</p>
                        <a href="manage_announcements.php" class="btn">Kelola</a>
                    </div>

                    <div class="control-card">
                        <i class="fas fa-chalkboard-teacher"></i>
                        <h3>Manajemen Kelas</h3>
                        <p>Kelola kelas training</p>
                        <a href="manage_classes.php" class="btn">Kelola</a>
                    </div>

                    <div class="control-card">
                        <i class="fas fa-id-card"></i>
                        <h3>Absensi RFID</h3>
                        <p>Kelola absensi training dengan RFID</p>
                        <a href="rfid_attendance_offline.php" class="btn">Kelola</a>
                    </div>
<!-- 
                    <div class="control-card">
                        <i class="fas fa-mobile-alt"></i>
                        <h3>Absensi NFC</h3>
                        <p>Absensi training dengan smartphone NFC</p>
                        <a href="../nfc_reader.php" class="btn">Akses</a>
                    </div> -->

                    <div class="control-card">
                        <i class="fas fa-credit-card"></i>
                        <h3>Kartu RFID</h3>
                        <p>Kelola nomor kartu RFID karyawan</p>
                        <a href="manage_card_numbers.php" class="btn">Kelola</a>
                    </div>

                                        <div class="control-card">
                                            <i class="fas fa-question"></i>
                                            <h3>Manajemen FAQ</h3>
                                            <p>Kelola FAQ atau pertanyaan umum</p>
                                            <a href="manage_faq.php" class="btn">Kelola</a>
                                        </div>
                                    </div>
                </div>
        </div>

        <!-- Last Updated Section -->
        <div class="last-updated">
            <i class="fas fa-sync-alt"></i> Terakhir diperbarui: <?= $formatted_datetime ?>
        </div>
    </div>
    <?php include '../config/footer.php'; ?>
</body>
</html>
