<?php
// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../../view/login.php');
    exit();
}

// Set header untuk download file Excel
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="Template_Upload_Karyawan.xlsx"');
header('Cache-Control: max-age=0');

// Require library PhpSpreadsheet
require '../../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// Buat spreadsheet baru
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('Template Karyawan');

// Definisikan header kolom
$columns = [
    'A' => 'NIK*',
    'B' => 'Nama*',
    'C' => 'Tgl Masuk* (Format: DD-MM-YYYY)',
    'D' => 'JK* (L/P)',
    'E' => 'Level Karyawan',
    'F' => 'Tgl Lahir (Format: DD-MM-YYYY)',
    'G' => 'Agama',
    'H' => 'Pendidikan Akhir',
    'I' => 'No. Telp',
    'J' => 'Dept',
    'K' => 'Bagian',
    'L' => 'Jabatan',
    'M' => 'Group',
    'N' => 'Status',
    'O' => 'PT'
];

// Set header kolom
foreach ($columns as $col => $value) {
    $sheet->setCellValue($col . '1', $value);
}

// Tambahkan contoh data
$exampleData = [
    ['305000181', 'Rizqi Ahsan Setiawan', '2025-01-02', 'L', '1', '31-01-2007', 'Islam', 'SMK', '0895602416781', 'HRGA', 'HRT', 'Helper GA', 'HRD', 'PKL', 'PT PAS'],
];

// Format kolom tanggal sebagai teks untuk mencegah Excel mengubah format
$dateColumns = ['C', 'F']; // Kolom tanggal masuk dan tanggal lahir

// Tambahkan contoh data ke sheet
$row = 2;
foreach ($exampleData as $data) {
    $col = 'A';
    foreach ($data as $value) {
        $sheet->setCellValue($col . $row, $value);
        $col++;
    }
    $row++;
}

// Tambahkan sheet kedua untuk petunjuk
$instructionSheet = $spreadsheet->createSheet();
$instructionSheet->setTitle('Petunjuk');

// Tambahkan petunjuk
$instructions = [
    ['Petunjuk Pengisian Template Upload Karyawan'],
    [''],
    ['1. Kolom yang ditandai dengan tanda bintang (*) adalah kolom yang wajib diisi.'],
    ['2. Format tanggal dapat berupa:'],
    ['   - DD-MM-YYYY (contoh: 01-01-2023)'],
    ['   - DD/MM/YYYY (contoh: 01/01/2023)'],
    ['   - Untuk mencegah Excel mengubah format tanggal, tambahkan tanda petik di awal (contoh: \'01-01-2023)'],
    ['3. Kolom JK hanya boleh diisi dengan "L" untuk Laki-laki atau "P" untuk Perempuan.'],
    ['4. NIK harus berupa angka dan unik (tidak boleh sama dengan NIK yang sudah ada).'],
    ['5. PENTING: Untuk NIK yang panjang (lebih dari 12 digit), tambahkan tanda petik di awal (contoh: \'1234567890123456) untuk mencegah Excel mengubah format menjadi notasi ilmiah.'],
    ['6. Pastikan tidak ada spasi di awal atau akhir data.'],
    ['7. Jangan mengubah nama kolom atau urutan kolom.'],
    ['8. Jangan menambahkan kolom baru.'],
    ['9. Jangan menghapus baris contoh, tambahkan data baru di bawah contoh.'],
    ['10. Setelah selesai mengisi, simpan file dan upload melalui menu "Upload Karyawan".'],
    [''],
    ['Keterangan Kolom:'],
    [''],
    ['NIK*', 'Nomor Induk Karyawan, wajib diisi, harus berupa angka dan unik. Untuk NIK yang panjang, tambahkan tanda petik di awal (contoh: \'1234567890123456).'],
    ['Nama*', 'Nama lengkap karyawan, wajib diisi.'],
    ['Tgl Masuk*', 'Tanggal masuk karyawan, wajib diisi. Format yang didukung: DD-MM-YYYY, DD/MM/YYYY. Tambahkan tanda petik di awal untuk mencegah perubahan format.'],
    ['JK*', 'Jenis Kelamin, wajib diisi, isi dengan "L" untuk Laki-laki atau "P" untuk Perempuan.'],
    ['Level Karyawan', 'Level karyawan, opsional, contoh: 1, 2, 3, 4.'],
    ['Tgl Lahir', 'Tanggal lahir karyawan, opsional. Format yang didukung: DD-MM-YYYY, DD/MM/YYYY. Tambahkan tanda petik di awal untuk mencegah perubahan format.'],
    ['Agama', 'Agama karyawan, opsional.'],
    ['Pendidikan Akhir', 'Pendidikan terakhir karyawan, opsional.'],
    ['No. Telp', 'Nomor telepon karyawan, opsional.'],
    ['Dept', 'Departemen karyawan, opsional.'],
    ['Bagian', 'Bagian karyawan, opsional.'],
    ['Jabatan', 'Jabatan karyawan, opsional.'],
    ['Group', 'Group karyawan, opsional.'],
    ['Status', 'Status karyawan, opsional, contoh: Aktif, Tidak Aktif, Resign, Habis Kontrak.'],
    ['PT', 'Nama perusahaan, opsional.']
];

// Tambahkan petunjuk ke sheet
$row = 1;
foreach ($instructions as $instruction) {
    if (count($instruction) == 1) {
        $instructionSheet->setCellValue('A' . $row, $instruction[0]);
        if ($row == 1) {
            // Judul petunjuk
            $instructionSheet->mergeCells('A1:B1');
            $instructionSheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        }
    } else if (count($instruction) == 2) {
        $instructionSheet->setCellValue('A' . $row, $instruction[0]);
        $instructionSheet->setCellValue('B' . $row, $instruction[1]);
    }
    $row++;
}

// Set lebar kolom
foreach (range('A', 'O') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// Set style untuk header
$headerStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FFFFFF'],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'BF0000'],
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['rgb' => '000000'],
        ],
    ],
];

$sheet->getStyle('A1:O1')->applyFromArray($headerStyle);
$sheet->getRowDimension(1)->setRowHeight(30);

// Set style untuk data
$dataStyle = [
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['rgb' => '000000'],
        ],
    ],
    'alignment' => [
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
];

$sheet->getStyle('A2:O' . ($row - 1))->applyFromArray($dataStyle);

// Format kolom tanggal sebagai teks
foreach ($dateColumns as $col) {
    // Format header dengan warna berbeda untuk menekankan format tanggal
    $sheet->getStyle($col . '1')->getFill()->setFillType(Fill::FILL_SOLID)->setStartColor(new \PhpOffice\PhpSpreadsheet\Style\Color('FFEB3B'));

    // Format kolom sebagai teks untuk semua baris data
    for ($i = 2; $i <= $row; $i++) {
        // Set format sebagai teks
        $sheet->getStyle($col . $i)->getNumberFormat()->setFormatCode('@');

        // Tambahkan tanda petik di awal untuk memaksa Excel memperlakukan sebagai teks
        $currentValue = $sheet->getCell($col . $i)->getValue();
        if ($currentValue && substr($currentValue, 0, 1) !== "'") {
            $sheet->getCell($col . $i)->setValue("'" . $currentValue);
        }
    }

    // Tambahkan validasi data untuk kolom tanggal
    $validation = $sheet->getCell($col . '2')->getDataValidation();
    $validation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_CUSTOM);
    $validation->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_STOP);
    $validation->setAllowBlank(false);
    $validation->setShowInputMessage(true);
    $validation->setShowErrorMessage(true);
    $validation->setErrorTitle('Format Tanggal Salah');
    $validation->setError('Tanggal harus dalam format DD-MM-YYYY, contoh: 01-01-2023');
    $validation->setPromptTitle('Format Tanggal');
    $validation->setPrompt('Masukkan tanggal dalam format DD-MM-YYYY, contoh: 01-01-2023');
    $validation->setFormula1('LEN(TRIM(CLEAN(' . $col . '2)))=10');

    // Terapkan validasi ke seluruh kolom
    $sheet->setDataValidation($col . '2:' . $col . '1000', $validation);
}

// Set style untuk petunjuk
$instructionSheet->getColumnDimension('A')->setWidth(15);
$instructionSheet->getColumnDimension('B')->setWidth(80);

// Freeze pane pada header
$sheet->freezePane('A2');

// Aktifkan filter
$sheet->setAutoFilter('A1:O1');

// Set sheet pertama sebagai active sheet
$spreadsheet->setActiveSheetIndex(0);

// Tulis ke output
$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit;
