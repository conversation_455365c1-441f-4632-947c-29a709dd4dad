<?php
/**
 * Simplified API untuk mengambil detail training - untuk debugging
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Include necessary files
require_once '../../config/config.php';
require_once '../security.php';

try {
    // Get parameters
    $training_id = $_GET['id'] ?? '';
    $training_type = $_GET['type'] ?? '';

    if (empty($training_id) || empty($training_type)) {
        throw new Exception('Missing parameters');
    }

    $training_id = (int)$training_id;
    $training_detail = null;

    if ($training_type === 'offline') {
        // Simple query for offline training
        $query = "SELECT
                    id,
                    training_topic as title,
                    training_description as description,
                    start_date as date,
                    end_date,
                    location,
                    trainer_name as trainer,
                    status
                  FROM offline_training
                  WHERE id = ?";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $training_detail = $result->fetch_assoc();
            $training_detail['type'] = 'offline';

            // Get participants from offline_training_attendance table with karyawan data
            $participants_query = "SELECT DISTINCT
                                    ota.nik,
                                    ota.nama as name,
                                    COALESCE(k.dept, 'N/A') as dept,
                                    COALESCE(k.jabatan, 'N/A') as jabatan,
                                    COALESCE(k.bagian, 'N/A') as bagian,
                                    ota.status,
                                    ota.check_in,
                                    ota.check_out,
                                    DATE(ota.check_in) as attendance_date
                                  FROM offline_training_attendance ota
                                  LEFT JOIN karyawan k ON ota.nik = k.nik
                                  WHERE ota.offline_training_id = ?
                                  ORDER BY ota.nama ASC, ota.check_in DESC";

            $participants_stmt = $conn->prepare($participants_query);
            $participants_stmt->bind_param("i", $training_id);
            $participants_stmt->execute();
            $participants_result = $participants_stmt->get_result();

            $participants = [];
            $participant_summary = [];

            while ($participant = $participants_result->fetch_assoc()) {
                // For multi-day training, group by participant
                $participant_key = $participant['nik'] . '_' . $participant['name'];

                if (!isset($participant_summary[$participant_key])) {
                    $participant_summary[$participant_key] = [
                        'nik' => $participant['nik'],
                        'name' => $participant['name'],
                        'dept' => $participant['dept'] ?: 'N/A',
                        'jabatan' => $participant['jabatan'] ?: 'N/A',
                        'bagian' => $participant['bagian'] ?: 'N/A',
                        'status' => $participant['status'],
                        'attendance_days' => []
                    ];
                }

                // Add attendance day info
                if ($participant['attendance_date']) {
                    $participant_summary[$participant_key]['attendance_days'][] = [
                        'date' => $participant['attendance_date'],
                        'check_in' => $participant['check_in'],
                        'check_out' => $participant['check_out'],
                        'status' => $participant['status']
                    ];
                }
            }

            // Convert to array and add attendance summary
            foreach ($participant_summary as $participant) {
                $attendance_count = count($participant['attendance_days']);
                $participant['attendance_summary'] = $attendance_count > 0 ?
                    "Hadir {$attendance_count} hari" : "Belum hadir";
                $participants[] = $participant;
            }

            $training_detail['participants'] = $participants;
            $participants_stmt->close();

            // Store raw dates for JavaScript processing
            $raw_start_date = $training_detail['date'];
            $raw_end_date = $training_detail['end_date'];

            // Format date for multi-day
            if ($training_detail['date']) {
                $formatted_date = date('d M Y', strtotime($raw_start_date));
                if (!empty($raw_end_date) && $raw_end_date !== $raw_start_date) {
                    $formatted_date .= ' - ' . date('d M Y', strtotime($raw_end_date));
                    $training_detail['is_multiday'] = true;

                    // Calculate total days using raw dates
                    $start = new DateTime($raw_start_date);
                    $end = new DateTime($raw_end_date);
                    $interval = $start->diff($end);
                    $training_detail['total_days'] = $interval->days + 1;
                } else {
                    $training_detail['is_multiday'] = false;
                    $training_detail['total_days'] = 1;
                }

                // Set formatted date and keep raw dates
                $training_detail['date'] = $formatted_date;
                $training_detail['raw_date'] = $raw_start_date;
                $training_detail['raw_end_date'] = $raw_end_date;
            }
        }
        $stmt->close();

    } elseif ($training_type === 'online') {
        // Simple query for online training
        $query = "SELECT
                    id,
                    training_topic as title,
                    additional_info as description,
                    start_date as date,
                    end_date,
                    training_place as location,
                    contact_person as trainer,
                    status
                  FROM training_submissions
                  WHERE id = ?";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $training_detail = $result->fetch_assoc();
            $training_detail['type'] = 'online';
            $training_detail['location'] = $training_detail['location'] ?: 'Online';

            // Get participants from participants table
            $participants_query = "SELECT
                                    nik_participants as nik,
                                    nama_participants as name,
                                    departemen_participants as dept,
                                    jabatan_participants as jabatan,
                                    bagian_participants as bagian
                                  FROM participants
                                  WHERE training_id = ?
                                  ORDER BY nama_participants ASC";

            $participants_stmt = $conn->prepare($participants_query);
            $participants_stmt->bind_param("i", $training_id);
            $participants_stmt->execute();
            $participants_result = $participants_stmt->get_result();

            $participants = [];
            while ($participant = $participants_result->fetch_assoc()) {
                $participants[] = [
                    'nik' => $participant['nik'],
                    'name' => $participant['name'],
                    'dept' => $participant['dept'] ?: 'N/A',
                    'jabatan' => $participant['jabatan'] ?: 'N/A',
                    'bagian' => $participant['bagian'] ?: 'N/A',
                    'status' => 'Peserta',
                    'attendance_summary' => 'Online Training'
                ];
            }

            $training_detail['participants'] = $participants;
            $participants_stmt->close();

            // Store raw dates for JavaScript processing
            $raw_start_date = $training_detail['date'];
            $raw_end_date = $training_detail['end_date'];

            // Format date for multi-day
            if ($training_detail['date']) {
                $formatted_date = date('d M Y', strtotime($raw_start_date));
                if (!empty($raw_end_date) && $raw_end_date !== $raw_start_date) {
                    $formatted_date .= ' - ' . date('d M Y', strtotime($raw_end_date));
                    $training_detail['is_multiday'] = true;

                    // Calculate total days using raw dates
                    $start = new DateTime($raw_start_date);
                    $end = new DateTime($raw_end_date);
                    $interval = $start->diff($end);
                    $training_detail['total_days'] = $interval->days + 1;
                } else {
                    $training_detail['is_multiday'] = false;
                    $training_detail['total_days'] = 1;
                }

                // Set formatted date and keep raw dates
                $training_detail['date'] = $formatted_date;
                $training_detail['raw_date'] = $raw_start_date;
                $training_detail['raw_end_date'] = $raw_end_date;
            }
        }
        $stmt->close();
    }

    if (!$training_detail) {
        throw new Exception('Training not found');
    }

    echo json_encode([
        'success' => true,
        'training' => $training_detail
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

if (isset($conn)) {
    $conn->close();
}
?>
