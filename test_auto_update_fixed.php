<?php
/**
 * Quick Test untuk Auto-Update Training Status System (Fixed Version)
 * 
 * Test cepat setelah perbaikan database schema compatibility
 */

require_once 'config/config.php';
require_once 'config/auto_update_helper.php';

echo "<h2>🔧 Quick Test Auto-Update System (Fixed)</h2>";

// 1. Test Database Connection
echo "<h3>1. Database Connection</h3>";
try {
    $test_result = $conn->query("SELECT 1");
    if ($test_result) {
        echo "<p style='color: green;'>✅ Database connection: OK</p>";
    } else {
        echo "<p style='color: red;'>❌ Database connection: FAILED</p>";
        exit(1);
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
    exit(1);
}

// 2. Check Table Schema
echo "<h3>2. Table Schema Check</h3>";

// Check training_submissions columns
$columns_query = "SHOW COLUMNS FROM training_submissions";
$columns_result = $conn->query($columns_query);
$available_columns = [];

if ($columns_result) {
    while ($row = $columns_result->fetch_assoc()) {
        $available_columns[] = $row['Field'];
    }
    echo "<p>✅ training_submissions columns: " . implode(', ', $available_columns) . "</p>";
    
    // Check for required columns
    $required_columns = ['status', 'start_date', 'end_date', 'canceled_at'];
    $missing_columns = [];
    
    foreach ($required_columns as $col) {
        if (!in_array($col, $available_columns)) {
            $missing_columns[] = $col;
        }
    }
    
    if (empty($missing_columns)) {
        echo "<p style='color: green;'>✅ All required columns present</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Missing columns: " . implode(', ', $missing_columns) . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Could not check table schema</p>";
}

// 3. Test Auto-Update Function
echo "<h3>3. Auto-Update Function Test</h3>";

// Reset session untuk testing
if (isset($_SESSION['last_auto_update'])) {
    unset($_SESSION['last_auto_update']);
}

try {
    echo "<p>🔄 Running auto-update test...</p>";
    $results = autoUpdateTrainingStatus($conn);
    
    if ($results['success']) {
        echo "<p style='color: green;'>✅ Auto-update test: SUCCESS</p>";
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>";
        echo "<h4>📊 Results:</h4>";
        echo "<ul>";
        echo "<li><strong>Approved → Completed:</strong> {$results['approved_to_completed']}</li>";
        echo "<li><strong>Pending → Canceled:</strong> {$results['pending_to_canceled']}</li>";
        echo "<li><strong>Total Updated:</strong> {$results['total_updated']}</li>";
        echo "</ul>";
        
        if (!empty($results['details'])) {
            echo "<h4>📝 Details:</h4>";
            echo "<ul>";
            foreach ($results['details'] as $detail) {
                echo "<li>" . htmlspecialchars($detail) . "</li>";
            }
            echo "</ul>";
        }
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>❌ Auto-update test: FAILED</p>";
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
        echo "<h4>❌ Error Details:</h4>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($results['message']) . "</p>";
        
        if (!empty($results['errors'])) {
            echo "<h4>🐛 Errors:</h4>";
            echo "<ul>";
            foreach ($results['errors'] as $error) {
                echo "<li style='color: red;'>" . htmlspecialchars($error) . "</li>";
            }
            echo "</ul>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Auto-update test exception: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 4. Check Current Training Data
echo "<h3>4. Current Training Data</h3>";

$today = date('Y-m-d');

$data_queries = [
    'Total Active Training' => "SELECT COUNT(*) as count FROM training_submissions WHERE status IN ('Approved', 'Pending', 'Completed')",
    'Approved Training' => "SELECT COUNT(*) as count FROM training_submissions WHERE status = 'Approved'",
    'Pending Training' => "SELECT COUNT(*) as count FROM training_submissions WHERE status = 'Pending'", 
    'Completed Training' => "SELECT COUNT(*) as count FROM training_submissions WHERE status = 'Completed'",
    'Training with Past Dates' => "SELECT COUNT(*) as count FROM training_submissions WHERE start_date < '$today' AND status = 'Approved'"
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Category</th><th>Count</th></tr>";

foreach ($data_queries as $description => $query) {
    try {
        $result = $conn->query($query);
        $count = $result->fetch_assoc()['count'];
        echo "<tr><td>{$description}</td><td><strong>{$count}</strong></td></tr>";
    } catch (Exception $e) {
        echo "<tr><td>{$description}</td><td style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</td></tr>";
    }
}
echo "</table>";

// 5. Test Cron Script
echo "<h3>5. Cron Script Test</h3>";

$cron_script = __DIR__ . '/cron/auto_update_training.php';

if (file_exists($cron_script)) {
    echo "<p>✅ Cron script exists</p>";
    
    // Test syntax
    $output = [];
    $return_var = 0;
    exec("php -l \"$cron_script\" 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "<p style='color: green;'>✅ Cron script syntax: OK</p>";
    } else {
        echo "<p style='color: red;'>❌ Cron script syntax error:</p>";
        echo "<pre>" . implode("\n", $output) . "</pre>";
    }
} else {
    echo "<p style='color: red;'>❌ Cron script not found</p>";
}

// 6. System Status Summary
echo "<h3>6. System Status Summary</h3>";

$status_items = [
    'Database Connection' => $test_result ? '✅ OK' : '❌ Failed',
    'Auto-Update Helper' => function_exists('autoUpdateTrainingStatus') ? '✅ Loaded' : '❌ Missing',
    'Required Columns' => empty($missing_columns) ? '✅ Present' : '⚠️ Missing: ' . implode(', ', $missing_columns),
    'Cron Script' => file_exists($cron_script) ? '✅ Found' : '❌ Missing',
    'Auto-Update Function' => $results['success'] ? '✅ Working' : '❌ Failed'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Component</th><th>Status</th></tr>";

foreach ($status_items as $component => $status) {
    $color = strpos($status, '✅') !== false ? 'green' : (strpos($status, '⚠️') !== false ? 'orange' : 'red');
    echo "<tr><td>{$component}</td><td style='color: {$color};'>{$status}</td></tr>";
}
echo "</table>";

// 7. Next Steps
echo "<h3>7. Next Steps</h3>";

if ($results['success']) {
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>";
    echo "<h4>🎉 System Ready!</h4>";
    echo "<p>Auto-update system is working correctly. You can now:</p>";
    echo "<ol>";
    echo "<li><strong>Setup Cron Job:</strong><br><code>1 0 * * * /usr/bin/php " . __DIR__ . "/cron/auto_update_training.php</code></li>";
    echo "<li><strong>Monitor System:</strong><br><a href='admin/auto_update_monitor.php'>Auto-Update Monitor Dashboard</a></li>";
    echo "<li><strong>Delete Test Files:</strong><br><code>rm test_auto_update.php test_auto_update_fixed.php</code></li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
    echo "<h4>🔧 Troubleshooting Needed</h4>";
    echo "<p>There are still issues with the auto-update system. Please:</p>";
    echo "<ol>";
    echo "<li>Check the error messages above</li>";
    echo "<li>Verify database schema matches requirements</li>";
    echo "<li>Run <a href='reset_auto_update_session.php'>reset session tool</a> if needed</li>";
    echo "<li>Contact system administrator if issues persist</li>";
    echo "</ol>";
    echo "</div>";
}

// 8. Cleanup
echo "<h3>8. Cleanup</h3>";
echo "<p>After testing, you can delete this file:</p>";
echo "<code>rm " . __FILE__ . "</code>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h2 {
    color: #333;
    border-bottom: 2px solid #0066cc;
    padding-bottom: 10px;
}

h3 {
    color: #0066cc;
    margin-top: 30px;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

code {
    background-color: #f4f4f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

pre {
    background-color: #f4f4f4;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}
</style>
