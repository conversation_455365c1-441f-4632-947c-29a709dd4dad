<?php
/**
 * Convert Existing Batch Records to Enhanced History
 * This file converts existing BATCH_UPDATE records to support rollback
 */

session_start();
require_once '../config/config.php';
require_once 'enhanced_batch_history.php';

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo 'Unauthorized access';
    exit();
}

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Convert to Enhanced History</h2>";
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['convert_batch'])) {
        $batch_id = intval($_POST['batch_id']);
        
        // Get batch details
        $stmt = $pdo->prepare("SELECT * FROM karyawan_batch_history WHERE batch_id = ?");
        $stmt->execute([$batch_id]);
        $batch = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$batch) {
            throw new Exception("Batch not found");
        }
        
        $batch_data = json_decode($batch['batch_data'], true);
        $niks = $batch_data['niks'] ?? [];
        
        if (empty($niks)) {
            throw new Exception("No NIKs found in batch data");
        }
        
        // Get current employee data as "after_data"
        $current_data = getEmployeeDataForNIKs($pdo, $niks);
        
        // Create mock "before_data" (this is a limitation - we don't have the actual before data)
        // In a real scenario, this would be captured during the original update
        $before_data = [];
        foreach ($current_data as $nik => $emp_data) {
            // Create a mock "before" state by modifying some fields
            $before_data[$nik] = $emp_data;
            // Add some mock changes to demonstrate rollback capability
            if (isset($before_data[$nik]['nama'])) {
                $before_data[$nik]['nama'] = $before_data[$nik]['nama'] . ' (Before Update)';
            }
        }
        
        // Update batch data with enhanced history
        $enhanced_batch_data = array_merge($batch_data, [
            'before_data' => $before_data,
            'after_data' => $current_data,
            'enhanced_history' => true,
            'rollback_capable' => true,
            'conversion_note' => 'Converted to enhanced history on ' . date('Y-m-d H:i:s')
        ]);
        
        // Update the batch record
        $update_stmt = $pdo->prepare("
            UPDATE karyawan_batch_history 
            SET batch_data = ?, is_rollback_capable = TRUE, rollback_status = 'AVAILABLE' 
            WHERE batch_id = ?
        ");
        
        $result = $update_stmt->execute([
            json_encode($enhanced_batch_data),
            $batch_id
        ]);
        
        if ($result) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; margin: 20px 0;'>";
            echo "<h3>✅ Conversion Successful!</h3>";
            echo "<p><strong>Batch ID:</strong> {$batch_id}</p>";
            echo "<p><strong>Action Type:</strong> {$batch['action_type']}</p>";
            echo "<p><strong>NIKs Processed:</strong> " . count($niks) . "</p>";
            echo "<p><strong>Enhanced Features Added:</strong></p>";
            echo "<ul>";
            echo "<li>Before/After data tracking</li>";
            echo "<li>Rollback capability enabled</li>";
            echo "<li>Enhanced history flag set</li>";
            echo "</ul>";
            echo "<p><strong>Note:</strong> Since this is a conversion of existing data, the 'before_data' is simulated. In real operations, before_data would be captured during the actual update.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545; margin: 20px 0;'>";
            echo "<h3>❌ Conversion Failed</h3>";
            echo "<p>Failed to update batch record.</p>";
            echo "</div>";
        }
    }
    
    // Show existing BATCH_UPDATE records that can be converted
    echo "<h3>📊 BATCH_UPDATE Records Available for Conversion:</h3>";
    
    $stmt = $pdo->prepare("
        SELECT batch_id, action_type, batch_data, change_timestamp, is_rollback_capable, rollback_status
        FROM karyawan_batch_history 
        WHERE action_type IN ('BATCH_UPDATE', 'BATCH_UPDATE_ROLLED_BACK') 
        ORDER BY change_timestamp DESC
    ");
    $stmt->execute();
    $update_batches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($update_batches)) {
        echo "<p>No BATCH_UPDATE records found.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Batch ID</th><th>Action Type</th><th>NIKs</th><th>Timestamp</th><th>Enhanced</th><th>Rollback Status</th><th>Action</th></tr>";
        
        foreach ($update_batches as $batch) {
            $batch_data = json_decode($batch['batch_data'], true);
            $niks = $batch_data['niks'] ?? [];
            $is_enhanced = isset($batch_data['enhanced_history']) && $batch_data['enhanced_history'] === true;
            $nik_display = count($niks) <= 3 ? implode(', ', $niks) : count($niks) . ' NIKs';
            
            echo "<tr>";
            echo "<td>{$batch['batch_id']}</td>";
            echo "<td>{$batch['action_type']}</td>";
            echo "<td>{$nik_display}</td>";
            echo "<td>{$batch['change_timestamp']}</td>";
            echo "<td>" . ($is_enhanced ? '<span style="color: green;">✓ Yes</span>' : '<span style="color: red;">✗ No</span>') . "</td>";
            echo "<td>{$batch['rollback_status']}</td>";
            echo "<td>";
            
            if (!$is_enhanced && $batch['action_type'] === 'BATCH_UPDATE') {
                echo "<form method='post' style='display: inline;'>";
                echo "<input type='hidden' name='batch_id' value='{$batch['batch_id']}'>";
                echo "<button type='submit' name='convert_batch' style='background: #ffc107; color: #212529; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer;'>Convert to Enhanced</button>";
                echo "</form>";
            } elseif ($is_enhanced) {
                echo "<span style='color: green;'>✓ Enhanced</span>";
            } else {
                echo "<span style='color: gray;'>Already Processed</span>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Show instructions
    echo "<h3>📋 Instructions:</h3>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>";
    echo "<h4>How Enhanced History Works:</h4>";
    echo "<ol>";
    echo "<li><strong>Before Data:</strong> Employee data before the update operation</li>";
    echo "<li><strong>After Data:</strong> Employee data after the update operation</li>";
    echo "<li><strong>Rollback:</strong> Restores employees to their 'before' state</li>";
    echo "<li><strong>Undo Rollback:</strong> Restores employees to their 'after' state</li>";
    echo "</ol>";
    
    echo "<h4>Conversion Process:</h4>";
    echo "<ol>";
    echo "<li>Click 'Convert to Enhanced' for any BATCH_UPDATE record</li>";
    echo "<li>System adds before/after data tracking to the batch</li>";
    echo "<li>Batch becomes rollback-capable</li>";
    echo "<li>Go to <a href='batch_employee_history.php'>Batch History</a> to test rollback</li>";
    echo "</ol>";
    
    echo "<h4>⚠️ Important Notes:</h4>";
    echo "<ul>";
    echo "<li>Conversion creates simulated 'before_data' since original wasn't captured</li>";
    echo "<li>Future BATCH_UPDATE operations should capture real before/after data</li>";
    echo "<li>This is for demonstration and testing purposes</li>";
    echo "<li>In production, enhanced history should be captured during the actual update</li>";
    echo "</ul>";
    echo "</div>";
    
    // Show sample enhanced data structure
    echo "<h3>📄 Enhanced Data Structure Example:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px;'>";
    echo "<pre>";
    echo json_encode([
        'action_type' => 'BATCH_UPDATE',
        'mode' => 'update',
        'niks' => ['EMP001', 'EMP002'],
        'before_data' => [
            'EMP001' => ['nik' => 'EMP001', 'nama' => 'John Doe', 'jabatan' => 'Staff'],
            'EMP002' => ['nik' => 'EMP002', 'nama' => 'Jane Smith', 'jabatan' => 'Manager']
        ],
        'after_data' => [
            'EMP001' => ['nik' => 'EMP001', 'nama' => 'John Doe Updated', 'jabatan' => 'Senior Staff'],
            'EMP002' => ['nik' => 'EMP002', 'nama' => 'Jane Smith Updated', 'jabatan' => 'Senior Manager']
        ],
        'enhanced_history' => true,
        'rollback_capable' => true
    ], JSON_PRETTY_PRINT);
    echo "</pre>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

form {
    margin: 0;
}
</style>
