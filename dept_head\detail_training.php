<?php
// Display all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

include '../config/config.php';
include 'security.php';  // Ensure security.php is included
include '../config/notification_helper.php';  // Include notification helper

$id = $_GET['id'];

$query = "SELECT ts.id, ts.full_name,ts.assignment, ts.status, ts.training_topic, ts.nik, ts.departemen, ts.bagian, ts.jabatan, ts.internal_memo_image,
                 ts.training_type, ts.training_skill_type,
                 ts.start_date, ts.end_date, ts.is_confirmed,
                 ts.training_place, ts.training_cost,
                 ts.contact_person, ts.contact_number, ts.sharing_knowledge, ts.additional_info,
                 ts.approved_dept_head, ts.approved_hrd, ts.approved_ga, ts.approved_fm, ts.approved_dir,
                 ts.email, ts.phone, ts.comments_dept_head, ts.comments_hrd, ts.comments_ga, ts.comments_fm,
                 ts.training_time_start, ts.training_time_end,
                 ts.provider_type, ts.provider_name, ts.provider_address, ts.trainer_name_external, ts.additional_info_provider,
                 ts.trainer_name_internal, ts.trainer_nik_internal, ts.trainer_department_internal, ts.trainer_sub_department_internal, ts.trainer_position_internal,
                 r.role_name AS current_approver,
                 GROUP_CONCAT(p.nama_participants SEPARATOR ', ') AS participant_names,
                 GROUP_CONCAT(p.nik_participants SEPARATOR ', ') AS participant_niks,
                 GROUP_CONCAT(p.jabatan_participants SEPARATOR ', ') AS participant_jabatans,
                 GROUP_CONCAT(p.bagian_participants SEPARATOR ', ') AS participant_bagians,
                 GROUP_CONCAT(p.departemen_participants SEPARATOR ', ') AS participant_departemens
          FROM training_submissions ts
          LEFT JOIN roles r ON ts.current_approver_role_id = r.id
          LEFT JOIN participants p ON ts.id = p.training_id
          WHERE ts.id = ?
          GROUP BY ts.id";
$stmt = $conn->prepare($query);
if ($stmt === false) {
    die("Error preparing query.");
}


$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();

// Check if submission data is found
if (!$row) {
    die("Training submission not found.");
}

// Ambil informasi user saat ini (dipindahkan ke atas agar tersedia di template)
$user_query = "SELECT jabatan FROM users WHERE id = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("i", $_SESSION['user_id']);
$user_stmt->execute();
$user_result = $user_stmt->get_result();
$user_data = $user_result->fetch_assoc();
$current_jabatan = $user_data['jabatan'] ?? '';

// Cek jabatan user
$is_hrga_manager = ($current_jabatan == 'Manager HRGA');
$is_factory_manager = ($current_jabatan == 'Factory Manager');

// Process form submission if any
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $status = $_POST['status'];
    $comments = $_POST['comments'];
    $assignment = $_POST['assignment'];

    // Validasi komentar wajib untuk status Revise
    if ($status === 'Revise' && empty(trim($comments))) {
        $_SESSION['error'] = "Komentar wajib diisi untuk status Revise. Berikan alasan dan petunjuk yang jelas untuk revisi yang diperlukan.";
        header("Location: detail_training.php?id=$id&error=1");
        exit;
    }

    // Validate status
    if (!in_array($status, ['Approved', 'Rejected', 'Revise'])) {
        die("Invalid status.");
    }

    // Fetch current approver role ID and current status
    $queryRole = "SELECT current_approver_role_id, status, approved_dept_head, approved_hrd, approved_ga, approved_fm, approved_dir FROM training_submissions WHERE id = ?";
    $stmtRole = $conn->prepare($queryRole);
    $stmtRole->bind_param("i", $id);
    $stmtRole->execute();
    $resultRole = $stmtRole->get_result();
    $currentApprover = $resultRole->fetch_assoc();

    // Variabel user sudah didefinisikan di atas

    // Debug untuk melihat nilai status yang diterima
    error_log("Status yang diterima: " . $status);

    // ID pengguna dengan jabatan tertentu (berdasarkan hasil query sebelumnya)
    $chief_supervisor_ld_id = 44; // Rifka Singgih Maulida (Chief Supervisor Learning & Development)
    $factory_manager_id = 51;     // Linda (Factory Manager)
    $direktur_id = 50;            // Lembono Tjondro (Direktur)

    if ($status == 'Approved') {
        error_log("Masuk ke kondisi Approved");

        // Cek status training saat ini
        $checkStatusQuery = "SELECT status, approved_dept_head, approved_hrd, approved_ga FROM training_submissions WHERE id = ?";
        $checkStatusStmt = $conn->prepare($checkStatusQuery);
        $checkStatusStmt->bind_param("i", $id);
        $checkStatusStmt->execute();
        $statusResult = $checkStatusStmt->get_result();
        $statusData = $statusResult->fetch_assoc();
        $trainingStatus = $statusData['status'];
        $approvedDeptHead = $statusData['approved_dept_head'];
        $approvedHrd = $statusData['approved_hrd'];
        $approvedGa = $statusData['approved_ga'];

        // Jika user adalah Factory Manager atau HRGA Manager dan status masih Pending atau approved_dept_head masih Pending
        // maka proses sebagai Dept Head terlebih dahulu
        if (($is_factory_manager || $is_hrga_manager) && ($trainingStatus == 'Pending' || $approvedDeptHead == 'Pending')) {
            // Proses sebagai Dept Head terlebih dahulu - BISA MENGISI PENUGASAN
            $nextRoleId = 3; // Role ID untuk LnD
            $next_approver_id = $chief_supervisor_ld_id; // ID Chief Supervisor Learning & Development

            $updateQuery = "UPDATE training_submissions
                            SET status = 'Approved',
                                comments_dept_head = ?,
                                assignment = ?,
                                approved_dept_head = 'Approved',
                                current_approver_role_id = ?,
                                next_approver_id = ?
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("ssiii", $comments, $assignment, $nextRoleId, $next_approver_id, $id);
            error_log("Query untuk status Approved dari Dept Head (role ID 2 sebagai Dept Head, kirim ke LnD): " . $updateQuery);
        }
        // Jika user adalah Factory Manager dan training sudah disetujui Manager HRGA
        else if ($is_factory_manager && $approvedGa == 'Approved') {
            // Jika Factory Manager menyetujui training, kirim ke Direktur (role_id 5) - TIDAK BISA MENGISI PENUGASAN
            $nextRoleId = 5; // Role ID untuk Direktur
            $next_approver_id = $direktur_id; // ID Direktur

            $updateQuery = "UPDATE training_submissions
                            SET status = 'Approved',
                                comments_fm = ?,
                                approved_fm = 'Approved',
                                current_approver_role_id = ?,
                                next_approver_id = ?
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("siii", $comments, $nextRoleId, $next_approver_id, $id);
            error_log("Query untuk status Approved dari Factory Manager (kirim ke Direktur): " . $updateQuery);
        }
        // Jika user adalah Manager HRGA dan training sudah disetujui LnD
        else if ($is_hrga_manager && $approvedHrd == 'Approved') {
            // Manager HRGA approve - TIDAK BISA MENGISI PENUGASAN
            $nextRoleId = 2; // Role ID untuk Factory Manager
            $next_approver_id = $factory_manager_id; // ID Factory Manager

            $updateQuery = "UPDATE training_submissions
                            SET status = 'Approved',
                                comments_ga = ?,
                                approved_ga = 'Approved',
                                current_approver_role_id = ?,
                                next_approver_id = ?
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("siii", $comments, $nextRoleId, $next_approver_id, $id);
            error_log("Query untuk status Approved dari Manager HRGA (kirim ke Factory Manager): " . $updateQuery);
        }
        // Jika Dept Head biasa menyetujui, kirim ke LnD
        else {
            // Dept Head biasa approve - BISA MENGISI PENUGASAN
            $nextRoleId = 3; // Role ID untuk LnD
            $next_approver_id = $chief_supervisor_ld_id; // ID Chief Supervisor Learning & Development

            $updateQuery = "UPDATE training_submissions
                            SET status = 'Approved',
                                comments_dept_head = ?,
                                assignment = ?,
                                approved_dept_head = 'Approved',
                                current_approver_role_id = ?,
                                next_approver_id = ?
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("ssiii", $comments, $assignment, $nextRoleId, $next_approver_id, $id);
            error_log("Query untuk status Approved dari Dept Head (kirim ke LnD): " . $updateQuery);
        }
    } elseif ($status == 'Revise') {
        error_log("Masuk ke kondisi Revise");

        // Cek status training saat ini
        $checkStatusQuery = "SELECT status, approved_dept_head, approved_hrd, approved_ga FROM training_submissions WHERE id = ?";
        $checkStatusStmt = $conn->prepare($checkStatusQuery);
        $checkStatusStmt->bind_param("i", $id);
        $checkStatusStmt->execute();
        $statusResult = $checkStatusStmt->get_result();
        $statusData = $statusResult->fetch_assoc();
        $trainingStatus = $statusData['status'];
        $approvedDeptHead = $statusData['approved_dept_head'];
        $approvedHrd = $statusData['approved_hrd'];
        $approvedGa = $statusData['approved_ga'];

        // Jika user adalah Factory Manager atau HRGA Manager dan status masih Pending atau approved_dept_head masih Pending
        // maka proses sebagai Dept Head terlebih dahulu
        if (($is_factory_manager || $is_hrga_manager) && ($trainingStatus == 'Pending' || $approvedDeptHead == 'Pending')) {
            // Proses sebagai Dept Head terlebih dahulu - reset ke pemohon untuk revisi - BISA MENGISI PENUGASAN
            $updateQuery = "UPDATE training_submissions
                            SET status = 'Revise',
                                comments_dept_head = ?,
                                assignment = ?,
                                approved_dept_head = 'Revise',
                                current_approver_role_id = NULL,
                                next_approver_id = NULL
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("ssi", $comments, $assignment, $id);
            error_log("Query untuk Revise dari Dept Head (role ID 2 sebagai Dept Head): " . $updateQuery);
        }
        // Jika user adalah Factory Manager dan training sudah disetujui Manager HRGA
        else if ($is_factory_manager && $approvedGa == 'Approved') {
            // Jika Factory Manager meminta revisi - reset ke pemohon untuk revisi - TIDAK BISA MENGISI PENUGASAN
            $updateQuery = "UPDATE training_submissions
                            SET status = 'Revise',
                                comments_fm = ?,
                                approved_fm = 'Revise',
                                current_approver_role_id = NULL,
                                next_approver_id = NULL
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("si", $comments, $id);
            error_log("Query untuk Revise dari Factory Manager: " . $updateQuery);
        }
        // Jika user adalah Manager HRGA dan training sudah disetujui LnD
        else if ($is_hrga_manager && $approvedHrd == 'Approved') {
            // Jika Manager HRGA meminta revisi - reset ke pemohon untuk revisi - TIDAK BISA MENGISI PENUGASAN
            $updateQuery = "UPDATE training_submissions
                            SET status = 'Revise',
                                comments_ga = ?,
                                approved_ga = 'Revise',
                                current_approver_role_id = NULL,
                                next_approver_id = NULL
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("si", $comments, $id);
            error_log("Query untuk Revise dari Manager HRGA: " . $updateQuery);
        } else {
            // Jika Dept Head meminta revisi - reset ke pemohon untuk revisi - BISA MENGISI PENUGASAN
            $updateQuery = "UPDATE training_submissions
                            SET status = 'Revise',
                                comments_dept_head = ?,
                                assignment = ?,
                                approved_dept_head = 'Revise',
                                current_approver_role_id = NULL,
                                next_approver_id = NULL
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("ssi", $comments, $assignment, $id);
            error_log("Query untuk Revise dari Dept Head: " . $updateQuery);
        }


    } else if ($status == 'Rejected') {
        error_log("Masuk ke kondisi Rejected");
        // Jika status Rejected

        // Cek status training saat ini
        $checkStatusQuery = "SELECT status, approved_dept_head, approved_hrd, approved_ga FROM training_submissions WHERE id = ?";
        $checkStatusStmt = $conn->prepare($checkStatusQuery);
        $checkStatusStmt->bind_param("i", $id);
        $checkStatusStmt->execute();
        $statusResult = $checkStatusStmt->get_result();
        $statusData = $statusResult->fetch_assoc();
        $trainingStatus = $statusData['status'];
        $approvedDeptHead = $statusData['approved_dept_head'];
        $approvedHrd = $statusData['approved_hrd'];
        $approvedGa = $statusData['approved_ga'];

        // Jika user adalah Factory Manager atau HRGA Manager dan status masih Pending atau approved_dept_head masih Pending
        // maka proses sebagai Dept Head terlebih dahulu
        if (($is_factory_manager || $is_hrga_manager) && ($trainingStatus == 'Pending' || $approvedDeptHead == 'Pending')) {
            // Proses sebagai Dept Head terlebih dahulu - BISA MENGISI PENUGASAN
            $updateQuery = "UPDATE training_submissions
                            SET status = 'Rejected',
                                comments_dept_head = ?,
                                assignment = ?,
                                approved_dept_head = 'Rejected',
                                rejected_at = CURRENT_TIMESTAMP,
                                rejected_by = ?
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("ssii", $comments, $assignment, $_SESSION['user_id'], $id);
            error_log("Query untuk Rejected dari Dept Head (role ID 2 sebagai Dept Head): " . $updateQuery);
        }
        // Jika user adalah Factory Manager dan training sudah disetujui Manager HRGA
        else if ($is_factory_manager && $approvedGa == 'Approved') {
            // Jika Factory Manager menolak training - TIDAK BISA MENGISI PENUGASAN
            $updateQuery = "UPDATE training_submissions
                            SET status = 'Rejected',
                                comments_fm = ?,
                                approved_fm = 'Rejected',
                                rejected_at = CURRENT_TIMESTAMP,
                                rejected_by = ?
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("sii", $comments, $_SESSION['user_id'], $id);
            error_log("Query untuk Rejected dari Factory Manager: " . $updateQuery);
        }
        // Jika Manager HRGA menolak training dan training sudah disetujui LnD
        else if ($is_hrga_manager && $approvedHrd == 'Approved') {
            // Manager HRGA reject - TIDAK BISA MENGISI PENUGASAN
            $updateQuery = "UPDATE training_submissions
                            SET status = 'Rejected',
                                comments_ga = ?,
                                approved_ga = 'Rejected',
                                rejected_at = CURRENT_TIMESTAMP,
                                rejected_by = ?
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("sii", $comments, $_SESSION['user_id'], $id);
            error_log("Query untuk Rejected dari Manager HRGA: " . $updateQuery);
        } else {
            // Jika Dept Head biasa menolak training - BISA MENGISI PENUGASAN
            $updateQuery = "UPDATE training_submissions
                            SET status = 'Rejected',
                                comments_dept_head = ?,
                                assignment = ?,
                                approved_dept_head = 'Rejected',
                                rejected_at = CURRENT_TIMESTAMP,
                                rejected_by = ?
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("ssii", $comments, $assignment, $_SESSION['user_id'], $id);
            error_log("Query untuk Rejected dari Dept Head: " . $updateQuery);
        }
    }

    if ($updateStmt->execute()) {
        // Siapkan pengiriman email asinkron
        $email_token = hash('sha256', $id . 'training_email_secret');

        // Pastikan $nextRoleId terdefinisi, jika tidak gunakan nilai default
        $nextRoleId = isset($nextRoleId) ? $nextRoleId : 1; // Default ke role pemohon jika tidak ada

        $async_url = "../config/async_email.php?token=$email_token&training_id=$id&status=$status&next_approver_role_id=$nextRoleId";

        // Tambahkan next_approver_id jika ada
        if (isset($next_approver_id) && $next_approver_id !== NULL) {
            $async_url .= "&next_approver_id=$next_approver_id";
        }

        // Tambahkan comments jika ada
        if (!empty($comments)) {
            $async_url .= "&comments=" . urlencode($comments);
        }

        // Tambahkan assignment jika ada
        if (!empty($assignment)) {
            $async_url .= "&assignment=" . urlencode($assignment);
        }

        // Log URL untuk debugging
        error_log("Async email URL: $async_url");

        // Nonaktifkan pengiriman email asinkron untuk mencegah server kewalahan
        // Hanya log URL yang akan dipanggil
        error_log("DISABLED ASYNC EMAIL CALL: $async_url");

        // Tampilkan pesan simulasi ke pengguna
        $_SESSION['success'] = "Status training berhasil diperbarui! (Pengiriman email dinonaktifkan untuk mencegah server kewalahan)";

        // Log bahwa email akan dikirim secara asinkron
        error_log("Email notifikasi akan dikirim secara asinkron untuk training ID: $id");


        header("Location: dashboard.php");
        exit();
    } else {
        $_SESSION['error'] = "Gagal memperbarui status training.";
        header("Location: detail_training.php?id=" . $id);
        exit();
    }
}


?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<head>
    <script>
        function showAlert(message) {
            let alertBox = document.createElement("div");
            alertBox.innerText = message;
            alertBox.style.position = "fixed";
            alertBox.style.top = "20px";
            alertBox.style.left = "50%";
            alertBox.style.transform = "translateX(-50%)";
            alertBox.style.backgroundColor = "#28a745";
            alertBox.style.color = "white";
            alertBox.style.padding = "10px 20px";
            alertBox.style.borderRadius = "5px";
            alertBox.style.boxShadow = "0px 4px 6px rgba(0,0,0,0.1)";
            document.body.appendChild(alertBox);
            setTimeout(() => { alertBox.remove(); }, 3000);
        }
    </script>
</head>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container-form">
    <div class="form-container">
        <div class="latest-submissions">
            <h1>Detail Pengajuan Training</h1>
            <?php
            if ($row['status'] == 'Approved') {
                include '../config/tablea.php';
            } else {
                include '../config/table.php';
            }
            ?>



            <?php

            // Cek apakah user saat ini masih perlu memberikan approval
            $needsApproval = false;
            $approvalReason = "";

            // Jika Factory Manager dan training sudah disetujui Manager HRGA tapi belum Factory Manager
            if ($is_factory_manager && $row['approved_ga'] == 'Approved' && ($row['approved_fm'] != 'Approved')) {
                $needsApproval = true;
                $approvalReason = "Factory Manager perlu approve (approved_ga=Approved, approved_fm=" . ($row['approved_fm'] ?? 'NULL') . ")";
            }
            // Jika Manager HRGA dan training sudah disetujui LnD tapi belum Manager HRGA
            elseif ($is_hrga_manager && $row['approved_hrd'] == 'Approved' && ($row['approved_ga'] != 'Approved')) {
                $needsApproval = true;
                $approvalReason = "Manager HRGA perlu approve (approved_hrd=Approved, approved_ga=" . ($row['approved_ga'] ?? 'NULL') . ")";
            }
            // Jika Factory Manager atau Manager HRGA sebagai Dept Head dan training masih pending
            elseif (($is_factory_manager || $is_hrga_manager) && $row['status'] == 'Pending' && ($row['approved_dept_head'] != 'Approved')) {
                $needsApproval = true;
                $approvalReason = "Factory Manager/Manager HRGA sebagai Dept Head perlu approve (status=Pending, approved_dept_head=" . ($row['approved_dept_head'] ?? 'NULL') . ")";
            }
            // Jika user biasa sebagai Dept Head dan training masih pending
            elseif ($row['status'] == 'Pending' && ($row['approved_dept_head'] != 'Approved')) {
                $needsApproval = true;
                $approvalReason = "Dept Head perlu approve (status=Pending, approved_dept_head=" . ($row['approved_dept_head'] ?? 'NULL') . ")";
            }


            // Jika training sudah final approved (semua level sudah approve) atau user tidak perlu approve
            if ($row['status'] == 'Approved' && !$needsApproval): ?>
            <!-- Training sudah disetujui final, tampilkan status saja -->
            <div style="padding: 20px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #155724; margin-bottom: 15px;">
                    <i class="fas fa-check-circle" style="margin-right: 10px;"></i>
                    Training Sudah Disetujui Final
                </h3>
                <p style="color: #155724; margin-bottom: 10px;">
                    <strong>Status:</strong> <?= htmlspecialchars($row['status']) ?>
                </p>
                <?php if (!empty($row['comments_dept_head'])): ?>
                <p style="color: #155724; margin-bottom: 10px;">
                    <strong>Komentar Dept Head:</strong> <?= nl2br(htmlspecialchars($row['comments_dept_head'])) ?>
                </p>
                <?php endif; ?>
                <?php if (!empty($row['comments_ga'])): ?>
                <p style="color: #155724; margin-bottom: 10px;">
                    <strong>Komentar Manager HRGA:</strong> <?= nl2br(htmlspecialchars($row['comments_ga'])) ?>
                </p>
                <?php endif; ?>
                <?php if (!empty($row['comments_fm'])): ?>
                <p style="color: #155724; margin-bottom: 10px;">
                    <strong>Komentar Factory Manager:</strong> <?= nl2br(htmlspecialchars($row['comments_fm'])) ?>
                </p>
                <?php endif; ?>
                <?php if (!empty($row['assignment'])): ?>
                <p style="color: #155724; margin-bottom: 0;">
                    <strong>Penugasan:</strong> <?= nl2br(htmlspecialchars($row['assignment'])) ?>
                </p>
                <?php endif; ?>
            </div>
            <?php else: ?>
            <!-- Training belum disetujui, tampilkan form keputusan -->
            <h3>Berikan Keputusan</h3>
            <form method="POST">
                <label for="status">Status:</label>
                <select name="status" id="status">
                    <option value="Approved" <?= $row['status'] == 'Approved' ? 'selected' : '' ?>>Approved</option>
                    <option value="Rejected" <?= $row['status'] == 'Rejected' ? 'selected' : '' ?>>Rejected</option>
                    <option value="Revise" <?= $row['status'] == 'Revise' ? 'selected' : '' ?>>Revise</option>
                </select><br><br>

                <label for="comments">Komentar:</label><br>
                <small class="form-text text-muted">Tambahkan catatan untuk training yang di ajukan</small>
                <textarea name="comments" id="comments" rows="4" cols="50" placeholder="Tambahkan komentar (opsional)"></textarea><br><br>

                <?php
                // Cek apakah user saat ini adalah Dept Head (approval pertama)
                $isDeptHeadApproval = false;

                // Jika Factory Manager atau Manager HRGA sebagai Dept Head dan training masih pending
                if (($is_factory_manager || $is_hrga_manager) && $row['status'] == 'Pending' && ($row['approved_dept_head'] != 'Approved')) {
                    $isDeptHeadApproval = true;
                }
                // Jika user biasa sebagai Dept Head dan training masih pending
                elseif ($row['status'] == 'Pending' && ($row['approved_dept_head'] != 'Approved')) {
                    $isDeptHeadApproval = true;
                }
                ?>

                <!-- Form penugasan hanya muncul untuk Dept Head dan hanya jika status Approved -->
                <div id="assignment_section" style="display: none;">
                    <?php if ($isDeptHeadApproval && empty($row['assignment'])): ?>
                    <!-- Tampilkan form penugasan hanya untuk Dept Head (approval pertama) dan jika assignment masih kosong -->
                    <label for="assignment" class="required">Penugasan:</label><br>
                    <small class="form-text text-muted">Berikan tugas kepada peserta setelah training. Cth: melakukan Sharing Session ke Team Internal</small>
                    <textarea name="assignment" id="assignment" rows="4" cols="50" placeholder="Tambahkan penugasan" disabled></textarea><br><br>
                    <?php endif; ?>
                </div>

                <?php if (!empty($row['assignment'])): ?>
                <!-- Tampilkan penugasan yang sudah ada (read-only) -->
                <label>Penugasan (sudah diisi oleh Dept Head):</label><br>
                <div style="padding: 10px; background-color: #f9f9f9; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 15px; color: #666;">
                    <i class="fas fa-tasks" style="margin-right: 8px; color: #28a745;"></i>
                    <?= nl2br(htmlspecialchars($row['assignment'])) ?>
                </div>
                <!-- Kirim assignment yang sudah ada sebagai hidden field -->
                <input type="hidden" name="assignment" value="<?= htmlspecialchars($row['assignment']) ?>">
                <?php else: ?>
                <!-- Hidden field untuk assignment -->
                <input type="hidden" name="assignment" id="assignment_hidden" value="">
                <?php endif; ?>

                <button type="submit">Submit</button>
            </form>
            <?php endif; ?>
            <button><a style="color: white; text-decoration: none;" href="dashboard.php">Kembali ke Daftar Pengajuan</a></button>
        </div>
    </div>
</div>
<?php include '../config/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('status');
    const assignmentSection = document.getElementById('assignment_section');
    const assignmentTextarea = document.getElementById('assignment');
    const assignmentHidden = document.getElementById('assignment_hidden');

    // Cek apakah user adalah Dept Head approval
    const isDeptHeadApproval = <?= $isDeptHeadApproval ? 'true' : 'false' ?>;
    const hasExistingAssignment = <?= !empty($row['assignment']) ? 'true' : 'false' ?>;

    function toggleAssignmentSection() {
        if (!isDeptHeadApproval || hasExistingAssignment) {
            // Jika bukan Dept Head approval atau sudah ada assignment, jangan tampilkan form
            return;
        }

        const selectedStatus = statusSelect.value;

        if (selectedStatus === 'Approved') {
            // Tampilkan form penugasan hanya jika status Approved
            assignmentSection.style.display = 'block';
            if (assignmentTextarea) {
                assignmentTextarea.disabled = false; // Enable textarea
                assignmentTextarea.setAttribute('required', 'required'); // Set required when visible
            }
            if (assignmentHidden) {
                assignmentHidden.disabled = true; // Disable hidden field
            }
        } else {
            // Sembunyikan form penugasan jika status Rejected atau Revise
            assignmentSection.style.display = 'none';
            if (assignmentTextarea) {
                assignmentTextarea.value = ''; // Clear textarea
                assignmentTextarea.removeAttribute('required'); // Remove required when hidden
                assignmentTextarea.disabled = true; // Disable when hidden
            }
            if (assignmentHidden) {
                assignmentHidden.disabled = false; // Enable hidden field
                assignmentHidden.value = ''; // Set empty value
            }
        }
    }

    // Event listener untuk perubahan status
    if (statusSelect) {
        statusSelect.addEventListener('change', function() {
            toggleAssignmentSection();
            updateCommentsValidation();
        });

        // Jalankan saat halaman dimuat
        toggleAssignmentSection();
        updateCommentsValidation();
    }

    // Function untuk update validasi komentar
    function updateCommentsValidation() {
        const statusSelect = document.getElementById('status');
        const commentsField = document.getElementById('comments');
        const commentsLabel = document.querySelector("label[for='comments']");

        if (!statusSelect || !commentsField) return;

        const isRevise = statusSelect.value === 'Revise';

        if (isRevise) {
            commentsField.setAttribute('required', 'required');
            commentsField.style.borderColor = '#dc3545';
            if (commentsLabel) {
                commentsLabel.innerHTML = 'Komentar <span style="color: red;">*</span>';
            }
        } else {
            commentsField.removeAttribute('required');
            commentsField.style.borderColor = '';
            if (commentsLabel) {
                commentsLabel.innerHTML = 'Komentar';
            }
        }
    }

    // Validasi form saat submit
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const statusSelect = document.getElementById('status');
            const commentsField = document.getElementById('comments');
            const assignmentTextarea = document.getElementById('assignment');

            // Validasi komentar untuk status Revise
            if (statusSelect && commentsField && statusSelect.value === 'Revise') {
                if (!commentsField.value.trim()) {
                    e.preventDefault();
                    alert('Komentar wajib diisi untuk status Revise. Berikan alasan dan petunjuk yang jelas untuk revisi yang diperlukan.');
                    commentsField.focus();
                    return;
                }
            }

            // Validasi assignment untuk status Approved (jika visible dan enabled)
            if (statusSelect && assignmentTextarea && statusSelect.value === 'Approved') {
                if (assignmentTextarea.style.display !== 'none' &&
                    !assignmentTextarea.disabled &&
                    assignmentTextarea.hasAttribute('required') &&
                    !assignmentTextarea.value.trim()) {
                    e.preventDefault();
                    alert('Penugasan wajib diisi untuk status Approved. Berikan tugas yang jelas untuk peserta setelah training.');
                    assignmentTextarea.focus();
                    return;
                }
            }
        });
    }
});
</script>

</body>
</html>

<?php $conn->close(); ?>




