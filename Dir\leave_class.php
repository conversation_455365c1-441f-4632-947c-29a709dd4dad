<?php
/**
 * Leave Class Page for Pemohon (Applicant)
 * This page allows applicants to leave a class they have joined
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';

// Get user information
$user_id = $_SESSION['user_id'];

// Initialize variables
$success_message = '';
$error_message = '';
$class_info = null;

// Check if class ID is provided
$class_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($class_id <= 0) {
    header('Location: classroom.php');
    exit();
}

// Get class information
$class_query = "SELECT c.*, t.training_topic, t.training_type
               FROM training_classes c
               LEFT JOIN training_submissions t ON c.training_id = t.id
               WHERE c.id = ?";
$stmt = $conn->prepare($class_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$class = $result->fetch_assoc();
$stmt->close();

if (!$class) {
    header('Location: classroom.php');
    exit();
}

// Check if user is a participant in this class
$participant_query = "SELECT * FROM training_participants
                     WHERE class_id = ? AND user_id = ?";
$stmt = $conn->prepare($participant_query);
$stmt->bind_param("ii", $class_id, $user_id);
$stmt->execute();
$participant_result = $stmt->get_result();
$is_participant = $participant_result->num_rows > 0;
$participant = $participant_result->fetch_assoc();
$stmt->close();

if (!$is_participant) {
    header('Location: classroom.php');
    exit();
}

// Process leave request
if (isset($_POST['confirm_leave'])) {
    // Delete participant record
    $delete_query = "DELETE FROM training_participants
                    WHERE class_id = ? AND user_id = ?";
    $stmt = $conn->prepare($delete_query);
    $stmt->bind_param("ii", $class_id, $user_id);

    if ($stmt->execute()) {
        $success_message = "Anda telah berhasil keluar dari kelas: " . $class['title'];
        // Redirect to classroom page after successful leave
        header("Refresh: 3; URL=classroom.php");
    } else {
        $error_message = "Gagal keluar dari kelas: " . $conn->error;
    }
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .jarak {
        height: 80px;
    }

    .leave-class-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
    }

    .leave-class-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 6px 15px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .leave-class-header {
        background: linear-gradient(135deg, #f44336, #d32f2f);
        color: #fff;
        padding: 30px 20px;
        text-align: center;
    }

    .leave-class-header h1 {
        font-size: 1.8rem;
    }

    .leave-class-body {
        padding: 30px 20px;
    }

    @media (max-width: 576px) {
        .leave-class-header {
            padding: 20px 15px;
        }

        .leave-class-header h1 {
            font-size: 1.5rem;
        }

        .leave-class-body {
            padding: 20px 15px;
        }
    }

    .class-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        border-left: 4px solid #f44336;
    }

    .class-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
    }

    .class-meta {
        color: #666;
        margin-bottom: 10px;
    }

    .warning-text {
        color: #d32f2f;
        font-weight: 500;
        margin-bottom: 20px;
    }

    .btn-danger {
        background-color: #f44336;
        border-color: #f44336;
    }

    .btn-danger:hover {
        background-color: #d32f2f;
        border-color: #d32f2f;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="leave-class-container">
    <div class="leave-class-card">
        <div class="leave-class-header">
            <h1><i class="fas fa-sign-out-alt"></i> Keluar dari Kelas</h1>
            <p>Konfirmasi untuk keluar dari kelas ini</p>
        </div>

        <div class="leave-class-body">
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (empty($success_message)): ?>
                <div class="class-info">
                    <div class="class-title"><?= htmlspecialchars($class['title']) ?></div>
                    <div class="class-meta">
                        <div><strong>Topik:</strong> <?= htmlspecialchars($class['training_topic'] ?? 'Tidak tersedia') ?></div>
                        <div><strong>Tipe:</strong> <?= htmlspecialchars($class['training_type'] ?? 'Tidak tersedia') ?></div>
                        <?php if (!empty($class['start_date'])): ?>
                            <div><strong>Tanggal Mulai:</strong> <?= date('d M Y', strtotime($class['start_date'])) ?></div>
                        <?php endif; ?>
                        <?php if (!empty($class['end_date'])): ?>
                            <div><strong>Tanggal Selesai:</strong> <?= date('d M Y', strtotime($class['end_date'])) ?></div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="warning-text">
                    <p><i class="fas fa-exclamation-triangle"></i> Peringatan:</p>
                    <ul>
                        <li>Anda akan keluar dari kelas ini dan tidak akan dapat mengakses materi, kuis, tugas, dan diskusi.</li>
                        <li>Semua progres dan nilai Anda dalam kelas ini akan tetap tersimpan jika Anda bergabung kembali.</li>
                        <li>Anda dapat bergabung kembali dengan kelas ini nanti jika masih tersedia.</li>
                    </ul>
                </div>

                <form method="post" action="">
                    <div class="d-grid gap-2">
                        <button type="submit" name="confirm_leave" class="btn btn-danger btn-lg">
                            <i class="fas fa-sign-out-alt"></i> <span class="d-none d-sm-inline">Konfirmasi</span> Keluar dari Kelas
                        </button>
                        <a href="classroom_detail.php?id=<?= $class_id ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i> <span class="d-none d-sm-inline">Batal</span>
                        </a>
                    </div>
                </form>
            <?php else: ?>
                <div class="text-center mt-3">
                    <a href="classroom.php" class="btn btn-primary">
                        <i class="fas fa-home"></i> Kembali ke Daftar Kelas
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
