<?php
session_start();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to KMS</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color:rgb(224, 222, 222);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(191, 0, 0, 0.2);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 100%;
        }

        h1 {
            color: #BF0000;
            margin-bottom: 20px;
        }

        p {
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .btn {
            background-color: #BF0000;
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background-color: #900000;
            transform: scale(1.05);
        }

        .features {
            display: flex;
            justify-content: space-around;
            margin: 40px 0;
            flex-wrap: wrap;
        }

        .feature {
            margin: 10px;
            flex: 1;
            min-width: 120px;
        }

        .feature i {
            font-size: 40px;
            color: #BF0000;
            margin-bottom: 10px;
        }

        footer {
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }

        /* Error Message Styles */
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: left;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .error-message i {
            color: #dc3545;
            font-size: 20px;
        }

        .error-message .error-content {
            flex: 1;
        }

        .error-message .error-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .error-message .error-text {
            margin: 0;
            line-height: 1.4;
        }

        .error-close {
            background: none;
            border: none;
            color: #721c24;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            margin-left: 10px;
        }

        .error-close:hover {
            color: #dc3545;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
                margin: 0 15px;
            }
        }
    </style>
    <!-- Font Awesome untuk ikon -->
    <link rel="icon" href="asset/picture/image-removebg-preview (1).ico" width="100%">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="container">
        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="error-message" id="errorMessage">
                <i class="fas fa-exclamation-triangle"></i>
                <div class="error-content">
                    <div class="error-title">Akses Ditolak</div>
                    <p class="error-text"><?= htmlspecialchars($_SESSION['error_message']) ?></p>
                </div>
                <button class="error-close" onclick="closeErrorMessage()">&times;</button>
            </div>
            <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>

        <h1>Welcome to Knowledge Management System (KMS)</h1>
        <img src="asset/picture/logo kms.png" alt="Logo KMS">
        <br>
        <a href="view/login.php" class="btn" id="loginBtn">Login Now</a>

        <footer>
            <p>&copy; <?php echo date("Y"); ?> All Rights Reserved</p>
        </footer>
    </div>

    <script>
        // Function untuk menutup error message
        function closeErrorMessage() {
            const errorMessage = document.getElementById('errorMessage');
            if (errorMessage) {
                errorMessage.style.opacity = '0';
                errorMessage.style.transform = 'translateY(-10px)';
                setTimeout(function() {
                    errorMessage.style.display = 'none';
                }, 300);
            }
        }

        // Menambahkan efek hover pada tombol
        document.getElementById('loginBtn').addEventListener('mouseover', function() {
            this.style.boxShadow = '0 5px 15px rgba(191, 0, 0, 0.4)';
        });

        document.getElementById('loginBtn').addEventListener('mouseout', function() {
            this.style.boxShadow = 'none';
        });

        // Animasi sederhana saat halaman dimuat
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            container.style.transition = 'all 0.5s ease';

            setTimeout(function() {
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);

            // Auto-hide error message after 10 seconds
            const errorMessage = document.getElementById('errorMessage');
            if (errorMessage) {
                setTimeout(function() {
                    closeErrorMessage();
                }, 10000);
            }
        });
    </script>
</body>
</html>