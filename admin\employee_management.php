<?php
// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Include security.php untuk validasi akses admin
include 'security.php';


?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
button .btn, .btn-sm {
    width: 30px !important; 
    padding: 5px !important;
    font-size:10px!important;
    min-width:20px !important;
}
/* Perbaikan langsung untuk progress bar */
.progress {
    height: 20px !important;
    border-radius: 10px !important;
    background-color: #e9ecef !important;
    margin: 15px 0 !important;
    overflow: hidden !important;
    position: relative !important;
}

.progress-bar {
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    height: 100% !important;
    background-color: rgb(157, 0, 0) !important;
    color: white !important;
    text-align: center !important;
    line-height: 20px !important;
    font-weight: bold !important;
    transition: none !important;
}

/* Hapus semua transisi untuk memastikan perubahan langsung */
.progress-bar-animated {
    animation: none !important;
}
body {
    background-color: #f5f5f5;
    overflow-x: hidden; /* Prevent horizontal scrollbar */
}

/* Container & Card Styles */
.container-fluid {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    margin-top: 80px; /* Increased top margin to prevent navbar overlap */
    position: relative;
    z-index: 1; /* Ensure it's above the navbar */
}

.welcome-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    overflow: visible; /* Changed from hidden to visible */
    position: relative; /* Added for proper positioning */
}

.card-header {
    background: rgb(157, 0, 0) !important;
    color: white;
    padding: 20px;
    border-bottom: none;
    border-radius: 4px;
}

.card-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.card-header i {
    margin-right: 10px;
}

.card-body {
    padding: 30px;
    overflow: visible !important; /* Ensure dropdowns aren't clipped */
}

/* Search Box Styles */
.search-section {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
    position: relative; /* Added for proper positioning */
    overflow: visible; /* Ensure dropdowns aren't clipped */
}

/* Action buttons container */
.action-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-start;
    width: 100%;
}

/* Action button styles */
.action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 15px;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    min-width: 40px;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    color: white;
    text-decoration: none;
}

.action-button i {
    margin-right: 8px;
    font-size: 16px;
}

/* Specific button styles */
.add-button {
    background-color: #28a745;
}

.add-button:hover {
    background-color: #218838;
}

.upload-button {
    background-color: #007bff;
}

.upload-button:hover {
    background-color: #0069d9;
}

.delete-button {
    background-color: #dc3545;
}

.delete-button:hover {
    background-color: #c82333;
}

.duplicate-button {
    background-color: #ffc107;
    color: #212529;
}

.duplicate-button:hover {
    background-color: #e0a800;
    color: #212529;
}

.history-button {
    background-color: #6f42c1; /* Purple */
}

.history-button:hover {
    background-color: #5a349b;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    max-width: 100%;
    position: relative; /* Added for proper positioning */
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    table-layout: fixed;
}


.table th {
    background: #f8f9fa;
    color: #495057;
    font-weight: 600;
    text-align: left;
    padding: 15px 10px;
    border-bottom: 2px solid #dee2e6;
    white-space: nowrap;
    overflow: visible;
    position: relative;
    min-width: 100px;
}

/* Fixed action column */
.table th:last-child,
.table td:last-child {
    position: sticky;
    right: 0;
    background-color: #fff;
    z-index: 10;
    box-shadow: -5px 0 5px rgba(0, 0, 0, 0.05);
    min-width: 120px; /* Increased minimum width for action column */
    width: 120px; /* Increased fixed width for action column */
    text-align: center; /* Center align buttons */
    padding: 8px 5px; /* Reduced padding to fit buttons */
}

.table th:last-child {
    background-color: #f8f9fa;
}

.table tr:hover td:last-child {
    background-color: #f8f9fa;
}

/* Action buttons styling */
.btn-group {
    display: flex;
    justify-content: center;
    gap: 2px; /* Reduced gap between buttons */
    flex-wrap: nowrap; /* Prevent wrapping */
}

.btn-group .btn {
    padding: 5px 6px; /* Reduced padding */
    margin: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 30px; /* Set minimum width */
    font-size: 12px; /* Reduced font size */
    border-radius: 4px; /* Rounded corners */
    box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* Add shadow for better visibility */
}

/* Specific styles for action buttons */
.btn-group .btn-info {
    background-color: #17a2b8;
    color: white;
    border: none;
}

.btn-group .btn-warning {
    background-color: #ffc107;
    color: #212529;
    border: none;
}

.btn-group .btn-danger {
    background-color: #dc3545;
    color: white;
    border: none;
}

.btn-group .btn-info:hover {
    background-color: #138496;
}

.btn-group .btn-warning:hover {
    background-color: #e0a800;
}

.btn-group .btn-danger:hover {
    background-color: #c82333;
}

.table td {
    padding: 15px 10px;
    border-bottom: 1px solid #e9ecef;
    color: #495057;
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.table tr {
    cursor: pointer;
}

.table tr:hover {
    background-color: #f8f9fa;
}

/* Column Header Styles */
.column-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.column-header span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.column-filter {
    position: relative;
    margin-left: 5px;
    /* Ensure dropdown container is properly positioned */
    display: inline-block;
}

.filter-button {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 2px 5px;
    font-size: 12px;
    transition: all 0.2s;
    position: relative;
}

.filter-button:hover {
    color: #BF0000;
}

.filter-button.active-filter {
    color: #BF0000;
}

.filter-button.active-filter::after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    background-color: #BF0000;
    border-radius: 50%;
    top: -2px;
    right: -2px;
}

.filter-button.filter-applied {
    animation: pulse 1s;
}

.sort-highlight {
    animation: highlight-sort 1s;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes highlight-sort {
    0% { background-color: rgba(191, 0, 0, 0); }
    50% { background-color: rgba(191, 0, 0, 0.1); }
    100% { background-color: rgba(191, 0, 0, 0); }
}

.sort-icon {
    display: inline-block;
    margin-left: 5px;
    color: #BF0000;
}

.loading-text {
    color: #777;
    text-align: center;
    padding: 10px;
}

.error-text {
    color: #BF0000;
    text-align: center;
    padding: 10px;
}

#resetFiltersBtn.has-active-filters {
    background-color: #BF0000;
    color: white;
    border-color: #BF0000;
}

.filter-dropdown {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    width: 250px;
    z-index: 10; /* Increased z-index to ensure it appears above other elements */
    padding: 10px;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 5px;
}

.filter-dropdown.show {
    display: block;
}

.filter-search {
    margin-bottom: 10px;
}

.filter-search input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.filter-options {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 10px;
}

.filter-options label {
    display: block;
    padding: 5px 0;
    cursor: pointer;
}

.filter-options label:hover {
    background-color: #f8f9fa;
}

.filter-actions {
    display: flex;
    justify-content: space-between;
    border-top: 1px solid #dee2e6;
    padding-top: 10px;
}

.filter-actions button {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.filter-actions button:first-child {
    background-color: #BF0000;
    color: white;
}

.filter-actions button:last-child {
    background-color: #6c757d;
    color: white;
}

/* Sortable Column Styles */
.sortable-column {
    cursor: pointer;
}

.sortable-column .column-header span::after {
    content: '\f0dc';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-left: 5px;
    color: #dee2e6;
}

.sortable-column.sort-asc .column-header span::after {
    content: '\f0de';
    color: #BF0000;
}

.sortable-column.sort-desc .column-header span::after {
    content: '\f0dd';
    color: #BF0000;
}

/* Responsive Table */
@media screen and (max-width: 1200px) {
    .table th, .table td {
        padding: 10px;
        font-size: 14px;
    }

    .container-fluid {
        padding: 15px;
    }

    .card-body {
        padding: 20px;
    }

    .search-section {
        padding: 20px;
    }
}

@media screen and (max-width: 992px) {
    /* Improved responsive layout for search and action buttons */
    .search-actions-container {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box-container {
        width: 100%;
    }

    .action-buttons-container {
        flex-wrap: wrap;
        justify-content: flex-start;
        gap: 10px;
        margin-top: 15px;
    }

    .action-button {
        flex: 1 1 calc(50% - 10px);
        min-width: 150px;
    }

    /* Table controls responsive layout */
    .table-controls-section {
        flex-direction: column;
    }

    .entries-filters-section,
    .copy-controls-section {
        width: 100%;
        justify-content: flex-start;
    }

    .entries-control {
        margin-bottom: 10px;
    }

    .control-button {
        flex: 1 1 auto;
    }

    /* Table display controls */
    .table-display-controls {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-bottom: 15px;
    }

    .table-search-box {
        max-width: 100%;
        order: -1; /* Menampilkan kotak pencarian di atas tombol kolom */
    }

    .columns-toggle-button {
        width: 100%;
    }
}

/* Table Display Controls */
.table-display-controls {
    margin: 25px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    justify-content: space-between;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.table-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

#resetFiltersBtn {
    transition: all 0.3s ease;
}

#resetFiltersBtn.has-active-filters {
    background-color: #BF0000 !important;
    color: white !important;
    border-color: #BF0000 !important;
}

/* Table Search Box */
.table-search-box {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 8px;
    padding: 12px 15px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    flex: 1;
    max-width: 1000px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
    margin-right: auto;
}

.table-search-box:focus-within {
    box-shadow: 0 4px 12px rgba(191, 0, 0, 0.15);
    border-color: #BF0000;
}

.table-search-box i {
    color: #BF0000;
    margin-right: 12px;
    font-size: 18px;
}

.table-search-box input {
    flex: 1;
    border: none;
    outline: none;
    padding: 8px 0;
    font-size: 16px;
    width: 100%;
    color: #333;
}

.table-search-box input::placeholder {
    color: #999;
    font-style: italic;
}

.columns-toggle-button {
    background-color: #17a2b8;
    color: white;
    width: auto;
}

.columns-toggle-button:hover {
    background-color: #138496;
    color: white;
}

@media screen and (max-width: 768px) {
    .container-fluid {
        padding: 10px;
        margin-top: 60px; /* Adjust for smaller screens */
    }

    .card-body {
        padding: 15px;
    }

    .search-section {
        padding: 15px;
    }

    /* Improved mobile layout for action buttons */
    .action-buttons-container {
        grid-template-columns: 1fr;
    }

    .action-button {
        width: 100%;
        justify-content: flex-start;
    }

    /* Improved table controls for mobile */
    .table-controls-section {
        padding: 15px;
        gap: 10px;
    }

    .entries-filters-section,
    .copy-controls-section {
        gap: 8px;
    }

    .control-button {
        width: 100%;
        justify-content: flex-start;
    }

    .button-text {
        flex: 1;
        text-align: left;
    }

    /* Card view for mobile */
    .mobile-card-view {
        display: block;
    }

    .table-container {
        overflow-x: auto;
    }

    /* Pagination adjustments */
    .pagination-container {
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .pagination-buttons {
        min-width: max-content;
    }

    /* Improve filter dropdowns */
    .filter-dropdown {
        width: 200px;
        right: -100px;
    }

    /* Adjust modal size */
    .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    /* Column toggle dialog */
    .column-toggle-dialog {
        width: 95%;
        max-height: 90vh;
    }

    .column-toggle-options {
        grid-template-columns: 1fr;
    }
}

@media screen and (max-width: 576px) {
    .container-fluid {
        margin-top: 55px; /* Adjust for mobile screens */
        padding: 8px;
    }

    .card-header {
        padding: 15px;
    }

    .card-header h3 {
        font-size: 1.2rem;
    }

    .welcome-container {
        border-radius: 10px;
    }

    .search-section {
        padding: 12px;
        border-radius: 8px;
    }

    .search-box {
        padding: 8px 12px;
    }

    .search-box input {
        font-size: 14px;
        padding: 6px 0;
    }

    /* Stack action buttons vertically on very small screens */
    .action-button {
        width: 100%;
        padding: 10px 12px;
        justify-content: flex-start;
    }

    .action-button i {
        width: 20px;
        text-align: center;
    }

    /* Improve table controls for small screens */
    .table-controls-section {
        padding: 12px;
        border-radius: 8px;
    }

    .entries-control {
        width: 100%;
        justify-content: space-between;
    }

    .entries-select {
        flex: 1;
        max-width: 120px;
    }

    .control-button {
        padding: 8px 12px;
        font-size: 13px;
    }

    /* Hide some text on very small screens */
    .entries-info {
        font-size: 12px;
    }

    /* Adjust employee detail modal */
    .employee-info-grid {
        grid-template-columns: 1fr;
    }

    /* Improve copy columns dropdown */
    .copy-columns-menu {
        width: 200px;
        right: -100px;
    }

    /* Adjust pagination */
    .pagination-button {
        padding: 6px 10px;
        font-size: 12px;
    }
}

/* Cell Content Styles */
.table td[data-column="nama"] {
    min-width: 150px;
    max-width: 250px;
}

.table td[data-column="nik"] {
    min-width: 120px;
}

.table td[data-column="dept"],
.table td[data-column="bagian"],
.table td[data-column="jabatan"] {
    min-width: 120px;
}

/* Tooltip for truncated content */
.table th, .table td {
    position: relative;
}

.table th:hover::after,
.table td:hover::after {
    content: attr(title);
    position: absolute;
    left: 0;
    top: 100%;
    z-index: 1000;
    background-color: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    white-space: normal;
    max-width: 300px;
    word-wrap: break-word;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    display: none;
}

.table th[title]:hover::after,
.table td[title]:hover::after {
    display: block;
}

/* Pagination Styles */
.pagination-container {
    margin-top: 30px;
    display: flex;
    justify-content: center;
}

.pagination-buttons {
    display: flex;
    gap: 5px;
}

.pagination-button {
    padding: 8px 15px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s;
}

.pagination-button:hover {
    background: #f8f9fa;
}

.pagination-button.active {
    background: rgb(157, 0, 0);
    color: white;
    border-color: rgb(157, 0, 0);
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-ellipsis {
    padding: 8px 15px;
    color: #6c757d;
}

/* Loading Indicator */
.loading-indicator {
    display: none;
    text-align: center;
    padding: 20px;
}

.loading-indicator i {
    font-size: 24px;
    color: rgb(157, 0, 0);
    animation: spin 1s infinite linear;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Loading Message for Lazy Loading */
.loading-message {
    text-align: center;
    padding: 40px 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin: 20px auto;
    max-width: 600px;
}

/* Mobile Card View */
.mobile-cards-container {
    display: none;
    margin-top: 20px;
}

@media screen and (max-width: 768px) {
    .mobile-cards-container {
        display: block;
    }

    .table-container {
        display: none;
    }
}

.employee-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
    position: relative;
}

.employee-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.employee-card.selected-card {
    border-left: 4px solid #ffc107;
    background-color: #fff8e1;
}

.employee-card.inactive-card {
    border-left: 4px solid #dc3545;
    background-color: #f8f9fa;
    opacity: 0.8;
}

.card-header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.card-nik {
    font-weight: bold;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-select {
    display: flex;
    align-items: center;
}

.card-body-section {
    padding: 15px;
}

.card-info-item {
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
}

.card-info-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

.card-info-value {
    font-weight: 500;
    color: #333;
}

.card-actions {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #eee;
}

.card-actions .btn-group {
    display: flex;
    gap: 5px;
}

.card-actions .btn {
    padding: 8px 12px;
    font-size: 13px;
}

.loading-message i {
    font-size: 36px;
    color: rgb(157, 0, 0);
    margin-bottom: 15px;
    animation: spin 1s infinite linear;
}

.loading-message p {
    margin-bottom: 10px;
    font-size: 16px;
}

.loading-message .text-muted {
    color: #6c757d;
    font-size: 14px;
}

.loading-message .progress {
    height: 20px;
    border-radius: 10px;
    background-color: #e9ecef;
    margin: 15px 0;
}

.loading-message .progress-bar {
    background-color: rgb(157, 0, 0);
    border-radius: 10px;
    transition: width 0.3s ease;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 50px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* Table Controls Section */
.table-controls-section {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 25px;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    position: relative;
}

/* Entries and Filters Section */
.entries-filters-section {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    flex: 1;
    min-width: 300px;
}

/* Entries Control */
.entries-control {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.entries-label {
    color: #495057;
    font-weight: 500;
    white-space: nowrap;
}

.entries-select {
    padding: 8px 15px;
    border: 1px solid #ced4da;
    border-radius: 8px;
    background: white;
    min-width: 80px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Copy Controls Section */
.copy-controls-section {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    min-width: 300px;
}

/* Control Button Styles */
.control-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 15px;
    border-radius: 8px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    text-decoration: none;
    white-space: nowrap;
}

.control-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0,0,0,0.15);
}

.control-button i {
    margin-right: 8px;
    font-size: 14px;
}

/* Reset Button */
.reset-button {
    background-color: #6c757d;
    color: white;
}

.reset-button:hover {
    background-color: #5a6268;
    color: white;
}

/* Copy Button */
.copy-button {
    background-color: #BF0000;
    color: white;
}

.copy-button:hover {
    background-color: #a50000;
    color: white;
}

/* Reset NIK Button */
.reset-nik-button {
    background-color: #dc3545;
    color: white;
    opacity: 0.7;
}

.reset-nik-button:hover:not([disabled]) {
    background-color: #c82333;
    color: white;
    opacity: 1;
}

.reset-nik-button[disabled] {
    cursor: not-allowed;
    opacity: 0.5;
}

/* Columns Button */
.columns-button {
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #ced4da;
}

.columns-button:hover {
    background-color: #e9ecef;
    color: #212529;
}

.columns-button .fa-caret-down {
    margin-left: 5px;
    margin-right: 0;
}

/* Warning message for large data sets */
.entries-warning {
    padding: 8px 12px;
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    border-radius: 8px;
    color: #856404;
    font-size: 13px;
    display: flex;
    align-items: center;
    max-width: 100%;
    margin-top: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.entries-warning i {
    margin-right: 8px;
    color: #f0ad4e;
    font-size: 16px;
    flex-shrink: 0;
}

/* Style for warning options in dropdown */
#entriesSelect option[data-warning="true"] {
    color: #856404;
    background-color: #fff3cd;
}

/* Entries Info */
.entries-info {
    color: #6c757d;
    font-size: 14px;
    width: 100%;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

/* Button Link Style */
.btn-link {
    background: none;
    border: none;
    color: #BF0000;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 13px;
    text-decoration: underline;
}

.btn-link:hover {
    color: #a50000;
    text-decoration: none;
}

/* NIK Copy Button */
.nik-container {
    display: flex;
    align-items: center;
}

.nik-text {
    margin-right: 0px;
}

.copy-btn {
    background: none;
    border: none;
    color: #BF0000;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.2s;
}

.copy-btn:hover {
    background: #f8f9fa;
    color: #a50000;
}

.copy-btn.copied {
    color: #28a745;
}

/* Tooltip */
.tooltip {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    z-index: 1000;
}

/* Bulk Copy Button and Dropdown */
.bulk-copy-container {
    display: flex;
    position: relative;
    z-index: 10; /* Ensure container has higher z-index */
}

#bulkCopyBtn {
    background-color: #BF0000;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px 0 0 5px;
    cursor: pointer;
    transition: all 0.2s;
}

#resetSelectedNIKsBtn {
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s;
    margin-right: 5px;
}

#bulkCopyBtn:hover {
    background-color: #a50000;
}

#bulkCopyBtn.copied {
    background-color: #28a745;
    border-color: #28a745;
}

#copyColumnsBtn {
    border-radius: 0 5px 5px 0;
    border-left: 0;
    padding: 8px 10px;
}

.copy-columns-dropdown {
    position: relative;
    z-index: 1001; /* Match container z-index */
}

.copy-columns-menu {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    width: 250px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 9999; /* Increased z-index to ensure it appears above other elements */
    margin-top: 5px;
    overflow: visible; /* Ensure content isn't clipped */
    transform: translateZ(0); /* Force hardware acceleration */
}

.copy-columns-menu.show {
    display: block;
    position: absolute; /* Ensure it's positioned absolutely */
}

.copy-columns-header {
    padding: 10px;
    border-bottom: 1px solid #eee;
    font-weight: bold;
    color: #333;
}

.copy-columns-options {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
}

.copy-columns-info {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
    font-style: italic;
    text-align: center;
}

.copy-column-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    margin-bottom: 5px;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: #f9f9f9;
    cursor: move;
}

.copy-column-item:hover {
    background-color: #f0f0f0;
}

.copy-column-item.dragging {
    opacity: 0.5;
    border: 1px dashed #999;
}

.copy-column-item.drag-over {
    border: 1px dashed #BF0000;
    background-color: #fff8e1;
}

.drag-handle {
    margin-right: 8px;
    color: #999;
    cursor: grab;
}

.copy-columns-options label {
    display: block;
    margin-bottom: 0;
    cursor: pointer;
    flex-grow: 1;
}

.copy-columns-options input[type="checkbox"] {
    margin-right: 8px;
}

.copy-columns-footer {
    padding: 10px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
}

.copy-columns-footer button {
    padding: 3px 8px;
    font-size: 12px;
}

/* Selected Row Style */
.selected-row {
    background-color: #fff8e1 !important;
    border-left: 3px solid #ffc107;
}

.selected-row:hover {
    background-color: #ffecb3 !important;
}

/* Inactive Employee Style */
.inactive-row {
    background-color: #f8f9fa !important;
    color: #6c757d !important;
    border-left: 3px solid #dc3545;
    text-decoration: line-through;
}

.inactive-row:hover {
    background-color: #e9ecef !important;
}

.inactive-row td {
    opacity: 0.7;
}

.inactive-row .btn-group .btn {
    opacity: 0.8;
}

/* Optional Column Style */
.optional-column {
    display: none;
}

/* Table Actions Style */
.table-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

/* Column Toggle Dialog */
.column-toggle-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    z-index: 1100;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.column-toggle-dialog h3 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.column-toggle-select-actions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.column-toggle-select-actions button {
    padding: 5px 10px;
    font-size: 0.9rem;
}

.column-toggle-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    max-height: 300px;
    overflow-y: auto;
    padding-right: 5px;
}

.column-toggle-option {
    display: flex;
    align-items: center;
    padding: 8px;
    border: 1px solid #eee;
    border-radius: 4px;
}

.column-toggle-option input {
    margin-right: 10px;
}

.column-toggle-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    border-top: 1px solid #eee;
    padding-top: 15px;
}

/* Employee Detail Modal */
.employee-detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1050;
}

.employee-detail-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.employee-detail-header {
    background-color: #BF0000;
    color: white;
    padding: 15px 20px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.employee-detail-header h3 {
    margin: 0;
    font-size: 1.25rem;
}

.employee-detail-body {
    padding: 20px;
}

.employee-detail-close {
    background: transparent;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

.employee-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.employee-info-item {
    margin-bottom: 15px;
}

.employee-info-label {
    font-weight: bold;
    color: #555;
    margin-bottom: 5px;
    display: block;
}

.employee-info-value {
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

/* Add Employee Button Style */
#addEmployeeBtn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#addEmployeeBtn i {
    margin-right: 8px;
}

#addEmployeeBtn:hover {
    background-color: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.modal-dialog {
    margin: 30px auto;
    max-width: 800px;
}

.modal-content {
    position: relative;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    padding: 0;
    outline: 0;
}

.modal-header {
    padding: 15px 20px;
    background-color: #BF0000;
    color: white;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.close {
    color: white;
    background: transparent;
    border: none;
    font-size: 1.5rem;
    font-weight: 700;
    cursor: pointer;
}

.modal-body {
    padding: 20px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -10px;
    margin-left: -10px;
}

.form-group {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 10px;
    margin-bottom: 15px;
}

.form-group.full-width {
    flex: 0 0 100%;
    max-width: 100%;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.form-control {
    display: block;
    width: 100%;
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.required-field::after {
    content: ' *';
    color: #dc3545;
}
/* Spacer for navbar */
.container-fluid {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    margin-top: 80px; /* Increased top margin to prevent navbar overlap */
    position: relative;
    z-index: 1;
}
</style>
<body>
<?php include '../config/navbara.php'; ?>
<!-- Spacer to prevent navbar overlap -->
<div class="container-fluid">
    <div class="welcome-container">
        <div class="card-header">
            <h3><i class="fas fa-users"></i> Manajemen Karyawan</h3>
        </div>

        <div class="card-body">
            <div class="search-section">
                <!-- Action buttons container -->
                <div class="action-buttons-container">
                    <a href="add_employee.php" id="addEmployeeBtn" class="action-button add-button">
                        <i class="fas fa-plus"></i> <span class="button-text">Tambah Karyawan</span>
                    </a>
                    <a href="compare_import.php" id="compareImportBtn" class="action-button compare-button" style="background-color: #17a2b8;">
                        <i class="fas fa-sync-alt"></i> <span class="button-text">Bandingkan Data Karyawan</span>
                    </a>
                    <a href="upload_karyawan.php" id="uploadEmployeeBtn" class="action-button upload-button">
                        <i class="fas fa-upload"></i> <span class="button-text">Import Data Karyawan</span>
                    </a>
                    <a href="export_karyawan.php" id="exportEmployeeBtn" class="action-button export-button" style="background-color: #28a745;">
                        <i class="fas fa-file-export"></i> <span class="button-text">Export Data Karyawan</span>
                    </a>
                    <a href="delete_karyawan.php" id="deleteEmployeeBtn" class="action-button delete-button">
                        <i class="fas fa-trash"></i> <span class="button-text">Hapus Masal Data Karyawan</span>
                    </a>
                    <a href="manage_duplicate_employees.php" id="manageDuplicatesBtn" class="action-button duplicate-button">
                        <i class="fas fa-user-friends"></i> <span class="button-text">Kelola Duplikat Data Karyawan</span>
                    </a>
                    <a href="check_employee_count.php" id="checkEmployeeCountBtn" class="action-button info-button">
                        <i class="fas fa-chart-bar"></i> <span class="button-text">Lihat Statistik Data Karyawan</span>
                    </a>
                    <!-- <a href="employee_history.php" id="employeeHistoryBtn" class="action-button history-button">
                        <i class="fas fa-history"></i> <span class="button-text">Riwayat Perubahan</span>
                    </a> -->
                </div>
            </div>
            

            <!-- Table Controls Section -->
            <div class="table-controls-section">
                <!-- Entries and Filters Section -->
                <div class="entries-filters-section">
                    <!-- Entries Select -->
                    <div class="entries-control">
                        <div class="entries-label">Show</div>
                        <select id="entriesSelect" class="entries-select">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                            <option value="200">200</option>
                            <option value="500">500</option>
                            <option value="1000">1000</option>
                            <option value="2000" data-warning="true">2000 (Mungkin lambat)</option>
                            <option value="5000" data-warning="true">5000 (Lambat)</option>
                            <option value="10000" data-warning="true">10000 (Sangat lambat)</option>
                            <option value="20000" data-warning="true">20000 (Ekstrem lambat)</option>
                        </select>
                        <div class="entries-label">entries</div>
                    </div>

                    <!-- Reset Filters Button -->
                    <button id="resetFiltersBtn" class="control-button reset-button" onclick="resetAllFilters()">
                        <i class="fas fa-undo"></i> <span class="button-text">Reset Semua Filter</span>
                    </button>

                    <!-- Warning for large data sets -->
                    <div id="entriesWarning" class="entries-warning" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Menampilkan data dalam jumlah besar dapat menyebabkan browser menjadi lambat.</span>
                    </div>
                </div>

                <!-- Copy Controls Section -->
                <div class="copy-controls-section">
                    <!-- Bulk Copy Button -->
                    <button id="bulkCopyBtn" class="control-button copy-button" onclick="bulkCopyNIK()">
                        <i class="fas fa-copy"></i> <span class="button-text">Copy Kolom Terpilih</span>
                    </button>

                    <!-- Reset Selected NIKs Button -->
                    <button id="resetSelectedNIKsBtn" class="control-button reset-nik-button" onclick="resetSelectedNIKs()" disabled>
                        <i class="fas fa-times"></i> <span class="button-text">Reset NIK Terpilih</span>
                    </button>

                    <!-- Copy Columns Dropdown -->
                    <div class="copy-columns-dropdown">
                        <button id="copyColumnsBtn" class="control-button columns-button" type="button">
                            <i class="fas fa-columns"></i> <span class="button-text">Kolom</span> <i class="fas fa-caret-down"></i>
                        </button>
                        <div id="copyColumnsDropdown" class="copy-columns-menu">
                            <div class="copy-columns-header">Pilih kolom yang akan disalin:</div>
                            <div class="copy-columns-options" id="copyColumnsOptions">
                                <div class="copy-columns-info">Urutan kolom akan mengikuti urutan centang</div>
                                <div class="copy-column-item" draggable="true" data-column="nama">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="nama" checked> Nama</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="tgl_masuk">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="tgl_masuk"> Tgl Masuk</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="jk">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="jk"> JK</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="level_karyawan">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="level_karyawan"> Level Karyawan</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="tgl_lahir">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="tgl_lahir"> Tgl Lahir</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="agama">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="agama"> Agama</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="pendidikan_akhir">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="pendidikan_akhir"> Pendidikan Akhir</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="no_telp">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="no_telp"> No. Telp</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="dept">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="dept"> Dept</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="bagian">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="bagian"> Bagian</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="jabatan">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="jabatan"> Jabatan</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="group">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="group"> Group</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="status">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="status"> Status</label>
                                </div>
                                <div class="copy-column-item" draggable="true" data-column="pt">
                                    <span class="drag-handle"><i class="fas fa-grip-lines"></i></span>
                                    <label><input type="checkbox" name="copy_columns" value="pt"> PT</label>
                                </div>
                            </div>
                            <div class="copy-columns-footer">
                                <button id="selectAllColumns" class="btn-link">Pilih Semua</button>
                                <button id="deselectAllColumns" class="btn-link">Batal Pilih</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Entries Info -->
                <div id="entriesInfo" class="entries-info">
                    Menampilkan 0 sampai 0 dari 0 entries
                </div>
            </div>
            <div class="table-display-controls">
            <!-- Kotak pencarian utama di dekat tabel -->
            <div class="table-search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="tableSearchInput" placeholder="Cari berdasarkan NIK, nama, departemen, atau jabatan..." aria-label="Cari karyawan">
            </div>

            <div class="table-buttons">
                <button id="resetFiltersBtn" class="btn btn-outline-secondary" onclick="resetAllFilters()" disabled>
                    <i class="fas fa-filter"></i> Reset Filter
                </button>

                <button class="control-button columns-toggle-button" onclick="toggleColumns()">
                    <i class="fas fa-columns"></i> <span class="button-text">Tampilkan/Sembunyikan Kolom</span>
                </button>
            </div>
        </div>
            <div id="loadingIndicator" class="loading-indicator">
                <i class="fas fa-spinner"></i>
                <p>Memuat data...</p>
            </div>

            <div id="employeeTableContainer">
                <!-- Table will be inserted here by JavaScript -->
            </div>

            <div id="paginationContainer" class="pagination-container">
                <!-- Pagination will be inserted here by JavaScript -->
            </div>
        </div>
    </div>
</div>

<script>
// Variabel global untuk akses dari seluruh script
let searchTimeout;
let loadingIndicator;
let tableContainer;
let currentPage = 1;
let entriesPerPage = 10;
let totalEntries = 0;
let currentSearchQuery = '';

// Array untuk menyimpan NIK yang dipilih
let selectedNIKs = [];

// Array untuk menyimpan urutan pemilihan NIK
let selectedNIKsOrder = [];

// Objek untuk menyimpan pengaturan kolom
let columnSettings = {};

// Objek untuk menyimpan pengaturan kolom copy
let copyColumnSettings = {
    selected: [], // Kolom yang dipilih
    order: []     // Urutan kolom
};

// Objek untuk menyimpan filter kolom
let columnFilters = {};

// Variabel untuk menyimpan pengurutan kolom
let sortColumn = '';
let sortDirection = 'asc';

// Objek untuk menyimpan data unik untuk setiap kolom
let uniqueColumnValues = {};

// Fungsi untuk menyimpan pengaturan kolom copy ke localStorage
function saveCopyColumnSettings() {
    // Ambil semua item kolom dalam urutan saat ini
    const columnItems = document.querySelectorAll('.copy-column-item');

    // Reset pengaturan
    copyColumnSettings.selected = [];
    copyColumnSettings.order = [];

    // Simpan pengaturan berdasarkan status saat ini
    columnItems.forEach(item => {
        const column = item.getAttribute('data-column');
        const checkbox = item.querySelector('input[name="copy_columns"]');

        // Simpan urutan semua kolom
        copyColumnSettings.order.push(column);

        // Simpan kolom yang dipilih
        if (checkbox && checkbox.checked) {
            copyColumnSettings.selected.push(column);
        }
    });

    // Simpan ke localStorage
    localStorage.setItem('employeeCopyColumnSettings', JSON.stringify(copyColumnSettings));
}

// Fungsi untuk memuat pengaturan kolom copy dari localStorage
function loadCopyColumnSettings() {
    const savedSettings = localStorage.getItem('employeeCopyColumnSettings');

    if (savedSettings) {
        try {
            copyColumnSettings = JSON.parse(savedSettings);

            // Terapkan pengaturan ke UI
            applyCopyColumnSettings();
        } catch (e) {
            console.error('Error parsing copy column settings:', e);
        }
    }
}

// Fungsi untuk menerapkan pengaturan kolom copy ke UI
function applyCopyColumnSettings() {
    const container = document.getElementById('copyColumnsOptions');
    if (!container) return;

    // Ambil semua item kolom
    const items = Array.from(container.querySelectorAll('.copy-column-item'));

    // Jika ada pengaturan urutan, terapkan
    if (copyColumnSettings.order && copyColumnSettings.order.length > 0) {
        // Urutkan item berdasarkan pengaturan urutan
        items.sort((a, b) => {
            const aColumn = a.getAttribute('data-column');
            const bColumn = b.getAttribute('data-column');

            const aIndex = copyColumnSettings.order.indexOf(aColumn);
            const bIndex = copyColumnSettings.order.indexOf(bColumn);

            // Jika kolom tidak ada dalam pengaturan, letakkan di akhir
            if (aIndex === -1) return 1;
            if (bIndex === -1) return -1;

            return aIndex - bIndex;
        });

        // Terapkan urutan ke DOM
        items.forEach(item => {
            container.appendChild(item);
        });
    }

    // Terapkan pengaturan checkbox
    if (copyColumnSettings.selected && copyColumnSettings.selected.length > 0) {
        // Reset semua checkbox
        items.forEach(item => {
            const checkbox = item.querySelector('input[name="copy_columns"]');
            if (checkbox) {
                checkbox.checked = false;
            }
        });

        // Centang checkbox yang dipilih
        copyColumnSettings.selected.forEach(column => {
            const item = container.querySelector(`.copy-column-item[data-column="${column}"]`);
            if (item) {
                const checkbox = item.querySelector('input[name="copy_columns"]');
                if (checkbox) {
                    checkbox.checked = true;
                }
            }
        });
    }
}

// Fungsi helper untuk escape HTML yang aman
function escapeHtml(unsafe) {
    if (unsafe === null || unsafe === undefined) {
        return '';
    }
    // Konversi ke string untuk memastikan input bisa di-escape
    const str = String(unsafe);
    return str
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// Fungsi untuk update info entries
function updateEntriesInfo() {
    const start = ((currentPage - 1) * entriesPerPage) + 1;
    const end = Math.min(currentPage * entriesPerPage, totalEntries);
    document.getElementById('entriesInfo').textContent =
        `Menampilkan ${start} sampai ${end} dari ${totalEntries} entries`;
}

// Fungsi untuk update pagination
function updatePagination() {
    const totalPages = Math.ceil(totalEntries / entriesPerPage);
    const paginationContainer = document.getElementById('paginationContainer');

    let paginationHTML = '<div class="pagination-buttons">';

    // Previous button
    paginationHTML += `
        <button class="pagination-button"
                onclick="changePage(${currentPage - 1})"
                ${currentPage === 1 ? 'disabled' : ''}>
            Previous
        </button>`;

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <button class="pagination-button ${i === currentPage ? 'active' : ''}"
                        onclick="changePage(${i})">
                    ${i}
                </button>`;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<span class="pagination-ellipsis">...</span>';
        }
    }

    // Next button
    paginationHTML += `
        <button class="pagination-button"
                onclick="changePage(${currentPage + 1})"
                ${currentPage === totalPages ? 'disabled' : ''}>
            Next
        </button>`;

    paginationHTML += '</div>';
    paginationContainer.innerHTML = paginationHTML;
}

// Fungsi untuk ganti halaman
function changePage(newPage) {
    // Debug: Log halaman saat ini dan halaman baru
    console.log('Changing page from', currentPage, 'to', newPage);

    const totalPages = Math.ceil(totalEntries / entriesPerPage);
    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;

        // Debug: Log currentSearchQuery sebelum memanggil searchEmployees
        console.log('currentSearchQuery before search:', currentSearchQuery);

        // Pastikan currentSearchQuery adalah string
        const query = typeof currentSearchQuery === 'string' ? currentSearchQuery :
                     (typeof currentSearchQuery === 'object' && currentSearchQuery !== null && currentSearchQuery.trimmed !== undefined) ?
                     currentSearchQuery.trimmed : '';

        searchEmployees(query);
    }
}

// Fungsi untuk menerapkan pengaturan kolom yang tersimpan
function applyColumnSettings() {
    // Jika tidak ada pengaturan kolom, coba ambil dari localStorage
    if (Object.keys(columnSettings).length === 0) {
        const savedSettings = localStorage.getItem('employeeColumnSettings');
        if (savedSettings) {
            try {
                columnSettings = JSON.parse(savedSettings);
            } catch (e) {
                console.error('Error parsing column settings:', e);
                return;
            }
        } else {
            return; // Tidak ada pengaturan yang tersimpan
        }
    }

    // Terapkan pengaturan untuk setiap kolom
    Object.entries(columnSettings).forEach(([columnName, isVisible]) => {
        const cells = document.querySelectorAll(`#employeeTable th[data-column="${columnName}"], #employeeTable td[data-column="${columnName}"]`);
        cells.forEach(cell => {
            if (isVisible) {
                cell.classList.remove('optional-column');
            } else {
                cell.classList.add('optional-column');
            }
        });
    });
}

// Fungsi untuk update tabel
function updateTable(employees) {
    if (!Array.isArray(employees)) {
        console.error('Expected array of employees');
        return;
    }

    const tableContainer = document.getElementById('employeeTableContainer');

    if (!tableContainer) {
        console.error('Table container not found');
        return;
    }

    if (employees.length === 0 && selectedNIKs.length === 0) {
        tableContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <p>Tidak ada data yang ditemukan</p>
            </div>`;
        return;
    }

    tableContainer.innerHTML = `


        <!-- Mobile Cards View -->
        <div class="mobile-cards-container" id="mobileCardsContainer"></div>

        <!-- Desktop Table View -->
        <div class="table-container">
            <table class="table" id="employeeTable">
                <thead>
                    <tr>
                        <th width="30px"></th>
                        <th data-column="nik" class="sortable-column">
                            <div class="column-header">
                                <span>NIK</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('nik')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-nik">
                                        <div class="filter-search">
                                            <input type="text" placeholder="Cari NIK..." onkeyup="filterColumn('nik', this.value)">
                                        </div>
                                        <div class="filter-options" id="filter-options-nik"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('nik')">Terapkan</button>
                                            <button onclick="clearFilter('nik')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="nama" class="sortable-column">
                            <div class="column-header">
                                <span>Nama</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('nama')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-nama">
                                        <div class="filter-search">
                                            <input type="text" placeholder="Cari Nama..." onkeyup="filterColumn('nama', this.value)">
                                        </div>
                                        <div class="filter-options" id="filter-options-nama"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('nama')">Terapkan</button>
                                            <button onclick="clearFilter('nama')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="tgl_masuk" class="sortable-column">
                            <div class="column-header">
                                <span>Tgl Masuk</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('tgl_masuk')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-tgl_masuk">
                                        <div class="filter-search">
                                            <input type="text" placeholder="Cari Tanggal..." onkeyup="filterColumn('tgl_masuk', this.value)">
                                        </div>
                                        <div class="filter-options" id="filter-options-tgl_masuk"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('tgl_masuk')">Terapkan</button>
                                            <button onclick="clearFilter('tgl_masuk')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="jk" class="sortable-column">
                            <div class="column-header">
                                <span>JK</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('jk')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-jk">
                                        <div class="filter-options" id="filter-options-jk">
                                            <label><input type="checkbox" value="L"> Laki-laki (L)</label>
                                            <label><input type="checkbox" value="P"> Perempuan (P)</label>
                                        </div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('jk')">Terapkan</button>
                                            <button onclick="clearFilter('jk')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="level_karyawan" class="sortable-column">
                            <div class="column-header">
                                <span>Level</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('level_karyawan')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-level_karyawan">
                                        <div class="filter-search">
                                            <input type="text" placeholder="Cari Level..." onkeyup="filterColumn('level_karyawan', this.value)">
                                        </div>
                                        <div class="filter-options" id="filter-options-level_karyawan"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('level_karyawan')">Terapkan</button>
                                            <button onclick="clearFilter('level_karyawan')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="tgl_lahir" class="sortable-column">
                            <div class="column-header">
                                <span>Tgl Lahir</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('tgl_lahir')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-tgl_lahir">
                                        <div class="filter-search">
                                            <input type="text" placeholder="Cari Tanggal..." onkeyup="filterColumn('tgl_lahir', this.value)">
                                        </div>
                                        <div class="filter-options" id="filter-options-tgl_lahir"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('tgl_lahir')">Terapkan</button>
                                            <button onclick="clearFilter('tgl_lahir')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="agama" class="sortable-column">
                            <div class="column-header">
                                <span>Agama</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('agama')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-agama">
                                        <div class="filter-options" id="filter-options-agama"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('agama')">Terapkan</button>
                                            <button onclick="clearFilter('agama')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="pendidikan_akhir" class="sortable-column">
                            <div class="column-header">
                                <span>Pendidikan</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('pendidikan_akhir')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-pendidikan_akhir">
                                        <div class="filter-options" id="filter-options-pendidikan_akhir"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('pendidikan_akhir')">Terapkan</button>
                                            <button onclick="clearFilter('pendidikan_akhir')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="no_telp" class="sortable-column">
                            <div class="column-header">
                                <span>No. Telp</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('no_telp')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-no_telp">
                                        <div class="filter-search">
                                            <input type="text" placeholder="Cari No. Telp..." onkeyup="filterColumn('no_telp', this.value)">
                                        </div>
                                        <div class="filter-options" id="filter-options-no_telp"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('no_telp')">Terapkan</button>
                                            <button onclick="clearFilter('no_telp')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="dept" class="sortable-column">
                            <div class="column-header">
                                <span>Dept</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('dept')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-dept">
                                        <div class="filter-options" id="filter-options-dept"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('dept')">Terapkan</button>
                                            <button onclick="clearFilter('dept')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="bagian" class="sortable-column">
                            <div class="column-header">
                                <span>Bagian</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('bagian')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-bagian">
                                        <div class="filter-options" id="filter-options-bagian"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('bagian')">Terapkan</button>
                                            <button onclick="clearFilter('bagian')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="jabatan" class="sortable-column">
                            <div class="column-header">
                                <span>Jabatan</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('jabatan')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-jabatan">
                                        <div class="filter-options" id="filter-options-jabatan"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('jabatan')">Terapkan</button>
                                            <button onclick="clearFilter('jabatan')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="group" class="sortable-column">
                            <div class="column-header">
                                <span>Group</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('group')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-group">
                                        <div class="filter-options" id="filter-options-group"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('group')">Terapkan</button>
                                            <button onclick="clearFilter('group')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="status" class="sortable-column">
                            <div class="column-header">
                                <span>Status</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('status')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-status">
                                        <div class="filter-options" id="filter-options-status">
                                            <label><input type="checkbox" value="Aktif"> Aktif</label>
                                            <label><input type="checkbox" value="Tidak Aktif"> Tidak Aktif</label>
                                            <label><input type="checkbox" value="Resign"> Resign</label>
                                            <label><input type="checkbox" value="Habis Kontrak"> Habis Kontrak</label>
                                        </div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('status')">Terapkan</button>
                                            <button onclick="clearFilter('status')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th data-column="pt" class="sortable-column">
                            <div class="column-header">
                                <span>PT</span>
                                <div class="column-filter">
                                    <button class="filter-button" onclick="toggleFilter('pt')"><i class="fas fa-filter"></i></button>
                                    <div class="filter-dropdown" id="filter-pt">
                                        <div class="filter-options" id="filter-options-pt"></div>
                                        <div class="filter-actions">
                                            <button onclick="applyFilter('pt')">Terapkan</button>
                                            <button onclick="clearFilter('pt')">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody id="employeesTableBody"></tbody>
            </table>
        </div>
    `;

    const tableBody = document.getElementById('employeesTableBody');

    // Pisahkan karyawan yang dipilih dan yang tidak dipilih
    const selectedEmployees = [];
    const unselectedEmployees = [];

    // Kelompokkan karyawan berdasarkan NIK yang dipilih
    employees.forEach(employee => {
        if (selectedNIKs.includes(employee.nik)) {
            selectedEmployees.push(employee);
        } else {
            unselectedEmployees.push(employee);
        }
    });

    // Gabungkan karyawan yang dipilih di atas, diikuti yang tidak dipilih
    const sortedEmployees = [...selectedEmployees, ...unselectedEmployees];

    // Render tabel
    tableBody.innerHTML = sortedEmployees.map(employee => {
        const isSelected = selectedNIKs.includes(employee.nik);
        const isInactive = employee.status && (employee.status.toLowerCase().includes('tidak aktif') ||
                                              employee.status.toLowerCase().includes('keluar') ||
                                              employee.status.toLowerCase().includes('habis kontrak') ||
                                              employee.status.toLowerCase().includes('resign'));

        let rowClass = '';
        if (isSelected) rowClass += 'selected-row ';
        if (isInactive) rowClass += 'inactive-row ';

        const rowClassAttr = rowClass ? `class="${rowClass.trim()}"` : '';

        return `
        <tr ${rowClassAttr} data-nik="${escapeHtml(employee.nik)}">
            <td>
                <input type="checkbox" name="select-nik" value="${escapeHtml(employee.nik)}"
                       ${isSelected ? 'checked' : ''}
                       onchange="toggleNIKSelection(this)">
            </td>
            <td data-column="nik">
                <div class="nik-container">
                    <div class="nik-text" title="${escapeHtml(employee.nik)}">${escapeHtml(employee.nik)}</div>
                    <button class="copy-btn" onclick="copyNIK('${escapeHtml(employee.nik)}', this)" title="Copy Nik">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </td>
            <td data-column="nama" title="${escapeHtml(employee.nama)}">${escapeHtml(employee.nama)}</td>
            <td data-column="tgl_masuk" title="${escapeHtml(employee.tgl_masuk)}">${escapeHtml(employee.tgl_masuk || '-')}</td>
            <td data-column="jk" title="${escapeHtml(employee.jk)}">${escapeHtml(employee.jk || '-')}</td>
            <td data-column="level_karyawan" title="${escapeHtml(employee.level_karyawan)}">${escapeHtml(employee.level_karyawan || '-')}</td>
            <td data-column="tgl_lahir" title="${escapeHtml(employee.tgl_lahir)}">${escapeHtml(employee.tgl_lahir || '-')}</td>
            <td data-column="agama" title="${escapeHtml(employee.agama)}">${escapeHtml(employee.agama || '-')}</td>
            <td data-column="pendidikan_akhir" title="${escapeHtml(employee.pendidikan_akhir)}">${escapeHtml(employee.pendidikan_akhir || '-')}</td>
            <td data-column="no_telp" title="${escapeHtml(employee.no_telp)}">${escapeHtml(employee.no_telp || '-')}</td>
            <td data-column="dept" title="${escapeHtml(employee.dept)}">${escapeHtml(employee.dept || '-')}</td>
            <td data-column="bagian" title="${escapeHtml(employee.bagian)}">${escapeHtml(employee.bagian || '-')}</td>
            <td data-column="jabatan" title="${escapeHtml(employee.jabatan)}">${escapeHtml(employee.jabatan || '-')}</td>
            <td data-column="group" title="${escapeHtml(employee.group)}">${escapeHtml(employee.group || '-')}</td>
            <td data-column="status" title="${escapeHtml(employee.status)}">${escapeHtml(employee.status || '-')}</td>
            <td data-column="pt" title="${escapeHtml(employee.pt)}">${escapeHtml(employee.pt || '-')}</td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="showEmployeeDetail('${escapeHtml(employee.nik)}')" title="Lihat Detail">
                            <i class="fas fa-eye"></i>
                        </button>
                    <a href="edit_employee.php?nik=${escapeHtml(employee.nik)}" class="btn btn-sm btn-warning" title="Edit Data">
                        <i class="fas fa-edit"></i>
                    </a>
                    <button class="btn btn-sm btn-danger" onclick="deleteEmployee('${escapeHtml(employee.nik)}')" title="Hapus Data">
                        <i class="fas fa-trash"></i>
                    </button>
                    </td>
        </tr>
    `}).join('');

    // Isi tampilan kartu mobile dengan data yang sama
    populateMobileCards(sortedEmployees);

    // Simpan posisi kursor input pencarian sebelum memperbarui tabel
    const searchInput = document.getElementById('tableSearchInput');
    const cursorPosition = searchInput ? searchInput.selectionStart : null;

    // Terapkan pengaturan kolom setelah tabel diperbarui
    setTimeout(() => {
        applyColumnSettings();

        // Tambahkan event listener untuk klik pada baris tabel
        setupTableRowSelection();

        // Inisialisasi kotak pencarian tabel
        setupTableSearchInput();

        // Kembalikan posisi kursor jika sebelumnya disimpan
        if (searchInput && cursorPosition !== null) {
            setTimeout(() => {
                // Pastikan posisi kursor tidak melebihi panjang teks saat ini
                const maxPosition = searchInput.value.length;
                const newPosition = Math.min(cursorPosition, maxPosition);
                searchInput.setSelectionRange(newPosition, newPosition);
            }, 10);
        }

        // Tambahkan event listener untuk pengurutan kolom
        setupSortableColumns();
    }, 0);
}

// Fungsi untuk mencari karyawan
async function searchEmployees(query = '') {
    // Simpan posisi kursor saat ini
    const searchInput = document.getElementById('tableSearchInput');
    const cursorPosition = searchInput ? searchInput.selectionStart : null;
    const currentInputValue = searchInput ? searchInput.value : '';

    // Pastikan query adalah string untuk pencarian
    let searchQuery = '';
    if (typeof query === 'object') {
        if (query && query.trimmed !== undefined) {
            searchQuery = query.trimmed;
        }
    } else {
        searchQuery = typeof query === 'string' ? query.trim() : '';
    }

    // Debug log - awal pencarian
    if (window.debugLog) {
        window.debugLog('Memulai pencarian karyawan', {
            query: query,
            page: currentPage,
            limit: entriesPerPage,
            filters: columnFilters,
            sort: { column: sortColumn, direction: sortDirection },
            selectedNIKs: selectedNIKs.length
        });
    }

    // Debug: Tampilkan parameter pencarian di console
    console.log('Pencarian dengan parameter:', {
        query: query,
        page: currentPage,
        limit: entriesPerPage,
        filters: JSON.parse(JSON.stringify(columnFilters)),
        sort: { column: sortColumn, direction: sortDirection },
        selectedNIKs: selectedNIKs.length
    });

    const startTime = performance.now();

    try {
        // Simpan query saat ini
        if (typeof query === 'string') {
            currentSearchQuery = query;
        }

        // Debug log - query yang digunakan
        if (window.debugLog) {
            window.debugLog('Query yang digunakan', {
                currentSearchQuery: currentSearchQuery
            });
        }

        // Kirim NIK yang dipilih ke server
        const selectedNIKsParam = encodeURIComponent(JSON.stringify(selectedNIKs));

        // Kirim filter kolom ke server
        const columnFiltersParam = encodeURIComponent(JSON.stringify(columnFilters));

        // Debug: Tampilkan parameter yang dikirim ke server
        console.log('Parameter yang dikirim ke server:', {
            selectedNIKs: selectedNIKsParam,
            columnFilters: columnFiltersParam
        });

        // Cek apakah jumlah entri per halaman melebihi batas untuk lazy loading
        if (entriesPerPage > 1000) {
            // Debug log - menggunakan lazy loading
            if (window.debugLog) {
                window.debugLog('Menggunakan lazy loading', {
                    entriesPerPage: entriesPerPage
                });
            }

            // Gunakan lazy loading untuk data besar - tidak perlu menampilkan loading indicator standar
            await loadEmployeesLazy(query, selectedNIKsParam, columnFiltersParam);
        } else {
            // Debug log - menggunakan loading normal
            if (window.debugLog) {
                window.debugLog('Menggunakan loading normal', {
                    entriesPerPage: entriesPerPage
                });
            }

            // Gunakan metode normal untuk data kecil
            // Tampilkan loading indicator standar
            loadingIndicator.style.display = 'block';
            tableContainer.style.opacity = '0.5';

            // Buat URL dengan semua parameter
            let url = `search_employees.php?search=${encodeURIComponent(searchQuery)}&limit=${entriesPerPage}&page=${currentPage}&selected_niks=${selectedNIKsParam}&column_filters=${columnFiltersParam}&sort_column=${sortColumn}&sort_direction=${sortDirection}`;

            // Debug: Tampilkan URL yang akan digunakan
            console.log('URL request:', url);

            // Debug log - URL request
            if (window.debugLog) {
                window.debugLog('Mengirim request', {
                    url: cleanUrl
                });
            }

            const fetchStartTime = performance.now();
            const response = await fetch(url);
            const fetchEndTime = performance.now();

            // Debug log - response diterima
            if (window.debugLog) {
                window.debugLog('Response diterima', {
                    status: response.status,
                    statusText: response.statusText,
                    time: ((fetchEndTime - fetchStartTime) / 1000).toFixed(3) + ' detik',
                    headers: Object.fromEntries([...response.headers])
                });
            }

            // Debug: Tampilkan response status
            console.log('Response status:', response.status, response.statusText);

            // Cek content type
            const contentType = response.headers.get('content-type');
            console.log('Content-Type:', contentType);

            if (!contentType || !contentType.includes('application/json')) {
                if (window.debugLog) {
                    window.debugLog('Error: Content type tidak valid', {
                        contentType: contentType
                    });
                }

                // Jika bukan JSON, coba ambil text untuk debugging
                const responseText = await response.text();
                console.error('Response bukan JSON:', responseText.substring(0, 500));
                throw new Error('Response bukan dalam format JSON');
            }

            if (!response.ok) {
                if (window.debugLog) {
                    window.debugLog('Error: Response tidak OK', {
                        status: response.status,
                        statusText: response.statusText
                    });
                }

                // Coba ambil pesan error dari response
                const errorJson = await response.json().catch(e => ({ message: 'Tidak dapat parse response JSON' }));
                console.error('Error response:', errorJson);
                throw new Error(errorJson.message || 'Network response was not ok');
            }

            const jsonStartTime = performance.now();
            const data = await response.json();
            const jsonEndTime = performance.now();

            // Debug: Tampilkan data yang diterima
            console.log('Data received:', {
                success: data.success,
                totalRecords: data.total_records,
                employeeCount: data.employees ? data.employees.length : 0
            });

            // Debug log - parsing JSON
            if (window.debugLog) {
                window.debugLog('JSON parsing selesai', {
                    time: ((jsonEndTime - jsonStartTime) / 1000).toFixed(3) + ' detik',
                    success: data.success,
                    totalRecords: data.total_records,
                    employeeCount: data.employees ? data.employees.length : 0
                });
            }

            if (data.success) {
                totalEntries = data.total_records;

                // Debug log - update tabel
                if (window.debugLog) {
                    window.debugLog('Memperbarui tabel', {
                        totalEntries: totalEntries,
                        employeeCount: data.employees.length
                    });
                }

                const updateTableStartTime = performance.now();
                updateTable(data.employees);
                const updateTableEndTime = performance.now();

                // Debug log - tabel diperbarui
                if (window.debugLog) {
                    window.debugLog('Tabel diperbarui', {
                        time: ((updateTableEndTime - updateTableStartTime) / 1000).toFixed(3) + ' detik'
                    });
                }

                updatePagination();
                updateEntriesInfo();

                // Update tombol bulk copy setelah tabel diperbarui
                updateBulkCopyButton();

                // Perbarui nilai unik untuk setiap kolom jika ada
                if (data.unique_values) {
                    if (window.debugLog) {
                        window.debugLog('Memperbarui nilai unik kolom', {
                            columns: Object.keys(data.unique_values)
                        });
                    }

                    Object.keys(data.unique_values).forEach(column => {
                        uniqueColumnValues[column] = data.unique_values[column];
                    });
                }

                // Sembunyikan loading indicator standar
                loadingIndicator.style.display = 'none';
                tableContainer.style.opacity = '1';

                // Restore search input value and cursor position
                if (searchInput) {
                    // Only restore if the value has changed
                    if (searchInput.value !== currentInputValue) {
                        searchInput.value = currentInputValue;
                    }

                    // Restore cursor position
                    if (cursorPosition !== null) {
                        setTimeout(() => {
                            searchInput.focus();
                            searchInput.setSelectionRange(cursorPosition, cursorPosition);
                        }, 50);
                    }
                }
            } else {
                if (window.debugLog) {
                    window.debugLog('Error dari server', {
                        message: data.message || 'Tidak ada pesan error'
                    });
                }
                throw new Error(data.message || 'Terjadi kesalahan saat mencari data');
            }
        }
    } catch (error) {
        // Debug log - error
        if (window.debugLog) {
            window.debugLog('Error dalam searchEmployees', {
                message: error.message,
                stack: error.stack
            });
        }

        console.error('Error:', error);
        tableContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-circle"></i>
                <p>Terjadi kesalahan: ${error.message}</p>
            </div>`;

        // Pastikan loading indicator disembunyikan jika terjadi error
        loadingIndicator.style.display = 'none';
        tableContainer.style.opacity = '1';
    } finally {
        const endTime = performance.now();
        const totalTime = ((endTime - startTime) / 1000).toFixed(3);

        // Debug log - selesai
        if (window.debugLog) {
            window.debugLog('Pencarian karyawan selesai', {
                totalTime: totalTime + ' detik'
            });
        }
    }
}

// Fungsi untuk memuat data karyawan secara bertahap (lazy loading)
async function loadEmployeesLazy(query, selectedNIKsParam, columnFiltersParam) {
    // Debug log - mulai lazy loading
    if (window.debugLog) {
        window.debugLog('Memulai lazy loading', {
            query: query,
            entriesPerPage: entriesPerPage
        });
    }

    const startTime = performance.now();

    // Tampilkan pesan loading khusus untuk data besar
    tableContainer.innerHTML = `
        <div class="loading-message">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Memuat data dalam jumlah besar (${entriesPerPage} entri)...</p>
            <p class="text-muted">Ini mungkin membutuhkan waktu beberapa saat</p>
            <div class="progress mt-3" style="height: 20px;">
                <div id="loadingProgressBar" class="progress-bar progress-bar-striped progress-bar-animated bg-danger"
                     role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
            <p id="loadingStatus" class="mt-2">Mempersiapkan data...</p>
        </div>
    `;

    // Variabel untuk menyimpan semua data karyawan
    let allEmployees = [];
    let offset = 0;
    const chunkSize = 500; // Ukuran chunk yang lebih kecil untuk mengurangi beban server
    let totalRecords = 0;
    let loadedRecords = 0;

    // Debug log - konfigurasi chunk
    if (window.debugLog) {
        window.debugLog('Konfigurasi lazy loading', {
            chunkSize: chunkSize,
            maxEntries: entriesPerPage
        });
    }

    // Buat URL dasar
    const baseUrl = `get_employees_lazy.php?search=${encodeURIComponent(query)}
        &selected_niks=${selectedNIKsParam}
        &column_filters=${columnFiltersParam}
        &sort_column=${sortColumn}
        &sort_direction=${sortDirection}
        &limit=${chunkSize}`;

    const cleanBaseUrl = baseUrl.replace(/\s+/g, '');

    // Debug log - URL dasar
    if (window.debugLog) {
        window.debugLog('URL dasar lazy loading', {
            url: cleanBaseUrl
        });
    }

    // Ambil chunk pertama untuk mendapatkan total records
    const firstChunkUrl = `${cleanBaseUrl}&offset=0`;

    // Debug log - mengambil chunk pertama
    if (window.debugLog) {
        window.debugLog('Mengambil chunk pertama', {
            url: firstChunkUrl
        });
    }

    const chunkStartTime = performance.now();
    const firstResponse = await fetch(firstChunkUrl);
    const chunkEndTime = performance.now();

    // Debug log - response chunk pertama
    if (window.debugLog) {
        window.debugLog('Response chunk pertama', {
            status: firstResponse.status,
            statusText: firstResponse.statusText,
            time: ((chunkEndTime - chunkStartTime) / 1000).toFixed(3) + ' detik'
        });
    }

    if (!firstResponse.ok) {
        if (window.debugLog) {
            window.debugLog('Error: Response chunk pertama tidak OK', {
                status: firstResponse.status,
                statusText: firstResponse.statusText
            });
        }
        throw new Error('Network response was not ok');
    }

    const jsonStartTime = performance.now();
    const firstData = await firstResponse.json();
    const jsonEndTime = performance.now();

    // Debug log - parsing JSON chunk pertama
    if (window.debugLog) {
        window.debugLog('Parsing JSON chunk pertama', {
            time: ((jsonEndTime - jsonStartTime) / 1000).toFixed(3) + ' detik',
            success: firstData.success,
            totalRecords: firstData.total_records,
            employeeCount: firstData.employees ? firstData.employees.length : 0
        });
    }

    if (!firstData.success) {
        if (window.debugLog) {
            window.debugLog('Error: Chunk pertama tidak berhasil', {
                message: firstData.message || 'Tidak ada pesan error'
            });
        }
        throw new Error(firstData.message || 'Terjadi kesalahan saat mengambil data');
    }

    // Simpan total records dan chunk pertama
    totalRecords = firstData.total_records;
    allEmployees = [...firstData.employees];
    loadedRecords = firstData.employees.length;
    offset = loadedRecords;

    // Debug log - chunk pertama berhasil
    if (window.debugLog) {
        window.debugLog('Chunk pertama berhasil dimuat', {
            totalRecords: totalRecords,
            loadedRecords: loadedRecords,
            remainingRecords: totalRecords - loadedRecords
        });
    }

    // Update progress bar
    const progressBar = document.getElementById('loadingProgressBar');
    const loadingStatus = document.getElementById('loadingStatus');
    const updateProgress = () => {
        const percentage = Math.round((loadedRecords / totalRecords) * 100);
        progressBar.style.width = `${percentage}%`;
        progressBar.setAttribute('aria-valuenow', percentage);
        progressBar.textContent = `${percentage}%`;
        loadingStatus.textContent = `Memuat ${loadedRecords} dari ${totalRecords} data (${percentage}%)...`;

        // Debug log - update progress
        if (window.debugLog) {
            window.debugLog('Update progress', {
                loadedRecords: loadedRecords,
                totalRecords: totalRecords,
                percentage: percentage
            });
        }
    };

    updateProgress();

    // Ambil chunk berikutnya jika masih ada data
    let chunkCounter = 1;
    while (loadedRecords < totalRecords && loadedRecords < entriesPerPage) {
        // Buat URL untuk chunk berikutnya
        const nextChunkUrl = `${cleanBaseUrl}&offset=${offset}`;

        // Debug log - mengambil chunk berikutnya
        if (window.debugLog) {
            window.debugLog(`Mengambil chunk #${chunkCounter + 1}`, {
                url: nextChunkUrl,
                offset: offset
            });
        }

        // Ambil chunk berikutnya
        const chunkStartTime = performance.now();
        const response = await fetch(nextChunkUrl);
        const chunkEndTime = performance.now();

        // Debug log - response chunk
        if (window.debugLog) {
            window.debugLog(`Response chunk #${chunkCounter + 1}`, {
                status: response.status,
                statusText: response.statusText,
                time: ((chunkEndTime - chunkStartTime) / 1000).toFixed(3) + ' detik'
            });
        }

        if (!response.ok) {
            if (window.debugLog) {
                window.debugLog(`Error: Response chunk #${chunkCounter + 1} tidak OK`, {
                    status: response.status,
                    statusText: response.statusText
                });
            }
            throw new Error('Network response was not ok');
        }

        const jsonStartTime = performance.now();
        const data = await response.json();
        const jsonEndTime = performance.now();

        // Debug log - parsing JSON chunk
        if (window.debugLog) {
            window.debugLog(`Parsing JSON chunk #${chunkCounter + 1}`, {
                time: ((jsonEndTime - jsonStartTime) / 1000).toFixed(3) + ' detik',
                success: data.success,
                employeeCount: data.employees ? data.employees.length : 0,
                hasMore: data.has_more
            });
        }

        if (!data.success) {
            if (window.debugLog) {
                window.debugLog(`Error: Chunk #${chunkCounter + 1} tidak berhasil`, {
                    message: data.message || 'Tidak ada pesan error'
                });
            }
            throw new Error(data.message || 'Terjadi kesalahan saat mengambil data');
        }

        // Tambahkan data ke array
        allEmployees = [...allEmployees, ...data.employees];
        loadedRecords += data.employees.length;
        offset += data.employees.length;
        chunkCounter++;

        // Update progress
        updateProgress();

        // Hentikan jika sudah mencapai batas entri per halaman
        if (loadedRecords >= entriesPerPage) {
            if (window.debugLog) {
                window.debugLog('Batas entri per halaman tercapai', {
                    loadedRecords: loadedRecords,
                    entriesPerPage: entriesPerPage
                });
            }
            break;
        }

        // Hentikan jika tidak ada lagi data
        if (!data.has_more) {
            if (window.debugLog) {
                window.debugLog('Tidak ada lagi data untuk dimuat', {
                    loadedRecords: loadedRecords,
                    totalRecords: totalRecords
                });
            }
            break;
        }
    }

    // Batasi jumlah data yang ditampilkan sesuai entriesPerPage
    if (allEmployees.length > entriesPerPage) {
        if (window.debugLog) {
            window.debugLog('Membatasi data yang ditampilkan', {
                totalLoaded: allEmployees.length,
                limitedTo: entriesPerPage
            });
        }
        allEmployees = allEmployees.slice(0, entriesPerPage);
    }

    // Debug log - memperbarui tabel
    if (window.debugLog) {
        window.debugLog('Memperbarui tabel dengan data lazy loading', {
            employeeCount: allEmployees.length,
            totalRecords: totalRecords
        });
    }

    // Update total entries dan tabel
    totalEntries = totalRecords;

    const updateTableStartTime = performance.now();
    updateTable(allEmployees);
    const updateTableEndTime = performance.now();

    // Debug log - tabel diperbarui
    if (window.debugLog) {
        window.debugLog('Tabel diperbarui (lazy loading)', {
            time: ((updateTableEndTime - updateTableStartTime) / 1000).toFixed(3) + ' detik'
        });
    }

    updatePagination();
    updateEntriesInfo();

    // Update tombol bulk copy
    updateBulkCopyButton();

    const endTime = performance.now();
    const totalTime = ((endTime - startTime) / 1000).toFixed(3);

    // Debug log - lazy loading selesai
    if (window.debugLog) {
        window.debugLog('Lazy loading selesai', {
            totalTime: totalTime + ' detik',
            chunksLoaded: chunkCounter,
            recordsLoaded: loadedRecords,
            totalRecords: totalRecords
        });
    }
}

// Tunggu hingga DOM selesai dimuat
document.addEventListener('DOMContentLoaded', function() {
    // Inisialisasi elemen DOM
    loadingIndicator = document.getElementById('loadingIndicator');
    tableContainer = document.getElementById('employeeTableContainer');

    // Muat pengaturan kolom copy dari localStorage
    loadCopyColumnSettings();

    // Event listener untuk perubahan jumlah entries
    document.getElementById('entriesSelect').addEventListener('change', function(e) {
        entriesPerPage = parseInt(e.target.value);
        currentPage = 1; // Reset ke halaman pertama saat mengubah jumlah entri per halaman

        // Tampilkan peringatan jika memilih jumlah entri yang besar
        const warningElement = document.getElementById('entriesWarning');
        const selectedOption = e.target.options[e.target.selectedIndex];

        if (selectedOption.hasAttribute('data-warning')) {
            warningElement.style.display = 'flex';

            // Jika jumlah entri sangat besar, konfirmasi terlebih dahulu
            if (entriesPerPage >= 10000) {
                if (!confirm(`Anda akan menampilkan ${entriesPerPage} data sekaligus. Ini dapat menyebabkan browser menjadi lambat atau tidak responsif. Lanjutkan?`)) {
                    // Jika user membatalkan, kembalikan ke nilai default
                    e.target.value = '100';
                    entriesPerPage = 100;
                    warningElement.style.display = 'none';
                }
            }
        } else {
            warningElement.style.display = 'none';
        }

        searchEmployees(typeof currentSearchQuery === 'object' ? currentSearchQuery.trimmed : currentSearchQuery);
    });

    // Event listener untuk dropdown kolom yang akan disalin
    const copyColumnsBtn = document.getElementById('copyColumnsBtn');
    const copyColumnsDropdown = document.getElementById('copyColumnsDropdown');
    const copyColumnsOptions = document.getElementById('copyColumnsOptions');

    // Toggle dropdown saat tombol diklik
    copyColumnsBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        copyColumnsDropdown.classList.toggle('show');
    });

    // Tutup dropdown saat klik di luar dropdown
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.copy-columns-dropdown') && copyColumnsDropdown.classList.contains('show')) {
            copyColumnsDropdown.classList.remove('show');
        }
    });

    // Event listener untuk tombol Pilih Semua
    document.getElementById('selectAllColumns').addEventListener('click', function(e) {
        e.preventDefault();
        document.querySelectorAll('input[name="copy_columns"]').forEach(checkbox => {
            checkbox.checked = true;
        });
        // Simpan pengaturan setelah perubahan
        saveCopyColumnSettings();
    });

    // Event listener untuk tombol Batal Pilih
    document.getElementById('deselectAllColumns').addEventListener('click', function(e) {
        e.preventDefault();
        document.querySelectorAll('input[name="copy_columns"]').forEach(checkbox => {
            checkbox.checked = false;
        });
        // Simpan pengaturan setelah perubahan
        saveCopyColumnSettings();
    });

    // Event listener untuk checkbox kolom
    document.querySelectorAll('input[name="copy_columns"]').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // Simpan pengaturan setiap kali checkbox berubah
            saveCopyColumnSettings();
        });
    });

    // Implementasi drag and drop untuk mengatur urutan kolom
    if (copyColumnsOptions) {
        // Variabel untuk menyimpan elemen yang sedang di-drag
        let draggedItem = null;

        // Ambil semua item yang bisa di-drag
        const items = copyColumnsOptions.querySelectorAll('.copy-column-item');

        // Tambahkan event listener untuk setiap item
        items.forEach(item => {
            // Event saat mulai drag
            item.addEventListener('dragstart', function() {
                draggedItem = this;
                setTimeout(() => {
                    this.classList.add('dragging');
                }, 0);
            });

            // Event saat selesai drag
            item.addEventListener('dragend', function() {
                this.classList.remove('dragging');
                draggedItem = null;

                // Simpan pengaturan setelah drag selesai
                saveCopyColumnSettings();
            });

            // Event saat drag over item lain
            item.addEventListener('dragover', function(e) {
                e.preventDefault();
                if (draggedItem === this) return;

                const box = this.getBoundingClientRect();
                const mouseY = e.clientY;
                const boxMiddleY = box.top + box.height / 2;

                if (mouseY < boxMiddleY) {
                    // Posisikan di atas item ini
                    copyColumnsOptions.insertBefore(draggedItem, this);
                } else {
                    // Posisikan di bawah item ini
                    copyColumnsOptions.insertBefore(draggedItem, this.nextSibling);
                }
            });

            // Event saat drag enter
            item.addEventListener('dragenter', function(e) {
                e.preventDefault();
                if (draggedItem === this) return;
                this.classList.add('drag-over');
            });

            // Event saat drag leave
            item.addEventListener('dragleave', function() {
                this.classList.remove('drag-over');
            });

            // Event saat drop
            item.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('drag-over');
            });
        });

        // Event untuk container
        copyColumnsOptions.addEventListener('dragover', function(e) {
            e.preventDefault();
        });

        copyColumnsOptions.addEventListener('drop', function(e) {
            e.preventDefault();
        });
    }

    // Pencarian sekarang ditangani oleh tableSearchInput



    // Placeholder untuk kotak pencarian akan diatur saat tableSearchInput dibuat

    // Inisialisasi tombol bulk copy
    updateBulkCopyButton();

    // Tambahkan library SheetJS untuk export Excel
    const sheetJsScript = document.createElement('script');
    sheetJsScript.src = 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js';
    document.head.appendChild(sheetJsScript);

    // Muat pengaturan kolom dari localStorage
    const savedSettings = localStorage.getItem('employeeColumnSettings');
    if (savedSettings) {
        try {
            columnSettings = JSON.parse(savedSettings);
            console.log('Loaded column settings:', columnSettings);
        } catch (e) {
            console.error('Error parsing column settings:', e);
        }
    }

    // Tambahkan event listener untuk pengurutan kolom
    // Tunggu sedikit untuk memastikan tabel sudah di-render
    setTimeout(() => {
        const sortableColumns = document.querySelectorAll('th.sortable-column');
        console.log('Found sortable columns:', sortableColumns.length);

        if (sortableColumns && sortableColumns.length > 0) {
            sortableColumns.forEach(header => {
                header.addEventListener('click', function(e) {
                    // Jika klik pada tombol filter, jangan lakukan pengurutan
                    if (e.target.closest('.column-filter')) {
                        return;
                    }

                    const column = this.getAttribute('data-column');
                    console.log('Sorting by column:', column);
                    sortByColumn(column);
                });
            });
        } else {
            console.warn('No sortable columns found in the document');

            // Coba cari dengan selector yang berbeda
            const allHeaders = document.querySelectorAll('th[data-column]');
            console.log('Found headers with data-column attribute:', allHeaders.length);

            if (allHeaders && allHeaders.length > 0) {
                allHeaders.forEach(header => {
                    header.classList.add('sortable-column');
                    header.addEventListener('click', function(e) {
                        // Jika klik pada tombol filter, jangan lakukan pengurutan
                        if (e.target.closest('.column-filter')) {
                            return;
                        }

                        const column = this.getAttribute('data-column');
                        console.log('Sorting by column (fallback):', column);
                        sortByColumn(column);
                    });
                });
                console.log('Added sortable-column class and click handlers to headers');
            }
        }
    }, 500); // Tunggu 500ms untuk memastikan tabel sudah di-render

    // Initial load
    searchEmployees();
});

// Fungsi untuk setup kolom yang dapat diurutkan
function setupSortableColumns() {
    const sortableColumns = document.querySelectorAll('#employeeTable th.sortable-column');
    console.log('Setting up sortable columns:', sortableColumns.length);

    if (sortableColumns && sortableColumns.length > 0) {
        sortableColumns.forEach(header => {
            // Hapus event listener lama untuk mencegah duplikasi
            const newHeader = header.cloneNode(true);
            header.parentNode.replaceChild(newHeader, header);

            newHeader.addEventListener('click', function(e) {
                // Jika klik pada tombol filter, jangan lakukan pengurutan
                if (e.target.closest('.column-filter')) {
                    return;
                }

                const column = this.getAttribute('data-column');
                console.log('Sorting by column:', column);
                sortByColumn(column);
            });
        });
    } else {
        console.warn('No sortable columns found in the table');

        // Coba cari dengan selector yang berbeda
        const allHeaders = document.querySelectorAll('#employeeTable th[data-column]');
        console.log('Found headers with data-column attribute in table:', allHeaders.length);

        if (allHeaders && allHeaders.length > 0) {
            allHeaders.forEach(header => {
                // Hapus event listener lama untuk mencegah duplikasi
                const newHeader = header.cloneNode(true);
                header.parentNode.replaceChild(newHeader, header);

                newHeader.classList.add('sortable-column');
                newHeader.addEventListener('click', function(e) {
                    // Jika klik pada tombol filter, jangan lakukan pengurutan
                    if (e.target.closest('.column-filter')) {
                        return;
                    }

                    const column = this.getAttribute('data-column');
                    console.log('Sorting by column (fallback):', column);
                    sortByColumn(column);
                });
            });
            console.log('Added sortable-column class and click handlers to headers');
        }
    }
}



// Fungsi untuk menangani pemilihan/deseleksi NIK
function toggleNIKSelection(checkbox) {
    const nik = checkbox.value;

    if (checkbox.checked) {
        // Tambahkan NIK ke array jika belum ada
        if (!selectedNIKs.includes(nik)) {
            selectedNIKs.push(nik);
            // Tambahkan NIK ke array urutan pemilihan
            selectedNIKsOrder.push(nik);
        }

        // Tambahkan class ke baris untuk highlight (desktop view)
        const tableRow = checkbox.closest('tr');
        if (tableRow) {
            tableRow.classList.add('selected-row');
        }

        // Tambahkan class ke card untuk highlight (mobile view)
        const card = document.querySelector(`.employee-card[data-nik="${nik}"]`);
        if (card) {
            card.classList.add('selected-card');
        }
    } else {
        // Hapus NIK dari array
        selectedNIKs = selectedNIKs.filter(item => item !== nik);
        // Hapus NIK dari array urutan pemilihan
        selectedNIKsOrder = selectedNIKsOrder.filter(item => item !== nik);

        // Hapus class dari baris (desktop view)
        const tableRow = checkbox.closest('tr');
        if (tableRow) {
            tableRow.classList.remove('selected-row');
        }

        // Hapus class dari card (mobile view)
        const card = document.querySelector(`.employee-card[data-nik="${nik}"]`);
        if (card) {
            card.classList.remove('selected-card');
        }
    }

    // Log untuk debugging
    console.log('Selected NIKs:', selectedNIKs);
    console.log('Selected NIKs Order:', selectedNIKsOrder);

    // Update jumlah NIK yang dipilih pada tombol bulk copy
    updateBulkCopyButton();

    // Sync checkbox state between mobile and desktop views
    syncCheckboxState(nik, checkbox.checked);
}

// Fungsi untuk menyinkronkan status checkbox antara tampilan mobile dan desktop
function syncCheckboxState(nik, isChecked) {
    // Sync desktop view checkbox
    const tableCheckbox = document.querySelector(`#employeesTableBody input[value="${nik}"]`);
    if (tableCheckbox && tableCheckbox.checked !== isChecked) {
        tableCheckbox.checked = isChecked;
    }

    // Sync mobile view checkbox
    const cardCheckbox = document.querySelector(`.employee-card[data-nik="${nik}"] input[type="checkbox"]`);
    if (cardCheckbox && cardCheckbox.checked !== isChecked) {
        cardCheckbox.checked = isChecked;
    }
}

// Fungsi untuk update tombol bulk copy dan reset
function updateBulkCopyButton() {
    const bulkCopyBtn = document.getElementById('bulkCopyBtn');
    const resetSelectedNIKsBtn = document.getElementById('resetSelectedNIKsBtn');

    if (selectedNIKs.length > 0) {
        bulkCopyBtn.innerHTML = `<i class="fas fa-copy"></i> Copy ${selectedNIKs.length} NIK Terpilih`;
        bulkCopyBtn.disabled = false;
        resetSelectedNIKsBtn.disabled = false;
    } else {
        bulkCopyBtn.innerHTML = `<i class="fas fa-copy"></i> Copy Kolom Terpilih`;
        bulkCopyBtn.disabled = true;
        resetSelectedNIKsBtn.disabled = true;
    }
}

// Fungsi untuk mereset semua NIK yang dipilih
function resetSelectedNIKs() {
    // Konfirmasi reset
    if (selectedNIKs.length === 0) {
        return; // Tidak ada yang perlu direset
    }

    // Gunakan dialog konfirmasi kustom
    showConfirmDialog(`Apakah Anda yakin ingin menghapus semua ${selectedNIKs.length} NIK yang dipilih?`, () => {
        // Hapus semua centang pada checkbox
        document.querySelectorAll('input[name="select-nik"]:checked').forEach(checkbox => {
            checkbox.checked = false;

            // Hapus class selected-row dari baris tabel
            const tableRow = checkbox.closest('tr');
            if (tableRow) {
                tableRow.classList.remove('selected-row');
            }
        });

        // Hapus class selected-card dari kartu mobile
        document.querySelectorAll('.employee-card.selected-card').forEach(card => {
            card.classList.remove('selected-card');

            // Hapus centang pada checkbox di kartu
            const cardCheckbox = card.querySelector('input[type="checkbox"]');
            if (cardCheckbox) {
                cardCheckbox.checked = false;
            }
        });

        // Reset array NIK yang dipilih
        selectedNIKs = [];
        selectedNIKsOrder = [];

        // Update tombol bulk copy
        updateBulkCopyButton();

        // Tampilkan feedback visual dengan toast
        showToast('NIK berhasil direset', 'success');

        // Tampilkan feedback visual pada tombol
        const resetBtn = document.getElementById('resetSelectedNIKsBtn');
        const originalHTML = resetBtn.innerHTML;
        resetBtn.innerHTML = `<i class="fas fa-check"></i> NIK Direset!`;
        resetBtn.classList.add('btn-success');
        resetBtn.classList.remove('btn-outline-danger');

        // Kembalikan tampilan tombol setelah 2 detik
        setTimeout(() => {
            resetBtn.innerHTML = originalHTML;
            resetBtn.classList.remove('btn-success');
            resetBtn.classList.add('btn-outline-danger');
        }, 2000);
    });
}

// Function to bulk copy selected NIKs - global scope untuk akses dari HTML onclick
function bulkCopyNIK() {
    try {
        if (selectedNIKs.length === 0) {
            showToast('Silakan pilih minimal satu NIK terlebih dahulu', 'warning');
            return;
        }

        // Get selected columns to copy in the order they appear in the DOM
        const selectedColumnItems = Array.from(document.querySelectorAll('.copy-column-item'))
            .filter(item => item.querySelector('input[name="copy_columns"]:checked'));

        const selectedColumns = selectedColumnItems.map(item =>
            item.querySelector('input[name="copy_columns"]').value
        );

        // If no columns selected, just copy NIKs in the order they were selected
        if (selectedColumns.length === 0) {
            // Gunakan selectedNIKsOrder untuk mempertahankan urutan pemilihan
            const textToCopy = selectedNIKsOrder.join('\n');
            copyToClipboard(textToCopy, `${selectedNIKs.length} NIK berhasil disalin`);
            return;
        }

        // Get all rows from the table
        const rows = Array.from(document.querySelectorAll('#employeesTableBody tr'));

        // Debug info - tampilkan semua NIK yang ada di tabel saat ini
        console.log('Semua NIK dalam tabel saat ini:');
        const allNIKsInTable = [];
        rows.forEach(row => {
            const nikCell = row.querySelector('td[data-column="nik"]');
            if (nikCell) {
                const nikText = nikCell.querySelector('.nik-text');
                if (nikText) {
                    const nik = nikText.textContent.trim();
                    allNIKsInTable.push(nik);
                }
            }
        });
        console.log(allNIKsInTable);

        // Periksa apakah semua NIK yang dipilih ada dalam tabel saat ini
        const niksNotInCurrentPage = selectedNIKs.filter(nik => !allNIKsInTable.includes(nik));
        if (niksNotInCurrentPage.length > 0) {
            console.warn('NIK yang dipilih tetapi tidak ada di halaman tabel saat ini:', niksNotInCurrentPage);
        }

        // Filter rows that match selected NIKs
        const selectedRows = [];

        // Untuk memastikan urutan NIK sesuai dengan yang dipilih, kita loop melalui selectedNIKsOrder
        selectedNIKsOrder.forEach(selectedNik => {
            // Log untuk debugging
            console.log(`Mencari baris untuk NIK: ${selectedNik}`);

            // Cari baris yang sesuai dengan NIK ini
            let matchingRow = null;

            // Loop melalui semua baris untuk mencari yang cocok
            for (const row of rows) {
                const nikCell = row.querySelector('td[data-column="nik"]');
                if (!nikCell) continue;

                const nikText = nikCell.querySelector('.nik-text');
                if (!nikText) continue;

                const rowNik = nikText.textContent.trim();
                console.log(`Membandingkan dengan NIK baris: ${rowNik}`);

                if (rowNik === selectedNik) {
                    matchingRow = row;
                    console.log(`Baris ditemukan untuk NIK: ${selectedNik}`);
                    break;
                }
            }

            // Jika baris ditemukan, tambahkan ke selectedRows
            if (matchingRow) {
                selectedRows.push(matchingRow);
            } else {
                console.warn(`Tidak menemukan baris untuk NIK: ${selectedNik}`);
            }
        });

        // Log jumlah baris yang ditemukan
        console.log(`Total baris yang ditemukan: ${selectedRows.length} dari ${selectedNIKs.length} NIK yang dipilih`);

        // Prepare data for copying
        let textToCopy = '';

        // Jika ada NIK yang tidak ditemukan di halaman saat ini
        if (niksNotInCurrentPage.length > 0) {
            console.log(`${niksNotInCurrentPage.length} NIK tidak ditemukan di halaman saat ini:`, niksNotInCurrentPage);

            // Tanyakan kepada pengguna apakah ingin mengambil data dari server
            showConfirmDialog(`${niksNotInCurrentPage.length} NIK yang dipilih tidak ada di halaman tabel saat ini. Apakah Anda ingin mengambil data lengkap dari server? Jika tidak, hanya NIK yang akan disalin.`, async () => {
                try {
                    // Tampilkan loading indicator
                    loadingIndicator.style.display = 'flex';

                    // Ambil data karyawan dari server
                    const additionalData = await fetchEmployeeDataByNIKs(niksNotInCurrentPage, selectedColumns);

                    // Gabungkan data dari server dengan data dari halaman saat ini
                    const combinedTextToCopy = textToCopy + additionalData;
                    console.log('Combined text to copy:', combinedTextToCopy);

                    // Buat map untuk menyimpan data baris berdasarkan NIK
                    const rowDataMap = new Map();

                    // Parse additionalData to get NIK and full row data
                    const additionalDataLines = additionalData.trim().split('\n');
                    additionalDataLines.forEach(line => {
                        if (!line.trim()) return; // Skip empty lines

                        const parts = line.split('\t');
                        if (parts.length > 0) {
                            const nik = parts[0].trim();
                            if (nik) {
                                // Store row data in map with NIK as key
                                rowDataMap.set(nik, line);
                                console.log(`Added data for NIK ${nik} to rowDataMap`);
                            }
                        }
                    });

                    // Log all NIKs in rowDataMap for debugging
                    console.log('NIKs in rowDataMap:', Array.from(rowDataMap.keys()));

                    // Build combined text in the order of selectedNIKsOrder
                    let finalTextToCopy = '';
                    let missingNIKs = [];

                    selectedNIKsOrder.forEach(nik => {
                        console.log(`Checking if rowDataMap has NIK: ${nik}`);
                        if (rowDataMap.has(nik)) {
                            finalTextToCopy += rowDataMap.get(nik) + '\n';
                            console.log(`Added data for NIK ${nik} to finalTextToCopy`);
                        } else {
                            console.log(`NIK ${nik} not found in rowDataMap`);
                            missingNIKs.push(nik);
                        }
                    });

                    // Log final text for debugging
                    console.log('Final text to copy:', finalTextToCopy);
                    console.log('Missing NIKs:', missingNIKs);

                    // Jika ada NIK yang hilang, coba ambil dari data yang ada di halaman saat ini
                    if (missingNIKs.length > 0) {
                        console.log(`Trying to find ${missingNIKs.length} missing NIKs in current page data`);

                        // Cari di baris yang ada di halaman saat ini
                        const currentPageRows = Array.from(document.querySelectorAll('#employeesTableBody tr'));

                        missingNIKs.forEach(missingNik => {
                            // Cari baris dengan NIK yang hilang
                            let found = false;

                            for (const row of currentPageRows) {
                                const nikCell = row.querySelector('td[data-column="nik"]');
                                if (nikCell) {
                                    const nikText = nikCell.querySelector('.nik-text');
                                    if (nikText && nikText.textContent.trim() === missingNik) {
                                        // Baris ditemukan, ambil datanya
                                        const rowData = [];

                                        // Tambahkan NIK
                                        rowData.push(missingNik);

                                        // Tambahkan kolom yang dipilih
                                        selectedColumns.forEach(column => {
                                            const cell = row.querySelector(`td[data-column="${column}"]`);
                                            if (cell) {
                                                rowData.push(cell.textContent.trim());
                                            } else {
                                                rowData.push(''); // Tambahkan string kosong jika kolom tidak ditemukan
                                            }
                                        });

                                        // Tambahkan data baris ke finalTextToCopy
                                        const rowText = rowData.join('\t') + '\n';
                                        finalTextToCopy += rowText;
                                        console.log(`Added missing NIK ${missingNik} from current page data`);

                                        found = true;
                                        break;
                                    }
                                }
                            }

                            if (!found) {
                                console.warn(`Could not find data for NIK ${missingNik} in current page or server data`);
                            }
                        });
                    }

                    // Sembunyikan loading indicator
                    loadingIndicator.style.display = 'none';

                    // Pastikan semua NIK yang dipilih ada dalam finalTextToCopy
                    if (finalTextToCopy.trim() === '') {
                        console.warn('finalTextToCopy is empty, using combinedTextToCopy instead');
                        copyToClipboard(combinedTextToCopy, `${selectedNIKs.length} baris data berhasil disalin`);
                    } else {
                        copyToClipboard(finalTextToCopy, `${selectedNIKs.length} baris data berhasil disalin`);
                    }
                } catch (error) {
                    console.error('Error fetching employee data:', error);
                    showToast('Terjadi kesalahan saat mengambil data karyawan dari server. Hanya data yang tersedia di halaman saat ini yang akan disalin.', 'error');

                    // Sembunyikan loading indicator
                    loadingIndicator.style.display = 'none';

                    // Fallback ke data yang ada di halaman saat ini
                    copyDataFromCurrentPage();
                }
            }, () => {
                // Jika user tidak ingin mengambil data dari server, hanya salin NIK dengan urutan yang benar
                textToCopy = selectedNIKsOrder.join('\n');
                copyToClipboard(textToCopy, `${selectedNIKs.length} NIK berhasil disalin`);
            });

            // Kode untuk mengambil data dari server sudah ditangani oleh showConfirmDialog
            return;
        }

        // Jika semua NIK ditemukan di halaman saat ini atau tidak ada NIK yang dipilih
        copyDataFromCurrentPage();

        // Fungsi untuk menyalin data dari halaman saat ini
        function copyDataFromCurrentPage() {
            // Jika tidak ada baris yang ditemukan sama sekali, gunakan selectedNIKs saja
            if (selectedRows.length === 0 && selectedNIKs.length > 0) {
                console.log('Tidak ada baris yang ditemukan, menggunakan selectedNIKs saja');

                // Hanya salin NIK tanpa kolom tambahan, dengan urutan yang benar
                textToCopy = selectedNIKsOrder.join('\n');
                copyToClipboard(textToCopy, `${selectedNIKs.length} NIK berhasil disalin`);
                return;
            } else {
                // Buat map untuk menyimpan data baris berdasarkan NIK
                const rowDataMap = new Map();
                textToCopy = ''; // Reset textToCopy

                // Simpan data dari baris yang ditemukan di halaman saat ini ke dalam map
                selectedRows.forEach(row => {
                    const rowData = [];
                    let nik = '';

                    // Always add NIK first
                    const nikCell = row.querySelector('td[data-column="nik"]');
                    if (nikCell) {
                        const nikText = nikCell.querySelector('.nik-text');
                        if (nikText) {
                            nik = nikText.textContent.trim();
                            rowData.push(nik);
                        }
                    }

                    if (!nik) return; // Skip if no NIK found

                    // Add selected columns in the order they were selected
                    selectedColumns.forEach(column => {
                        const cell = row.querySelector(`td[data-column="${column}"]`);
                        if (cell) {
                            rowData.push(cell.textContent.trim());
                        } else {
                            rowData.push(''); // Add empty string if column not found
                        }
                    });

                    // Store row data in map with NIK as key
                    rowDataMap.set(nik, rowData.join('\t'));
                });

                // Build combined text in the order of selectedNIKsOrder
                selectedNIKsOrder.forEach(nik => {
                    if (rowDataMap.has(nik)) {
                        textToCopy += rowDataMap.get(nik) + '\n';
                    }
                });

                // Copy to clipboard
                copyToClipboard(textToCopy, `${selectedNIKs.length} baris data berhasil disalin`);
            }
        }

        // Debug info
        console.log('Selected NIKs:', selectedNIKs);
        console.log('Selected Rows:', selectedRows.length);
        console.log('Selected Columns:', selectedColumns);
        console.log('Text to Copy:', textToCopy);

    } catch (error) {
        console.error('Error copying data:', error);
        showToast('Terjadi kesalahan saat menyalin data', 'error');
    }
}

// Fungsi untuk mengambil data karyawan berdasarkan NIK dari server
async function fetchEmployeeDataByNIKs(niks, columns) {
    try {
        // Tampilkan loading indicator
        loadingIndicator.style.display = 'flex';

        // Buat URL dengan parameter NIK
        const url = `get_employees_by_nik.php?niks=${niks.join(',')}`;
        console.log('Fetching data from URL:', url);

        // Fetch data dari server
        const response = await fetch(url);

        // Cek response type
        const contentType = response.headers.get('content-type');
        console.log('Response content type:', contentType);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Server response error:', errorText);
            throw new Error(`HTTP error! status: ${response.status}, response: ${errorText.substring(0, 100)}...`);
        }

        // Cek apakah response adalah JSON
        if (!contentType || !contentType.includes('application/json')) {
            const text = await response.text();
            console.error('Response is not JSON:', text);
            throw new Error(`Expected JSON response but got ${contentType || 'unknown'} instead`);
        }

        // Parse JSON
        let data;
        try {
            data = await response.json();
        } catch (jsonError) {
            console.error('Error parsing JSON:', jsonError);
            throw new Error(`Failed to parse JSON response: ${jsonError.message}`);
        }

        console.log('Response data:', data);

        if (!data.success) {
            throw new Error(data.message || 'Terjadi kesalahan saat mengambil data karyawan');
        }

        // Format data untuk copy
        let formattedData = '';

        if (!data.employees || !Array.isArray(data.employees) || data.employees.length === 0) {
            console.warn('No employee data found in response');
            return formattedData; // Return empty string
        }

        // Log data yang diterima dari server
        console.log('Received employees data:', data.employees);
        console.log('Requested NIKs:', data.requested_niks);

        // Buat map untuk menyimpan data karyawan berdasarkan NIK
        const employeeMap = new Map();
        data.employees.forEach(employee => {
            employeeMap.set(employee.nik, employee);
        });

        // Gunakan urutan NIK yang diminta untuk memastikan urutan yang benar
        if (data.requested_niks && Array.isArray(data.requested_niks)) {
            data.requested_niks.forEach(nik => {
                if (employeeMap.has(nik)) {
                    const employee = employeeMap.get(nik);
                    const rowData = [];

                    // Always add NIK first
                    rowData.push(employee.nik);

                    // Add selected columns in the order they were selected
                    columns.forEach(column => {
                        if (employee[column] !== undefined) {
                            rowData.push(employee[column]);
                        } else {
                            rowData.push(''); // Add empty string if column not found
                        }
                    });

                    // Add row data to text
                    formattedData += rowData.join('\t') + '\n';
                }
            });
        } else {
            // Fallback jika requested_niks tidak tersedia
            data.employees.forEach(employee => {
                const rowData = [];

                // Always add NIK first
                rowData.push(employee.nik);

                // Add selected columns in the order they were selected
                columns.forEach(column => {
                    if (employee[column] !== undefined) {
                        rowData.push(employee[column]);
                    } else {
                        rowData.push(''); // Add empty string if column not found
                    }
                });

                // Add row data to text
                formattedData += rowData.join('\t') + '\n';
            });
        }

        console.log('Formatted data for copy:', formattedData);

        // Sembunyikan loading indicator
        loadingIndicator.style.display = 'none';

        return formattedData;
    } catch (error) {
        console.error('Error fetching employee data:', error);
        // Sembunyikan loading indicator
        loadingIndicator.style.display = 'none';
        throw error;
    }
}

// Helper function to copy text to clipboard
function copyToClipboard(text, successMessage) {
    // Pastikan text tidak kosong
    if (!text || text.trim() === '') {
        showToast('Tidak ada data yang dapat disalin', 'warning');
        return;
    }

    try {
        // Metode modern dengan Clipboard API jika tersedia
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                showCopySuccess(successMessage);
            }).catch(err => {
                console.error('Clipboard API error:', err);
                fallbackCopyMethod(text, successMessage);
            });
        } else {
            // Fallback untuk browser yang tidak mendukung Clipboard API
            fallbackCopyMethod(text, successMessage);
        }
    } catch (error) {
        console.error('Copy error:', error);
        showToast('Terjadi kesalahan saat menyalin data', 'error');
    }
}

// Fallback method untuk copy ke clipboard
function fallbackCopyMethod(text, successMessage) {
    // Create temporary textarea to copy text
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed'; // Avoid scrolling to bottom
    textarea.style.opacity = '0';
    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopySuccess(successMessage);
        } else {
            throw new Error('Copy command was unsuccessful');
        }
    } catch (err) {
        console.error('Fallback copy error:', err);
        showToast('Terjadi kesalahan saat menyalin data', 'error');
    } finally {
        document.body.removeChild(textarea);
    }
}

// Tampilkan feedback sukses copy
function showCopySuccess(successMessage) {
    // Visual feedback
    const bulkCopyBtn = document.getElementById('bulkCopyBtn');
    const originalHTML = bulkCopyBtn.innerHTML;

    bulkCopyBtn.innerHTML = `<i class="fas fa-check"></i> ${successMessage}!`;
    bulkCopyBtn.classList.add('copied');

    // Reset button after 2 seconds
    setTimeout(() => {
        updateBulkCopyButton();
        bulkCopyBtn.classList.remove('copied');
    }, 2000);

    console.log(successMessage);
}

// Fungsi untuk toggle kolom yang ditampilkan
function toggleColumns() {
    // Buat dialog untuk toggle kolom
    const dialog = document.createElement('div');
    dialog.className = 'column-toggle-dialog';
    dialog.id = 'columnToggleDialog';

    // Ambil semua kolom dari tabel
    const columns = document.querySelectorAll('#employeeTable th[data-column]');
    const columnNames = {
        'nik': 'NIK',
        'nama': 'Nama',
        'tgl_masuk': 'Tanggal Masuk',
        'jk': 'Jenis Kelamin',
        'level_karyawan': 'Level',
        'tgl_lahir': 'Tanggal Lahir',
        'agama': 'Agama',
        'pendidikan_akhir': 'Pendidikan',
        'no_telp': 'No. Telepon',
        'dept': 'Departemen',
        'bagian': 'Bagian',
        'jabatan': 'Jabatan',
        'group': 'Group',
        'status': 'Status',
        'pt': 'PT'
    };

    // Buat konten dialog
    dialog.innerHTML = `
        <h3><i class="fas fa-columns"></i> Tampilkan/Sembunyikan Kolom</h3>
        <div class="column-toggle-select-actions">
            <button class="btn btn-outline-primary" onclick="selectAllColumns()">Pilih Semua</button>
            <button class="btn btn-outline-secondary" onclick="clearAllColumns()">Clear Semua</button>
        </div>
        <div class="column-toggle-options">
            ${Array.from(columns).map(col => {
                const columnName = col.getAttribute('data-column');
                const isOptional = col.classList.contains('optional-column');
                const isVisible = !isOptional;
                return `
                    <div class="column-toggle-option">
                        <input type="checkbox" id="toggle-${columnName}"
                               data-column="${columnName}" ${isVisible ? 'checked' : ''}>
                        <label for="toggle-${columnName}">${columnNames[columnName] || columnName}</label>
                    </div>
                `;
            }).join('')}
        </div>
        <div class="column-toggle-actions">
            <button class="btn btn-secondary" onclick="closeColumnToggleDialog()">Batal</button>
            <button class="btn btn-primary" onclick="applyColumnToggle()">Terapkan</button>
        </div>
    `;

    // Tambahkan dialog ke body
    document.body.appendChild(dialog);
}

// Fungsi untuk menutup dialog toggle kolom
function closeColumnToggleDialog() {
    const dialog = document.getElementById('columnToggleDialog');
    if (dialog) {
        document.body.removeChild(dialog);
    }
}

// Fungsi untuk memilih semua kolom
function selectAllColumns() {
    const checkboxes = document.querySelectorAll('#columnToggleDialog input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

// Fungsi untuk membersihkan semua pilihan kolom
function clearAllColumns() {
    const checkboxes = document.querySelectorAll('#columnToggleDialog input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

// Fungsi untuk menerapkan toggle kolom
function applyColumnToggle() {
    // Ambil semua checkbox
    const checkboxes = document.querySelectorAll('#columnToggleDialog input[type="checkbox"]');

    // Reset pengaturan kolom global
    columnSettings = {};

    // Terapkan pengaturan untuk setiap kolom
    checkboxes.forEach(checkbox => {
        const columnName = checkbox.getAttribute('data-column');
        const isVisible = checkbox.checked;
        columnSettings[columnName] = isVisible;

        // Tampilkan atau sembunyikan kolom
        const cells = document.querySelectorAll(`#employeeTable th[data-column="${columnName}"], #employeeTable td[data-column="${columnName}"]`);
        cells.forEach(cell => {
            if (isVisible) {
                cell.classList.remove('optional-column');
            } else {
                cell.classList.add('optional-column');
            }
        });
    });

    // Simpan pengaturan di localStorage
    localStorage.setItem('employeeColumnSettings', JSON.stringify(columnSettings));

    // Tutup dialog
    closeColumnToggleDialog();
}

// Fungsi untuk export data ke Excel
function exportToExcel() {
    try {
        // Ambil tabel
        const table = document.getElementById('employeeTable');
        if (!table) {
            throw new Error('Tabel tidak ditemukan');
        }

        // Buat workbook baru
        const wb = XLSX.utils.book_new();

        // Ambil data dari tabel (termasuk kolom yang tersembunyi)
        const rows = [];

        // Ambil header
        const headerRow = [];
        const headers = table.querySelectorAll('th[data-column]');
        headers.forEach(header => {
            headerRow.push(header.textContent.trim());
        });
        rows.push(headerRow);

        // Ambil data baris
        const dataRows = table.querySelectorAll('tbody tr');
        dataRows.forEach(row => {
            const rowData = [];
            const cells = row.querySelectorAll('td[data-column]');
            cells.forEach(cell => {
                // Ambil teks dari cell (tanpa HTML)
                rowData.push(cell.textContent.trim());
            });
            rows.push(rowData);
        });

        // Buat worksheet
        const ws = XLSX.utils.aoa_to_sheet(rows);

        // Tambahkan worksheet ke workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Karyawan');

        // Export workbook ke file Excel
        XLSX.writeFile(wb, 'Data_Karyawan.xlsx');
    } catch (error) {
        console.error('Error exporting to Excel:', error);
        showToast('Terjadi kesalahan saat export ke Excel: ' + error.message, 'error');
    }
}

// Fungsi untuk menampilkan detail karyawan
async function showEmployeeDetail(nik) {
    try {
        // Tampilkan loading
        const loadingModal = document.createElement('div');
        loadingModal.className = 'employee-detail-modal';
        loadingModal.innerHTML = `
            <div class="employee-detail-content" style="padding: 30px; text-align: center;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #BF0000;"></i>
                <p style="margin-top: 15px;">Memuat data karyawan...</p>
            </div>
        `;
        document.body.appendChild(loadingModal);

        // Ambil data karyawan dari server
        const response = await fetch(`get_employee_detail.php?nik=${encodeURIComponent(nik)}`);

        // Cek content type
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Response bukan dalam format JSON');
        }

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || 'Terjadi kesalahan saat mengambil data karyawan');
        }

        // Hapus loading modal
        document.body.removeChild(loadingModal);

        // Buat modal detail karyawan
        const employee = data.employee;
        const modal = document.createElement('div');
        modal.className = 'employee-detail-modal';
        modal.id = 'employeeDetailModal';

        // Format data karyawan
        const formatData = (value) => value ? escapeHtml(value) : '-';

        modal.innerHTML = `
            <div class="employee-detail-content">
                <div class="employee-detail-header">
                    <h3><i class="fas fa-user"></i> Detail Karyawan</h3>
                    <button class="employee-detail-close" onclick="closeEmployeeDetail()">&times;</button>
                </div>
                <div class="employee-detail-body">
                    <div class="employee-info-grid">
                        <div class="employee-info-item">
                            <span class="employee-info-label">NIK</span>
                            <div class="employee-info-value">${formatData(employee.nik)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">Nama</span>
                            <div class="employee-info-value">${formatData(employee.nama)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">Tanggal Masuk</span>
                            <div class="employee-info-value">${formatData(employee.tgl_masuk)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">Jenis Kelamin</span>
                            <div class="employee-info-value">${formatData(employee.jk) === 'L' ? 'Laki-laki' : (formatData(employee.jk) === 'P' ? 'Perempuan' : '-')}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">Level Karyawan</span>
                            <div class="employee-info-value">${formatData(employee.level_karyawan)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">Tanggal Lahir</span>
                            <div class="employee-info-value">${formatData(employee.tgl_lahir)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">Agama</span>
                            <div class="employee-info-value">${formatData(employee.agama)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">Pendidikan Terakhir</span>
                            <div class="employee-info-value">${formatData(employee.pendidikan_akhir)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">No. Telepon</span>
                            <div class="employee-info-value">${formatData(employee.no_telp)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">Departemen</span>
                            <div class="employee-info-value">${formatData(employee.dept)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">Bagian</span>
                            <div class="employee-info-value">${formatData(employee.bagian)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">Jabatan</span>
                            <div class="employee-info-value">${formatData(employee.jabatan)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">Group</span>
                            <div class="employee-info-value">${formatData(employee.group)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">Status</span>
                            <div class="employee-info-value">${formatData(employee.status)}</div>
                        </div>
                        <div class="employee-info-item">
                            <span class="employee-info-label">PT</span>
                            <div class="employee-info-value">${formatData(employee.pt)}</div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Tambahkan modal ke body
        document.body.appendChild(modal);

    } catch (error) {
        console.error('Error showing employee detail:', error);
        showToast('Terjadi kesalahan saat menampilkan detail karyawan: ' + error.message, 'error');

        // Hapus loading modal jika ada
        const loadingModal = document.querySelector('.employee-detail-modal');
        if (loadingModal) {
            document.body.removeChild(loadingModal);
        }
    }
}

// Fungsi untuk menutup detail karyawan
function closeEmployeeDetail() {
    const modal = document.getElementById('employeeDetailModal');
    if (modal) {
        document.body.removeChild(modal);
    }
}

// Fungsi untuk menghapus karyawan
async function deleteEmployee(nik) {
    // Tampilkan dialog konfirmasi
    showConfirmDialog(`Apakah Anda yakin ingin menghapus karyawan dengan NIK ${nik}?`, async () => {
        try {
            // Tampilkan loading indicator
            loadingIndicator.style.display = 'block';

            // Kirim request ke server untuk menghapus karyawan
            console.log(`Mengirim request ke: delete_employee.php?nik=${encodeURIComponent(nik)}`);
            const response = await fetch(`delete_employee.php?nik=${encodeURIComponent(nik)}`);
            console.log('Response status:', response.status);

            // Cek content type
            const contentType = response.headers.get('content-type');
            console.log('Response content type:', contentType);

            if (!contentType || !contentType.includes('application/json')) {
                // Jika bukan JSON, coba ambil text response untuk debugging
                const text = await response.text();
                console.error('Response is not JSON:', text);
                throw new Error(`Response bukan dalam format JSON. Content-Type: ${contentType || 'tidak ada'}`);
            }

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();
            console.log('Response data:', data);

            if (data.success) {
                // Tampilkan pesan sukses
                showToast('Karyawan berhasil dihapus', 'success');

                // Refresh tabel
                searchEmployees(currentSearchQuery);
            } else {
                // Tampilkan pesan error
                throw new Error(data.message || 'Gagal menghapus karyawan');
            }
        } catch (error) {
            console.error('Error deleting employee:', error);
            showToast(`Terjadi kesalahan saat menghapus karyawan: ${error.message}`, 'error');
        } finally {
            // Sembunyikan loading indicator
            loadingIndicator.style.display = 'none';
        }
    });
}

// Fungsi untuk toggle dropdown filter
function toggleFilter(column) {
    // Debug: Log column yang di-toggle
    console.log('Toggling filter for column:', column);

    // Tutup semua dropdown yang terbuka
    const openDropdowns = document.querySelectorAll('.filter-dropdown.show');

    openDropdowns.forEach(dropdown => {
        if (dropdown.id !== `filter-${column}`) {
            dropdown.classList.remove('show');
        }
    });

    // Toggle dropdown yang dipilih
    const dropdown = document.getElementById(`filter-${column}`);

    // Debug: Log dropdown element
    console.log('Dropdown element:', dropdown);

    if (!dropdown) {
        console.warn(`Dropdown filter-${column} tidak ditemukan`);

        // Debug: Coba cari dropdown dengan selector yang berbeda
        const allDropdowns = document.querySelectorAll('.filter-dropdown');
        console.log('All filter dropdowns:', allDropdowns.length);
        allDropdowns.forEach(d => console.log(' - ' + d.id));
        return;
    }

    // Toggle class show
    dropdown.classList.toggle('show');

    // Debug: Log status dropdown setelah toggle
    const isShowing = dropdown.classList.contains('show');
    console.log('Dropdown is now shown:', isShowing);

    // Jika dropdown ditampilkan, muat nilai unik jika belum dimuat
    if (isShowing) {
        // Reset posisi dropdown
        dropdown.style.top = '';
        dropdown.style.left = '';
        dropdown.style.right = '';

        // Muat data unik untuk kolom tersebut jika belum dimuat
        if (!uniqueColumnValues[column] || uniqueColumnValues[column].length === 0) {
            loadUniqueValues(column);
        }

        // Pastikan dropdown tidak terpotong oleh container
        setTimeout(() => {
            const dropdownRect = dropdown.getBoundingClientRect();
            const tableContainer = document.querySelector('.table-container');
            const containerRect = tableContainer.getBoundingClientRect();

            // Jika dropdown keluar dari container di sebelah kanan
            if (dropdownRect.right > containerRect.right) {
                dropdown.style.right = '0';
                dropdown.style.left = 'auto';
            }

            // Jika dropdown keluar dari container di sebelah kiri
            if (dropdownRect.left < containerRect.left) {
                dropdown.style.left = '0';
                dropdown.style.right = 'auto';
            }

            // Jika dropdown keluar dari viewport di bagian bawah
            if (dropdownRect.bottom > window.innerHeight) {
                dropdown.style.maxHeight = `${window.innerHeight - dropdownRect.top - 20}px`;
            }
        }, 0);

        // Tambahkan event listener untuk menutup dropdown saat klik di luar
        document.addEventListener('click', function closeDropdown(e) {
            if (!e.target.closest('.column-filter')) {
                dropdown.classList.remove('show');
                document.removeEventListener('click', closeDropdown);
            }
        });
    }
}

// Fungsi untuk memuat nilai unik untuk kolom tertentu
function loadUniqueValues(column) {
    // Debug log
    if (window.debugLog) {
        window.debugLog(`Memuat nilai unik untuk kolom: ${column}`, {
            activeFilters: columnFilters
        });
    }

    // Buat URL dengan filter yang sudah ada
    let url = `get_unique_values.php?column=${column}`;

    // Tambahkan filter yang sudah ada ke URL
    const activeFilters = {};

    // Tambahkan filter PT jika ada
    if (columnFilters['pt'] && column !== 'pt') {
        activeFilters['pt'] = columnFilters['pt'];
    }

    // Tambahkan filter dept jika ada
    if (columnFilters['dept'] && column !== 'dept') {
        activeFilters['dept'] = columnFilters['dept'];
    }

    // Tambahkan filter bagian jika ada dan kolom bukan dept atau bagian
    if (columnFilters['bagian'] && column !== 'dept' && column !== 'bagian') {
        activeFilters['bagian'] = columnFilters['bagian'];
    }

    // Tambahkan filter jabatan jika ada dan kolom bukan dept, bagian, atau jabatan
    if (columnFilters['jabatan'] && column !== 'dept' && column !== 'bagian' && column !== 'jabatan') {
        activeFilters['jabatan'] = columnFilters['jabatan'];
    }

    // Tambahkan filter group jika ada dan kolom adalah nama atau nik
    if (columnFilters['group'] && (column === 'nama' || column === 'nik')) {
        activeFilters['group'] = columnFilters['group'];
    }

    // Jika ada filter aktif, tambahkan ke URL
    if (Object.keys(activeFilters).length > 0) {
        url += `&filters=${encodeURIComponent(JSON.stringify(activeFilters))}`;
    }

    // Debug log
    if (window.debugLog) {
        window.debugLog(`URL untuk memuat nilai unik: ${url}`, {
            activeFilters: activeFilters
        });
    }

    // Tampilkan loading di dropdown
    const optionsContainer = document.getElementById(`filter-options-${column}`);

    // Periksa apakah container ada
    if (!optionsContainer) {
        console.warn(`Container filter-options-${column} tidak ditemukan`);
        return;
    }

    optionsContainer.innerHTML = '<p class="loading-text"><i class="fas fa-spinner fa-spin"></i> Loading...</p>';

    // Ambil data dari server
    fetch(url)
        .then(response => {
            // Periksa status response
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Periksa content type
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error(`Expected JSON response but got ${contentType || 'unknown'}`);
            }

            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Debug log
                if (window.debugLog) {
                    window.debugLog(`Nilai unik untuk kolom ${column} berhasil dimuat`, {
                        count: data.values.length,
                        values: data.values.slice(0, 10) // Hanya tampilkan 10 nilai pertama untuk debugging
                    });
                }

                uniqueColumnValues[column] = data.values;
                displayUniqueValues(column, data.values);
            } else {
                console.error(`Error loading unique values for ${column}:`, data.message);
                if (optionsContainer) {
                    optionsContainer.innerHTML = `
                        <p class="error-text">
                            <i class="fas fa-exclamation-circle"></i>
                            Error: ${data.message || 'Failed to load data'}
                        </p>
                        <button class="btn btn-outline-secondary" onclick="loadUniqueValues('${column}')">
                            <i class="fas fa-sync-alt"></i> Coba Lagi
                        </button>
                    `;
                }
            }
        })
        .catch(error => {
            console.error('Error loading unique values:', error);

            // Debug log
            if (window.debugLog) {
                window.debugLog(`Error memuat nilai unik untuk kolom ${column}`, {
                    error: error.message,
                    stack: error.stack
                });
            }

            if (optionsContainer) {
                optionsContainer.innerHTML = `
                    <p class="error-text">
                        <i class="fas fa-exclamation-circle"></i>
                        Error: ${error.message || 'Failed to load data'}
                    </p>
                    <button class="btn btn-outline-secondary" onclick="loadUniqueValues('${column}')">
                        <i class="fas fa-sync-alt"></i> Coba Lagi
                    </button>
                `;
            }
        });
}

// Fungsi untuk menampilkan nilai unik dalam dropdown
function displayUniqueValues(column, values) {
    const optionsContainer = document.getElementById(`filter-options-${column}`);

    // Periksa apakah container ada
    if (!optionsContainer) {
        console.warn(`Container filter-options-${column} tidak ditemukan`);
        return;
    }

    // Jika sudah ada opsi khusus (seperti untuk JK atau Status), jangan timpa
    if (column === 'jk' || column === 'status') {
        return;
    }

    // Kosongkan container
    optionsContainer.innerHTML = '';

    // Tambahkan checkbox untuk setiap nilai unik
    values.forEach(value => {
        if (value) { // Hanya tampilkan nilai yang tidak kosong
            const label = document.createElement('label');
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.value = value;

            // Centang checkbox jika nilai sudah difilter
            if (columnFilters[column] && columnFilters[column].includes(value)) {
                checkbox.checked = true;
            }

            label.appendChild(checkbox);
            label.appendChild(document.createTextNode(` ${value}`));
            optionsContainer.appendChild(label);
        }
    });

    // Jika tidak ada nilai, tampilkan pesan
    if (optionsContainer.children.length === 0) {
        optionsContainer.innerHTML = '<p>Tidak ada data</p>';
    }
}

// Fungsi untuk filter kolom berdasarkan input pencarian
function filterColumn(column, value) {
    const optionsContainer = document.getElementById(`filter-options-${column}`);

    // Periksa apakah container ada
    if (!optionsContainer) {
        console.warn(`Container filter-options-${column} tidak ditemukan`);
        return;
    }

    const options = optionsContainer.querySelectorAll('label');

    options.forEach(option => {
        const text = option.textContent.toLowerCase();
        if (text.includes(value.toLowerCase())) {
            option.style.display = '';
        } else {
            option.style.display = 'none';
        }
    });
}

// Fungsi untuk menerapkan filter
function applyFilter(column) {
    // Debug log
    if (window.debugLog) {
        window.debugLog(`Menerapkan filter untuk kolom: ${column}`);
    }

    // Ambil semua checkbox yang dicentang
    const checkboxes = document.querySelectorAll(`#filter-options-${column} input[type="checkbox"]:checked`);

    // Simpan nilai yang dicentang dalam array
    const values = Array.from(checkboxes).map(checkbox => checkbox.value);

    // Debug log
    if (window.debugLog) {
        window.debugLog(`Nilai filter yang dipilih untuk ${column}:`, {
            values: values,
            count: values.length
        });
    }

    // Simpan filter sebelumnya untuk perbandingan
    const previousFilters = JSON.parse(JSON.stringify(columnFilters));

    // Simpan filter dalam objek columnFilters
    if (values.length > 0) {
        columnFilters[column] = values;
    } else {
        delete columnFilters[column];
    }

    // Debug: Tampilkan semua filter yang aktif di console
    console.log('Filter yang aktif setelah perubahan:', JSON.parse(JSON.stringify(columnFilters)));

    // Perbarui tampilan header kolom untuk menunjukkan filter aktif
    updateFilterIndicator(column, values.length > 0);

    // Tutup dropdown
    const dropdown = document.getElementById(`filter-${column}`);
    if (dropdown) {
        dropdown.classList.remove('show');
    } else {
        console.warn(`Dropdown filter-${column} tidak ditemukan`);
    }

    // Reset ke halaman pertama
    currentPage = 1;

    // Tampilkan loading indicator
    loadingIndicator.style.display = 'block';
    tableContainer.style.opacity = '0.5';

    // Jika kolom adalah pt, perbarui semua filter terkait
    if (column === 'pt') {
        // Debug log
        if (window.debugLog) {
            window.debugLog(`Memperbarui filter terkait untuk pt: ${values.join(', ')}`);
        }

        // Jika filter PT berubah, hapus filter yang mungkin tidak relevan
        if (JSON.stringify(previousFilters[column]) !== JSON.stringify(values)) {
            // Hapus filter yang mungkin tidak relevan dengan PT baru
            ['dept', 'bagian', 'jabatan', 'group', 'nama', 'nik'].forEach(relatedColumn => {
                if (columnFilters[relatedColumn]) {
                    delete columnFilters[relatedColumn];
                    updateFilterIndicator(relatedColumn, false);
                    console.log(`Reset filter ${relatedColumn} karena PT berubah`);
                }
            });
        }

        // Perbarui filter terkait
        updateRelatedFilters('pt', values, 'dept');
        updateRelatedFilters('pt', values, 'bagian');
        updateRelatedFilters('pt', values, 'jabatan');
        updateRelatedFilters('pt', values, 'group');
        updateRelatedFilters('pt', values, 'nama');
        updateRelatedFilters('pt', values, 'nik');
    }
    // Jika kolom adalah dept, perbarui filter terkait
    else if (column === 'dept') {
        // Debug log
        if (window.debugLog) {
            window.debugLog(`Memperbarui filter terkait untuk dept: ${values.join(', ')}`);
        }

        // Jika filter Dept berubah, hapus filter yang mungkin tidak relevan
        if (JSON.stringify(previousFilters[column]) !== JSON.stringify(values)) {
            // Hapus filter yang mungkin tidak relevan dengan Dept baru
            ['bagian', 'jabatan', 'group', 'nama', 'nik'].forEach(relatedColumn => {
                if (columnFilters[relatedColumn]) {
                    delete columnFilters[relatedColumn];
                    updateFilterIndicator(relatedColumn, false);
                    console.log(`Reset filter ${relatedColumn} karena Dept berubah`);
                }
            });
        }

        updateRelatedFilters('dept', values, 'bagian');
        updateRelatedFilters('dept', values, 'jabatan');
        updateRelatedFilters('dept', values, 'nama');
        updateRelatedFilters('dept', values, 'nik');
        updateRelatedFilters('dept', values, 'group');
    }
    // Jika kolom adalah bagian, perbarui filter terkait
    else if (column === 'bagian') {
        // Debug log
        if (window.debugLog) {
            window.debugLog(`Memperbarui filter terkait untuk bagian: ${values.join(', ')}`);
        }

        // Jika filter Bagian berubah, hapus filter yang mungkin tidak relevan
        if (JSON.stringify(previousFilters[column]) !== JSON.stringify(values)) {
            // Hapus filter yang mungkin tidak relevan dengan Bagian baru
            ['jabatan', 'group', 'nama', 'nik'].forEach(relatedColumn => {
                if (columnFilters[relatedColumn]) {
                    delete columnFilters[relatedColumn];
                    updateFilterIndicator(relatedColumn, false);
                    console.log(`Reset filter ${relatedColumn} karena Bagian berubah`);
                }
            });
        }

        updateRelatedFilters('bagian', values, 'jabatan');
        updateRelatedFilters('bagian', values, 'nama');
        updateRelatedFilters('bagian', values, 'nik');
        updateRelatedFilters('bagian', values, 'group');
    }
    // Jika kolom adalah jabatan, perbarui filter nama, NIK, dan group
    else if (column === 'jabatan') {
        // Debug log
        if (window.debugLog) {
            window.debugLog(`Memperbarui filter terkait untuk jabatan: ${values.join(', ')}`);
        }

        // Jika filter Jabatan berubah, hapus filter yang mungkin tidak relevan
        if (JSON.stringify(previousFilters[column]) !== JSON.stringify(values)) {
            // Hapus filter yang mungkin tidak relevan dengan Jabatan baru
            ['group', 'nama', 'nik'].forEach(relatedColumn => {
                if (columnFilters[relatedColumn]) {
                    delete columnFilters[relatedColumn];
                    updateFilterIndicator(relatedColumn, false);
                    console.log(`Reset filter ${relatedColumn} karena Jabatan berubah`);
                }
            });
        }

        updateRelatedFilters('jabatan', values, 'nama');
        updateRelatedFilters('jabatan', values, 'nik');
        updateRelatedFilters('jabatan', values, 'group');
    }
    // Jika kolom adalah group, perbarui filter nama dan NIK
    else if (column === 'group') {
        // Debug log
        if (window.debugLog) {
            window.debugLog(`Memperbarui filter terkait untuk group: ${values.join(', ')}`);
        }

        // Jika filter Group berubah, hapus filter yang mungkin tidak relevan
        if (JSON.stringify(previousFilters[column]) !== JSON.stringify(values)) {
            // Hapus filter yang mungkin tidak relevan dengan Group baru
            ['nama', 'nik'].forEach(relatedColumn => {
                if (columnFilters[relatedColumn]) {
                    delete columnFilters[relatedColumn];
                    updateFilterIndicator(relatedColumn, false);
                    console.log(`Reset filter ${relatedColumn} karena Group berubah`);
                }
            });
        }

        updateRelatedFilters('group', values, 'nama');
        updateRelatedFilters('group', values, 'nik');
    }

    // Perbarui counter filter aktif
    updateActiveFiltersCount();

    // Perbarui tabel
    searchEmployees(currentSearchQuery);

    // Tampilkan feedback visual
    const filterButton = document.querySelector(`.column-header[data-column="${column}"] .filter-button`);
    if (filterButton) {
        // Tambahkan class untuk menunjukkan filter aktif
        if (values.length > 0) {
            filterButton.classList.add('active-filter');
        } else {
            filterButton.classList.remove('active-filter');
        }

        // Tambahkan animasi feedback
        filterButton.classList.add('filter-applied');
        setTimeout(() => {
            filterButton.classList.remove('filter-applied');
        }, 1000);
    }
}

// Fungsi untuk memperbarui indikator filter
function updateFilterIndicator(column, isActive) {
    const header = document.querySelector(`.sortable-column[data-column="${column}"]`);
    if (!header) return;

    const filterButton = header.querySelector('.filter-button');
    if (!filterButton) return;

    if (isActive) {
        filterButton.classList.add('active-filter');
        filterButton.setAttribute('title', 'Filter aktif');
    } else {
        filterButton.classList.remove('active-filter');
        filterButton.setAttribute('title', 'Filter');
    }
}

// Fungsi untuk memperbarui jumlah filter aktif
function updateActiveFiltersCount() {
    const activeFiltersCount = Object.keys(columnFilters).length;
    const resetFiltersBtn = document.getElementById('resetFiltersBtn');

    if (resetFiltersBtn) {
        if (activeFiltersCount > 0) {
            resetFiltersBtn.innerHTML = `<i class="fas fa-filter"></i> Reset Filter (${activeFiltersCount})`;
            resetFiltersBtn.classList.add('has-active-filters');
            resetFiltersBtn.disabled = false;
        } else {
            resetFiltersBtn.innerHTML = `<i class="fas fa-filter"></i> Reset Filter`;
            resetFiltersBtn.classList.remove('has-active-filters');
            resetFiltersBtn.disabled = true;
        }
    }
}

// Fungsi untuk reset semua filter
function resetAllFilters() {
    // Debug: Log filter sebelum reset
    console.log('Filter sebelum reset:', JSON.parse(JSON.stringify(columnFilters)));

    // Reset objek columnFilters
    for (const key in columnFilters) {
        if (columnFilters.hasOwnProperty(key)) {
            delete columnFilters[key];
        }
    }

    // Debug: Log filter setelah reset
    console.log('Filter setelah reset:', JSON.parse(JSON.stringify(columnFilters)));

    // Reset semua checkbox filter
    document.querySelectorAll('.filter-options input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Reset indikator filter aktif
    document.querySelectorAll('.filter-button').forEach(button => {
        button.classList.remove('active-filter');
    });

    // Reset ke halaman pertama
    currentPage = 1;

    // Update counter filter aktif
    updateActiveFiltersCount();

    // Perbarui tabel
    searchEmployees(currentSearchQuery);

    // Tampilkan feedback visual
    const resetBtn = document.getElementById('resetFiltersBtn');
    if (resetBtn) {
        const originalHTML = resetBtn.innerHTML;
        resetBtn.innerHTML = `<i class="fas fa-check"></i> Filter Direset!`;
        resetBtn.classList.add('btn-success');
        resetBtn.classList.remove('btn-outline-secondary');

        setTimeout(() => {
            resetBtn.innerHTML = originalHTML;
            resetBtn.classList.remove('btn-success');
            resetBtn.classList.add('btn-outline-secondary');
            resetBtn.disabled = true;
        }, 2000);
    }

    // Tampilkan toast
    showToast('Semua filter berhasil direset', 'success');
}

// Fungsi untuk menghapus filter
function clearFilter(column) {
    // Hapus filter dari objek columnFilters
    delete columnFilters[column];

    // Hapus centang dari semua checkbox
    const checkboxes = document.querySelectorAll(`#filter-options-${column} input[type="checkbox"]`);

    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    // Tutup dropdown
    const dropdown = document.getElementById(`filter-${column}`);
    if (dropdown) {
        dropdown.classList.remove('show');
    } else {
        console.warn(`Dropdown filter-${column} tidak ditemukan`);
    }

    // Fungsi helper untuk reset filter
    function resetFilter(filterColumn) {
        delete columnFilters[filterColumn];
        const checkboxes = document.querySelectorAll(`#filter-options-${filterColumn} input[type="checkbox"]`);
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        loadUniqueValues(filterColumn);
    }

    // Jika kolom adalah pt, reset filter terkait
    if (column === 'pt') {
        // Reset semua filter terkait
        resetFilter('dept');
        resetFilter('bagian');
        resetFilter('jabatan');
        resetFilter('nama');
        resetFilter('nik');
        resetFilter('group');
    }

    // Jika kolom adalah dept, reset filter terkait
    if (column === 'dept') {
        // Reset semua filter terkait
        resetFilter('bagian');
        resetFilter('jabatan');
        resetFilter('nama');
        resetFilter('nik');
        resetFilter('group');
    }

    // Jika kolom adalah bagian, reset filter terkait
    if (column === 'bagian') {
        resetFilter('jabatan');
        resetFilter('nama');
        resetFilter('nik');
        resetFilter('group');
    }

    // Jika kolom adalah jabatan, reset filter terkait
    if (column === 'jabatan') {
        resetFilter('nama');
        resetFilter('nik');
        resetFilter('group');
    }

    // Jika kolom adalah group, reset filter terkait
    if (column === 'group') {
        resetFilter('nama');
        resetFilter('nik');
    }

    // Reset ke halaman pertama
    currentPage = 1;

    // Perbarui tabel
    searchEmployees(currentSearchQuery);
}

// Fungsi untuk mereset semua filter
function resetAllFilters() {
    // Hapus semua filter
    columnFilters = {};

    // Reset semua checkbox
    const checkboxes = document.querySelectorAll('.filter-options input[type="checkbox"]');

    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    // Tutup semua dropdown yang terbuka
    const openDropdowns = document.querySelectorAll('.filter-dropdown.show');

    openDropdowns.forEach(dropdown => {
        dropdown.classList.remove('show');
    });

    // Reset ke halaman pertama
    currentPage = 1;

    // Muat ulang nilai unik untuk semua kolom
    const columns = ['dept', 'bagian', 'jabatan', 'group', 'nama', 'nik', 'status', 'pt', 'agama', 'pendidikan_akhir', 'jk', 'level_karyawan'];

    columns.forEach(column => {
        loadUniqueValues(column);
    });

    // Tampilkan visual feedback
    const resetBtn = document.getElementById('resetFiltersBtn');
    if (!resetBtn) {
        console.warn('Tombol reset tidak ditemukan');
    } else {
        const originalHTML = resetBtn.innerHTML;
        resetBtn.innerHTML = '<i class="fas fa-check"></i> Filter Direset';
        resetBtn.classList.add('btn-success');
        resetBtn.classList.remove('btn-outline-secondary');

        // Kembalikan tampilan tombol setelah 2 detik
        setTimeout(() => {
            resetBtn.innerHTML = originalHTML;
            resetBtn.classList.remove('btn-success');
            resetBtn.classList.add('btn-outline-secondary');
        }, 2000);
    }

    // Perbarui tabel
    searchEmployees(currentSearchQuery);
}

// Fungsi untuk memperbarui filter terkait
async function updateRelatedFilters(parentColumn, parentValues, childColumn) {
    // Debug: Log parameter
    console.log(`Updating related filters: ${parentColumn} -> ${childColumn}`, {
        parentValues: parentValues
    });

    if (!parentValues || parentValues.length === 0) {
        // Jika tidak ada nilai induk yang dipilih, muat semua nilai anak
        console.log(`No parent values selected, loading all values for ${childColumn}`);
        loadUniqueValues(childColumn);
        return;
    }

    try {
        // Tampilkan loading di dropdown anak
        const childOptionsContainer = document.getElementById(`filter-options-${childColumn}`);

        // Periksa apakah container ada
        if (!childOptionsContainer) {
            console.warn(`Container filter-options-${childColumn} tidak ditemukan`);

            // Debug: Coba cari container dengan selector yang berbeda
            const allContainers = document.querySelectorAll('[id^="filter-options-"]');
            console.log('All filter option containers:', allContainers.length);
            allContainers.forEach(c => console.log(' - ' + c.id));

            // Coba buat container jika tidak ada
            if (!document.getElementById(`filter-${childColumn}`)) {
                console.warn(`Dropdown filter-${childColumn} tidak ditemukan`);
                return;
            }

            const dropdown = document.getElementById(`filter-${childColumn}`);
            if (!dropdown) {
                console.warn(`Dropdown filter-${childColumn} tidak ditemukan`);
                return;
            }

            // Cek apakah dropdown sudah memiliki container options
            if (!dropdown.querySelector('.filter-options')) {
                const optionsDiv = document.createElement('div');
                optionsDiv.className = 'filter-options';
                optionsDiv.id = `filter-options-${childColumn}`;

                // Cari posisi untuk menyisipkan container
                const searchDiv = dropdown.querySelector('.filter-search');
                if (searchDiv) {
                    searchDiv.after(optionsDiv);
                } else {
                    dropdown.prepend(optionsDiv);
                }

                console.log(`Created filter-options-${childColumn} container`);
            }

            return;
        }

        childOptionsContainer.innerHTML = '<p>Loading...</p>';

        // Ambil nilai anak yang terkait dengan nilai induk
        const childValues = [];

        // Ambil nilai anak untuk setiap nilai induk
        for (const parentValue of parentValues) {
            const response = await fetch(`get_related_values.php?parent_column=${parentColumn}&parent_value=${encodeURIComponent(parentValue)}&child_column=${childColumn}`);

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();

            if (data.success) {
                // Tambahkan nilai anak ke array
                childValues.push(...data.values);
            }
        }

        // Hapus duplikat
        const uniqueChildValues = [...new Set(childValues)];

        // Simpan nilai anak yang unik
        uniqueColumnValues[childColumn] = uniqueChildValues;

        // Tampilkan nilai anak yang unik
        displayUniqueValues(childColumn, uniqueChildValues);

        // Jika ada filter anak yang aktif, centang checkbox yang sesuai
        if (columnFilters[childColumn]) {
            const childCheckboxes = document.querySelectorAll(`#filter-options-${childColumn} input[type="checkbox"]`);
            childCheckboxes.forEach(checkbox => {
                if (columnFilters[childColumn].includes(checkbox.value)) {
                    checkbox.checked = true;
                }
            });
        }
    } catch (error) {
        console.error(`Error updating ${childColumn} filter:`, error);
        const childOptionsContainer = document.getElementById(`filter-options-${childColumn}`);
        if (childOptionsContainer) {
            childOptionsContainer.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    }
}

// Fungsi untuk mengurutkan kolom
function sortByColumn(column) {
    // Debug log
    if (window.debugLog) {
        window.debugLog(`Mengurutkan berdasarkan kolom: ${column}`, {
            previousColumn: sortColumn,
            previousDirection: sortDirection
        });
    }

    // Jika kolom yang sama diklik, balik arah pengurutan
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        // Jika kolom berbeda, set kolom baru dan arah ke ascending
        sortColumn = column;
        sortDirection = 'asc';
    }

    // Debug log
    if (window.debugLog) {
        window.debugLog(`Pengurutan diubah menjadi: ${sortColumn} ${sortDirection}`);
    }

    // Tampilkan loading indicator
    loadingIndicator.style.display = 'block';
    tableContainer.style.opacity = '0.5';

    // Perbarui tampilan header
    updateSortIndicator();

    // Reset ke halaman pertama saat mengurutkan kolom
    currentPage = 1;

    // Perbarui tabel
    searchEmployees(currentSearchQuery);

    // Tambahkan feedback visual
    const header = document.querySelector(`.sortable-column[data-column="${column}"]`);
    if (header) {
        header.classList.add('sort-highlight');
        setTimeout(() => {
            header.classList.remove('sort-highlight');
        }, 1000);
    }
}

// Fungsi untuk memperbarui indikator pengurutan
function updateSortIndicator() {
    // Hapus kelas sort dari semua header
    document.querySelectorAll('.sortable-column').forEach(header => {
        header.classList.remove('sort-asc', 'sort-desc');
    });

    // Tambahkan kelas sort ke header yang aktif
    if (sortColumn) {
        const header = document.querySelector(`.sortable-column[data-column="${sortColumn}"]`);
        if (header) {
            header.classList.add(`sort-${sortDirection}`);

            // Tambahkan tooltip untuk menunjukkan arah pengurutan
            const directionText = sortDirection === 'asc' ? 'Ascending (A-Z)' : 'Descending (Z-A)';
            header.setAttribute('title', `Diurutkan: ${directionText}`);

            // Tambahkan ikon pengurutan jika belum ada
            const headerText = header.querySelector('.column-header span');
            if (headerText) {
                // Hapus ikon yang sudah ada
                const existingIcon = headerText.querySelector('.sort-icon');
                if (existingIcon) {
                    existingIcon.remove();
                }

                // Tambahkan ikon baru
                const icon = document.createElement('i');
                icon.className = `fas fa-sort-${sortDirection === 'asc' ? 'up' : 'down'} sort-icon`;
                icon.style.marginLeft = '5px';
                headerText.appendChild(icon);
            }
        }
    }
}

// Function untuk copy NIK individual - global scope untuk akses dari HTML onclick
function copyNIK(nik, button) {
    try {
        // Buat temporary textarea untuk menyalin teks
        const textarea = document.createElement('textarea');
        textarea.value = nik || '';
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);

        // Tampilkan feedback visual
        let originalText = '';
        let targetButton = button;

        // Jika button tidak diberikan (dipanggil dari mobile card)
        if (!button) {
            // Cari tombol copy di card dengan NIK yang sesuai
            const card = document.querySelector(`.employee-card[data-nik="${nik}"]`);
            if (card) {
                targetButton = card.querySelector('.copy-btn');
                if (targetButton) {
                    originalText = targetButton.innerHTML;
                    targetButton.innerHTML = '<i class="fas fa-check"></i>';
                    targetButton.classList.add('copied');
                }
            }

            // Tampilkan toast notification untuk mobile
            showToast('NIK disalin!', 'success', 2000);
        } else {
            // Untuk tampilan desktop
            originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.classList.add('copied');

            // Tampilkan tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = 'NIK disalin!';
            tooltip.style.position = 'absolute';

            // Posisikan tooltip di atas tombol
            const rect = button.getBoundingClientRect();
            tooltip.style.top = `${rect.top - 30}px`;
            tooltip.style.left = `${rect.left}px`;
            tooltip.style.display = 'block';

            document.body.appendChild(tooltip);

            // Hapus tooltip setelah 2 detik
            setTimeout(() => {
                if (document.body.contains(tooltip)) {
                    document.body.removeChild(tooltip);
                }
            }, 2000);
        }

        // Kembalikan tampilan tombol setelah 2 detik
        if (targetButton) {
            setTimeout(() => {
                targetButton.innerHTML = originalText;
                targetButton.classList.remove('copied');
            }, 2000);
        }
    } catch (error) {
        console.error('Error copying NIK:', error);
    }
}

// Fungsi untuk mengisi tampilan kartu mobile
function populateMobileCards(employees) {
    const mobileCardsContainer = document.getElementById('mobileCardsContainer');
    if (!mobileCardsContainer) {
        console.warn('Container mobileCardsContainer tidak ditemukan');
        return;
    }

    if (!Array.isArray(employees)) {
        console.error('Expected array of employees in populateMobileCards');
        return;
    }

    let cardsHTML = '';

    employees.forEach(employee => {
        const isSelected = selectedNIKs.includes(employee.nik);
        const isInactive = employee.status === 'Tidak Aktif' || employee.status === 'Resign' || employee.status === 'Habis Kontrak';

        cardsHTML += `
            <div class="employee-card ${isSelected ? 'selected-card' : ''} ${isInactive ? 'inactive-card' : ''}" data-nik="${employee.nik}">
                <div class="card-header-section">
                    <div class="card-nik">
                        <span class="nik-text">${employee.nik}</span>
                        <button class="copy-btn" onclick="copyNIK('${employee.nik}')" title="Copy NIK">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="card-select">
                        <input type="checkbox" value="${employee.nik}" ${isSelected ? 'checked' : ''} onchange="toggleNIKSelection(this)">
                    </div>
                </div>
                <div class="card-body-section">
                    <div class="card-info-item">
                        <div class="card-info-label">Nama</div>
                        <div class="card-info-value">${employee.nama || '-'}</div>
                    </div>
                    <div class="card-info-item">
                        <div class="card-info-label">Departemen</div>
                        <div class="card-info-value">${employee.dept || '-'}</div>
                    </div>
                    <div class="card-info-item">
                        <div class="card-info-label">Bagian</div>
                        <div class="card-info-value">${employee.bagian || '-'}</div>
                    </div>
                    <div class="card-info-item">
                        <div class="card-info-label">Jabatan</div>
                        <div class="card-info-value">${employee.jabatan || '-'}</div>
                    </div>
                </div>
                <div class="card-actions">
                    <div class="btn-group">
                        <button class="btn btn-info" onclick="showEmployeeDetail('${employee.nik}')" title="Detail">
                            <i class="fas fa-eye"></i> Detail
                        </button>
                        <button class="btn btn-warning" onclick="editEmployee('${employee.nik}')" title="Edit">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-danger" onclick="deleteEmployee('${employee.nik}')" title="Hapus">
                            <i class="fas fa-trash"></i> Hapus
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    mobileCardsContainer.innerHTML = cardsHTML;

    // Setup event listeners for mobile cards
    setupMobileCardSelection();
}

// Fungsi untuk setup event listener pada kartu mobile
function setupMobileCardSelection() {
    // Periksa apakah ada kartu mobile
    const mobileCards = document.querySelectorAll('.employee-card');

    if (!mobileCards || mobileCards.length === 0) {
        console.warn('Tidak ada kartu mobile yang ditemukan');
        return;
    }

    // Tambahkan event listener untuk klik pada kartu (selain tombol)
    mobileCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Jika klik pada tombol atau checkbox, jangan lakukan apa-apa
            if (e.target.tagName === 'BUTTON' || e.target.tagName === 'INPUT' ||
                e.target.tagName === 'I' || e.target.closest('button') || e.target.closest('input')) {
                return;
            }

            // Toggle checkbox
            const checkbox = this.querySelector('input[type="checkbox"]');
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                toggleNIKSelection(checkbox);
            }
        });
    });
}

// Fungsi untuk setup event listener pada baris tabel
function setupTableRowSelection() {
    // Periksa apakah ada tabel body
    const tableRows = document.querySelectorAll('#employeesTableBody tr');

    if (!tableRows || tableRows.length === 0) {
        console.warn('Tidak ada baris tabel yang ditemukan');
        return;
    }

    // Tambahkan event listener untuk klik pada baris tabel (selain tombol dan checkbox)
    tableRows.forEach(row => {
        row.addEventListener('click', function(e) {
            // Jika klik pada tombol, checkbox, atau ikon, jangan lakukan apa-apa
            if (e.target.tagName === 'BUTTON' || e.target.tagName === 'INPUT' || e.target.tagName === 'A' ||
                e.target.tagName === 'I' || e.target.closest('button') || e.target.closest('input') || e.target.closest('a')) {
                return;
            }

            // Toggle checkbox
            const checkbox = this.querySelector('input[name="select-nik"]');
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                toggleNIKSelection(checkbox);
            }
        });
    });
}

// Fungsi untuk menampilkan toast notification
function showToast(message, type = 'success', duration = 3000) {
    // Hapus toast yang sudah ada jika ada
    const existingToast = document.querySelector('.toast-notification');
    if (existingToast) {
        document.body.removeChild(existingToast);
    }

    // Buat toast baru
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;

    // Set icon berdasarkan type
    let icon = 'check';
    if (type === 'error') icon = 'exclamation-circle';
    if (type === 'warning') icon = 'exclamation-triangle';
    if (type === 'info') icon = 'info-circle';

    toast.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;
    document.body.appendChild(toast);

    // Animasi toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // Hapus toast setelah durasi tertentu
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// Fungsi untuk menampilkan dialog konfirmasi kustom
function showConfirmDialog(message, onConfirm, onCancel) {
    // Hapus dialog yang sudah ada jika ada
    const existingDialog = document.querySelector('.confirm-dialog-container');
    if (existingDialog) {
        document.body.removeChild(existingDialog);
    }

    // Buat dialog konfirmasi
    const dialogContainer = document.createElement('div');
    dialogContainer.className = 'confirm-dialog-container';

    dialogContainer.innerHTML = `
        <div class="confirm-dialog">
            <div class="confirm-dialog-header">
                <i class="fas fa-question-circle"></i>
                <h4>Konfirmasi</h4>
            </div>
            <div class="confirm-dialog-body">
                <p>${message}</p>
            </div>
            <div class="confirm-dialog-footer">
                <button class="btn btn-secondary btn-cancel">Batal</button>
                <button class="btn btn-danger btn-confirm">Ya, Lanjutkan</button>
            </div>
        </div>
    `;

    document.body.appendChild(dialogContainer);

    // Tambahkan event listener untuk tombol
    const cancelBtn = dialogContainer.querySelector('.btn-cancel');
    const confirmBtn = dialogContainer.querySelector('.btn-confirm');

    cancelBtn.addEventListener('click', () => {
        document.body.removeChild(dialogContainer);
        if (onCancel) onCancel();
    });

    confirmBtn.addEventListener('click', () => {
        document.body.removeChild(dialogContainer);
        if (onConfirm) onConfirm();
    });

    // Animasi dialog
    setTimeout(() => {
        dialogContainer.classList.add('show');
    }, 10);
}

// Tambahkan CSS untuk toast notification dan dialog konfirmasi
document.head.insertAdjacentHTML('beforeend', `
<style>
.toast-notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.toast-notification.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.toast-success i {
    color: #4CAF50;
}

.toast-error i {
    color: #F44336;
}

.toast-warning i {
    color: #FF9800;
}

.toast-info i {
    color: #2196F3;
}

.confirm-dialog-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.confirm-dialog-container.show {
    opacity: 1;
}

.confirm-dialog {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.confirm-dialog-container.show .confirm-dialog {
    transform: translateY(0);
}

.confirm-dialog-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px;
}

.confirm-dialog-header i {
    color: #BF0000;
    font-size: 1.2rem;
}

.confirm-dialog-header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: #212529;
}

.confirm-dialog-body {
    padding: 20px;
}

.confirm-dialog-body p {
    margin: 0;
    color: #495057;
}

.confirm-dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

@media screen and (max-width: 576px) {
    .confirm-dialog {
        width: 95%;
    }

    .confirm-dialog-header,
    .confirm-dialog-body,
    .confirm-dialog-footer {
        padding: 12px 15px;
    }
}
</style>
`);

// Fungsi untuk menangani pencarian
function handleSearch(query) {
    clearTimeout(searchTimeout);

    // Simpan query asli (dengan spasi) untuk ditampilkan di kotak pencarian
    const rawQuery = query;
    // Gunakan query yang di-trim untuk pencarian
    const searchQuery = query.trim();

    // Simpan kedua versi query
    currentSearchQuery = {
        raw: rawQuery,     // Query asli dengan spasi untuk ditampilkan di kotak pencarian
        trimmed: searchQuery  // Query yang di-trim untuk pencarian
    };

    currentPage = 1; // Reset ke halaman pertama saat melakukan pencarian baru

    // Simpan posisi kursor saat ini
    const searchInput = document.getElementById('tableSearchInput');
    const cursorPosition = searchInput ? searchInput.selectionStart : null;

    // Simpan nilai input saat ini
    const currentValue = searchInput ? searchInput.value : '';

    searchTimeout = setTimeout(() => {
        searchEmployees(currentSearchQuery);

        // Kembalikan nilai input dan posisi kursor setelah pencarian
        if (searchInput) {
            // Pastikan nilai input tidak berubah
            if (searchInput.value !== currentValue) {
                searchInput.value = currentValue;
            }

            // Kembalikan posisi kursor
            if (cursorPosition !== null) {
                setTimeout(() => {
                    searchInput.focus();
                    searchInput.setSelectionRange(cursorPosition, cursorPosition);
                }, 50);
            }
        }
    }, 300);
}

// Fungsi untuk menginisialisasi kotak pencarian
function setupTableSearchInput() {
    const tableSearchInput = document.getElementById('tableSearchInput');
    if (!tableSearchInput) {
        console.warn('tableSearchInput not found');
        return;
    }

    // Tambahkan event listener untuk input
    tableSearchInput.addEventListener('input', function(e) {
        // Simpan posisi kursor sebelum menangani pencarian
        const cursorPosition = this.selectionStart;
        const currentValue = this.value;

        // Panggil handleSearch dengan nilai saat ini
        handleSearch(currentValue);

        // Pastikan nilai input tidak berubah dan kembalikan posisi kursor
        setTimeout(() => {
            // Jika nilai berubah, kembalikan ke nilai asli
            if (this.value !== currentValue) {
                this.value = currentValue;
            }

            // Kembalikan posisi kursor
            this.focus();
            this.setSelectionRange(cursorPosition, cursorPosition);
        }, 0);
    });

    // Selalu isi dengan nilai pencarian asli (dengan spasi) jika ada
    if (currentSearchQuery && typeof currentSearchQuery === 'object') {
        tableSearchInput.value = currentSearchQuery.raw || '';
    } else if (typeof currentSearchQuery === 'string') {
        // Untuk kompatibilitas dengan kode lama
        tableSearchInput.value = currentSearchQuery || '';
    } else {
        tableSearchInput.value = '';
    }

    // Atur placeholder yang lebih informatif
    tableSearchInput.placeholder = 'Contoh: "john it 2023" untuk mencari John di departemen IT tahun 2023';

    // Fokus pada kotak pencarian jika ada query pencarian aktif
    if (currentSearchQuery && (currentSearchQuery.raw || (typeof currentSearchQuery === 'string' && currentSearchQuery))) {
        // Gunakan setTimeout untuk menghindari masalah dengan fokus
        setTimeout(() => {
            // Hanya fokus pada input tanpa mengubah posisi kursor
            // Ini memungkinkan pengguna untuk mengedit di posisi mana pun dalam teks
            tableSearchInput.focus();
        }, 100);
    }
}

// Observer untuk mendeteksi perubahan pada DOM dan menginisialisasi kotak pencarian
const searchBoxObserver = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            // Cek apakah tableSearchInput sudah ada di DOM
            if (document.getElementById('tableSearchInput')) {
                setupTableSearchInput();
                // Tidak menghentikan observer agar dapat mendeteksi kotak pencarian baru
                // ketika tabel diperbarui
            }
        }
    });
});

// Mulai observasi
searchBoxObserver.observe(document.body, { childList: true, subtree: true });
</script>
<?php include '../config/footer.php';?>
<!-- Script untuk memperbaiki masalah lazy loading progress bar -->
<script src="js/override-progress-bar.js"></script>
</body>
</html>