<?php
// File: admin/manage_training_registrations.php
// Halaman untuk mengelola pendaftaran training

include '../config/config.php';
include 'security.php';

// Check if user is admin
if ($_SESSION['role_id'] != 1) {
    header("Location: ../pemohon/index.php");
    exit();
}

$page_title = "Kelola Pendaftaran Training";
include 'header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-user-check"></i> Kelola Pendaftaran Training
                        <span class="badge bg-warning" id="pendingCount">0</span>
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Filter Tabs -->
                    <ul class="nav nav-tabs mb-3" id="registrationTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab">
                                <i class="fas fa-clock"></i> Pending <span class="badge bg-warning ms-1" id="pendingTabCount">0</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="approved-tab" data-bs-toggle="tab" data-bs-target="#approved" type="button" role="tab">
                                <i class="fas fa-check-circle"></i> Disetujui <span class="badge bg-success ms-1" id="approvedTabCount">0</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="rejected-tab" data-bs-toggle="tab" data-bs-target="#rejected" type="button" role="tab">
                                <i class="fas fa-times-circle"></i> Ditolak <span class="badge bg-danger ms-1" id="rejectedTabCount">0</span>
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="registrationTabContent">
                        <!-- Pending Registrations -->
                        <div class="tab-pane fade show active" id="pending" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-hover" id="pendingTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Training</th>
                                            <th>Peserta</th>
                                            <th>Didaftarkan Oleh</th>
                                            <th>Tipe</th>
                                            <th>Tanggal Daftar</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody id="pendingTableBody">
                                        <!-- Data will be loaded via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Approved Registrations -->
                        <div class="tab-pane fade" id="approved" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-hover" id="approvedTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Training</th>
                                            <th>Peserta</th>
                                            <th>Didaftarkan Oleh</th>
                                            <th>Tipe</th>
                                            <th>Disetujui Oleh</th>
                                            <th>Tanggal Disetujui</th>
                                        </tr>
                                    </thead>
                                    <tbody id="approvedTableBody">
                                        <!-- Data will be loaded via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Rejected Registrations -->
                        <div class="tab-pane fade" id="rejected" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-hover" id="rejectedTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Training</th>
                                            <th>Peserta</th>
                                            <th>Didaftarkan Oleh</th>
                                            <th>Tipe</th>
                                            <th>Ditolak Oleh</th>
                                            <th>Alasan</th>
                                            <th>Tanggal Ditolak</th>
                                        </tr>
                                    </thead>
                                    <tbody id="rejectedTableBody">
                                        <!-- Data will be loaded via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Approval/Rejection -->
<div class="modal fade" id="actionModal" tabindex="-1" aria-labelledby="actionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="actionModalLabel">Konfirmasi Pendaftaran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="actionModalContent">
                    <!-- Content will be loaded here -->
                </div>
                <div class="mb-3">
                    <label for="adminNotes" class="form-label">Catatan Admin (Opsional)</label>
                    <textarea class="form-control" id="adminNotes" rows="3" placeholder="Berikan catatan jika diperlukan..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-success" id="approveBtn" onclick="processRegistration('approved')">
                    <i class="fas fa-check"></i> Setujui
                </button>
                <button type="button" class="btn btn-danger" id="rejectBtn" onclick="processRegistration('rejected')">
                    <i class="fas fa-times"></i> Tolak
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentRegistrationId = null;

document.addEventListener('DOMContentLoaded', function() {
    // Load data when tabs are shown
    document.getElementById('pending-tab').addEventListener('shown.bs.tab', () => loadRegistrations('pending'));
    document.getElementById('approved-tab').addEventListener('shown.bs.tab', () => loadRegistrations('approved'));
    document.getElementById('rejected-tab').addEventListener('shown.bs.tab', () => loadRegistrations('rejected'));
    
    // Load pending registrations by default
    loadRegistrations('pending');
    
    // Auto refresh every 30 seconds for pending registrations
    setInterval(() => {
        if (document.getElementById('pending-tab').classList.contains('active')) {
            loadRegistrations('pending');
        }
    }, 30000);
});

function loadRegistrations(status) {
    const tableBody = document.getElementById(status + 'TableBody');
    tableBody.innerHTML = '<tr><td colspan="6" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>';
    
    fetch(`training_registration_admin_api.php?action=get_registrations&status=${status}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayRegistrations(data.registrations, status);
                updateTabCounts(data.counts);
            } else {
                tableBody.innerHTML = `<tr><td colspan="6" class="text-center text-danger">Error: ${data.message}</td></tr>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading data</td></tr>';
        });
}

function displayRegistrations(registrations, status) {
    const tableBody = document.getElementById(status + 'TableBody');
    
    if (registrations.length === 0) {
        tableBody.innerHTML = `<tr><td colspan="6" class="text-center text-muted">Tidak ada data ${status}</td></tr>`;
        return;
    }
    
    let html = '';
    registrations.forEach(reg => {
        const trainingTitle = reg.training_title || 'Training tidak ditemukan';
        const registrationType = reg.registration_type === 'self' ? 'Diri Sendiri' : 'Oleh Supervisor';
        
        html += `
            <tr>
                <td>
                    <strong>${trainingTitle}</strong><br>
                    <small class="text-muted">${reg.training_type === 'internal' ? 'Internal' : 'Eksternal'}</small>
                </td>
                <td>
                    <strong>${reg.participant_nama}</strong><br>
                    <small class="text-muted">NIK: ${reg.participant_nik}</small><br>
                    <small class="text-muted">${reg.participant_dept} - ${reg.participant_jabatan}</small>
                </td>
                <td>
                    <strong>${reg.registered_by_name}</strong><br>
                    <small class="text-muted">${reg.registered_by_dept}</small><br>
                    <span class="badge bg-${reg.registration_type === 'self' ? 'primary' : 'info'}">${registrationType}</span>
                </td>
                <td>
                    <span class="badge bg-${reg.training_type === 'internal' ? 'success' : 'warning'}">${reg.training_type === 'internal' ? 'Internal' : 'Eksternal'}</span>
                </td>
        `;
        
        if (status === 'pending') {
            html += `
                <td>${new Date(reg.created_at).toLocaleDateString('id-ID')}</td>
                <td>
                    <button class="btn btn-success btn-sm me-1" onclick="showActionModal(${reg.id}, 'approve')">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="showActionModal(${reg.id}, 'reject')">
                        <i class="fas fa-times"></i>
                    </button>
                </td>
            `;
        } else if (status === 'approved') {
            html += `
                <td>${reg.approved_by_name || 'System'}</td>
                <td>${new Date(reg.approved_at).toLocaleDateString('id-ID')}</td>
            `;
        } else if (status === 'rejected') {
            html += `
                <td>${reg.approved_by_name || 'System'}</td>
                <td>${reg.admin_notes || '-'}</td>
                <td>${new Date(reg.approved_at).toLocaleDateString('id-ID')}</td>
            `;
        }
        
        html += '</tr>';
    });
    
    tableBody.innerHTML = html;
}

function updateTabCounts(counts) {
    document.getElementById('pendingCount').textContent = counts.pending || 0;
    document.getElementById('pendingTabCount').textContent = counts.pending || 0;
    document.getElementById('approvedTabCount').textContent = counts.approved || 0;
    document.getElementById('rejectedTabCount').textContent = counts.rejected || 0;
}

function showActionModal(registrationId, action) {
    currentRegistrationId = registrationId;
    
    // Get registration details
    fetch(`training_registration_admin_api.php?action=get_registration_detail&id=${registrationId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const reg = data.registration;
                const actionText = action === 'approve' ? 'menyetujui' : 'menolak';
                const actionColor = action === 'approve' ? 'success' : 'danger';
                
                document.getElementById('actionModalContent').innerHTML = `
                    <div class="alert alert-${actionColor}">
                        <h6>Anda akan ${actionText} pendaftaran berikut:</h6>
                        <ul class="mb-0">
                            <li><strong>Training:</strong> ${reg.training_title}</li>
                            <li><strong>Peserta:</strong> ${reg.participant_nama} (${reg.participant_nik})</li>
                            <li><strong>Didaftarkan oleh:</strong> ${reg.registered_by_name}</li>
                            <li><strong>Tipe:</strong> ${reg.registration_type === 'self' ? 'Diri Sendiri' : 'Oleh Supervisor'}</li>
                        </ul>
                    </div>
                `;
                
                // Show/hide buttons based on action
                document.getElementById('approveBtn').style.display = action === 'approve' ? 'inline-block' : 'none';
                document.getElementById('rejectBtn').style.display = action === 'reject' ? 'inline-block' : 'none';
                
                // Clear notes
                document.getElementById('adminNotes').value = '';
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('actionModal'));
                modal.show();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading registration details');
        });
}

function processRegistration(action) {
    if (!currentRegistrationId) return;
    
    const notes = document.getElementById('adminNotes').value.trim();
    
    const data = {
        action: 'process_registration',
        registration_id: currentRegistrationId,
        status: action,
        admin_notes: notes
    };
    
    fetch('training_registration_admin_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('actionModal'));
            modal.hide();
            
            // Reload current tab
            const activeTab = document.querySelector('.nav-link.active').id.replace('-tab', '');
            loadRegistrations(activeTab);
            
            // Also reload pending if we're not on pending tab
            if (activeTab !== 'pending') {
                loadRegistrations('pending');
            }
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('❌ Terjadi kesalahan');
    });
}
</script>

<?php include 'footer.php'; ?>
