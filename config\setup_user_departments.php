<?php
/**
 * Script untuk mengisi tabel user_departments berdasarkan data users
 * Ini diperlukan agar sistem notifikasi bisa menemukan dept head yang tepat
 */

require_once __DIR__ . '/config.php';

echo "🔧 Setting up User Departments Mapping\n";
echo "======================================\n\n";

// Cek data saat ini di user_departments
$check_query = "SELECT COUNT(*) as count FROM user_departments";
$check_result = $conn->query($check_query);
$current_count = $check_result->fetch_assoc()['count'];

echo "📊 Current user_departments records: $current_count\n\n";

// Ambil semua users dengan role_id = 2 (dept head)
$dept_heads_query = "SELECT id, name, dept, jabatan FROM users WHERE role_id = 2 AND is_active = 1";
$dept_heads_result = $conn->query($dept_heads_query);

if ($dept_heads_result->num_rows > 0) {
    echo "👥 Found " . $dept_heads_result->num_rows . " department heads:\n";
    
    $insert_count = 0;
    $update_count = 0;
    
    while ($dept_head = $dept_heads_result->fetch_assoc()) {
        echo "   - {$dept_head['name']} ({$dept_head['jabatan']}) - Dept: {$dept_head['dept']}\n";
        
        // Cek apakah sudah ada mapping untuk user ini
        $existing_query = "SELECT COUNT(*) as count FROM user_departments WHERE user_id = ? AND dept = ?";
        $existing_stmt = $conn->prepare($existing_query);
        $existing_stmt->bind_param("is", $dept_head['id'], $dept_head['dept']);
        $existing_stmt->execute();
        $existing_result = $existing_stmt->get_result();
        $existing_count = $existing_result->fetch_assoc()['count'];
        
        if ($existing_count == 0) {
            // Insert mapping baru
            $insert_query = "INSERT INTO user_departments (user_id, dept) VALUES (?, ?)";
            $insert_stmt = $conn->prepare($insert_query);
            $insert_stmt->bind_param("is", $dept_head['id'], $dept_head['dept']);
            
            if ($insert_stmt->execute()) {
                echo "     ✅ Added mapping: {$dept_head['name']} → {$dept_head['dept']}\n";
                $insert_count++;
            } else {
                echo "     ❌ Failed to add mapping: " . $insert_stmt->error . "\n";
            }
        } else {
            echo "     ℹ️  Mapping already exists\n";
            $update_count++;
        }
    }
    
    echo "\n📈 Summary:\n";
    echo "   ➕ New mappings added: $insert_count\n";
    echo "   ♻️  Existing mappings: $update_count\n";
    
} else {
    echo "❌ No department heads found with role_id = 2\n";
}

// Tambahkan mapping khusus untuk Manager HRGA jika belum ada
echo "\n🎯 Setting up special mappings...\n";

// Cari Manager HRGA
$hrga_manager_query = "SELECT id, name FROM users WHERE jabatan = 'Manager HRGA' AND role_id = 2 AND is_active = 1";
$hrga_manager_result = $conn->query($hrga_manager_query);

if ($hrga_manager_result->num_rows > 0) {
    $hrga_manager = $hrga_manager_result->fetch_assoc();
    
    // Pastikan Manager HRGA bertanggung jawab untuk departemen HRGA
    $hrga_check_query = "SELECT COUNT(*) as count FROM user_departments WHERE user_id = ? AND dept = 'HRGA'";
    $hrga_check_stmt = $conn->prepare($hrga_check_query);
    $hrga_check_stmt->bind_param("i", $hrga_manager['id']);
    $hrga_check_stmt->execute();
    $hrga_check_result = $hrga_check_stmt->get_result();
    $hrga_exists = $hrga_check_result->fetch_assoc()['count'];
    
    if ($hrga_exists == 0) {
        $hrga_insert_query = "INSERT INTO user_departments (user_id, dept) VALUES (?, 'HRGA')";
        $hrga_insert_stmt = $conn->prepare($hrga_insert_query);
        $hrga_insert_stmt->bind_param("i", $hrga_manager['id']);
        
        if ($hrga_insert_stmt->execute()) {
            echo "   ✅ Added HRGA mapping for Manager HRGA: {$hrga_manager['name']}\n";
        } else {
            echo "   ❌ Failed to add HRGA mapping: " . $hrga_insert_stmt->error . "\n";
        }
    } else {
        echo "   ℹ️  Manager HRGA already mapped to HRGA department\n";
    }
} else {
    echo "   ⚠️  No Manager HRGA found\n";
}

// Tampilkan mapping final
echo "\n📋 Final user_departments mapping:\n";
$final_query = "SELECT u.name, u.jabatan, ud.dept 
                FROM user_departments ud 
                JOIN users u ON ud.user_id = u.id 
                WHERE u.role_id = 2 
                ORDER BY ud.dept, u.name";
$final_result = $conn->query($final_query);

if ($final_result->num_rows > 0) {
    $current_dept = '';
    while ($mapping = $final_result->fetch_assoc()) {
        if ($mapping['dept'] != $current_dept) {
            echo "\n   📁 {$mapping['dept']}:\n";
            $current_dept = $mapping['dept'];
        }
        echo "      👤 {$mapping['name']} ({$mapping['jabatan']})\n";
    }
} else {
    echo "   ❌ No mappings found\n";
}

echo "\n✅ User departments setup completed!\n";

$conn->close();
?>
