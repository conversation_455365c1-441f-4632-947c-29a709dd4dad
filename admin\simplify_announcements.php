<?php
/**
 * Script untuk menyederhanakan manage_announcements.php
 * Menghapus bagian filter berdasarkan jabatan/role dan semua orang
 */

echo "🔧 SIMPLIFYING ANNOUNCEMENTS MANAGEMENT\n";
echo "=======================================\n\n";

$file_path = __DIR__ . '/manage_announcements.php';

if (!file_exists($file_path)) {
    echo "❌ File not found: $file_path\n";
    exit(1);
}

echo "📄 Reading file: $file_path\n";
$content = file_get_contents($file_path);

// 1. Update recipient type handling in PHP
echo "1️⃣ Simplifying recipient type handling...\n";

$old_recipient_handling = '    // Handle new recipients based on recipient_type
    $recipient_type = isset($post_data[\'recipient_type\']) ? $post_data[\'recipient_type\'] : \'all\';
    $selected_recipients = isset($post_data[\'recipients\']) ? $post_data[\'recipients\'] : [];
    $target_role = isset($post_data[\'target_role\']) ? $post_data[\'target_role\'] : \'\';

    error_log("Recipient Type: " . $recipient_type);
    error_log("Selected Recipients: " . print_r($selected_recipients, true));
    error_log("Target Role: " . $target_role);

    if ($recipient_type === \'all\') {
        // Send to all active users
        $users_query = "SELECT id FROM users WHERE is_active = 1";
        $result = $conn->query($users_query);

        $count = 0;
        while ($user = $result->fetch_assoc()) {
            $insert_recipient_query = "INSERT IGNORE INTO announcement_recipients (announcement_id, user_id) VALUES (?, ?)";
            $stmt_recipient = $conn->prepare($insert_recipient_query);
            $stmt_recipient->bind_param("ii", $announcement_id, $user[\'id\']);
            $stmt_recipient->execute();
            $stmt_recipient->close();
            $count++;
        }
        error_log("Inserted $count recipients for \'all\' type");

    } elseif ($recipient_type === \'role\' && !empty($target_role)) {
        // Send to users with specific role
        $users_query = "SELECT id FROM users WHERE is_active = 1 AND role_id = ?";
        $stmt = $conn->prepare($users_query);
        $stmt->bind_param("s", $target_role);
        $stmt->execute();
        $result = $stmt->get_result();

        $count = 0;
        while ($user = $result->fetch_assoc()) {
            $insert_recipient_query = "INSERT IGNORE INTO announcement_recipients (announcement_id, user_id) VALUES (?, ?)";
            $stmt_recipient = $conn->prepare($insert_recipient_query);
            $stmt_recipient->bind_param("ii", $announcement_id, $user[\'id\']);
            $stmt_recipient->execute();
            $stmt_recipient->close();
            $count++;
        }
        $stmt->close();
        error_log("Inserted $count recipients for role \'$target_role\'");

    } elseif ($recipient_type === \'specific\') {';

$new_recipient_handling = '    // Handle recipients (always specific recipients now)
    $selected_recipients = isset($post_data[\'recipients\']) ? $post_data[\'recipients\'] : [];

    error_log("Selected Recipients: " . print_r($selected_recipients, true));

    // Handle specific recipients from multiple sources';

if (strpos($content, $old_recipient_handling) !== false) {
    $content = str_replace($old_recipient_handling, $new_recipient_handling, $content);
    echo "   ✅ Updated recipient type handling\n";
} else {
    echo "   ⚠️  Recipient type handling not found or already updated\n";
}

// 2. Remove recipient type determination logic
echo "2️⃣ Removing recipient type determination logic...\n";

$old_determination = '// Get existing recipients for editing and determine recipient type
$existing_recipients = [];
$current_recipient_type = \'all\'; // default
$recipient_info = [];

if ($edit_announcement) {
    // Get recipients with user details
    $recipients_query = "SELECT ar.user_id, u.name, u.email, u.role_id, u.dept, u.bagian, u.jabatan, r.role_name
                        FROM announcement_recipients ar
                        JOIN users u ON ar.user_id = u.id
                        LEFT JOIN (
                            SELECT 1 as role_id, \'Pemohon\' as role_name
                            UNION SELECT 2, \'Dept Head\'
                            UNION SELECT 3, \'HRD\'
                            UNION SELECT 4, \'GA\'
                            UNION SELECT 5, \'Factory Manager\'
                            UNION SELECT 6, \'Direktur\'
                            UNION SELECT 99, \'Admin\'
                        ) r ON u.role_id = r.role_id
                        WHERE ar.announcement_id = ?
                        ORDER BY u.name";
    $stmt = $conn->prepare($recipients_query);
    $stmt->bind_param("i", $announcement_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $existing_recipients[] = $row[\'user_id\'];
        $recipient_info[] = $row;
    }
    $stmt->close();

    // Determine recipient type based on recipients
    if (!empty($existing_recipients)) {
        // Check if all users are recipients
        $total_users_query = "SELECT COUNT(*) as total FROM users WHERE is_active = 1";
        $total_result = $conn->query($total_users_query);
        $total_users = $total_result->fetch_assoc()[\'total\'];

        if (count($existing_recipients) == $total_users) {
            // All users are recipients
            $current_recipient_type = \'all\';
        } else {
            // Check if all recipients have the same role
            $roles = array_unique(array_column($recipient_info, \'role_id\'));
            if (count($roles) == 1) {
                // Check if all users with this role are recipients
                $role_id = $roles[0];
                $role_users_query = "SELECT COUNT(*) as total FROM users WHERE is_active = 1 AND role_id = ?";
                $stmt = $conn->prepare($role_users_query);
                $stmt->bind_param("s", $role_id);
                $stmt->execute();
                $role_total = $stmt->get_result()->fetch_assoc()[\'total\'];
                $stmt->close();

                if (count($existing_recipients) == $role_total) {
                    // All users with this role are recipients
                    $current_recipient_type = \'role\';
                } else {
                    // Specific users
                    $current_recipient_type = \'specific\';
                }
            } else {
                // Multiple roles or partial role coverage = specific users
                $current_recipient_type = \'specific\';
            }
        }
    }
}';

$new_determination = '// Get existing recipients for editing (always specific recipients now)
$existing_recipients = [];
$recipient_info = [];

if ($edit_announcement) {
    // Get recipients with user details
    $recipients_query = "SELECT ar.user_id, u.name, u.email, u.role_id, u.dept, u.bagian, u.jabatan, r.role_name
                        FROM announcement_recipients ar
                        JOIN users u ON ar.user_id = u.id
                        LEFT JOIN (
                            SELECT 1 as role_id, \'Pemohon\' as role_name
                            UNION SELECT 2, \'Dept Head\'
                            UNION SELECT 3, \'HRD\'
                            UNION SELECT 4, \'GA\'
                            UNION SELECT 5, \'Factory Manager\'
                            UNION SELECT 6, \'Direktur\'
                            UNION SELECT 99, \'Admin\'
                        ) r ON u.role_id = r.role_id
                        WHERE ar.announcement_id = ?
                        ORDER BY u.name";
    $stmt = $conn->prepare($recipients_query);
    $stmt->bind_param("i", $announcement_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $existing_recipients[] = $row[\'user_id\'];
        $recipient_info[] = $row;
    }
    $stmt->close();
}';

if (strpos($content, $old_determination) !== false) {
    $content = str_replace($old_determination, $new_determination, $content);
    echo "   ✅ Removed recipient type determination logic\n";
} else {
    echo "   ⚠️  Recipient type determination logic not found or already updated\n";
}

// 3. Update form validation in JavaScript
echo "3️⃣ Updating form validation...\n";

$old_validation = '        // Simple form validation
        const form = document.querySelector(\'form\');
        if (form) {
            form.addEventListener(\'submit\', function(e) {
                const recipientType = document.querySelector(\'input[name="recipient_type"]:checked\');

                if (!recipientType) {
                    e.preventDefault();
                    alert(\'Silakan pilih jenis penerima pengumuman.\');
                    return false;
                }

                if (recipientType.value === \'role\') {
                    const targetRole = document.getElementById(\'target_role_select\').value;
                    if (!targetRole) {
                        e.preventDefault();
                        alert(\'Silakan pilih role penerima pengumuman.\');
                        return false;
                    }
                }

                if (recipientType.value === \'specific\') {
                    const checkedUsers = document.querySelectorAll(\'.user-checkbox:checked\');
                    if (checkedUsers.length === 0) {
                        e.preventDefault();
                        alert(\'Silakan pilih minimal satu pengguna untuk menerima pengumuman ini.\');
                        return false;
                    }
                }
            });
        }';

$new_validation = '        // Simple form validation - only check if users are selected
        const form = document.querySelector(\'form\');
        if (form) {
            form.addEventListener(\'submit\', function(e) {
                const checkedUsers = document.querySelectorAll(\'.user-checkbox:checked\');
                if (checkedUsers.length === 0) {
                    e.preventDefault();
                    alert(\'Silakan pilih minimal satu pengguna untuk menerima pengumuman ini.\');
                    return false;
                }
            });
        }';

if (strpos($content, $old_validation) !== false) {
    $content = str_replace($old_validation, $new_validation, $content);
    echo "   ✅ Updated form validation\n";
} else {
    echo "   ⚠️  Form validation not found or already updated\n";
}

// 4. Write the updated content back to file
echo "4️⃣ Writing updated content to file...\n";

if (file_put_contents($file_path, $content)) {
    echo "   ✅ File updated successfully\n";
} else {
    echo "   ❌ Failed to write file\n";
    exit(1);
}

echo "\n📊 SUMMARY:\n";
echo "===========\n";
echo "✅ Removed 'All Users' option\n";
echo "✅ Removed 'By Role/Jabatan' option\n";
echo "✅ Simplified to 'Specific Users' only\n";
echo "✅ Updated PHP recipient handling\n";
echo "✅ Updated JavaScript validation\n";
echo "✅ Removed unnecessary logic\n";

echo "\n🎯 RESULT:\n";
echo "==========\n";
echo "Now the announcement system only has 'Pilih Pengguna Tertentu' option\n";
echo "which includes all customization features:\n";
echo "- Search by name/email/department\n";
echo "- Filter by department\n";
echo "- Filter by level\n";
echo "- Filter by jabatan\n";
echo "- Select all/deselect all\n";
echo "- Individual user selection\n";

echo "\n🚀 ANNOUNCEMENTS SIMPLIFIED SUCCESSFULLY!\n";
?>
