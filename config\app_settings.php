<?php
/**
 * Helper file untuk mendapatkan pengaturan aplikasi
 * File ini harus di-include setelah config.php
 */

// Fungsi untuk mendapatkan pengaturan aplikasi
if (!function_exists('get_app_settings')) {
    function get_app_settings() {
        global $conn;
        static $settings = null;
        static $last_check = 0;

        // Jika settings sudah di-cache dan belum lewat 5 detik, langsung kembalikan
        $now = time();
        if ($settings !== null && ($now - $last_check) < 5) {
            return $settings;
        }

        // Update waktu pengecekan terakhir
        $last_check = $now;

        // Jika koneksi database tersedia, ambil dari database
        if (isset($conn) && $conn instanceof mysqli && !$conn->connect_error) {
            try {
                $query = "SELECT * FROM settings WHERE id = 1";
                $result = $conn->query($query);

                if ($result && $result->num_rows > 0) {
                    $settings = $result->fetch_assoc();
                    return $settings;
                }
            } catch (Exception $e) {
                // Jika terjadi error, gunakan default
                error_log("Error getting app settings: " . $e->getMessage());
            }
        }

        // Default settings jika tidak bisa mengambil dari database
        return [
            'app_name' => 'Training Center',
            'company_logo' => 'asset/picture/logo-pas-with-text-removebg-preview.png',
            'timezone' => 'Asia/Jakarta'
        ];
    }
}

// Fungsi untuk mendapatkan nama aplikasi
if (!function_exists('get_app_name')) {
    function get_app_name() {
        $settings = get_app_settings();
        return $settings['app_name'] ?? 'Training Center';
    }
}

// Fungsi untuk mendapatkan logo perusahaan
if (!function_exists('get_company_logo')) {
    function get_company_logo() {
        $settings = get_app_settings();
        $default_logo = BASE_URL . 'asset/picture/logo-pas-with-text-removebg-preview.png';

        // Jika logo dari database, pastikan menggunakan BASE_URL
        if (isset($settings['company_logo'])) {
            // Jika logo dimulai dengan http:// atau https://, gunakan apa adanya
            if (preg_match('/^https?:\/\//', $settings['company_logo'])) {
                return $settings['company_logo'];
            }

            // Jika logo dimulai dengan ../ atau /, hapus dan tambahkan BASE_URL
            $logo_path = preg_replace('/^(\.\.\/|\/)+/', '', $settings['company_logo']);
            return BASE_URL . $logo_path;
        }

        return $default_logo;
    }
}

// Fungsi untuk mendapatkan timezone
if (!function_exists('get_timezone')) {
    function get_timezone() {
        $settings = get_app_settings();
        return $settings['timezone'] ?? 'Asia/Jakarta';
    }
}
