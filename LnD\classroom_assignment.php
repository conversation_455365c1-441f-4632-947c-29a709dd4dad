<?php
/**
 * Classroom Assignment Page for Pemohon (Applicant)
 * This page allows applicants to view and submit assignments
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['full_name'] ?? 'Pengguna';

// Check if assignment ID is provided
$assignment_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($assignment_id <= 0) {
    header('Location: classroom.php');
    exit();
}

// Initialize variables
$assignment = null;
$class = null;
$submission = null;
$success_message = '';
$error_message = '';

// Check if the classroom tables exist
$classroom_tables_exist = true;
$required_tables = [
    'training_assignments',
    'training_assignment_submissions',
    'training_classes'
];

foreach ($required_tables as $table) {
    $check_table_query = "SHOW TABLES LIKE '$table'";
    $table_result = $conn->query($check_table_query);
    if (!$table_result || $table_result->num_rows == 0) {
        $classroom_tables_exist = false;
        break;
    }
}

if (!$classroom_tables_exist) {
    $error_message = "Fitur classroom belum diaktifkan. Silakan hubungi administrator sistem.";
} else {
    // Check if the original_filename column exists in training_assignment_submissions
    $check_column_query = "SHOW COLUMNS FROM training_assignment_submissions LIKE 'original_filename'";
    $column_result = $conn->query($check_column_query);
    $original_filename_exists = ($column_result && $column_result->num_rows > 0);

    if (!$original_filename_exists) {
        // Add the column if it doesn't exist
        $add_column_query = "ALTER TABLE training_assignment_submissions
                           ADD COLUMN original_filename varchar(255) DEFAULT NULL AFTER file_path";
        $conn->query($add_column_query);
    }
    // Get assignment details
    $assignment_query = "SELECT a.*, c.title as class_title, c.id as class_id
                        FROM training_assignments a
                        JOIN training_classes c ON a.class_id = c.id
                        JOIN training_participants p ON c.id = p.class_id AND p.user_id = ?
                        WHERE a.id = ? AND a.is_published = 1";

    $stmt = $conn->prepare($assignment_query);
    $stmt->bind_param("ii", $user_id, $assignment_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $assignment = $result->fetch_assoc();
        $class_id = $assignment['class_id'];

        // Get class details
        $class_query = "SELECT c.*, t.training_topic
                       FROM training_classes c
                       JOIN training_submissions t ON c.training_id = t.id
                       WHERE c.id = ?";

        $stmt = $conn->prepare($class_query);
        $stmt->bind_param("i", $class_id);
        $stmt->execute();
        $class_result = $stmt->get_result();

        if ($class_result->num_rows > 0) {
            $class = $class_result->fetch_assoc();
        }

        // Check if user has already submitted this assignment
        $submission_query = "SELECT s.*, u.name as grader_name
                           FROM training_assignment_submissions s
                           LEFT JOIN users u ON s.graded_by = u.id
                           WHERE s.assignment_id = ? AND s.user_id = ?";

        $stmt = $conn->prepare($submission_query);
        $stmt->bind_param("ii", $assignment_id, $user_id);
        $stmt->execute();
        $submission_result = $stmt->get_result();

        if ($submission_result->num_rows > 0) {
            $submission = $submission_result->fetch_assoc();
        }

        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_assignment'])) {
            // Check if submission exists and is already graded
            if ($submission && $submission['status'] == 'graded') {
                $error_message = "Tugas yang sudah dinilai tidak dapat diedit.";
            } else {
                $submission_text = trim($_POST['submission_text']);
                $file_path = '';
                $original_filename = '';

                // Handle file upload if provided
                if (isset($_FILES['submission_file']) && $_FILES['submission_file']['error'] == 0) {
                    $upload_dir = '../uploads/assignments/';

                    // Create directory if it doesn't exist
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0777, true);
                    }

                    $original_filename = basename($_FILES['submission_file']['name']);
                    $file_extension = strtolower(pathinfo($original_filename, PATHINFO_EXTENSION));

                    // Define allowed file extensions
                    $allowed_file_types = $assignment['allowed_file_types'] ?? '';

                    // If no allowed file types specified, use default common types
                    if (empty($allowed_file_types) || $allowed_file_types == '0') {
                        $allowed_file_types = 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif,zip,rar';
                    }

                    $allowed_extensions = array_map('trim', explode(',', $allowed_file_types));

                    // Check if file extension is allowed
                    if (!in_array($file_extension, $allowed_extensions)) {
                        $error_message = "Tipe file tidak diizinkan. Tipe file yang diizinkan: " . $allowed_file_types;
                    }
                    // Check file size
                    else if ($_FILES['submission_file']['size'] > $assignment['max_file_size']) {
                        $error_message = "Ukuran file terlalu besar. Maksimal " . floor($assignment['max_file_size'] / (1024 * 1024)) . "MB.";
                    } else {
                        $file_name = time() . '_' . $original_filename;
                        $target_file = $upload_dir . $file_name;

                        // Move uploaded file
                        if (move_uploaded_file($_FILES['submission_file']['tmp_name'], $target_file)) {
                            $file_path = $file_name;
                        } else {
                            $error_message = "Gagal mengunggah file.";
                        }
                    }
                }

                if (empty($error_message)) {
                    if ($submission) {
                        // Update existing submission
                        $update_query = "UPDATE training_assignment_submissions
                                       SET submission_text = ?, file_path = ?, original_filename = ?, status = 'submitted', submitted_at = NOW()
                                       WHERE id = ?";

                        $stmt = $conn->prepare($update_query);

                        // If no new file was uploaded, keep the existing file
                        if (empty($file_path) && !empty($submission['file_path'])) {
                            $file_path = $submission['file_path'];
                            $original_filename = $submission['original_filename'] ?? '';
                        }

                        $stmt->bind_param("sssi", $submission_text, $file_path, $original_filename, $submission['id']);

                        if ($stmt->execute()) {
                            $success_message = "Tugas berhasil diperbarui.";

                            // Refresh submission data with grader info
                            $stmt = $conn->prepare($submission_query);
                            $stmt->bind_param("ii", $assignment_id, $user_id);
                            $stmt->execute();
                            $submission_result = $stmt->get_result();
                            $submission = $submission_result->fetch_assoc();
                        } else {
                            $error_message = "Gagal memperbarui tugas: " . $conn->error;
                        }
                    } else {
                        // Create new submission
                        $insert_query = "INSERT INTO training_assignment_submissions
                                       (assignment_id, user_id, submission_text, file_path, original_filename, status)
                                       VALUES (?, ?, ?, ?, ?, 'submitted')";

                        $stmt = $conn->prepare($insert_query);
                        $stmt->bind_param("iisss", $assignment_id, $user_id, $submission_text, $file_path, $original_filename);

                        if ($stmt->execute()) {
                            $success_message = "Tugas berhasil dikumpulkan.";

                            // Get the new submission with grader info
                            $stmt = $conn->prepare($submission_query);
                            $stmt->bind_param("ii", $assignment_id, $user_id);
                            $stmt->execute();
                            $submission_result = $stmt->get_result();
                            $submission = $submission_result->fetch_assoc();
                        } else {
                            $error_message = "Gagal mengumpulkan tugas: " . $conn->error;
                        }
                    }
                }
            }
        }
    } else {
        $error_message = "Tugas tidak ditemukan atau Anda tidak memiliki akses.";
    }

    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
:root {
    --primary-color: rgb(157, 0, 0);
    --primary-color-dark: rgb(127, 0, 0);
    --primary-color-light: rgba(157, 0, 0, 0.1);
    --secondary-color: #2c3e50;
    --success-color: #4CAF50;
    --success-color-light: #e8f5e9;
    --warning-color: #ffc107;
    --danger-color: #f44336;
    --info-color: #2196F3;
    --text-dark: #333;
    --text-light: #757575;
    --white: #ffffff;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
    --shadow-lg: 0 6px 15px rgba(0,0,0,0.1);
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
}

.jarak {
    height: 80px;
}

.assignment-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.assignment-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--white);
    padding: var(--spacing-xl) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.assignment-header h1 {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    font-weight: 700;
}

.assignment-header .class-info {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: var(--spacing-sm);
}

.assignment-card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.assignment-card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
}

.assignment-card-body {
    padding: var(--spacing-lg);
}

.assignment-details {
    margin-bottom: var(--spacing-lg);
}

.assignment-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    color: var(--text-dark);
}

.assignment-meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.assignment-instructions {
    background-color: #f8f9fa;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-lg);
}

.submission-form {
    background-color: #f8f9fa;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
}

.submission-status {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-lg);
}

.submission-status.submitted {
    background-color: var(--success-color-light);
    color: var(--success-color);
}

.submission-status.graded {
    background-color: var(--info-color);
    color: var(--white);
}

.submission-status.returned {
    background-color: var(--warning-color);
    color: var(--text-dark);
}

.submission-details {
    background-color: #f8f9fa;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
}

.submission-text {
    background-color: var(--white);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-md);
    border: 1px solid #dee2e6;
}

.submission-file {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background-color: var(--white);
    border-radius: var(--border-radius-sm);
    border: 1px solid #dee2e6;
}

.submission-file i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.feedback-section {
    background-color: #f8f9fa;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
}

.grade-display {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: var(--spacing-md);
}

.feedback-text {
    background-color: var(--white);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    border: 1px solid #dee2e6;
}

.grader-info {
    background-color: #f8f9fa;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    border-left: 4px solid var(--primary-color);
}

.grader-info .text-muted {
    color: var(--text-light) !important;
}

.grader-info i {
    color: var(--primary-color);
    margin-right: 5px;
}

@media (max-width: 768px) {
    .assignment-meta {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="assignment-container">
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $error_message ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $success_message ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if ($assignment): ?>
        <div class="welcome-section">
            <h1><?= htmlspecialchars($assignment['title']) ?></h1>
            <div class="class-info">
                <i class="fas fa-chalkboard"></i> <?= htmlspecialchars($assignment['class_title']) ?>
            </div>
        </div>

        <div class="assignment-card">
            <div class="assignment-card-header">
                <h2>Detail Tugas</h2>
            </div>
            <div class="assignment-card-body">
                <div class="assignment-details">
                    <div class="assignment-meta">
                        <div class="assignment-meta-item">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Tenggat: <?= date('d M Y H:i', strtotime($assignment['due_date'])) ?></span>
                        </div>
                        <?php if ($assignment['points']): ?>
                            <div class="assignment-meta-item">
                                <i class="fas fa-star"></i>
                                <span>Poin: <?= $assignment['points'] ?></span>
                            </div>
                        <?php endif; ?>
                        <div class="assignment-meta-item">
                            <i class="fas fa-clock"></i>
                            <span>Status:
                                <?php
                                    if ($submission) {
                                        echo $submission['status'] == 'submitted' ? 'Dikumpulkan' :
                                            ($submission['status'] == 'graded' ? 'Dinilai' : 'Dikembalikan');
                                    } else {
                                        echo 'Belum Dikumpulkan';
                                    }
                                ?>
                            </span>
                        </div>
                    </div>

                    <div class="assignment-description">
                        <?= !empty($assignment['description']) ? nl2br(htmlspecialchars($assignment['description'])) : 'Tidak ada deskripsi' ?>
                    </div>

                    <?php if (!empty($assignment['instructions'])): ?>
                        <h4 class="mt-4 mb-2">Instruksi</h4>
                        <div class="assignment-instructions">
                            <?= nl2br(htmlspecialchars($assignment['instructions'])) ?>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if ($submission): ?>
                    <div class="submission-status <?= $submission['status'] ?>">
                        <h4>
                            <?php
                                if ($submission['status'] == 'submitted') {
                                    echo '<i class="fas fa-check-circle"></i> Tugas Telah Dikumpulkan';
                                } elseif ($submission['status'] == 'graded') {
                                    echo '<i class="fas fa-award"></i> Tugas Telah Dinilai';
                                } else {
                                    echo '<i class="fas fa-undo"></i> Tugas Dikembalikan';
                                }
                            ?>
                        </h4>
                        <p>Dikumpulkan pada: <?= date('d M Y H:i', strtotime($submission['submitted_at'])) ?></p>
                    </div>

                    <div class="submission-details">
                        <h4 class="mb-3">Jawaban Anda</h4>

                        <?php if (!empty($submission['submission_text'])): ?>
                            <div class="submission-text">
                                <?= nl2br(htmlspecialchars($submission['submission_text'])) ?>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($submission['file_path'])): ?>
                            <h5 class="mt-3 mb-2">File Lampiran</h5>
                            <div class="submission-file">
                                <i class="fas fa-file"></i>
                                <a href="../download.php?type=assignment&id=<?= $submission['id'] ?>" target="_blank">
                                    <?php
                                    if (!empty($submission['original_filename'])) {
                                        echo htmlspecialchars($submission['original_filename']);
                                    } elseif (strpos($submission['file_path'], '_') !== false) {
                                        echo htmlspecialchars(substr($submission['file_path'], strpos($submission['file_path'], '_') + 1));
                                    } else {
                                        echo 'File Lampiran';
                                    }
                                    ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if ($submission['status'] == 'graded' && $submission['grade'] !== null): ?>
                        <div class="feedback-section">
                            <h4 class="mb-3">Penilaian</h4>

                            <div class="grade-display">
                                <?= $submission['grade'] ?> / <?= $assignment['points'] ?? 100 ?>
                            </div>

                            <!-- Informasi Penilai -->
                            <div class="grader-info mb-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted" style="color:gray !important;">
                                            <i class="fas fa-user-check"></i>
                                            <strong>Dinilai oleh:</strong>
                                            <?= !empty($submission['grader_name']) ? htmlspecialchars($submission['grader_name']) : 'Sistem' ?>
                                        </small>
                                    </div>
                                    <?php if (!empty($submission['graded_at'])): ?>
                                    <div class="col-md-6">
                                        <small class="text-muted" style="color:gray !important;">
                                            <i class="fas fa-clock"></i>
                                            <strong>Tanggal penilaian:</strong>
                                            <?= date('d M Y H:i', strtotime($submission['graded_at'])) ?>
                                        </small>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php if (!empty($submission['feedback'])): ?>
                                <h5 class="mt-3 mb-2">Umpan Balik</h5>
                                <div class="feedback-text">
                                    <?= nl2br(htmlspecialchars($submission['feedback'])) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (strtotime($assignment['due_date']) > time() && $submission['status'] != 'graded'): ?>
                        <div class="mt-4">
                            <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#editSubmissionForm">
                                <i class="fas fa-edit"></i> Edit Jawaban
                            </button>
                        </div>

                        <div class="collapse mt-3" id="editSubmissionForm">
                            <form method="post" enctype="multipart/form-data" class="submission-form">
                                <h4 class="mb-3">Edit Jawaban</h4>

                                <div class="mb-3">
                                    <label for="submission_text" class="form-label">Jawaban Anda</label>
                                    <textarea class="form-control" id="submission_text" name="submission_text" rows="6"><?= htmlspecialchars($submission['submission_text'] ?? '') ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="submission_file" class="form-label">File Lampiran (Opsional)</label>
                                    <input class="form-control" type="file" id="submission_file" name="submission_file">
                                    <div class="form-text">Format file: <?= !empty($assignment['allowed_file_types']) && $assignment['allowed_file_types'] != '0' ? $assignment['allowed_file_types'] : 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif,zip,rar' ?>. Maksimal <?= floor($assignment['max_file_size'] / (1024 * 1024)) ?>MB.</div>

                                    <?php if (!empty($submission['file_path'])): ?>
                                        <div class="mt-2">
                                            <small>File saat ini:
                                                <a href="../download.php?type=assignment&id=<?= $submission['id'] ?>" target="_blank">
                                                    <?php
                                                    if (!empty($submission['original_filename'])) {
                                                        echo htmlspecialchars($submission['original_filename']);
                                                    } elseif (strpos($submission['file_path'], '_') !== false) {
                                                        echo htmlspecialchars(substr($submission['file_path'], strpos($submission['file_path'], '_') + 1));
                                                    } else {
                                                        echo 'File Lampiran';
                                                    }
                                                    ?>
                                                </a>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <button type="submit" name="submit_assignment" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Perbarui Jawaban
                                </button>
                            </form>
                        </div>
                    <?php elseif ($submission['status'] == 'graded'): ?>
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> Tugas yang sudah dinilai tidak dapat diedit.
                            </div>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <?php if (strtotime($assignment['due_date']) > time()): ?>
                        <form method="post" enctype="multipart/form-data" class="submission-form">
                            <h4 class="mb-3">Kumpulkan Jawaban</h4>

                            <div class="mb-3">
                                <label for="submission_text" class="form-label">Jawaban Anda</label>
                                <textarea class="form-control" id="submission_text" name="submission_text" rows="6" required></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="submission_file" class="form-label">File Lampiran (Opsional)</label>
                                <input class="form-control" type="file" id="submission_file" name="submission_file">
                                <div class="form-text">Format file: <?= !empty($assignment['allowed_file_types']) && $assignment['allowed_file_types'] != '0' ? $assignment['allowed_file_types'] : 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif,zip,rar' ?>. Maksimal <?= floor($assignment['max_file_size'] / (1024 * 1024)) ?>MB.</div>
                            </div>

                            <button type="submit" name="submit_assignment" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Kumpulkan Tugas
                            </button>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> Batas waktu pengumpulan tugas telah berakhir.
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>

        <div class="d-flex justify-content-between mt-4">
            <a href="classroom_detail.php?id=<?= $assignment['class_id'] ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali ke Kelas
            </a>
        </div>
    <?php else: ?>
        <div class="alert alert-danger">
            <h4><i class="fas fa-exclamation-triangle"></i> Tugas Tidak Ditemukan</h4>
            <p>Tugas yang Anda cari tidak ditemukan atau Anda tidak memiliki akses.</p>
            <a href="classroom.php" class="btn btn-primary mt-3">Kembali ke Classroom</a>
        </div>
    <?php endif; ?>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
