<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi variabel
$success_message = "";
$error_message = "";
$results = [];
$template_file = "user_template.xlsx";

// Proses form submit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Cek apakah file diupload
    if (isset($_FILES['excel_file']) && $_FILES['excel_file']['error'] == 0) {
        $file_name = $_FILES['excel_file']['name'];
        $file_tmp = $_FILES['excel_file']['tmp_name'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        // Cek ekstensi file
        if ($file_ext == 'xlsx' || $file_ext == 'xls') {
            // Buat direktori uploads jika belum ada
            $upload_dir = '../uploads/excel/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $upload_file = $upload_dir . basename($file_name);

            // Upload file
            if (move_uploaded_file($file_tmp, $upload_file)) {
                // Load library PhpSpreadsheet
                require '../vendor/autoload.php';

                try {
                    // Baca file Excel
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($upload_file);
                    $reader->setReadDataOnly(true);
                    $spreadsheet = $reader->load($upload_file);
                    $worksheet = $spreadsheet->getActiveSheet();
                    $rows = $worksheet->toArray();

                    // Hapus header (baris pertama)
                    $header = array_shift($rows);

                    // Validasi header
                    $required_columns = ['nama', 'nik', 'email', 'role_id', 'dept', 'bagian', 'jabatan'];
                    $missing_columns = [];

                    foreach ($required_columns as $column) {
                        if (!in_array($column, $header)) {
                            $missing_columns[] = $column;
                        }
                    }

                    if (!empty($missing_columns)) {
                        $error_message = "Kolom yang diperlukan tidak ditemukan: " . implode(', ', $missing_columns);
                    } else {
                        // Mapping kolom
                        $column_indexes = [];
                        foreach ($header as $index => $column_name) {
                            $column_indexes[strtolower($column_name)] = $index;
                        }

                        // Mulai transaksi database
                        $conn->begin_transaction();

                        $success_count = 0;
                        $error_count = 0;

                        // Proses setiap baris
                        foreach ($rows as $row_index => $row) {
                            $row_num = $row_index + 2; // +2 karena baris 1 adalah header

                            // Skip baris kosong
                            if (empty(array_filter($row))) {
                                continue;
                            }

                            // Ambil data dari baris
                            $name = trim($row[$column_indexes['nama']]);
                            $nik = trim($row[$column_indexes['nik']]);
                            $email = trim($row[$column_indexes['email']]);
                            $role_id = (int)$row[$column_indexes['role_id']];
                            $dept = trim($row[$column_indexes['dept']]);
                            $bagian = trim($row[$column_indexes['bagian']]);
                            $jabatan = trim($row[$column_indexes['jabatan']]);

                            // Validasi data
                            $validation_errors = [];

                            if (empty($name)) $validation_errors[] = "Nama kosong";
                            if (empty($nik)) $validation_errors[] = "NIK kosong";
                            if (empty($email)) $validation_errors[] = "Email kosong";
                            if (empty($role_id)) $validation_errors[] = "Role ID kosong";
                            if (empty($dept)) $validation_errors[] = "Departemen kosong";
                            if (empty($bagian)) $validation_errors[] = "Bagian kosong";
                            if (empty($jabatan)) $validation_errors[] = "Jabatan kosong";

                            // Cek apakah email, NIK, atau nama sudah digunakan
                            $checkQuery = "SELECT * FROM users WHERE email = ? OR nik = ? OR name = ?";
                            $stmtCheck = $conn->prepare($checkQuery);
                            $stmtCheck->bind_param("sss", $email, $nik, $name);
                            $stmtCheck->execute();
                            $resultCheck = $stmtCheck->get_result();

                            if ($resultCheck->num_rows > 0) {
                                $row_data = $resultCheck->fetch_assoc();
                                if ($row_data['email'] == $email) {
                                    $validation_errors[] = "Email sudah digunakan";
                                }
                                if ($row_data['nik'] == $nik) {
                                    $validation_errors[] = "NIK sudah digunakan";
                                }
                                if ($row_data['name'] == $name) {
                                    $validation_errors[] = "Nama sudah digunakan";
                                }
                            }

                            // Jika ada error validasi, skip baris ini
                            if (!empty($validation_errors)) {
                                $results[] = [
                                    'row' => $row_num,
                                    'status' => 'error',
                                    'message' => implode(', ', $validation_errors),
                                    'data' => [
                                        'name' => $name,
                                        'nik' => $nik,
                                        'email' => $email,
                                        'role_id' => $role_id,
                                        'dept' => $dept,
                                        'bagian' => $bagian,
                                        'jabatan' => $jabatan
                                    ]
                                ];
                                $error_count++;
                                continue;
                            }

                            // Tambahkan pengguna baru
                            $query = "INSERT INTO users (name, email, nik, role_id, dept, bagian, jabatan, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, 1)";
                            $stmt = $conn->prepare($query);
                            $stmt->bind_param("sssisss", $name, $email, $nik, $role_id, $dept, $bagian, $jabatan);

                            if ($stmt->execute()) {
                                $user_id = $conn->insert_id;

                                // Tambahkan departemen default
                                $query = "INSERT INTO user_departments (user_id, dept) VALUES (?, ?)";
                                $stmt = $conn->prepare($query);
                                $stmt->bind_param("is", $user_id, $dept);
                                $stmt->execute();

                                // Log aktivitas
                                if (file_exists('../config/activity_logger.php')) {
                                    include_once '../config/activity_logger.php';
                                    if (function_exists('log_activity')) {
                                        $role_names = [
                                            1 => 'Pemohon',
                                            2 => 'Dept Head',
                                            3 => 'LnD',
                                            4 => 'FM',
                                            5 => 'Direktur',
                                            6 => 'HRGA',
                                            99 => 'Admin'
                                        ];
                                        $role_name = $role_names[$role_id] ?? 'Unknown';

                                        log_activity($_SESSION['user_id'], "Menambahkan pengguna baru (bulk): {$name} (NIK: {$nik}) dengan role {$role_name}", "user", [
                                            'user_id' => $user_id,
                                            'name' => $name,
                                            'nik' => $nik,
                                            'role_id' => $role_id,
                                            'dept' => $dept,
                                            'bagian' => $bagian,
                                            'jabatan' => $jabatan
                                        ]);
                                    }
                                }

                                $results[] = [
                                    'row' => $row_num,
                                    'status' => 'success',
                                    'message' => 'Berhasil ditambahkan',
                                    'data' => [
                                        'name' => $name,
                                        'nik' => $nik,
                                        'email' => $email,
                                        'role_id' => $role_id,
                                        'dept' => $dept,
                                        'bagian' => $bagian,
                                        'jabatan' => $jabatan
                                    ]
                                ];
                                $success_count++;
                            } else {
                                $results[] = [
                                    'row' => $row_num,
                                    'status' => 'error',
                                    'message' => 'Gagal menambahkan: ' . $stmt->error,
                                    'data' => [
                                        'name' => $name,
                                        'nik' => $nik,
                                        'email' => $email,
                                        'role_id' => $role_id,
                                        'dept' => $dept,
                                        'bagian' => $bagian,
                                        'jabatan' => $jabatan
                                    ]
                                ];
                                $error_count++;
                            }
                        }

                        // Commit transaksi jika berhasil
                        $conn->commit();

                        $success_message = "Import selesai. Berhasil: $success_count, Gagal: $error_count";
                    }
                } catch (Exception $e) {
                    // Rollback transaksi jika terjadi error
                    try {
                        $conn->rollback();
                    } catch (Exception $rollbackEx) {
                        // Ignore rollback error
                    }
                    $error_message = "Error: " . $e->getMessage();
                }

                // Hapus file upload
                unlink($upload_file);
            } else {
                $error_message = "Gagal mengupload file.";
            }
        } else {
            $error_message = "Format file tidak didukung. Harap upload file Excel (.xlsx atau .xls).";
        }
    } else {
        $error_message = "Harap pilih file untuk diupload.";
    }
}

// Query untuk mengambil daftar role
$query = "SELECT * FROM roles";
$result = $conn->query($query);
$roles = [];
while ($row = $result->fetch_assoc()) {
    $roles[$row['id']] = $row['role_name'];
}
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    html, body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 0;
        color: #333;
        height: 100%;
    }

    body {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
    }

    .content-wrapper {
        flex: 1;
        width: 100%;
        display: flex;
        flex-direction: column;
    }

    .container-form {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 150px 20px 40px 20px; /* Increased top padding to prevent navbar overlap */
        width: 100%;
    }

    .form-container {
        background: #fff;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        width: 100%;
        max-width: 800px;
        transition: all 0.3s ease;
    }

    .form-container:hover {
        box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
    }

    h2 {
        text-align: center;
        color: #BF0000;
        margin-bottom: 25px;
        font-weight: 600;
        position: relative;
        padding-bottom: 10px;
    }

    h2:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background-color: #BF0000;
        border-radius: 3px;
    }

    h3 {
        color: #BF0000;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .form-group {
        margin-bottom: 20px;
    }

    label {
        display: block;
        margin: 12px 0 8px;
        font-weight: 500;
        color: #444;
        font-size: 15px;
    }

    input, select {
        width: 100%;
        padding: 12px 15px;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-sizing: border-box;
        font-size: 15px;
        transition: all 0.3s ease;
        background-color: #f9f9f9;
    }

    input:focus, select:focus {
        outline: none;
        border-color: #BF0000;
        box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
        background-color: #fff;
    }

    button {
        width: 100%;
        padding: 14px;
        background-color: #BF0000;
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-top: 10px;
        box-shadow: 0 4px 6px rgba(191, 0, 0, 0.1);
    }

    button:hover {
        background-color: #a00000;
        transform: translateY(-2px);
        box-shadow: 0 6px 8px rgba(191, 0, 0, 0.15);
    }

    button:active {
        transform: translateY(0);
    }

    .success-message {
        color: #2e7d32;
        font-weight: 500;
        text-align: center;
        margin-bottom: 20px;
        padding: 10px;
        background-color: #e8f5e9;
        border-radius: 8px;
        border-left: 4px solid #2e7d32;
    }

    .error {
        color: #d32f2f;
        font-weight: 500;
        text-align: center;
        margin-bottom: 20px;
        padding: 10px;
        background-color: #ffebee;
        border-radius: 8px;
        border-left: 4px solid #d32f2f;
    }

    .back-link {
        display: block;
        text-align: center;
        margin-top: 20px;
        color: #BF0000;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .back-link:hover {
        color: #a00000;
        text-decoration: underline;
    }

    .info-text {
        display: block;
        font-size: 13px;
        color: #666;
        margin-top: 5px;
        font-style: italic;
    }

    .results-container {
        margin-top: 30px;
        border-top: 1px solid #ddd;
        padding-top: 20px;
    }

    .results-container table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
    }

    .results-container th, .results-container td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }

    .results-container th {
        background-color: #f2f2f2;
        font-weight: 600;
    }

    .results-container tr.success {
        background-color: #e8f5e9;
    }

    .results-container tr.error {
        background-color: #ffebee;
    }

    .jarak {
        height: 100px;
    }
</style>
<body>
<?php include '../config/navbarb.php'; ?>
<div class="jarak"></div>
<div class="content-wrapper">
    <div class="container-form">
        <div class="form-container">
            <h2>Bulk Create Users</h2>

            <?php if (!empty($success_message)) { ?>
                <div class="success-message">
                    <p><?php echo $success_message; ?></p>
                </div>
            <?php } ?>

            <?php if (!empty($error_message)) { ?>
                <div class="error">
                    <p><?php echo $error_message; ?></p>
                </div>
            <?php } ?>

            <form method="POST" action="bulk_create_users.php" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="excel_file">Upload File Excel</label>
                    <input type="file" name="excel_file" id="excel_file" accept=".xlsx, .xls" required>
                    <small class="info-text">Upload file Excel dengan format yang sesuai. <a href="download_template.php">Download template</a></small>
                </div>

                <button type="submit">Import Users</button>
            </form>

            <?php if (!empty($results)) { ?>
                <div class="results-container" style="margin-top: 30px;">
                    <h3>Hasil Import</h3>
                    <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                        <thead>
                            <tr>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Baris</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Nama</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">NIK</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Email</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Role</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Status</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Pesan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $result) { ?>
                                <tr style="background-color: <?php echo $result['status'] == 'success' ? '#e8f5e9' : '#ffebee'; ?>">
                                    <td style="border: 1px solid #ddd; padding: 8px;"><?php echo $result['row']; ?></td>
                                    <td style="border: 1px solid #ddd; padding: 8px;"><?php echo $result['data']['name']; ?></td>
                                    <td style="border: 1px solid #ddd; padding: 8px;"><?php echo $result['data']['nik']; ?></td>
                                    <td style="border: 1px solid #ddd; padding: 8px;"><?php echo $result['data']['email']; ?></td>
                                    <td style="border: 1px solid #ddd; padding: 8px;"><?php echo $roles[$result['data']['role_id']] ?? 'Unknown'; ?></td>
                                    <td style="border: 1px solid #ddd; padding: 8px;"><?php echo $result['status'] == 'success' ? 'Berhasil' : 'Gagal'; ?></td>
                                    <td style="border: 1px solid #ddd; padding: 8px;"><?php echo $result['message']; ?></td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            <?php } ?>

            <a href="index.php" class="back-link">Back to List</a>
        </div>
    </div>
</div>
<?php include '../config/footer.php'; ?>
</body>
</html>
