<?php
session_start();
include '../config/config.php';
include '../config/access_control.php';

// Check if user is logged in and has admin access
if (!isset($_SESSION['user_id'])) {
    header("Location: ../view/login.php");
    exit();
}
// Handle test email sending
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_email'])) {
    $training_id = (int)$_POST['training_id'];
    $action = $_POST['action'];
    $test_email = $_POST['test_email'];
    $comments = $_POST['comments'] ?? '';
    $assignment = $_POST['assignment'] ?? '';

    if ($training_id && $action && $test_email) {
        require_once '../config/director_approval_email_helper.php';

        // Get training details first
        $query = "SELECT ts.*, u.name as requester_name, u.email as requester_email,
                         k.nama as requester_nama, k.dept as requester_dept
                  FROM training_submissions ts
                  LEFT JOIN users u ON ts.user_id = u.id
                  LEFT JOIN karyawan k ON u.nik = k.nik
                  WHERE ts.id = ?";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($training = $result->fetch_assoc()) {
            // Test email sending
            if ($action === 'approved') {
                $email_result = sendDirectorApprovalNotification($training_id, $conn, $comments, $assignment);
            } else {
                $email_result = sendDirectorRejectionNotification($training_id, $conn, $comments);
            }

            if ($email_result['success']) {
                $_SESSION['test_success'] = "Test email berhasil dikirim ke: " . implode(', ', array_column($email_result['results'], 'recipient'));
            } else {
                $_SESSION['test_error'] = "Test email gagal: " . $email_result['message'];
            }
        } else {
            $_SESSION['test_error'] = "Training ID tidak ditemukan";
        }
        $stmt->close();
    }
}

// Get sample training data for testing
$sample_query = "SELECT ts.id, ts.training_topic, ts.status, ts.created_at,
                        u.name as requester_name, k.dept as requester_dept,
                        ts.approved_dept_head, ts.approved_hrd, ts.approved_ga, ts.approved_fm
                 FROM training_submissions ts
                 LEFT JOIN users u ON ts.user_id = u.id
                 LEFT JOIN karyawan k ON u.nik = k.nik
                 WHERE ts.approved_fm = 'Approved'
                 ORDER BY ts.created_at DESC LIMIT 10";

$sample_result = $conn->query($sample_query);
$sample_trainings = $sample_result->fetch_all(MYSQLI_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    .test-container {
        max-width: 1000px;
        margin: 20px auto;
        padding: 20px;
    }

    .test-header {
        background: linear-gradient(135deg, #BF0000 0%, #8B0000 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 30px;
    }

    .test-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }

    .card-header {
        background: #f8f9fa;
        padding: 20px;
        border-bottom: 1px solid #eee;
        font-weight: 600;
        color: #333;
    }

    .card-content {
        padding: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
        color: #555;
    }

    .form-control {
        width: 100%;
        padding: 12px;
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #BF0000;
        box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
    }

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: #BF0000;
        color: white;
    }

    .btn-primary:hover {
        background: #8B0000;
        transform: translateY(-2px);
    }

    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .alert-success {
        background: #d4edda;
        color: #155724;
        border-left: 4px solid #28a745;
    }

    .alert-error {
        background: #f8d7da;
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    .training-list {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 8px;
    }

    .training-item {
        padding: 15px;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .training-item:hover {
        background-color: #f8f9fa;
    }

    .training-item:last-child {
        border-bottom: none;
    }

    .training-item.selected {
        background-color: #ffe6e6;
        border-left: 4px solid #BF0000;
    }

    .info-box {
        background: #e3f2fd;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #2196f3;
        margin-bottom: 20px;
    }

    .info-box h4 {
        margin-top: 0;
        color: #1976d2;
    }

    .approval-status {
        display: flex;
        gap: 10px;
        margin-top: 5px;
    }

    .status-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
    }

    .status-approved {
        background: #d4edda;
        color: #155724;
    }

    .status-pending {
        background: #fff3cd;
        color: #856404;
    }
</style>

<body>
    <?php include '../config/navbara.php'; ?>
    <div class="jarak-form"></div>

    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-crown"></i> Test Email Approval Direktur</h1>
            <p>Test sistem email notification untuk approval training eksternal oleh Direktur</p>
        </div>

        <?php if (isset($_SESSION['test_success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($_SESSION['test_success']) ?>
            </div>
            <?php unset($_SESSION['test_success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['test_error'])): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($_SESSION['test_error']) ?>
            </div>
            <?php unset($_SESSION['test_error']); ?>
        <?php endif; ?>

        <div class="info-box">
            <h4><i class="fas fa-info-circle"></i> Informasi Test Email Direktur</h4>
            <p>Gunakan halaman ini untuk menguji sistem email notification approval Direktur untuk training eksternal.</p>
            <ul>
                <li><strong>approved:</strong> Email ke pemohon dan department head saat training disetujui Direktur</li>
                <li><strong>rejected:</strong> Email ke pemohon saat training ditolak Direktur</li>
            </ul>
            <p><strong>Catatan:</strong> Hanya training yang sudah disetujui Factory Manager yang bisa ditest untuk approval Direktur.</p>
        </div>

        <div class="test-card">
            <div class="card-header">
                <i class="fas fa-paper-plane"></i> Test Email Notification Direktur
            </div>
            <div class="card-content">
                <form method="POST">
                    <div class="form-group">
                        <label class="form-label">Pilih Training untuk Test:</label>
                        <div class="training-list">
                            <?php if (empty($sample_trainings)): ?>
                                <div style="padding: 20px; text-align: center; color: #666;">
                                    Tidak ada training yang sudah disetujui Factory Manager
                                </div>
                            <?php else: ?>
                                <?php foreach ($sample_trainings as $training): ?>
                                    <div class="training-item" onclick="selectTraining(<?= $training['id'] ?>)">
                                        <strong>ID #<?= $training['id'] ?> - <?= htmlspecialchars($training['training_topic']) ?></strong><br>
                                        <small>
                                            Pemohon: <?= htmlspecialchars($training['requester_name']) ?> |
                                            Dept: <?= htmlspecialchars($training['requester_dept']) ?> |
                                            Status: <?= htmlspecialchars($training['status']) ?> |
                                            Tanggal: <?= date('d M Y H:i', strtotime($training['created_at'])) ?>
                                        </small>
                                        <div class="approval-status">
                                            <span class="status-badge <?= $training['approved_dept_head'] === 'Approved' ? 'status-approved' : 'status-pending' ?>">
                                                Dept Head: <?= $training['approved_dept_head'] ?? 'Pending' ?>
                                            </span>
                                            <span class="status-badge <?= $training['approved_hrd'] === 'Approved' ? 'status-approved' : 'status-pending' ?>">
                                                L&D: <?= $training['approved_hrd'] ?? 'Pending' ?>
                                            </span>
                                            <span class="status-badge <?= $training['approved_ga'] === 'Approved' ? 'status-approved' : 'status-pending' ?>">
                                                HRGA: <?= $training['approved_ga'] ?? 'Pending' ?>
                                            </span>
                                            <span class="status-badge <?= $training['approved_fm'] === 'Approved' ? 'status-approved' : 'status-pending' ?>">
                                                Factory: <?= $training['approved_fm'] ?? 'Pending' ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                        <input type="hidden" name="training_id" id="selected_training_id" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Action Type:</label>
                        <select name="action" class="form-control" required>
                            <option value="">Pilih Action</option>
                            <option value="approved">Approved by Director (ke Pemohon & Dept Head)</option>
                            <option value="rejected">Rejected by Director (ke Pemohon)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Test Email Address:</label>
                        <input type="email" name="test_email" class="form-control"
                               placeholder="<EMAIL>" required>
                        <small style="color: #666; font-size: 12px;">
                            Email akan dikirim ke alamat ini untuk testing (menggantikan penerima asli)
                        </small>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Comments Direktur (Opsional):</label>
                        <textarea name="comments" class="form-control" rows="3"
                                  placeholder="Komentar dari Direktur..."></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Assignment/Penugasan (Opsional):</label>
                        <textarea name="assignment" class="form-control" rows="3"
                                  placeholder="Penugasan khusus dari Direktur..."></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Kirim Test Email
                    </button>
                </form>
            </div>
        </div>

        <div class="test-card">
            <div class="card-header">
                <i class="fas fa-cogs"></i> Email Configuration Status
            </div>
            <div class="card-content">
                <?php
                // Check email configuration
                $settings_query = "SELECT smtp_server, smtp_port, sender_email, sender_name FROM settings WHERE id = 1";
                $settings_result = $conn->query($settings_query);
                $settings = $settings_result->fetch_assoc();
                ?>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <strong>SMTP Server:</strong><br>
                        <?= htmlspecialchars($settings['smtp_server'] ?? 'Not configured') ?>
                    </div>
                    <div>
                        <strong>SMTP Port:</strong><br>
                        <?= htmlspecialchars($settings['smtp_port'] ?? 'Not configured') ?>
                    </div>
                    <div>
                        <strong>Sender Email:</strong><br>
                        <?= htmlspecialchars($settings['sender_email'] ?? 'Not configured') ?>
                    </div>
                    <div>
                        <strong>Sender Name:</strong><br>
                        <?= htmlspecialchars($settings['sender_name'] ?? 'Not configured') ?>
                    </div>
                </div>

                <?php if (!$settings || empty($settings['smtp_server'])): ?>
                    <div class="alert alert-error" style="margin-top: 20px;">
                        <i class="fas fa-exclamation-triangle"></i>
                        Email configuration belum lengkap. Silakan konfigurasi SMTP settings terlebih dahulu.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function selectTraining(trainingId) {
            // Remove previous selection
            document.querySelectorAll('.training-item').forEach(item => {
                item.classList.remove('selected');
            });

            // Add selection to clicked item
            event.target.closest('.training-item').classList.add('selected');

            // Set hidden input value
            document.getElementById('selected_training_id').value = trainingId;
        }
    </script>

    <footer>
        <?php include '../config/footer.php'; ?>
    </footer>
</body>
</html>
