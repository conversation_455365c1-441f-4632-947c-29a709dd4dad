# Allow access only through PHP scripts
Order deny,allow
<PERSON>y from all
Allow from 127.0.0.1

# Prevent directory listing
Options -Indexes

# Disable PHP execution in this directory
<IfModule mod_php5.c>
    php_flag engine off
</IfModule>
<IfModule mod_php7.c>
    php_flag engine off
</IfModule>
<IfModule mod_php.c>
    php_flag engine off
</IfModule>

# Prevent script execution
Options -ExecCGI
AddHandler cgi-script .bat .cmd .sh .exe .php .pl .py .cgi .asp .aspx .jsp
