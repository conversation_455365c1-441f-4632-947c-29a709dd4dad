<?php
session_start();
include '../../config/config.php';

// Cek autentikasi
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Get request type
$type = isset($_GET['type']) ? $_GET['type'] : '';

// Response array
$response = [
    'success' => true,
    'data' => []
];

// Get departments
if ($type == 'departments' || $type == 'all') {
    $query = "SELECT * FROM departments ORDER BY code";
    $result = $conn->query($query);
    
    if ($result) {
        $departments = [];
        while ($row = $result->fetch_assoc()) {
            $departments[] = $row;
        }
        $response['data']['departments'] = $departments;
    } else {
        $response['success'] = false;
        $response['message'] = "Error fetching departments: " . $conn->error;
    }
}

// Get sub departments
if ($type == 'sub_departments' || $type == 'all') {
    $departmentId = isset($_GET['department_id']) ? intval($_GET['department_id']) : 0;
    
    $query = "SELECT s.*, d.code as department_code 
              FROM sub_departments s
              JOIN departments d ON s.department_id = d.id";
    
    if ($departmentId > 0) {
        $query .= " WHERE s.department_id = " . $departmentId;
    }
    
    $query .= " ORDER BY s.code";
    $result = $conn->query($query);
    
    if ($result) {
        $subDepartments = [];
        while ($row = $result->fetch_assoc()) {
            $subDepartments[] = $row;
        }
        $response['data']['sub_departments'] = $subDepartments;
    } else {
        $response['success'] = false;
        $response['message'] = "Error fetching sub departments: " . $conn->error;
    }
}

// Get positions
if ($type == 'positions' || $type == 'all') {
    $subDepartmentId = isset($_GET['sub_department_id']) ? intval($_GET['sub_department_id']) : 0;
    
    $query = "SELECT p.*, s.code as sub_department_code, d.code as department_code
              FROM positions p
              JOIN sub_departments s ON p.sub_department_id = s.id
              JOIN departments d ON s.department_id = d.id";
    
    if ($subDepartmentId > 0) {
        $query .= " WHERE p.sub_department_id = " . $subDepartmentId;
    }
    
    $query .= " ORDER BY p.name";
    $result = $conn->query($query);
    
    if ($result) {
        $positions = [];
        while ($row = $result->fetch_assoc()) {
            $positions[] = $row;
        }
        $response['data']['positions'] = $positions;
    } else {
        $response['success'] = false;
        $response['message'] = "Error fetching positions: " . $conn->error;
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
?>
