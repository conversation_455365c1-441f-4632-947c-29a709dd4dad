<?php
// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php untuk koneksi database
include '../config/config.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Set header untuk download file Excel
if (isset($_POST['export'])) {
    // Ambil filter dari form jika ada
    $filter_status = isset($_POST['filter_status']) ? $_POST['filter_status'] : [];
    $filter_pt = isset($_POST['filter_pt']) ? $_POST['filter_pt'] : [];
    $filter_dept = isset($_POST['filter_dept']) ? $_POST['filter_dept'] : [];
    
    // Buat query dasar
    $query = "SELECT nik, nama, tgl_masuk, jk, level_karyawan, tgl_lahir, agama, pendidikan_akhir, no_telp, dept, bagian, jabatan, `group`, status, pt FROM karyawan WHERE 1=1";
    
    // Tambahkan filter jika ada
    if (!empty($filter_status)) {
        $status_list = "'" . implode("','", array_map(function($item) use ($conn) {
            return mysqli_real_escape_string($conn, $item);
        }, $filter_status)) . "'";
        $query .= " AND status IN ($status_list)";
    }
    
    if (!empty($filter_pt)) {
        $pt_list = "'" . implode("','", array_map(function($item) use ($conn) {
            return mysqli_real_escape_string($conn, $item);
        }, $filter_pt)) . "'";
        $query .= " AND pt IN ($pt_list)";
    }
    
    if (!empty($filter_dept)) {
        $dept_list = "'" . implode("','", array_map(function($item) use ($conn) {
            return mysqli_real_escape_string($conn, $item);
        }, $filter_dept)) . "'";
        $query .= " AND dept IN ($dept_list)";
    }
    
    // Eksekusi query
    $result = $conn->query($query);
    
    if ($result) {
        // Set header untuk download file Excel
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="data_karyawan_' . date('Ymd_His') . '.xls"');
        header('Cache-Control: max-age=0');
        
        // Output header Excel
        echo "NIK\tNama\tTgl Masuk\tJK\tLevel Karyawan\tTgl Lahir\tAgama\tPendidikan Akhir\tNo. Telp\tDept\tBagian\tJabatan\tGroup\tStatus\tPT\n";
        
        // Output data
        while ($row = $result->fetch_assoc()) {
            echo implode("\t", array_map(function($item) {
                // Handle special characters in Excel
                return str_replace(["\r", "\n", "\t"], [" ", " ", " "], $item);
            }, $row)) . "\n";
        }
        
        exit;
    } else {
        $error_message = "Gagal mengambil data: " . $conn->error;
    }
}

// Ambil daftar status untuk filter
$status_query = "SELECT DISTINCT status FROM karyawan WHERE status IS NOT NULL AND status != '' ORDER BY status";
$status_result = $conn->query($status_query);
$status_list = [];
if ($status_result) {
    while ($row = $status_result->fetch_assoc()) {
        $status_list[] = $row['status'];
    }
}

// Ambil daftar PT untuk filter
$pt_query = "SELECT DISTINCT pt FROM karyawan WHERE pt IS NOT NULL AND pt != '' ORDER BY pt";
$pt_result = $conn->query($pt_query);
$pt_list = [];
if ($pt_result) {
    while ($row = $pt_result->fetch_assoc()) {
        $pt_list[] = $row['pt'];
    }
}

// Ambil daftar departemen untuk filter
$dept_query = "SELECT DISTINCT dept FROM karyawan WHERE dept IS NOT NULL AND dept != '' ORDER BY dept";
$dept_result = $conn->query($dept_query);
$dept_list = [];
if ($dept_result) {
    while ($row = $dept_result->fetch_assoc()) {
        $dept_list[] = $row['dept'];
    }
}

// Tutup koneksi database
$conn->close();
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    body {
        background-color: #f5f5f5;
        overflow-x: hidden;
    }

    .container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
    }

    .card {
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 20px;
    }

    .card-header {
        background-color: #BF0000 !important;
        color: white;
        padding: 15px 20px;
    }

    .card-header h3 {
        margin: 0;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
    }

    .card-header h3 i {
        margin-right: 10px;
    }

    .card-body {
        padding: 20px;
        background-color: white;
    }

    .filter-section {
        color: #a50000;
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .filter-title {
        font-size: 1.2rem;
        text-transform: uppercase;
        text-align: center;
        color: #a50000;
        font-weight: 600;
        margin-bottom: 15px;
        color: #333;
    }

    .filter-options {
        color: #a50000;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 15px;
    }

    .filter-option {
        background-color: #fff;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 8px 15px;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        align-items: center;
    }

    .filter-option:hover {
        border-color: #BF0000;
    }

    .filter-option input {
        margin-right: 8px;
    }

    .filter-actions {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }

    .btn-primary {
        background-color: #BF0000;
        border-color: #BF0000;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #a00000;
        border-color: #a00000;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #5a6268;
    }

    .select-actions {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
    }

    .select-action {
        
        border-color: #721c24!important;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 8px 15px;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 14px;
    }

    .select-action:hover {
        background-color: #a50000;
        color: white!important;
    }

    .jarak {
        height: 100px;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 5px;
    }

    .alert-info {
        color: #0c5460;
        background-color: #d1ecf1;
        border-color: #bee5eb;
    }

    .alert-warning {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeeba;
    }

    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    @media (max-width: 768px) {
        .filter-options {
            flex-direction: column;
            gap: 5px;
        }

        .filter-option {
            width: 100%;
        }

        .filter-actions {
            flex-direction: column;
        }

        .btn-primary, .btn-secondary {
            width: 100%;
        }
    }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container">
    <div class="card">
        <div class="card-header">
            <h3><i class="fas fa-file-export"></i> Export Data Karyawan</h3>
        </div>
        <div class="card-body">
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Gunakan fitur ini untuk mengekspor data karyawan dari database ke file Excel. File yang dihasilkan dapat digunakan sebagai template untuk update data karyawan.
            </div>

            <form method="post" action="">
                <div class="filter-section">
                    <div class="filter-title">Filter Status</div>
                    <div class="select-actions">
                        <button type="button" class="select-action" id="selectAllStatus" style="color:white!important;">Pilih Semua</button>
                        <button type="button" class="select-action" id="deselectAllStatus" style="color:white!important;">Hapus Semua</button>
                    </div>
                    <div class="filter-options">
                        <?php foreach ($status_list as $status): ?>
                            <label class="filter-option">
                                <input type="checkbox" name="filter_status[]" value="<?php echo htmlspecialchars($status); ?>" checked>
                                <?php echo htmlspecialchars($status); ?>
                            </label>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="filter-section">
                    <div class="filter-title">Filter PT</div>
                    <div class="select-actions">
                        <button type="button" class="select-action" id="selectAllPT" style="color:white!important;">Pilih Semua</button>
                        <button type="button" class="select-action" id="deselectAllPT" style="color:white!important;">Hapus Semua</button>
                    </div>
                    <div class="filter-options">
                        <?php foreach ($pt_list as $pt): ?>
                            <label class="filter-option">
                                <input type="checkbox" name="filter_pt[]" value="<?php echo htmlspecialchars($pt); ?>" checked>
                                <?php echo htmlspecialchars($pt); ?>
                            </label>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="filter-section">
                    <div class="filter-title">Filter Departemen</div>
                    <div class="select-actions">
                        <button type="button" class="select-action" id="selectAllDept" style="color:white!important;">Pilih Semua</button>
                        <button type="button" class="select-action" id="deselectAllDept" style="color:white!important;">Hapus Semua</button>
                    </div>
                    <div class="filter-options">
                        <?php foreach ($dept_list as $dept): ?>
                            <label class="filter-option">
                                <input type="checkbox" name="filter_dept[]" value="<?php echo htmlspecialchars($dept); ?>" checked>
                                <?php echo htmlspecialchars($dept); ?>
                            </label>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="filter-actions">
                    <button type="submit" name="export" class="btn btn-primary">
                        <i class="fas fa-file-export"></i> Export Data
                    </button>
                    <a href="employee_management.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    // Select/deselect all checkboxes for Status
    document.getElementById('selectAllStatus').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('input[name="filter_status[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
    });

    document.getElementById('deselectAllStatus').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('input[name="filter_status[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    });

    // Select/deselect all checkboxes for PT
    document.getElementById('selectAllPT').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('input[name="filter_pt[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
    });

    document.getElementById('deselectAllPT').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('input[name="filter_pt[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    });

    // Select/deselect all checkboxes for Dept
    document.getElementById('selectAllDept').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('input[name="filter_dept[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
    });

    document.getElementById('deselectAllDept').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('input[name="filter_dept[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    });
</script>
</body>
</html>
