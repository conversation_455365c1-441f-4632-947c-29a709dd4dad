
<style>
.container-form {
    padding: 20px;
    max-width: 1200px;
    margin-top: 100px;
    margin: 0 auto;
}

.welcome-section {
    background: rgb(157, 0, 0);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    text-align: center;
}

.welcome-section h1 {
    margin: 0;
    font-size: 24px;
}

.welcome-section p {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
}

.stat-card h3 {
    margin: 0;
    color: #333;
    font-size: 16px;
}

.stat-card p {
    margin: 10px 0 0;
    font-size: 24px;
    color: rgb(157, 0, 0);
    font-weight: bold;
}

    table {
        table-layout: fixed;
        width: 100%;
        border-collapse: collapse;
    }

    td, th {
        white-space: normal;
        word-wrap: break-word;
        overflow-wrap: break-word;
        max-width: 300px;
        padding: 10px;
        vertical-align: top;
    }

    .department-badge,
    .participants-count,
    .date-fixed,
    .date-pending {
        display: block;
        white-space: normal;
    }

.submissions-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.submissions-table table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed; /* Fixed table layout for better control */
}

.submissions-table th, .submissions-table td {
    padding: 12px;
    word-wrap: break-word; /* Enable word wrapping */
    overflow-wrap: break-word; /* Modern browsers */
    hyphens: auto; /* Add hyphens where possible */
    max-width: 250px; /* Maximum width for cells */
    position: relative; /* For tooltip positioning */
}

/* Tooltip styles */
[data-tooltip] {
    position: relative;
    cursor: help;
}

/* CSS tooltips disabled in favor of JavaScript tooltips */
[data-tooltip]::before,
[data-tooltip]::after {
    display: none !important;
}

.submissions-table th {
    background: #a50000;
    color: white;
    text-align: left;
}

.submissions-table td {
    border-bottom: 1px solid #eee;
    vertical-align: top; /* Align content to top */
}

.status-badge {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    box-sizing: border-box;
}

.status-pending {
    background-color: #fff3e0;
    color: #f57c00;
}

.status-approved {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.status-rejected {
    background-color: rgba(244, 67, 54, 0.2);
    color: #d32f2f;
    border: 1px solid rgba(244, 67, 54, 0.4);
}

.action-button {
    display: inline-block;
    padding: 8px 16px;
    background: #a50000;
    color: white;
    border-radius: 4px;
    text-decoration: none;
    transition: background 0.3s ease;
}

.action-button:hover {
    background: #800000;
}

.department-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    display: inline-block;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    box-sizing: border-box;
}

.date-fixed {
    background: #e3f2fd;
    color: rgb(63, 224, 18);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    display: inline-block;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    box-sizing: border-box;
}

.date-pending {
    background: #fff3e0;
    color: darkorange;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    display: inline-block;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    box-sizing: border-box;
}

.participants-count {
    background: #f3e5f5;
    color: #7b1fa2;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;
    max-width: 100%;
    box-sizing: border-box;
}

/* Responsive table styles */
@media screen and (max-width: 768px) {
    .submissions-table {
        overflow-x: auto;
        display: block;
    }

    .submissions-table table {
        min-width: 650px; /* Ensure table doesn't get too small */
    }

    .submissions-table th,
    .submissions-table td {
        padding: 8px;
        font-size: 13px;
    }

    .status-badge,
    .department-badge,
    .date-fixed,
    .date-pending,
    .participants-count {
        padding: 3px 6px;
        font-size: 11px;
    }

    .action-button {
        padding: 6px 10px;
        font-size: 12px;
    }
}

</style>

<div class="welcome-section">
        <h1>Dashboard Persetujuan Training</h1>
        <p>Selamat datang, <?= htmlspecialchars($_SESSION['user_name'] ?? 'Department Head') ?></p>
    </div>

    <div class="stats-section">
        <div class="stat-card">
            <h3>Total Pengajuan Departemen</h3>
            <p><?= $stats['total'] ?></p>
        </div>
        <div class="stat-card">
            <h3>Menunggu Persetujuan Anda</h3>
            <p><?= $stats['pending'] ?></p>
        </div>
        <div class="stat-card">
            <h3>Disetujui Oleh Anda</h3>
            <p><?= $stats['approved'] ?></p>
        </div>

        <div class="stat-card">
            <h3>Dibatalkan Oleh Anda</h3>
            <p><?= $stats['rejected'] ?></p>
        </div>
    </div>

    <div class="submissions-table">
    <div class="responsive-table-container">
        <table>
            <colgroup>
                <col style="width: 20%"> <!-- Topik Training -->
                <col style="width: 15%"> <!-- Pengaju -->
                <col style="width: 15%"> <!-- Departemen -->
                <col style="width: 10%"> <!-- Peserta -->
                <col style="width: 15%"> <!-- Tanggal -->
                <col style="width: 15%"> <!-- Status -->
                <col style="width: 10%"> <!-- Aksi -->
            </colgroup>
            <tr>
                <th>Topik Training</th>
                <th>Pengaju</th>
                <th>Departemen</th>
                <th>Peserta</th>
                <th>Tanggal</th>
                <th>Status</th>
                <th>Aksi</th>
            </tr>
            <?php if (empty($result_array)): ?>
                <tr>
                    <td colspan="7" style="text-align: center; padding: 20px;">
                        Tidak ada training yang perlu di-approve saat ini.
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($result_array as $row): ?>
                    <tr>
                        <td data-tooltip="<?= htmlspecialchars($row['training_topic']) ?>">
                            <?= htmlspecialchars($row['training_topic']) ?>
                        </td>
                        <td data-tooltip="<?= htmlspecialchars($row['requester_name']) ?>">
                            <?= htmlspecialchars($row['requester_name']) ?>
                        </td>
                        <td>
                            <span class="department-badge" data-tooltip="<?= htmlspecialchars($row['departemen'] ?? 'N/A') ?>">
                                <i class="fas fa-building"></i>
                                <?= htmlspecialchars($row['departemen'] ?? 'N/A') ?>
                            </span>
                        </td>
                        <td>
                            <span class="participants-count" data-tooltip="<?= $row['total_participants'] ?> peserta">
                                <i class="fas fa-users"></i>
                                <?= $row['total_participants'] ?>
                            </span>
                        </td>
                        <td>
                            <?php
                            if (!empty($row['start_date'])) {
                                // Ada tanggal training
                                if ($row['is_confirmed'] == 1) {
                                    // Tanggal sudah dikonfirmasi/fixed
                                    if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                                        // Multi-day training yang sudah dikonfirmasi
                                        echo '<span class="date-confirmed" style="color: #2e7d32; background-color: rgba(76, 175, 80, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #2e7d32; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;" data-tooltip="Periode dikonfirmasi: ' . htmlspecialchars($row['start_date']) . ' sampai dengan ' . htmlspecialchars($row['end_date']) . '">';
                                        echo '<i class="fas fa-calendar-check"></i> ';
                                        echo htmlspecialchars($row['start_date']) . ' s/d ' . htmlspecialchars($row['end_date']);
                                        echo '<span style="background-color: rgba(76, 175, 80, 0.2); color: #155724; border: 1px solid rgba(76, 175, 80, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Dikonfirmasi</span>';
                                        echo '</span>';
                                    } else {
                                        // Single day training yang sudah dikonfirmasi
                                        echo '<span class="date-confirmed" style="color: #2e7d32; background-color: rgba(76, 175, 80, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #2e7d32; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;" data-tooltip="Tanggal dikonfirmasi: ' . htmlspecialchars($row['start_date']) . '">';
                                        echo '<i class="fas fa-calendar-check"></i> ';
                                        echo htmlspecialchars($row['start_date']);
                                        echo '<span style="background-color: rgba(76, 175, 80, 0.2); color: #155724; border: 1px solid rgba(76, 175, 80, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Dikonfirmasi</span>';
                                        echo '</span>';
                                    }
                                } else {
                                    // Tanggal belum dikonfirmasi (pending)
                                    if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                                        // Multi-day training yang masih pending
                                        echo '<span class="date-pending" style="color: #f57c00; background-color: rgba(255, 193, 7, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #f57c00; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;" data-tooltip="Periode pending: ' . htmlspecialchars($row['start_date']) . ' sampai dengan ' . htmlspecialchars($row['end_date']) . '">';
                                        echo '<i class="fas fa-calendar-alt"></i> ';
                                        echo htmlspecialchars($row['start_date']) . ' s/d ' . htmlspecialchars($row['end_date']);
                                        echo '<span style="background-color: rgba(255, 193, 7, 0.2); color: #856404; border: 1px solid rgba(255, 193, 7, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Pending</span>';
                                        echo '</span>';
                                    } else {
                                        // Single day training yang masih pending
                                        echo '<span class="date-pending" style="color: #f57c00; background-color: rgba(255, 193, 7, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #f57c00; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;" data-tooltip="Tanggal pending: ' . htmlspecialchars($row['start_date']) . '">';
                                        echo '<i class="fas fa-calendar-alt"></i> ';
                                        echo htmlspecialchars($row['start_date']);
                                        echo '<span style="background-color: rgba(255, 193, 7, 0.2); color: #856404; border: 1px solid rgba(255, 193, 7, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Pending</span>';
                                        echo '</span>';
                                    }
                                }
                            } else {
                                // Belum ada tanggal sama sekali
                                echo '<span class="date-pending" style="color: #f57c00; background-color: rgba(255, 193, 7, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #f57c00; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;" data-tooltip="Tanggal belum ditentukan">';
                                echo '<i class="fas fa-calendar-times"></i> ';
                                echo 'Belum Ditentukan';
                                echo '<span style="background-color: rgba(255, 193, 7, 0.2); color: #856404; border: 1px solid rgba(255, 193, 7, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Menunggu</span>';
                                echo '</span>';
                            }
                            ?>
                        </td>
                        <td>
                          <?php if (isset($row['status']) && $row['status'] == 'Rejected'): ?>
                              <?php
                              $status_text = "Training telah dibatalkan";
                              $tooltip_text = $status_text;

                              if (!empty($row['rejected_by'])) {
                                  // Ambil nama user yang membatalkan
                                  $rejecter_query = "SELECT name, jabatan FROM users WHERE id = ?";
                                  $rejecter_stmt = $conn->prepare($rejecter_query);
                                  $rejecter_stmt->bind_param("i", $row['rejected_by']);
                                  $rejecter_stmt->execute();
                                  $rejecter_result = $rejecter_stmt->get_result();

                                  if ($rejecter_row = $rejecter_result->fetch_assoc()) {
                                      $rejecter_name = $rejecter_row['name'];
                                      $rejecter_position = $rejecter_row['jabatan'];
                                      $status_text = "Training telah dibatalkan oleh " . htmlspecialchars($rejecter_name) . " (" . htmlspecialchars($rejecter_position) . ")";
                                      $tooltip_text = $status_text;

                                      if (!empty($row['rejected_at'])) {
                                          $rejected_date = new DateTime($row['rejected_at']);
                                          $status_text .= " pada " . $rejected_date->format('d-m-Y H:i');
                                          $tooltip_text = $status_text;
                                      }
                                  }
                                  $rejecter_stmt->close();
                              }
                              ?>
                              <i class="fas fa-times-circle" style="color: #d32f2f;"></i>
                              <span class="status-badge status-rejected" data-tooltip="<?= htmlspecialchars($tooltip_text) ?>">
                                  <?= $status_text ?>
                              </span>
                          <?php else: ?>
                              <i class="fas fa-hourglass-half" style="color: #f57c00;"></i>
                              <?php if ($is_hrga_manager && isset($row['approved_dir']) && $row['approved_dir'] != 'Pending'): ?>
                                  <span class="status-badge <?= ($row['approved_dir'] ?? 'Pending') == 'Approved' ? 'status-approved' : 'status-pending' ?>"
                                        data-tooltip="Status: <?= htmlspecialchars($row['approved_dir'] ?? 'Pending') ?>">
                                      <?= htmlspecialchars($row['approved_dir'] ?? 'Pending') ?>
                                  </span>
                              <?php else: ?>
                                  <span class="status-badge <?= ($row['approved_dir'] ?? 'Pending') == 'Approved' ? 'status-approved' : 'status-pending' ?>"
                                        data-tooltip="Status: <?= htmlspecialchars($row['approved_dir'] ?? 'Pending') ?>">
                                      <?= htmlspecialchars($row['approved_dir'] ?? 'Pending') ?>
                                  </span>
                              <?php endif; ?>
                          <?php endif; ?>
                        </td>
                        <td>
                            <a href="detail_training.php?id=<?= $row['id'] ?>" class="action-button" data-tooltip="Lihat detail training">
                                <i class="fas fa-info-circle"></i>
                                Detail
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </table>
    </div>
    </div>

    <script>
    // JavaScript to position tooltips correctly
    document.addEventListener('DOMContentLoaded', function() {
        // Get all elements with data-tooltip attribute
        const tooltipElements = document.querySelectorAll('[data-tooltip]');

        // Create a single tooltip element to be reused
        const tooltip = document.createElement('div');
        tooltip.id = 'custom-tooltip';
        tooltip.style.position = 'fixed';
        tooltip.style.zIndex = '9999';
        tooltip.style.padding = '8px 12px';
        tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';
        tooltip.style.color = 'white';
        tooltip.style.borderRadius = '4px';
        tooltip.style.fontSize = '12px';
        tooltip.style.maxWidth = '300px';
        tooltip.style.boxShadow = '0 3px 8px rgba(0, 0, 0, 0.3)';
        tooltip.style.textAlign = 'center';
        tooltip.style.pointerEvents = 'none';
        tooltip.style.opacity = '0';
        tooltip.style.transition = 'opacity 0.2s ease-in-out';
        document.body.appendChild(tooltip);

        tooltipElements.forEach(element => {
            // Remove the pseudo-element tooltips
            element.removeAttribute('data-tooltip-active');

            // Store the tooltip text in a custom attribute
            const tooltipText = element.getAttribute('data-tooltip');
            element.setAttribute('data-tooltip-text', tooltipText);

            element.addEventListener('mouseenter', function(e) {
                // Get tooltip content
                const tooltipContent = this.getAttribute('data-tooltip-text');
                if (!tooltipContent) return;

                // Set tooltip content
                tooltip.textContent = tooltipContent;

                // Position tooltip above the element
                const rect = this.getBoundingClientRect();
                const tooltipHeight = tooltip.offsetHeight;

                // Position tooltip centered above the element
                tooltip.style.left = rect.left + (rect.width / 2) + 'px';
                tooltip.style.top = (rect.top - tooltipHeight - 10) + 'px';
                tooltip.style.transform = 'translateX(-50%)';

                // Make sure tooltip is fully visible
                setTimeout(() => {
                    const tooltipRect = tooltip.getBoundingClientRect();

                    // Check if tooltip is outside viewport on the left
                    if (tooltipRect.left < 10) {
                        tooltip.style.left = '10px';
                        tooltip.style.transform = 'none';
                    }

                    // Check if tooltip is outside viewport on the right
                    if (tooltipRect.right > window.innerWidth - 10) {
                        tooltip.style.left = (window.innerWidth - 10) + 'px';
                        tooltip.style.transform = 'translateX(-100%)';
                    }

                    // Check if tooltip is outside viewport on the top
                    if (tooltipRect.top < 10) {
                        tooltip.style.top = (rect.bottom + 10) + 'px';
                    }
                }, 0);

                // Show tooltip
                tooltip.style.opacity = '1';
            });

            element.addEventListener('mouseleave', function() {
                tooltip.style.opacity = '0';
            });
        });

        // Hide tooltip when clicking anywhere
        document.addEventListener('click', function() {
            tooltip.style.opacity = '0';
        });
    });
    </script>