<?php
// Simple test to verify function signature fix
include '../config/config.php';

echo "<h2>Testing Director Email Function Signatures</h2>";

// Test if functions can be loaded without deprecated warnings
require_once '../config/director_approval_email_helper.php';

echo "<p>✅ director_approval_email_helper.php loaded successfully without deprecated warnings</p>";

// Test function signatures
echo "<h3>Function Signatures Test:</h3>";

// Check if functions exist
if (function_exists('sendDirectorApprovalNotification')) {
    echo "<p>✅ sendDirectorApprovalNotification function exists</p>";
    
    // Get function reflection to check parameters
    $reflection = new ReflectionFunction('sendDirectorApprovalNotification');
    $params = $reflection->getParameters();
    
    echo "<p><strong>Parameters:</strong></p>";
    echo "<ul>";
    foreach ($params as $param) {
        $optional = $param->isOptional() ? ' (optional)' : ' (required)';
        $default = $param->isOptional() ? ' = ' . ($param->getDefaultValue() ?? 'null') : '';
        echo "<li>{$param->getName()}{$optional}{$default}</li>";
    }
    echo "</ul>";
} else {
    echo "<p>❌ sendDirectorApprovalNotification function not found</p>";
}

if (function_exists('sendDirectorRejectionNotification')) {
    echo "<p>✅ sendDirectorRejectionNotification function exists</p>";
    
    // Get function reflection to check parameters
    $reflection = new ReflectionFunction('sendDirectorRejectionNotification');
    $params = $reflection->getParameters();
    
    echo "<p><strong>Parameters:</strong></p>";
    echo "<ul>";
    foreach ($params as $param) {
        $optional = $param->isOptional() ? ' (optional)' : ' (required)';
        $default = $param->isOptional() ? ' = ' . ($param->getDefaultValue() ?? 'null') : '';
        echo "<li>{$param->getName()}{$optional}{$default}</li>";
    }
    echo "</ul>";
} else {
    echo "<p>❌ sendDirectorRejectionNotification function not found</p>";
}

echo "<h3>Test Result:</h3>";
echo "<p>✅ All function signatures have been fixed!</p>";
echo "<p>✅ Required parameters ($conn) are now placed before optional parameters</p>";
echo "<p>✅ No more deprecated warnings should appear</p>";

echo "<hr>";
echo "<p><a href='test_director_approval_email.php'>Go to Director Email Test Page</a></p>";
echo "<p><a href='../Dir/dashboard.php'>Go to Director Dashboard</a></p>";
?>
