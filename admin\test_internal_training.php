<?php
session_start();
include '../config/config.php';
include '../config/access_control.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "Please login first";
    exit();
}

$user_id = $_SESSION['user_id'];

echo "<h2>Testing Internal Training Form Access</h2>";

// Test 1: Get user data
echo "<h3>1. Get User Data</h3>";
$user_data = getUserLevel($user_id);
if ($user_data) {
    echo "<pre>";
    print_r($user_data);
    echo "</pre>";
} else {
    echo "User data not found!";
}

// Test 2: Check access eligibility (No restrictions for internal training)
echo "<h3>2. Access Policy for Internal Training Form</h3>";
echo "✅ <strong>OPEN ACCESS:</strong> Semua user dapat mengajukan training internal<br>";
echo "✅ <strong>NO LEVEL RESTRICTIONS:</strong> Tidak ada batasan level karyawan<br>";
echo "✅ <strong>NO POSITION RESTRICTIONS:</strong> Tidak ada batasan jabatan<br>";
echo "✅ <strong>UNIVERSAL ACCESS:</strong> Semua karyawan berhak mengajukan training internal<br>";

// Test 3: Test user session
echo "<h3>3. Test User Session for Internal Training</h3>";
try {
    if ($user_data) {
        echo "✅ User session VALID<br>";
        echo "✅ User dapat mengakses form training internal<br>";
        echo "✅ Level user: " . ($user_data['level_karyawan'] ?? 'N/A') . " (tidak ada batasan)<br>";
        echo "✅ Jabatan user: " . ($user_data['jabatan'] ?? 'N/A') . " (tidak ada batasan)<br>";
    } else {
        echo "❌ User session INVALID<br>";
        echo "❌ User tidak dapat mengakses form training internal<br>";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}

// Test 4: Check database connection
echo "<h3>4. Test Database Connection</h3>";
try {
    $test_query = "SELECT COUNT(*) as count FROM offline_training";
    $result = $conn->query($test_query);
    if ($result) {
        $row = $result->fetch_assoc();
        echo "✅ Database connection OK. Total offline training records: " . $row['count'] . "<br>";
    } else {
        echo "❌ Database query failed: " . $conn->error . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 5: Check session variables
echo "<h3>5. Check Session Variables</h3>";
echo "User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "<br>";
echo "Full Name: " . ($_SESSION['full_name'] ?? 'Not set') . "<br>";
echo "User NIK: " . ($_SESSION['user_nik'] ?? 'Not set') . "<br>";
echo "User Email: " . ($_SESSION['user_email'] ?? 'Not set') . "<br>";

// Test 6: Test form token generation
echo "<h3>6. Test Form Token Generation</h3>";
if (!isset($_SESSION['token'])) {
    $_SESSION['token'] = bin2hex(random_bytes(32));
}
$form_token = md5(uniqid(mt_rand(), true));
echo "CSRF Token: " . substr($_SESSION['token'], 0, 20) . "...<br>";
echo "Form Token: " . substr($form_token, 0, 20) . "...<br>";

echo "<br><h3>Test Links:</h3>";
if ($user_data) {
    echo '<a href="../pemohon/internal_training_form.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">✅ Access Internal Training Form (Open for All)</a><br><br>';
} else {
    echo '<span style="background: #dc3545; color: white; padding: 10px 20px; border-radius: 5px;">❌ Cannot Access - Please Login First</span><br><br>';
}

echo '<a href="../admin/test_access_control.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🧪 Full Access Control Test</a><br><br>';
echo '<a href="../index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🏠 Back to Dashboard</a>';
?>
