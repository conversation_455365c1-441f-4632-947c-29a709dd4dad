# Meningkatkan keamanan website
# File ini harus ditempatkan di root direktori website

# Aktifkan mesin rewrite
<IfModule mod_rewrite.c>
    RewriteEngine On

    # Redirect HTTP ke HTTPS
    # Uncomment baris di bawah ini jika sudah menggunakan HTTPS
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Mencegah akses langsung ke file PHP di direktori tertentu
    # Komentar sementara untuk debugging
    # RewriteRule ^config/.*\.php$ - [F,L]
    # RewriteRule ^logs/.*$ - [F,L]

    # Mencegah akses ke file sensitif
    RewriteRule ^\.git - [F,L]
    RewriteRule ^composer\.(json|lock)$ - [F,L]
    RewriteRule ^package(-lock)?\.json$ - [F,L]
    RewriteRule ^Dockerfile - [F,L]
    RewriteRule ^docker-compose\.yml - [F,L]
    RewriteRule ^\.env - [F,L]

    # Mencegah directory listing
    Options -Indexes
</IfModule>

# Mencegah akses ke file dengan ekstensi tertentu
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|git|svn)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Mencegah akses ke file backup dan temporary
<FilesMatch "\.(bak|config|sql|fla|psd|ini|log|sh|inc|swp|dist|tmp|temp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Mencegah akses ke file dot (hidden files)
<FilesMatch "^\.">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Mencegah akses ke direktori tertentu
<IfModule mod_rewrite.c>
    RewriteRule ^(vendor|node_modules|tests)/ - [F,L]
</IfModule>

# Menambahkan header keamanan
<IfModule mod_headers.c>
    # Mencegah clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"

    # Mencegah MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"

    # Mengaktifkan XSS protection di browser
    Header always set X-XSS-Protection "1; mode=block"

    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Permissions Policy (formerly Feature Policy)
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"

    # HTTP Strict Transport Security (HSTS)
    # Uncomment baris di bawah ini jika sudah menggunakan HTTPS
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

    # Content Security Policy (CSP)
    # Komentar sementara untuk debugging
    # Header always set Content-Security-Policy "default-src 'self'; script-src 'self' https://code.jquery.com https://cdn.jsdelivr.net 'unsafe-inline'; style-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-inline'; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self';"
</IfModule>

# Mencegah akses ke file PHP di direktori uploads
<IfModule mod_rewrite.c>
    RewriteRule ^uploads/.*\.php$ - [F,L]
</IfModule>

# Mencegah PHP execution di direktori uploads
<Directory "uploads">
    <FilesMatch "\.php$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# Mencegah akses ke file sensitif - komentar sementara untuk debugging
# <Files ~ "^(config\.php|constants\.php|security\.php|mail\.php)$">
#     Order Allow,Deny
#     Deny from all
# </Files>

# Mencegah PHP information leakage
<IfModule mod_php7.c>
    php_flag expose_php Off
    php_flag display_errors Off
    php_flag display_startup_errors Off
    php_flag log_errors On
    php_flag ignore_repeated_errors On
    php_flag ignore_repeated_source On
    php_flag track_errors Off
    php_flag register_globals Off
    php_flag allow_url_fopen Off
    php_flag allow_url_include Off
</IfModule>

# Mencegah PHP information leakage (PHP 8)
<IfModule mod_php8.c>
    php_flag expose_php Off
    php_flag display_errors Off
    php_flag display_startup_errors Off
    php_flag log_errors On
    php_flag ignore_repeated_errors On
    php_flag ignore_repeated_source On
    php_flag track_errors Off
    php_flag register_globals Off
    php_flag allow_url_fopen Off
    php_flag allow_url_include Off
</IfModule>

# Mengatur default character set
AddDefaultCharset UTF-8

# Mengatur timezone
SetEnv TZ Asia/Jakarta

# Mencegah hotlinking
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
    RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?google.com [NC]
    RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?bing.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif)$ - [NC,F,L]
</IfModule>

# Mengatur cache control
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Mengatur compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
