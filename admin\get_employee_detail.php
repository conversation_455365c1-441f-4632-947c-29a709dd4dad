<?php
// <PERSON>lai output buffering untuk menangkap semua output
ob_start();

// Nonaktifkan error reporting yang bisa merusak JSON
error_reporting(0);
ini_set('display_errors', 0);

// Set header JSON di awal
header('Content-Type: application/json');

// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

try {
    // Validasi input
    if (empty($_GET['nik'])) {
        throw new Exception('NIK tidak boleh kosong');
    }
    
    $nik = $_GET['nik'];
    
    // Ambil data karyawan dari database
    $query = "SELECT * FROM karyawan WHERE nik = ?";
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Error preparing query: " . $conn->error);
    }
    
    $stmt->bind_param("s", $nik);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception('Karyawan dengan NIK tersebut tidak ditemukan');
    }
    
    $employee = $result->fetch_assoc();
    
    // Log aktivitas
    $user_id = $_SESSION['user_id'];
    $log_query = "INSERT INTO activity_logs (user_id, action, category, timestamp) 
                  VALUES (?, CONCAT('Melihat detail karyawan: ', ?), 'employee', NOW())";
    
    $log_stmt = $conn->prepare($log_query);
    if ($log_stmt) {
        $log_stmt->bind_param("is", $user_id, $employee['nama']);
        $log_stmt->execute();
    }
    
    // Kembalikan response sukses
    echo json_encode([
        'success' => true, 
        'employee' => $employee
    ]);
    
} catch (Exception $e) {
    // Kembalikan response error
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    // Bersihkan output buffer dan kirim respons
    $output = ob_get_clean();
    
    // Periksa apakah output adalah JSON yang valid
    json_decode($output);
    if (json_last_error() !== JSON_ERROR_NONE) {
        // Jika bukan JSON valid, ganti dengan pesan error JSON
        echo json_encode([
            'success' => false,
            'message' => 'Server error: Invalid JSON response',
            'debug_info' => 'Output contains non-JSON content'
        ]);
    } else {
        // Jika JSON valid, kirim output asli
        echo $output;
    }
    
    // Pastikan tidak ada output lain setelah ini
    exit();
}
