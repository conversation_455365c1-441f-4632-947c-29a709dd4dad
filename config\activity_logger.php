<?php
/**
 * Activity Logger - Helper untuk mencatat aktivitas pengguna
 * 
 * File ini berisi fungsi-fungsi untuk mencatat aktivitas pengguna ke dalam database
 * dan menampilkan log aktivitas.
 */

// Fungsi untuk mencatat aktivitas
if (!function_exists('log_activity')) {
    /**
     * Mencatat aktivitas pengguna ke dalam database
     * 
     * @param int $user_id ID pengguna yang melakukan aktivitas
     * @param string $action Deskripsi aktivitas yang dilakukan
     * @param string $category Kategori aktivitas (settings, training, employee, dll)
     * @param array $details Detail tambahan tentang aktivitas (opsional)
     * @return bool True jika berhasil, False jika gagal
     */
    function log_activity($user_id, $action, $category, $details = []) {
        global $conn;
        
        // Jika koneksi database tidak tersedia, return false
        if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
            return false;
        }
        
        try {
            // Cek apakah tabel activity_logs sudah ada
            $tableCheckQuery = "SHOW TABLES LIKE 'activity_logs'";
            $tableExists = $conn->query($tableCheckQuery)->num_rows > 0;
            
            // Jika tabel belum ada, buat tabel
            if (!$tableExists) {
                $createTableQuery = "CREATE TABLE activity_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    action VARCHAR(255) NOT NULL,
                    category VARCHAR(50) NOT NULL,
                    details TEXT NULL,
                    ip_address VARCHAR(45) NULL,
                    user_agent VARCHAR(255) NULL,
                    timestamp DATETIME NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )";
                $conn->query($createTableQuery);
                
                // Buat indeks untuk mempercepat query
                $conn->query("CREATE INDEX idx_activity_logs_category ON activity_logs(category)");
                $conn->query("CREATE INDEX idx_activity_logs_timestamp ON activity_logs(timestamp)");
                $conn->query("CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id)");
            }
            
            // Siapkan detail dalam format JSON jika ada
            $details_json = !empty($details) ? json_encode($details) : null;
            
            // Dapatkan IP address dan user agent
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            
            // Siapkan query untuk menyimpan log
            $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action, category, details, ip_address, user_agent, timestamp) 
                                   VALUES (?, ?, ?, ?, ?, ?, NOW())");
            
            $stmt->bind_param("isssss", $user_id, $action, $category, $details_json, $ip_address, $user_agent);
            $result = $stmt->execute();
            $stmt->close();
            
            return $result;
        } catch (Exception $e) {
            // Jika terjadi error, return false
            // Dalam produksi, sebaiknya log error ini ke file log
            return false;
        }
    }
}

// Fungsi untuk mendapatkan log aktivitas
if (!function_exists('get_activity_logs')) {
    /**
     * Mendapatkan log aktivitas dari database
     * 
     * @param array $filters Filter untuk log (category, user_id, date_from, date_to)
     * @param int $limit Jumlah log yang akan diambil
     * @param int $offset Offset untuk pagination
     * @return array Array berisi log aktivitas
     */
    function get_activity_logs($filters = [], $limit = 100, $offset = 0) {
        global $conn;
        
        // Jika koneksi database tidak tersedia, return array kosong
        if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
            return [];
        }
        
        try {
            // Siapkan query dasar
            $query = "SELECT al.*, u.name as user_name, u.nik as user_nik 
                     FROM activity_logs al 
                     LEFT JOIN users u ON al.user_id = u.id 
                     WHERE 1=1";
            $params = [];
            $types = "";
            
            // Tambahkan filter kategori jika ada
            if (!empty($filters['category']) && $filters['category'] !== 'all') {
                $query .= " AND al.category = ?";
                $params[] = $filters['category'];
                $types .= "s";
            }
            
            // Tambahkan filter user_id jika ada
            if (!empty($filters['user_id'])) {
                $query .= " AND al.user_id = ?";
                $params[] = $filters['user_id'];
                $types .= "i";
            }
            
            // Tambahkan filter tanggal dari jika ada
            if (!empty($filters['date_from'])) {
                $query .= " AND al.timestamp >= ?";
                $params[] = $filters['date_from'] . " 00:00:00";
                $types .= "s";
            }
            
            // Tambahkan filter tanggal sampai jika ada
            if (!empty($filters['date_to'])) {
                $query .= " AND al.timestamp <= ?";
                $params[] = $filters['date_to'] . " 23:59:59";
                $types .= "s";
            }
            
            // Tambahkan filter pencarian jika ada
            if (!empty($filters['search'])) {
                $query .= " AND (al.action LIKE ? OR u.name LIKE ? OR u.nik LIKE ?)";
                $search_term = "%" . $filters['search'] . "%";
                $params[] = $search_term;
                $params[] = $search_term;
                $params[] = $search_term;
                $types .= "sss";
            }
            
            // Tambahkan ordering dan limit
            $query .= " ORDER BY al.timestamp DESC LIMIT ?, ?";
            $params[] = $offset;
            $params[] = $limit;
            $types .= "ii";
            
            // Siapkan dan jalankan query
            $stmt = $conn->prepare($query);
            
            if (!empty($params)) {
                $stmt->bind_param($types, ...$params);
            }
            
            $stmt->execute();
            $result = $stmt->get_result();
            $logs = [];
            
            while ($row = $result->fetch_assoc()) {
                // Decode details jika ada
                if (!empty($row['details'])) {
                    $row['details'] = json_decode($row['details'], true);
                }
                $logs[] = $row;
            }
            
            $stmt->close();
            return $logs;
        } catch (Exception $e) {
            // Jika terjadi error, return array kosong
            return [];
        }
    }
}

// Fungsi untuk mendapatkan jumlah total log aktivitas
if (!function_exists('get_activity_logs_count')) {
    /**
     * Mendapatkan jumlah total log aktivitas dari database
     * 
     * @param array $filters Filter untuk log (category, user_id, date_from, date_to)
     * @return int Jumlah total log aktivitas
     */
    function get_activity_logs_count($filters = []) {
        global $conn;
        
        // Jika koneksi database tidak tersedia, return 0
        if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
            return 0;
        }
        
        try {
            // Siapkan query dasar
            $query = "SELECT COUNT(*) as total FROM activity_logs al 
                     LEFT JOIN users u ON al.user_id = u.id 
                     WHERE 1=1";
            $params = [];
            $types = "";
            
            // Tambahkan filter kategori jika ada
            if (!empty($filters['category']) && $filters['category'] !== 'all') {
                $query .= " AND al.category = ?";
                $params[] = $filters['category'];
                $types .= "s";
            }
            
            // Tambahkan filter user_id jika ada
            if (!empty($filters['user_id'])) {
                $query .= " AND al.user_id = ?";
                $params[] = $filters['user_id'];
                $types .= "i";
            }
            
            // Tambahkan filter tanggal dari jika ada
            if (!empty($filters['date_from'])) {
                $query .= " AND al.timestamp >= ?";
                $params[] = $filters['date_from'] . " 00:00:00";
                $types .= "s";
            }
            
            // Tambahkan filter tanggal sampai jika ada
            if (!empty($filters['date_to'])) {
                $query .= " AND al.timestamp <= ?";
                $params[] = $filters['date_to'] . " 23:59:59";
                $types .= "s";
            }
            
            // Tambahkan filter pencarian jika ada
            if (!empty($filters['search'])) {
                $query .= " AND (al.action LIKE ? OR u.name LIKE ? OR u.nik LIKE ?)";
                $search_term = "%" . $filters['search'] . "%";
                $params[] = $search_term;
                $params[] = $search_term;
                $params[] = $search_term;
                $types .= "sss";
            }
            
            // Siapkan dan jalankan query
            $stmt = $conn->prepare($query);
            
            if (!empty($params)) {
                $stmt->bind_param($types, ...$params);
            }
            
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            $stmt->close();
            
            return $row['total'] ?? 0;
        } catch (Exception $e) {
            // Jika terjadi error, return 0
            return 0;
        }
    }
}

// Fungsi untuk menghapus log aktivitas
if (!function_exists('clear_activity_logs')) {
    /**
     * Menghapus log aktivitas dari database
     * 
     * @param array $filters Filter untuk log yang akan dihapus (category, user_id, date_from, date_to)
     * @return bool True jika berhasil, False jika gagal
     */
    function clear_activity_logs($filters = []) {
        global $conn;
        
        // Jika koneksi database tidak tersedia, return false
        if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
            return false;
        }
        
        try {
            // Siapkan query dasar
            $query = "DELETE FROM activity_logs WHERE 1=1";
            $params = [];
            $types = "";
            
            // Tambahkan filter kategori jika ada
            if (!empty($filters['category']) && $filters['category'] !== 'all') {
                $query .= " AND category = ?";
                $params[] = $filters['category'];
                $types .= "s";
            }
            
            // Tambahkan filter user_id jika ada
            if (!empty($filters['user_id'])) {
                $query .= " AND user_id = ?";
                $params[] = $filters['user_id'];
                $types .= "i";
            }
            
            // Tambahkan filter tanggal dari jika ada
            if (!empty($filters['date_from'])) {
                $query .= " AND timestamp >= ?";
                $params[] = $filters['date_from'] . " 00:00:00";
                $types .= "s";
            }
            
            // Tambahkan filter tanggal sampai jika ada
            if (!empty($filters['date_to'])) {
                $query .= " AND timestamp <= ?";
                $params[] = $filters['date_to'] . " 23:59:59";
                $types .= "s";
            }
            
            // Siapkan dan jalankan query
            $stmt = $conn->prepare($query);
            
            if (!empty($params)) {
                $stmt->bind_param($types, ...$params);
            }
            
            $result = $stmt->execute();
            $stmt->close();
            
            return $result;
        } catch (Exception $e) {
            // Jika terjadi error, return false
            return false;
        }
    }
}

// Fungsi untuk mendapatkan kategori log aktivitas
if (!function_exists('get_activity_log_categories')) {
    /**
     * Mendapatkan daftar kategori log aktivitas yang tersedia
     * 
     * @return array Array berisi kategori log aktivitas
     */
    function get_activity_log_categories() {
        global $conn;
        
        // Jika koneksi database tidak tersedia, return array default
        if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
            return [
                'settings' => 'Pengaturan',
                'training' => 'Pelatihan',
                'employee' => 'Karyawan',
                'user' => 'Pengguna',
                'login' => 'Login/Logout',
                'other' => 'Lainnya'
            ];
        }
        
        try {
            // Ambil kategori dari database
            $query = "SELECT DISTINCT category FROM activity_logs ORDER BY category";
            $result = $conn->query($query);
            $categories = [];
            
            while ($row = $result->fetch_assoc()) {
                $category = $row['category'];
                
                // Terjemahkan kategori ke bahasa Indonesia
                switch ($category) {
                    case 'settings':
                        $categories[$category] = 'Pengaturan';
                        break;
                    case 'training':
                        $categories[$category] = 'Pelatihan';
                        break;
                    case 'employee':
                        $categories[$category] = 'Karyawan';
                        break;
                    case 'user':
                        $categories[$category] = 'Pengguna';
                        break;
                    case 'login':
                        $categories[$category] = 'Login/Logout';
                        break;
                    default:
                        $categories[$category] = ucfirst($category);
                        break;
                }
            }
            
            // Jika tidak ada kategori, gunakan default
            if (empty($categories)) {
                return [
                    'settings' => 'Pengaturan',
                    'training' => 'Pelatihan',
                    'employee' => 'Karyawan',
                    'user' => 'Pengguna',
                    'login' => 'Login/Logout',
                    'other' => 'Lainnya'
                ];
            }
            
            return $categories;
        } catch (Exception $e) {
            // Jika terjadi error, return array default
            return [
                'settings' => 'Pengaturan',
                'training' => 'Pelatihan',
                'employee' => 'Karyawan',
                'user' => 'Pengguna',
                'login' => 'Login/Logout',
                'other' => 'Lainnya'
            ];
        }
    }
}
?>
