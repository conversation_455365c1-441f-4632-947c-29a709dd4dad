<?php
session_start();
include '../config/config.php';

// Cek autentikasi
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Ambil parameter limit jika ada
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 1000; // Default limit 1000 karyawan

// Base query - hanya ambil karyawan yang belum memiliki akun
$query = "SELECT k.nik, k.nama, k.dept, k.bagian, k.jabatan
          FROM karyawan k
          LEFT JOIN users u ON k.nik = u.nik
          WHERE u.id IS NULL
          LIMIT ?";

try {
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();

    $employees = [];
    while ($row = $result->fetch_assoc()) {
        $employees[] = $row;
    }

    // Hitung total karyawan (untuk informasi)
    $totalQuery = "SELECT COUNT(*) as total FROM karyawan";
    $totalResult = $conn->query($totalQuery);
    $totalRow = $totalResult->fetch_assoc();
    $totalEmployees = $totalRow['total'];

    // Hitung karyawan yang sudah memiliki akun
    $existingQuery = "SELECT COUNT(*) as existing FROM users WHERE nik IN (SELECT nik FROM karyawan)";
    $existingResult = $conn->query($existingQuery);
    $existingRow = $existingResult->fetch_assoc();
    $existingAccounts = $existingRow['existing'];

    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'data' => $employees,
        'count' => count($employees),
        'total_employees' => $totalEmployees,
        'existing_accounts' => $existingAccounts,
        'message' => 'Menampilkan ' . count($employees) . ' karyawan yang belum memiliki akun dari total ' . $totalEmployees . ' karyawan.'
    ]);

} catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
