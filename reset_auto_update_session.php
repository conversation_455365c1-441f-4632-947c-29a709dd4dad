<?php
/**
 * Reset Auto-Update Session Script
 * 
 * Script ini untuk membersihkan session dan flag auto-update
 * jika ada masalah dengan function redeclare atau session stuck
 */

session_start();

echo "<h2>🔄 Reset Auto-Update Session</h2>";

// 1. Clear auto-update related session variables
$cleared_sessions = [];

if (isset($_SESSION['last_auto_update'])) {
    $cleared_sessions[] = 'last_auto_update: ' . $_SESSION['last_auto_update'];
    unset($_SESSION['last_auto_update']);
}

if (isset($_SESSION['auto_update_disabled'])) {
    $cleared_sessions[] = 'auto_update_disabled: ' . ($_SESSION['auto_update_disabled'] ? 'true' : 'false');
    unset($_SESSION['auto_update_disabled']);
}

if (isset($_SESSION['auto_update_notification'])) {
    $cleared_sessions[] = 'auto_update_notification: ' . json_encode($_SESSION['auto_update_notification']);
    unset($_SESSION['auto_update_notification']);
}

if (isset($_SESSION['manual_trigger_result'])) {
    $cleared_sessions[] = 'manual_trigger_result: ' . json_encode($_SESSION['manual_trigger_result']);
    unset($_SESSION['manual_trigger_result']);
}

// 2. Display results
if (empty($cleared_sessions)) {
    echo "<p style='color: green;'>✅ No auto-update session variables found to clear.</p>";
} else {
    echo "<p style='color: orange;'>🧹 Cleared the following session variables:</p>";
    echo "<ul>";
    foreach ($cleared_sessions as $session) {
        echo "<li><code>" . htmlspecialchars($session) . "</code></li>";
    }
    echo "</ul>";
}

// 3. Check current session status
echo "<h3>Current Session Status</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Session Variable</th><th>Value</th></tr>";

$session_vars = [
    'user_id' => $_SESSION['user_id'] ?? 'Not set',
    'user_name' => $_SESSION['user_name'] ?? 'Not set',
    'role_id' => $_SESSION['role_id'] ?? 'Not set',
    'last_auto_update' => $_SESSION['last_auto_update'] ?? 'Not set',
    'auto_update_disabled' => isset($_SESSION['auto_update_disabled']) ? ($_SESSION['auto_update_disabled'] ? 'true' : 'false') : 'Not set',
];

foreach ($session_vars as $var => $value) {
    echo "<tr><td>{$var}</td><td>" . htmlspecialchars($value) . "</td></tr>";
}
echo "</table>";

// 4. Test database connection
echo "<h3>Database Connection Test</h3>";
try {
    include 'config/config.php';
    $test_result = $conn->query("SELECT 1");
    if ($test_result) {
        echo "<p style='color: green;'>✅ Database connection: OK</p>";
    } else {
        echo "<p style='color: red;'>❌ Database connection: FAILED</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 5. Check if auto-update helper is loadable
echo "<h3>Auto-Update Helper Test</h3>";
try {
    // Reset any previous definition check
    if (defined('AUTO_UPDATE_HELPER_LOADED')) {
        echo "<p style='color: blue;'>ℹ️ Auto-update helper already loaded in this session</p>";
    }
    
    // Try to include the helper
    require_once 'config/auto_update_helper.php';
    
    if (function_exists('autoUpdateTrainingStatus')) {
        echo "<p style='color: green;'>✅ Auto-update helper functions: Available</p>";
        
        // Test a simple function call
        if (function_exists('isAutoUpdateRunToday')) {
            $is_run_today = isAutoUpdateRunToday($conn);
            echo "<p style='color: green;'>✅ Function test (isAutoUpdateRunToday): " . ($is_run_today ? 'true' : 'false') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Auto-update helper functions: Not available</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Auto-update helper error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 6. Force disable auto-update for this session
if (isset($_GET['disable'])) {
    $_SESSION['auto_update_disabled'] = true;
    echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0;'>";
    echo "<p><strong>⚠️ Auto-update disabled for this session</strong></p>";
    echo "<p>Auto-update hook will not run until you logout or clear this flag.</p>";
    echo "</div>";
}

// 7. Re-enable auto-update
if (isset($_GET['enable'])) {
    if (isset($_SESSION['auto_update_disabled'])) {
        unset($_SESSION['auto_update_disabled']);
    }
    if (isset($_SESSION['last_auto_update'])) {
        unset($_SESSION['last_auto_update']);
    }
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>";
    echo "<p><strong>✅ Auto-update re-enabled for this session</strong></p>";
    echo "<p>Auto-update hook will run normally on next page load.</p>";
    echo "</div>";
}

// 8. Actions
echo "<h3>Actions</h3>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='?disable=1' style='background-color: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🚫 Disable Auto-Update</a>";
echo "<a href='?enable=1' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>✅ Enable Auto-Update</a>";
echo "<a href='?' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔄 Refresh Status</a>";
echo "</div>";

// 9. Instructions
echo "<h3>Troubleshooting Instructions</h3>";
echo "<div style='background-color: #e7f3ff; padding: 15px; border-left: 4px solid #2196F3; margin: 10px 0;'>";
echo "<h4>If you're getting 'Cannot redeclare function' error:</h4>";
echo "<ol>";
echo "<li><strong>Clear session:</strong> Click 'Disable Auto-Update' above</li>";
echo "<li><strong>Restart browser:</strong> Close all browser tabs and restart</li>";
echo "<li><strong>Clear PHP opcache:</strong> Restart web server if using opcache</li>";
echo "<li><strong>Check includes:</strong> Make sure you're using require_once instead of include</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #6c757d; margin: 10px 0;'>";
echo "<h4>File Inclusion Best Practices:</h4>";
echo "<ul>";
echo "<li>Use <code>require_once</code> instead of <code>include</code></li>";
echo "<li>Auto-update helper has built-in inclusion guard</li>";
echo "<li>Session variables are properly managed</li>";
echo "<li>Functions are only declared once per PHP process</li>";
echo "</ul>";
echo "</div>";

// 10. Quick links
echo "<h3>Quick Links</h3>";
echo "<ul>";
echo "<li><a href='test_auto_update.php'>🧪 Test Auto-Update System</a></li>";
echo "<li><a href='admin/auto_update_monitor.php'>📊 Auto-Update Monitor Dashboard</a></li>";
echo "<li><a href='view/login.php'>🔐 Login Page</a></li>";
echo "</ul>";

// 11. Cleanup option
echo "<h3>Cleanup</h3>";
echo "<p>After troubleshooting, you can delete this file:</p>";
echo "<code>rm " . __FILE__ . "</code>";

if (isset($conn)) {
    $conn->close();
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h2 {
    color: #333;
    border-bottom: 2px solid #0066cc;
    padding-bottom: 10px;
}

h3 {
    color: #0066cc;
    margin-top: 30px;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

code {
    background-color: #f4f4f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
