/**
 * AJAX Card Management System - JavaScript
 * 
 * Provides AJAX functionality for the RFID card management system
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    let currentPage = 1;
    let currentSearch = '';
    let currentDept = '';
    let searchTimeout;
    const loadingSpinner = document.getElementById('loading_spinner');
    
    // Elements
    const searchInput = document.querySelector('input[name="search"]');
    const deptSelect = document.querySelector('select[name="dept"]');
    const tableContainer = document.querySelector('.table-responsive');
    const paginationContainer = document.querySelector('.pagination-container');
    
    // Initialize tooltips
    function initTooltips() {
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }
    
    // Load data from server
    function loadData(page = 1, search = '', dept = '') {
        // Show loading spinner
        if (loadingSpinner) loadingSpinner.classList.remove('d-none');
        
        // Update current values
        currentPage = page;
        currentSearch = search;
        currentDept = dept;
        
        // Build URL with parameters
        const url = `get_card_data.php?page=${page}&search=${encodeURIComponent(search)}&dept=${encodeURIComponent(dept)}`;
        
        // Make AJAX request
        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateTable(data.data);
                    updatePagination(data.pagination);
                    updateDepartmentFilter(data.departments);
                } else {
                    showNotification('Error loading data: ' + data.message, 'danger');
                }
                
                // Hide loading spinner
                if (loadingSpinner) loadingSpinner.classList.add('d-none');
            })
            .catch(error => {
                console.error('Error loading data:', error);
                showNotification('Error loading data. Please try again.', 'danger');
                
                // Hide loading spinner
                if (loadingSpinner) loadingSpinner.classList.add('d-none');
            });
    }
    
    // Update table with new data
    function updateTable(data) {
        if (!tableContainer) return;
        
        let html = `
            <table class="table table-bordered table-hover" style="width: 100%; table-layout: fixed;">
                <colgroup>
                    <col style="width: 10%"> <!-- NIK -->
                    <col style="width: 20%"> <!-- Nama -->
                    <col style="width: 12%"> <!-- Departemen -->
                    <col style="width: 12%"> <!-- Bagian -->
                    <col style="width: 12%"> <!-- Jabatan -->
                    <col style="width: 14%"> <!-- Nomor Kartu RFID -->
                    <col style="width: 20%"> <!-- Aksi -->
                </colgroup>
                <thead class="bg-light">
                    <tr>
                        <th>NIK</th>
                        <th>Nama</th>
                        <th>Departemen</th>
                        <th>Bagian</th>
                        <th>Jabatan</th>
                        <th class="text-center">Nomor Kartu RFID</th>
                        <th class="text-center">Aksi</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        if (data.length === 0) {
            html += `
                <tr>
                    <td colspan="7" class="text-center">
                        <div class="py-4">
                            <i class="fas fa-search fa-3x text-gray-300 mb-3"></i>
                            <p class="text-gray-500 mb-0">Tidak ada data karyawan yang ditemukan</p>
                            <p class="text-gray-500">Coba ubah filter pencarian Anda</p>
                        </div>
                    </td>
                </tr>
            `;
        } else {
            data.forEach(karyawan => {
                html += `
                    <tr>
                        <td>${karyawan.nik}</td>
                        <td>${karyawan.nama}</td>
                        <td>${karyawan.dept}</td>
                        <td>${karyawan.bagian}</td>
                        <td>${karyawan.jabatan}</td>
                        <td class="text-center">
                            ${karyawan.card_number ? 
                                `<span class="card-number-badge">${karyawan.card_number}</span>` : 
                                `<span class="text-muted">-</span>`
                            }
                        </td>
                        <td class="text-center">
                            <div class="action-buttons d-flex justify-content-center">
                                <button type="button" class="btn btn-sm btn-primary edit-card me-2" 
                                    data-id="${karyawan.id}" 
                                    data-nik="${karyawan.nik}" 
                                    data-nama="${karyawan.nama}" 
                                    data-card="${karyawan.card_number || ''}" 
                                    data-bs-toggle="tooltip" 
                                    title="Edit Nomor Kartu">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button type="button" class="btn btn-sm btn-info scan-card" 
                                    data-id="${karyawan.id}" 
                                    data-nik="${karyawan.nik}" 
                                    data-nama="${karyawan.nama}" 
                                    data-bs-toggle="tooltip" 
                                    title="Scan Kartu RFID">
                                    <i class="fas fa-id-card"></i> Scan
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        }
        
        html += `
                </tbody>
            </table>
        `;
        
        tableContainer.innerHTML = html;
        
        // Reinitialize event listeners for buttons
        initButtonListeners();
        
        // Initialize tooltips
        initTooltips();
    }
    
    // Update pagination
    function updatePagination(pagination) {
        if (!paginationContainer) return;
        
        if (pagination.total_pages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }
        
        let html = `
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
        `;
        
        // Previous button
        if (pagination.current_page > 1) {
            html += `
                <li class="page-item">
                    <a class="page-link page-prev" href="#" data-page="${pagination.current_page - 1}">
                        <i class="fas fa-chevron-left"></i> Sebelumnya
                    </a>
                </li>
            `;
        }
        
        // Page numbers
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link page-number" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        }
        
        // Next button
        if (pagination.current_page < pagination.total_pages) {
            html += `
                <li class="page-item">
                    <a class="page-link page-next" href="#" data-page="${pagination.current_page + 1}">
                        Selanjutnya <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;
        }
        
        html += `
                </ul>
            </nav>
        `;
        
        paginationContainer.innerHTML = html;
        
        // Add event listeners to pagination links
        document.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = parseInt(this.getAttribute('data-page'));
                loadData(page, currentSearch, currentDept);
            });
        });
    }
    
    // Update department filter
    function updateDepartmentFilter(departments) {
        if (!deptSelect) return;
        
        // Save current selection
        const currentSelection = deptSelect.value;
        
        // Clear options except the first one
        while (deptSelect.options.length > 1) {
            deptSelect.remove(1);
        }
        
        // Add department options
        departments.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept;
            option.textContent = dept;
            if (dept === currentSelection) {
                option.selected = true;
            }
            deptSelect.appendChild(option);
        });
    }
    
    // Initialize button listeners
    function initButtonListeners() {
        // Edit card buttons
        document.querySelectorAll('.edit-card').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const nik = this.getAttribute('data-nik');
                const nama = this.getAttribute('data-nama');
                const card = this.getAttribute('data-card');
                
                document.getElementById('edit_karyawan_id').value = id;
                document.getElementById('edit_nik').value = nik;
                document.getElementById('edit_nama').value = nama;
                document.getElementById('edit_card_number').value = card;
                
                // Show modal
                const editCardModal = new bootstrap.Modal(document.getElementById('editCardModal'));
                editCardModal.show();
            });
        });
        
        // Scan card buttons
        document.querySelectorAll('.scan-card').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const nik = this.getAttribute('data-nik');
                const nama = this.getAttribute('data-nama');
                
                document.getElementById('scan_karyawan_id').value = id;
                
                // Update selected employee info
                const selectedEmployee = document.getElementById('selected_employee');
                const selectedEmployeeInfo = document.getElementById('selected_employee_info');
                
                if (selectedEmployee && selectedEmployeeInfo) {
                    selectedEmployeeInfo.textContent = nama + ' (' + nik + ')';
                    selectedEmployee.classList.remove('d-none');
                }
                
                // Focus on scan input
                setTimeout(() => {
                    document.getElementById('scan_card_number').focus();
                }, 500);
                
                // Scroll to scan form
                document.querySelector('.card:first-of-type').scrollIntoView({ behavior: 'smooth' });
            });
        });
    }
    
    // Show notification
    function showNotification(message, type = 'info') {
        const notificationArea = document.getElementById('notification_area');
        if (!notificationArea) return;
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.role = 'alert';
        
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        notificationArea.appendChild(alertDiv);
        
        // Auto dismiss after 5 seconds
        setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => {
                notificationArea.removeChild(alertDiv);
            }, 150);
        }, 5000);
    }
    
    // Add event listeners
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                loadData(1, this.value, currentDept);
            }, 500);
        });
    }
    
    if (deptSelect) {
        deptSelect.addEventListener('change', function() {
            loadData(1, currentSearch, this.value);
        });
    }
    
    // Handle form submissions
    const scanForm = document.getElementById('scan-form');
    if (scanForm) {
        scanForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading spinner
            if (loadingSpinner) loadingSpinner.classList.remove('d-none');
            
            const formData = new FormData(this);
            
            fetch('manage_card_numbers.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                // Extract success or error message from the response
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const successAlert = doc.querySelector('.alert-success');
                const errorAlert = doc.querySelector('.alert-danger');
                
                if (successAlert) {
                    showNotification(successAlert.textContent, 'success');
                    // Reload data
                    loadData(currentPage, currentSearch, currentDept);
                    // Clear form
                    document.getElementById('scan_card_number').value = '';
                    document.getElementById('scan_karyawan_id').value = '';
                    document.getElementById('selected_employee').classList.add('d-none');
                } else if (errorAlert) {
                    showNotification(errorAlert.textContent, 'danger');
                } else {
                    showNotification('Operasi selesai.', 'info');
                }
                
                // Hide loading spinner
                if (loadingSpinner) loadingSpinner.classList.add('d-none');
            })
            .catch(error => {
                console.error('Error submitting form:', error);
                showNotification('Error submitting form. Please try again.', 'danger');
                
                // Hide loading spinner
                if (loadingSpinner) loadingSpinner.classList.add('d-none');
            });
        });
    }
    
    // Initial data load
    loadData();
});
