<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Judul halaman
$pageTitle = "Kelola Departemen";
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    .container-form {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .card-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .btn-add {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .btn-edit {
        background-color: #2196F3;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 5px;
    }
    
    .btn-delete {
        background-color: #F44336;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
    }
    
    table {
        width: 100%;
        border-collapse: collapse;
    }
    
    table th, table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    
    table th {
        background-color:rgb(191, 0, 0);
    }
    
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }
    
    .modal-content {
        background-color: white;
        margin: 10% auto;
        padding: 20px;
        border-radius: 8px;
        width: 50%;
        max-width: 500px;
    }
    
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .modal-close {
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
    }
    
    .form-group input, .form-group textarea, .form-group select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    .form-group textarea {
        height: 100px;
    }
    
    .form-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
    }
    
    .btn-submit {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
        margin-left: 10px;
    }
    
    .btn-cancel {
        background-color: #ccc;
        color: black;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .alert {
        padding: 10px;
        margin-bottom: 15px;
        border-radius: 4px;
    }
    
    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .hidden {
        display: none;
    }
    .jarak-form {
        height:80px;
    }
</style>

<body>
<?php include '../config/navbarb.php'; ?>
<div class="jarak-form"></div>
<div class="container-form">
    <button style="border-radius: 10px; margin-bottom:10px;">
        <a href="manage_organization.php" style="text-decoration:none; color:white">Kembali</a>
    </button>
    <div class="card">
        <div class="card-header">
            <h2><?php echo $pageTitle; ?></h2>
            <button class="btn-add" id="btnAddDepartment">Tambah Departemen</button>
        </div>
        <div class="card-body">
            <div id="alertContainer" class="hidden"></div>
            <table id="departmentsTable">
                <thead>
                    <tr>
                        <th>Kode</th>
                        <th>Nama</th>
                        <th>Deskripsi</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal for adding/editing department -->
<div id="departmentModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">Tambah Departemen</h3>
            <span class="modal-close">&times;</span>
        </div>
        <form id="departmentForm">
            <input type="hidden" id="departmentId" name="id">
            <div class="form-group">
                <label for="departmentCode">Kode Departemen *</label>
                <input type="text" id="departmentCode" name="code" required maxlength="10">
            </div>
            <div class="form-group">
                <label for="departmentName">Nama Departemen *</label>
                <input type="text" id="departmentName" name="name" required maxlength="100">
            </div>
            <div class="form-group">
                <label for="departmentDescription">Deskripsi</label>
                <textarea id="departmentDescription" name="description"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn-cancel" id="btnCancelDepartment">Batal</button>
                <button type="submit" class="btn-submit">Simpan</button>
            </div>
        </form>
    </div>
</div>

<!-- Modal for confirmation -->
<div id="confirmationModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Konfirmasi</h3>
            <span class="modal-close">&times;</span>
        </div>
        <p id="confirmationMessage">Apakah Anda yakin ingin menghapus departemen ini?</p>
        <div class="form-actions">
            <button type="button" class="btn-cancel" id="btnCancelConfirmation">Batal</button>
            <button type="button" class="btn-submit" id="btnConfirm">Ya, Hapus</button>
        </div>
    </div>
</div>

<script>
    // Global variables
    let departments = [];
    let currentDepartmentId = null;
    
    // DOM elements
    const departmentsTable = document.getElementById('departmentsTable');
    const departmentModal = document.getElementById('departmentModal');
    const confirmationModal = document.getElementById('confirmationModal');
    const departmentForm = document.getElementById('departmentForm');
    const alertContainer = document.getElementById('alertContainer');
    
    // Buttons
    const btnAddDepartment = document.getElementById('btnAddDepartment');
    const btnCancelDepartment = document.getElementById('btnCancelDepartment');
    const btnCancelConfirmation = document.getElementById('btnCancelConfirmation');
    const btnConfirm = document.getElementById('btnConfirm');
    
    // Modal close buttons
    const modalCloseButtons = document.querySelectorAll('.modal-close');
    
    // Load departments on page load
    document.addEventListener('DOMContentLoaded', loadDepartments);
    
    // Event listeners
    btnAddDepartment.addEventListener('click', showAddDepartmentModal);
    btnCancelDepartment.addEventListener('click', closeDepartmentModal);
    btnCancelConfirmation.addEventListener('click', closeConfirmationModal);
    departmentForm.addEventListener('submit', saveDepartment);
    
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            departmentModal.style.display = 'none';
            confirmationModal.style.display = 'none';
        });
    });
    
    // Functions
    function loadDepartments() {
        fetch('api/get_organization_data.php?type=departments')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    departments = data.data.departments;
                    renderDepartmentsTable();
                } else {
                    showAlert('danger', 'Error loading departments: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'Error loading departments: ' + error.message);
            });
    }
    
    function renderDepartmentsTable() {
        const tbody = departmentsTable.querySelector('tbody');
        tbody.innerHTML = '';
        
        if (departments.length === 0) {
            const tr = document.createElement('tr');
            tr.innerHTML = '<td colspan="4" style="text-align: center;">Tidak ada data departemen</td>';
            tbody.appendChild(tr);
            return;
        }
        
        departments.forEach(department => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${department.code}</td>
                <td>${department.name}</td>
                <td>${department.description || '-'}</td>
                <td>
                    <button class="btn-edit" data-id="${department.id}">Edit</button>
                    <button class="btn-delete" data-id="${department.id}">Hapus</button>
                </td>
            `;
            tbody.appendChild(tr);
        });
        
        // Add event listeners to edit and delete buttons
        const editButtons = tbody.querySelectorAll('.btn-edit');
        const deleteButtons = tbody.querySelectorAll('.btn-delete');
        
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const departmentId = this.getAttribute('data-id');
                showEditDepartmentModal(departmentId);
            });
        });
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const departmentId = this.getAttribute('data-id');
                showDeleteConfirmation(departmentId);
            });
        });
    }
    
    function showAddDepartmentModal() {
        currentDepartmentId = null;
        document.getElementById('modalTitle').textContent = 'Tambah Departemen';
        departmentForm.reset();
        document.getElementById('departmentId').value = '';
        departmentModal.style.display = 'block';
    }
    
    function showEditDepartmentModal(departmentId) {
        const department = departments.find(d => d.id == departmentId);
        if (!department) return;
        
        currentDepartmentId = departmentId;
        document.getElementById('modalTitle').textContent = 'Edit Departemen';
        document.getElementById('departmentId').value = department.id;
        document.getElementById('departmentCode').value = department.code;
        document.getElementById('departmentName').value = department.name;
        document.getElementById('departmentDescription').value = department.description || '';
        
        departmentModal.style.display = 'block';
    }
    
    function closeDepartmentModal() {
        departmentModal.style.display = 'none';
    }
    
    function showDeleteConfirmation(departmentId) {
        const department = departments.find(d => d.id == departmentId);
        if (!department) return;
        
        currentDepartmentId = departmentId;
        document.getElementById('confirmationMessage').textContent = `Apakah Anda yakin ingin menghapus departemen "${department.name}" (${department.code})?`;
        
        btnConfirm.onclick = function() {
            deleteDepartment(departmentId);
        };
        
        confirmationModal.style.display = 'block';
    }
    
    function closeConfirmationModal() {
        confirmationModal.style.display = 'none';
    }
    
    function saveDepartment(event) {
        event.preventDefault();
        
        const formData = new FormData(departmentForm);
        formData.append('type', 'department');
        formData.append('action', currentDepartmentId ? 'update' : 'create');
        
        fetch('api/manage_organization_data.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                closeDepartmentModal();
                loadDepartments();
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Error saving department: ' + error.message);
        });
    }
    
    function deleteDepartment(departmentId) {
        const formData = new FormData();
        formData.append('type', 'department');
        formData.append('action', 'delete');
        formData.append('id', departmentId);
        
        fetch('api/manage_organization_data.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                closeConfirmationModal();
                loadDepartments();
            } else {
                showAlert('danger', data.message);
                closeConfirmationModal();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Error deleting department: ' + error.message);
            closeConfirmationModal();
        });
    }
    
    function showAlert(type, message) {
        alertContainer.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        alertContainer.classList.remove('hidden');
        
        // Hide alert after 5 seconds
        setTimeout(() => {
            alertContainer.classList.add('hidden');
        }, 5000);
    }
</script>

<?php include '../config/footer.php'; ?>
</body>
</html>
