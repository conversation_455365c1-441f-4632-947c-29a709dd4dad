<?php
/**
 * Admin Calendar Management - Man<PERSON><PERSON><PERSON> Kalender Training untuk Admin
 */

include '../config/config.php';
include 'security.php';

// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Admin';

// Get statistics
$stats = [];

// Count offline trainings
$query = "SELECT COUNT(*) as count FROM offline_training";
$result = $conn->query($query);
$stats['offline_total'] = $result->fetch_assoc()['count'];

// Count online trainings (training_submissions)
$query = "SELECT COUNT(*) as count FROM training_submissions WHERE status = 'Approved'";
$result = $conn->query($query);
$stats['online_total'] = $result->fetch_assoc()['count'];

// Count upcoming trainings
$query = "SELECT COUNT(*) as count FROM offline_training WHERE start_date >= CURDATE()";
$result = $conn->query($query);
$stats['offline_upcoming'] = $result->fetch_assoc()['count'];

$query = "SELECT COUNT(*) as count FROM training_submissions
          WHERE status = 'Approved' AND start_date >= CURDATE()";
$result = $conn->query($query);
$stats['online_upcoming'] = $result->fetch_assoc()['count'];

?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manajemen Kalender Training - Admin</title>
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDMyIDMyIj4KICA8cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9IiM5ZDAwMDAiLz4KICA8dGV4dCB4PSIxNiIgeT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IndoaXRlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIGZvbnQtd2VpZ2h0PSJib2xkIj5UPC90ZXh0Pgo8L3N2Zz4=">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/calendar-multiday.css" rel="stylesheet">
    <link href="css/search-dropdown.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #9d0000;
            --primary-dark: #7f0000;
            --primary-light: rgba(157, 0, 0, 0.1);
            --success-color: #4CAF50;
            --info-color: #2196F3;
            --warning-color: #ff9800;
            --danger-color: #f44336;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }

        .stat-card {
            text-align: center;
            padding: 2rem 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(157, 0, 0, 0.3);
        }

        .calendar-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .calendar-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .calendar-nav button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            margin: 0 10px;
            transition: background 0.3s ease;
        }

        .calendar-nav button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background-color: #e9ecef;
        }

        .calendar-day-header {
            background: var(--primary-light);
            color: var(--primary-color);
            padding: 1rem;
            text-align: center;
            font-weight: 600;
        }

        .calendar-day {
            background: white;
            min-height: 120px;
            padding: 10px;
            position: relative;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .calendar-day:hover {
            background-color: #f8f9fa;
        }

        .calendar-day.today {
            background: var(--primary-light);
            border: 2px solid var(--primary-color);
        }

        .calendar-day.other-month {
            background: #f8f9fa;
            color: #adb5bd;
        }

        .calendar-event {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.75rem;
            margin-bottom: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .calendar-event:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .calendar-event.offline {
            background: var(--success-color);
        }

        .calendar-event.online {
            background: var(--info-color);
        }

        /* Hidden event styling */
        .calendar-event.event-hidden {
            opacity: 0.5;
            background: linear-gradient(135deg, #6c757d, #495057) !important;
            border: 2px dashed #adb5bd;
            position: relative;
        }

        .calendar-event.event-hidden::before {
            content: "🔒";
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .calendar-event.event-hidden:hover {
            opacity: 0.8;
            transform: scale(1.02);
        }

        .add-event-btn {
            position: absolute;
            bottom: 5px;
            right: 5px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            font-size: 0.7rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .calendar-day:hover .add-event-btn {
            opacity: 1;
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(157, 0, 0, 0.25);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        /* User view mode styles */
        .training-detail-user .detail-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .training-detail-user .detail-item {
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
        }

        .training-detail-user .training-type-badge {
            text-align: center;
        }

        .training-detail-user .participants-list {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
        }

        .training-detail-user .participant-item {
            padding: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        .training-detail-user .participant-item:last-child {
            border-bottom: none;
        }

        /* User mode calendar adjustments */
        .user-view-mode .add-event-btn {
            display: none !important;
        }

        .user-view-mode .calendar-event {
            cursor: default !important;
        }

        @media (max-width: 768px) {
            .calendar-day {
                min-height: 80px;
                padding: 5px;
            }

            .calendar-event {
                font-size: 0.65rem;
                padding: 2px 4px;
            }

            .stat-number {
                font-size: 2rem;
            }

            .training-detail-user .detail-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-calendar-alt me-2"></i>
                Manajemen Kalender Training
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="../view/logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="stat-number text-success"><?= $stats['offline_total'] ?></div>
                    <div class="stat-label">Total Training Internal</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="stat-number text-info"><?= $stats['online_total'] ?></div>
                    <div class="stat-label">Total Training Eksternal</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="stat-number text-warning"><?= $stats['offline_upcoming'] ?></div>
                    <div class="stat-label">Offline Mendatang</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="stat-number text-primary"><?= $stats['online_upcoming'] ?></div>
                    <div class="stat-label">Online Mendatang</div>
                </div>
            </div>
        </div>

        <!-- Calendar View Mode Info -->
        <div class="alert alert-info" id="viewModeInfo" style="display: none;">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Mode User:</strong> Menampilkan kalender seperti yang dilihat oleh user (tanpa tombol admin dan training tersembunyi).
            <button class="btn btn-sm btn-outline-info ms-2" onclick="toggleCalendarView()">
                <i class="fas fa-cog me-1"></i>Kembali ke Mode Admin
            </button>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-tools me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <!-- Search Training Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-muted mb-3">
                                    <i class="fas fa-search me-2"></i>Cari Training
                                </h6>
                                <div id="trainingSearchDropdown"></div>
                            </div>
                        </div>

                        <hr>

                        <!-- Action Buttons -->
                        <div class="row">
                            <!-- <div class="col-md-2 mb-2">
                                <button class="btn btn-primary w-100" onclick="addTraining('offline')">
                                    <i class="fas fa-plus me-2"></i>Buat Training Internal
                                </button>
                            </div>
                            <div class="col-md-2 mb-2">
                                <button class="btn btn-primary w-100" onclick="addTraining('online')">
                                    <i class="fas fa-plus me-2"></i>Buat Training Eksternal
                                </button>
                            </div> -->
                            <div class="col-md-2 mb-2">
                                <button class="btn btn-primary w-100" onclick="showSelectTrainingModal('offline')">
                                    <i class="fas fa-list me-2"></i>Pilih Training Internal
                                </button>
                            </div>
                            <div class="col-md-2 mb-2">
                                <button class="btn btn-primary w-100" onclick="showSelectTrainingModal('online')">
                                    <i class="fas fa-list me-2"></i>Pilih Training Eksternal
                                </button>
                            </div>
                            <div class="col-md-2 mb-2">
                                <button class="btn btn-outline-primary w-100" onclick="exportCalendar()">
                                    <i class="fas fa-download me-2"></i>Export Kalender
                                </button>
                            </div>
                            <div class="col-md-2 mb-2">
                                <button class="btn btn-outline-secondary w-100" onclick="viewReports()">
                                    <i class="fas fa-chart-bar me-2"></i>Laporan Training
                                </button>
                            </div>
                            <div class="col-md-2 mb-2">
                                <button class="btn btn-info w-100" onclick="toggleCalendarView()" id="viewToggleBtn">
                                    <i class="fas fa-eye me-2" id="viewToggleIcon"></i>
                                    <span id="viewToggleText">Mode User</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Calendar -->
        <div class="row">
            <div class="col-12">
                <div class="calendar-container">
                    <div class="calendar-header">
                        <div class="calendar-nav">
                            <button id="prevMonth" type="button">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <h3 class="mb-0 mx-3" id="calendarTitle">Loading...</h3>
                            <button id="nextMonth" type="button">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <div>
                            <button id="todayBtn" class="btn btn-light btn-sm">
                                <i class="fas fa-calendar-day me-1"></i>Hari Ini
                            </button>
                        </div>
                    </div>
                    <div class="calendar-grid" id="calendarGrid">
                        <!-- Calendar will be rendered here -->
                    </div>
                    <div class="legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: var(--success-color);"></div>
                            <span>Training Internal</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: var(--info-color);"></div>
                            <span>Training Eksternal</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: linear-gradient(135deg, #6c757d, #495057); border: 2px dashed #adb5bd; opacity: 0.5;"></div>
                            <span>🔒 Training Disembunyikan</span>
                        </div>
                    </div>

                    <!-- Toggle untuk menampilkan hidden events -->
                    <div class="mt-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="showHiddenEvents" checked>
                            <label class="form-check-label" for="showHiddenEvents">
                                <i class="fas fa-eye-slash"></i> Tampilkan training yang disembunyikan
                            </label>
                        </div>
                        <small class="text-muted">Matikan untuk menyembunyikan training yang di-hide dari tampilan kalender</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Training Detail Modal -->
    <div class="modal fade" id="trainingModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Detail Training</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Select Existing Training Modal -->
    <div class="modal fade" id="selectTrainingModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="selectModalTitle">Pilih Training</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <input type="text" class="form-control" id="searchTraining" placeholder="Cari training...">
                    </div>
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Pilih</th>
                                    <th>Judul Training</th>
                                    <th>Tanggal</th>
                                    <th>Lokasi</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="trainingListBody">
                                <!-- Training list will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Training Modal -->
    <div class="modal fade" id="addTrainingModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalTitle">Tambah Training</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="trainingForm">
                        <input type="hidden" id="trainingId" name="training_id">
                        <input type="hidden" id="trainingType" name="training_type">

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="trainingTitle" class="form-label">Judul Training *</label>
                                <input type="text" class="form-control" id="trainingTitle" name="training_title" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="trainingDate" class="form-label">Tanggal Mulai *</label>
                                <input type="date" class="form-control" id="trainingDate" name="training_date" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="trainingEndDate" class="form-label">Tanggal Selesai</label>
                                <input type="date" class="form-control" id="trainingEndDate" name="training_end_date">
                                <small class="text-muted">Kosongkan jika training hanya 1 hari</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="trainingTimeStart" class="form-label">Waktu Mulai</label>
                                <input type="time" class="form-control" id="trainingTimeStart" name="training_time_start">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="trainingTimeEnd" class="form-label">Waktu Selesai</label>
                                <input type="time" class="form-control" id="trainingTimeEnd" name="training_time_end">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="trainingLocation" class="form-label">Lokasi</label>
                                <input type="text" class="form-control" id="trainingLocation" name="training_location">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="trainingTrainer" class="form-label">Trainer/Instruktur</label>
                                <input type="text" class="form-control" id="trainingTrainer" name="training_trainer">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="maxParticipants" class="form-label">Maksimal Peserta</label>
                                <input type="number" class="form-control" id="maxParticipants" name="max_participants" min="1">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="trainingStatus" class="form-label">Status</label>
                                <select class="form-control" id="trainingStatus" name="training_status">
                                    <option value="Active">Active</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Cancelled">Cancelled</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="trainingDescription" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="trainingDescription" name="training_description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" onclick="saveTraining()">Simpan</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/search-training.js"></script>
    <script src="calendar_admin.js"></script>

    <script>
        // Initialize search dropdown when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize training search dropdown
            const searchDropdown = new TrainingSearchDropdown('trainingSearchDropdown', {
                placeholder: 'Cari training berdasarkan judul, trainer, atau lokasi...',
                minLength: 2,
                maxResults: 8,
                showFilters: true,
                onSelect: function(training) {
                    handleTrainingSelection(training);
                }
            });

            // Store reference globally for other functions
            window.trainingSearchDropdown = searchDropdown;
        });

        /**
         * Handle training selection from search dropdown
         */
        function handleTrainingSelection(training) {
            console.log('Training selected:', training);

            // Navigate to the training date in calendar
            if (training.date) {
                // Extract date from formatted date string
                const dateMatch = training.date.match(/(\d{1,2})\s+(\w+)\s+(\d{4})/);
                if (dateMatch) {
                    const [, day, monthName, year] = dateMatch;
                    const monthNames = {
                        'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
                        'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
                    };

                    const targetDate = new Date(parseInt(year), monthNames[monthName], parseInt(day));

                    // Navigate calendar to the target month
                    if (window.calendar && window.calendar.navigateToDate) {
                        window.calendar.navigateToDate(targetDate);
                    } else {
                        // Fallback: set current date and reload calendar
                        window.currentDate = targetDate;
                        if (typeof loadCalendar === 'function') {
                            loadCalendar();
                        }
                    }

                    // Show success message
                    showNotification(`Navigasi ke training "${training.title}" pada ${training.date}`, 'success');

                    // Highlight the training after a short delay
                    setTimeout(() => {
                        highlightTrainingInCalendar(training.id, training.type);
                    }, 500);
                } else {
                    // If date parsing fails, just show the training detail
                    showTrainingDetail(training.id, training.type);
                }
            } else {
                // If no date, just show the training detail
                showTrainingDetail(training.id, training.type);
            }
        }

        /**
         * Highlight training in calendar
         */
        function highlightTrainingInCalendar(trainingId, trainingType) {
            // Find and highlight the training event in calendar
            const eventSelector = `[data-training-id="${trainingId}"][data-training-type="${trainingType}"]`;
            const eventElement = document.querySelector(eventSelector);

            if (eventElement) {
                // Add highlight class
                eventElement.classList.add('training-highlighted');

                // Scroll to the event
                eventElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Remove highlight after 3 seconds
                setTimeout(() => {
                    eventElement.classList.remove('training-highlighted');
                }, 3000);

                // Auto-click to show details
                setTimeout(() => {
                    eventElement.click();
                }, 1000);
            }
        }

        /**
         * Show training detail directly
         */
        function showTrainingDetail(trainingId, trainingType) {
            // Use existing function to show training detail
            if (typeof displayTrainingDetail === 'function') {
                // Fetch training detail and display
                fetch(`api/get_training_detail_simple.php?id=${trainingId}&type=${trainingType}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayTrainingDetail(data.training);
                        } else {
                            showNotification('Gagal memuat detail training', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching training detail:', error);
                        showNotification('Terjadi kesalahan saat memuat detail training', 'error');
                    });
            }
        }

        /**
         * Show notification
         */
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>

    <style>
        /* Additional styles for search integration */
        .training-highlighted {
            animation: highlightPulse 2s ease-in-out;
            border: 2px solid #ffc107 !important;
            box-shadow: 0 0 15px rgba(255, 193, 7, 0.5) !important;
        }

        @keyframes highlightPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 15px rgba(255, 193, 7, 0.5);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 25px rgba(255, 193, 7, 0.8);
            }
        }

        /* Search dropdown container adjustments */
        #trainingSearchDropdown {
            max-width: 600px;
            margin: 0 auto;
        }

        @media (max-width: 768px) {
            #trainingSearchDropdown {
                max-width: 100%;
            }
        }
    </style>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
