<?php
/**
 * Create Quiz Page for Admin/Trainer
 * This page allows admins/trainers to create quizzes for training classes
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if the quiz tables exist
$quiz_tables_exist = true;
$required_tables = [
    'training_quizzes',
    'training_questions',
    'training_question_options',
    'training_quiz_attempts',
    'training_quiz_answers'
];

foreach ($required_tables as $table) {
    $check_table_query = "SHOW TABLES LIKE '$table'";
    $table_result = $conn->query($check_table_query);
    if (!$table_result || $table_result->num_rows == 0) {
        $quiz_tables_exist = false;
        break;
    }
}

// Check if class_id is provided
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$class = null;

if ($class_id > 0) {
    // Get class information
    $class_query = "SELECT c.*, t.training_topic
                   FROM training_classes c
                   JOIN training_submissions t ON c.training_id = t.id
                   WHERE c.id = ?";
    $stmt = $conn->prepare($class_query);
    $stmt->bind_param("i", $class_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $class = $result->fetch_assoc();
    $stmt->close();
}

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_quiz'])) {
    // Get form data
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $instructions = trim($_POST['instructions']);
    $time_limit = !empty($_POST['time_limit']) ? intval($_POST['time_limit']) : null;
    $passing_score = !empty($_POST['passing_score']) ? intval($_POST['passing_score']) : null;
    $is_published = isset($_POST['is_published']) ? 1 : 0;
    $allow_multiple_attempts = isset($_POST['allow_multiple_attempts']) ? 1 : 0;
    $max_attempts = !empty($_POST['max_attempts']) ? intval($_POST['max_attempts']) : null;
    $randomize_questions = isset($_POST['randomize_questions']) ? 1 : 0;

    // Process answer display options
    $answer_display_option = $_POST['answer_display_option'] ?? 'no_display';
    $show_correct_answers = ($answer_display_option === 'show_correct') ? 1 : 0;
    $hide_all_answers = ($answer_display_option === 'hide_all') ? 1 : 0;

    // Validate input
    if (empty($title)) {
        $error_message = "Judul kuis harus diisi.";
    } elseif ($class_id <= 0 || !$class) {
        $error_message = "Kelas tidak valid.";
    } else {
        // Insert quiz
        $insert_query = "INSERT INTO training_quizzes (
                        class_id, title, description, instructions, time_limit,
                        passing_score, is_published, allow_multiple_attempts, max_attempts,
                        randomize_questions, show_correct_answers, hide_all_answers, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param(
            "isssiiiiiiiii",
            $class_id, $title, $description, $instructions, $time_limit,
            $passing_score, $is_published, $allow_multiple_attempts, $max_attempts,
            $randomize_questions, $show_correct_answers, $hide_all_answers, $user_id
        );

        if ($stmt->execute()) {
            $quiz_id = $stmt->insert_id;
            $success_message = "Kuis berhasil dibuat. Sekarang Anda dapat menambahkan pertanyaan.";

            // Redirect to add questions page
            header("Location: edit_quiz.php?id=" . $quiz_id);
            exit();
        } else {
            $error_message = "Gagal membuat kuis: " . $conn->error;
        }
        $stmt->close();
    }
}

// Get available classes
$classes = [];
$classes_query = "SELECT c.id, c.title, t.training_topic
                 FROM training_classes c
                 JOIN training_submissions t ON c.training_id = t.id
                 WHERE c.status = 'active'
                 ORDER BY c.created_at DESC";
$result = $conn->query($classes_query);

if ($result) {
    while ($row = $result->fetch_assoc()) {
        $classes[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .form-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .form-section h3 {
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .quiz-options {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }

    .setup-required {
        background-color: #f8d7da;
        color: #721c24;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: center;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1><i class="fas fa-question-circle"></i> Buat Kuis Baru</h1>
            <p class="text-muted">Buat kuis untuk mengevaluasi pemahaman peserta training</p>

            <?php if (!$quiz_tables_exist): ?>
                <div class="setup-required">
                    <h3><i class="fas fa-exclamation-triangle"></i> Pengaturan Kuis Diperlukan</h3>
                    <p>Tabel database untuk fitur kuis belum dibuat. Silakan jalankan script setup terlebih dahulu.</p>
                    <a href="../config/create_quiz_tables.php" class="btn btn-primary">Setup Tabel Kuis</a>
                </div>
            <?php else: ?>
                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?= $success_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $error_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="form-section">
                    <h3>Informasi Kuis</h3>

                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="class_id" class="form-label">Kelas Training</label>
                            <?php if ($class): ?>
                                <input type="hidden" name="class_id" value="<?= $class_id ?>">
                                <div class="form-control bg-light"><?= htmlspecialchars($class['title']) ?> - <?= htmlspecialchars($class['training_topic']) ?></div>
                            <?php else: ?>
                                <select class="form-select" id="class_id" name="class_id" required>
                                    <option value="">Pilih Kelas Training</option>
                                    <?php foreach ($classes as $class_item): ?>
                                        <option value="<?= $class_item['id'] ?>" <?= ($class_id == $class_item['id']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($class_item['title']) ?> - <?= htmlspecialchars($class_item['training_topic']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">Pilih kelas training tempat kuis ini akan ditambahkan</div>
                            <?php endif; ?>
                        </div>

                        <div class="mb-3">
                            <label for="title" class="form-label">Judul Kuis</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi Kuis</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            <div class="form-text">Deskripsi singkat tentang kuis ini</div>
                        </div>
                        <div class="mb-3">
    <label for="instructions" class="form-label">Instruksi Pengerjaan</label>
    <textarea class="form-control" id="instructions" name="instructions" rows="3"><?= htmlspecialchars($quiz['instructions'] ?? 'Silakan jawab pertanyaan dengan cermat. Anda memiliki waktu terbatas untuk menyelesaikan kuis ini.') ?></textarea>
    <div class="form-text">Instruksi untuk peserta tentang cara mengerjakan kuis</div>
</div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="time_limit" class="form-label">Batas Waktu (menit)</label>
                                    <input type="number" class="form-control" id="time_limit" name="time_limit" min="0">
                                    <div class="form-text">Biarkan kosong jika tidak ada batas waktu</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="passing_score" class="form-label">Nilai Kelulusan (%)</label>
                                    <input type="number" class="form-control" id="passing_score" name="passing_score" min="0" max="100">
                                    <div class="form-text">Nilai minimum untuk lulus kuis (dalam persen)</div>
                                </div>
                            </div>
                        </div>

                        <div class="quiz-options">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_published" name="is_published">
                                <label class="form-check-label" for="is_published">
                                    Publikasikan Kuis
                                </label>
                                <div class="form-text">Jika dicentang, kuis akan langsung terlihat oleh peserta</div>
                            </div>

                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="allow_multiple_attempts" name="allow_multiple_attempts">
                                <label class="form-check-label" for="allow_multiple_attempts">
                                    Izinkan Percobaan Berulang
                                </label>
                                <div class="form-text">Peserta dapat mengerjakan kuis lebih dari sekali</div>
                            </div>

                            <div class="form-group max-attempts-container" style="display: none; padding: 10px; margin-top: 10px; background-color: #f8f9fa; border-radius: 5px; border-left: 3px solid #6c757d;">
                                <label for="max_attempts" class="form-label">Jumlah Percobaan Maksimal</label>
                                <input type="number" class="form-control" id="max_attempts" name="max_attempts" min="0">
                                <div class="form-text">Biarkan kosong jika tidak ada batasan jumlah percobaan</div>
                            </div>

                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="randomize_questions" name="randomize_questions">
                                <label class="form-check-label" for="randomize_questions">
                                    Acak Urutan Pertanyaan
                                </label>
                                <div class="form-text">Urutan pertanyaan akan diacak untuk setiap peserta</div>
                            </div>

                        </div>

                        <div class="mb-3">
                            <label class="form-label">Pengaturan Tampilan Jawaban</label>
                            <div class="form-text mb-2">Pilih salah satu opsi di bawah ini untuk mengatur tampilan jawaban benar/salah</div>

                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="answer_display_option" id="option_hide_all" value="hide_all">
                                <label class="form-check-label" for="option_hide_all">
                                    <strong>Sembunyikan Semua Jawaban Benar/Salah</strong>
                                </label>
                                <div class="form-text">Peserta tidak dapat melihat jawaban benar/salah sama sekali (untuk mencegah mengingat jawaban)</div>
                            </div>

                            <div class="form-check mt-2">
                                <input class="form-check-input" type="radio" name="answer_display_option" id="option_show_correct" value="show_correct">
                                <label class="form-check-label" for="option_show_correct">
                                    <strong>Tampilkan Jawaban Benar</strong>
                                </label>
                                <div class="form-text">Peserta dapat melihat jawaban yang benar setelah selesai</div>
                            </div>

                            <div class="form-check mt-2">
                                <input class="form-check-input" type="radio" name="answer_display_option" id="option_no_display" value="no_display" checked>
                                <label class="form-check-label" for="option_no_display">
                                    <strong>Tidak Menampilkan Jawaban Benar</strong>
                                </label>
                                <div class="form-text">Peserta hanya melihat jawaban mereka sendiri tanpa indikasi benar/salah</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" name="save_quiz" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan dan Lanjutkan
                            </button>

                            <?php if ($class_id): ?>
                                <a href="manage_class.php?id=<?= $class_id ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Kembali ke Kelas
                                </a>
                            <?php else: ?>
                                <a href="manage_classes.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Kembali ke Daftar Kelas
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    // Add any JavaScript functionality here
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);

        // Handle allow_multiple_attempts checkbox
        const allowMultipleAttemptsCheckbox = document.getElementById('allow_multiple_attempts');
        const maxAttemptsContainer = document.querySelector('.max-attempts-container');

        allowMultipleAttemptsCheckbox.addEventListener('change', function() {
            maxAttemptsContainer.style.display = this.checked ? 'block' : 'none';

            // If unchecked, clear the max_attempts value
            if (!this.checked) {
                document.getElementById('max_attempts').value = '';
            }
        });
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
