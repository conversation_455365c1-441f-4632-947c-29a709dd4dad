<?php
/**
 * API untuk search training suggestions dengan autocomplete
 */

// Suppress deprecated warnings untuk clean JSON output
error_reporting(E_ALL & ~E_DEPRECATED & ~E_NOTICE);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Include necessary files
require_once '../../config/config.php';
require_once '../security.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role_id'], [2, 3, 4, 5, 99])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    // Get parameters
    $query = $_GET['q'] ?? '';
    $type = $_GET['type'] ?? 'all'; // all, online, offline
    $limit = (int)($_GET['limit'] ?? 10);

    if (strlen($query) < 2) {
        echo json_encode([
            'success' => true,
            'suggestions' => [],
            'message' => 'Minimal 2 karakter untuk pencarian'
        ]);
        exit;
    }

    $suggestions = [];
    $search_param = "%{$query}%";

    // Search offline training
    if ($type === 'all' || $type === 'offline') {
        $offline_query = "SELECT
                            id,
                            training_topic as title,
                            training_description as description,
                            start_date,
                            end_date,
                            location,
                            trainer_name as trainer,
                            status,
                            'offline' as type,
                            CASE
                                WHEN training_topic LIKE ? THEN 1
                                WHEN trainer_name LIKE ? THEN 2
                                WHEN location LIKE ? THEN 3
                                ELSE 4
                            END as relevance_score
                          FROM offline_training
                          WHERE (training_topic LIKE ? OR trainer_name LIKE ? OR location LIKE ? OR training_description LIKE ?)
                          AND status IN ('Active', 'Completed')
                          ORDER BY relevance_score ASC, start_date DESC
                          LIMIT ?";

        $stmt = $conn->prepare($offline_query);
        $stmt->bind_param("sssssssi",
            $search_param, $search_param, $search_param, // for relevance scoring (3 params)
            $search_param, $search_param, $search_param, $search_param, // for WHERE clause (4 params)
            $limit // LIMIT (1 param)
        );
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            // Format date
            $date_display = '';
            if ($row['start_date']) {
                $date_display = date('d M Y', strtotime($row['start_date']));
                if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                    $date_display .= ' - ' . date('d M Y', strtotime($row['end_date']));
                }
            }

            // Highlight matching text with null safety
            $highlighted_title = highlightMatch($row['title'] ?? '', $query);
            $highlighted_trainer = highlightMatch($row['trainer'] ?? '', $query);
            $highlighted_location = highlightMatch($row['location'] ?? '', $query);

            $suggestions[] = [
                'id' => $row['id'],
                'type' => 'offline',
                'title' => $row['title'] ?? '',
                'highlighted_title' => $highlighted_title,
                'description' => $row['description'] ?? '',
                'date' => $date_display,
                'location' => $row['location'] ?? '',
                'highlighted_location' => $highlighted_location,
                'trainer' => $row['trainer'] ?? '',
                'highlighted_trainer' => $highlighted_trainer,
                'status' => $row['status'] ?? 'Active',
                'relevance' => $row['relevance_score'],
                'icon' => 'fas fa-building',
                'badge_color' => 'success'
            ];
        }
        $stmt->close();
    }

    // Search online training
    if ($type === 'all' || $type === 'online') {
        $online_query = "SELECT
                            id,
                            training_topic as title,
                            additional_info as description,
                            start_date,
                            end_date,
                            COALESCE(training_place, 'Online') as location,
                            contact_person as trainer,
                            status,
                            'online' as type,
                            CASE
                                WHEN training_topic LIKE ? THEN 1
                                WHEN contact_person LIKE ? THEN 2
                                WHEN training_place LIKE ? THEN 3
                                ELSE 4
                            END as relevance_score
                         FROM training_submissions
                         WHERE (training_topic LIKE ? OR contact_person LIKE ? OR training_place LIKE ? OR additional_info LIKE ?)
                         AND status = 'Approved'
                         ORDER BY relevance_score ASC, start_date DESC
                         LIMIT ?";

        $stmt = $conn->prepare($online_query);
        $stmt->bind_param("sssssssi",
            $search_param, $search_param, $search_param, // for relevance scoring (3 params)
            $search_param, $search_param, $search_param, $search_param, // for WHERE clause (4 params)
            $limit // LIMIT (1 param)
        );
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            // Format date
            $date_display = '';
            if ($row['start_date']) {
                $date_display = date('d M Y', strtotime($row['start_date']));
                if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                    $date_display .= ' - ' . date('d M Y', strtotime($row['end_date']));
                }
            }

            // Highlight matching text with null safety
            $highlighted_title = highlightMatch($row['title'] ?? '', $query);
            $highlighted_trainer = highlightMatch($row['trainer'] ?? '', $query);
            $highlighted_location = highlightMatch($row['location'] ?? '', $query);

            $suggestions[] = [
                'id' => $row['id'],
                'type' => 'online',
                'title' => $row['title'] ?? '',
                'highlighted_title' => $highlighted_title,
                'description' => $row['description'] ?? '',
                'date' => $date_display,
                'location' => $row['location'] ?? 'Online',
                'highlighted_location' => $highlighted_location,
                'trainer' => $row['trainer'] ?? '',
                'highlighted_trainer' => $highlighted_trainer,
                'status' => $row['status'] ?? 'Approved',
                'relevance' => $row['relevance_score'],
                'icon' => 'fas fa-laptop',
                'badge_color' => 'info'
            ];
        }
        $stmt->close();
    }

    // Sort by relevance across both types
    usort($suggestions, function($a, $b) {
        if ($a['relevance'] === $b['relevance']) {
            return strcmp($a['title'], $b['title']);
        }
        return $a['relevance'] - $b['relevance'];
    });

    // Limit final results
    $suggestions = array_slice($suggestions, 0, $limit);

    echo json_encode([
        'success' => true,
        'suggestions' => $suggestions,
        'count' => count($suggestions),
        'query' => $query,
        'type' => $type
    ]);

} catch (Exception $e) {
    error_log("Error in search_training_suggestions.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error searching training: ' . $e->getMessage()
    ]);
}

/**
 * Highlight matching text in search results
 */
function highlightMatch($text, $query) {
    // Handle null or empty values
    if ($text === null || $text === '') {
        return '';
    }

    if (empty($query)) {
        return htmlspecialchars($text);
    }

    $highlighted = preg_replace(
        '/(' . preg_quote($query, '/') . ')/i',
        '<mark class="search-highlight">$1</mark>',
        htmlspecialchars($text)
    );

    return $highlighted;
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
