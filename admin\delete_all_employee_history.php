<?php
session_start();
require_once '../includes/auth_check.php'; // Ensure user is logged in and is an admin
require_once '../config/config.php'; // Database connection and configuration
require_once '../config/activity_logger.php'; // Include the activity logger

// Konversi koneksi mysqli ke PDO
$pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Akses ditolak. Anda tidak memiliki izin untuk melakukan tindakan ini.']);
    exit;
}

$adminUsername = $_SESSION['name'] ?? 'Admin Tidak Dikenal'; // User performing the deletion

try {
    // Begin Transaction
    $pdo->beginTransaction();
    
    // Count records before deletion for reporting
    $countStmt = $pdo->query("SELECT COUNT(*) FROM karyawan_history");
    $count = $countStmt->fetchColumn();
    
    if ($count == 0) {
        throw new Exception("Tidak ada riwayat untuk dihapus.");
    }
    
    // Delete all records from karyawan_history
    $deleteStmt = $pdo->prepare("TRUNCATE TABLE karyawan_history");
    $deleteSuccess = $deleteStmt->execute();
    
    if (!$deleteSuccess) {
        throw new Exception("Gagal menghapus semua riwayat.");
    }
    
    // Log the deletion action
    $userId = $_SESSION['user_id'] ?? 0;
    $logMessage = "Menghapus semua riwayat karyawan ($count catatan) oleh $adminUsername.";
    $details = ['message' => $logMessage, 'count' => $count];
    log_activity($userId, 'Hapus Semua Riwayat', 'Riwayat Karyawan', $details);
    
    // Commit Transaction
    $pdo->commit();
    
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'message' => 'Semua riwayat berhasil dihapus.', 'count' => $count]);
    
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    // Log the database error
    error_log("Database Error during all history deletion: " . $e->getMessage());
    $dbErrorMsg = "Kesalahan database saat menghapus semua riwayat: " . $e->getMessage();
    $userId = $_SESSION['user_id'] ?? 0;
    $details = ['error' => $dbErrorMsg];
    log_activity($userId, 'Hapus Semua Riwayat Gagal', 'Kesalahan Hapus Riwayat', $details);
    
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Kesalahan database saat menghapus semua riwayat: ' . $e->getMessage()]);
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    // Log the general error
    error_log("General Error during all history deletion: " . $e->getMessage());
    $generalErrorMsg = "Kesalahan saat menghapus semua riwayat: " . $e->getMessage();
    $userId = $_SESSION['user_id'] ?? 0;
    $details = ['error' => $generalErrorMsg];
    log_activity($userId, 'Hapus Semua Riwayat Gagal', 'Kesalahan Hapus Riwayat', $details);
    
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Gagal menghapus semua riwayat: ' . $e->getMessage()]);
}

exit;
