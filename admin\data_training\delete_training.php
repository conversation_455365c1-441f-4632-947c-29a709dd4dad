<?php
session_start();
include '../../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../../view/login.php');
    exit();
}

if (isset($_GET['id'])) {
    $id = $_GET['id'];
    
    try {
        // Mulai transaction
        $conn->begin_transaction();

        // Hapus data training
        $delete_query = "DELETE FROM data_training WHERE id = ?";
        $stmt = $conn->prepare($delete_query);
        $stmt->bind_param("i", $id);
        
        if ($stmt->execute()) {
            $conn->commit();
            header('Location: view_training.php?success=1');
            exit();
        } else {
            throw new Exception("Gagal menghapus data");
        }
    } catch (Exception $e) {
        $conn->rollback();
        header('Location: view_training.php?error=' . urlencode($e->getMessage()));
        exit();
    }
} else {
    header('Location: view_training.php');
    exit();
}
?>