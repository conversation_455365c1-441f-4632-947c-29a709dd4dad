<?php
session_start();
include '../config/config.php';
require_once '../includes/SystemManager.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

echo "<h2>Debug Delete FAQ Submission</h2>";

// Test delete submission
if (isset($_POST['test_delete'])) {
    $submission_id = intval($_POST['submission_id']);
    
    echo "<h3>Testing Delete Submission ID: $submission_id</h3>";
    
    try {
        // Initialize SystemManager
        echo "<p>1. Initializing SystemManager...</p>";
        $systemManager = new SystemManager($conn);
        echo "<p>✅ SystemManager initialized</p>";
        
        // Get FAQManager
        echo "<p>2. Getting FAQManager...</p>";
        $faqManager = $systemManager->getFAQManager();
        echo "<p>✅ FAQManager obtained</p>";
        
        // Check if submission exists
        echo "<p>3. Checking if submission exists...</p>";
        $submission = $faqManager->getSubmission($submission_id);
        if ($submission) {
            echo "<p>✅ Submission found:</p>";
            echo "<ul>";
            echo "<li>ID: " . htmlspecialchars($submission['id']) . "</li>";
            echo "<li>Question: " . htmlspecialchars(substr($submission['question'], 0, 50)) . "...</li>";
            echo "<li>Status: " . htmlspecialchars($submission['status']) . "</li>";
            echo "<li>Created: " . htmlspecialchars($submission['created_at']) . "</li>";
            echo "</ul>";
        } else {
            echo "<p>❌ Submission not found</p>";
            exit();
        }
        
        // Test delete
        echo "<p>4. Attempting to delete submission...</p>";
        $result = $faqManager->deleteSubmission($submission_id, $_SESSION['user_id']);
        
        if ($result) {
            echo "<p>✅ Submission deleted successfully</p>";
        } else {
            echo "<p>❌ Failed to delete submission</p>";
        }
        
        // Verify deletion
        echo "<p>5. Verifying deletion...</p>";
        $check_submission = $faqManager->getSubmission($submission_id);
        if (!$check_submission) {
            echo "<p>✅ Submission successfully removed from database</p>";
        } else {
            echo "<p>❌ Submission still exists in database</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Error: " . $e->getMessage() . "</p>";
        echo "<p>Stack trace:</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
}

// Get all submissions for testing
try {
    $systemManager = new SystemManager($conn);
    $faqManager = $systemManager->getFAQManager();
    $submissions = $faqManager->getSubmissions(null, 10);
    
    echo "<h3>Available FAQ Submissions for Testing</h3>";
    
    if (empty($submissions)) {
        echo "<p>No submissions found. Please create some FAQ submissions first.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Question</th><th>Status</th><th>Created</th><th>Action</th></tr>";
        
        foreach ($submissions as $submission) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($submission['id']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($submission['question'], 0, 50)) . "...</td>";
            echo "<td>" . htmlspecialchars($submission['status']) . "</td>";
            echo "<td>" . htmlspecialchars($submission['created_at']) . "</td>";
            echo "<td>";
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='submission_id' value='" . $submission['id'] . "'>";
            echo "<button type='submit' name='test_delete' onclick='return confirm(\"Are you sure you want to delete this submission?\")'>Delete</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error loading submissions: " . $e->getMessage() . "</p>";
}

// Test direct database delete
echo "<h3>Direct Database Test</h3>";
if (isset($_POST['test_direct_delete'])) {
    $submission_id = intval($_POST['direct_submission_id']);
    
    echo "<p>Testing direct database delete for submission ID: $submission_id</p>";
    
    try {
        // Check if submission exists
        $check_query = "SELECT * FROM faq_submissions WHERE id = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("i", $submission_id);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        
        if ($result->num_rows > 0) {
            echo "<p>✅ Submission exists in database</p>";
            
            // Delete directly
            $delete_query = "DELETE FROM faq_submissions WHERE id = ?";
            $delete_stmt = $conn->prepare($delete_query);
            $delete_stmt->bind_param("i", $submission_id);
            
            if ($delete_stmt->execute()) {
                echo "<p>✅ Direct database delete successful</p>";
                echo "<p>Affected rows: " . $delete_stmt->affected_rows . "</p>";
            } else {
                echo "<p>❌ Direct database delete failed: " . $conn->error . "</p>";
            }
        } else {
            echo "<p>❌ Submission not found in database</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Direct delete error: " . $e->getMessage() . "</p>";
    }
}

echo "<h4>Direct Database Delete Test</h4>";
echo "<form method='POST'>";
echo "<input type='number' name='direct_submission_id' placeholder='Submission ID' required>";
echo "<button type='submit' name='test_direct_delete' onclick='return confirm(\"Direct delete - are you sure?\")'>Direct Delete</button>";
echo "</form>";

echo "<p><a href='manage_faq.php'>Back to FAQ Management</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
button { padding: 5px 10px; margin: 2px; }
</style>
