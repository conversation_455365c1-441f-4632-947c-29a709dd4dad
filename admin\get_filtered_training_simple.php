<?php
// Disable error display to prevent HTML output
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Set content type early
header('Content-Type: application/json');

// Start session
session_start();

// Include database connection
include '../config/config.php';

// Check authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Check database connection
if (!$conn) {
    echo json_encode(['error' => 'Database connection failed']);
    exit();
}

try {
    // First, let's check what columns exist to avoid datetime issues
    $columns_query = "SHOW COLUMNS FROM training_submissions";
    $columns_result = $conn->query($columns_query);
    $available_columns = [];
    while ($col = $columns_result->fetch_assoc()) {
        $available_columns[] = $col['Field'];
    }

    error_log("Available columns in training_submissions: " . implode(', ', $available_columns));

    // Build safe query with basic columns and date columns if they exist
    $safe_columns = ['ts.id', 'ts.full_name', 'ts.training_topic', 'ts.status', 'ts.email'];

    // Add date columns if they exist
    if (in_array('start_date', $available_columns)) {
        $safe_columns[] = 'ts.start_date';
    } else {
        $safe_columns[] = 'NULL as start_date';
    }

    if (in_array('end_date', $available_columns)) {
        $safe_columns[] = 'ts.end_date';
    } else {
        $safe_columns[] = 'NULL as end_date';
    }

    if (in_array('is_confirmed', $available_columns)) {
        $safe_columns[] = 'ts.is_confirmed';
    } else {
        $safe_columns[] = 'NULL as is_confirmed';
    }

    // Add created_at only if it exists and we can safely use it
    if (in_array('created_at', $available_columns)) {
        $safe_columns[] = 'ts.created_at as submission_date';
    } else if (in_array('submission_date', $available_columns)) {
        $safe_columns[] = 'ts.submission_date';
    } else {
        $safe_columns[] = 'NULL as submission_date';
    }

    $safe_columns[] = 'u.name as requester_name';

    $query = "SELECT " . implode(', ', $safe_columns) . "
              FROM training_submissions ts
              LEFT JOIN users u ON ts.user_id = u.id
              WHERE ts.id > 0";

    // Skip deleted_at condition for now to avoid datetime issues
    // if (in_array('deleted_at', $available_columns)) {
    //     $query .= " AND (ts.deleted_at IS NULL OR ts.deleted_at = '')";
    // }

    $query .= " ORDER BY ts.id DESC LIMIT 50";

    error_log("Executing query: " . $query);

    $result = $conn->query($query);

    if (!$result) {
        throw new Exception('Query failed: ' . $conn->error);
    }

    // Fetch data
    $trainings = [];
    while ($row = $result->fetch_assoc()) {
        $trainings[] = [
            'id' => (int)$row['id'],
            'full_name' => $row['full_name'] ?? '',
            'training_topic' => $row['training_topic'] ?? '',
            'training_date' => $row['start_date'] ?? null, // Use start_date as primary date
            'training_date_fixed' => ($row['is_confirmed'] == 1) ? $row['start_date'] : null,
            'training_date_start' => $row['start_date'] ?? null,
            'training_date_end' => $row['end_date'] ?? null,
            'status' => $row['status'] ?? 'pending',
            'email' => $row['email'] ?? '',
            'submission_date' => $row['submission_date'] ?? null,
            'requester_name' => $row['requester_name'] ?? ''
        ];
    }

    // Log for debugging
    error_log("Simple API - Records found: " . count($trainings));

    // Return JSON response
    echo json_encode($trainings);
    
} catch (Exception $e) {
    // Log error and return error response
    error_log("get_filtered_training_simple.php error: " . $e->getMessage());
    echo json_encode(['error' => $e->getMessage()]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
