# Panduan Penggunaan Absensi NFC

## Daftar Isi
1. [Pendahuluan](#pendahuluan)
2. [Persyaratan](#persyaratan)
3. [<PERSON>](#cara-menggunakan)
4. [Troubleshooting](#troubleshooting)
5. [FAQ](#faq)

## Pendahuluan

Sistem Absensi NFC adalah fitur yang memungkinkan Anda menggunakan smartphone dengan kemampuan NFC untuk membaca kartu RFID yang sama yang digunakan dalam sistem absensi RFID. Dengan fitur ini, Anda tidak perlu membawa perangkat pembaca RFID khusus, cukup gunakan smartphone Anda.

### Keuntungan Menggunakan NFC

- **Mobilitas**: Tidak perlu membawa perangkat pembaca RFID khusus
- **Kemudahan**: Proses absensi dapat dilakukan di mana saja
- **Integrasi**: Terintegrasi langsung dengan sistem absensi yang sudah ada
- **Real-time**: Data absensi langsung tercatat dalam database

## Persyaratan

Untuk menggunakan fitur Absensi NFC, Anda memerlukan:

1. **Smartphone dengan NFC**: 
   - Android: Versi 6.0 (Marshmallow) atau lebih baru
   - iPhone: iPhone 7 atau lebih baru dengan iOS 13+

2. **Browser yang Mendukung Web NFC API**:
   - Chrome 89+ di Android
   - Safari di iOS 13+ (dengan batasan)

3. **Kartu RFID yang Terdaftar**:
   - Kartu harus sudah terdaftar dalam sistem
   - Format kartu: MIFARE Classic, MIFARE Ultralight, atau ISO 14443-A

4. **Koneksi Internet**:
   - Diperlukan untuk mengirim data ke server

## Cara Menggunakan

### Langkah 1: Akses Halaman NFC Reader

1. Buka browser di smartphone Anda
2. Akses URL: `http://[alamat-server]/Training/nfc_reader.php`
3. Atau dari menu admin, klik "Absensi NFC"

### Langkah 2: Pilih Training

1. Dari dropdown, pilih training yang akan dihadiri
2. Pastikan Anda memilih training yang benar

### Langkah 3: Scan Kartu RFID

1. Klik tombol "Scan NFC"
2. Aktifkan NFC pada smartphone Anda jika belum aktif
3. Tempelkan kartu RFID ke bagian belakang smartphone
   - Pada kebanyakan smartphone, sensor NFC berada di bagian tengah atau atas bagian belakang
   - Jangan menggerakkan kartu selama proses pembacaan
4. Tunggu hingga proses pembacaan selesai (biasanya 1-2 detik)

### Langkah 4: Verifikasi Hasil

1. Sistem akan menampilkan hasil pembacaan:
   - Nama karyawan
   - NIK
   - Departemen
   - Status absensi (Check-in atau Check-out)
2. Jika berhasil, data akan langsung tercatat dalam sistem

## Posisi Sensor NFC pada Berbagai Smartphone

Posisi sensor NFC bervariasi tergantung model smartphone:

- **Samsung**: Umumnya di bagian tengah belakang
- **iPhone**: Di bagian atas belakang
- **Google Pixel**: Di bagian atas belakang
- **Xiaomi**: Biasanya di tengah atau atas belakang
- **Oppo/Vivo**: Umumnya di bagian tengah belakang

Jika Anda kesulitan menemukan posisi sensor NFC, coba gerakkan kartu perlahan di sekitar bagian belakang smartphone hingga terdeteksi.

## Troubleshooting

### Kartu Tidak Terbaca

1. **Pastikan NFC Aktif**:
   - Buka pengaturan smartphone
   - Aktifkan NFC
   - Pada beberapa perangkat, Anda juga perlu mengaktifkan "Tap and Pay"

2. **Posisi Kartu**:
   - Pastikan kartu ditempelkan dengan benar ke sensor NFC
   - Coba berbagai posisi di bagian belakang smartphone

3. **Jenis Kartu**:
   - Pastikan kartu RFID Anda kompatibel (MIFARE Classic, MIFARE Ultralight, atau ISO 14443-A)

4. **Interferensi**:
   - Lepaskan casing smartphone jika terlalu tebal
   - Hindari area dengan banyak perangkat elektronik

### Error "NFC Tidak Didukung"

1. **Periksa Kompatibilitas Browser**:
   - Gunakan Chrome 89+ di Android
   - Gunakan Safari di iOS 13+

2. **Periksa Perangkat**:
   - Pastikan smartphone Anda memiliki kemampuan NFC
   - Beberapa smartphone entry-level mungkin tidak memiliki NFC

### Error "Kartu Tidak Terdaftar"

1. **Verifikasi Kartu**:
   - Pastikan kartu sudah terdaftar dalam sistem
   - Hubungi admin untuk memeriksa status kartu

2. **Coba Kartu Lain**:
   - Jika Anda memiliki kartu lain, coba gunakan untuk memastikan sistem berfungsi

## FAQ

### Apakah semua smartphone mendukung NFC?

Tidak. Hanya smartphone menengah ke atas yang biasanya dilengkapi dengan NFC. Periksa spesifikasi smartphone Anda untuk memastikan.

### Apakah saya perlu menginstal aplikasi khusus?

Tidak. Fitur ini berjalan di browser web standar yang mendukung Web NFC API, seperti Chrome di Android atau Safari di iOS 13+.

### Apakah kartu RFID saya akan rusak jika dibaca dengan smartphone?

Tidak. Proses pembacaan NFC tidak merusak kartu RFID. Ini adalah proses pasif yang hanya membaca data.

### Apakah data saya aman?

Ya. Komunikasi antara smartphone dan server dilindungi dengan protokol keamanan standar. Data hanya dikirim ke server yang sah.

### Bisakah saya menggunakan NFC untuk absensi orang lain?

Tidak direkomendasikan. Sistem ini dirancang untuk penggunaan pribadi, dan menggunakan untuk absensi orang lain dapat dianggap sebagai pelanggaran kebijakan perusahaan.

### Apakah saya bisa menggunakan fitur ini offline?

Tidak. Koneksi internet diperlukan untuk mengirim data ke server dan memverifikasi identitas pengguna.

---

Untuk bantuan lebih lanjut, silakan hubungi tim IT atau admin sistem.
