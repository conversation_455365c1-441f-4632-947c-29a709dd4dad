<?php
require '../config/config.php';
// session_start() sudah dipanggil di config.php

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: ../admin/dashboard.php");
    exit();
}

// Rate Limiting
if (!isset($_SESSION['submit_count'])) {
    $_SESSION['submit_count'] = 1;
    $_SESSION['first_submit'] = time();
} else {
    if (time() - $_SESSION['first_submit'] < 60) {
        if ($_SESSION['submit_count'] >= 3) {
            $_SESSION['errors'] = ["Anda terlalu banyak melakukan submit! Coba lagi nanti."];
            header("Location: form.php");
            exit();
        }
        $_SESSION['submit_count']++;
    } else {
        $_SESSION['submit_count'] = 1;
        $_SESSION['first_submit'] = time();
    }
}

// CSRF Protection
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['token']) {
    $_SESSION['errors'] = ["Invalid CSRF token!"];
    header("Location: form.php");
    exit();
}

// Validasi Input
$errors = [];
$requiredFields = ['full_name', 'nik', 'department', 'Bagian', 'Jabatan', 'email', 'phone', 'training_topic', 'training_date', 'additional_info'];

foreach ($requiredFields as $field) {
    if (empty($_POST[$field])) {
        $errors[] = "Field " . ucfirst(str_replace('_', ' ', $field)) . " wajib diisi!";
    }
}

if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
    $errors[] = "Format email tidak valid!";
}


if (!DateTime::createFromFormat('Y-m-d', $_POST['training_date'])) {
    $errors[] = "Format tanggal tidak valid!";
}

if (!empty($errors)) {
    $_SESSION['errors'] = $errors;
    header("Location: form.php");
    exit();
}

function sanitize_input($data) {
    return filter_var($data, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
}

$full_name = sanitize_input($_POST['full_name']);
$nik = sanitize_input($_POST['nik']);
$departemen = sanitize_input($_POST['department']); // Changed from departemen to department
$bagian = sanitize_input($_POST['Bagian']);        // Changed from bagian to Bagian
$jabatan = sanitize_input($_POST['Jabatan']);      // Changed from jabatan to Jabatan
$email = sanitize_input($_POST['email']);
$phone = sanitize_input($_POST['phone']);
$training_topic = sanitize_input($_POST['training_topic']);
$training_date = sanitize_input($_POST['training_date']);
$additional_info = sanitize_input($_POST['additional_info']);

// Mendapatkan user_id dari session
$user_id = $_SESSION['user_id']; // Pastikan ini sudah ada di session Anda

// Set status awal "Pending Dept Head"
$status = 'Pending'; // Status awal training
$query = "INSERT INTO training_submissions (user_id, full_name, nik, departemen, bagian, jabatan, email, phone, training_topic, training_date, additional_info, status, current_approver_role_id, 
            approved_dept_head, approved_hrd, approved_ga, approved_gm) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 2, 'Pending', 'Pending', 'Pending', 'Pending')";

$stmt = $conn->prepare($query);
$stmt->bind_param("isssssssssss", $user_id, $full_name, $nik, $departemen, $bagian, $jabatan, $email, $phone, $training_topic, $training_date, $additional_info, $status);
$stmt->execute();

// Mendapatkan training_id yang baru disisipkan
$training_id = $stmt->insert_id;

// Simpan data peserta jika ada
if (!empty($_POST['nama']) && is_array($_POST['nama'])) {
    // Menyimpan peserta
    $stmt_participant = $conn->prepare("INSERT INTO participants (training_id, nama_participants, nik_participants, jabatan_participants, bagian_participants, departemen_participants) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt_participant->bind_param("isssss", $training_id, $nama, $nik, $jabatan, $bagian, $departemen);

    for ($i = 0; $i < count($_POST['nama']); $i++) {
        $nama = filter_var($_POST['nama'][$i], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $nik = filter_var($_POST['nik'][$i], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $jabatan = filter_var($_POST['jabatan'][$i], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $bagian = filter_var($_POST['bagian'][$i], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $departemen = filter_var($_POST['departemen'][$i], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $stmt_participant->execute();
    }
    $stmt_participant->close();
}

$_SESSION['success'] = "Pengajuan training berhasil disimpan! Status training: Pending Dept Head.";

$conn->close();
header("Location: ../admin/dashboard.php");
exit();
