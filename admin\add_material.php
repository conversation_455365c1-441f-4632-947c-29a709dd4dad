<?php
/**
 * Add Material Page for Admin
 * This page allows admins to add materials to a training class
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if class ID is provided
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;

if ($class_id <= 0) {
    header('Location: manage_classes.php');
    exit();
}

// Get class information
$class_query = "SELECT c.*, t.training_topic
               FROM training_classes c
               JOIN training_submissions t ON c.training_id = t.id
               WHERE c.id = ?";
$stmt = $conn->prepare($class_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$class = $result->fetch_assoc();
$stmt->close();

if (!$class) {
    header('Location: manage_classes.php');
    exit();
}

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_material'])) {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $type = $_POST['type'];
    $is_published = isset($_POST['is_published']) ? 1 : 0;
    $external_url = null;
    $file_path = null;

    // Validate input
    if (empty($title)) {
        $error_message = "Judul materi harus diisi.";
    } else {
        // Get the highest order number
        $order_query = "SELECT MAX(order_number) as max_order FROM training_materials WHERE class_id = ?";
        $stmt = $conn->prepare($order_query);
        $stmt->bind_param("i", $class_id);
        $stmt->execute();
        $order_result = $stmt->get_result();
        $order_row = $order_result->fetch_assoc();
        $order_number = ($order_row['max_order'] !== null) ? $order_row['max_order'] + 1 : 0;
        $stmt->close();

        // Handle file upload or external URL
        if ($type == 'link') {
            $external_url = trim($_POST['external_url']);
            if (empty($external_url)) {
                $error_message = "URL harus diisi untuk materi tipe tautan.";
            }
        } else if ($type == 'document' || $type == 'video' || $type == 'other') {
            // Detailed logging for debugging
            error_log("Processing file upload for type: " . $type);
            error_log("FILES array: " . print_r($_FILES, true));

            // Check if file was uploaded
            if (!isset($_FILES['file'])) {
                $error_message = "File tidak ditemukan dalam permintaan. Pastikan form memiliki atribut enctype='multipart/form-data'.";
                error_log("File upload error: File input not found in request");
            } else if ($_FILES['file']['error'] == UPLOAD_ERR_NO_FILE) {
                $error_message = "File harus diunggah untuk materi tipe " . ($type == 'document' ? 'dokumen' : ($type == 'video' ? 'video' : 'lainnya')) . ".";
                error_log("File upload error: No file was uploaded (UPLOAD_ERR_NO_FILE)");
            } else if (!isset($_FILES['file']['tmp_name']) || empty($_FILES['file']['tmp_name'])) {
                $error_message = "File tidak terunggah dengan benar (tmp_name kosong).";
                error_log("File upload error: tmp_name is empty or not set");
            } else if (!is_uploaded_file($_FILES['file']['tmp_name'])) {
                $error_message = "File tidak terunggah dengan benar (bukan file yang diunggah).";
                error_log("File upload error: Not an uploaded file");
            } else if ($_FILES['file']['error'] != UPLOAD_ERR_OK) {
                // Handle specific upload errors
                switch ($_FILES['file']['error']) {
                    case UPLOAD_ERR_INI_SIZE:
                        $error_message = "File terlalu besar (melebihi upload_max_filesize di php.ini).";
                        break;
                    case UPLOAD_ERR_FORM_SIZE:
                        $error_message = "File terlalu besar (melebihi MAX_FILE_SIZE di form HTML).";
                        break;
                    case UPLOAD_ERR_PARTIAL:
                        $error_message = "File hanya terunggah sebagian.";
                        break;
                    case UPLOAD_ERR_NO_TMP_DIR:
                        $error_message = "Folder sementara tidak ditemukan.";
                        break;
                    case UPLOAD_ERR_CANT_WRITE:
                        $error_message = "Gagal menulis file ke disk.";
                        break;
                    case UPLOAD_ERR_EXTENSION:
                        $error_message = "Upload dihentikan oleh ekstensi PHP.";
                        break;
                    default:
                        $error_message = "Terjadi kesalahan saat mengunggah file (kode: " . $_FILES['file']['error'] . ").";
                }

                // Log upload error for debugging
                error_log("File upload error code: " . $_FILES['file']['error']);
            } else {
                // Log successful file upload
                error_log("File upload successful. Processing file: " . $_FILES['file']['name']);
                error_log("Temporary file: " . $_FILES['file']['tmp_name']);
                error_log("File size: " . $_FILES['file']['size'] . " bytes");
                error_log("File type: " . $_FILES['file']['type']);
                // File was successfully uploaded
                $upload_dir = '../uploads/materials/';

                // Create directory if it doesn't exist
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                $file_name = $_FILES['file']['name'];
                $file_tmp = $_FILES['file']['tmp_name'];
                $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

                // Log file info for debugging
                error_log("File uploaded: " . $file_name . ", Type: " . $_FILES['file']['type'] . ", Size: " . $_FILES['file']['size']);

                // Validate file type for document
                if ($type == 'document') {
                    $allowed_exts = array('pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt');
                    if (!in_array($file_ext, $allowed_exts)) {
                        $error_message = "Format file tidak didukung untuk tipe dokumen. Format yang didukung: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT.";
                    }
                }

                // Validate file type for video
                if ($type == 'video') {
                    $allowed_exts = array('mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv');
                    if (!in_array($file_ext, $allowed_exts)) {
                        $error_message = "Format file tidak didukung untuk tipe video. Format yang didukung: MP4, AVI, MOV, WMV, FLV, MKV.";
                    }
                }

                if (empty($error_message)) {
                    // Generate unique file name
                    $new_file_name = uniqid() . '_' . $file_name;
                    $file_path = 'uploads/materials/' . $new_file_name;

                    // Move uploaded file
                    if (move_uploaded_file($file_tmp, '../' . $file_path)) {
                        // File uploaded successfully
                        error_log("File successfully moved to: " . '../' . $file_path);
                    } else {
                        $error_message = "Gagal mengunggah file. Periksa izin folder uploads.";
                        error_log("Failed to move uploaded file from " . $file_tmp . " to " . '../' . $file_path);
                    }
                }
            }
        }

        if (empty($error_message)) {
            // Insert material
            $insert_query = "INSERT INTO training_materials (
                            class_id, title, description, type, file_path, external_url,
                            order_number, is_published, created_by
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param(
                "isssssiis",
                $class_id, $title, $description, $type, $file_path, $external_url,
                $order_number, $is_published, $user_id
            );

            if ($stmt->execute()) {
                $success_message = "Materi berhasil ditambahkan.";

                // Clear form data
                $_POST = [];
            } else {
                $error_message = "Gagal menambahkan materi: " . $conn->error;
            }
            $stmt->close();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .material-type-options {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }

    .material-type-option {
        flex: 1;
        text-align: center;
        padding: 15px;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .material-type-option:hover {
        background-color: #f8f9fa;
    }

    .material-type-option.active {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }

    .material-type-option i {
        font-size: 2rem;
        margin-bottom: 10px;
        color: #6c757d;
    }

    .material-type-option.active i {
        color: #212529;
    }

    .material-type-option h5 {
        margin-bottom: 5px;
    }

    .material-type-option p {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .type-specific-fields {
        margin-top: 20px;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="welcome-section">
                <h1><i class="fas fa-book"></i> Tambah Materi</h1>
                <a href="manage_class.php?id=<?= $class_id ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Kelas
                </a>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Informasi Kelas</h5>
                </div>
                <div class="card-body">
                    <h5><?= htmlspecialchars($class['title']) ?></h5>
                    <p class="text-muted"><?= htmlspecialchars($class['training_topic']) ?></p>
                </div>
            </div>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
                <div class="alert alert-info">
                    <h5>Debug Info:</h5>
                    <ul>
                        <li>File Name: <?= isset($_FILES['file']['name']) ? htmlspecialchars($_FILES['file']['name']) : 'N/A' ?></li>
                        <li>File Type: <?= isset($_FILES['file']['type']) ? htmlspecialchars($_FILES['file']['type']) : 'N/A' ?></li>
                        <li>File Size: <?= isset($_FILES['file']['size']) ? number_format($_FILES['file']['size'] / 1024, 2) . ' KB' : 'N/A' ?></li>
                        <li>Error Code: <?= isset($_FILES['file']['error']) ? $_FILES['file']['error'] : 'N/A' ?></li>
                        <li>Temp File: <?= isset($_FILES['file']['tmp_name']) ? htmlspecialchars($_FILES['file']['tmp_name']) : 'N/A' ?></li>
                        <li>Selected Type: <?= isset($_POST['type']) ? htmlspecialchars($_POST['type']) : 'N/A' ?></li>
                        <li>Form Method: <?= $_SERVER['REQUEST_METHOD'] ?></li>
                        <li>Content Type: <?= $_SERVER['CONTENT_TYPE'] ?? 'N/A' ?></li>
                        <li>Form Data: <?= htmlspecialchars(print_r($_POST, true)) ?></li>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Tambah Materi Baru</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="" enctype="multipart/form-data" id="material-form" onsubmit="return validateForm()">
                        <!-- Max file size: 50MB -->
                        <input type="hidden" name="MAX_FILE_SIZE" value="52428800" />
                        <div class="mb-3">
                            <label for="title" class="form-label">Judul Materi</label>
                            <input type="text" class="form-control" id="title" name="title" value="<?= isset($_POST['title']) ? htmlspecialchars($_POST['title']) : '' ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi Materi</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?= isset($_POST['description']) ? htmlspecialchars($_POST['description']) : '' ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Jenis Materi</label>
                            <div class="material-type-options">
                                <div class="material-type-option" data-type="document">
                                    <i class="fas fa-file-alt"></i>
                                    <h5>Dokumen</h5>
                                    <p>Unggah file dokumen (PDF, Word, Excel, dll)</p>
                                </div>
                                <div class="material-type-option" data-type="video">
                                    <i class="fas fa-video"></i>
                                    <h5>Video</h5>
                                    <p>Unggah file video (MP4, AVI, dll)</p>
                                </div>
                                <div class="material-type-option" data-type="link">
                                    <i class="fas fa-link"></i>
                                    <h5>Tautan</h5>
                                    <p>Tambahkan tautan ke sumber eksternal</p>
                                </div>
                                <div class="material-type-option" data-type="other">
                                    <i class="fas fa-file"></i>
                                    <h5>Lainnya</h5>
                                    <p>Jenis materi lainnya</p>
                                </div>
                            </div>
                            <input type="hidden" id="type" name="type" value="document" required>
                        </div>

                        <div class="type-specific-fields">
                            <div id="document-fields" class="mb-3">
                                <label for="document_file" class="form-label">Unggah Dokumen</label>
                                <input type="file" class="form-control" id="document_file" name="file" accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt" required>
                                <div class="form-text">Format yang didukung: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT</div>
                            </div>

                            <div id="video-fields" class="mb-3" style="display: none;">
                                <label for="video_file" class="form-label">Unggah Video</label>
                                <input type="file" class="form-control" id="video_file" name="file" accept=".mp4,.avi,.mov,.wmv,.flv,.mkv" required>
                                <div class="form-text">Format yang didukung: MP4, AVI, MOV, WMV, FLV, MKV</div>
                            </div>

                            <div id="link-fields" class="mb-3" style="display: none;">
                                <label for="external_url" class="form-label">URL</label>
                                <input type="url" class="form-control" id="external_url" name="external_url" value="<?= isset($_POST['external_url']) ? htmlspecialchars($_POST['external_url']) : '' ?>">
                                <div class="form-text">Masukkan URL lengkap termasuk http:// atau https://</div>
                            </div>

                            <div id="other-fields" class="mb-3" style="display: none;">
                                <label for="other_file" class="form-label">Unggah File</label>
                                <input type="file" class="form-control" id="other_file" name="file" required>
                                <div class="form-text">Unggah file dalam format apapun. Pastikan file tidak terlalu besar (maks. 50MB).</div>
                            </div>
                        </div>

                        <!-- Debug information for file upload -->
                        <div class="mb-3 bg-light p-3 rounded">
                            <h6>Status Unggahan File:</h6>
                            <div id="file-upload-status">Belum ada file yang dipilih</div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_published" name="is_published" <?= isset($_POST['is_published']) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="is_published">Publikasikan Materi</label>
                            <div class="form-text">Jika dicentang, materi akan langsung terlihat oleh peserta</div>
                        </div>

                        <button type="submit" name="add_material" class="btn btn-primary">
                            <i class="fas fa-save"></i> Simpan Materi
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    // Form validation function
    function validateForm() {
        const selectedType = document.getElementById('type').value;
        let isValid = true;
        let errorMessage = '';

        console.log('Validating form for type:', selectedType);

        // Check if file is selected for document type
        if (selectedType === 'document') {
            const fileInput = document.getElementById('document_file');
            console.log('Document file input:', fileInput);
            console.log('Files selected:', fileInput.files.length);

            if (fileInput.files.length === 0) {
                errorMessage = 'Silakan pilih file dokumen untuk diunggah.';
                isValid = false;
            } else {
                console.log('Selected file:', fileInput.files[0].name);
                console.log('File size:', fileInput.files[0].size);
            }
        }

        // Check if file is selected for video type
        else if (selectedType === 'video') {
            const fileInput = document.getElementById('video_file');
            console.log('Video file input:', fileInput);
            console.log('Files selected:', fileInput.files.length);

            if (fileInput.files.length === 0) {
                errorMessage = 'Silakan pilih file video untuk diunggah.';
                isValid = false;
            } else {
                console.log('Selected file:', fileInput.files[0].name);
                console.log('File size:', fileInput.files[0].size);
            }
        }

        // Check if URL is provided for link type
        else if (selectedType === 'link') {
            const urlInput = document.getElementById('external_url');
            console.log('URL input:', urlInput);
            console.log('URL value:', urlInput.value);

            if (!urlInput.value.trim()) {
                errorMessage = 'Silakan masukkan URL.';
                isValid = false;
            }
        }

        // Check if file is selected for other type
        else if (selectedType === 'other') {
            const fileInput = document.getElementById('other_file');
            console.log('Other file input:', fileInput);
            console.log('Files selected:', fileInput.files.length);

            if (fileInput.files.length === 0) {
                errorMessage = 'Silakan pilih file untuk diunggah.';
                isValid = false;
            } else {
                console.log('Selected file:', fileInput.files[0].name);
                console.log('File size:', fileInput.files[0].size);
            }
        }

        // Show error message if validation fails
        if (!isValid) {
            console.log('Validation failed:', errorMessage);

            // Create alert element
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show';
            alertDiv.setAttribute('role', 'alert');
            alertDiv.innerHTML = `
                ${errorMessage}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            // Insert alert before the form
            const form = document.querySelector('form');
            form.parentNode.insertBefore(alertDiv, form);

            // Scroll to the alert
            alertDiv.scrollIntoView({ behavior: 'smooth', block: 'start' });

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alertDiv);
                bsAlert.close();
            }, 5000);
        } else {
            console.log('Form validation passed');
        }

        return isValid;
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Add form submission handler
        const form = document.getElementById('material-form');
        form.addEventListener('submit', function(event) {
            // Log form submission
            console.log('Form is being submitted');
            console.log('Form enctype:', form.enctype);

            const formData = new FormData(form);
            for (const [key, value] of formData.entries()) {
                if (key === 'file' && value instanceof File) {
                    console.log(`${key}: ${value.name} (${value.size} bytes)`);
                } else {
                    console.log(`${key}: ${value}`);
                }
            }
        });
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);

        // Material type selection
        const typeOptions = document.querySelectorAll('.material-type-option');
        const typeInput = document.getElementById('type');
        const documentFields = document.getElementById('document-fields');
        const videoFields = document.getElementById('video-fields');
        const linkFields = document.getElementById('link-fields');
        const otherFields = document.getElementById('other-fields');

        // Set initial active state
        typeOptions.forEach(option => {
            if (option.dataset.type === typeInput.value) {
                option.classList.add('active');
            }
        });

        // Show/hide fields based on selected type
        function updateTypeFields() {
            const selectedType = typeInput.value;

            // Show/hide fields
            documentFields.style.display = selectedType === 'document' ? 'block' : 'none';
            videoFields.style.display = selectedType === 'video' ? 'block' : 'none';
            linkFields.style.display = selectedType === 'link' ? 'block' : 'none';
            otherFields.style.display = selectedType === 'other' ? 'block' : 'none';

            // Handle required attributes for file inputs
            const docFileInput = document.getElementById('document_file');
            const videoFileInput = document.getElementById('video_file');
            const otherFileInput = document.getElementById('other_file');
            const urlInput = document.getElementById('external_url');

            // Reset required attributes and disable unused inputs
            if (docFileInput) {
                docFileInput.required = (selectedType === 'document');
                docFileInput.disabled = (selectedType !== 'document');
            }
            if (videoFileInput) {
                videoFileInput.required = (selectedType === 'video');
                videoFileInput.disabled = (selectedType !== 'video');
            }
            if (otherFileInput) {
                otherFileInput.required = (selectedType === 'other');
                otherFileInput.disabled = (selectedType !== 'other');
            }
            if (urlInput) {
                urlInput.required = (selectedType === 'link');
                urlInput.disabled = (selectedType !== 'link');
            }

            // Log the current state for debugging
            console.log('Selected type:', selectedType);
            console.log('Document field visible:', documentFields.style.display);
            console.log('Document input required:', docFileInput ? docFileInput.required : 'N/A');
            console.log('Document input disabled:', docFileInput ? docFileInput.disabled : 'N/A');
        }

        updateTypeFields();

        typeOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove active class from all options
                typeOptions.forEach(opt => opt.classList.remove('active'));

                // Add active class to clicked option
                this.classList.add('active');

                // Update hidden input value
                typeInput.value = this.dataset.type;

                // Update visible fields
                updateTypeFields();
            });
        });

        // Add event listeners to file inputs to show selected filename
        const fileInputs = document.querySelectorAll('input[type="file"]');
        const fileUploadStatus = document.getElementById('file-upload-status');

        fileInputs.forEach(input => {
            input.addEventListener('change', function() {
                if (this.files.length > 0) {
                    // Show filename next to input
                    const fileNameSpan = document.createElement('span');
                    fileNameSpan.className = 'ms-2 text-success';
                    fileNameSpan.textContent = 'File dipilih: ' + this.files[0].name;

                    // Remove any existing filename display
                    const existingSpan = this.parentNode.querySelector('span');
                    if (existingSpan) {
                        existingSpan.remove();
                    }

                    // Add new filename display
                    this.parentNode.appendChild(fileNameSpan);

                    // Update file upload status
                    const fileSize = (this.files[0].size / 1024).toFixed(2);
                    fileUploadStatus.innerHTML = `
                        <div class="alert alert-info mb-0">
                            <strong>File dipilih:</strong> ${this.files[0].name}<br>
                            <strong>Ukuran:</strong> ${fileSize} KB<br>
                            <strong>Tipe:</strong> ${this.files[0].type || 'Tidak terdeteksi'}<br>
                            <strong>Input ID:</strong> ${this.id}<br>
                            <strong>Input Name:</strong> ${this.name}
                        </div>
                    `;

                    console.log('File selected:', {
                        name: this.files[0].name,
                        size: this.files[0].size,
                        type: this.files[0].type,
                        inputId: this.id,
                        inputName: this.name
                    });
                } else {
                    fileUploadStatus.textContent = 'Belum ada file yang dipilih';
                }
            });
        });
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
