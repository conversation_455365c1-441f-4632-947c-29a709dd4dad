<?php
session_start();
require_once '../includes/auth_check.php'; // Ensure user is logged in and is an admin
require_once '../config/config.php'; // Database connection and configuration
require_once 'record_batch_employee_history.php'; // Include batch history functions

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo "<p class='text-danger'>A<PERSON><PERSON> ditolak. Anda tidak memiliki izin untuk melihat detail ini.</p>";
    exit;
}

if (!isset($_POST['batch_id'])) {
    echo "<p class='text-danger'>ID Batch tidak disediakan.</p>";
    exit;
}

$batchId = intval($_POST['batch_id']);

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get batch details
    $batch = getBatchHistoryDetails($batchId);
    
    if (!$batch) {
        echo "<p class='text-danger'>Data batch tidak ditemukan.</p>";
        exit;
    }
    
    // Get user name
    $userQuery = $pdo->prepare("SELECT name FROM users WHERE id = ?");
    $userQuery->execute([$batch['changed_by']]);
    $userName = $userQuery->fetchColumn() ?: 'Unknown';
    
    // Determine action type label
    $actionText = '';
    switch($batch['action_type']) {
        case 'BATCH_INSERT': $actionText = 'Penambahan Batch'; break;
        case 'BATCH_UPDATE': $actionText = 'Perubahan Batch'; break;
        case 'BATCH_DELETE': $actionText = 'Penghapusan Batch'; break;
        default: $actionText = $batch['action_type'];
    }
    
    // Get batch data
    $batchData = $batch['batch_data'];
    $mode = isset($batchData['mode']) ? $batchData['mode'] : 'unknown';
    $mode_label = ($mode === 'add') ? 'Tambah' : (($mode === 'update') ? 'Update' : $mode);
    
    $total_success = isset($batchData['total_success']) ? $batchData['total_success'] : 0;
    $total_error = isset($batchData['total_error']) ? $batchData['total_error'] : 0;
    $total_skipped = isset($batchData['total_skipped']) ? $batchData['total_skipped'] : 0;
    $niks = isset($batchData['niks']) ? $batchData['niks'] : [];
    
    // Start building the HTML output
    $output = "<div class='detail-header'>";
    $output .= "<p><strong>ID Batch:</strong> {$batchId}</p>";
    $output .= "<p><strong>Jenis Aksi:</strong> <span class='badge " . strtolower(str_replace('_', '-', $batch['action_type'])) . "'>{$actionText}</span></p>";
    $output .= "<p><strong>Mode:</strong> {$mode_label}</p>";
    $output .= "<p><strong>Waktu:</strong> " . date('d M Y H:i:s', strtotime($batch['change_timestamp'])) . "</p>";
    $output .= "<p><strong>Dilakukan Oleh:</strong> " . htmlspecialchars($userName) . "</p>";
    $output .= "</div><hr>";
    
    $output .= "<div class='row'>";
    $output .= "<div class='col-md-4'>";
    $output .= "<div class='card'>";
    $output .= "<div class='card-header bg-success text-white'><strong>Sukses</strong></div>";
    $output .= "<div class='card-body'><h3 class='text-center'>{$total_success}</h3></div>";
    $output .= "</div>";
    $output .= "</div>";
    
    $output .= "<div class='col-md-4'>";
    $output .= "<div class='card'>";
    $output .= "<div class='card-header bg-danger text-white'><strong>Error</strong></div>";
    $output .= "<div class='card-body'><h3 class='text-center'>{$total_error}</h3></div>";
    $output .= "</div>";
    $output .= "</div>";
    
    $output .= "<div class='col-md-4'>";
    $output .= "<div class='card'>";
    $output .= "<div class='card-header bg-warning'><strong>Dilewati</strong></div>";
    $output .= "<div class='card-body'><h3 class='text-center'>{$total_skipped}</h3></div>";
    $output .= "</div>";
    $output .= "</div>";
    $output .= "</div><hr>";
    
    // Display NIKs
    $output .= "<h4>Daftar NIK yang Diproses:</h4>";
    
    if (count($niks) > 0) {
        $output .= "<div class='nik-list' style='max-height: 300px; overflow-y: auto;'>";
        $output .= "<div class='row'>";
        
        // Display NIKs in columns
        $niksPerColumn = ceil(count($niks) / 3);
        $nikChunks = array_chunk($niks, $niksPerColumn);
        
        foreach ($nikChunks as $chunk) {
            $output .= "<div class='col-md-4'><ul class='list-group'>";
            foreach ($chunk as $nik) {
                $output .= "<li class='list-group-item'>" . htmlspecialchars($nik) . "</li>";
            }
            $output .= "</ul></div>";
        }
        
        $output .= "</div></div>";
    } else {
        $output .= "<p>Tidak ada NIK yang diproses.</p>";
    }
    
    echo $output;
    
} catch (Exception $e) {
    echo "<p class='text-danger'>Error: " . $e->getMessage() . "</p>";
}
?>
