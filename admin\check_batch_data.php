<?php
/**
 * Check Batch Data
 * This file checks what batch data exists and why rollback might not be available
 */

session_start();
require_once '../config/config.php';

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo 'Unauthorized access';
    exit();
}

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Batch Data Analysis</h2>";
    
    // Check all batch records
    echo "<h3>1. All Batch Records:</h3>";
    $stmt = $pdo->prepare("SELECT batch_id, action_type, is_rollback_capable, rollback_status, change_timestamp FROM karyawan_batch_history ORDER BY change_timestamp DESC");
    $stmt->execute();
    $all_batches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($all_batches)) {
        echo "<p style='color: red;'>❌ No batch history records found at all!</p>";
        echo "<p>This means no batch operations have been recorded yet.</p>";
        echo "<p>To test rollback functionality, you need to:</p>";
        echo "<ol>";
        echo "<li>Go to admin/upload_karyawan.php</li>";
        echo "<li>Upload a CSV file with employee data</li>";
        echo "<li>This will create BATCH_INSERT records</li>";
        echo "<li>Then return to batch history to see rollback options</li>";
        echo "</ol>";
    } else {
        echo "<p>Found " . count($all_batches) . " batch records:</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Batch ID</th><th>Action Type</th><th>Rollback Capable</th><th>Rollback Status</th><th>Timestamp</th></tr>";
        
        foreach ($all_batches as $batch) {
            $rollback_capable = $batch['is_rollback_capable'] ? 'Yes' : 'No';
            $color = $batch['is_rollback_capable'] ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>" . $batch['batch_id'] . "</td>";
            echo "<td>" . $batch['action_type'] . "</td>";
            echo "<td style='color: {$color};'>" . $rollback_capable . "</td>";
            echo "<td>" . $batch['rollback_status'] . "</td>";
            echo "<td>" . $batch['change_timestamp'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Analyze action types
        echo "<h3>2. Action Type Analysis:</h3>";
        $action_types = array_count_values(array_column($all_batches, 'action_type'));
        
        foreach ($action_types as $action_type => $count) {
            echo "<p><strong>{$action_type}:</strong> {$count} records</p>";
            
            if ($action_type === 'BATCH_INSERT') {
                echo "<p style='color: green;'>✓ This should be rollback capable</p>";
            } elseif (in_array($action_type, ['BATCH_UPDATE', 'BATCH_DELETE'])) {
                echo "<p style='color: orange;'>⚠️ Requires enhanced history for rollback</p>";
            } else {
                echo "<p style='color: red;'>❌ Unknown action type - may need manual update</p>";
            }
        }
        
        // Check for records that should be rollback capable but aren't
        echo "<h3>3. Rollback Capability Issues:</h3>";
        $should_be_capable = array_filter($all_batches, function($batch) {
            return $batch['action_type'] === 'BATCH_INSERT' && !$batch['is_rollback_capable'];
        });
        
        if (!empty($should_be_capable)) {
            echo "<p style='color: red;'>Found " . count($should_be_capable) . " BATCH_INSERT records that should be rollback capable but aren't:</p>";
            
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Batch ID</th><th>Action Type</th><th>Timestamp</th><th>Fix Action</th></tr>";
            
            foreach ($should_be_capable as $batch) {
                echo "<tr>";
                echo "<td>" . $batch['batch_id'] . "</td>";
                echo "<td>" . $batch['action_type'] . "</td>";
                echo "<td>" . $batch['change_timestamp'] . "</td>";
                echo "<td><button onclick='fixBatch(" . $batch['batch_id'] . ")' class='btn btn-warning'>Fix This Batch</button></td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p><button onclick='fixAllBatches()' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Fix All BATCH_INSERT Records</button></p>";
        } else {
            echo "<p style='color: green;'>✓ All BATCH_INSERT records are properly marked as rollback capable</p>";
        }
    }
    
    // Check if upload_karyawan.php is recording batch history
    echo "<h3>4. Batch History Recording Check:</h3>";
    if (file_exists('upload_karyawan.php')) {
        echo "<p>✓ upload_karyawan.php exists</p>";
        
        // Check if it includes the batch history recording
        $upload_content = file_get_contents('upload_karyawan.php');
        if (strpos($upload_content, 'recordBatchEmployeeHistory') !== false) {
            echo "<p>✓ upload_karyawan.php includes batch history recording</p>";
        } else {
            echo "<p style='color: red;'>❌ upload_karyawan.php does not include batch history recording</p>";
            echo "<p>You may need to update upload_karyawan.php to record batch operations</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ upload_karyawan.php not found</p>";
    }
    
    echo "<h3>5. Next Steps:</h3>";
    if (empty($all_batches)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
        echo "<h4>To Create Test Data for Rollback:</h4>";
        echo "<ol>";
        echo "<li><strong>Create a test CSV file</strong> with employee data</li>";
        echo "<li><strong>Go to admin/upload_karyawan.php</strong></li>";
        echo "<li><strong>Upload the CSV file</strong> (this will create BATCH_INSERT records)</li>";
        echo "<li><strong>Return to batch history</strong> to see rollback options</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<h4>Rollback Should Now Work:</h4>";
        echo "<ol>";
        echo "<li><strong>Go to admin/batch_employee_history.php</strong></li>";
        echo "<li><strong>Look for BATCH_INSERT records</strong></li>";
        echo "<li><strong>Click the yellow 'Rollback' button</strong></li>";
        echo "<li><strong>Follow the rollback process</strong></li>";
        echo "</ol>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<script>
function fixBatch(batchId) {
    if (confirm('Fix rollback capability for batch ID ' + batchId + '?')) {
        fetch('fix_batch_rollback.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'batch_id=' + batchId
        })
        .then(response => response.text())
        .then(data => {
            alert('Batch fixed: ' + data);
            location.reload();
        })
        .catch(error => {
            alert('Error: ' + error);
        });
    }
}

function fixAllBatches() {
    if (confirm('Fix rollback capability for all BATCH_INSERT records?')) {
        fetch('fix_batch_rollback.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'fix_all=1'
        })
        .then(response => response.text())
        .then(data => {
            alert('All batches fixed: ' + data);
            location.reload();
        })
        .catch(error => {
            alert('Error: ' + error);
        });
    }
}
</script>

<style>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.btn {
    padding: 5px 10px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}
</style>
