/**
 * Custom Modal Library
 * Pengganti untuk alert(), confirm(), dan prompt() bawaan browser
 */
class CustomModal {
    constructor() {
        this.modalOverlay = null;
        this.modal = null;
        this.createModalElements();
        this.callbacks = {
            confirm: null,
            cancel: null,
            close: null
        };
    }

    createModalElements() {
        // Buat overlay
        this.modalOverlay = document.createElement('div');
        this.modalOverlay.className = 'custom-modal-overlay';

        // Buat modal container
        this.modal = document.createElement('div');
        this.modal.className = 'custom-modal';

        // Tambahkan modal ke overlay
        this.modalOverlay.appendChild(this.modal);

        // Tambahkan overlay ke body
        document.body.appendChild(this.modalOverlay);

        // Event listener untuk close modal saat klik overlay
        this.modalOverlay.addEventListener('click', (e) => {
            if (e.target === this.modalOverlay) {
                this.close(true); // true indicates this is a cancel action
            }
        });

        // Event listener untuk keyboard escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modalOverlay.classList.contains('active')) {
                this.close(true); // true indicates this is a cancel action
            }
        });
    }

    setContent(options) {
        const { title, message, type, confirmText, cancelText, showClose } = options;

        // Reset modal content
        this.modal.innerHTML = '';
        this.modal.className = 'custom-modal';

        if (type) {
            this.modal.classList.add(type);
        }

        // Create header
        const header = document.createElement('div');
        header.className = 'custom-modal-header';

        // Add icon based on type
        let iconClass = '';
        switch (type) {
            case 'alert':
                iconClass = 'fas fa-exclamation-circle';
                break;
            case 'confirm':
                iconClass = 'fas fa-question-circle';
                break;
            case 'info':
                iconClass = 'fas fa-info-circle';
                break;
            case 'success':
                iconClass = 'fas fa-check-circle';
                break;
            case 'warning':
                iconClass = 'fas fa-exclamation-triangle';
                break;
            default:
                iconClass = 'fas fa-bell';
        }

        // Create title with icon
        const titleElement = document.createElement('h3');
        titleElement.className = 'modal-title';
        titleElement.innerHTML = `<i class="${iconClass} custom-modal-icon"></i>${title || 'Notification'}`;
        header.appendChild(titleElement);

        // Add close button if needed
        if (showClose !== false) {
            const closeBtn = document.createElement('button');
            closeBtn.className = 'custom-modal-close';
            closeBtn.innerHTML = '&times;';
            closeBtn.addEventListener('click', () => this.close());
            header.appendChild(closeBtn);
        }

        this.modal.appendChild(header);

        // Create body
        const body = document.createElement('div');
        body.className = 'custom-modal-body';
        body.innerHTML = message || '';
        this.modal.appendChild(body);

        // Create footer with buttons
        const footer = document.createElement('div');
        footer.className = 'custom-modal-footer';

        // For confirm modals, add cancel button
        if (type === 'confirm') {
            const cancelBtn = document.createElement('button');
            cancelBtn.className = 'custom-modal-btn custom-modal-btn-secondary';
            cancelBtn.textContent = cancelText || 'Batal';
            cancelBtn.addEventListener('click', () => {
                this.close(true); // true indicates this is a cancel action
            });
            footer.appendChild(cancelBtn);
        }

        // Add confirm/OK button
        const confirmBtn = document.createElement('button');
        confirmBtn.className = 'custom-modal-btn custom-modal-btn-primary';
        confirmBtn.textContent = confirmText || 'OK';
        confirmBtn.addEventListener('click', () => {
            // Store the callback before closing the modal
            const confirmCallback = this.callbacks.confirm;

            // Close the modal first
            this.close();

            // Then execute the callback after a short delay
            // This ensures the modal is fully closed before executing potentially complex code
            if (typeof confirmCallback === 'function') {
                setTimeout(() => {
                    confirmCallback();
                }, 50);
            }
        });
        footer.appendChild(confirmBtn);

        this.modal.appendChild(footer);
    }

    open(options = {}) {
        this.setContent(options);
        this.modalOverlay.classList.add('active');
        this.modal.classList.add('modal-fade-in');

        // Set focus to confirm button
        setTimeout(() => {
            const confirmBtn = this.modal.querySelector('.custom-modal-btn-primary');
            if (confirmBtn) {
                confirmBtn.focus();
            }
        }, 100);

        // Set callbacks
        this.callbacks.confirm = options.onConfirm || null;
        this.callbacks.cancel = options.onCancel || null;
        this.callbacks.close = options.onClose || null;

        return this;
    }

    close(isCancel = false) {
        this.modal.classList.remove('modal-fade-in');
        this.modal.classList.add('modal-fade-out');

        // Store callbacks before closing
        const closeCallback = this.callbacks.close;
        const cancelCallback = isCancel ? this.callbacks.cancel : null;

        setTimeout(() => {
            this.modalOverlay.classList.remove('active');
            this.modal.classList.remove('modal-fade-out');

            // Execute cancel callback if this is a cancel action
            if (isCancel && typeof cancelCallback === 'function') {
                cancelCallback();
            }

            // Always execute close callback if provided
            if (typeof closeCallback === 'function') {
                closeCallback();
            }
        }, 300);

        return this;
    }

    // Static methods as replacements for native functions
    static alert(message, title = 'Peringatan', options = {}) {
        const modal = new CustomModal();
        return modal.open({
            type: 'alert',
            title: title,
            message: message,
            confirmText: options.confirmText || 'OK',
            onConfirm: options.onConfirm || null,
            onClose: options.onClose || null
        });
    }

    static confirm(message, title = 'Konfirmasi', options = {}) {
        const modal = new CustomModal();

        // Ensure we have valid callback functions
        const onConfirm = typeof options.onConfirm === 'function' ? options.onConfirm : null;
        const onCancel = typeof options.onCancel === 'function' ? options.onCancel : null;
        const onClose = typeof options.onClose === 'function' ? options.onClose : null;

        return modal.open({
            type: 'confirm',
            title: title,
            message: message,
            confirmText: options.confirmText || 'Ya',
            cancelText: options.cancelText || 'Tidak',
            onConfirm: function() {
                if (onConfirm) {
                    // Execute the callback with a slight delay to ensure the modal is closed first
                    setTimeout(() => {
                        onConfirm();
                    }, 100);
                }
            },
            onCancel: function() {
                if (onCancel) {
                    // Execute the callback with a slight delay to ensure the modal is closed first
                    setTimeout(() => {
                        onCancel();
                    }, 100);
                }
            },
            onClose: function() {
                if (onClose) {
                    // Execute the callback with a slight delay to ensure the modal is closed first
                    setTimeout(() => {
                        onClose();
                    }, 100);
                }
            }
        });
    }

    static info(message, title = 'Informasi', options = {}) {
        const modal = new CustomModal();
        return modal.open({
            type: 'info',
            title: title,
            message: message,
            confirmText: options.confirmText || 'OK',
            onConfirm: options.onConfirm || null,
            onClose: options.onClose || null
        });
    }

    static success(message, title = 'Sukses', options = {}) {
        const modal = new CustomModal();
        return modal.open({
            type: 'success',
            title: title,
            message: message,
            confirmText: options.confirmText || 'OK',
            onConfirm: options.onConfirm || null,
            onClose: options.onClose || null
        });
    }

    static warning(message, title = 'Peringatan', options = {}) {
        const modal = new CustomModal();
        return modal.open({
            type: 'warning',
            title: title,
            message: message,
            confirmText: options.confirmText || 'OK',
            onConfirm: options.onConfirm || null,
            onClose: options.onClose || null
        });
    }
}

// Override native alert, confirm, and prompt functions
window.originalAlert = window.alert;
window.originalConfirm = window.confirm;
window.originalPrompt = window.prompt;

window.alert = function(message) {
    CustomModal.alert(message);
};

/**
 * This is a special function to handle confirmation dialogs
 * It should be used instead of the standard confirm() function
 *
 * @param {string} message - The confirmation message
 * @param {Function} onYes - Function to call when user clicks "Yes"
 * @param {Function} onNo - Function to call when user clicks "No" (optional)
 * @param {string} title - The title of the confirmation dialog (optional)
 */
window.confirmAction = function(message, onYes, onNo, title = 'Konfirmasi') {
    const modal = new CustomModal();
    modal.open({
        type: 'confirm',
        title: title,
        message: message,
        confirmText: 'Ya',
        cancelText: 'Tidak',
        onConfirm: function() {
            if (typeof onYes === 'function') {
                // Execute with a slight delay to ensure modal is closed
                setTimeout(() => {
                    onYes();
                }, 100);
            }
        },
        onCancel: function() {
            if (typeof onNo === 'function') {
                // Execute with a slight delay to ensure modal is closed
                setTimeout(() => {
                    onNo();
                }, 100);
            }
        }
    });
};

/**
 * Override the standard confirm function
 * This is not ideal because confirm() is synchronous, but we're doing our best
 */
window.confirm = function(message) {
    // Store the original caller's code
    const caller = arguments.callee.caller;
    let confirmResult = false;

    // Create a custom modal
    const modal = new CustomModal();
    modal.open({
        type: 'confirm',
        title: 'Konfirmasi',
        message: message,
        confirmText: 'Ya',
        cancelText: 'Tidak',
        onConfirm: function() {
            confirmResult = true;

            // Try to find the form that might be associated with this confirmation
            if (caller && typeof caller === 'function') {
                try {
                    // Look for form submissions in the caller function
                    const callerStr = caller.toString();
                    const formSubmitMatch = callerStr.match(/document\.getElementById\(['"](.*?)['"].*?\)\.submit\(\)/);

                    if (formSubmitMatch && formSubmitMatch[1]) {
                        const formId = formSubmitMatch[1];
                        const form = document.getElementById(formId);
                        if (form) {
                            setTimeout(() => {
                                form.submit();
                            }, 100);
                        }
                    }
                } catch (e) {
                    console.error('Error executing form submit:', e);
                }
            }

            // Also try to find and click any button that might be associated with this confirmation
            setTimeout(() => {
                // Look for buttons that might be related to the confirmation
                const buttons = document.querySelectorAll('button[id*="confirm"], button[id*="yes"], button[id*="ok"], button[class*="confirm"], button[class*="yes"], button[class*="ok"]');
                if (buttons.length > 0) {
                    // Click the first matching button
                    buttons[0].click();
                }
            }, 100);
        }
    });

    // Always return false from the synchronous part
    // The actual action will be handled by the onConfirm callback
    return confirmResult;
};

// Expose the CustomModal class globally
window.CustomModal = CustomModal;
