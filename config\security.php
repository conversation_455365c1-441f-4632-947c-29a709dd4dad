<?php
/**
 * File keamanan global untuk website
 * File ini berisi fungsi-fungsi keamanan dan validasi akses
 */

// Konfigurasi session yang lebih aman
ini_set('session.cookie_httponly', 1); // Mencegah akses cookie melalui JavaScript
ini_set('session.use_only_cookies', 1); // Hanya gunakan cookies untuk session
// Nonaktifkan sementara untuk debugging
// ini_set('session.cookie_secure', 1); // Hanya kirim cookie melalui HTTPS
ini_set('session.cookie_samesite', 'Lax'); // Mencegah CSRF
ini_set('session.gc_maxlifetime', 3600); // Session timeout setelah 1 jam

// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Regenerate session ID setiap 15 menit untuk mencegah session fixation
if (!isset($_SESSION['last_regeneration']) || (time() - $_SESSION['last_regeneration']) > 900) {
    session_regenerate_id(true);
    $_SESSION['last_regeneration'] = time();
}

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role yang sesuai
if ($_SESSION['role_id'] != 6) { // Ganti angka sesuai role
    header('Location: ../view/login.php'); // Redirect ke halaman utama jika tidak punya akses
    exit();
}

// Fungsi untuk menghasilkan token CSRF
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time']) || (time() - $_SESSION['csrf_token_time']) > 3600) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    }
    return $_SESSION['csrf_token'];
}

// Fungsi untuk memverifikasi token CSRF
function verify_csrf_token($token) {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return false;
    }

    // Token kadaluarsa setelah 1 jam
    if ((time() - $_SESSION['csrf_token_time']) > 3600) {
        return false;
    }

    return hash_equals($_SESSION['csrf_token'], $token);
}

// Fungsi untuk sanitasi input
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Fungsi untuk validasi email
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Fungsi untuk validasi URL
function validate_url($url) {
    return filter_var($url, FILTER_VALIDATE_URL);
}

// Fungsi untuk validasi integer
function validate_int($int, $min = null, $max = null) {
    $options = array();
    if ($min !== null) $options['min_range'] = $min;
    if ($max !== null) $options['max_range'] = $max;

    return filter_var($int, FILTER_VALIDATE_INT, array('options' => $options));
}

// Fungsi untuk memeriksa rate limit
function checkRateLimit($user_id, $action_type = 'login', $timeWindow = 300, $maxAttempts = 5) {
    global $conn;

    if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
        return false; // Jika tidak bisa mengakses database, anggap tidak rate limited
    }

    try {
        $query = "SELECT COUNT(*) as attempts FROM rate_limits
                  WHERE user_id = ? AND action_type = ? AND attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("isi", $user_id, $action_type, $timeWindow);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        // Jika jumlah percobaan melebihi batas
        if ($row['attempts'] >= $maxAttempts) {
            return true; // Rate limited
        }

        // Catat percobaan baru
        $insertQuery = "INSERT INTO rate_limits (user_id, action_type, attempt_time, ip_address)
                        VALUES (?, ?, NOW(), ?)";
        $insertStmt = $conn->prepare($insertQuery);
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $insertStmt->bind_param("iss", $user_id, $action_type, $ip);
        $insertStmt->execute();

        return false; // Tidak rate limited
    } catch (Exception $e) {
        error_log("Error checking rate limit: " . $e->getMessage());
        return false; // Jika terjadi error, anggap tidak rate limited
    }
}

// Fungsi untuk menambahkan security headers
function add_security_headers() {
    // Content Security Policy
    header("Content-Security-Policy: default-src 'self'; script-src 'self' https://code.jquery.com https://cdn.jsdelivr.net 'unsafe-inline'; style-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-inline'; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self';");

    // Prevent Clickjacking
    header('X-Frame-Options: SAMEORIGIN');

    // Prevent MIME type sniffing
    header('X-Content-Type-Options: nosniff');

    // Enable XSS protection in browsers
    header('X-XSS-Protection: 1; mode=block');

    // Referrer Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');

    // Permissions Policy (formerly Feature Policy)
    header("Permissions-Policy: geolocation=(), microphone=(), camera=()");

    // HTTP Strict Transport Security (HSTS)
    header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
}

// Tambahkan security headers
add_security_headers();

// Fungsi untuk mencatat aktivitas keamanan
function log_security_event($user_id, $event_type, $description, $ip_address = null) {
    global $conn;

    if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
        // Jika koneksi database tidak tersedia, log ke file
        $log_dir = __DIR__ . '/../logs';
        if (!file_exists($log_dir)) {
            mkdir($log_dir, 0755, true);
        }

        $log_message = date('Y-m-d H:i:s') . " | User ID: $user_id | Event: $event_type | $description | IP: " . ($ip_address ?? $_SERVER['REMOTE_ADDR']) . "\n";
        error_log($log_message, 3, $log_dir . '/security.log');
        return false;
    }

    try {
        $ip = $ip_address ?? $_SERVER['REMOTE_ADDR'];
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

        $query = "INSERT INTO security_logs (user_id, event_type, description, ip_address, user_agent, created_at)
                  VALUES (?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("issss", $user_id, $event_type, $description, $ip, $user_agent);
        return $stmt->execute();
    } catch (Exception $e) {
        // Jika gagal menyimpan ke database, log ke file
        $log_dir = __DIR__ . '/../logs';
        if (!file_exists($log_dir)) {
            mkdir($log_dir, 0755, true);
        }

        $log_message = date('Y-m-d H:i:s') . " | User ID: $user_id | Event: $event_type | $description | IP: $ip | Error: " . $e->getMessage() . "\n";
        error_log($log_message, 3, $log_dir . '/security.log');
        return false;
    }
}
?>
