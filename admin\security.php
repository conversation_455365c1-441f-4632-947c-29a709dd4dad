<?php
// Periksa apakah session sudah dimulai sebelum memanggil session_start()
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Periksa apakah config.php sudah di-include sebelumnya
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Check if user has admin role (role_id = 99) or other authorized roles (2, 3, 4, 5)
if (!in_array($_SESSION['role_id'], [2, 3, 4, 5, 99])) {
    header('Location: ../view/login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// Fetch user details from database
$query = "SELECT id, role_id, name FROM users WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

// If user not found, redirect to login
if (!$user) {
    header('Location: ../view/login.php');
    exit();
}