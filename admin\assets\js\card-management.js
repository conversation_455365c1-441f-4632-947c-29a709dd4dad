// Fungsi untuk memuat data tabel
function loadTableData(page = 1) {
    const searchQuery = document.getElementById('search_input').value;
    const departmentSelect = document.getElementById('department');
    const selectedDepartment = departmentSelect.value;

    // <PERSON><PERSON><PERSON>an loading spinner
    document.getElementById('loading_placeholder').style.display = 'block';

    // Buat URL dengan parameter
    const url = `manage_card_numbers.php?page=${page}${searchQuery ? '&search=' + encodeURIComponent(searchQuery) : ''}${selectedDepartment ? '&dept=' + encodeURIComponent(selectedDepartment) : ''}`;

    // <PERSON>rim request AJAX
    fetch(url)
        .then(response => response.text())
        .then(html => {
            // Update konten tabel
            document.querySelector('.table-responsive').innerHTML = html;
            // Sembunyikan loading spinner
            document.getElementById('loading_placeholder').style.display = 'none';
            // Update URL browser tanpa reload
            window.history.pushState({}, '', url);
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('loading_placeholder').style.display = 'none';
        });
}

// Event listener untuk input pencarian
document.getElementById('search_input').addEventListener('input', debounce(() => {
    loadTableData();
}, 500));

// Event listener untuk dropdown departemen
document.getElementById('department').addEventListener('change', () => {
    loadTableData();
});

// Event listener untuk tombol reset
document.getElementById('reset_filter').addEventListener('click', () => {
    document.getElementById('search_input').value = '';
    document.getElementById('department').value = '';
    loadTableData();
});

// Fungsi debounce untuk mengurangi frekuensi request
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Load data saat halaman dimuat
document.addEventListener('DOMContentLoaded', () => {
    loadTableData();
});
