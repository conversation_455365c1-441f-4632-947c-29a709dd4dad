# Sistem Auto-Update Training Status

## Deskripsi
Sistem otomatis untuk mengupdate status training berdasarkan tanggal training yang sudah lewat. Sistem ini mengatasi masalah dimana training tidak ter-update statusnya jika tidak ada user yang mengakses halaman tertentu.

## Masalah yang Diselesaikan

### Sebelum:
- ❌ Training hanya diupdate jika ada user yang mengakses halaman tertentu
- ❌ Training bisa "terjebak" dalam status "Approved" meskipun sudah lewat berbulan-bulan
- ❌ Sistem bergantung pada user behavior
- ❌ Data laporan tidak akurat

### Sesudah:
- ✅ Training diupdate otomatis setiap hari via cron job
- ✅ Backup mechanism via hook di aplikasi
- ✅ Konsistensi data terjamin
- ✅ Monitoring dan logging lengkap

## Arsitektur Sistem

### 1. **Auto-Update Helper** (`config/auto_update_helper.php`)
- Fungsi terpusat untuk auto-update
- Logging ke database
- Error handling yang robust
- Statistics dan monitoring

### 2. **Cron Job** (`cron/auto_update_training.php`)
- <PERSON><PERSON><PERSON><PERSON> otomatis setiap hari jam 00:01
- Independent dari user access
- Comprehensive logging
- Health checks

### 3. **Application Hook** (`config/config.php`)
- Backup mechanism jika cron job gagal
- Berjalan saat user login
- Sekali per hari per session
- Non-blocking (tidak mengganggu aplikasi)

### 4. **Monitoring Dashboard** (`admin/auto_update_monitor.php`)
- Real-time monitoring
- Manual trigger capability
- Statistics dan reports
- System health checks

## Logika Update

### Training External (training_submissions):
```sql
-- Approved → Completed
UPDATE training_submissions 
SET status = 'Completed', completed_at = NOW()
WHERE status = 'Approved' 
AND (start_date < TODAY OR (end_date IS NOT NULL AND end_date < TODAY))

-- Pending → Canceled (dengan grace period 1 hari)
UPDATE training_submissions 
SET status = 'Canceled', canceled_at = NOW()
WHERE status = 'Pending' 
AND start_date < DATE_SUB(TODAY, INTERVAL 1 DAY)
```

### Training Internal (offline_training):
```sql
-- Approved → Completed
UPDATE offline_training 
SET status = 'Completed', updated_at = NOW()
WHERE status = 'Approved' 
AND (start_date < TODAY OR (end_date IS NOT NULL AND end_date < TODAY))
```

## Setup dan Instalasi

### 1. **Otomatis (Recommended)**
```bash
# Jalankan setup script
chmod +x setup_cron.sh
./setup_cron.sh
```

### 2. **Manual Setup**

#### A. Buat direktori logs:
```bash
mkdir -p logs
chmod 755 logs
```

#### B. Set permissions:
```bash
chmod 755 cron/auto_update_training.php
```

#### C. Install cron job:
```bash
# Edit crontab
crontab -e

# Tambahkan baris berikut:
1 0 * * * /usr/bin/php /path/to/training/cron/auto_update_training.php
```

#### D. Test cron job:
```bash
php cron/auto_update_training.php
```

## Monitoring dan Maintenance

### 1. **Dashboard Monitoring**
- URL: `admin/auto_update_monitor.php`
- Akses: Admin only (role_id = 99)
- Fitur:
  - Status hari ini
  - Pending counts
  - Manual trigger
  - Statistics 30 hari
  - Recent activity

### 2. **Log Files**
```bash
# Cron job logs
tail -f logs/auto_update_cron.log

# Database logs
SELECT * FROM auto_update_logs ORDER BY created_at DESC LIMIT 10;

# System error logs
tail -f /var/log/apache2/error.log | grep "auto-update"
```

### 3. **Health Checks**
```bash
# Cek cron job aktif
crontab -l | grep auto_update_training

# Test database connection
php -r "include 'config/config.php'; echo 'DB OK: ' . $conn->ping();"

# Test auto-update function
php -r "include 'config/config.php'; include 'config/auto_update_helper.php'; var_dump(isAutoUpdateRunToday(\$conn));"
```

## Troubleshooting

### 1. **Cron Job Tidak Berjalan**
```bash
# Cek cron service
sudo systemctl status cron

# Cek cron logs
sudo tail -f /var/log/cron.log

# Test manual
php cron/auto_update_training.php
```

### 2. **Permission Errors**
```bash
# Fix permissions
chmod 755 cron/auto_update_training.php
chmod 755 logs/
chown www-data:www-data logs/
```

### 3. **Database Errors**
```bash
# Cek koneksi database
php -r "include 'config/config.php'; echo \$conn->connect_error;"

# Cek tabel auto_update_logs
mysql -u root -p db_training -e "DESCRIBE auto_update_logs;"
```

### 4. **PHP Path Issues**
```bash
# Find PHP path
which php

# Update cron job dengan path yang benar
crontab -e
```

## Konfigurasi

### 1. **Disable Auto-Update untuk Session Tertentu**
```php
// Di awal script yang tidak ingin auto-update
$_SESSION['auto_update_disabled'] = true;
```

### 2. **Custom Grace Period**
Edit di `config/auto_update_helper.php`:
```php
// Ubah grace period dari 1 hari ke nilai lain
AND start_date < DATE_SUB(?, INTERVAL 2 DAY) // 2 hari grace period
```

### 3. **Custom Cron Schedule**
```bash
# Setiap 6 jam
0 */6 * * * /usr/bin/php /path/to/training/cron/auto_update_training.php

# Setiap hari jam 02:00
0 2 * * * /usr/bin/php /path/to/training/cron/auto_update_training.php

# Setiap hari kerja jam 01:00
0 1 * * 1-5 /usr/bin/php /path/to/training/cron/auto_update_training.php
```

## API Functions

### Core Functions:
- `autoUpdateTrainingStatus($conn)` - Main update function
- `isAutoUpdateRunToday($conn)` - Check if run today
- `forceAutoUpdateTrainingStatus($conn)` - Force run
- `getAutoUpdateStats($conn, $days)` - Get statistics

### Usage Examples:
```php
// Manual trigger
include 'config/auto_update_helper.php';
$results = forceAutoUpdateTrainingStatus($conn);

// Check status
$is_run = isAutoUpdateRunToday($conn);

// Get stats
$stats = getAutoUpdateStats($conn, 30);
```

## Database Schema

### auto_update_logs Table:
```sql
CREATE TABLE auto_update_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    update_date DATE NOT NULL,
    approved_to_completed INT DEFAULT 0,
    pending_to_canceled INT DEFAULT 0,
    total_updated INT DEFAULT 0,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_update_date (update_date)
);
```

## Performance Impact

### Minimal Impact:
- Cron job: Berjalan di background, tidak mempengaruhi user
- Application hook: Hanya sekali per hari per session
- Database queries: Optimized dengan prepared statements
- Logging: Asynchronous, tidak blocking

### Resource Usage:
- Memory: ~2-5MB per execution
- CPU: ~1-2 seconds execution time
- Database: 2-3 queries per run
- Disk: ~1KB log per day

## Security

### Access Control:
- Monitoring dashboard: Admin only
- Cron script: Server-level access required
- Database logs: Protected by application permissions

### Error Handling:
- All errors logged
- Graceful degradation
- Transaction rollback on failure
- No sensitive data in logs

## Future Enhancements

### Planned Features:
1. Email notifications untuk admin
2. Slack/Teams integration
3. Advanced scheduling options
4. Bulk operations interface
5. Export/import configurations

### Possible Improvements:
1. Real-time updates via WebSocket
2. Machine learning untuk prediksi training completion
3. Integration dengan calendar systems
4. Mobile app notifications

## Support

### Contact:
- Developer: Training System Team
- Documentation: AUTO_UPDATE_SYSTEM.md
- Monitoring: admin/auto_update_monitor.php
- Logs: logs/auto_update_cron.log

### Resources:
- Cron job tutorial: https://crontab.guru/
- PHP CLI documentation: https://php.net/manual/en/features.commandline.php
- MySQL events: https://dev.mysql.com/doc/refman/8.0/en/events.html
