<?php
include '../config/config.php';
// session_start() sudah dipanggil di config.php

// Aktifkan error reporting untuk debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Ambil informasi pengguna dari database
$user_id = $_SESSION['user_id'];
$query = "SELECT id, name, email, dept, bagian, jabatan, role_id, nik, password
          FROM users
          WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $user = $result->fetch_assoc();
    $user_id = $user['id'] ?? 'Tidak Diketahui';
    $user_name = $user['name'] ?? 'Tidak Diketahui';
    $email = $user['email'] ?? 'Tidak Diketahui';
    $dept = $user['dept'] ?? 'Tidak Diketahui';
    $bagian = $user['bagian'] ?? 'Tidak Diketahui';
    $jabatan = $user['jabatan'] ?? 'Tidak Diketahui';
    $role_id = $user['role_id'] ?? 'Tidak Diketahui';
    $nik = $user['nik'] ?? 'Tidak Diketahui';
    $current_password = $user['password'] ?? ''; // Default ke string kosong jika null
    // user_id sudah didefinisikan dari $_SESSION['user_id']
} else {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi variabel untuk verifikasi email
require_once 'email_helper.php';
$message = '';
$verification_code = '';
$new_email_temp = '';

// Cek apakah ada email yang sedang dalam proses verifikasi
if (isset($_SESSION['email_verification'])) {
    $new_email_temp = $_SESSION['email_verification']['email'];
    $verification_deadline = $_SESSION['email_verification']['deadline'];

    // Cek apakah kode verifikasi sudah kadaluarsa (15 menit)
    if (time() > $verification_deadline) {
        unset($_SESSION['email_verification']);
        $new_email_temp = '';
    }
}

// Cek apakah ada flag update_success dari redirect
if (isset($_SESSION['update_success']) && $_SESSION['update_success']) {
    $success_message = isset($_SESSION['update_success_message']) ? $_SESSION['update_success_message'] : 'Informasi berhasil diperbarui!';
    echo "<script>
        document.addEventListener('DOMContentLoaded', function() {
            const modal = new CustomModal();
            modal.alert({
                title: 'Berhasil!',
                message: '" . addslashes($success_message) . "',
                type: 'success'
            });
        });
    </script>";
    unset($_SESSION['update_success']); // Hapus flag setelah digunakan
    unset($_SESSION['update_success_message']); // Hapus pesan setelah digunakan
}
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Debug output
    error_log("POST data received: " . print_r($_POST, true));

    // Gunakan isset untuk menghindari trim(null)
    $edit_profile = isset($_POST['edit_profile']) ? (int)$_POST['edit_profile'] : 0;
    $edit_password = isset($_POST['edit_password']) ? (int)$_POST['edit_password'] : 0;

    // Ambil nilai default dari hidden input
    $default_username = isset($_POST['default_username']) ? trim($_POST['default_username']) : $user_name;
    $default_email = isset($_POST['default_email']) ? trim($_POST['default_email']) : $email;

    // Jika form profile tidak aktif, gunakan nilai default
    $new_username = $edit_profile ? (isset($_POST['username']) ? trim($_POST['username']) : $user_name) : $default_username;
    $new_email = $edit_profile ? (isset($_POST['email']) ? trim($_POST['email']) : $email) : $default_email;

    // Jika form password tidak aktif, kosongkan nilai password
    $current_input_password = $edit_password ? (isset($_POST['current_password']) ? trim($_POST['current_password']) : '') : '';
    $new_password = $edit_password ? (isset($_POST['new_password']) ? trim($_POST['new_password']) : '') : '';
    $confirm_password = $edit_password ? (isset($_POST['confirm_password']) ? trim($_POST['confirm_password']) : '') : '';

    error_log("edit_profile: $edit_profile, edit_password: $edit_password");
    error_log("new_username: $new_username, new_email: $new_email");
    error_log("current_username: $user_name, current_email: $email");
    error_log("current_input_password: " . (!empty($current_input_password) ? 'provided' : 'empty'));
    error_log("new_password: " . (!empty($new_password) ? 'provided' : 'empty'));
    error_log("confirm_password: " . (!empty($confirm_password) ? 'provided' : 'empty'));

    // Debug output untuk perbandingan nilai
    error_log("Comparing values: new_username='$new_username' vs user_name='$user_name'");
    error_log("Comparing values: new_email='$new_email' vs email='$email'");
    error_log("Comparing values: default_username='$default_username' vs user_name='$user_name'");
    error_log("Comparing values: default_email='$default_email' vs email='$email'");

    // Validasi input
    $validation_error = false;

    // Validasi username dan email jika edit_profile aktif
    if ($edit_profile) {
        if (empty($new_username)) {
            echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Error!',
        message: 'Username tidak boleh kosong!',
        type: 'alert'
    });
</script>";
            $validation_error = true;
        } elseif (empty($new_email)) {
            echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Error!',
        message: 'Email tidak boleh kosong!',
        type: 'alert'
    });
</script>";
            $validation_error = true;
        } elseif (!filter_var($new_email, FILTER_VALIDATE_EMAIL)) {
            echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Error!',
        message: 'Format email tidak valid!',
        type: 'alert'
    });
</script>";
            $validation_error = true;
        } elseif ($new_email !== $email) {
            // Jika email baru berbeda dengan email saat ini
            // Periksa apakah email baru valid
            if (!filter_var($new_email, FILTER_VALIDATE_EMAIL)) {
                $message_html = '<div class="alert alert-danger" style="text-align: left; padding: 15px; background-color: #fff2f2; border-left: 4px solid #c40000; margin-bottom: 15px;">';
                $message_html .= '<strong>Format Email Tidak Valid!</strong><br>';
                $message_html .= 'Silakan masukkan alamat email yang valid.';
                $message_html .= '</div>';

                echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Email Tidak Valid',
        message: " . json_encode($message_html) . ",
        type: 'alert'
    });
</script>";
                $validation_error = true;
            } elseif (!isset($_SESSION['email_verification']) || $_SESSION['email_verification']['email'] !== $new_email) {
                // Kirim kode verifikasi ke email baru
                $verification_code = generateVerificationCode();

                // Simpan email baru sebagai pending_email di database
                $update_pending_query = "UPDATE users SET
                                        pending_email = ?,
                                        verification_code = ?,
                                        verification_expires = DATE_ADD(NOW(), INTERVAL 15 MINUTE)
                                        WHERE id = ?";
                $update_pending_stmt = $conn->prepare($update_pending_query);
                $update_pending_stmt->bind_param("ssi", $new_email, $verification_code, $user_id);
                $update_pending_stmt->execute();

                if (sendVerificationEmail($new_email, $verification_code, true, $user_id)) {
                    // Simpan informasi verifikasi di session
                    $_SESSION['email_verification'] = [
                        'email' => $new_email,
                        'code' => $verification_code,
                        'deadline' => time() + (15 * 60) // 15 menit
                    ];
                    $escaped_email = htmlspecialchars($new_email, ENT_QUOTES, 'UTF-8');
                    $message_html = '<div class="alert alert-info" style="text-align: left; padding: 15px; background-color: #e8f4fd; border-left: 4px solid #0c5460; margin-bottom: 15px;">';
                    $message_html .= '<strong>Verifikasi Email Diperlukan</strong><br>';
                    $message_html .= 'Email verifikasi telah dikirim ke: <strong>' . $escaped_email . '</strong><br><br>';
                    $message_html .= '<i class="fas fa-info-circle"></i> Untuk keamanan akun Anda, kami perlu memverifikasi bahwa Anda memiliki akses ke alamat email baru ini.<br><br>';
                    $message_html .= 'Silakan:<br>';
                    $message_html .= '1. Cek inbox atau folder spam email Anda<br>';
                    $message_html .= '2. Klik link verifikasi yang ada di email<br>';
                    $message_html .= '3. Setelah verifikasi berhasil, email Anda akan diperbarui';
                    $message_html .= '</div>';

                    echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Email Verifikasi Telah Dikirim',
        message: " . json_encode($message_html) . ",
        type: 'info'
    });
</script>";
                    $validation_error = true;
                } else {
                    $escaped_email = htmlspecialchars($new_email, ENT_QUOTES, 'UTF-8');
                    $message_html = '<div class="alert alert-danger" style="text-align: left; padding: 15px; background-color: #fff2f2; border-left: 4px solid #c40000; margin-bottom: 15px;">';
                    $message_html .= '<strong>Terjadi Kesalahan!</strong><br>';
                    $message_html .= 'Sistem tidak dapat mengirim kode verifikasi ke email: <strong>' . $escaped_email . '</strong><br><br>';
                    $message_html .= 'Kemungkinan penyebab:<br>';
                    $message_html .= '- Alamat email tidak valid atau tidak dapat dijangkau<br>';
                    $message_html .= '- Masalah pada server email<br><br>';
                    $message_html .= 'Silakan periksa alamat email Anda dan coba lagi.';
                    $message_html .= '</div>';

                    echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Gagal Mengirim Kode Verifikasi',
        message: " . json_encode($message_html) . ",
        type: 'alert'
    });
</script>";
                    $validation_error = true;
                }
            } elseif (!isset($_POST['verification_code'])) {
                $verification_email = $_SESSION['email_verification']['email'];
                $escaped_verification_email = htmlspecialchars($verification_email, ENT_QUOTES, 'UTF-8');

                $message_html = '<div class="alert alert-warning" style="text-align: left; padding: 15px; background-color: #fff3cd; border-left: 4px solid #856404; margin-bottom: 15px;">';
                $message_html .= '<strong>Perhatian!</strong><br>';
                $message_html .= 'Anda harus memasukkan kode verifikasi untuk menyelesaikan perubahan email.<br><br>';
                $message_html .= 'Kode verifikasi telah dikirim ke: <strong>' . $escaped_verification_email . '</strong><br>';
                $message_html .= 'Silakan periksa inbox atau folder spam email Anda.';
                $message_html .= '</div>';

                echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Kode Verifikasi Diperlukan',
        message: " . json_encode($message_html) . ",
        type: 'warning'
    });
</script>";
                $validation_error = true;
            } elseif ($_POST['verification_code'] !== $_SESSION['email_verification']['code']) {
                $verification_email = $_SESSION['email_verification']['email'];
                $entered_code = $_POST['verification_code'];
                $escaped_verification_email = htmlspecialchars($verification_email, ENT_QUOTES, 'UTF-8');

                $message_html = '<div class="alert alert-danger" style="text-align: left; padding: 15px; background-color: #fff2f2; border-left: 4px solid #c40000; margin-bottom: 15px;">';
                $message_html .= '<strong>Kode Tidak Cocok!</strong><br>';
                $message_html .= 'Kode verifikasi yang Anda masukkan tidak valid.<br><br>';
                $message_html .= 'Pastikan Anda memasukkan kode 6 digit yang dikirim ke: <strong>' . $escaped_verification_email . '</strong><br>';
                $message_html .= 'Atau klik link verifikasi yang ada di email.';
                $message_html .= '</div>';

                echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Kode Verifikasi Tidak Valid',
        message: " . json_encode($message_html) . ",
        type: 'alert'
    });
</script>";
                $validation_error = true;
            } else {
                // Kode verifikasi valid, update email di database
                if (updateVerifiedEmail($user_id, $conn)) {
                    // Email berhasil diperbarui, hapus session verifikasi
                    unset($_SESSION['email_verification']);

                    // Set email baru sebagai email saat ini
                    $email = $new_email;

                    $escaped_email = htmlspecialchars($new_email, ENT_QUOTES, 'UTF-8');

                    $message_html = '<div class="alert alert-success" style="text-align: left; padding: 15px; background-color: #f0fff4; border-left: 4px solid #0d6832; margin-bottom: 15px;">';
                    $message_html .= '<strong>Selamat!</strong><br>';
                    $message_html .= 'Email Anda berhasil diverifikasi dan diperbarui menjadi: <strong>' . $escaped_email . '</strong><br><br>';
                    $message_html .= 'Anda akan menerima semua notifikasi sistem di alamat email baru ini.';
                    $message_html .= '</div>';

                    echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Email Berhasil Diperbarui!',
        message: " . json_encode($message_html) . ",
        type: 'success'
    });
</script>";
                }
            }
        }
    }

    // Validasi password jika edit_password aktif, ada input password, dan tidak ada error validasi sebelumnya
    if ($edit_password && !$validation_error) {
        // Jika ada input password saat ini, validasi password
        if (!empty($current_input_password)) {
            // Validasi password saat ini
            if (empty($current_password) || !password_verify($current_input_password, $current_password)) {
                echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Error!',
        message: 'Password saat ini salah!',
        type: 'alert'
    });
</script>";
                error_log("Password verification failed: current_input_password=$current_input_password");
                error_log("Current password from DB exists: " . (empty($current_password) ? 'No' : 'Yes'));
                error_log("Password verify result: " . (password_verify($current_input_password, $current_password) ? 'Success' : 'Failed'));
                $validation_error = true;
            }
            // Jika password saat ini benar, periksa password baru
            elseif (!empty($new_password) && $new_password !== $confirm_password) {
                echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Error!',
        message: 'Konfirmasi password tidak cocok!',
        type: 'alert'
    });
</script>";
                $validation_error = true;
            }
            // Jika password saat ini benar tapi password baru kosong, anggap pengguna hanya ingin mengubah username/email
            elseif (empty($new_password)) {
                // Set edit_password ke false karena tidak ada perubahan password
                $edit_password = 0;
                error_log("Password not changing: edit_password set to 0");
            }
        } else {
            // Jika tidak ada input password saat ini tapi pengguna mencoba mengubah password
            if (!empty($new_password) || !empty($confirm_password)) {
                echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Error!',
        message: 'Masukkan password saat ini untuk mengubah password!',
        type: 'alert'
    });
</script>";
                $validation_error = true;
            } else {
                // Jika semua field password kosong, anggap pengguna hanya ingin mengubah username/email
                $edit_password = 0;
                error_log("No password fields filled: edit_password set to 0");
            }
        }
    }

    // Jika tidak ada error validasi, lanjutkan dengan update
    if (!$validation_error) {
        // Debug output
        error_log("Processing changes: edit_profile=$edit_profile, edit_password=$edit_password");

        // Cek apakah ada perubahan pada username, email, atau password
        $username_changed = $new_username !== $user_name;
        $email_changed = $new_email !== $email;
        $profile_changed = $edit_profile && ($username_changed || $email_changed);
        $password_changed = $edit_password && !empty($new_password);
        $has_changes = $profile_changed || $password_changed;

        error_log("Username changed: " . ($username_changed ? 'true' : 'false') . " ($new_username vs $user_name)");
        error_log("Email changed: " . ($email_changed ? 'true' : 'false') . " ($new_email vs $email)");

        error_log("edit_profile: $edit_profile, profile_changed: " . ($profile_changed ? 'true' : 'false'));
        error_log("edit_password: $edit_password, password_changed: " . ($password_changed ? 'true' : 'false'));
        error_log("has_changes: " . ($has_changes ? 'true' : 'false'));

        if (!$has_changes) {
            // Tidak ada perubahan, tampilkan pesan
            echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Informasi',
        message: 'Tidak ada perubahan yang dilakukan.',
        type: 'info'
    });
</script>";
            error_log("No changes detected");
        } else {
            error_log("Changes detected, proceeding with update");
            // Cek apakah email baru sudah digunakan oleh pengguna lain (nama boleh sama)
            $check_query = "SELECT id FROM users WHERE email = ? AND id != ?";
            $stmt_check = $conn->prepare($check_query);
            $stmt_check->bind_param("si", $new_email, $user_id);
            $stmt_check->execute();
            $check_result = $stmt_check->get_result();

            if ($check_result->num_rows > 0) {
                echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Error!',
        message: 'Email sudah digunakan oleh pengguna lain!',
        type: 'alert'
    });
</script>";
            } else {
                // Update data pengguna
                $update_query = "UPDATE users SET name = ?";
                $params = [$new_username];
                $types = "s";

                // Hanya update email jika tidak ada perubahan email atau jika verifikasi sudah selesai
                if ($email_changed) {
                    // Email akan diupdate melalui proses verifikasi
                    error_log("Email change detected but will be updated after verification");
                } else {
                    $update_query .= ", email = ?";
                    $params[] = $new_email;
                    $types .= "s";
                }

                if (!empty($new_password)) {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $update_query .= ", password = ?";
                    $params[] = $hashed_password;
                    $types .= "s";
                    error_log("New password will be updated. Length: " . strlen($new_password));
                    error_log("Hashed password length: " . strlen($hashed_password));
                }

                $update_query .= " WHERE id = ?";
                $params[] = $user_id;
                $types .= "i";

                $stmt_update = $conn->prepare($update_query);
                $stmt_update->bind_param($types, ...$params);

                error_log("Executing update query: $update_query");
                error_log("Parameters: " . print_r($params, true));
                error_log("Types string: $types");
                error_log("User ID: $user_id");

                if ($stmt_update->execute()) {
                    error_log("Update successful");
                    if (!empty($new_password)) {
                        $_SESSION['update_success_message'] = 'Informasi dan password berhasil diperbarui!';
                        error_log("Password updated");
                    } else {
                        $_SESSION['update_success_message'] = 'Informasi berhasil diperbarui!';
                        error_log("Information updated");
                    }
                    $_SESSION['user_name'] = $new_username; // Update session
                    $_SESSION['update_success'] = true; // Set flag untuk menampilkan pesan sukses
                    error_log("Session updated and redirecting: user_name=$new_username, email=$new_email");

                    // Redirect ke halaman yang sama untuk me-refresh data
                    header('Location: ' . $_SERVER['PHP_SELF']);
                    exit();
                } else {
                    echo "<script>
    const modal = new CustomModal();
    modal.alert({
        title: 'Error!',
        message: 'Gagal memperbarui informasi!',
        type: 'alert'
    });
</script>";
                    error_log("Update failed: {$stmt_update->error}");
                }
                $stmt_update->close();
            }
            $stmt_check->close();
        }
    }
}

$stmt->close();
mysqli_close($conn);
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    /* Tambahan style untuk form verifikasi */
    .verification-code-input {
        text-align: center;
        letter-spacing: 0.5em;
        font-size: 1.2em;
        padding: 10px;
        margin: 10px 0;
        width: 200px;
    }
    .verification-info {
        background-color: #f8f9fa;
        border-left: 4px solid #BF0000;
        padding: 15px;
        margin: 15px 0;
        font-size: 0.9em;
        color: #666;
    }
    .resend-code {
        color: #BF0000;
        text-decoration: underline;
        cursor: pointer;
        margin-top: 10px;
        display: inline-block;
    }
    body {
        font-family: Arial, sans-serif;
        background-color: #f4f4f4;
        margin: 0;
        padding: 0;
    }
    .container-form {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 80vh;
        margin-top: 70px; /* Add top margin to prevent navbar overlap */
        position: relative;
        z-index: 1; /* Ensure it's above the navbar */
    }

    @media screen and (max-width: 768px) {
        .container-form {
            margin-top: 60px; /* Adjust for smaller screens */
        }
    }

    @media screen and (max-width: 480px) {
        .container-form {
            margin-top: 55px; /* Adjust for mobile screens */
        }
    }
    .form-container {
        background: #fff;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 500px;
        text-align: center;
        transition: all 0.3s ease;
    }

    @media screen and (max-width: 768px) {
        .form-container {
            padding: 25px;
            max-width: 90%;
        }
    }

    @media screen and (max-width: 480px) {
        .form-container {
            padding: 20px 15px;
            max-width: 95%;
        }
    }
    h2 {
        color: #BF0000;
        margin-bottom: 20px;
        font-size: 2rem;
        font-weight: 600;
    }

    @media screen and (max-width: 768px) {
        h2 {
            font-size: 1.8rem;
        }
    }

    @media screen and (max-width: 480px) {
        h2 {
            font-size: 1.6rem;
            margin-bottom: 15px;
        }
    }
    .info-box {
        background: #f9f9f9;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 25px;
        text-align: left;
        border-left: 4px solid #BF0000;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .info-box p {
        margin: 12px 0;
        font-size: 1.1rem;
        color: #555;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
    }

    .info-box p:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .info-box strong {
        color: #333;
        display: inline-block;
        width: 120px;
        font-weight: 600;
    }

    @media screen and (max-width: 480px) {
        .info-box {
            padding: 15px;
        }

        .info-box p {
            font-size: 1rem;
            margin: 10px 0;
        }

        .info-box strong {
            width: 100px;
        }
    }
    .edit-form {
        margin-top: 25px;
        background: #fff;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .form-section {
        margin-top: 25px;
        border-top: 1px solid #eee;
        padding-top: 20px;
        position: relative;
    }

    .form-section:before {
        content: '';
        position: absolute;
        top: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 3px;
        background-color: #BF0000;
        border-radius: 2px;
    }

    .form-section h3 {
        color: #BF0000;
        margin-bottom: 15px;
        font-size: 1.3rem;
        font-weight: 600;
        text-align: center;
    }

    @media screen and (max-width: 480px) {
        .edit-form {
            padding: 15px;
        }

        .form-section {
            margin-top: 20px;
            padding-top: 15px;
        }

        .form-section h3 {
            font-size: 1.2rem;
        }
    }

.info-text {
    font-size: 0.9em;
    color: #666;
    margin: 8px 0;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border-left: 3px solid #BF0000;
    display: block;
}

@media screen and (max-width: 480px) {
    .info-text {
        font-size: 0.85em;
        padding: 6px 10px;
    }
}
    .edit-form label {
        display: block;
        text-align: left;
        margin: 12px 0 8px;
        font-weight: 600;
        color: #444;
        font-size: 15px;
    }

    .edit-form .input-group {
        position: relative;
        margin-bottom: 18px;
    }

    @media screen and (max-width: 480px) {
        .edit-form label {
            font-size: 14px;
            margin: 10px 0 6px;
        }

        .edit-form .input-group {
            margin-bottom: 15px;
        }
    }
    .edit-form input {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-sizing: border-box;
        font-size: 15px;
        transition: all 0.3s ease;
    }

    .edit-form input:focus {
        border-color: #BF0000;
        outline: none;
        box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
    }

    @media screen and (max-width: 480px) {
        .edit-form input {
            padding: 10px;
            font-size: 14px;
        }
    }
    .edit-form .toggle-password {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #666;
        user-select: none;
        /* Tambahkan style untuk icon */
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background: transparent;
        border: none;
        padding: 0;
    }

    .toggle-password:hover {
        color: rgb(157, 0, 0);
    }

    .toggle-password i {
        font-size: 16px;
    }

    .password-group {
        position: relative;
    }

    .password-group .form-control {
        padding-right: 40px; /* Memberikan ruang untuk icon */
    }
    .btn-group {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
    }

    .toggle-edit-section {
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
        margin-top: 25px;
    }

    .btn-toggle {
        background-color: #f8f9fa;
        color: #BF0000;
        border: 1px solid #BF0000;
        padding: 12px 20px;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        min-width: 180px;
        justify-content: center;
    }

    .btn-toggle:hover {
        color: #BF0000;
        background-color: #fff0f0;
        transform: translateY(-2px);
        box-shadow: 0 3px 8px rgba(191, 0, 0, 0.15);
    }

    .btn-toggle i {
        font-size: 14px;
    }

    .edit-form-section {
        display: none;
        animation: fadeIn 0.3s ease;
    }

    .password-section-header {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
        position: relative;
    }

    .password-section-header:before {
        content: '';
        position: absolute;
        top: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 3px;
        background-color: #BF0000;
        border-radius: 2px;
    }

    .password-fields {
        margin-top: 15px;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @media screen and (max-width: 480px) {
        .btn-toggle {
            padding: 10px 15px;
            font-size: 14px;
            min-width: 160px;
        }
    }
    .submit-button-container {
        display: flex;
        justify-content: center;
        margin-top: 30px;
        margin-bottom: 20px;
    }

    button, .btn {
        padding: 12px 20px;
        color: white;
        background-color: #BF0000;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    button:hover, .btn:hover {
        color: white;
        background-color: #a00000;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    button:active, .btn:active {
        transform: translateY(0);
    }

    @media screen and (max-width: 480px) {
        button, .btn {
            padding: 10px 15px;
            font-size: 0.9rem;
        }
    }
    .welcome-text {
        font-size: 1.2rem;
        color: #555;
        margin-bottom: 25px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #BF0000;
    }

    @media screen and (max-width: 480px) {
        .welcome-text {
            font-size: 1.1rem;
            padding: 12px;
            margin-bottom: 20px;
        }
    }
    .error-message, .success-message, .info-message {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 8px;
        text-align: center;
        font-weight: 500;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    .error-message {
        background: #fff2f2;
        color: #c40000;
        border: 1px solid #ffcfcf;
    }

    .success-message {
        background: #f0fff4;
        color: #0d6832;
        border: 1px solid #c3e6cb;
    }

    .info-message {
        background: #e8f4fd;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }

    @media screen and (max-width: 480px) {
        .error-message, .success-message, .info-message {
            padding: 12px;
            font-size: 14px;
            margin-bottom: 15px;
        }
    }
    .open-email-btn {
        color: white!important;
        margin: 10px auto; display: inline-flex; align-items: center; padding: 6px 12px; background-color: #BF0000; border-radius: 4px; text-decoration: none; color: white; font-size: 14px; font-weight: bold;
    }
    .open-email-btn:hover {
        background-color: #a00000;
        transform: translateY(-2px);
        box-shadow: 0 3px 8px rgba(191, 0, 0, 0.15);
    }
</style>
<body>
<?php include '../config/navbarb.php'; ?>

<div class="container-form">
    <div class="form-container">
        <h2>Informasi Pengguna</h2>
        <p class="welcome-text">Selamat datang, <?= htmlspecialchars($user_name) ?>! Berikut adalah informasi akun Anda.</p>

        <div class="info-box">
            <p><strong>ID Akun:</strong> <?= htmlspecialchars($user_id)?></p>
            <p><strong>Username:</strong> <?= htmlspecialchars($user_name) ?></p>
            <p><strong>NIK:</strong> <?= htmlspecialchars($nik) ?></p>
            <p><strong>Departemen:</strong> <?= htmlspecialchars($dept) ?></p>
            <p><strong>Bagian:</strong> <?= htmlspecialchars($bagian) ?></p>
            <p><strong>Jabatan:</strong> <?= htmlspecialchars($jabatan) ?></p>
            <p><strong>Role ID:</strong> <?= htmlspecialchars($role_id) ?></p>
            <p><strong>Email:</strong> <?= htmlspecialchars($email) ?></p>
        </div>

        <div class="toggle-edit-section">
            <button type="button" id="toggleEditForm" class="btn-toggle">
                <i class="fas fa-user-edit"></i> Ubah Informasi Akun
            </button>
        </div>

        <form method="POST" class="edit-form">
            <!-- Hidden inputs untuk menandai form yang aktif -->
            <input type="hidden" name="edit_profile" id="edit_profile" value="0">
            <input type="hidden" name="edit_password" id="edit_password" value="0">

            <!-- Input tersembunyi untuk nilai default -->
            <input type="hidden" name="default_username" value="<?= htmlspecialchars($user_name) ?>">
            <input type="hidden" name="default_email" value="<?= htmlspecialchars($email) ?>">

            <div id="editFormSection" class="edit-form-section">
                <h3>Informasi Profil</h3>
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" name="username" id="username" value="<?= htmlspecialchars($user_name) ?>" placeholder="Masukkan username baru">
                </div>

                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" name="email" id="email" value="<?= htmlspecialchars($email) ?>" placeholder="Masukkan email baru">
                    <?php if (isset($_SESSION['email_verification'])): ?>
                    <div class="verification-info">
                        <p><i class="fas fa-info-circle"></i> <strong>Verifikasi Email Diperlukan</strong></p>
                        <p>Kode verifikasi telah dikirim ke email baru Anda: <strong><?php echo htmlspecialchars($_SESSION['email_verification']['email']); ?></strong></p>
                        <p>Silakan cek email Anda dan klik link verifikasi yang telah kami kirimkan untuk menyelesaikan proses perubahan email.</p>
                        <p><small>Kode akan kadaluarsa dalam <?php echo ceil(($_SESSION['email_verification']['deadline'] - time()) / 60); ?> menit</small></p>

                        <div style="margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                            <p><strong>Buka Email Anda:</strong></p>
                            <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 8px;">
                                <!-- <a href="https://mail.google.com" target="_blank" style="display: inline-flex; align-items: center; padding: 6px 12px; background-color: #f1f3f4; border-radius: 4px; text-decoration: none; color: #202124; font-size: 14px;">
                                    <i class="fab fa-google" style="margin-right: 8px; color: #4285F4;"></i> Gmail
                                </a>
                                <a href="https://outlook.live.com/mail" target="_blank" style="display: inline-flex; align-items: center; padding: 6px 12px; background-color: #f1f3f4; border-radius: 4px; text-decoration: none; color: #202124; font-size: 14px;">
                                    <i class="fab fa-microsoft" style="margin-right: 8px; color: #0078D4;"></i> Outlook
                                </a>
                                <a href="https://mail.yahoo.com" target="_blank" style="display: inline-flex; align-items: center; padding: 6px 12px; background-color: #f1f3f4; border-radius: 4px; text-decoration: none; color: #202124; font-size: 14px;">
                                    <i class="fab fa-yahoo" style="margin-right: 8px; color: #6001D2;"></i> Yahoo Mail
                                </a> -->
                                <?php
                                // Deteksi domain email dan tambahkan link khusus jika tersedia
                                $email = $_SESSION['email_verification']['email'];
                                $domain = substr(strrchr($email, "@"), 1);
                                $custom_link = '';
                                $domain_name = '';

                                switch(strtolower($domain)) {
                                    case 'gmail.com':
                                        $custom_link = 'https://mail.google.com';
                                        $domain_name = 'Gmail';
                                        break;
                                    case 'outlook.com':
                                    case 'hotmail.com':
                                    case 'live.com':
                                        $custom_link = 'https://outlook.live.com/mail';
                                        $domain_name = 'Outlook';
                                        break;
                                    case 'yahoo.com':
                                    case 'yahoo.co.id':
                                        $custom_link = 'https://mail.yahoo.com';
                                        $domain_name = 'Yahoo Mail';
                                        break;
                                    case 'zoho.com':
                                        $custom_link = 'https://mail.zoho.com';
                                        $domain_name = 'Zoho Mail';
                                        break;
                                    case 'aol.com':
                                        $custom_link = 'https://mail.aol.com';
                                        $domain_name = 'AOL Mail';
                                        break;
                                    case 'protonmail.com':
                                    case 'proton.me':
                                        $custom_link = 'https://mail.proton.me';
                                        $domain_name = 'ProtonMail';
                                        break;
                                    case 'icloud.com':
                                    case 'me.com':
                                        $custom_link = 'https://www.icloud.com/mail';
                                        $domain_name = 'iCloud Mail';
                                        break;
                                    case 'yandex.com':
                                    case 'yandex.ru':
                                        $custom_link = 'https://mail.yandex.com';
                                        $domain_name = 'Yandex Mail';
                                        break;
                                    // Tambahkan domain email lain jika diperlukan
                                    default:
                                        $custom_link = '';
                                        $domain_name = $domain;
                                }

                                if (!empty($custom_link)) {
                                    echo '<a href="' . $custom_link . '" target="_blank" class="open-email-btn">';
                                    echo '<i class="fas fa-envelope" style="margin-right: 8px;"></i> Buka ' . htmlspecialchars($domain_name);
                                    echo '</a>';
                                }
                                ?>
                            </div>
                        </div>

                        <div style="margin-top: 15px;">
                            <a href="#" class="open-email-btn" onclick="resendVerificationCode(); return false;"><i class="fas fa-paper-plane" style="margin-right: 8px;"></i> Kirim ulang email verifikasi</a>
                        </div>
                        <div id="verification-status" style="margin-top: 10px; display: none;">
                            <div class="loading-spinner" style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #BF0000; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                            <span id="verification-message" style="margin-left: 10px; font-style: italic; color: #666;">Mengirim email verifikasi...</span>
                        </div>
                    </div>
                    <style>
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    </style>
                    <?php endif; ?>
                </div>

                <h3 class="password-section-header">Keamanan</h3>
                <div class="password-fields">
                    <div class="form-group">
                        <label for="current_password">Password Saat Ini</label>
                        <div class="password-group">
                            <input type="password" id="current_password" name="current_password"
                                class="form-control" placeholder="Masukkan password saat ini" onkeyup="checkCurrentPassword()">
                            <span class="toggle-password" onclick="togglePassword('current_password')">
                                <i class="far fa-eye"></i>
                            </span>
                        </div>
                        <div id="password-feedback"></div>
                        <span class="info-text">Masukkan password saat ini untuk mengubah password</span>
                    </div>

                    <div id="new-password-section" style="display: none;">
                        <div class="form-group">
                            <label for="new_password">Password Baru</label>
                            <div class="password-group">
                                <input type="password" id="new_password" name="new_password"
                                    class="form-control" placeholder="Masukkan password baru">
                                <span class="toggle-password" onclick="togglePassword('new_password')">
                                    <i class="far fa-eye"></i>
                                </span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="confirm_password">Konfirmasi Password Baru</label>
                            <div class="password-group">
                                <input type="password" id="confirm_password" name="confirm_password"
                                    class="form-control" placeholder="Konfirmasi password baru">
                                <span class="toggle-password" onclick="togglePassword('confirm_password')">
                                    <i class="far fa-eye"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>


            <div class="submit-button-container">
                <button type="submit"><i class="fas fa-save"></i> Simpan Perubahan</button>
            </div>
            </div>
        </form>
        <div class="btn-group">
            <a href="<?php echo getDashboardUrl(); ?>" class="btn" style="display: inline-block; text-decoration: none;">
                <i class="fas fa-home"></i> Home
            </a>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>
<script>
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = input.nextElementSibling.querySelector('i');

    if (input.type === "password") {
        input.type = "text";
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = "password";
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function resendVerificationCode() {
    // Tampilkan indikator loading
    const resendLink = document.querySelector('.resend-code');
    const verificationStatus = document.getElementById('verification-status');
    const verificationMessage = document.getElementById('verification-message');

    if (resendLink) {
        resendLink.style.display = 'none';
    }

    if (verificationStatus) {
        verificationStatus.style.display = 'block';
        verificationMessage.textContent = 'Mengirim kode verifikasi...';
    }

    // Kirim request AJAX ke resend_verification.php
    fetch('../config/resend_verification.php', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Tampilkan pesan sukses
            if (verificationStatus) {
                verificationMessage.textContent = 'Kode verifikasi baru telah dikirim!';
                setTimeout(() => {
                    verificationStatus.style.display = 'none';
                    if (resendLink) {
                        resendLink.style.display = 'inline-block';
                    }
                }, 2000);
            }

            // Dapatkan email dari info verifikasi
            const verificationEmail = document.querySelector('.verification-info p:first-child + p').textContent.split(': ')[1].replace('</strong>', '').replace('<strong>', '');

            // Deteksi domain email
            const domain = verificationEmail.split('@')[1];
            let emailLink = '';
            let domainName = '';

            // Tentukan link email berdasarkan domain
            switch(domain.toLowerCase()) {
                case 'gmail.com':
                    emailLink = 'https://mail.google.com';
                    domainName = 'Gmail';
                    break;
                case 'outlook.com':
                case 'hotmail.com':
                case 'live.com':
                    emailLink = 'https://outlook.live.com/mail';
                    domainName = 'Outlook';
                    break;
                case 'yahoo.com':
                case 'yahoo.co.id':
                    emailLink = 'https://mail.yahoo.com';
                    domainName = 'Yahoo Mail';
                    break;
                case 'zoho.com':
                    emailLink = 'https://mail.zoho.com';
                    domainName = 'Zoho Mail';
                    break;
                case 'aol.com':
                    emailLink = 'https://mail.aol.com';
                    domainName = 'AOL Mail';
                    break;
                case 'protonmail.com':
                case 'proton.me':
                    emailLink = 'https://mail.proton.me';
                    domainName = 'ProtonMail';
                    break;
                case 'icloud.com':
                case 'me.com':
                    emailLink = 'https://www.icloud.com/mail';
                    domainName = 'iCloud Mail';
                    break;
                case 'yandex.com':
                case 'yandex.ru':
                    emailLink = 'https://mail.yandex.com';
                    domainName = 'Yandex Mail';
                    break;
                default:
                    emailLink = '';
                    domainName = domain;
            }

            // Buat tombol email jika domain dikenali
            let emailButton = '';
            if (emailLink) {
                emailButton = `<a href="${emailLink}" target="_blank" class="open-email-btn">
                    <i class="fas fa-envelope" style="margin-right: 8px;"></i> Buka ${domainName}
                </a>`;
            }

            const message_html = `
                <div style="text-align: left; padding: 10px;">
                    <p><i class="fas fa-check-circle" style="color: #0d6832;"></i> <strong>Email Verifikasi Telah Dikirim!</strong></p>
                    <p>Email verifikasi baru telah dikirim ke: <strong>${verificationEmail}</strong></p>
                    <div style="margin-top: 15px; padding: 10px; background-color: #f0fff4; border-left: 3px solid #0d6832;">
                        <p><strong>Langkah selanjutnya:</strong></p>
                        <ol style="padding-left: 20px; margin-top: 5px;">
                            <li>Cek inbox email Anda</li>
                            <li>Jika tidak ada, periksa folder spam/junk</li>
                            <li>Klik link verifikasi di email</li>
                        </ol>
                    </div>
                    <div style="text-align: center; margin-top: 15px;">
                        ${emailButton}
                        <div style="margin-top: 10px; display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;">
                            <a href="https://mail.google.com" target="_blank" style="display: inline-flex; align-items: center; padding: 6px 12px; background-color: #f1f3f4; border-radius: 4px; text-decoration: none; color: #202124; font-size: 14px;">
                                <i class="fab fa-google" style="margin-right: 8px; color: #4285F4;"></i> Gmail
                            </a>
                            <a href="https://outlook.live.com/mail" target="_blank" style="display: inline-flex; align-items: center; padding: 6px 12px; background-color: #f1f3f4; border-radius: 4px; text-decoration: none; color: #202124; font-size: 14px;">
                                <i class="fab fa-microsoft" style="margin-right: 8px; color: #0078D4;"></i> Outlook
                            </a>
                            <a href="https://mail.yahoo.com" target="_blank" style="display: inline-flex; align-items: center; padding: 6px 12px; background-color: #f1f3f4; border-radius: 4px; text-decoration: none; color: #202124; font-size: 14px;">
                                <i class="fab fa-yahoo" style="margin-right: 8px; color: #6001D2;"></i> Yahoo
                            </a>
                        </div>
                    </div>
                </div>
            `;

            const modal = new CustomModal();
            modal.alert({
                title: 'Email Verifikasi Terkirim',
                message: message_html,
                type: 'success'
            });
        } else {
            // Tampilkan pesan error
            if (verificationStatus) {
                verificationStatus.style.display = 'none';
            }

            if (resendLink) {
                resendLink.style.display = 'inline-block';
            }

            const message_html = `
                <div style="text-align: left; padding: 10px;">
                    <p><i class="fas fa-exclamation-circle" style="color: #c40000;"></i> <strong>Gagal Mengirim Email Verifikasi</strong></p>
                    <p>${data.message || 'Terjadi kesalahan saat mengirim email verifikasi.'}</p>
                    <div style="margin-top: 15px; padding: 10px; background-color: #fff2f2; border-left: 3px solid #c40000;">
                        <p><strong>Kemungkinan penyebab:</strong></p>
                        <ul style="padding-left: 20px; margin-top: 5px;">
                            <li>Alamat email tidak valid</li>
                            <li>Masalah pada server email</li>
                            <li>Koneksi internet terputus</li>
                        </ul>
                        <p style="margin-top: 10px;">Silakan coba lagi atau hubungi administrator.</p>
                    </div>
                </div>
            `;

            const modal = new CustomModal();
            modal.alert({
                title: 'Gagal Mengirim Email',
                message: message_html,
                type: 'alert'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);

        if (verificationStatus) {
            verificationStatus.style.display = 'none';
        }

        if (resendLink) {
            resendLink.style.display = 'inline-block';
        }

        const message_html = `
            <div style="text-align: left; padding: 10px;">
                <p><i class="fas fa-exclamation-triangle" style="color: #856404;"></i> <strong>Terjadi Kesalahan</strong></p>
                <p>Sistem tidak dapat mengirim email verifikasi saat ini.</p>
                <div style="margin-top: 15px; padding: 10px; background-color: #fff3cd; border-left: 3px solid #856404;">
                    <p><strong>Saran:</strong></p>
                    <ul style="padding-left: 20px; margin-top: 5px;">
                        <li>Periksa koneksi internet Anda</li>
                        <li>Pastikan alamat email yang dimasukkan benar</li>
                        <li>Coba lagi setelah beberapa saat</li>
                        <li>Jika masalah berlanjut, hubungi administrator</li>
                    </ul>
                </div>
            </div>
        `;

        const modal = new CustomModal();
        modal.alert({
            title: 'Gagal Mengirim Email',
            message: message_html,
            type: 'warning'
        });
    });
}

function checkCurrentPassword() {
    const currentPassword = document.getElementById('current_password');
    const newPasswordSection = document.getElementById('new-password-section');
    const passwordValue = currentPassword.value.trim();

    // Jika password saat ini kosong, sembunyikan bagian password baru
    if (passwordValue === '') {
        newPasswordSection.style.display = 'none';
        document.getElementById('new_password').value = '';
        document.getElementById('confirm_password').value = '';
        return;
    }

    // Jika password saat ini diisi, verifikasi melalui AJAX
    if (passwordValue.length > 0) {
        // Tampilkan indikator loading
        currentPassword.style.borderColor = '#ccc';

        // Buat objek FormData untuk mengirim data
        const formData = new FormData();
        formData.append('action', 'verify_password');
        formData.append('current_password', passwordValue);

        // Kirim request AJAX
        fetch('verify_password.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Password benar, tampilkan bagian password baru
                newPasswordSection.style.display = 'block';
                currentPassword.style.borderColor = '#4CAF50';

                // Tambahkan indikator visual bahwa password benar
                const passwordFeedback = document.getElementById('password-feedback');
                if (passwordFeedback) {
                    passwordFeedback.innerHTML = '<small class="text-success"><i class="fas fa-check-circle"></i> Password benar</small>';
                }
            } else {
                // Password salah, sembunyikan bagian password baru
                newPasswordSection.style.display = 'none';
                document.getElementById('new_password').value = '';
                document.getElementById('confirm_password').value = '';
                currentPassword.style.borderColor = '#BF0000';

                // Tambahkan indikator visual bahwa password salah
                const passwordFeedback = document.getElementById('password-feedback');
                if (passwordFeedback) {
                    passwordFeedback.innerHTML = '<small class="text-danger"><i class="fas fa-times-circle"></i> Password salah</small>';
                }
            }
        })
        .catch(error => {
            console.error('Error verifying password:', error);
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Toggle untuk form edit
    const toggleEditBtn = document.getElementById('toggleEditForm');
    const editFormSection = document.getElementById('editFormSection');
    let isEditFormVisible = false;

    // Inisialisasi pengecekan password saat halaman dimuat
    checkCurrentPassword();

    // Fungsi untuk menampilkan/menyembunyikan form edit
    toggleEditBtn.addEventListener('click', function() {
        if (isEditFormVisible) {
            editFormSection.style.display = 'none';
            toggleEditBtn.innerHTML = '<i class="fas fa-user-edit"></i> Ubah Informasi Akun';
            document.getElementById('edit_profile').value = '0';
            document.getElementById('edit_password').value = '0';

            // Reset nilai input
            document.getElementById('username').value = '<?= htmlspecialchars($user_name) ?>';
            document.getElementById('email').value = '<?= htmlspecialchars($email) ?>';
            document.getElementById('current_password').value = '';
            document.getElementById('new_password').value = '';
            document.getElementById('confirm_password').value = '';
        } else {
            editFormSection.style.display = 'block';
            toggleEditBtn.innerHTML = '<i class="fas fa-times"></i> Batal Ubah Informasi';
            document.getElementById('edit_profile').value = '1';
            document.getElementById('edit_password').value = '1';
        }

        isEditFormVisible = !isEditFormVisible;
    });

    // Tambahkan event listener untuk form submit
    document.querySelector('.edit-form').addEventListener('submit', function(e) {
        // Cek apakah ada perubahan pada username atau email
        const usernameInput = document.getElementById('username');
        const emailInput = document.getElementById('email');
        const defaultUsername = document.querySelector('input[name="default_username"]').value;
        const defaultEmail = document.querySelector('input[name="default_email"]').value;

        // Jika ada perubahan tapi form tidak terlihat, tampilkan form dan set flag
        if ((usernameInput.value !== defaultUsername || emailInput.value !== defaultEmail)) {
            document.getElementById('edit_profile').value = '1';
            console.log('Profile changes detected, setting edit_profile to 1');

            // Jika email berubah, tampilkan konfirmasi
            if (emailInput.value !== defaultEmail) {
                // Validasi format email
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(emailInput.value)) {
                    const modal = new CustomModal();
                    modal.alert({
                        title: 'Format Email Tidak Valid',
                        message: 'Silakan masukkan alamat email yang benar.',
                        type: 'alert'
                    });
                    e.preventDefault();
                    return false;
                }

                // Hentikan submit form untuk menampilkan konfirmasi
                e.preventDefault();

                // Buat pesan konfirmasi dengan format HTML
                const confirmMessage = `
                    <div style="text-align: left; padding: 10px;">
                        <p>Anda akan mengubah alamat email Anda:</p>
                        <p><strong>Dari:</strong> ${defaultEmail}</p>
                        <p><strong>Menjadi:</strong> ${emailInput.value}</p>
                        <div style="margin-top: 15px; padding: 10px; background-color: #f8f9fa; border-left: 3px solid #0c5460;">
                            <p><i class="fas fa-info-circle"></i> <strong>Penting:</strong></p>
                            <p>Anda perlu memverifikasi email baru Anda sebelum perubahan diterapkan.</p>
                            <p>Email verifikasi akan dikirim ke alamat email baru Anda.</p>
                        </div>
                    </div>
                `;

                // Gunakan confirmAction untuk menampilkan dialog konfirmasi
                confirmAction(
                    confirmMessage,
                    function() {
                        // Jika user mengklik "Ya", submit form
                        document.querySelector('.edit-form').submit();
                    },
                    function() {
                        // Jika user mengklik "Tidak", tidak lakukan apa-apa
                        console.log('Perubahan email dibatalkan');
                    },
                    'Konfirmasi Perubahan Email'
                );

                return false;
            }
        }

        // Cek apakah ada perubahan pada password
        const currentPassword = document.getElementById('current_password').value;
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;

        // Hanya aktifkan edit_password jika password saat ini diisi
        if (currentPassword.trim() !== '') {
            document.getElementById('edit_password').value = '1';
            console.log('Current password provided, setting edit_password to 1');

            // Jika password baru tidak diisi, tampilkan peringatan
            if (newPassword.trim() === '') {
                alert('Anda telah memasukkan password saat ini tetapi belum memasukkan password baru. Silakan isi password baru atau kosongkan password saat ini.');
                e.preventDefault(); // Mencegah form submit
                return false;
            }

            // Jika password baru dan konfirmasi tidak cocok
            if (newPassword !== confirmPassword) {
                alert('Password baru dan konfirmasi password tidak cocok.');
                e.preventDefault(); // Mencegah form submit
                return false;
            }
        } else {
            // Jika password saat ini kosong, pastikan flag edit_password dinonaktifkan
            document.getElementById('edit_password').value = '0';
            console.log('Current password not provided, setting edit_password to 0');
        }

        // Debug output
        console.log('edit_profile: ' + document.getElementById('edit_profile').value);
        console.log('Form submitted');
        console.log('Username: ' + usernameInput.value + ' vs ' + defaultUsername);
        console.log('Email: ' + emailInput.value + ' vs ' + defaultEmail);
        console.log('edit_password: ' + document.getElementById('edit_password').value);
    });
});
</script>
</body>
</html>
