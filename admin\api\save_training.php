<?php
/**
 * API untuk menyimpan (create/update) training
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include necessary files
require_once '../../config/config.php';
require_once '../security.php';

// Check if user is admin (same validation as security.php)
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role_id'], [2, 3, 4, 5, 99])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized - Admin access required']);
    exit;
}

try {
    // Get form data
    $training_id = $_POST['training_id'] ?? '';
    $training_type = $_POST['training_type'] ?? '';
    $training_title = $_POST['training_title'] ?? '';
    $training_date = $_POST['training_date'] ?? '';
    $training_time_start = $_POST['training_time_start'] ?? '';
    $training_time_end = $_POST['training_time_end'] ?? '';
    $training_location = $_POST['training_location'] ?? '';
    $training_trainer = $_POST['training_trainer'] ?? '';
    $max_participants = $_POST['max_participants'] ?? null;
    $training_status = $_POST['training_status'] ?? 'Active';
    $training_description = $_POST['training_description'] ?? '';

    // Validate required fields
    if (empty($training_title) || empty($training_date) || empty($training_type)) {
        throw new Exception('Judul training, tanggal, dan tipe training harus diisi');
    }

    // Validate date format
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $training_date)) {
        throw new Exception('Format tanggal tidak valid');
    }

    // Validate time format if provided
    if (!empty($training_time_start) && !preg_match('/^\d{2}:\d{2}$/', $training_time_start)) {
        throw new Exception('Format waktu mulai tidak valid');
    }

    if (!empty($training_time_end) && !preg_match('/^\d{2}:\d{2}$/', $training_time_end)) {
        throw new Exception('Format waktu selesai tidak valid');
    }

    $user_id = $_SESSION['user_id'];

    if ($training_type === 'offline') {
        // Handle offline training
        if (empty($training_id)) {
            // Create new offline training with new structure
            $query = "INSERT INTO offline_training
                      (training_topic, training_description, start_date, end_date, is_confirmed,
                       training_time_start, training_time_end, location, trainer_name,
                       max_participants, status, created_by)
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $stmt = $conn->prepare($query);

            // Handle end date - if not provided, set to NULL for single day event
            $end_date = !empty($_POST['training_end_date']) ? $_POST['training_end_date'] : null;
            $is_confirmed = 1; // Admin created events are confirmed by default
            $time_start = $training_time_start ?: null;
            $time_end = $training_time_end ?: null;

            $stmt->bind_param("sssssisssssi",
                $training_title,
                $training_description,
                $training_date,
                $end_date,
                $is_confirmed,
                $time_start,
                $time_end,
                $training_location,
                $training_trainer,
                $max_participants,
                $training_status,
                $user_id
            );

            if ($stmt->execute()) {
                $new_id = $conn->insert_id;
                echo json_encode([
                    'success' => true,
                    'message' => 'Training Internal berhasil dibuat',
                    'training_id' => $new_id
                ]);
            } else {
                throw new Exception('Gagal membuat Training Internal: ' . $stmt->error);
            }
            $stmt->close();

        } else {
            // Update existing offline training with new structure
            $query = "UPDATE offline_training
                      SET training_topic = ?, training_description = ?, start_date = ?,
                          end_date = ?, is_confirmed = ?, training_time_start = ?,
                          training_time_end = ?, location = ?, trainer_name = ?,
                          max_participants = ?, status = ?, updated_at = NOW()
                      WHERE id = ?";

            $stmt = $conn->prepare($query);

            // Handle end date - if not provided, set to NULL for single day event
            $end_date = !empty($_POST['training_end_date']) ? $_POST['training_end_date'] : null;
            $is_confirmed = 1; // Admin updated events are confirmed by default
            $time_start = $training_time_start ?: null;
            $time_end = $training_time_end ?: null;

            $stmt->bind_param("sssssisssssi",
                $training_title,
                $training_description,
                $training_date,
                $end_date,
                $is_confirmed,
                $time_start,
                $time_end,
                $training_location,
                $training_trainer,
                $max_participants,
                $training_status,
                $training_id
            );

            if ($stmt->execute()) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Training Internal berhasil diupdate',
                    'training_id' => $training_id
                ]);
            } else {
                throw new Exception('Gagal mengupdate Training Internal: ' . $stmt->error);
            }
            $stmt->close();
        }

    } elseif ($training_type === 'online') {
        // Handle online training (training submissions) with new structure
        if (empty($training_id)) {
            // Create new training submission with Approved status and new structure
            $query = "INSERT INTO training_submissions
                      (user_id, training_topic, additional_info, start_date, end_date, is_confirmed,
                       training_time_start, training_time_end, training_place, contact_person,
                       status, created_at)
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Approved', NOW())";

            $stmt = $conn->prepare($query);

            // Handle end date - if not provided, set to NULL for single day event
            $end_date = !empty($_POST['training_end_date']) ? $_POST['training_end_date'] : null;
            $is_confirmed = 1; // Admin created events are confirmed by default
            $time_start = $training_time_start ?: null;
            $time_end = $training_time_end ?: null;
            $location = $training_location ?: 'Online';

            $stmt->bind_param("issssissss",
                $user_id,
                $training_title,
                $training_description,
                $training_date,
                $end_date,
                $is_confirmed,
                $time_start,
                $time_end,
                $location,
                $training_trainer
            );

            if ($stmt->execute()) {
                $new_id = $conn->insert_id;
                echo json_encode([
                    'success' => true,
                    'message' => 'Training Eksternal berhasil dibuat di training_submissions',
                    'training_id' => $new_id
                ]);
            } else {
                throw new Exception('Gagal membuat Training Eksternal: ' . $stmt->error);
            }
            $stmt->close();

        } else {
            // Update existing training submission with new structure
            $query = "UPDATE training_submissions
                      SET training_topic = ?, additional_info = ?, start_date = ?,
                          end_date = ?, is_confirmed = ?, training_time_start = ?,
                          training_time_end = ?, training_place = ?, contact_person = ?,
                          status = 'Approved', updated_at = NOW()
                      WHERE id = ?";

            $stmt = $conn->prepare($query);

            // Handle end date - if not provided, set to NULL for single day event
            $end_date = !empty($_POST['training_end_date']) ? $_POST['training_end_date'] : null;
            $is_confirmed = 1; // Admin updated events are confirmed by default
            $time_start = $training_time_start ?: null;
            $time_end = $training_time_end ?: null;
            $location = $training_location ?: 'Online';

            $stmt->bind_param("sssssisssi",
                $training_title,
                $training_description,
                $training_date,
                $end_date,
                $is_confirmed,
                $time_start,
                $time_end,
                $location,
                $training_trainer,
                $training_id
            );

            if ($stmt->execute()) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Training Eksternal berhasil diupdate di training_submissions',
                    'training_id' => $training_id
                ]);
            } else {
                throw new Exception('Gagal mengupdate Training Eksternal: ' . $stmt->error);
            }
            $stmt->close();
        }

    } else {
        throw new Exception('Tipe training tidak valid');
    }

} catch (Exception $e) {
    error_log("Error in save_training.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
