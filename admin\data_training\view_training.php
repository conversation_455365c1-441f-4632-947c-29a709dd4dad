<?php
session_start();
include '../../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../../view/login.php');
    exit();
}

// Inisialisasi variabel pencarian dan filter
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$month_filter = isset($_GET['month']) ? $_GET['month'] : '';
$year_filter = isset($_GET['year']) ? $_GET['year'] : '';
$type_filter = isset($_GET['training_type']) ? $_GET['training_type'] : '';

// Buat query dasar
$query = "SELECT dt.*, k.nama as nama_karyawan
          FROM data_training dt
          LEFT JOIN karyawan k ON dt.nik = k.nik
          WHERE 1=1";

// Tambahkan kondisi pencarian
if ($search) {
    $query .= " AND (dt.nik LIKE '%$search%'
                OR dt.nama LIKE '%$search%'
                OR dt.nama_training LIKE '%$search%')";
}

if ($month_filter) {
    $query .= " AND dt.bulan = '$month_filter'";
}

if ($year_filter) {
    $query .= " AND dt.tahun = '$year_filter'";
}

if ($type_filter) {
    $query .= " AND dt.training_type = '$type_filter'";
}

$query .= " ORDER BY dt.created_at DESC";
$result = $conn->query($query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Training Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <style>
        .navbar {
            background-color: #f8f9fa;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
        }
        .content-wrapper {
            padding: 20px;
        }
        .filter-card {
            margin-bottom: 20px;
        }
        .action-buttons .btn {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <span class="navbar-brand">Training Data Management</span>
            <div>
                <a href="database_training.php" class="btn btn-primary">Add New Training</a>
                <a href="../dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
            </div>
        </div>
    </nav>

    <div class="container content-wrapper">
        <!-- Filter Card -->
        <div class="card filter-card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="search"
                               placeholder="Search by NIK, Name, or Training..."
                               value="<?= htmlspecialchars($search) ?>">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="month">
                            <option value="">Select Month</option>
                            <?php
                            $bulan = array("Januari", "Februari", "Maret", "April", "Mei", "Juni",
                                         "Juli", "Agustus", "September", "Oktober", "November", "Desember");
                            foreach($bulan as $b) {
                                $selected = ($month_filter == $b) ? 'selected' : '';
                                echo "<option value='$b' $selected>$b</option>";
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="year">
                            <option value="">Select Year</option>
                            <?php
                            $current_year = date('Y');
                            for($year = $current_year - 5; $year <= $current_year + 1; $year++) {
                                $selected = ($year_filter == $year) ? 'selected' : '';
                                echo "<option value='$year' $selected>$year</option>";
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="training_type">
                            <option value="">Training Type</option>
                            <option value="Soft" <?= ($type_filter == 'Soft') ? 'selected' : '' ?>>Soft</option>
                            <option value="Tech" <?= ($type_filter == 'Tech') ? 'selected' : '' ?>>Tech</option>
                            <option value="Compliance" <?= ($type_filter == 'Compliance') ? 'selected' : '' ?>>Compliance</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary">Filter</button>
                        <a href="view_training.php" class="btn btn-secondary">Reset</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Data Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="trainingTable">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>NIK</th>
                                <th>Name</th>
                                <th>Training</th>
                                <th>Type</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if ($result && $result->num_rows > 0) {
                                $no = 1;
                                while ($row = $result->fetch_assoc()) {
                                    ?>
                                    <tr>
                                        <td><?= $no++ ?></td>
                                        <td><?= htmlspecialchars($row['nik']) ?></td>
                                        <td><?= htmlspecialchars($row['nama']) ?></td>
                                        <td><?= htmlspecialchars($row['nama_training']) ?></td>
                                        <td><?= htmlspecialchars($row['training_type']) ?></td>
                                        <td><?= date('d/m/Y', strtotime($row['tanggal_mulai'])) ?></td>
                                        <td><?= htmlspecialchars($row['status_training']) ?></td>
                                        <td class="action-buttons">
                                            <a href='detail_training.php?id=<?= $row['id'] ?>'
                                               class='btn btn-info btn-sm'>Detail</a>
                                            <a href='edit_training.php?id=<?= $row['id'] ?>'
                                               class='btn btn-warning btn-sm'>Edit</a>
                                            <button onclick='deleteTraining(<?= $row['id'] ?>)'
                                                    class='btn btn-danger btn-sm'>Delete</button>
                                        </td>
                                    </tr>
                                    <?php
                                }
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // Fungsi untuk menampilkan toast notification
        function showToast(message, type = 'success', duration = 3000) {
            // Hapus toast yang sudah ada jika ada
            const existingToast = document.querySelector('.toast-notification');
            if (existingToast) {
                document.body.removeChild(existingToast);
            }

            // Buat toast baru
            const toast = document.createElement('div');
            toast.className = `toast-notification toast-${type}`;

            // Set icon berdasarkan type
            let icon = 'check';
            if (type === 'error') icon = 'exclamation-circle';
            if (type === 'warning') icon = 'exclamation-triangle';
            if (type === 'info') icon = 'info-circle';

            toast.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;
            document.body.appendChild(toast);

            // Animasi toast
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);

            // Hapus toast setelah durasi tertentu
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, duration);
        }

        // Fungsi untuk menampilkan dialog konfirmasi kustom
        function showConfirmDialog(message, onConfirm, onCancel) {
            // Hapus dialog yang sudah ada jika ada
            const existingDialog = document.querySelector('.confirm-dialog-container');
            if (existingDialog) {
                document.body.removeChild(existingDialog);
            }

            // Buat dialog konfirmasi
            const dialogContainer = document.createElement('div');
            dialogContainer.className = 'confirm-dialog-container';

            dialogContainer.innerHTML = `
                <div class="confirm-dialog">
                    <div class="confirm-dialog-header">
                        <i class="fas fa-question-circle"></i>
                        <h4>Konfirmasi</h4>
                    </div>
                    <div class="confirm-dialog-body">
                        <p>${message}</p>
                    </div>
                    <div class="confirm-dialog-footer">
                        <button class="btn btn-secondary btn-cancel">Batal</button>
                        <button class="btn btn-danger btn-confirm">Ya, Lanjutkan</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialogContainer);

            // Tambahkan event listener untuk tombol
            const cancelBtn = dialogContainer.querySelector('.btn-cancel');
            const confirmBtn = dialogContainer.querySelector('.btn-confirm');

            cancelBtn.addEventListener('click', () => {
                document.body.removeChild(dialogContainer);
                if (onCancel) onCancel();
            });

            confirmBtn.addEventListener('click', () => {
                document.body.removeChild(dialogContainer);
                if (onConfirm) onConfirm();
            });

            // Animasi dialog
            setTimeout(() => {
                dialogContainer.classList.add('show');
            }, 10);
        }

        // Tambahkan CSS untuk toast notification dan dialog konfirmasi
        document.head.insertAdjacentHTML('beforeend', `
        <style>
        .toast-notification {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(100px);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            opacity: 0;
            transition: transform 0.3s ease, opacity 0.3s ease;
        }

        .toast-notification.show {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }

        .toast-success i {
            color: #4CAF50;
        }

        .toast-error i {
            color: #F44336;
        }

        .toast-warning i {
            color: #FF9800;
        }

        .toast-info i {
            color: #2196F3;
        }

        .confirm-dialog-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .confirm-dialog-container.show {
            opacity: 1;
        }

        .confirm-dialog {
            background-color: white;
            border-radius: 8px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }

        .confirm-dialog-container.show .confirm-dialog {
            transform: translateY(0);
        }

        .confirm-dialog-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .confirm-dialog-header i {
            color: #BF0000;
            font-size: 1.2rem;
        }

        .confirm-dialog-header h4 {
            margin: 0;
            font-size: 1.1rem;
            color: #212529;
        }

        .confirm-dialog-body {
            padding: 20px;
        }

        .confirm-dialog-body p {
            margin: 0;
            color: #495057;
        }

        .confirm-dialog-footer {
            padding: 15px 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        @media screen and (max-width: 576px) {
            .confirm-dialog {
                width: 95%;
            }

            .confirm-dialog-header,
            .confirm-dialog-body,
            .confirm-dialog-footer {
                padding: 12px 15px;
            }
        }
        </style>
        `);

        $(document).ready(function() {
            $('#trainingTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "language": {
                    "lengthMenu": "Show _MENU_ entries",
                    "zeroRecords": "No data found",
                    "info": "Showing page _PAGE_ of _PAGES_",
                    "infoEmpty": "No records available",
                    "infoFiltered": "(filtered from _MAX_ total records)",
                    "search": "Search:",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                },
                "ordering": true,
                "searching": true
            });
        });

        function deleteTraining(id) {
            showConfirmDialog('Are you sure you want to delete this training data?', () => {
                window.location.href = 'delete_training.php?id=' + id;
            });
        }
    </script>
</body>
</html>

