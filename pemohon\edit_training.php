<?php
include '../config/config.php';
include 'security.php';

// Pastikan CSRF token ada
if (!isset($_SESSION['token'])) {
    $_SESSION['token'] = bin2hex(random_bytes(32));
}

// Validasi ID training
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = "ID training tidak valid.";
    header("Location: dashboard.php");
    exit();
}

$training_id = $_GET['id'];
$user_id = $_SESSION['user_id'];

// Ambil data training dan pastikan milik user yang login
$query = "SELECT ts.*,
                 GROUP_CONCAT(p.id SEPARATOR ',') AS participant_ids,
                 GROUP_CONCAT(p.nama_participants SEPARATOR ',') AS participant_names,
                 GROUP_CONCAT(p.nik_participants SEPARATOR ',') AS participant_niks,
                 GROUP_CONCAT(p.jabatan_participants SEPARATOR ',') AS participant_jabatans,
                 GROUP_CONCAT(p.bagian_participants SEPARATOR ',') AS participant_bagians,
                 GROUP_CONCAT(p.departemen_participants SEPARATOR ',') AS participant_departemens
          FROM training_submissions ts
          LEFT JOIN participants p ON ts.id = p.training_id
          WHERE ts.id = ? AND ts.user_id = ?
          GROUP BY ts.id";

$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $training_id, $user_id);
$stmt->execute();
$result = $stmt->get_result();
$training = $result->fetch_assoc();

if (!$training) {
    $_SESSION['error'] = "Training tidak ditemukan atau Anda tidak memiliki akses.";
    header("Location: dashboard.php");
    exit();
}

// Pastikan training berstatus Revise
if ($training['status'] != 'Revise') {
    $_SESSION['error'] = "Training ini tidak dapat diedit karena statusnya bukan 'Revise'.";
    header("Location: detail_training.php?id=" . $training_id);
    exit();
}

// Proses form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validasi CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['token']) {
        $_SESSION['error'] = "Token keamanan tidak valid.";
        header("Location: edit_training.php?id=" . $training_id);
        exit();
    }

    // Ambil data dari form - konsisten dengan form.php
    $training_topic = trim($_POST['training_topic']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $training_skill_type = $_POST['training_skill_type'] ?? '';
    $start_date = $_POST['start_date'] ?? '';
    $additional_info = trim($_POST['additional_info']);

    // Validasi data wajib - konsisten dengan form.php
    $errors = [];
    if (empty($training_topic)) $errors[] = "Nama training harus diisi.";
    if (empty($email)) $errors[] = "Email harus diisi.";
    if (empty($phone)) $errors[] = "Nomor telepon harus diisi.";
    if (empty($start_date)) $errors[] = "Tanggal training harus diisi.";
    if (empty($training_skill_type)) $errors[] = "Jenis kompetensi training harus dipilih.";
    if (empty($additional_info)) $errors[] = "Urgensi training harus diisi.";

    // Validasi tanggal minimal 1 bulan ke depan dari tanggal pengajuan awal (untuk revisi)
    if (!empty($start_date)) {
        $selected_date = new DateTime($start_date);

        // Untuk revisi, gunakan tanggal pengajuan awal sebagai referensi
        $reference_date = new DateTime($training['submission_date'] ?? $training['created_at'] ?? 'now');
        $min_date = clone $reference_date;
        $min_date->add(new DateInterval('P1M'));

        if ($selected_date < $min_date) {
            $reference_date_formatted = $reference_date->format('d/m/Y');
            $min_date_formatted = $min_date->format('d/m/Y');
            $errors[] = "Tanggal training harus minimal 1 bulan dari tanggal pengajuan awal ({$reference_date_formatted}). Tanggal minimal: {$min_date_formatted}";
        }
    }

    if (empty($errors)) {
        try {
            $conn->begin_transaction();

            // Update training submission - reset approval status
            // Field yang boleh diedit pemohon - konsisten dengan form.php
            $update_query = "UPDATE training_submissions SET
                training_topic = ?,
                email = ?,
                phone = ?,
                training_skill_type = ?,
                start_date = ?,
                additional_info = ?,
                status = 'Pending',
                current_approver_role_id = 2,
                next_approver_id = NULL,
                approved_dept_head = 'Pending',
                approved_hrd = 'Pending',
                approved_ga = 'Pending',
                approved_fm = 'Pending',
                approved_dir = 'Pending',
                comments_dept_head = NULL,
                comments_hrd = NULL,
                comments_ga = NULL,
                comments_fm = NULL,
                comments_dir = NULL,
                is_confirmed = 0
                WHERE id = ? AND user_id = ?";

            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("ssssssii",
                $training_topic, $email, $phone, $training_skill_type,
                $start_date, $additional_info,
                $training_id, $user_id);

            if (!$stmt->execute()) {
                throw new Exception("Gagal mengupdate training: " . $stmt->error);
            }

            // Update participants jika ada dengan pencegahan duplikasi
            if (isset($_POST['nama']) && is_array($_POST['nama'])) {
                // Hapus participants lama
                $delete_participants = "DELETE FROM participants WHERE training_id = ?";
                $stmt = $conn->prepare($delete_participants);
                $stmt->bind_param("i", $training_id);
                $stmt->execute();

                // Insert participants baru dengan pencegahan duplikasi
                $insert_participant = "INSERT INTO participants (training_id, nama_participants, nik_participants, jabatan_participants, bagian_participants, departemen_participants) VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($insert_participant);

                $processed_niks = []; // Array untuk tracking NIK yang sudah diproses

                for ($i = 0; $i < count($_POST['nama']); $i++) {
                    $nama = trim($_POST['nama'][$i] ?? '');
                    $nik = trim($_POST['nik'][$i] ?? '');
                    $jabatan = trim($_POST['jabatan'][$i] ?? '');
                    $bagian = trim($_POST['bagian'][$i] ?? '');
                    $departemen = trim($_POST['departemen'][$i] ?? '');

                    if (!empty($nama) && !empty($nik)) {
                        // Normalisasi NIK untuk pengecekan duplikasi
                        $normalized_nik = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $nik));

                        // Cek duplikasi NIK
                        if (!in_array($normalized_nik, $processed_niks)) {
                            $processed_niks[] = $normalized_nik;

                            $stmt->bind_param("isssss",
                                $training_id,
                                $nama,
                                $nik,
                                $jabatan,
                                $bagian,
                                $departemen
                            );
                            $stmt->execute();
                        }
                    }
                }
            }

            $conn->commit();
            $_SESSION['success'] = "Training berhasil direvisi dan dikirim ulang untuk persetujuan.";
            header("Location: dashboard.php");
            exit();

        } catch (Exception $e) {
            $conn->rollback();
            $_SESSION['error'] = "Terjadi kesalahan: " . $e->getMessage();
        }
    } else {
        $_SESSION['errors'] = $errors;
    }
}

// Parse participants data dengan pencegahan duplikasi
$participants = [];
$seen_niks = []; // Array untuk tracking NIK yang sudah ada

if (!empty($training['participant_names'])) {
    $names = explode(',', $training['participant_names']);
    $niks = explode(',', $training['participant_niks']);
    $positions = explode(',', $training['participant_jabatans']);
    $sub_depts = explode(',', $training['participant_bagians']);
    $depts = explode(',', $training['participant_departemens']);

    for ($i = 0; $i < count($names); $i++) {
        // Hanya tambahkan participant jika nama tidak kosong
        $name = trim($names[$i] ?? '');
        $nik = trim($niks[$i] ?? '');

        if (!empty($name) && !empty($nik)) {
            // Cek duplikasi NIK (case insensitive)
            $normalized_nik = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $nik));

            if (!in_array($normalized_nik, $seen_niks)) {
                $seen_niks[] = $normalized_nik;
                $participants[] = [
                    'name' => $name,
                    'nik' => $nik,
                    'position' => trim($positions[$i] ?? ''),
                    'sub_department' => trim($sub_depts[$i] ?? ''),
                    'department' => trim($depts[$i] ?? '')
                ];
            }
        }
    }
}


?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<link rel="stylesheet" href="../assets/css/form-style.css">
<style>
.edit-container {
    max-width: 1000px;
    margin: 30px auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
}

.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #BF0000, #800000);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.revise-notice {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.revise-notice h4 {
    color: #856404;
    margin-bottom: 15px;
}

.comment-box {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 10px;
    border-left: 4px solid #dc3545;
}

.form-section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.form-section h3 {
    color: #BF0000;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #BF0000;
    box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
}

.required {
    color: #dc3545;
}

.btn-group {
    text-align: center;
    margin-top: 30px;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #BF0000;
    color: white;
}

.btn-primary:hover {
    background-color: #a00000;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
    text-decoration: none;
    color: white;
}

.alert {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.info-note {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    color: #0c5460;
}

.info-note h5 {
    margin-bottom: 10px;
    color: #0c5460;
}

.info-note ul {
    margin: 10px 0 0 20px;
}

/* Search Employee Styles */
.search-section {
    margin-bottom: 20px;
    position: relative;
}

.search-section input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
}

.search-section input:focus {
    outline: none;
    border-color: #BF0000;
    box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
}

#employee_results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.employee-result {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
}

.employee-result:hover {
    background-color: #f8f9fa;
}

.employee-result:last-child {
    border-bottom: none;
}

.employee-info strong {
    color: #333;
}

.employee-info small {
    color: #666;
}

.no-results, .error {
    padding: 12px;
    text-align: center;
    color: #666;
    font-style: italic;
}

.error {
    color: #dc3545;
}

/* Participant Controls */
.participant-controls {
    margin-bottom: 20px;
}

.btn-add-participant {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn-add-participant:hover {
    background-color: #218838;
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    margin-bottom: 20px;
}

#participantTable {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

#participantTable th,
#participantTable td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

#participantTable th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

#participantTable tr:hover {
    background-color: #f8f9fa;
}

.manual-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.manual-input:focus {
    outline: none;
    border-color: #BF0000;
}

/* Mobile Participant Cards */
.mobile-participant-card {
    display: none;
}

.mobile-participant-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
}

.mobile-participant-header {
    background-color: #f8f9fa;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
}

.mobile-participant-body {
    padding: 12px;
}

.mobile-participant-row {
    display: flex;
    margin-bottom: 8px;
}

.mobile-participant-label {
    font-weight: 600;
    width: 100px;
    color: #333;
}

.mobile-participant-value {
    flex: 1;
    color: #666;
}

.manual-input-mobile {
    width: 100%;
    padding: 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.manual-input-mobile:focus {
    outline: none;
    border-color: #BF0000;
}

/* Responsive */
@media (max-width: 768px) {
    .table-responsive {
        display: none;
    }

    .mobile-participant-card {
        display: block;
    }

    .participant-controls {
        text-align: center;
    }
}

.participants-section h3 {
    color: #BF0000;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

.participant-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
    gap: 10px;
    margin-bottom: 10px;
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
    font-weight: 600;
    color: #333;
}

.participant-header-item {
    font-size: 14px;
    text-align: center;
}

.participant-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
    gap: 10px;
    margin-bottom: 15px;
    align-items: end;
}

.participant-row input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.participant-row input:focus {
    outline: none;
    border-color: #BF0000;
    box-shadow: 0 0 0 2px rgba(191, 0, 0, 0.1);
}

.btn-remove {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
}

.btn-add {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container-form">
    <div class="edit-container">
        <div class="page-header">
            <h1><i class="fas fa-edit"></i> Edit Training</h1>
            <p>Revisi Pengajuan Training</p>
        </div>

        <!-- Tampilkan komentar revisi -->
        <div class="revise-notice">
            <h4><i class="fas fa-exclamation-triangle"></i> Komentar Revisi</h4>
            <p>Training Anda perlu direvisi berdasarkan komentar berikut:</p>

            <?php if (!empty($training['comments_dept_head'])): ?>
                <div class="comment-box">
                    <strong>Dept Head:</strong>
                    <p><?= nl2br(htmlspecialchars($training['comments_dept_head'] ?? '')) ?></p>
                </div>
            <?php endif; ?>

            <?php if (!empty($training['comments_hrd'])): ?>
                <div class="comment-box">
                    <strong>LnD:</strong>
                    <p><?= nl2br(htmlspecialchars($training['comments_hrd'] ?? '')) ?></p>
                </div>
            <?php endif; ?>

            <?php if (!empty($training['comments_ga'])): ?>
                <div class="comment-box">
                    <strong>Manager HRGA:</strong>
                    <p><?= nl2br(htmlspecialchars($training['comments_ga'] ?? '')) ?></p>
                </div>
            <?php endif; ?>

            <?php if (!empty($training['comments_fm'])): ?>
                <div class="comment-box">
                    <strong>Factory Manager:</strong>
                    <p><?= nl2br(htmlspecialchars($training['comments_fm'] ?? '')) ?></p>
                </div>
            <?php endif; ?>

            <?php if (!empty($training['comments_dir'])): ?>
                <div class="comment-box">
                    <strong>Direktur:</strong>
                    <p><?= nl2br(htmlspecialchars($training['comments_dir'] ?? '')) ?></p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Info tentang field yang bisa diedit -->
        <div class="info-note">
            <h5><i class="fas fa-info-circle"></i> Informasi Penting</h5>
            <p>Anda dapat mengedit informasi berikut sesuai kebutuhan revisi:</p>
            <ul>
                <li><strong>Email</strong> - Email kontak yang dapat dihubungi</li>
                <li><strong>Nomor Telepon</strong> - Nomor telepon aktif yang dapat dihubungi</li>
                <li><strong>Nama Training</strong> - Nama/topik training yang diinginkan</li>
                <li><strong>Jenis Kompetensi Training</strong> - Soft Skill, Technical Skill, atau Compliance</li>
                <li><strong>Estimasi Tanggal Training</strong> - Tanggal yang diinginkan (minimal 1 bulan dari tanggal pengajuan awal)</li>
                <li><strong>Urgensi Training</strong> - Penjelasan mengapa training ini dibutuhkan</li>
                <li><strong>Peserta Training</strong> - Daftar peserta yang akan mengikuti training</li>
            </ul>
            <p><strong>Catatan:</strong> Detail seperti biaya, kontak trainer, waktu training, dan sharing knowledge akan ditentukan oleh tim LnD setelah training disetujui.</p>
        </div>

        <!-- Tampilkan pesan error/success -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= $_SESSION['success'] ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> <?= $_SESSION['error'] ?>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['errors'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> Terdapat kesalahan:
                <ul style="margin: 10px 0 0 20px;">
                    <?php foreach ($_SESSION['errors'] as $error): ?>
                        <li><?= htmlspecialchars($error) ?></li>
                    <?php endforeach; ?>
                </ul>
                <?php unset($_SESSION['errors']); ?>
            </div>
        <?php endif; ?>

        <!-- Form Edit Training -->
        <form method="POST" action="">
            <input type="hidden" name="csrf_token" value="<?= $_SESSION['token'] ?>">

            <!-- Informasi Kontak -->
            <div class="form-section">
                <h3><i class="fas fa-address-book"></i> Informasi Kontak</h3>

                <div class="form-group">
                    <label for="email">Email <span class="required">*</span></label>
                    <input type="email" id="email" name="email" required
                           value="<?= htmlspecialchars($training['email'] ?? '') ?>"
                           placeholder="contoh <EMAIL>">
                    <small style="color: #666; font-size: 12px;">Email dapat diubah jika diperlukan</small>
                </div>

                <div class="form-group">
                    <label for="phone">Nomor Telepon <span class="required">*</span></label>
                    <input type="tel" id="phone" name="phone" required
                           pattern="\d{10,15}"
                           title="Nomor telepon harus berisi 10-15 digit angka"
                           value="<?= htmlspecialchars($training['phone'] ?? '') ?>"
                           placeholder="Contoh: 081234567890">
                    <small style="color: #666; font-size: 12px;">Masukkan nomor telepon aktif yang dapat dihubungi (10-15 digit)</small>
                </div>
            </div>

            <!-- Informasi Training -->
            <div class="form-section">
                <h3><i class="fas fa-graduation-cap"></i> Informasi Training</h3>

                <div class="form-group">
                    <label for="training_topic">Nama Training <span class="required">*</span></label>
                    <input type="text" id="training_topic" name="training_topic" required
                           value="<?= htmlspecialchars($training['training_topic'] ?? '') ?>"
                           placeholder="Masukkan nama training yang diinginkan">
                    <small style="color: #666; font-size: 12px;">Masukkan nama/topik training yang diinginkan</small>
                </div>

                <div class="form-group">
                    <label for="training_skill_type">Jenis Kompetensi Training <span class="required">*</span></label>
                    <select id="training_skill_type" name="training_skill_type" required>
                        <option value="">Pilih kompetensi</option>
                        <option value="Soft Skill" <?= $training['training_skill_type'] == 'Soft Skill' ? 'selected' : '' ?>>Soft Skill</option>
                        <option value="Technical Skill" <?= $training['training_skill_type'] == 'Technical Skill' ? 'selected' : '' ?>>Technical Skill</option>
                        <option value="Compliance" <?= $training['training_skill_type'] == 'Compliance' ? 'selected' : '' ?>>Compliance</option>
                    </select>
                    <small style="color: #666; font-size: 12px;">Pilih jenis kompetensi training yang sesuai</small>
                </div>
            </div>

            <!-- Jadwal Training -->
            <div class="form-section">
                <h3><i class="fas fa-calendar-alt"></i> Jadwal Training</h3>

                <div class="form-group">
                    <label for="start_date">Estimasi Tanggal Training <span class="required">*</span></label>
                    <?php
                    // Untuk revisi, gunakan tanggal pengajuan awal sebagai referensi
                    $reference_date = new DateTime($training['submission_date'] ?? $training['created_at'] ?? 'now');
                    $min_date = clone $reference_date;
                    $min_date->add(new DateInterval('P1M'));
                    $min_date_str = $min_date->format('Y-m-d');
                    ?>
                    <input type="date" id="start_date" name="start_date" required
                           value="<?= htmlspecialchars($training['start_date'] ?? '') ?>"
                           min="<?= $min_date_str ?>">
                    <small style="color: #666; font-size: 12px;">
                        Tanggal training minimal harus 1 bulan dari tanggal pengajuan awal
                        (<?= $reference_date->format('d/m/Y') ?>) - Minimal: <?= $min_date->format('d/m/Y') ?>
                    </small>
                </div>
            </div>

            <!-- Urgensi Training -->
            <div class="form-section">
                <h3><i class="fas fa-exclamation-circle"></i> Urgensi Training</h3>

                <div class="form-group">
                    <label for="additional_info">Urgensi Training <span class="required">*</span></label>
                    <textarea id="additional_info" name="additional_info" rows="4" required
                              placeholder="Jelaskan Mengapa Training Ini Dibutuhkan"><?= htmlspecialchars($training['additional_info'] ?? '') ?></textarea>
                    <small style="color: #666; font-size: 12px;">Jelaskan tujuan training, urgensi pelaksanaan, rekomendasi vendor (jika ada), dasar regulasi yang mewajibkan (jika ada), dan informasi penting lainnya terkait kebutuhan training ini</small>
                </div>
            </div>

            <!-- Peserta Training -->
            <div class="form-section">
                <h3><i class="fas fa-users"></i> Peserta Training</h3>

                <!-- Search Employee Section -->
                <div class="search-section">
                    <input type="text" id="search_employee" placeholder="Cari karyawan berdasarkan nama, NIK, atau departemen...">
                    <div id="employee_results"></div>
                    <small class="form-text text-muted">Ketik minimal 3 karakter untuk mencari karyawan</small>
                </input>

                <div class="participant-controls">
                    <button type="button" class="btn-add-participant" onclick="addParticipantManual()">
                        <i class="fas fa-user-plus"></i> Tambah Peserta Manual
                    </button>
                </div>

                <!-- Tabel untuk tampilan desktop -->
                <div class="table-responsive">
                    <table id="participantTable">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Nama</th>
                                <th>NIK</th>
                                <th>Departemen</th>
                                <th>Bagian</th>
                                <th>Jabatan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody id="participant_list">
                            <?php if (!empty($participants)): ?>
                                <?php foreach ($participants as $index => $participant): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <td>
                                            <?= htmlspecialchars(trim($participant['name'] ?? '')) ?>
                                            <input type="hidden" name="nama[]" value="<?= htmlspecialchars(trim($participant['name'] ?? '')) ?>">
                                        </td>
                                        <td>
                                            <?= htmlspecialchars(trim($participant['nik'] ?? '')) ?>
                                            <input type="hidden" name="nik[]" value="<?= htmlspecialchars(trim($participant['nik'] ?? '')) ?>">
                                        </td>
                                        <td>
                                            <?= htmlspecialchars(trim($participant['department'] ?? '')) ?>
                                            <input type="hidden" name="departemen[]" value="<?= htmlspecialchars(trim($participant['department'] ?? '')) ?>">
                                        </td>
                                        <td>
                                            <?= htmlspecialchars(trim($participant['sub_department'] ?? '')) ?>
                                            <input type="hidden" name="bagian[]" value="<?= htmlspecialchars(trim($participant['sub_department'] ?? '')) ?>">
                                        </td>
                                        <td>
                                            <?= htmlspecialchars(trim($participant['position'] ?? '')) ?>
                                            <input type="hidden" name="jabatan[]" value="<?= htmlspecialchars(trim($participant['position'] ?? '')) ?>">
                                        </td>
                                        <td>
                                            <button type="button" class="btn-remove" onclick="removeParticipant(this)">
                                                <i class="fas fa-trash"></i> Hapus
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Card untuk tampilan mobile -->
                <div class="mobile-participant-card" id="mobile_participant_list">
                    <?php if (!empty($participants)): ?>
                        <?php foreach ($participants as $index => $participant): ?>
                            <div class="mobile-participant-item" data-index="<?= $index + 1 ?>" data-nik="<?= htmlspecialchars(trim($participant['nik'] ?? '')) ?>">
                                <div class="mobile-participant-header">
                                    <span>Peserta #<?= $index + 1 ?></span>
                                    <button type="button" class="btn-remove" onclick="removeParticipant(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <div class="mobile-participant-body">
                                    <div class="mobile-participant-row">
                                        <div class="mobile-participant-label">Nama:</div>
                                        <div class="mobile-participant-value">
                                            <?= htmlspecialchars(trim($participant['name'] ?? '')) ?>
                                        </div>
                                    </div>
                                    <div class="mobile-participant-row">
                                        <div class="mobile-participant-label">NIK:</div>
                                        <div class="mobile-participant-value">
                                            <?= htmlspecialchars(trim($participant['nik'] ?? '')) ?>
                                        </div>
                                    </div>
                                    <div class="mobile-participant-row">
                                        <div class="mobile-participant-label">Departemen:</div>
                                        <div class="mobile-participant-value">
                                            <?= htmlspecialchars(trim($participant['department'] ?? '')) ?>
                                        </div>
                                    </div>
                                    <div class="mobile-participant-row">
                                        <div class="mobile-participant-label">Bagian:</div>
                                        <div class="mobile-participant-value">
                                            <?= htmlspecialchars(trim($participant['sub_department'] ?? '')) ?>
                                        </div>
                                    </div>
                                    <div class="mobile-participant-row">
                                        <div class="mobile-participant-label">Jabatan:</div>
                                        <div class="mobile-participant-value">
                                            <?= htmlspecialchars(trim($participant['position'] ?? '')) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Tombol Submit -->
            <div class="btn-group">
                <a href="detail_training.php?id=<?= $training_id ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Batal
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> Kirim Ulang Training
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Inisialisasi array participants dengan data existing
if (!window.participants) {
    window.participants = [];
}

// Load existing participants ke array
<?php if (!empty($participants)): ?>
    <?php foreach ($participants as $participant): ?>
        window.participants.push({
            nama: "<?= addslashes(trim($participant['name'] ?? '')) ?>",
            nik: "<?= addslashes(trim($participant['nik'] ?? '')) ?>",
            departemen: "<?= addslashes(trim($participant['department'] ?? '')) ?>",
            bagian: "<?= addslashes(trim($participant['sub_department'] ?? '')) ?>",
            jabatan: "<?= addslashes(trim($participant['position'] ?? '')) ?>"
        });
    <?php endforeach; ?>
<?php endif; ?>

// Event listener untuk search employee
document.addEventListener('DOMContentLoaded', function() {
    const searchEmployee = document.getElementById('search_employee');
    if (searchEmployee) {
        searchEmployee.addEventListener('input', function(e) {
            const query = this.value.trim();
            if (query.length < 3) {
                document.getElementById('employee_results').innerHTML = '';
                return;
            }

            fetch(`../admin/search_employees.php?search=${encodeURIComponent(query)}&limit=10`)
                .then(response => response.json())
                .then(data => {
                    const resultsDiv = document.getElementById('employee_results');
                    resultsDiv.innerHTML = '';

                    if (!data.success || data.employees.length === 0) {
                        resultsDiv.innerHTML = '<div class="no-results">Tidak ada karyawan ditemukan</div>';
                        return;
                    }

                    data.employees.forEach(employee => {
                        const div = document.createElement('div');
                        div.className = 'employee-result';
                        div.innerHTML = `
                            <div class="employee-info">
                                <strong>${employee.nama}</strong> (${employee.nik})
                                <br><small>${employee.jabatan} - ${employee.dept}</small>
                            </div>
                        `;
                        div.onclick = () => addParticipant({
                            nama: employee.nama,
                            nik: employee.nik,
                            departemen: employee.dept,
                            bagian: employee.bagian || employee.dept,
                            jabatan: employee.jabatan
                        });
                        resultsDiv.appendChild(div);
                    });
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('employee_results').innerHTML =
                        '<div class="error">Terjadi kesalahan saat mencari karyawan</div>';
                });
        });
    }
});

function addParticipant(participant) {
    // Validasi data
    if (!participant.nama || !participant.nik || !participant.departemen ||
        !participant.bagian || !participant.jabatan) {
        alert('Data peserta tidak lengkap!');
        return;
    }

    // Cek duplikasi NIK - lebih ketat
    const normalizedNIK = participant.nik.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();

    // Debug: Log NIK yang akan ditambahkan
    console.log('Adding participant with NIK:', participant.nik, 'normalized:', normalizedNIK);

    // Cek di array participants
    const isDuplicateInArray = window.participants.some(p => {
        const existingNIK = p.nik.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
        console.log('Checking against array NIK:', p.nik, 'normalized:', existingNIK);
        return existingNIK === normalizedNIK;
    });

    // Cek di tabel yang sudah ada menggunakan input hidden values
    const existingNikInputs = document.querySelectorAll('input[name="nik[]"]');
    const isDuplicateInTable = Array.from(existingNikInputs).some(input => {
        const existingNIK = input.value.trim().replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
        console.log('Checking against table NIK:', input.value.trim(), 'normalized:', existingNIK);
        return existingNIK === normalizedNIK;
    });

    if (isDuplicateInArray || isDuplicateInTable) {
        console.log('Duplicate found - Array:', isDuplicateInArray, 'Table:', isDuplicateInTable);
        alert('NIK "' + participant.nik + '" sudah ada dalam daftar peserta!');
        return;
    }

    // Tambah ke array participants
    window.participants.push(participant);

    // Update desktop table
    const tbody = document.getElementById('participant_list');
    const rowNum = tbody.children.length + 1;

    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${rowNum}</td>
        <td>
            ${participant.nama}
            <input type="hidden" name="nama[]" value="${participant.nama}">
        </td>
        <td>
            ${participant.nik}
            <input type="hidden" name="nik[]" value="${participant.nik}">
        </td>
        <td>
            ${participant.departemen}
            <input type="hidden" name="departemen[]" value="${participant.departemen}">
        </td>
        <td>
            ${participant.bagian}
            <input type="hidden" name="bagian[]" value="${participant.bagian}">
        </td>
        <td>
            ${participant.jabatan}
            <input type="hidden" name="jabatan[]" value="${participant.jabatan}">
        </td>
        <td>
            <button type="button" class="btn-remove" onclick="removeParticipant(this)">
                <i class="fas fa-trash"></i> Hapus
            </button>
        </td>
    `;

    tbody.appendChild(row);

    // Update mobile view (without hidden inputs to avoid duplication)
    const mobileList = document.getElementById('mobile_participant_list');
    const mobileCard = document.createElement('div');
    mobileCard.className = 'mobile-participant-item';
    mobileCard.setAttribute('data-index', rowNum);
    mobileCard.setAttribute('data-nik', participant.nik);
    mobileCard.innerHTML = `
        <div class="mobile-participant-header">
            <span>Peserta #${rowNum}</span>
            <button type="button" class="btn-remove" onclick="removeParticipant(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="mobile-participant-body">
            <div class="mobile-participant-row">
                <div class="mobile-participant-label">Nama:</div>
                <div class="mobile-participant-value">${participant.nama}</div>
            </div>
            <div class="mobile-participant-row">
                <div class="mobile-participant-label">NIK:</div>
                <div class="mobile-participant-value">${participant.nik}</div>
            </div>
            <div class="mobile-participant-row">
                <div class="mobile-participant-label">Departemen:</div>
                <div class="mobile-participant-value">${participant.departemen}</div>
            </div>
            <div class="mobile-participant-row">
                <div class="mobile-participant-label">Bagian:</div>
                <div class="mobile-participant-value">${participant.bagian}</div>
            </div>
            <div class="mobile-participant-row">
                <div class="mobile-participant-label">Jabatan:</div>
                <div class="mobile-participant-value">${participant.jabatan}</div>
            </div>
        </div>
    `;
    mobileList.appendChild(mobileCard);

    // Clear search results
    const searchResults = document.getElementById('employee_results');
    if (searchResults) {
        searchResults.innerHTML = '';
    }

    // Reset search input
    const searchInput = document.getElementById('search_employee');
    if (searchInput) {
        searchInput.value = '';
    }
}

function addParticipantManual() {
    // Add to desktop table
    let tbody = document.getElementById("participant_list");
    let index = tbody.getElementsByTagName("tr").length + 1;
    let row = document.createElement("tr");
    row.innerHTML = `
        <td>${index}</td>
        <td><input type="text" name="nama[]" required placeholder="Nama Peserta" class="manual-input" onblur="validateManualInput(this)"></td>
        <td><input type="text" name="nik[]" required placeholder="NIK Peserta" class="manual-input" onblur="validateNIKDuplicate(this)"></td>
        <td><input type="text" name="departemen[]" required placeholder="Departemen" class="manual-input"></td>
        <td><input type="text" name="bagian[]" required placeholder="Bagian" class="manual-input"></td>
        <td><input type="text" name="jabatan[]" required placeholder="Jabatan" class="manual-input"></td>
        <td><button type="button" class="btn-remove" onclick="removeParticipant(this)"><i class="fas fa-trash"></i> Hapus</button></td>
    `;
    tbody.appendChild(row);

    // Add to mobile view
    let mobileList = document.getElementById("mobile_participant_list");
    let mobileCard = document.createElement("div");
    mobileCard.className = "mobile-participant-item";
    mobileCard.setAttribute("data-index", index);
    mobileCard.innerHTML = `
        <div class="mobile-participant-header">
            <span>Peserta #${index}</span>
            <button type="button" class="btn-remove" onclick="removeParticipant(this)"><i class="fas fa-trash"></i></button>
        </div>
        <div class="mobile-participant-body">
            <div class="mobile-participant-row">
                <div class="mobile-participant-label">Nama:</div>
                <div class="mobile-participant-value"><input type="text" name="nama[]" required placeholder="Nama Peserta" class="manual-input-mobile"></div>
            </div>
            <div class="mobile-participant-row">
                <div class="mobile-participant-label">NIK:</div>
                <div class="mobile-participant-value"><input type="text" name="nik[]" required placeholder="NIK Peserta" class="manual-input-mobile"></div>
            </div>
            <div class="mobile-participant-row">
                <div class="mobile-participant-label">Departemen:</div>
                <div class="mobile-participant-value"><input type="text" name="departemen[]" required placeholder="Departemen" class="manual-input-mobile"></div>
            </div>
            <div class="mobile-participant-row">
                <div class="mobile-participant-label">Bagian:</div>
                <div class="mobile-participant-value"><input type="text" name="bagian[]" required placeholder="Bagian" class="manual-input-mobile"></div>
            </div>
            <div class="mobile-participant-row">
                <div class="mobile-participant-label">Jabatan:</div>
                <div class="mobile-participant-value"><input type="text" name="jabatan[]" required placeholder="Jabatan" class="manual-input-mobile"></div>
            </div>
        </div>
    `;
    mobileList.appendChild(mobileCard);

    // Sync inputs between desktop and mobile views
    syncInputs(index - 1);
}

function syncInputs(index) {
    const desktopInputs = document.querySelectorAll(`#participant_list tr:nth-child(${index + 1}) input`);
    const mobileInputs = document.querySelectorAll(`.mobile-participant-item[data-index="${index + 1}"] input`);

    // Sync desktop to mobile
    desktopInputs.forEach((input, i) => {
        input.addEventListener('input', function() {
            if (mobileInputs[i]) mobileInputs[i].value = this.value;
        });
    });

    // Sync mobile to desktop
    mobileInputs.forEach((input, i) => {
        input.addEventListener('input', function() {
            if (desktopInputs[i]) desktopInputs[i].value = this.value;
        });
    });
}

function removeParticipant(button) {
    let nik;

    // Remove from desktop table
    if (button.closest('tr')) {
        const row = button.closest('tr');
        nik = row.querySelector('input[name="nik[]"]')?.value || row.cells[2].textContent.trim();
        row.remove();
    } else if (button.closest('.mobile-participant-item')) {
        const mobileCard = button.closest('.mobile-participant-item');
        nik = mobileCard.getAttribute('data-nik') || mobileCard.querySelector('input[name="nik[]"]')?.value;
        mobileCard.remove();
    }

    // Hapus dari array participants
    if (nik) {
        window.participants = window.participants.filter(p => p.nik !== nik);
    }

    // Remove from mobile view if not already removed
    const mobileCard = document.querySelector(`.mobile-participant-item[data-nik="${nik}"]`);
    if (mobileCard) {
        mobileCard.remove();
    }

    // Remove from desktop view if not already removed
    const desktopRows = document.querySelectorAll('#participant_list tr');
    desktopRows.forEach(row => {
        const rowNik = row.querySelector('input[name="nik[]"]')?.value || row.cells[2]?.textContent.trim();
        if (rowNik === nik) {
            row.remove();
        }
    });

    // Update desktop table indices
    const tbody = document.getElementById('participant_list');
    Array.from(tbody.children).forEach((row, i) => {
        row.cells[0].textContent = i + 1;
    });

    // Update mobile card indices
    const mobileCards = document.querySelectorAll('.mobile-participant-item');
    mobileCards.forEach((card, i) => {
        card.setAttribute('data-index', i + 1);
        card.querySelector('.mobile-participant-header span').innerText = `Peserta #${i + 1}`;
    });
}

// Function untuk validasi NIK duplikasi pada input manual
function validateNIKDuplicate(input) {
    const nik = input.value.trim();
    if (!nik) return;

    const normalizedNIK = nik.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
    const currentInput = input;

    console.log('Validating manual NIK:', nik, 'normalized:', normalizedNIK);

    // Cek duplikasi di semua input NIK kecuali input saat ini
    const allNikInputs = document.querySelectorAll('input[name="nik[]"]');
    let isDuplicate = false;

    Array.from(allNikInputs).forEach(nikInput => {
        if (nikInput !== currentInput && nikInput.value.trim()) {
            const existingNIK = nikInput.value.trim();
            const existingNormalizedNIK = existingNIK.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
            console.log('Checking against manual NIK:', existingNIK, 'normalized:', existingNormalizedNIK);

            if (existingNormalizedNIK === normalizedNIK) {
                isDuplicate = true;
            }
        }
    });

    // Juga cek di array participants
    const isDuplicateInArray = window.participants.some(p => {
        const existingNIK = p.nik.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
        console.log('Checking against array NIK:', p.nik, 'normalized:', existingNIK);
        return existingNIK === normalizedNIK;
    });

    if (isDuplicate || isDuplicateInArray) {
        console.log('Manual duplicate found - Inputs:', isDuplicate, 'Array:', isDuplicateInArray);
        alert('NIK "' + nik + '" sudah ada dalam daftar peserta!');
        input.value = '';
        input.focus();
    }
}

// Function untuk validasi input manual
function validateManualInput(input) {
    const value = input.value.trim();
    if (value) {
        input.value = value; // Trim whitespace
    }
}

// Validasi tanggal - untuk revisi training
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date untuk input tanggal training (1 bulan dari tanggal pengajuan awal)
    const trainingDateInput = document.getElementById('start_date');
    if (trainingDateInput) {
        // Gunakan tanggal pengajuan awal sebagai referensi untuk revisi
        <?php
        $reference_date_js = $reference_date->format('Y-m-d');
        $min_date_js = $min_date->format('Y-m-d');
        ?>
        const referenceDate = new Date('<?= $reference_date_js ?>');
        const minDate = new Date('<?= $min_date_js ?>');

        // Set min attribute sudah diset di PHP, tapi pastikan konsisten
        trainingDateInput.min = '<?= $min_date_js ?>';

        // Tambahkan validasi saat input berubah
        trainingDateInput.addEventListener('change', function() {
            const selectedDate = new Date(this.value);
            if (selectedDate < minDate) {
                const referenceDateStr = referenceDate.toLocaleDateString('id-ID');
                const minDateStr = minDate.toLocaleDateString('id-ID');
                alert(`Tanggal training harus minimal 1 bulan dari tanggal pengajuan awal (${referenceDateStr}). Tanggal minimal: ${minDateStr}`);
                this.value = ''; // Reset nilai input
            }
        });
    }

    // Validasi form sebelum submit
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Validasi tanggal menggunakan tanggal pengajuan awal sebagai referensi
            const trainingDate = new Date(trainingDateInput.value);
            const minDate = new Date('<?= $min_date_js ?>');

            if (trainingDate < minDate) {
                e.preventDefault();
                const referenceDateStr = new Date('<?= $reference_date_js ?>').toLocaleDateString('id-ID');
                const minDateStr = minDate.toLocaleDateString('id-ID');
                alert(`Tanggal training harus minimal 1 bulan dari tanggal pengajuan awal (${referenceDateStr}). Tanggal minimal: ${minDateStr}`);
                trainingDateInput.focus();
                return false;
            }

            // Validasi duplikasi NIK sebelum submit
            const nikInputs = document.querySelectorAll('input[name="nik[]"]');
            const niks = [];
            const nikDetails = []; // For debugging
            let hasDuplicate = false;

            nikInputs.forEach((input, index) => {
                const nik = input.value.trim();
                if (nik) {
                    const normalizedNIK = nik.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();

                    // Debug info
                    nikDetails.push({
                        index: index,
                        original: nik,
                        normalized: normalizedNIK,
                        element: input
                    });

                    if (niks.includes(normalizedNIK)) {
                        hasDuplicate = true;
                        console.log('Duplicate NIK found:', nikDetails);
                        alert('Ditemukan NIK duplikat: "' + nik + '". Silakan periksa kembali daftar peserta.');
                        input.focus();
                        return;
                    }
                    niks.push(normalizedNIK);
                }
            });

            if (hasDuplicate) {
                e.preventDefault();
                return false;
            }
        });
    }
});
</script>
</div>
<?php include '../config/footer.php'; ?>
</body>
</html>

<?php $conn->close(); ?>
