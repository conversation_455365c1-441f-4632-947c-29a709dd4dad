<?php
/**
 * Reset User Password - Admin Only
 * API untuk reset password user ke password yang diketahui
 */

session_start();
include '../config/config.php';

// Set JSON header
header('Content-Type: application/json');

// Check admin access
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'Access denied. Admin only.'
    ]);
    exit;
}

// Check if POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid JSON input'
    ]);
    exit;
}

$user_id = $input['user_id'] ?? null;
$new_password = $input['new_password'] ?? null;

// Validate input
if (!$user_id || !$new_password) {
    echo json_encode([
        'success' => false,
        'message' => 'User ID and new password are required'
    ]);
    exit;
}

// Validate user exists
$check_query = "SELECT id, name, nik, email FROM users WHERE id = ?";
$check_stmt = $conn->prepare($check_query);
$check_stmt->bind_param("i", $user_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();

if ($check_result->num_rows === 0) {
    echo json_encode([
        'success' => false,
        'message' => 'User not found'
    ]);
    exit;
}

$user_data = $check_result->fetch_assoc();

try {
    // Hash new password
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    
    // Update password
    $update_query = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("si", $hashed_password, $user_id);
    
    if ($update_stmt->execute()) {
        // Log activity
        if (file_exists('../config/activity_logger.php')) {
            include_once '../config/activity_logger.php';
            if (function_exists('log_activity')) {
                log_activity(
                    $_SESSION['user_id'], 
                    "Reset password user: {$user_data['name']} (NIK: {$user_data['nik']}) ke password: {$new_password}",
                    "user_password_reset",
                    [
                        'target_user_id' => $user_id,
                        'target_user_name' => $user_data['name'],
                        'target_user_nik' => $user_data['nik'],
                        'new_password' => $new_password,
                        'admin_user_id' => $_SESSION['user_id']
                    ]
                );
            }
        }
        
        // Also log to file for security audit
        $log_entry = date('Y-m-d H:i:s') . " | Admin ID: " . $_SESSION['user_id'] . 
                    " | Reset password for User ID: {$user_id} ({$user_data['name']}) to: {$new_password}\n";
        
        $log_file = '../logs/password_reset.log';
        $log_dir = dirname($log_file);
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
        
        echo json_encode([
            'success' => true,
            'message' => "Password berhasil direset ke: {$new_password}",
            'user_name' => $user_data['name'],
            'user_nik' => $user_data['nik'],
            'new_password' => $new_password
        ]);
        
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update password: ' . $conn->error
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
