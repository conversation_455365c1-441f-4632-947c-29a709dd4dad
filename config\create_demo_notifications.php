<?php
/**
 * Script untuk membuat notifikasi demo yang bisa dilihat di browser
 */

// Disable session untuk testing
define('NO_SESSION', true);

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/../includes/notification_helper.php';

echo "🔔 MEMBUAT NOTIFIKASI DEMO\n";
echo "==========================\n\n";

// Cari user aktif
$users_query = "SELECT id, name, email, role_id FROM users WHERE is_active = 1 ORDER BY id LIMIT 5";
$users_result = $conn->query($users_query);

$demo_users = [];
while ($user = $users_result->fetch_assoc()) {
    $demo_users[] = $user;
}

if (empty($demo_users)) {
    echo "❌ Tidak ada user aktif ditemukan\n";
    exit(1);
}

echo "👥 User yang akan menerima notifikasi demo:\n";
foreach ($demo_users as $user) {
    echo "   - {$user['name']} (ID: {$user['id']}, Role: {$user['role_id']})\n";
}
echo "\n";

// Notifikasi demo yang akan dibuat
$demo_notifications = [
    [
        'title' => '🎓 Training Baru: Digital Marketing',
        'message' => 'Training "Digital Marketing Fundamentals" telah dibuka untuk pendaftaran. Kuota terbatas, segera daftar!',
        'type' => 'info'
    ],
    [
        'title' => '✅ Pengajuan Training Disetujui',
        'message' => 'Selamat! Pengajuan training Anda telah disetujui oleh atasan. Silakan cek detail di dashboard.',
        'type' => 'success'
    ],
    [
        'title' => '⚠️ Deadline Assignment Mendekati',
        'message' => 'Anda memiliki assignment yang akan berakhir dalam 2 hari. Pastikan menyelesaikan tepat waktu.',
        'type' => 'warning'
    ],
    [
        'title' => '❌ Training Dibatalkan',
        'message' => 'Training yang dijadwalkan hari ini dibatalkan karena trainer berhalangan. Akan ada penjadwalan ulang.',
        'type' => 'error'
    ],
    [
        'title' => '📅 Reminder: Training Besok',
        'message' => 'Jangan lupa! Training "Leadership Development" akan dimulai besok pukul 09:00 di ruang meeting.',
        'type' => 'warning'
    ]
];

echo "📝 Membuat notifikasi demo...\n";

$created_count = 0;
foreach ($demo_users as $index => $user) {
    $notification = $demo_notifications[$index % count($demo_notifications)];
    
    try {
        $created = createNotification(
            $user['id'],
            null, // General notification
            $notification['title'],
            $notification['message'],
            $notification['type']
        );
        
        if ($created) {
            echo "   ✅ '{$notification['title']}' → {$user['name']}\n";
            $created_count++;
        } else {
            echo "   ❌ Gagal membuat notifikasi untuk {$user['name']}\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Error untuk {$user['name']}: " . $e->getMessage() . "\n";
    }
}

echo "\n📊 Hasil:\n";
echo "   ✅ Berhasil dibuat: $created_count notifikasi\n";
echo "   👥 User yang menerima: " . count($demo_users) . " user\n";

// Tampilkan statistik terbaru
$stats_query = "SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread,
                    COUNT(DISTINCT user_id) as users_with_notifications
                FROM training_notifications";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

echo "\n📈 Statistik sistem:\n";
echo "   📊 Total notifikasi: {$stats['total']}\n";
echo "   📬 Belum dibaca: {$stats['unread']}\n";
echo "   👥 User dengan notifikasi: {$stats['users_with_notifications']}\n";

echo "\n🎯 CARA MELIHAT NOTIFIKASI:\n";
echo "============================\n";
echo "1. 🌐 Buka browser dan login ke sistem training\n";
echo "2. 🔔 Lihat icon bell di header navigation (kanan atas)\n";
echo "3. 🔴 Jika ada badge merah dengan angka, berarti ada notifikasi baru\n";
echo "4. 👆 Klik icon bell untuk melihat dropdown notifikasi\n";
echo "5. 📄 Klik 'Lihat Semua Notifikasi' untuk halaman lengkap\n";
echo "6. ✅ Klik notifikasi untuk mark as read\n";

echo "\n📱 URL untuk test:\n";
echo "   - Pemohon: http://localhost/training/pemohon/\n";
echo "   - Dept Head: http://localhost/training/dept_head/\n";
echo "   - LnD: http://localhost/training/LnD/\n";
echo "   - Director: http://localhost/training/Dir/\n";

echo "\n🧹 Untuk membersihkan notifikasi demo:\n";
echo "   php config/cleanup_demo_notifications.php\n";

echo "\n🚀 NOTIFIKASI DEMO SIAP DILIHAT DI BROWSER!\n";

$conn->close();
?>
