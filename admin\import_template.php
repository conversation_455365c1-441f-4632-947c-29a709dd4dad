<?php
/**
 * Import Template Page for Admin
 * This page allows admins to download an Excel template and import training data
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Handle form submissions
$success_message = '';
$error_message = '';
$import_results = [];

// Handle template download
if (isset($_GET['download_template'])) {
    $template_type = isset($_GET['type']) ? $_GET['type'] : '';

    if ($template_type == 'class') {
        // Download template kelas
        header('Location: generate_class_template.php');
        exit();
    } elseif ($template_type == 'quiz') {
        // Download template kuis
        header('Location: generate_quiz_template.php');
        exit();
    } else {
        // Template tidak valid
        $error_message = "Tipe template tidak valid.";
    }
}

// Handle import submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['import_data'])) {
    // Redirect to the process_import.php script
    // The actual processing will be done there
    header('Location: process_import.php');
    exit();
}

// Check for session messages
if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

if (isset($_SESSION['error_message'])) {
    $error_message = $_SESSION['error_message'];
    unset($_SESSION['error_message']);
}

if (isset($_SESSION['import_results'])) {
    $import_results = $_SESSION['import_results'];
    unset($_SESSION['import_results']);
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .import-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .import-section h3 {
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .template-info {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .template-sheet {
        margin-bottom: 15px;
    }

    .template-sheet h5 {
        color: #0d6efd;
        margin-bottom: 5px;
    }

    .import-results {
        margin-top: 30px;
    }

    .result-item {
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    .result-success {
        background-color: #d4edda;
        color: #155724;
    }

    .result-error {
        background-color: #f8d7da;
        color: #721c24;
    }

    .result-warning {
        background-color: #fff3cd;
        color: #856404;
    }

    .upload-area {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 30px;
        text-align: center;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .upload-area:hover {
        border-color: #0d6efd;
    }

    .upload-icon {
        font-size: 48px;
        color: #6c757d;
        margin-bottom: 15px;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1 class="welcome-section"><i class="fas fa-file-import"></i> Import Data Training
            <p class="text-white">Import kelas dan kuis training dari file Excel secara massal</p>
            </h1>
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="form-container" style="margin-bottom:10px;">
                <h3>Template Excel</h3>
                <div class="template-info">
                    <p>Kami menyediakan dua template Excel terpisah untuk mengimpor data:</p>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0"><i class="fas fa-chalkboard-teacher"></i> Template Kelas</h5>
                                </div>
                                <div class="card-body">
                                    <p>Template ini berisi:</p>
                                    <ul>
                                        <li>Informasi dasar kelas training</li>
                                        <li>Tanggal mulai dan selesai</li>
                                        <li>Status kelas</li>
                                    </ul>
                                    <p class="text-muted">Gunakan template ini untuk membuat kelas training baru secara massal.</p>
                                </div>
                                <div class="card-footer text-center">
                                    <a href="import_template.php?download_template=1&type=class" class="btn btn-primary">
                                        <i class="fas fa-download"></i> Download Template Kelas
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0"><i class="fas fa-question-circle"></i> Template Kuis</h5>
                                </div>
                                <div class="card-body">
                                    <p>Template ini berisi:</p>
                                    <ul>
                                        <li>Informasi kuis</li>
                                        <li>Pertanyaan kuis</li>
                                        <li>Opsi jawaban</li>
                                    </ul>
                                    <p class="text-muted">Gunakan template ini untuk membuat kuis dan pertanyaan secara massal.</p>
                                </div>
                                <div class="card-footer text-center">
                                    <a href="import_template.php?download_template=1&type=quiz" class="btn btn-success">
                                        <i class="fas fa-download"></i> Download Template Kuis
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="import-section">
                <h3>Import Data</h3>
                <form method="post" action="process_import.php" enctype="multipart/form-data">
                    <div class="upload-area" id="upload-area">
                        <div class="upload-icon">
                            <i class="fas fa-file-excel"></i>
                        </div>
                        <h4>Pilih File Excel atau Drag & Drop di sini</h4>
                        <p class="text-muted">Format file yang didukung: .xlsx, .xls</p>
                        <input type="file" name="import_file" id="import-file" class="form-control" style="display: none;" accept=".xlsx, .xls">
                        <button type="button" id="select-file-btn" class="btn btn-outline-primary mt-3">
                            <i class="fas fa-folder-open"></i> Pilih File
                        </button>
                    </div>

                    <div id="file-info" class="alert alert-info" style="display: none;">
                        <i class="fas fa-info-circle"></i> File terpilih: <span id="file-name"></span>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> <strong>Penting:</strong> Pastikan Anda menggunakan template yang disediakan. Jika Anda ingin mengimpor kelas, gunakan template kelas. Jika Anda ingin mengimpor kuis, gunakan template kuis.
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" name="import_data" id="import-btn" class="btn btn-success" disabled>
                            <i class="fas fa-file-import"></i> Import Data
                        </button>
                    </div>
                </form>
            </div>

            <?php if (!empty($import_results)): ?>
                <div class="import-results">
                    <h3>Hasil Import</h3>

                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-clipboard-check"></i> Ringkasan Hasil Import</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $success_count = 0;
                            $error_count = 0;
                            $info_count = 0;

                            foreach ($import_results as $result) {
                                if ($result['type'] == 'success') $success_count++;
                                elseif ($result['type'] == 'error') $error_count++;
                                elseif ($result['type'] == 'info') $info_count++;
                            }
                            ?>

                            <div class="row text-center">
                                <div class="col-md-4">
                                    <div class="p-3 bg-success text-white rounded mb-3">
                                        <h3><?= $success_count ?></h3>
                                        <p class="mb-0">Berhasil</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="p-3 bg-danger text-white rounded mb-3">
                                        <h3><?= $error_count ?></h3>
                                        <p class="mb-0">Error</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="p-3 bg-info text-white rounded mb-3">
                                        <h3><?= $info_count ?></h3>
                                        <p class="mb-0">Informasi</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="accordion" id="importResultsAccordion">
                        <?php
                        $sheet_groups = [];

                        // Group results by sheet
                        foreach ($import_results as $result) {
                            $sheet = $result['sheet'];
                            if (!isset($sheet_groups[$sheet])) {
                                $sheet_groups[$sheet] = [];
                            }
                            $sheet_groups[$sheet][] = $result;
                        }

                        $i = 0;
                        foreach ($sheet_groups as $sheet => $results):
                            $i++;
                        ?>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading<?= $i ?>">
                                    <button class="accordion-button <?= ($i > 1) ? 'collapsed' : '' ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?= $i ?>" aria-expanded="<?= ($i == 1) ? 'true' : 'false' ?>" aria-controls="collapse<?= $i ?>">
                                        <i class="fas fa-table me-2"></i> <?= $sheet ?>
                                    </button>
                                </h2>
                                <div id="collapse<?= $i ?>" class="accordion-collapse collapse <?= ($i == 1) ? 'show' : '' ?>" aria-labelledby="heading<?= $i ?>" data-bs-parent="#importResultsAccordion">
                                    <div class="accordion-body">
                                        <?php foreach ($results as $result): ?>
                                            <div class="result-item result-<?= $result['type'] ?> mb-2 p-2 rounded">
                                                <?php if ($result['type'] == 'success'): ?>
                                                    <i class="fas fa-check-circle me-2"></i>
                                                <?php elseif ($result['type'] == 'error'): ?>
                                                    <i class="fas fa-times-circle me-2"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-info-circle me-2"></i>
                                                <?php endif; ?>
                                                <?= $result['message'] ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const uploadArea = document.getElementById('upload-area');
        const importFile = document.getElementById('import-file');
        const selectFileBtn = document.getElementById('select-file-btn');
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const importBtn = document.getElementById('import-btn');

        // Handle file selection button
        selectFileBtn.addEventListener('click', function() {
            importFile.click();
        });

        // Handle file selection
        importFile.addEventListener('change', function() {
            if (this.files.length > 0) {
                fileName.textContent = this.files[0].name;
                fileInfo.style.display = 'block';
                importBtn.disabled = false;
            } else {
                fileInfo.style.display = 'none';
                importBtn.disabled = true;
            }
        });

        // Handle drag and drop
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('border-primary');
        });

        uploadArea.addEventListener('dragleave', function() {
            uploadArea.classList.remove('border-primary');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('border-primary');

            if (e.dataTransfer.files.length > 0) {
                importFile.files = e.dataTransfer.files;
                fileName.textContent = e.dataTransfer.files[0].name;
                fileInfo.style.display = 'block';
                importBtn.disabled = false;
            }
        });
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
