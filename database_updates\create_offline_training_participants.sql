-- Create table for offline training participants
-- This table will store who is registered/allowed to attend each training

CREATE TABLE IF NOT EXISTS `offline_training_participants` (
  `id` int NOT NULL AUTO_INCREMENT,
  `offline_training_id` int NOT NULL,
  `karyawan_id` int NOT NULL,
  `nik` varchar(20) NOT NULL,
  `nama` varchar(100) NOT NULL,
  `jabatan` varchar(100) DEFAULT NULL,
  `departemen` varchar(100) DEFAULT NULL,
  `bagian` varchar(100) DEFAULT NULL,
  `status` enum('registered','confirmed','cancelled') NOT NULL DEFAULT 'registered',
  `registered_by` int DEFAULT NULL,
  `registered_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `notes` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_participant` (`offline_training_id`,`karyawan_id`),
  KEY `idx_training_id` (`offline_training_id`),
  KEY `idx_karyawan_id` (`karyawan_id`),
  KEY `idx_nik` (`nik`),
  CONSTRAINT `fk_offline_training_participants_training` 
    FOREIGN KEY (`offline_training_id`) REFERENCES `offline_training` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_offline_training_participants_karyawan` 
    FOREIGN KEY (`karyawan_id`) REFERENCES `karyawan` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_offline_training_participants_registered_by` 
    FOREIGN KEY (`registered_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Add index for better performance
CREATE INDEX `idx_training_status` ON `offline_training_participants` (`offline_training_id`, `status`);
CREATE INDEX `idx_search` ON `offline_training_participants` (`nama`, `nik`, `departemen`);
