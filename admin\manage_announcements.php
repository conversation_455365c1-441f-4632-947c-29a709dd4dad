<?php
/**
 * Manage Announcements Page for Admin
 * This page allows admins to create, edit, and delete announcements
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';
include '../includes/notification_helper.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}


// Create announcements table if it doesn't exist
$create_table_query = "CREATE TABLE IF NOT EXISTS announcements (
    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    image_path VARCHAR(500) NULL,
    link_url VARCHAR(500) NULL,
    link_text VARCHAR(255) NULL,
    created_by INT(11) NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
    active TINYINT(1) NOT NULL DEFAULT 1,
    target_role VARCHAR(50) NULL,
    expiry_date DATE NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if (!$conn->query($create_table_query)) {
    $error_message = "Error creating announcements table: " . $conn->error;
}

// Create announcement_recipients table if it doesn't exist
$create_recipients_table = "CREATE TABLE IF NOT EXISTS announcement_recipients (
    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    announcement_id INT(11) NOT NULL,
    user_id INT(11) NOT NULL,
    assigned_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_announcement_recipient (announcement_id, user_id),
    FOREIGN KEY (announcement_id) REFERENCES announcements(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if (!$conn->query($create_recipients_table)) {
    $error_message = "Error creating announcement_recipients table: " . $conn->error;
}

// Add new columns to existing announcements table if they don't exist
$columns_to_add = [
    'image_path' => 'VARCHAR(500) NULL',
    'link_url' => 'VARCHAR(500) NULL',
    'link_text' => 'VARCHAR(255) NULL'
];

foreach ($columns_to_add as $column_name => $column_definition) {
    $check_column_query = "SHOW COLUMNS FROM announcements LIKE '$column_name'";
    $result = $conn->query($check_column_query);

    if ($result->num_rows == 0) {
        $alter_query = "ALTER TABLE announcements ADD COLUMN $column_name $column_definition";
        if (!$conn->query($alter_query)) {
            $error_message = "Error adding column $column_name: " . $conn->error;
        }
    }
}

// Handle form submissions
$success_message = '';
$error_message = '';

// Handle delete action
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $delete_id = $_GET['delete'];
    $delete_query = "DELETE FROM announcements WHERE id = ?";
    $stmt = $conn->prepare($delete_query);
    $stmt->bind_param("i", $delete_id);

    if ($stmt->execute()) {
        $success_message = "Pengumuman berhasil dihapus.";
    } else {
        $error_message = "Gagal menghapus pengumuman: " . $conn->error;
    }
    $stmt->close();
}

// Handle toggle active status
if (isset($_GET['toggle']) && is_numeric($_GET['toggle'])) {
    $toggle_id = $_GET['toggle'];

    // Get current status
    $status_query = "SELECT active FROM announcements WHERE id = ?";
    $stmt = $conn->prepare($status_query);
    $stmt->bind_param("i", $toggle_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();

    if ($row) {
        $new_status = $row['active'] ? 0 : 1;
        $update_query = "UPDATE announcements SET active = ? WHERE id = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("ii", $new_status, $toggle_id);

        if ($stmt->execute()) {
            $status_text = $new_status ? "diaktifkan" : "dinonaktifkan";
            $success_message = "Pengumuman berhasil " . $status_text . ".";
        } else {
            $error_message = "Gagal mengubah status pengumuman: " . $conn->error;
        }
        $stmt->close();
    }
}

// Handle add/edit form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_announcement'])) {
    // Debug: Log all POST data
    error_log("=== FORM SUBMISSION DEBUG ===");
    error_log("Full POST data: " . print_r($_POST, true));
    error_log("recipient_type: " . (isset($_POST['recipient_type']) ? $_POST['recipient_type'] : 'NOT SET'));
    error_log("recipients: " . (isset($_POST['recipients']) ? print_r($_POST['recipients'], true) : 'NOT SET'));
    error_log("specific_user_id: " . (isset($_POST['specific_user_id']) ? $_POST['specific_user_id'] : 'NOT SET'));
    error_log("multiple_users: " . (isset($_POST['multiple_users']) ? print_r($_POST['multiple_users'], true) : 'NOT SET'));

    $title = trim($_POST['title']);
    $content = trim($_POST['content']);
    $active = isset($_POST['active']) ? 1 : 0;
    $target_role = !empty($_POST['target_role']) ? $_POST['target_role'] : NULL;
    $expiry_date = !empty($_POST['expiry_date']) ? $_POST['expiry_date'] : NULL;
    $link_url = !empty($_POST['link_url']) ? trim($_POST['link_url']) : NULL;
    $link_text = !empty($_POST['link_text']) ? trim($_POST['link_text']) : NULL;
    $user_id = $_SESSION['user_id'];

    // Handle image upload
    $image_path = NULL;
    $upload_error = '';

    if (isset($_FILES['announcement_image']) && $_FILES['announcement_image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../uploads/announcements/';

        // Create directory if it doesn't exist
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $file_info = pathinfo($_FILES['announcement_image']['name']);
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $file_extension = strtolower($file_info['extension']);

        if (in_array($file_extension, $allowed_extensions)) {
            $max_file_size = 5 * 1024 * 1024; // 5MB
            if ($_FILES['announcement_image']['size'] <= $max_file_size) {
                $new_filename = 'announcement_' . time() . '_' . uniqid() . '.' . $file_extension;
                $upload_path = $upload_dir . $new_filename;

                if (move_uploaded_file($_FILES['announcement_image']['tmp_name'], $upload_path)) {
                    $image_path = 'uploads/announcements/' . $new_filename;
                } else {
                    $upload_error = 'Gagal mengupload gambar.';
                }
            } else {
                $upload_error = 'Ukuran file terlalu besar. Maksimal 5MB.';
            }
        } else {
            $upload_error = 'Format file tidak didukung. Gunakan JPG, PNG, GIF, atau WebP.';
        }
    }

    // Validate input
    if (empty($title) || empty($content)) {
        $error_message = "Judul dan konten pengumuman harus diisi.";
    } elseif (!empty($upload_error)) {
        $error_message = $upload_error;
    } else {
        // Check if it's an edit or a new announcement
        if (isset($_POST['announcement_id']) && is_numeric($_POST['announcement_id'])) {
            // Update existing announcement
            $announcement_id = $_POST['announcement_id'];

            // Handle existing image for edit
            if ($image_path === NULL && isset($_POST['keep_existing_image']) && $_POST['keep_existing_image'] === '1') {
                // Keep existing image - get current image path
                $current_query = "SELECT image_path FROM announcements WHERE id = ?";
                $current_stmt = $conn->prepare($current_query);
                $current_stmt->bind_param("i", $announcement_id);
                $current_stmt->execute();
                $current_result = $current_stmt->get_result();
                if ($current_row = $current_result->fetch_assoc()) {
                    $image_path = $current_row['image_path'];
                }
                $current_stmt->close();
            }

            $update_query = "UPDATE announcements SET
                            title = ?,
                            content = ?,
                            image_path = ?,
                            link_url = ?,
                            link_text = ?,
                            active = ?,
                            target_role = ?,
                            expiry_date = ?
                            WHERE id = ?";
            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("sssssissi", $title, $content, $image_path, $link_url, $link_text, $active, $target_role, $expiry_date, $announcement_id);

            if ($stmt->execute()) {
                // Handle announcement recipients for update
                $recipient_count = handleAnnouncementRecipients($conn, $announcement_id, $_POST);

                // Create notifications for updated announcement
                if ($recipient_count > 0) {
                    $recipients_query = "SELECT user_id FROM announcement_recipients WHERE announcement_id = ?";
                    $stmt_recipients = $conn->prepare($recipients_query);
                    $stmt_recipients->bind_param("i", $announcement_id);
                    $stmt_recipients->execute();
                    $recipients_result = $stmt_recipients->get_result();

                    $recipient_ids = [];
                    while ($recipient_row = $recipients_result->fetch_assoc()) {
                        $recipient_ids[] = $recipient_row['user_id'];
                    }
                    $stmt_recipients->close();

                    // Create notifications
                    $notification_count = createAnnouncementNotifications($announcement_id, $recipient_ids, $title, $content);
                    error_log("Created $notification_count notifications for updated announcement ID $announcement_id");
                }

                $success_message = "Pengumuman berhasil diperbarui.";
            } else {
                $error_message = "Gagal memperbarui pengumuman: " . $conn->error;
            }
        } else {
            // Insert new announcement
            $insert_query = "INSERT INTO announcements (title, content, image_path, link_url, link_text, created_by, active, target_role, expiry_date)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("sssssiiss", $title, $content, $image_path, $link_url, $link_text, $user_id, $active, $target_role, $expiry_date);

            if ($stmt->execute()) {
                $announcement_id = $conn->insert_id;
                // Handle announcement recipients for new announcement
                $recipient_count = handleAnnouncementRecipients($conn, $announcement_id, $_POST);

                // Create notifications for new announcement
                if ($recipient_count > 0) {
                    $recipients_query = "SELECT user_id FROM announcement_recipients WHERE announcement_id = ?";
                    $stmt_recipients = $conn->prepare($recipients_query);
                    $stmt_recipients->bind_param("i", $announcement_id);
                    $stmt_recipients->execute();
                    $recipients_result = $stmt_recipients->get_result();

                    $recipient_ids = [];
                    while ($recipient_row = $recipients_result->fetch_assoc()) {
                        $recipient_ids[] = $recipient_row['user_id'];
                    }
                    $stmt_recipients->close();

                    // Create notifications
                    $notification_count = createAnnouncementNotifications($announcement_id, $recipient_ids, $title, $content);
                    error_log("Created $notification_count notifications for new announcement ID $announcement_id");
                }

                $success_message = "Pengumuman baru berhasil ditambahkan.";
            } else {
                $error_message = "Gagal menambahkan pengumuman: " . $conn->error;
            }
        }
        $stmt->close();
    }
}

// Function to handle announcement recipients
function handleAnnouncementRecipients($conn, $announcement_id, $post_data) {
    // Debug logging
    error_log("=== DEBUG handleAnnouncementRecipients ===");
    error_log("Announcement ID: " . $announcement_id);
    error_log("POST Data: " . print_r($post_data, true));

    // Delete existing recipients
    $delete_query = "DELETE FROM announcement_recipients WHERE announcement_id = ?";
    $stmt = $conn->prepare($delete_query);
    $stmt->bind_param("i", $announcement_id);
    $stmt->execute();
    $stmt->close();

    // Handle recipients (always specific recipients now)
    $selected_recipients = isset($post_data['recipients']) ? $post_data['recipients'] : [];

    error_log("Selected Recipients: " . print_r($selected_recipients, true));

    // Handle specific recipients from multiple sources
        // Handle specific recipients from multiple sources
        $recipients_to_insert = [];

        // 1. From hidden inputs (recipients[])
        if (!empty($selected_recipients)) {
            $recipients_to_insert = array_merge($recipients_to_insert, $selected_recipients);
            error_log("Found recipients from hidden inputs: " . print_r($selected_recipients, true));
        }

        // 2. From single user dropdown
        if (!empty($post_data['specific_user_id'])) {
            $recipients_to_insert[] = $post_data['specific_user_id'];
            error_log("Found recipient from single dropdown: " . $post_data['specific_user_id']);
        }

        // 3. From multiple users dropdown
        if (!empty($post_data['multiple_users'])) {
            $recipients_to_insert = array_merge($recipients_to_insert, $post_data['multiple_users']);
            error_log("Found recipients from multiple dropdown: " . print_r($post_data['multiple_users'], true));
        }

        // Remove duplicates and insert
        $recipients_to_insert = array_unique($recipients_to_insert);
        error_log("Final recipients to insert: " . print_r($recipients_to_insert, true));

        if (!empty($recipients_to_insert)) {
            $count = 0;
            foreach ($recipients_to_insert as $recipient_id) {
                if (!empty($recipient_id)) {
                    $insert_recipient_query = "INSERT IGNORE INTO announcement_recipients (announcement_id, user_id) VALUES (?, ?)";
                    $stmt_recipient = $conn->prepare($insert_recipient_query);
                    $stmt_recipient->bind_param("ii", $announcement_id, $recipient_id);
                    $stmt_recipient->execute();
                    $stmt_recipient->close();
                    $count++;
                }
            }
            error_log("Inserted $count recipients for 'specific' type");
        } else {
            error_log("No specific recipients found to insert");
            $count = 0;
        }

    error_log("=== END DEBUG ===");
    return $count;
}

// Get announcement for editing if ID is provided
$edit_announcement = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_id = $_GET['edit'];
    $edit_query = "SELECT * FROM announcements WHERE id = ?";
    $stmt = $conn->prepare($edit_query);
    $stmt->bind_param("i", $edit_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $edit_announcement = $result->fetch_assoc();
    $stmt->close();

    if (!$edit_announcement) {
        $error_message = "Pengumuman tidak ditemukan.";
    }
}

// Get existing recipients for editing and determine recipient type
$existing_recipients = [];
$current_recipient_type = 'all'; // default
$recipient_info = [];

if ($edit_announcement) {
    // Get recipients with user details
    $recipients_query = "SELECT ar.user_id, u.name, u.email, u.role_id, u.dept, u.bagian, u.jabatan, r.role_name
                        FROM announcement_recipients ar
                        JOIN users u ON ar.user_id = u.id
                        LEFT JOIN roles r ON u.role_id = r.id
                        WHERE ar.announcement_id = ?
                        ORDER BY u.name";
    $stmt = $conn->prepare($recipients_query);
    $stmt->bind_param("i", $edit_announcement['id']);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $existing_recipients[] = $row['user_id'];
        $recipient_info[] = $row;
    }
    $stmt->close();

    // Determine recipient type based on recipients
    if (!empty($existing_recipients)) {
        // Count total active users
        $total_users_query = "SELECT COUNT(*) as total FROM users WHERE is_active = 1";
        $total_result = $conn->query($total_users_query);
        $total_users = $total_result->fetch_assoc()['total'];

        if (count($existing_recipients) == $total_users) {
            // All users are recipients
            $current_recipient_type = 'all';
        } else {
            // Check if all recipients have the same role
            $roles = array_unique(array_column($recipient_info, 'role_id'));
            if (count($roles) == 1) {
                // Check if all users with this role are recipients
                $role_id = $roles[0];
                $role_users_query = "SELECT COUNT(*) as total FROM users WHERE is_active = 1 AND role_id = ?";
                $stmt = $conn->prepare($role_users_query);
                $stmt->bind_param("s", $role_id);
                $stmt->execute();
                $role_total = $stmt->get_result()->fetch_assoc()['total'];
                $stmt->close();

                if (count($existing_recipients) == $role_total) {
                    // All users with this role are recipients
                    $current_recipient_type = 'role';
                } else {
                    // Specific users
                    $current_recipient_type = 'specific';
                }
            } else {
                // Multiple roles or partial role coverage = specific users
                $current_recipient_type = 'specific';
            }
        }
    }
}

// Get all active users for recipient selection
$users = [];
$users_query = "SELECT u.id, u.name, u.email, u.dept, u.bagian, u.jabatan, u.role_id, r.role_name
               FROM users u
               LEFT JOIN roles r ON u.role_id = r.id
               WHERE u.is_active = 1
               ORDER BY u.name";
$result = $conn->query($users_query);

if ($result) {
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }
}

// Get all announcements
$announcements = [];
$query = "SELECT a.*, u.name as creator_name
          FROM announcements a
          LEFT JOIN users u ON a.created_by = u.id
          ORDER BY a.created_at DESC";
$result = $conn->query($query);

if ($result) {
    while ($row = $result->fetch_assoc()) {
        $announcements[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .announcement-card {
        margin-bottom: 20px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: box-shadow 0.3s ease;
    }

    .announcement-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .announcement-header {
        padding: 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .announcement-title {
        font-weight: 600;
        margin: 0;
        color: #333;
    }

    .announcement-meta {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .announcement-content {
        padding: 15px;
        background-color: #fff;
    }

    .announcement-footer {
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-top: 1px solid #e9ecef;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .status-badge {
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-active {
        background-color: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }

    .form-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .form-section h3 {
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .announcement-list {
        margin-top: 30px;
    }

    .btn-icon {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .target-role {
        display: inline-block;
        padding: 2px 6px;
        background-color: #e9ecef;
        border-radius: 4px;
        font-size: 0.75rem;
        margin-left: 5px;
    }

    .expiry-date {
        color: #dc3545;
        font-size: 0.85rem;
    }

    .expired {
        text-decoration: line-through;
        opacity: 0.7;
    }

    .users-list {
        background-color: #f8f9fa;
    }

    .user-info {
        padding: 5px 0;
    }

    .user-name {
        margin-bottom: 2px;
    }

    /* User selection card styling */
    #specific_recipients .card {
        border: 2px solid #007bff;
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.1);
    }

    #specific_recipients .card-header {
        background-color: #007bff !important;
        border-bottom: 1px solid #0056b3;
    }

    #specific_recipients .card-body {
        background-color: #f8f9fa;
        padding: 1.5rem;
    }

    /* User item styling */
    .user-item {
        background-color: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 8px;
        transition: all 0.2s ease;
    }

    .user-item:hover {
        background-color: #e3f2fd;
        border-color: #2196f3;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .user-item .form-check-input:checked ~ .form-check-label {
        background-color: #e8f5e9;
    }

    /* Search and filter styling */
    #user_search, #dept_filter, #level_filter {
        border: 2px solid #dee2e6;
        border-radius: 6px;
        padding: 8px 12px;
    }

    #user_search:focus, #role_filter:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Button styling */
    #select_all, #deselect_all {
        border-radius: 20px;
        padding: 6px 12px;
        font-size: 0.875rem;
    }

    /* Users container */
    #users_container {
        background-color: white;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        max-height: 400px;
        overflow-y: auto;
        padding: 15px;
    }

    /* Prevent alert auto-dismiss affecting user selection */
    #specific_recipients {
        position: relative;
        z-index: 10;
    }

    #specific_recipients .alert {
        position: relative;
        z-index: 1;
    }

    .user-details {
        line-height: 1.2;
    }

    .form-check-label {
        cursor: pointer;
        width: 100%;
    }

    .form-check-input:checked + .form-check-label .user-info {
        background-color: #e3f2fd;
        border-radius: 4px;
        padding: 8px;
        margin: -3px;
    }

    .user-item {
        transition: all 0.2s ease;
    }

    .user-item.hidden {
        display: none;
    }

    .search-interface {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 15px;
    }

    .users-list-container {
        background-color: #ffffff;
        min-height: 200px;
    }

    #search_users {
        border-radius: 6px 0 0 6px;
    }

    #clear_search {
        border-radius: 0 6px 6px 0;
    }

    .input-group-text {
        background-color: #e9ecef;
        border-color: #ced4da;
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    #search_info {
        font-size: 0.875rem;
    }

    .user-item:hover {
        background-color: #f8f9fa;
        border-radius: 4px;
        padding: 5px;
        margin: -5px;
    }

    .btn-group .btn {
        border-radius: 4px;
        margin: 0 2px;
    }

    .pagination-info {
        font-size: 0.875rem;
        color: #6c757d;
    }

    /* Simple User-Friendly Styles */
    .simple-options .form-check {
        transition: all 0.2s ease;
    }

    .simple-options .form-check:hover {
        background-color: #f8f9fa;
    }

    .simple-options .form-check input:checked + label {
        background-color: #e3f2fd;
        border-color: #2196f3 !important;
    }

    .form-select-lg {
        font-size: 1rem;
        padding: 12px 16px;
    }

    kbd {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 3px;
        padding: 2px 6px;
        font-size: 0.8rem;
        color: #495057;
    }

    #role_selection, #specific_recipients {
        margin-top: 1rem;
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="form-container">
            <div class="welcome-section">
            <h1><i class="fas fa-bullhorn"></i> Kelola Pengumuman</h1>
            <p class="text-white">Buat dan kelola pengumuman untuk pengguna sistem</p>
            </div>
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="form-section">
                <h3><?= $edit_announcement ? 'Edit Pengumuman' : 'Tambah Pengumuman Baru' ?></h3>

                <form method="post" action="" enctype="multipart/form-data">
                    <?php if ($edit_announcement): ?>
                        <input type="hidden" name="announcement_id" value="<?= $edit_announcement['id'] ?>">
                    <?php endif; ?>

                    <div class="mb-3">
                        <label for="title" class="form-label">Judul Pengumuman</label>
                        <input type="text" class="form-control" id="title" name="title" required
                               value="<?= $edit_announcement ? htmlspecialchars($edit_announcement['title']) : '' ?>">
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">Isi Pengumuman</label>
                        <textarea class="form-control" id="content" name="content" rows="5" required><?= $edit_announcement ? htmlspecialchars($edit_announcement['content']) : '' ?></textarea>
                    </div>

                    <!-- Image Upload Section -->
                    <div class="mb-3">
                        <label for="announcement_image" class="form-label">Gambar Pengumuman (Opsional)</label>
                        <?php if ($edit_announcement && !empty($edit_announcement['image_path'])): ?>
                            <div class="current-image mb-2">
                                <img src="../<?= htmlspecialchars($edit_announcement['image_path']) ?>"
                                     alt="Current Image"
                                     style="max-width: 200px; max-height: 150px; object-fit: cover; border-radius: 8px;">
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="checkbox" id="keep_existing_image" name="keep_existing_image" value="1" checked>
                                    <label class="form-check-label" for="keep_existing_image">
                                        Pertahankan gambar yang ada
                                    </label>
                                </div>
                            </div>
                        <?php endif; ?>
                        <input type="file" class="form-control" id="announcement_image" name="announcement_image"
                               accept="image/jpeg,image/jpg,image/png,image/gif,image/webp">
                        <div class="form-text">Format yang didukung: JPG, PNG, GIF, WebP. Maksimal 5MB.</div>
                    </div>

                    <!-- Link Section -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="link_url" class="form-label">URL Link (Opsional)</label>
                                <input type="url" class="form-control" id="link_url" name="link_url"
                                       placeholder="https://example.com"
                                       value="<?= $edit_announcement && $edit_announcement['link_url'] ? htmlspecialchars($edit_announcement['link_url']) : '' ?>">
                                <div class="form-text">Link yang akan ditampilkan di pengumuman</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="link_text" class="form-label">Teks Link</label>
                                <input type="text" class="form-control" id="link_text" name="link_text"
                                       placeholder="Baca Selengkapnya"
                                       value="<?= $edit_announcement && $edit_announcement['link_text'] ? htmlspecialchars($edit_announcement['link_text']) : '' ?>">
                                <div class="form-text">Teks yang akan ditampilkan untuk link</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="expiry_date" class="form-label">Tanggal Kedaluwarsa (Opsional)</label>
                                <input type="date" class="form-control" id="expiry_date" name="expiry_date"
                                       value="<?= $edit_announcement && $edit_announcement['expiry_date'] ? $edit_announcement['expiry_date'] : '' ?>">
                                <div class="form-text">Pengumuman akan otomatis dinonaktifkan setelah tanggal ini</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="active" name="active"
                               <?= (!$edit_announcement || ($edit_announcement && $edit_announcement['active'])) ? 'checked' : '' ?>>
                        <label class="form-check-label" for="active">Aktif</label>
                    </div>
                </div>

                <div class="form-section">
                    <h3><i class="fas fa-paper-plane"></i> Kirim Pengumuman Ke:</h3>

                    <!-- Direct to Specific Users Selection -->
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Pilih Penerima Pengumuman</strong>
                        <p class="mb-0">Gunakan fitur pencarian dan filter di bawah untuk memilih penerima pengumuman secara spesifik. Anda dapat memilih berdasarkan nama, departemen, level, atau jabatan.</p>
                    </div>



                    <!-- Specific Recipients Section -->
                    <div id="specific_recipients">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-user-check"></i> Pilih Pengguna Tertentu</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recipient_info)): ?>
                                    <div class="alert alert-info mb-3">
                                        <strong>Penerima saat ini (<?= count($recipient_info) ?> orang):</strong>
                                        <div class="mt-2">
                                            <?php foreach ($recipient_info as $recipient): ?>
                                                <span class="badge bg-success me-1 mb-1">
                                                    <i class="fas fa-user"></i> <?= htmlspecialchars($recipient['name']) ?>
                                                </span>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                            <!-- Search Box -->
                            <div class="mb-3">
                                <label class="form-label"><strong>Cari Pengguna:</strong></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="user_search" placeholder="Ketik nama, email, atau departemen...">
                                    <button class="btn btn-outline-secondary" type="button" id="clear_search">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Filter by Department -->
                            <div class="mb-3">
                                <label class="form-label"><strong>Filter berdasarkan Departemen:</strong></label>
                                <select class="form-select" id="dept_filter">
                                    <option value="">Semua Departemen</option>
                                    <?php
                                    // Get unique departments from karyawan table
                                    $dept_query = "SELECT DISTINCT k.dept FROM karyawan k
                                                  INNER JOIN users u ON k.nik = u.nik
                                                  WHERE u.is_active = 1 AND k.dept IS NOT NULL AND k.dept != ''
                                                  ORDER BY k.dept";
                                    $dept_result = $conn->query($dept_query);
                                    while ($dept_row = $dept_result->fetch_assoc()) {
                                        echo '<option value="' . htmlspecialchars($dept_row['dept']) . '">' . htmlspecialchars($dept_row['dept']) . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>

                            <!-- Filter by Level Karyawan -->
                            <div class="mb-3">
                                <label class="form-label"><strong>Filter berdasarkan Level Karyawan:</strong></label>
                                <select class="form-select" id="level_filter">
                                    <option value="">Semua Level</option>
                                    <?php
                                    // Get unique levels from karyawan table
                                    $level_query = "SELECT DISTINCT k.level_karyawan FROM karyawan k
                                                   INNER JOIN users u ON k.nik = u.nik
                                                   WHERE u.is_active = 1 AND k.level_karyawan IS NOT NULL
                                                   ORDER BY k.level_karyawan";
                                    $level_result = $conn->query($level_query);
                                    while ($level_row = $level_result->fetch_assoc()) {
                                        $level = $level_row['level_karyawan'];
                                        echo '<option value="' . $level . '">Level ' . $level . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>

                            <!-- Users List with Checkboxes -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label class="form-label mb-0"><strong>Pilih Pengguna:</strong></label>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-outline-primary" id="select_all">
                                            <i class="fas fa-check-square"></i> Pilih Semua
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="deselect_all">
                                            <i class="fas fa-square"></i> Batal Semua
                                        </button>
                                    </div>
                                </div>

                                <div class="border rounded p-3" style="max-height: 400px; overflow-y: auto;" id="users_container">
                                    <?php
                                    // Get all active users with karyawan data for checkboxes
                                    $users_checkbox_query = "SELECT u.id, u.name, u.email, u.role_id,
                                                                   COALESCE(k.dept, u.dept) as dept,
                                                                   COALESCE(k.bagian, u.bagian) as bagian,
                                                                   k.level_karyawan, k.jabatan,
                                                                   r.role_name
                                                           FROM users u
                                                           LEFT JOIN roles r ON u.role_id = r.id
                                                           LEFT JOIN karyawan k ON u.nik = k.nik
                                                           WHERE u.is_active = 1
                                                           ORDER BY u.name";
                                    $users_checkbox_result = $conn->query($users_checkbox_query);

                                    while ($user = $users_checkbox_result->fetch_assoc()) {
                                        $role_names = [
                                            '1' => 'Pemohon', '2' => 'Dept Head', '3' => 'HRD',
                                            '4' => 'GA', '5' => 'Factory Manager', '6' => 'Direktur', '99' => 'Admin'
                                        ];
                                        $role_label = $role_names[$user['role_id']] ?? ($user['role_name'] ?? 'Role ' . $user['role_id']);
                                        $is_selected = in_array($user['id'], $existing_recipients);

                                        $user_info = htmlspecialchars($user['name']);
                                        if (!empty($user['dept'])) {
                                            $user_info .= ' - ' . htmlspecialchars($user['dept']);
                                            if (!empty($user['bagian'])) {
                                                $user_info .= ' (' . htmlspecialchars($user['bagian']) . ')';
                                            }
                                        }
                                        $user_info .= ' - ' . $role_label;

                                        // Create search data including dept and level
                                        $search_data = strtolower($user['name'] . ' ' . $user['email'] . ' ' . $user['dept'] . ' ' . $user['bagian'] . ' ' . $role_label . ' ' . $user['jabatan']);

                                        echo '<div class="form-check user-item"
                                                data-role="' . $user['role_id'] . '"
                                                data-dept="' . htmlspecialchars($user['dept']) . '"
                                                data-level="' . ($user['level_karyawan'] ?? '') . '"
                                                data-search="' . $search_data . '">';
                                        echo '<input class="form-check-input user-checkbox" type="checkbox" name="multiple_users[]" value="' . $user['id'] . '" id="user_' . $user['id'] . '"' . ($is_selected ? ' checked' : '') . '>';
                                        echo '<label class="form-check-label w-100" for="user_' . $user['id'] . '">';
                                        echo '<div class="d-flex justify-content-between align-items-center">';
                                        echo '<div>';
                                        echo '<strong>' . htmlspecialchars($user['name']) . '</strong><br>';
                                        echo '<small class="text-muted">' . htmlspecialchars($user['email']) . '</small>';
                                        echo '</div>';
                                        echo '<div class="text-end">';
                                        echo '<span class="badge bg-secondary">' . $role_label . '</span>';
                                        if (!empty($user['level_karyawan'])) {
                                            echo '<br><span class="badge bg-info">Level ' . $user['level_karyawan'] . '</span>';
                                        }
                                        if (!empty($user['dept'])) {
                                            echo '<br><small class="text-muted">' . htmlspecialchars($user['dept']);
                                            if (!empty($user['bagian'])) {
                                                echo ' - ' . htmlspecialchars($user['bagian']);
                                            }
                                            echo '</small>';
                                        }
                                        if (!empty($user['jabatan'])) {
                                            echo '<br><small class="text-primary">' . htmlspecialchars($user['jabatan']) . '</small>';
                                        }
                                        echo '</div>';
                                        echo '</div>';
                                        echo '</label>';
                                        echo '</div>';
                                    }
                                    ?>
                                </div>

                                <div class="mt-2 d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        <span id="selected_count">0</span> pengguna dipilih
                                    </small>
                                    <small class="text-muted" id="filter_info">
                                        <i class="fas fa-filter"></i>
                                        <span id="visible_count">0</span> ditampilkan
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden inputs for selected recipients -->
                    <div id="hidden_recipients"></div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" name="save_announcement" class="btn btn-primary">
                            <i class="fas fa-save"></i> <?= $edit_announcement ? 'Perbarui Pengumuman' : 'Simpan Pengumuman' ?>
                        </button>

                        <?php if ($edit_announcement): ?>
                            <a href="manage_announcements.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Batal Edit
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>

            <div class="announcement-list">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3>Daftar Pengumuman</h3>
                    <!-- <div>
                        <a href="migrate_announcements.php" class="btn btn-warning btn-sm">
                            <i class="fas fa-database"></i> Migrate Data
                        </a>
                        <a href="test_specific_announcement.php" class="btn btn-success btn-sm">
                            <i class="fas fa-vial"></i> Test
                        </a>
                        <a href="debug_announcements.php" class="btn btn-info btn-sm">
                            <i class="fas fa-bug"></i> Debug
                        </a>
                    </div> -->
                </div>

                <?php if (empty($announcements)): ?>
                    <div class="alert alert-info">
                        Belum ada pengumuman yang dibuat. Silakan buat pengumuman baru.
                    </div>
                <?php else: ?>
                    <?php foreach ($announcements as $announcement): ?>
                        <?php
                            $is_expired = false;
                            if (!empty($announcement['expiry_date'])) {
                                $expiry_date = new DateTime($announcement['expiry_date']);
                                $today = new DateTime();
                                $is_expired = $today > $expiry_date;
                            }
                        ?>
                        <div class="announcement-card <?= $is_expired ? 'expired' : '' ?>">
                            <div class="announcement-header">
                                <div>
                                    <h5 class="announcement-title">
                                        <?= htmlspecialchars($announcement['title']) ?>
                                        <?php if (!empty($announcement['target_role'])): ?>
                                            <span class="target-role">
                                                <?php
                                                    $role_names = [
                                                        '1' => 'Pemohon',
                                                        '2' => 'Dept Head',
                                                        '3' => 'HRD',
                                                        '4' => 'GA',
                                                        '5' => 'Factory Manager',
                                                        '6' => 'Direktur'
                                                    ];
                                                    echo isset($role_names[$announcement['target_role']]) ? $role_names[$announcement['target_role']] : 'Role ' . $announcement['target_role'];
                                                ?>
                                            </span>
                                        <?php endif; ?>
                                    </h5>
                                    <div class="announcement-meta">
                                        <span><i class="fas fa-user"></i> <?= htmlspecialchars($announcement['creator_name']) ?></span>
                                        <span class="ms-2"><i class="fas fa-calendar"></i> <?= date('d M Y H:i', strtotime($announcement['created_at'])) ?></span>
                                        <?php if (!empty($announcement['expiry_date'])): ?>
                                            <span class="ms-2 expiry-date"><i class="fas fa-clock"></i> Kedaluwarsa: <?= date('d M Y', strtotime($announcement['expiry_date'])) ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div>
                                    <span class="status-badge <?= $announcement['active'] ? 'status-active' : 'status-inactive' ?>">
                                        <?= $announcement['active'] ? 'Aktif' : 'Nonaktif' ?>
                                    </span>
                                </div>
                            </div>
                            <div class="announcement-content">
                                <?= nl2br(htmlspecialchars($announcement['content'])) ?>

                                <!-- Display Image if exists -->
                                <?php if (!empty($announcement['image_path'])): ?>
                                    <div class="announcement-image mt-3">
                                        <img src="../<?= htmlspecialchars($announcement['image_path']) ?>"
                                             alt="Announcement Image"
                                             style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    </div>
                                <?php endif; ?>

                                <!-- Display Link if exists -->
                                <?php if (!empty($announcement['link_url'])): ?>
                                    <div class="announcement-link mt-3">
                                        <a href="<?= htmlspecialchars($announcement['link_url']) ?>"
                                           target="_blank"
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-external-link-alt"></i>
                                            <?= !empty($announcement['link_text']) ? htmlspecialchars($announcement['link_text']) : 'Baca Selengkapnya' ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="announcement-footer">
                                <a href="manage_announcements.php?toggle=<?= $announcement['id'] ?>" class="btn btn-sm <?= $announcement['active'] ? 'btn-warning' : 'btn-success' ?> btn-icon">
                                    <i class="fas <?= $announcement['active'] ? 'fa-eye-slash' : 'fa-eye' ?>"></i> <?= $announcement['active'] ? 'Nonaktifkan' : 'Aktifkan' ?>
                                </a>
                                <a href="manage_announcements.php?edit=<?= $announcement['id'] ?>" class="btn btn-sm btn-primary btn-icon">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a href="manage_announcements.php?delete=<?= $announcement['id'] ?>" class="btn btn-sm btn-danger btn-icon" onclick="return confirma    ction('Apakah Anda yakin ingin menghapus pengumuman ini?')">
                                    <i class="fas fa-trash"></i> Hapus
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
                                        </div>
<?php include '../config/footer.php'; ?>

<script>
    // Simplified JavaScript for user selection
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds (but not in user selection area)
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                // Skip alerts inside specific_recipients section
                if (alert.closest('#specific_recipients')) {
                    return;
                }

                if (alert.classList.contains('alert-success') || alert.classList.contains('alert-danger')) {
                    try {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    } catch (e) {
                        console.log('Alert already dismissed or not dismissible');
                    }
                }
            });
        }, 5000);

        // Specific recipients section (always visible now)
        const specificRecipientsDiv = document.getElementById('specific_recipients');

        // Simple user selection elements with debugging
        const userSearch = document.getElementById('user_search');
        const clearSearchBtn = document.getElementById('clear_search');
        const deptFilter = document.getElementById('dept_filter');
        const levelFilter = document.getElementById('level_filter');
        const selectAllBtn = document.getElementById('select_all');
        const deselectAllBtn = document.getElementById('deselect_all');
        const selectedCountSpan = document.getElementById('selected_count');
        const usersContainer = document.getElementById('users_container');



        // Function to update selected count
        function updateSelectedCount() {
            const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
            if (selectedCountSpan) {
                selectedCountSpan.textContent = checkedBoxes.length;
            }
        }

        // Function to show/hide users based on search and filters
        function filterUsers() {
            const searchTerm = userSearch ? userSearch.value.toLowerCase() : '';
            const selectedDept = deptFilter ? deptFilter.value : '';
            const selectedLevel = levelFilter ? levelFilter.value : '';
            const userItems = document.querySelectorAll('.user-item');

            let visibleCount = 0;

            userItems.forEach(item => {
                const searchData = item.getAttribute('data-search') || '';
                const userDept = item.getAttribute('data-dept') || '';
                const userLevel = item.getAttribute('data-level') || '';

                const matchesSearch = searchTerm === '' || searchData.includes(searchTerm);
                const matchesDept = selectedDept === '' || userDept === selectedDept;
                const matchesLevel = selectedLevel === '' || userLevel === selectedLevel;

                if (matchesSearch && matchesDept && matchesLevel) {
                    item.style.display = 'block';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });

            // Update visible count info
            const visibleCountSpan = document.getElementById('visible_count');
            if (visibleCountSpan) {
                visibleCountSpan.textContent = visibleCount;
            }
            console.log(`Filtered users: ${visibleCount} visible out of ${userItems.length} total`);
        }

        // Simple recipient type toggle with debugging
        function toggleRecipientsOptions() {
            console.log('toggleRecipientsOptions called');

            // Hide all sections first
            if (roleSelectionDiv) {
                roleSelectionDiv.style.display = 'none';
                console.log('Hidden role selection div');
            }
            if (specificRecipientsDiv) {
                specificRecipientsDiv.style.display = 'none';
                console.log('Hidden specific recipients div');
            }

            if (sendToAllRadio && sendToAllRadio.checked) {
                console.log('Send to all selected');
                // Show nothing additional - just send to all
            } else if (sendToRoleRadio && sendToRoleRadio.checked) {
                console.log('Send to role selected');
                // Show role selection
                if (roleSelectionDiv) {
                    roleSelectionDiv.style.display = 'block';
                    console.log('Showing role selection div');
                }
            } else if (sendToSpecificRadio && sendToSpecificRadio.checked) {
                console.log('Send to specific selected');
                // Show specific recipients selection
                if (specificRecipientsDiv) {
                    specificRecipientsDiv.style.display = 'block';
                    console.log('Showing specific recipients div');
                    updateSelectedCount(); // Update count when showing specific recipients
                } else {
                    console.error('specificRecipientsDiv not found!');
                }
            }
        }

        // No need for radio button setup since we only have specific recipients now

        // Simple search functionality
        if (userSearch) {
            userSearch.addEventListener('input', function() {
                filterUsers();
            });
        }

        // Clear search button
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', function() {
                userSearch.value = '';
                filterUsers();
            });
        }

        // Department filter
        if (deptFilter) {
            deptFilter.addEventListener('change', function() {
                console.log('Department filter changed to:', this.value);
                filterUsers();
            });
        }

        // Level filter
        if (levelFilter) {
            levelFilter.addEventListener('change', function() {
                console.log('Level filter changed to:', this.value);
                filterUsers();
            });
        }

        // Select all visible users
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', function() {
                const visibleCheckboxes = document.querySelectorAll('.user-item:not([style*="display: none"]) .user-checkbox');
                visibleCheckboxes.forEach(checkbox => {
                    checkbox.checked = true;
                });
                updateSelectedCount();
            });
        }

        // Deselect all users
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', function() {
                const userCheckboxes = document.querySelectorAll('.user-checkbox');
                userCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                updateSelectedCount();
            });
        }

        // Add event listeners to checkboxes for count update
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('user-checkbox')) {
                updateSelectedCount();
            }
        });

        // Simple form validation - only check if users are selected
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                const checkedUsers = document.querySelectorAll('.user-checkbox:checked');
                if (checkedUsers.length === 0) {
                    e.preventDefault();
                    alert('Silakan pilih minimal satu pengguna untuk menerima pengumuman ini.');
                    return false;
                }
            });
        }

        // Initialize counts on page load
        updateSelectedCount();
        filterUsers(); // Initialize visible count
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
