<?php
// File: admin/export_offline_attendance.php
// Deskripsi: Script untuk mengekspor data absensi Training Internal ke Excel

// Aktifkan output buffering
ob_start();

include '../config/config.php';
include 'security.php';

// Cek apakah ID training ada
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: training_management.php?training_type=internal");
    exit();
}

$training_id = $_GET['id'];

// Ambil data training
$query_training = "SELECT * FROM offline_training WHERE id = ?";
$stmt = $conn->prepare($query_training);
$stmt->bind_param("i", $training_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: training_management.php?training_type=internal");
    exit();
}

$training = $result->fetch_assoc();
$stmt->close();

// Ambil data absensi
$query_att = "
    SELECT ota.*, k.dept, k.bagian, k.jabatan
    FROM offline_training_attendance ota
    LEFT JOIN karyawan k ON ota.karyawan_id = k.id
    WHERE ota.offline_training_id = ?
    ORDER BY ota.nama ASC
";
$stmt = $conn->prepare($query_att);
$stmt->bind_param("i", $training_id);
$stmt->execute();
$result_att = $stmt->get_result();
$stmt->close();

// Load Composer autoload
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// Buat spreadsheet
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('Absensi Offline');

// Judul utama
$sheet->setCellValue('A1', 'DATA ABSENSI Training Internal');
$sheet->mergeCells('A1:J1');
$sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
$sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

// Info training
$sheet->setCellValue('A3', 'Topik Training:');
$sheet->setCellValue('B3', $training['training_topic']);
$sheet->mergeCells('B3:J3');

$sheet->setCellValue('A4', 'Tanggal:');
$sheet->setCellValue('B4', date('d-m-Y', strtotime($training['training_date'])));
$sheet->mergeCells('B4:J4');

$sheet->setCellValue('A5', 'Waktu:');
$sheet->setCellValue('B5', 
    ($training['training_time_start'] ? date('H:i', strtotime($training['training_time_start'])) : '-') .
    ' s/d ' .
    ($training['training_time_end'] ? date('H:i', strtotime($training['training_time_end'])) : '-')
);
$sheet->mergeCells('B5:J5');

$sheet->setCellValue('A6', 'Lokasi:');
$sheet->setCellValue('B6', $training['location']);
$sheet->mergeCells('B6:J6');

$sheet->setCellValue('A7', 'Trainer:');
$sheet->setCellValue('B7', $training['trainer_name']);
$sheet->mergeCells('B7:J7');

$sheet->setCellValue('A8', 'Status:');
$sheet->setCellValue('B8', ucfirst($training['status']));
$sheet->mergeCells('B8:J8');

// Header tabel
$headers = ['No','NIK','Nama','Departemen','Bagian','Jabatan','Check In','Check Out','Status','Keterangan'];
$col = 'A';
foreach ($headers as $h) {
    $sheet->setCellValue($col.'10', $h);
    $col++;
}

// Styling header tabel
$headerStyle = [
    'font' => ['bold' => true],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E2EFDA']],
];
$sheet->getStyle("A10:J10")->applyFromArray($headerStyle);

// Isi data tabel
$row = 11;
$no = 1;
while ($r = $result_att->fetch_assoc()) {
    $sheet->setCellValue("A{$row}", $no++);
    $sheet->setCellValue("B{$row}", $r['nik']);
    $sheet->setCellValue("C{$row}", $r['nama']);
    $sheet->setCellValue("D{$row}", $r['dept'] ?? '-');
    $sheet->setCellValue("E{$row}", $r['bagian'] ?? '-');
    $sheet->setCellValue("F{$row}", $r['jabatan'] ?? '-');
    $sheet->setCellValue("G{$row}", $r['check_in'] ? date('d-m-Y H:i:s', strtotime($r['check_in'])) : '-');
    $sheet->setCellValue("H{$row}", $r['check_out'] ? date('d-m-Y H:i:s', strtotime($r['check_out'])) : '-');
    $sheet->setCellValue("I{$row}", ucfirst($r['status']));
    $sheet->setCellValue("J{$row}", $r['keterangan'] ?? '-');
    $row++;
}

// Styling data
$sheet->getStyle("A11:J".($row-1))
      ->getBorders()->getAllBorders()
      ->setBorderStyle(Border::BORDER_THIN);

// Auto width kolom
foreach (range('A','J') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// Tanda tangan
$row += 2;
$sheet->setCellValue("I{$row}", 'Mengetahui,');
$sheet->mergeCells("I{$row}:J{$row}");
$sheet->getStyle("I{$row}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

$row += 5;
$sheet->setCellValue("I{$row}", '(____________________)');
$sheet->mergeCells("I{$row}:J{$row}");
$sheet->getStyle("I{$row}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

$row++;
$sheet->setCellValue("I{$row}", 'HRGA Manager');
$sheet->mergeCells("I{$row}:J{$row}");
$sheet->getStyle("I{$row}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

// Bersihkan buffer, kirim header download
ob_clean();
$filename = 'Absensi_Offline_'.preg_replace('/[^a-zA-Z0-9]/','_',$training['training_topic']).'_'.date('Y-m-d').'.xlsx';
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header("Content-Disposition: attachment;filename=\"{$filename}\"");
header('Cache-Control: max-age=0');

$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit;
