<?php
/**
 * Helper functions for managing role permissions
 */

/**
 * Check if a role has a specific permission
 * 
 * @param string $role Role name (instructor, assistant, student)
 * @param string $permission Permission name
 * @return bool True if the role has the permission
 */
function roleHasPermission($role, $permission) {
    global $conn;
    
    $query = "SELECT COUNT(*) as count FROM training_role_permissions 
              WHERE role = ? AND permission = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $role, $permission);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    return $row['count'] > 0;
}

/**
 * Check if a user has a specific permission in a class
 * 
 * @param int $user_id User ID
 * @param int $class_id Class ID
 * @param string $permission Permission name
 * @return bool True if the user has the permission
 */
function userHasPermission($user_id, $class_id, $permission) {
    $role = getUserRoleInClass($user_id, $class_id);
    
    if ($role === null) {
        return false;
    }
    
    return roleHasPermission($role, $permission);
}

/**
 * Get all permissions for a role
 * 
 * @param string $role Role name
 * @return array Array of permission names
 */
function getRolePermissions($role) {
    global $conn;
    
    $query = "SELECT permission FROM training_role_permissions WHERE role = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $role);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $permissions = [];
    while ($row = $result->fetch_assoc()) {
        $permissions[] = $row['permission'];
    }
    
    return $permissions;
}

/**
 * Add a permission to a role
 * 
 * @param string $role Role name
 * @param string $permission Permission name
 * @return bool True if the permission was added successfully
 */
function addRolePermission($role, $permission) {
    global $conn;
    
    $query = "INSERT IGNORE INTO training_role_permissions (role, permission) VALUES (?, ?)";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $role, $permission);
    
    return $stmt->execute();
}

/**
 * Remove a permission from a role
 * 
 * @param string $role Role name
 * @param string $permission Permission name
 * @return bool True if the permission was removed successfully
 */
function removeRolePermission($role, $permission) {
    global $conn;
    
    $query = "DELETE FROM training_role_permissions WHERE role = ? AND permission = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $role, $permission);
    
    return $stmt->execute();
}

/**
 * Get all available permissions
 * 
 * @return array Array of all permission names
 */
function getAllPermissions() {
    global $conn;
    
    $query = "SELECT DISTINCT permission FROM training_role_permissions ORDER BY permission";
    $result = $conn->query($query);
    
    $permissions = [];
    while ($row = $result->fetch_assoc()) {
        $permissions[] = $row['permission'];
    }
    
    return $permissions;
}

/**
 * Check permission and redirect if not allowed
 * 
 * @param int $user_id User ID
 * @param int $class_id Class ID
 * @param string $permission Permission name
 * @param string $redirect_url URL to redirect to if not allowed
 */
function requirePermission($user_id, $class_id, $permission, $redirect_url = '../pemohon/classroom.php') {
    if (!userHasPermission($user_id, $class_id, $permission)) {
        $_SESSION['error'] = "Anda tidak memiliki izin untuk melakukan tindakan ini.";
        header("Location: $redirect_url");
        exit();
    }
}
