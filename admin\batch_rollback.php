<?php
/**
 * Batch Rollback Functionality
 * This file handles rolling back batch operations on employee data
 */

session_start();
require_once '../config/config.php';
require_once 'record_batch_employee_history.php';
require_once 'enhanced_batch_history.php';
require_once 'fallback_rollback_handler.php';

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['batch_id'])) {
    $batch_id = intval($_POST['batch_id']);
    
    try {
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Get batch history details
        $batch_details = getBatchHistoryDetails($batch_id);
        
        if (!$batch_details) {
            throw new Exception("Batch history not found");
        }
        
        $batch_data = $batch_details['batch_data'];
        $action_type = $batch_details['action_type'];

        // Extract base action type (remove _ROLLED_BACK suffix if present)
        $base_action_type = str_replace('_ROLLED_BACK', '', $action_type);

        // Check if this is an enhanced rollback operation
        if ($base_action_type === 'BATCH_UPDATE' && !strpos($action_type, '_ROLLED_BACK') && supportsEnhancedRollback($batch_data, $action_type)) {
            // Use enhanced rollback for BATCH_UPDATE (first time)
            $rollback_result = rollbackBatchUpdate($pdo, $batch_id);

            if ($rollback_result['success']) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Enhanced rollback completed successfully',
                    'results' => $rollback_result['results'],
                    'rollback_batch_id' => $rollback_result['batch_id']
                ]);
                exit();
            } else {
                // Try fallback rollback if enhanced rollback fails
                $fallback_result = handleFallbackRollback($pdo, $batch_id, $action_type, $batch_data);

                if ($fallback_result['success']) {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'message' => 'Fallback rollback completed successfully',
                        'results' => $fallback_result['results'],
                        'rollback_batch_id' => $fallback_result['batch_id']
                    ]);
                    exit();
                } else {
                    throw new Exception("Enhanced rollback failed: " . $rollback_result['message'] . ". Fallback rollback also failed: " . $fallback_result['message']);
                }
            }
        } elseif ($action_type === 'BATCH_UPDATE_ROLLED_BACK' && supportsEnhancedRollback($batch_data, $action_type)) {
            // Use rollback again for BATCH_UPDATE_ROLLED_BACK
            $rollback_result = rollbackRolledBackBatch($pdo, $batch_id);

            if ($rollback_result['success']) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Rollback again completed successfully',
                    'results' => $rollback_result['results'],
                    'rollback_batch_id' => $rollback_result['batch_id']
                ]);
                exit();
            } else {
                // Try fallback rollback if enhanced rollback fails
                $fallback_result = handleFallbackRollback($pdo, $batch_id, $action_type, $batch_data);

                if ($fallback_result['success']) {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'message' => 'Fallback rollback again completed successfully',
                        'results' => $fallback_result['results'],
                        'rollback_batch_id' => $fallback_result['batch_id']
                    ]);
                    exit();
                } else {
                    throw new Exception("Enhanced rollback failed: " . $rollback_result['message'] . ". Fallback rollback also failed: " . $fallback_result['message']);
                }
            }
        } elseif ($action_type === 'BATCH_ROLLBACK' && supportsEnhancedRollback($batch_data, $action_type)) {
            // Undo rollback (rollback the rollback)
            $undo_result = rollbackBatchRollback($pdo, $batch_id);

            if ($undo_result['success']) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Rollback undone successfully',
                    'results' => $undo_result['results'],
                    'rollback_batch_id' => $undo_result['batch_id']
                ]);
                exit();
            } else {
                // Try fallback rollback if enhanced undo fails
                $fallback_result = handleFallbackRollback($pdo, $batch_id, $action_type, $batch_data);

                if ($fallback_result['success']) {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'message' => 'Fallback undo rollback completed successfully',
                        'results' => $fallback_result['results'],
                        'rollback_batch_id' => $fallback_result['batch_id']
                    ]);
                    exit();
                } else {
                    throw new Exception("Enhanced undo failed: " . $undo_result['message'] . ". Fallback undo also failed: " . $fallback_result['message']);
                }
            }
        }

        // Try standard rollback first
        try {
            // Start transaction for standard rollback
            $pdo->beginTransaction();

            $rollback_results = performRollback($pdo, $action_type, $batch_data);

            // Record rollback operation
            $rollback_batch_id = recordBatchEmployeeHistory(
                $pdo,
                'BATCH_ROLLBACK',
                $batch_data['niks'] ?? [],
                'rollback',
                $rollback_results['success_count'],
                $rollback_results['error_count'],
                $rollback_results['skipped_count']
            );

            // Mark original batch as rolled back
            $stmt = $pdo->prepare("UPDATE karyawan_batch_history SET action_type = CONCAT(action_type, '_ROLLED_BACK'), rollback_status = 'ROLLED_BACK' WHERE batch_id = ?");
            $stmt->execute([$batch_id]);

            $pdo->commit();
        } catch (Exception $standard_error) {
            if ($pdo->inTransaction()) {
                $pdo->rollback();
            }

            // Try fallback rollback if standard rollback fails
            $fallback_result = handleFallbackRollback($pdo, $batch_id, $action_type, $batch_data);

            if ($fallback_result['success']) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Fallback rollback completed successfully (standard rollback failed)',
                    'results' => $fallback_result['results'],
                    'rollback_batch_id' => $fallback_result['batch_id']
                ]);
                exit();
            } else {
                throw new Exception("Standard rollback failed: " . $standard_error->getMessage() . ". Fallback rollback also failed: " . $fallback_result['message']);
            }
        }
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Rollback completed successfully',
            'results' => $rollback_results,
            'rollback_batch_id' => $rollback_batch_id
        ]);
        
    } catch (Exception $e) {
        // Only rollback if transaction is active
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollback();
        }

        error_log("Rollback error: " . $e->getMessage());
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Rollback failed: ' . $e->getMessage()
        ]);
    }
} else {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}

/**
 * Perform rollback based on action type
 */
function performRollback($pdo, $action_type, $batch_data) {
    $success_count = 0;
    $error_count = 0;
    $skipped_count = 0;
    $errors = [];
    
    $niks = $batch_data['niks'] ?? [];

    // Extract base action type for processing
    $base_action_type = str_replace('_ROLLED_BACK', '', $action_type);

    switch ($base_action_type) {
        case 'BATCH_INSERT':
            // For INSERT operations, we DELETE the inserted records
            foreach ($niks as $nik) {
                try {
                    // Check if employee still exists
                    $check_stmt = $pdo->prepare("SELECT id FROM karyawan WHERE nik = ?");
                    $check_stmt->execute([$nik]);
                    
                    if ($check_stmt->fetch()) {
                        // Delete the employee
                        $delete_stmt = $pdo->prepare("DELETE FROM karyawan WHERE nik = ?");
                        if ($delete_stmt->execute([$nik])) {
                            $success_count++;
                        } else {
                            $error_count++;
                            $errors[] = "Failed to delete employee with NIK: $nik";
                        }
                    } else {
                        $skipped_count++;
                        $errors[] = "Employee with NIK $nik not found (may have been deleted already)";
                    }
                } catch (Exception $e) {
                    $error_count++;
                    $errors[] = "Error processing NIK $nik: " . $e->getMessage();
                }
            }
            break;
            
        case 'BATCH_UPDATE':
            // For UPDATE operations, we need to restore previous values
            // This requires enhanced history tracking (will be implemented)
            $error_count = count($niks);
            $errors[] = "Rollback for UPDATE operations requires enhanced history tracking. This feature will be available in the next update.";
            break;
            
        case 'BATCH_DELETE':
            // For DELETE operations, we need to restore deleted records
            // This requires enhanced history tracking (will be implemented)
            $error_count = count($niks);
            $errors[] = "Rollback for DELETE operations requires enhanced history tracking. This feature will be available in the next update.";
            break;
            
        default:
            throw new Exception("Unknown action type: $action_type");
    }
    
    return [
        'success_count' => $success_count,
        'error_count' => $error_count,
        'skipped_count' => $skipped_count,
        'errors' => $errors
    ];
}

// Enhanced batch history functions are now in enhanced_batch_history.php
