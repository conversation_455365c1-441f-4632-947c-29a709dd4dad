<?php
session_start();
include '../config/config.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Handle date fields - using unified start_date and end_date structure
    $start_date = !empty($_POST['start_date']) ? $_POST['start_date'] : null;
    $end_date = !empty($_POST['end_date']) ? $_POST['end_date'] : null;
    $is_confirmed = isset($_POST['is_confirmed']) ? 1 : 0;

    // Handle optional fields with null defaults
    $additional_info = $_POST['additional_info'] ?? null;
    $comments_dept_head = $_POST['comments_dept_head'] ?? null;
    $comments_hrd = $_POST['comments_hrd'] ?? null;
    $comments_ga = $_POST['comments_ga'] ?? null;
    $comments_fm = $_POST['comments_fm'] ?? null;
    $comments_dir = $_POST['comments_dir'] ?? null;

    // Handle trainer arrays for internal provider
    $provider_type = $_POST['provider_type'] ?? '';

    // Debug: Log provider type and external vendor information
    error_log("Provider Type: " . $provider_type);
    error_log("Provider Name: " . ($_POST['provider_name'] ?? 'NULL'));
    error_log("Provider Address: " . ($_POST['provider_address'] ?? 'NULL'));
    error_log("Trainer Name External: " . ($_POST['trainer_name_external'] ?? 'NULL'));
    error_log("Additional Info Provider: " . ($_POST['additional_info_provider'] ?? 'NULL'));

    if ($provider_type === 'Internal') {
        // Check if trainer data is provided as arrays
        if (isset($_POST['trainer_name_internal']) && is_array($_POST['trainer_name_internal'])) {
            $trainer_name_internal = json_encode($_POST['trainer_name_internal']);
            $trainer_nik_internal = json_encode($_POST['trainer_nik_internal'] ?? []);
            $trainer_position_internal = json_encode($_POST['trainer_position_internal'] ?? []);
            $trainer_department_internal = json_encode($_POST['trainer_department_internal'] ?? []);
            $trainer_sub_department_internal = json_encode($_POST['trainer_sub_department_internal'] ?? []);

            // Clear external provider fields
            $provider_name = '';
            $provider_address = '';
            $trainer_name_external = '';
            $additional_info_provider = '';
        } else {
            // If not arrays, set to empty JSON arrays
            $trainer_name_internal = '[]';
            $trainer_nik_internal = '[]';
            $trainer_position_internal = '[]';
            $trainer_department_internal = '[]';
            $trainer_sub_department_internal = '[]';

            // Clear external provider fields
            $provider_name = '';
            $provider_address = '';
            $trainer_name_external = '';
            $additional_info_provider = '';
        }
    } else if ($provider_type === 'External' || $provider_type === 'Eksternal') {
        // For external provider, clear internal trainer fields
        $trainer_name_internal = '[]';
        $trainer_nik_internal = '[]';
        $trainer_position_internal = '[]';
        $trainer_department_internal = '[]';
        $trainer_sub_department_internal = '[]';

        // Use external provider fields
        $provider_name = $_POST['provider_name'] ?? '';
        $provider_address = $_POST['provider_address'] ?? '';
        $trainer_name_external = $_POST['trainer_name_external'] ?? '';
        $additional_info_provider = $_POST['additional_info_provider'] ?? '';
    } else {
        // Default values if provider type is not specified
        $trainer_name_internal = $_POST['trainer_name_internal'] ?? '';
        $trainer_nik_internal = $_POST['trainer_nik_internal'] ?? '';
        $trainer_position_internal = $_POST['trainer_position_internal'] ?? '';
        $trainer_department_internal = $_POST['trainer_department_internal'] ?? '';
        $trainer_sub_department_internal = $_POST['trainer_sub_department_internal'] ?? '';
        $provider_name = $_POST['provider_name'] ?? '';
        $provider_address = $_POST['provider_address'] ?? '';
        $trainer_name_external = $_POST['trainer_name_external'] ?? '';
        $additional_info_provider = $_POST['additional_info_provider'] ?? '';
    }

    // Handle assignment field
    $assignment = $_POST['assignment'] ?? null;

    // Handle internal memo image upload
    $internal_memo_image = null;
    if (isset($_FILES['internal_memo_image']) && $_FILES['internal_memo_image']['error'] === UPLOAD_ERR_OK) {
        // Validate file type
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png'];
        $file_type = $_FILES['internal_memo_image']['type'];

        if (!in_array($file_type, $allowed_types)) {
            $_SESSION['error'] = "Format file tidak valid. Hanya file JPG, JPEG, dan PNG yang diperbolehkan.";
            header('Location: edit_training.php?id=' . $_POST['id']);
            exit();
        }

        // Validate file size (max 2MB)
        if ($_FILES['internal_memo_image']['size'] > 2 * 1024 * 1024) {
            $_SESSION['error'] = "Ukuran file terlalu besar. Maksimal 2MB.";
            header('Location: edit_training.php?id=' . $_POST['id']);
            exit();
        }

        // Create upload directory if it doesn't exist
        $upload_dir = '../uploads/internal_memo/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        // Generate unique filename
        $file_extension = pathinfo($_FILES['internal_memo_image']['name'], PATHINFO_EXTENSION);
        $unique_filename = 'internal_memo_' . $_POST['id'] . '_' . time() . '.' . $file_extension;
        $upload_path = $upload_dir . $unique_filename;

        // Move uploaded file
        if (move_uploaded_file($_FILES['internal_memo_image']['tmp_name'], $upload_path)) {
            $internal_memo_image = 'uploads/internal_memo/' . $unique_filename;
        } else {
            $_SESSION['error'] = "Gagal mengupload file internal memo.";
            header('Location: edit_training.php?id=' . $_POST['id']);
            exit();
        }
    } else {
        // If no new file is uploaded, keep the existing one
        $stmt = $conn->prepare("SELECT internal_memo_image FROM training_submissions WHERE id = ?");
        $stmt->bind_param("i", $_POST['id']);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            $internal_memo_image = $row['internal_memo_image'];
        }
        $stmt->close();
    }

    // Count the number of parameters to ensure they match
    $params = [
        $_POST['user_id'] ?? '',
        $_POST['full_name'] ?? '',
        $_POST['email'] ?? '',
        $_POST['phone'] ?? '',
        $_POST['training_topic'] ?? '',
        $_POST['training_type'] ?? '',
        $provider_type,
        $trainer_name_internal,
        $trainer_nik_internal,
        $trainer_position_internal,
        $trainer_department_internal,
        $trainer_sub_department_internal,
        $provider_name,
        $provider_address,
        $trainer_name_external,
        $additional_info_provider,
        $start_date,           // Using unified start_date
        $end_date,             // Using unified end_date
        $is_confirmed,         // Using is_confirmed flag
        $_POST['training_place'] ?? '',
        $_POST['training_cost'] ?? '',
        $_POST['contact_person'] ?? '',
        $_POST['contact_number'] ?? '',
        $_POST['sharing_knowledge'] ?? '',
        $additional_info,      // Using nullable field
        $_POST['status'] ?? '',
        $_POST['current_approver_role_id'] ?? '',
        $_POST['approved_dept_head'] ?? '',
        $_POST['approved_hrd'] ?? '',
        $_POST['approved_ga'] ?? '',
        $_POST['approved_fm'] ?? '',
        $_POST['approved_dir'] ?? '',
        $comments_dept_head,   // Using nullable field
        $comments_hrd,         // Using nullable field
        $comments_ga,          // Using nullable field
        $comments_fm,          // Using nullable field
        $comments_dir,         // Using nullable field
        $_POST['training_time_start'] ?? '',
        $_POST['training_time_end'] ?? '',
        $_POST['nik'] ?? '',
        $_POST['jabatan'] ?? '',
        $_POST['bagian'] ?? '',
        $_POST['departemen'] ?? '',
        $assignment,           // Assignment field
        $internal_memo_image,  // Internal memo image
        $_POST['id']
    ];

    $query = "UPDATE training_submissions SET
        user_id = ?,
        full_name = ?,
        email = ?,
        phone = ?,
        training_topic = ?,
        training_type = ?,
        provider_type = ?,
        trainer_name_internal = ?,
        trainer_nik_internal = ?,
        trainer_position_internal = ?,
        trainer_department_internal = ?,
        trainer_sub_department_internal = ?,
        provider_name = ?,
        provider_address = ?,
        trainer_name_external = ?,
        additional_info_provider = ?,
        start_date = ?,
        end_date = ?,
        is_confirmed = ?,
        training_place = ?,
        training_cost = ?,
        contact_person = ?,
        contact_number = ?,
        sharing_knowledge = ?,
        additional_info = ?,
        status = ?,
        current_approver_role_id = ?,
        approved_dept_head = ?,
        approved_hrd = ?,
        approved_ga = ?,
        approved_fm = ?,
        approved_dir = ?,
        comments_dept_head = ?,
        comments_hrd = ?,
        comments_ga = ?,
        comments_fm = ?,
        comments_dir = ?,
        training_time_start = ?,
        training_time_end = ?,
        nik = ?,
        jabatan = ?,
        bagian = ?,
        departemen = ?,
        assignment = ?,
        internal_memo_image = ?
        WHERE id = ?";

    // Create the type definition string
    // Most are strings, but is_confirmed is integer, and id is integer
    $types = str_repeat('s', 18) . 'i' . str_repeat('s', count($params) - 20) . 'i'; // is_confirmed (pos 19) and id (last) are integers

    $stmt = $conn->prepare($query);

    // Bind parameters dynamically
    $stmt->bind_param($types, ...$params);

    try {
        if ($stmt->execute()) {
            $training_id = $_POST['id'];

            // Handle participants
            // 1. Update existing participants
            if (isset($_POST['participant_id']) && is_array($_POST['participant_id'])) {
                $update_participant_stmt = $conn->prepare("UPDATE participants SET
                    nama_participants = ?,
                    nik_participants = ?,
                    jabatan_participants = ?,
                    bagian_participants = ?,
                    departemen_participants = ?
                    WHERE id = ?");

                for ($i = 0; $i < count($_POST['participant_id']); $i++) {
                    $update_participant_stmt->bind_param(
                        "sssssi",
                        $_POST['participant_name'][$i],
                        $_POST['participant_nik'][$i],
                        $_POST['participant_position'][$i],
                        $_POST['participant_sub_department'][$i],
                        $_POST['participant_department'][$i],
                        $_POST['participant_id'][$i]
                    );
                    $update_participant_stmt->execute();
                }
            }

            // 2. Add new participants
            if (isset($_POST['new_participant_name']) && is_array($_POST['new_participant_name'])) {
                $insert_participant_stmt = $conn->prepare("INSERT INTO participants
                    (training_id, nama_participants, nik_participants, jabatan_participants, bagian_participants, departemen_participants)
                    VALUES (?, ?, ?, ?, ?, ?)");

                for ($i = 0; $i < count($_POST['new_participant_name']); $i++) {
                    $insert_participant_stmt->bind_param(
                        "isssss",
                        $training_id,
                        $_POST['new_participant_name'][$i],
                        $_POST['new_participant_nik'][$i],
                        $_POST['new_participant_position'][$i],
                        $_POST['new_participant_sub_department'][$i],
                        $_POST['new_participant_department'][$i]
                    );
                    $insert_participant_stmt->execute();
                }
            }

            $_SESSION['success'] = "Data training berhasil diperbarui!";
            header('Location: training_management.php');
            exit();
        }
    } catch (mysqli_sql_exception $e) {
        $_SESSION['error'] = "Gagal memperbarui data training: " . $e->getMessage();
        header('Location: edit_training.php?id=' . $_POST['id']);
        exit();
    }
} else {
    header('Location: training_management.php');
    exit();
}
?>

