<?php
include '../config/config.php';
include 'security.php'; // Pastikan pengguna sudah login

session_start();

// Validasi ID dari URL
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['errors'] = ["ID pengajuan tidak valid."];
    header("Location: form.php");
    exit();
}

$id = $_GET['id'];

// Validasi CSRF token (opsional, tambahkan jika diperlukan)
if (!isset($_GET['csrf_token']) || $_GET['csrf_token'] !== $_SESSION['token']) {
    $_SESSION['errors'] = ["Token CSRF tidak valid."];
    header("Location: form.php");
    exit();
}

// Pastikan hanya pemohon atau admin yang bisa membatalkan dan training belum approved
$query = "SELECT user_id, status FROM training_submissions WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();

if (!$row || ($row['user_id'] != $_SESSION['user_id'] && $_SESSION['role_id'] != 1)) {
    $_SESSION['errors'] = ["Anda tidak memiliki izin untuk membatalkan pengajuan ini."];
    header("Location: index.php");
    exit();
}

// Cek apakah training sudah approved - jika sudah, tidak bisa dibatalkan
if ($row['status'] == 'Approved') {
    $_SESSION['errors'] = ["Training yang sudah disetujui tidak dapat dibatalkan."];
    header("Location: detail_training.php?id=" . $id);
    exit();
}

// Hapus data dari tabel participants terlebih dahulu (karena ada foreign key)
$delete_participants = "DELETE FROM participants WHERE training_id = ?";
$stmt = $conn->prepare($delete_participants);
$stmt->bind_param("i", $id);
$stmt->execute();

// Hapus data dari tabel training_submissions
$delete_submission = "DELETE FROM training_submissions WHERE id = ?";
$stmt = $conn->prepare($delete_submission);
$stmt->bind_param("i", $id);

if ($stmt->execute()) {
    $_SESSION['success'] = "Pengajuan training berhasil dibatalkan.";
} else {
    $_SESSION['errors'] = ["Gagal membatalkan pengajuan: " . $stmt->error];
}

$stmt->close();
$conn->close();
header("Location: ../admin/dashboard.php"); // Redirect ke dashboard setelah pembatalan
exit();
?>