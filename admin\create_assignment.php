<?php
/**
 * Create Assignment Page for Admin
 * This page allows admins to create a new assignment for a training class
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if class ID is provided
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;

if ($class_id <= 0) {
    header('Location: manage_classes.php');
    exit();
}

// Get class information
$class_query = "SELECT c.*, t.training_topic
               FROM training_classes c
               JOIN training_submissions t ON c.training_id = t.id
               WHERE c.id = ?";
$stmt = $conn->prepare($class_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$class = $result->fetch_assoc();
$stmt->close();

if (!$class) {
    header('Location: manage_classes.php');
    exit();
}

// Check if the training_assignments table exists
$assignments_table_exists = false;
$check_assignments_table = $conn->query("SHOW TABLES LIKE 'training_assignments'");
if ($check_assignments_table && $check_assignments_table->num_rows > 0) {
    $assignments_table_exists = true;
} else {
    // Create the training_assignments table if it doesn't exist
    $create_assignments_table = "CREATE TABLE IF NOT EXISTS `training_assignments` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `class_id` int(11) NOT NULL,
        `title` varchar(255) NOT NULL,
        `description` text,
        `instructions` text,
        `due_date` datetime NOT NULL,
        `points` int(11) DEFAULT NULL,
        `allow_attachments` tinyint(1) DEFAULT '1',
        `max_file_size` int(11) DEFAULT '5242880',
        `allowed_file_types` varchar(255) DEFAULT 'pdf,doc,docx,ppt,pptx,xls,xlsx,txt,zip,rar,jpg,jpeg,png',
        `is_published` tinyint(1) DEFAULT '0',
        `created_by` int(11) NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `class_id` (`class_id`),
        KEY `created_by` (`created_by`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

    if ($conn->query($create_assignments_table) === TRUE) {
        $assignments_table_exists = true;
    }

    // Create the training_assignment_submissions table if it doesn't exist
    $create_submissions_table = "CREATE TABLE IF NOT EXISTS `training_assignment_submissions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `assignment_id` int(11) NOT NULL,
        `user_id` int(11) NOT NULL,
        `content` text,
        `file_path` varchar(255) DEFAULT NULL,
        `original_filename` varchar(255) DEFAULT NULL,
        `status` enum('submitted','graded','returned') NOT NULL DEFAULT 'submitted',
        `grade` int(11) DEFAULT NULL,
        `feedback` text,
        `submitted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `graded_at` timestamp NULL DEFAULT NULL,
        `graded_by` int(11) DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `assignment_id` (`assignment_id`),
        KEY `user_id` (`user_id`),
        KEY `graded_by` (`graded_by`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

    $conn->query($create_submissions_table);
}

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_assignment'])) {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $instructions = trim($_POST['instructions']);
    $due_date = $_POST['due_date'] . ' ' . $_POST['due_time'];
    $points = !empty($_POST['points']) ? intval($_POST['points']) : null;
    $allow_attachments = isset($_POST['allow_attachments']) ? 1 : 0;
    $max_file_size = !empty($_POST['max_file_size']) ? intval($_POST['max_file_size']) * 1024 * 1024 : 5242880; // Convert MB to bytes
    $allowed_file_types = !empty($_POST['allowed_file_types']) ? trim($_POST['allowed_file_types']) : 'pdf,doc,docx,ppt,pptx,xls,xlsx,txt,zip,rar,jpg,jpeg,png';
    $is_published = isset($_POST['is_published']) ? 1 : 0;

    // Validate input
    if (empty($title)) {
        $error_message = "Judul tugas harus diisi.";
    } elseif (empty($due_date)) {
        $error_message = "Tenggat waktu harus diisi.";
    } else {
        // Check if the allow_attachments column exists
        $check_column_query = "SHOW COLUMNS FROM training_assignments LIKE 'allow_attachments'";
        $column_result = $conn->query($check_column_query);
        $allow_attachments_exists = ($column_result && $column_result->num_rows > 0);

        if (!$allow_attachments_exists) {
            // Add the column if it doesn't exist
            $add_column_query = "ALTER TABLE training_assignments
                               ADD COLUMN allow_attachments tinyint(1) DEFAULT '1' AFTER points,
                               ADD COLUMN max_file_size int(11) DEFAULT '5242880' AFTER allow_attachments,
                               ADD COLUMN allowed_file_types varchar(255) DEFAULT 'pdf,doc,docx,ppt,pptx,xls,xlsx,txt,zip,rar,jpg,jpeg,png' AFTER max_file_size";
            $conn->query($add_column_query);
        }

        // Insert new assignment
        $insert_query = "INSERT INTO training_assignments
                       (class_id, title, description, instructions, due_date, points,
                        allow_attachments, max_file_size, allowed_file_types, is_published, created_by)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param("issssiisiii", $class_id, $title, $description, $instructions, $due_date,
                         $points, $allow_attachments, $max_file_size, $allowed_file_types, $is_published, $user_id);

        if ($stmt->execute()) {
            $assignment_id = $stmt->insert_id;
            $success_message = "Tugas berhasil dibuat.";

            // Redirect to manage class page
            $_SESSION['success_message'] = $success_message;
            header("Location: manage_class.php?id=$class_id");
            exit();
        } else {
            $error_message = "Gagal membuat tugas: " . $conn->error;
        }

        $stmt->close();
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .form-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .form-section h3 {
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .class-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .class-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .class-topic {
        color: #6c757d;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="welcome-section">
                <h1><i class="fas fa-tasks"></i> Buat Tugas Baru</h1>
                <a href="manage_class.php?id=<?= $class_id ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Kelas
                </a>
            </div>

            <?php if (!$assignments_table_exists): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle"></i> Fitur Tugas Tidak Tersedia</h5>
                    <p>Tabel database untuk fitur tugas tidak dapat dibuat. Silakan hubungi administrator sistem.</p>
                </div>
            <?php else: ?>
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $error_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?= $success_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="class-info">
                    <div class="class-title"><?= htmlspecialchars($class['title']) ?></div>
                    <div class="class-topic"><?= htmlspecialchars($class['training_topic']) ?></div>
                </div>

                <div class="form-section">
                    <h3>Informasi Tugas</h3>

                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="title" class="form-label">Judul Tugas <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi Tugas</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            <div class="form-text">Deskripsi singkat tentang tugas ini.</div>
                        </div>

                        <div class="mb-3">
                            <label for="instructions" class="form-label">Instruksi Tugas</label>
                            <textarea class="form-control" id="instructions" name="instructions" rows="5"></textarea>
                            <div class="form-text">Instruksi detail tentang cara mengerjakan tugas ini.</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="due_date" class="form-label">Tanggal Tenggat <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="due_date" name="due_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="due_time" class="form-label">Waktu Tenggat <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="due_time" name="due_time" value="23:59" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="points" class="form-label">Poin</label>
                            <input type="number" class="form-control" id="points" name="points" min="0" max="100">
                            <div class="form-text">Poin maksimal yang dapat diperoleh untuk tugas ini.</div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="allow_attachments" name="allow_attachments" checked>
                            <label class="form-check-label" for="allow_attachments">Izinkan Lampiran File</label>
                        </div>

                        <div class="row" id="attachment_options">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_file_size" class="form-label">Ukuran File Maksimal (MB)</label>
                                    <input type="number" class="form-control" id="max_file_size" name="max_file_size" min="1" max="50" value="5">
                                </div>
                            </div>
                            <div class="mb-3">
                                    <label for="allowed_file_types" class="form-label">Tipe File yang Diizinkan</label>
                                    <input type="text" class="form-control" id="allowed_file_types" name="allowed_file_types" value="pdf,doc,docx,ppt,pptx,xls,xlsx,txt,zip,rar,jpg,jpeg,png,mp4,mp3,csv,rtf">
                                    <div class="form-text">Pisahkan dengan koma (contoh: pdf,doc,docx)</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_published" name="is_published">
                            <label class="form-check-label" for="is_published">Publikasikan Tugas</label>
                            <div class="form-text">Jika dicentang, tugas akan langsung terlihat oleh peserta.</div>
                        </div>

                        <button type="submit" name="create_assignment" class="btn btn-primary">
                            <i class="fas fa-save"></i> Buat Tugas
                        </button>
                    </form>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);

        // Toggle attachment options based on allow_attachments checkbox
        const allowAttachmentsCheckbox = document.getElementById('allow_attachments');
        const attachmentOptions = document.getElementById('attachment_options');

        function toggleAttachmentOptions() {
            if (allowAttachmentsCheckbox.checked) {
                attachmentOptions.style.display = 'flex';
            } else {
                attachmentOptions.style.display = 'none';
            }
        }

        if (allowAttachmentsCheckbox && attachmentOptions) {
            toggleAttachmentOptions();
            allowAttachmentsCheckbox.addEventListener('change', toggleAttachmentOptions);
        }
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
