<?php
// File: pemohon/training_registration_api.php
// API untuk pendaftaran training dari kalender

include '../config/config.php';
include '../admin/security.php';

header('Content-Type: application/json');

$response = ['success' => false, 'message' => '', 'data' => null];

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'register_self':
                $training_id = $input['training_id'] ?? 0;
                $training_type = $input['training_type'] ?? 'internal';
                
                // Get current user info
                $user_query = "SELECT u.*, k.id as karyawan_id, k.nik, k.nama, k.dept, k.jabatan 
                              FROM users u 
                              LEFT JOIN karyawan k ON u.nik = k.nik 
                              WHERE u.id = ?";
                $user_stmt = $conn->prepare($user_query);
                $user_stmt->bind_param("i", $_SESSION['user_id']);
                $user_stmt->execute();
                $user_result = $user_stmt->get_result();
                
                if ($user_result->num_rows == 0) {
                    $response['message'] = 'Data user tidak ditemukan';
                    break;
                }
                
                $user_data = $user_result->fetch_assoc();
                
                if (!$user_data['karyawan_id']) {
                    $response['message'] = 'Data karyawan tidak ditemukan. Hubungi admin.';
                    break;
                }
                
                // Check if already registered
                $check_query = "SELECT id FROM training_registrations 
                               WHERE training_id = ? AND training_type = ? AND participant_id = ?";
                $check_stmt = $conn->prepare($check_query);
                $check_stmt->bind_param("isi", $training_id, $training_type, $user_data['karyawan_id']);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                
                if ($check_result->num_rows > 0) {
                    $response['message'] = 'Anda sudah terdaftar untuk training ini';
                    break;
                }
                
                // Register user
                $register_query = "INSERT INTO training_registrations 
                                  (training_id, training_type, participant_id, participant_nik, participant_nama, 
                                   participant_dept, participant_jabatan, registered_by, registered_by_name, 
                                   registered_by_dept, registration_type) 
                                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'self')";
                
                $register_stmt = $conn->prepare($register_query);
                $register_stmt->bind_param("isississss", 
                    $training_id, $training_type, $user_data['karyawan_id'], $user_data['nik'], 
                    $user_data['nama'], $user_data['dept'], $user_data['jabatan'], 
                    $_SESSION['user_id'], $user_data['name'], $user_data['dept']
                );
                
                if ($register_stmt->execute()) {
                    $registration_id = $conn->insert_id;
                    
                    // Create notification for admins
                    createAdminNotification($conn, $registration_id);
                    
                    $response['success'] = true;
                    $response['message'] = 'Pendaftaran berhasil! Menunggu konfirmasi admin.';
                } else {
                    $response['message'] = 'Gagal mendaftarkan. Coba lagi.';
                }
                break;
                
            case 'register_employee':
                $training_id = $input['training_id'] ?? 0;
                $training_type = $input['training_type'] ?? 'internal';
                $employee_ids = $input['employee_ids'] ?? [];
                
                // Check user level
                $user_query = "SELECT u.*, k.level_karyawan, k.dept as user_dept 
                              FROM users u 
                              LEFT JOIN karyawan k ON u.nik = k.nik 
                              WHERE u.id = ?";
                $user_stmt = $conn->prepare($user_query);
                $user_stmt->bind_param("i", $_SESSION['user_id']);
                $user_stmt->execute();
                $user_result = $user_stmt->get_result();
                
                if ($user_result->num_rows == 0) {
                    $response['message'] = 'Data user tidak ditemukan';
                    break;
                }
                
                $user_data = $user_result->fetch_assoc();
                
                // Check if user has permission (level 4+ or supervisor/chief)
                $has_permission = ($user_data['level_karyawan'] >= 4) || 
                                 (stripos($user_data['jabatan'], 'supervisor') !== false) || 
                                 (stripos($user_data['jabatan'], 'chief') !== false);
                
                if (!$has_permission) {
                    $response['message'] = 'Anda tidak memiliki izin untuk mendaftarkan karyawan lain';
                    break;
                }
                
                $registered_count = 0;
                $errors = [];
                
                foreach ($employee_ids as $employee_id) {
                    // Get employee data
                    $emp_query = "SELECT * FROM karyawan WHERE id = ? AND dept = ?";
                    $emp_stmt = $conn->prepare($emp_query);
                    $emp_stmt->bind_param("is", $employee_id, $user_data['user_dept']);
                    $emp_stmt->execute();
                    $emp_result = $emp_stmt->get_result();
                    
                    if ($emp_result->num_rows == 0) {
                        $errors[] = "Karyawan ID $employee_id tidak ditemukan atau bukan dari departemen Anda";
                        continue;
                    }
                    
                    $emp_data = $emp_result->fetch_assoc();
                    
                    // Check if already registered
                    $check_query = "SELECT id FROM training_registrations 
                                   WHERE training_id = ? AND training_type = ? AND participant_id = ?";
                    $check_stmt = $conn->prepare($check_query);
                    $check_stmt->bind_param("isi", $training_id, $training_type, $employee_id);
                    $check_stmt->execute();
                    $check_result = $check_stmt->get_result();
                    
                    if ($check_result->num_rows > 0) {
                        $errors[] = $emp_data['nama'] . " sudah terdaftar";
                        continue;
                    }
                    
                    // Register employee
                    $register_query = "INSERT INTO training_registrations 
                                      (training_id, training_type, participant_id, participant_nik, participant_nama, 
                                       participant_dept, participant_jabatan, registered_by, registered_by_name, 
                                       registered_by_dept, registration_type) 
                                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'supervisor')";
                    
                    $register_stmt = $conn->prepare($register_query);
                    $register_stmt->bind_param("isississss", 
                        $training_id, $training_type, $employee_id, $emp_data['nik'], 
                        $emp_data['nama'], $emp_data['dept'], $emp_data['jabatan'], 
                        $_SESSION['user_id'], $user_data['name'], $user_data['user_dept']
                    );
                    
                    if ($register_stmt->execute()) {
                        $registration_id = $conn->insert_id();
                        createAdminNotification($conn, $registration_id);
                        $registered_count++;
                    } else {
                        $errors[] = "Gagal mendaftarkan " . $emp_data['nama'];
                    }
                }
                
                $response['success'] = true;
                $response['message'] = "Berhasil mendaftarkan $registered_count karyawan. " . 
                                     (count($errors) > 0 ? "Errors: " . implode(", ", $errors) : "");
                break;
                
            default:
                $response['message'] = 'Invalid action';
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'get_my_registrations':
                $user_query = "SELECT k.id as karyawan_id FROM users u 
                              LEFT JOIN karyawan k ON u.nik = k.nik 
                              WHERE u.id = ?";
                $user_stmt = $conn->prepare($user_query);
                $user_stmt->bind_param("i", $_SESSION['user_id']);
                $user_stmt->execute();
                $user_result = $user_stmt->get_result();
                
                if ($user_result->num_rows > 0) {
                    $user_data = $user_result->fetch_assoc();
                    
                    $reg_query = "SELECT tr.*, 
                                         CASE 
                                             WHEN tr.training_type = 'internal' THEN ot.judul_training
                                             ELSE ts.training_name
                                         END as training_title
                                  FROM training_registrations tr
                                  LEFT JOIN offline_training ot ON tr.training_id = ot.id AND tr.training_type = 'internal'
                                  LEFT JOIN training_submissions ts ON tr.training_id = ts.id AND tr.training_type = 'external'
                                  WHERE tr.participant_id = ?
                                  ORDER BY tr.created_at DESC";
                    
                    $reg_stmt = $conn->prepare($reg_query);
                    $reg_stmt->bind_param("i", $user_data['karyawan_id']);
                    $reg_stmt->execute();
                    $reg_result = $reg_stmt->get_result();
                    
                    $registrations = [];
                    while ($row = $reg_result->fetch_assoc()) {
                        $registrations[] = $row;
                    }
                    
                    $response['success'] = true;
                    $response['registrations'] = $registrations;
                } else {
                    $response['message'] = 'Data karyawan tidak ditemukan';
                }
                break;
                
            default:
                $response['message'] = 'Invalid action';
        }
    }
    
} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
}

function createAdminNotification($conn, $registration_id) {
    // Get all admin users (role_id 1 or level 4+)
    $admin_query = "SELECT DISTINCT u.id 
                   FROM users u 
                   LEFT JOIN karyawan k ON u.nik = k.nik 
                   WHERE u.role_id = 1 OR k.level_karyawan >= 4";
    $admin_result = $conn->query($admin_query);
    
    while ($admin = $admin_result->fetch_assoc()) {
        $notif_query = "INSERT INTO training_registration_notifications (registration_id, admin_id) VALUES (?, ?)";
        $notif_stmt = $conn->prepare($notif_query);
        $notif_stmt->bind_param("ii", $registration_id, $admin['id']);
        $notif_stmt->execute();
    }
}

echo json_encode($response);
?>
