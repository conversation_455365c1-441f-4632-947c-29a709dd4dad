# Fitur Password Default - Dokumentasi

## Deskripsi
Fitur ini secara otomatis mengarahkan user ke halaman `change_password.php` ketika mereka login menggunakan password default "asdf". Ini meningkatkan keamanan aplikasi dengan memaksa user mengubah password default mereka.

## Implementasi

### 1. Modifikasi Login Process (`view/login.php`)

**Lokasi perubahan:** Line 460-466

```php
// Cek apakah menggunakan password default "asdf"
if ($password === 'asdf') {
    $_SESSION['default_password_used'] = true;
    $_SESSION['require_password_change'] = true;
    header('Location: change_password.php?reason=default');
    exit();
}
```

**Fitur:**
- Deteksi otomatis password "asdf" saat login
- Set session flag untuk tracking
- Redirect langsung ke change_password.php dengan parameter reason=default

### 2. Modifikasi Change Password Page (`view/change_password.php`)

**<PERSON><PERSON>an utama:**

#### A. Include Security Helper
```php
include '../config/security_helper.php';
```

#### B. Deteksi Password Default
```php
$default_password_used = isset($_SESSION['default_password_used']) && $_SESSION['default_password_used'];

// Ambil reason dari URL parameter
$reason = $_GET['reason'] ?? '';
if ($reason === 'default') {
    $default_password_used = true;
}
```

#### C. Pesan Khusus untuk Password Default
```php
<?php if ($default_password_used): ?>
    <p style="color: #d9534f; font-weight: 600;">
        <i class="fas fa-exclamation-triangle"></i> 
        Anda menggunakan password default "asdf". Untuk keamanan akun, silakan ubah password Anda sekarang.
    </p>
<?php endif; ?>
```

#### D. Session Cleanup
```php
if (isset($_SESSION['default_password_used'])) {
    unset($_SESSION['default_password_used']);
}
```

## Flow Aplikasi

### 1. Login dengan Password Default
```
User login dengan password "asdf"
    ↓
Sistem deteksi password default
    ↓
Set session flags:
- $_SESSION['default_password_used'] = true
- $_SESSION['require_password_change'] = true
    ↓
Redirect ke: change_password.php?reason=default
```

### 2. Change Password Process
```
User di halaman change_password.php
    ↓
Tampilkan pesan warning khusus
    ↓
User mengisi form change password
    ↓
Validasi password baru (sesuai security settings)
    ↓
Update password di database
    ↓
Clear session flags
    ↓
Redirect ke dashboard sesuai role
```

## Security Features

### 1. Password Validation
- Menggunakan fungsi `validate_password_strength()` dari `security_helper.php`
- Validasi berdasarkan settings database:
  - Minimal length
  - Require uppercase
  - Require numbers  
  - Require special characters

### 2. Session Management
- Flag `default_password_used` untuk tracking
- Flag `require_password_change` untuk enforcement
- Automatic cleanup setelah password berhasil diubah

### 3. URL Parameters
- Parameter `reason=default` untuk identifikasi sumber redirect
- Parameter `reason=expired` untuk password expired

## Testing

### 1. Manual Testing
1. Buat user dengan password "asdf"
2. Login dengan user tersebut
3. Verifikasi redirect ke change_password.php
4. Verifikasi pesan warning muncul
5. Ubah password dan verifikasi redirect ke dashboard

### 2. Automated Testing
Gunakan file `test_default_password.php`:
```bash
# Akses via browser
http://your-domain/training/test_default_password.php
```

File ini akan:
- Cek user dengan password default
- Buat test user jika belum ada
- Berikan instruksi testing
- Sediakan opsi cleanup

## File yang Dimodifikasi

1. **view/login.php**
   - Tambah deteksi password "asdf"
   - Tambah redirect logic

2. **view/change_password.php**
   - Tambah include security_helper.php
   - Tambah deteksi reason parameter
   - Tambah pesan khusus password default
   - Tambah session cleanup

## Dependencies

1. **config/security_helper.php**
   - Fungsi `validate_password_strength()`
   - Fungsi `get_security_settings()`

2. **Database Tables**
   - `users` table dengan kolom `password`
   - `settings` table untuk security settings

## Keamanan Tambahan

### 1. Rate Limiting
- Existing rate limiting tetap berlaku
- Tidak ada bypass untuk password default

### 2. Session Security
- Session regeneration tetap berjalan
- CSRF protection tetap aktif

### 3. Logging
- Login attempts tetap dicatat
- Password changes dicatat dengan timestamp

## Maintenance

### 1. Monitoring
- Monitor user dengan password default via admin panel
- Check security logs untuk pattern login

### 2. Cleanup
- Hapus test files setelah deployment
- Monitor dan alert jika ada user baru dengan password default

## Troubleshooting

### 1. Redirect Loop
**Gejala:** User stuck di change_password.php
**Solusi:** 
- Cek session flags
- Pastikan password berhasil diupdate
- Clear session manual jika perlu

### 2. Function Not Found
**Gejala:** Error validate_password_strength() not found
**Solusi:**
- Pastikan security_helper.php di-include
- Cek path file

### 3. Database Error
**Gejala:** Error saat update password
**Solusi:**
- Cek koneksi database
- Cek struktur tabel users
- Cek permissions

## Future Enhancements

1. **Admin Notification**
   - Email admin ketika user login dengan password default
   - Dashboard alert untuk admin

2. **Bulk Password Reset**
   - Tool untuk reset multiple user passwords
   - Force password change untuk semua user

3. **Password History**
   - Prevent reuse of recent passwords
   - Track password change history

4. **Advanced Detection**
   - Detect other common weak passwords
   - Password strength scoring
