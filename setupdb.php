<?php
/**
 * Script Pengaturan Database
 *
 * Script ini membuat database dan semua tabel yang diperlukan untuk Sistem Training.
 * Script ini membaca perintah SQL dari setupdb.sql dan mengeksekusinya dengan informasi debugging yang detail.
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database configuration
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'db_training';

// Create backup directory if it doesn't exist
$backup_dir = 'backups';
if (!file_exists($backup_dir)) {
    mkdir($backup_dir, 0755, true);
}

// Set execution time limit to 5 minutes
set_time_limit(300);

// Function to display messages
function showMessage($message, $isError = false) {
    echo '<div style="padding: 10px; margin: 5px; border-radius: 5px; ' .
         ($isError ? 'background-color: #ffebee; color: #c62828;' : 'background-color: #e8f5e9; color: #2e7d32;') .
         '">' . $message . '</div>';
}

// Function to check MySQL server status
function checkMySqlStatus($db_host, $db_user, $db_pass) {
    try {
        $conn = new mysqli($db_host, $db_user, $db_pass);
        if ($conn->connect_error) {
            return [
                'status' => false,
                'message' => "Connection failed: " . $conn->connect_error
            ];
        }

        // Get server info
        $server_info = $conn->server_info;
        $host_info = $conn->host_info;

        // Get MySQL version
        $version_query = $conn->query("SELECT VERSION() as version");
        $version = $version_query ? $version_query->fetch_assoc()['version'] : 'Unknown';

        // Check if we can create databases
        $can_create_db = false;
        $grants_query = $conn->query("SHOW GRANTS FOR CURRENT_USER()");
        if ($grants_query) {
            while ($row = $grants_query->fetch_array()) {
                if (strpos($row[0], "ALL PRIVILEGES") !== false ||
                    strpos($row[0], "CREATE") !== false) {
                    $can_create_db = true;
                    break;
                }
            }
        }

        $conn->close();

        return [
            'status' => true,
            'server_info' => $server_info,
            'host_info' => $host_info,
            'version' => $version,
            'can_create_db' => $can_create_db
        ];
    } catch (Exception $e) {
        return [
            'status' => false,
            'message' => "Exception: " . $e->getMessage()
        ];
    }
}

// Function to check if database exists
function checkDatabaseExists($db_host, $db_user, $db_pass, $db_name) {
    try {
        $conn = new mysqli($db_host, $db_user, $db_pass);
        if ($conn->connect_error) {
            return [
                'status' => false,
                'message' => "Connection failed: " . $conn->connect_error
            ];
        }

        $result = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$db_name'");
        $exists = $result && $result->num_rows > 0;

        if ($exists) {
            // Get database size
            $size_query = $conn->query("SELECT
                table_schema AS 'Database',
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
                FROM information_schema.TABLES
                WHERE table_schema = '$db_name'
                GROUP BY table_schema");

            $size = $size_query ? $size_query->fetch_assoc()['Size (MB)'] : 'Unknown';

            // Get table count
            $conn->select_db($db_name);
            $tables_query = $conn->query("SHOW TABLES");
            $table_count = $tables_query ? $tables_query->num_rows : 0;

            // Get table list
            $tables = [];
            if ($tables_query) {
                while ($row = $tables_query->fetch_array()) {
                    $tables[] = $row[0];
                }
            }
        }

        $conn->close();

        return [
            'status' => true,
            'exists' => $exists,
            'size' => $exists ? $size : null,
            'table_count' => $exists ? $table_count : 0,
            'tables' => $exists ? $tables : []
        ];
    } catch (Exception $e) {
        return [
            'status' => false,
            'message' => "Exception: " . $e->getMessage()
        ];
    }
}

// Function to backup database
function backupDatabase($db_host, $db_user, $db_pass, $db_name) {
    try {
        // Check if database exists
        $conn = new mysqli($db_host, $db_user, $db_pass);
        if ($conn->connect_error) {
            return [
                'success' => false,
                'message' => "Koneksi gagal: " . $conn->connect_error
            ];
        }

        $result = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$db_name'");
        $exists = $result && $result->num_rows > 0;

        if (!$exists) {
            return [
                'success' => false,
                'message' => "Database '$db_name' tidak ditemukan."
            ];
        }

        // Create backup directory if it doesn't exist
        $backup_dir = 'backups';
        if (!file_exists($backup_dir)) {
            if (!mkdir($backup_dir, 0755, true)) {
                return [
                    'success' => false,
                    'message' => "Gagal membuat direktori backup."
                ];
            }
        }

        // Generate backup filename with timestamp
        $timestamp = date('Y-m-d_H-i-s');
        $backup_file = "$backup_dir/{$db_name}_backup_{$timestamp}.sql";

        // Build mysqldump command
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            // Windows
            $mysqldump_cmd = "mysqldump";
        } else {
            // Linux/Unix/Mac
            $mysqldump_cmd = "mysqldump";
        }

        $command = "$mysqldump_cmd -h $db_host -u $db_user";
        if (!empty($db_pass)) {
            $command .= " -p" . escapeshellarg($db_pass);
        }
        $command .= " $db_name > " . escapeshellarg($backup_file) . " 2>&1";

        // Execute command
        $output = [];
        $return_var = 0;
        exec($command, $output, $return_var);

        if ($return_var !== 0) {
            return [
                'success' => false,
                'message' => "Backup gagal. Error code: $return_var",
                'output' => implode("\n", $output)
            ];
        }

        // Check if backup file was created and has content
        if (!file_exists($backup_file) || filesize($backup_file) === 0) {
            return [
                'success' => false,
                'message' => "Backup file tidak dibuat atau kosong.",
                'output' => implode("\n", $output)
            ];
        }

        // Get file size
        $size_bytes = filesize($backup_file);
        $size_kb = round($size_bytes / 1024, 2);
        $size_mb = round($size_kb / 1024, 2);

        $size_str = $size_mb >= 1 ? "$size_mb MB" : "$size_kb KB";

        return [
            'success' => true,
            'message' => "Backup berhasil dibuat.",
            'file' => $backup_file,
            'size' => $size_str,
            'timestamp' => $timestamp
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "Exception: " . $e->getMessage()
        ];
    }
}

// Function to get list of backup files
function getBackupFiles($backup_dir = 'backups') {
    if (!file_exists($backup_dir)) {
        return [];
    }

    $files = [];
    $dir = opendir($backup_dir);

    while (($file = readdir($dir)) !== false) {
        if ($file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) == 'sql') {
            $full_path = $backup_dir . '/' . $file;
            $size_bytes = filesize($full_path);
            $size_kb = round($size_bytes / 1024, 2);
            $size_mb = round($size_kb / 1024, 2);
            $size_str = $size_mb >= 1 ? "$size_mb MB" : "$size_kb KB";

            // Get database name and tables from backup file
            $db_info = getBackupFileInfo($full_path);

            $files[] = [
                'name' => $file,
                'path' => $full_path,
                'size' => $size_str,
                'size_bytes' => $size_bytes,
                'date' => date('Y-m-d H:i:s', filemtime($full_path)),
                'db_name' => $db_info['db_name'],
                'table_count' => $db_info['table_count'],
                'tables' => $db_info['tables']
            ];
        }
    }

    closedir($dir);

    // Sort files by date (newest first)
    usort($files, function($a, $b) {
        return filemtime($b['path']) - filemtime($a['path']);
    });

    return $files;
}

// Function to get database info from backup file
function getBackupFileInfo($file_path) {
    $result = [
        'db_name' => 'Unknown',
        'table_count' => 0,
        'tables' => []
    ];

    if (!file_exists($file_path)) {
        return $result;
    }

    // Open the file
    $handle = fopen($file_path, 'r');
    if (!$handle) {
        return $result;
    }

    // Read first 100 lines to find database name
    $line_count = 0;
    $max_lines = 100;
    $tables = [];
    $db_name_found = false;

    while (($line = fgets($handle)) !== false && $line_count < $max_lines) {
        $line_count++;

        // Look for database name
        if (!$db_name_found && preg_match('/USE\s+`?([^`]+)`?/i', $line, $matches)) {
            $result['db_name'] = $matches[1];
            $db_name_found = true;
        }

        // Look for table creation
        if (preg_match('/CREATE TABLE\s+`?([^`]+)`?/i', $line, $matches)) {
            $tables[] = $matches[1];
        }
    }

    // If we didn't find enough tables in the first 100 lines, scan the whole file
    if (count($tables) < 2) {
        // Reset file pointer
        rewind($handle);

        // Scan for table creation statements
        $tables = [];
        while (($line = fgets($handle)) !== false) {
            if (preg_match('/CREATE TABLE\s+`?([^`]+)`?/i', $line, $matches)) {
                $tables[] = $matches[1];
            }
        }
    }

    fclose($handle);

    $result['table_count'] = count($tables);
    $result['tables'] = $tables;

    return $result;
}

// Function to delete backup file
function deleteBackupFile($file_path) {
    // Debug information
    $debug_info = [];
    $debug_info[] = "File path: $file_path";

    if (!file_exists($file_path)) {
        return [
            'success' => false,
            'message' => "File tidak ditemukan: $file_path",
            'debug' => $debug_info
        ];
    }

    $debug_info[] = "File exists: Yes";
    $debug_info[] = "File size: " . filesize($file_path) . " bytes";
    $debug_info[] = "File permissions: " . substr(sprintf('%o', fileperms($file_path)), -4);

    // Check if file is in the backups directory
    $backup_dir = realpath('backups');
    $file_real_path = realpath($file_path);

    $debug_info[] = "Backup directory: $backup_dir";
    $debug_info[] = "File real path: $file_real_path";

    if (!$file_real_path || strpos($file_real_path, $backup_dir) !== 0) {
        return [
            'success' => false,
            'message' => "File tidak berada di direktori backup yang diizinkan.",
            'debug' => $debug_info
        ];
    }

    $debug_info[] = "File is in backup directory: Yes";

    // Make sure we have write permissions to the directory
    if (!is_writable(dirname($file_real_path))) {
        $debug_info[] = "Directory is writable: No";
        return [
            'success' => false,
            'message' => "Tidak memiliki izin untuk menulis ke direktori backup.",
            'debug' => $debug_info
        ];
    }

    $debug_info[] = "Directory is writable: Yes";

    // Try to delete the file
    $result = @unlink($file_real_path);
    $debug_info[] = "Unlink result: " . ($result ? "Success" : "Failed");

    if ($result) {
        return [
            'success' => true,
            'message' => "File backup berhasil dihapus.",
            'debug' => $debug_info
        ];
    } else {
        $error = error_get_last();
        $debug_info[] = "Error: " . ($error ? $error['message'] : "Unknown error");

        return [
            'success' => false,
            'message' => "Gagal menghapus file backup. Periksa izin file.",
            'debug' => $debug_info
        ];
    }
}

// Function to import database from backup file
function importDatabase($backup_file, $db_host, $db_user, $db_pass, $db_name) {
    try {
        // Increase memory limit and execution time for large imports
        ini_set('memory_limit', '512M');
        set_time_limit(300); // 5 minutes

        // Check if backup file exists
        if (!file_exists($backup_file)) {
            return [
                'success' => false,
                'message' => "File backup tidak ditemukan: $backup_file"
            ];
        }

        // Check if file is empty
        if (filesize($backup_file) === 0) {
            return [
                'success' => false,
                'message' => "File backup kosong: $backup_file"
            ];
        }

        // Connect to MySQL server
        $conn = new mysqli($db_host, $db_user, $db_pass);
        if ($conn->connect_error) {
            return [
                'success' => false,
                'message' => "Koneksi gagal: " . $conn->connect_error
            ];
        }

        // Check if database exists, create if not
        $result = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$db_name'");
        $exists = $result && $result->num_rows > 0;

        if (!$exists) {
            if (!$conn->query("CREATE DATABASE `$db_name`")) {
                return [
                    'success' => false,
                    'message' => "Gagal membuat database: " . $conn->error
                ];
            }
        }

        // Select database and disable foreign key checks
        $conn->select_db($db_name);
        $conn->query("SET FOREIGN_KEY_CHECKS=0");

        // Start timing
        $start_time = microtime(true);

        // METODE 1: Menggunakan command line mysql (cara cepat)
        $command = '';
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            // Windows
            $command = "mysql -h {$db_host} -u {$db_user}";
            if (!empty($db_pass)) {
                $command .= " -p" . escapeshellarg($db_pass);
            }
            $command .= " {$db_name} < " . escapeshellarg($backup_file) . " 2>&1";
            // Use cmd.exe to handle redirection
            $command = "cmd /c \"{$command}\"";
        } else {
            // Linux/Unix/Mac
            $command = "mysql -h {$db_host} -u {$db_user}";
            if (!empty($db_pass)) {
                $command .= " -p" . escapeshellarg($db_pass);
            }
            $command .= " {$db_name} < " . escapeshellarg($backup_file) . " 2>&1";
        }

        // Execute command
        $output = [];
        $return_var = 0;
        exec($command, $output, $return_var);

        // Check if command line method failed
        if ($return_var !== 0) {
            // METODE 2: Menggunakan PHP untuk membaca dan mengeksekusi SQL (metode alternatif)
            // Reset database
            $conn->query("DROP DATABASE IF EXISTS `$db_name`");
            $conn->query("CREATE DATABASE `$db_name`");
            $conn->select_db($db_name);
            $conn->query("SET FOREIGN_KEY_CHECKS=0");

            // Read the file line by line and execute SQL statements
            $handle = fopen($backup_file, 'r');
            if ($handle) {
                $query = '';
                $executed_queries = 0;
                $failed_queries = 0;
                $line_number = 0;

                while (($line = fgets($handle)) !== false) {
                    $line_number++;
                    $line = trim($line);

                    // Skip comments and empty lines
                    if (empty($line) || strpos($line, '--') === 0 || strpos($line, '/*') === 0) {
                        continue;
                    }

                    $query .= $line . "\n";

                    // If the line ends with a semicolon, execute the query
                    if (substr($line, -1) === ';') {
                        try {
                            if ($conn->query($query)) {
                                $executed_queries++;
                            } else {
                                $failed_queries++;
                            }
                        } catch (Exception $e) {
                            $failed_queries++;
                        }

                        $query = '';
                    }
                }

                fclose($handle);

                // If we have more successful queries than failures, consider it a success
                if ($executed_queries > 0 && $failed_queries < $executed_queries) {
                    $total_time = round(microtime(true) - $start_time, 2);

                    // Get table count
                    $tables_query = $conn->query("SHOW TABLES");
                    $table_count = $tables_query ? $tables_query->num_rows : 0;

                    // Get database size
                    $size_query = $conn->query("SELECT
                        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
                        FROM information_schema.TABLES
                        WHERE table_schema = '$db_name'");
                    $size = $size_query ? $size_query->fetch_assoc()['Size (MB)'] : 'Unknown';

                    // Re-enable foreign key checks
                    $conn->query("SET FOREIGN_KEY_CHECKS=1");

                    // Close connection
                    $conn->close();

                    return [
                        'success' => true,
                        'message' => "Import berhasil diselesaikan menggunakan metode alternatif. {$executed_queries} query berhasil dieksekusi.",
                        'time' => $total_time,
                        'table_count' => $table_count,
                        'size' => $size,
                        'method' => 'alternative'
                    ];
                } else {
                    // Both methods failed
                    return [
                        'success' => false,
                        'message' => "Import gagal dengan kedua metode. Metode alternatif: {$executed_queries} berhasil, {$failed_queries} gagal.",
                        'output' => implode("\n", $output)
                    ];
                }
            } else {
                // Could not open file for reading
                return [
                    'success' => false,
                    'message' => "Gagal membuka file untuk dibaca.",
                    'output' => implode("\n", $output)
                ];
            }
        } else {
            // Command line method succeeded
            $total_time = round(microtime(true) - $start_time, 2);

            // Get database status after import
            $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
            if ($conn->connect_error) {
                return [
                    'success' => true,
                    'message' => "Import berhasil, tetapi tidak dapat memeriksa status database.",
                    'time' => $total_time,
                    'method' => 'command_line'
                ];
            }

            // Get table count
            $tables_query = $conn->query("SHOW TABLES");
            $table_count = $tables_query ? $tables_query->num_rows : 0;

            // Get database size
            $size_query = $conn->query("SELECT
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
                FROM information_schema.TABLES
                WHERE table_schema = '$db_name'");
            $size = $size_query ? $size_query->fetch_assoc()['Size (MB)'] : 'Unknown';

            // Re-enable foreign key checks
            $conn->query("SET FOREIGN_KEY_CHECKS=1");

            // Close connection
            $conn->close();

            return [
                'success' => true,
                'message' => "Import berhasil diselesaikan menggunakan command line.",
                'time' => $total_time,
                'table_count' => $table_count,
                'size' => $size,
                'method' => 'command_line'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "Exception: " . $e->getMessage()
        ];
    }
}

// Function to execute SQL commands with detailed logging
function executeSqlFile($filename, $db_host, $db_user, $db_pass, $db_name = 'db_training') {
    // Read SQL file
    if (!file_exists($filename)) {
        showMessage("File SQL tidak ditemukan: $filename", true);
        return false;
    }

    $sql = file_get_contents($filename);
    if (!$sql) {
        showMessage("Tidak dapat membaca file SQL: $filename", true);
        return false;
    }

    // Connect to MySQL server (without selecting a database)
    $conn = new mysqli($db_host, $db_user, $db_pass);
    if ($conn->connect_error) {
        showMessage("Koneksi gagal: " . $conn->connect_error, true);
        return false;
    }

    // Create database if it doesn't exist
    if (!$conn->query("CREATE DATABASE IF NOT EXISTS `$db_name`")) {
        showMessage("Gagal membuat database: " . $conn->error, true);
        return false;
    }

    // Select the database
    if (!$conn->select_db($db_name)) {
        showMessage("Gagal memilih database: " . $conn->error, true);
        return false;
    }

    // Enable logging of all queries
    $conn->query("SET profiling = 1");

    // A completely different approach to handle stored procedures and triggers

    // First, let's identify all the trigger and procedure blocks
    $sql_lines = explode("\n", $sql);
    $in_procedure = false;
    $current_procedure = '';
    $procedures = [];
    $clean_sql_lines = [];

    foreach ($sql_lines as $line) {
        // Check if this line starts a DELIMITER block
        if (preg_match('/^\s*DELIMITER\s+/i', $line)) {
            $in_procedure = true;
            $current_procedure = '';
            continue;
        }

        // Check if this line ends a DELIMITER block
        if ($in_procedure && preg_match('/^\s*DELIMITER\s+;/i', $line)) {
            $in_procedure = false;
            if (!empty($current_procedure)) {
                $procedures[] = $current_procedure;
            }
            continue;
        }

        // If we're in a procedure/trigger block, add to current procedure
        if ($in_procedure) {
            $current_procedure .= $line . "\n";
        } else {
            // Otherwise, add to the clean SQL lines
            $clean_sql_lines[] = $line;
        }
    }

    // Rebuild the SQL without the procedure/trigger blocks
    $sql = implode("\n", $clean_sql_lines);

    // Process the procedures to make them executable
    foreach ($procedures as $key => $proc) {
        // Remove any DELIMITER statements within the procedure
        $proc = preg_replace('/^\s*DELIMITER\s+.*$/im', '', $proc);

        // Replace $$ or // with ; if they're being used as statement terminators
        $proc = preg_replace('/\$\$/', ';', $proc);
        $proc = preg_replace('/\/\//', ';', $proc);

        $procedures[$key] = $proc;
    }

    // Split SQL file into individual commands
    $commands = explode(';', $sql);
    $totalCommands = count($commands) + count($procedures);
    $successCount = 0;
    $errorCount = 0;
    $errors = [];
    $executed_commands = [];
    $start_time = microtime(true);

    // Execute each command
    foreach ($commands as $command) {
        $command = trim($command);
        if (empty($command)) continue;

        // Skip DELIMITER commands
        if (preg_match('/^\s*DELIMITER/i', $command)) {
            $successCount++;
            $executed_commands[] = [
                'command' => substr($command, 0, 100) . (strlen($command) > 100 ? '...' : ''),
                'status' => 'Skipped (DELIMITER command)',
                'time' => '0 ms',
                'affected_rows' => 0
            ];
            continue;
        }

        $cmd_start_time = microtime(true);
        $success = $conn->query($command);
        $cmd_end_time = microtime(true);
        $execution_time = round(($cmd_end_time - $cmd_start_time) * 1000, 2); // in milliseconds

        if ($success) {
            $successCount++;
            $executed_commands[] = [
                'command' => substr($command, 0, 100) . (strlen($command) > 100 ? '...' : ''),
                'status' => 'Success',
                'time' => $execution_time . ' ms',
                'affected_rows' => $conn->affected_rows
            ];
        } else {
            $errorCount++;
            $errors[] = "Error mengeksekusi perintah: " . $conn->error . "\nPerintah: " . substr($command, 0, 100) . "...";
            $executed_commands[] = [
                'command' => substr($command, 0, 100) . (strlen($command) > 100 ? '...' : ''),
                'status' => 'Error: ' . $conn->error,
                'time' => $execution_time . ' ms',
                'affected_rows' => 0
            ];
        }
    }

    // Now execute any stored procedures, functions, or triggers we extracted earlier
    foreach ($procedures as $procedure) {
        // Skip empty procedures
        if (trim($procedure) == '') {
            continue;
        }

        // Extract the type of procedure for better logging
        $proc_type = "Stored Procedure/Trigger";
        if (preg_match('/CREATE\s+(DEFINER\s*=\s*[^\s]+\s+)?(PROCEDURE|FUNCTION|TRIGGER)/i', $procedure, $matches)) {
            $proc_type = $matches[2];
        }

        // Create a new connection for each procedure to avoid issues with previous queries
        $proc_conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
        if ($proc_conn->connect_error) {
            $errorCount++;
            $errors[] = "Error connecting to database for procedure execution: " . $proc_conn->connect_error;
            continue;
        }

        $cmd_start_time = microtime(true);

        // Try to execute the procedure
        try {
            // For triggers, we need to handle them differently
            if (stripos($procedure, 'CREATE TRIGGER') !== false) {
                // For triggers, we need to make sure we have the correct syntax
                // Remove any trailing semicolons and ensure proper END keyword
                $procedure = preg_replace('/END\s*;?\s*$/i', 'END', $procedure);

                // Extract the trigger name for better error reporting
                preg_match('/CREATE\s+(DEFINER\s*=\s*[^\s]+\s+)?TRIGGER\s+`?([^`\s]+)`?/i', $procedure, $trigger_matches);
                $trigger_name = isset($trigger_matches[2]) ? $trigger_matches[2] : 'unknown';

                // Check if the trigger already exists and drop it if it does
                $check_trigger = $proc_conn->query("SHOW TRIGGERS LIKE '$trigger_name'");
                if ($check_trigger && $check_trigger->num_rows > 0) {
                    $proc_conn->query("DROP TRIGGER IF EXISTS `$trigger_name`");
                }

                // Execute the trigger creation
                $success = $proc_conn->query($procedure);
            } else {
                // For stored procedures and functions
                // Extract the name for better error reporting
                preg_match('/CREATE\s+(DEFINER\s*=\s*[^\s]+\s+)?(PROCEDURE|FUNCTION)\s+`?([^`\s(]+)`?/i', $procedure, $proc_matches);
                $proc_name = isset($proc_matches[3]) ? $proc_matches[3] : 'unknown';
                $proc_type_lower = isset($proc_matches[2]) ? strtolower($proc_matches[2]) : 'procedure';

                // Check if it already exists and drop it if it does
                $check_proc = $proc_conn->query("SHOW $proc_type_lower STATUS WHERE Name = '$proc_name'");
                if ($check_proc && $check_proc->num_rows > 0) {
                    $proc_conn->query("DROP $proc_type_lower IF EXISTS `$proc_name`");
                }

                // Execute the procedure/function creation
                $success = $proc_conn->multi_query($procedure);

                // Clear any result sets
                while ($proc_conn->more_results()) {
                    $proc_conn->next_result();
                }
            }

            $cmd_end_time = microtime(true);
            $execution_time = round(($cmd_end_time - $cmd_start_time) * 1000, 2); // in milliseconds

            if ($success) {
                $successCount++;
                $executed_commands[] = [
                    'command' => substr($procedure, 0, 100) . (strlen($procedure) > 100 ? '...' : ''),
                    'status' => "Success ($proc_type)",
                    'time' => $execution_time . ' ms',
                    'affected_rows' => 0
                ];
            } else {
                $errorCount++;
                $errors[] = "Error executing $proc_type: " . $proc_conn->error . "\nCommand: " . substr($procedure, 0, 100) . "...";
                $executed_commands[] = [
                    'command' => substr($procedure, 0, 100) . (strlen($procedure) > 100 ? '...' : ''),
                    'status' => 'Error: ' . $proc_conn->error,
                    'time' => $execution_time . ' ms',
                    'affected_rows' => 0
                ];
            }
        } catch (Exception $e) {
            $cmd_end_time = microtime(true);
            $execution_time = round(($cmd_end_time - $cmd_start_time) * 1000, 2);

            $errorCount++;
            $errors[] = "Exception executing $proc_type: " . $e->getMessage() . "\nCommand: " . substr($procedure, 0, 100) . "...";
            $executed_commands[] = [
                'command' => substr($procedure, 0, 100) . (strlen($procedure) > 100 ? '...' : ''),
                'status' => 'Exception: ' . $e->getMessage(),
                'time' => $execution_time . ' ms',
                'affected_rows' => 0
            ];
        }

        // Close the procedure connection
        $proc_conn->close();
    }

    $total_time = round(microtime(true) - $start_time, 2);

    // Get profiling information
    $profiling = [];
    $profile_result = $conn->query("SHOW PROFILES");
    if ($profile_result) {
        while ($row = $profile_result->fetch_assoc()) {
            $profiling[] = $row;
        }
    }

    // Close connection
    $conn->close();

    // Return results
    return [
        'total' => $totalCommands,
        'success' => $successCount,
        'errors' => $errorCount,
        'error_messages' => $errors,
        'executed_commands' => $executed_commands,
        'total_time' => $total_time,
        'profiling' => $profiling
    ];
}

// HTML header
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengaturan Database</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }
        h2 {
            color: #444;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
        }
        h3 {
            color: #555;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .step {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .step h2 {
            margin-top: 0;
            color: #444;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
        }
        .success {
            color: #2e7d32;
            font-weight: bold;
        }
        .warning {
            color: #ff8f00;
            font-weight: bold;
        }
        .error {
            color: #c62828;
            font-weight: bold;
        }
        .info-box {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .warning-box {
            background-color: #fff8e1;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .error-box {
            background-color: #ffebee;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #f44336;
        }
        .success-box {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        .error-details {
            background-color: #ffebee;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ffcdd2;
        }
        .code {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            border: 1px solid #ddd;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #a50000;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin-top: 20px;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .button:hover {
            background-color: #8b0000;
        }
        .button-secondary {
            background-color: #757575;
        }
        .button-secondary:hover {
            background-color: #616161;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
            padding: 10px;
            text-align: left;
        }
        td {
            padding: 8px 10px;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .collapsible {
            background-color: #f1f1f1;
            color: #444;
            cursor: pointer;
            padding: 18px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 15px;
            border-radius: 5px;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .active, .collapsible:hover {
            background-color: #e0e0e0;
        }
        .collapsible:after {
            content: '\002B';
            color: #777;
            font-weight: bold;
            float: right;
            margin-left: 5px;
        }
        .active:after {
            content: "\2212";
        }
        .content {
            padding: 0 18px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.2s ease-out;
            background-color: #fafafa;
            border-radius: 0 0 5px 5px;
        }
        .badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            margin-left: 5px;
        }
        .badge-success {
            background-color: #4caf50;
        }
        .badge-error {
            background-color: #f44336;
        }
        .badge-warning {
            background-color: #ff9800;
        }
        .badge-info {
            background-color: #2196f3;
        }
        .progress-container {
            width: 100%;
            background-color: #f1f1f1;
            border-radius: 5px;
            margin: 10px 0;
        }
        .progress-bar {
            height: 20px;
            border-radius: 5px;
            text-align: center;
            line-height: 20px;
            color: white;
            font-weight: bold;
        }
        .progress-success {
            background-color: #4caf50;
        }
        .progress-error {
            background-color: #f44336;
        }
        .progress-warning {
            background-color: #ff9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Pengaturan Database</h1>

        <?php
        // Check if action is specified in URL
        if (isset($_GET['action'])) {
            if ($_GET['action'] === 'import') {
            // Redirect to import page
            echo '<div class="step">';
            echo '<h2>Import Database</h2>';

            // Check MySQL server status
            $mysql_status = checkMySqlStatus($db_host, $db_user, $db_pass);

            if ($mysql_status['status']) {
                // Get list of backup files
                $backup_files = getBackupFiles();

                if (empty($backup_files)) {
                    echo '<div class="warning-box">';
                    echo '<p><strong>Peringatan:</strong> Tidak ada file backup yang ditemukan.</p>';
                    echo '<p>Silakan buat backup terlebih dahulu atau upload file backup ke direktori <span class="code">backups/</span>.</p>';
                    echo '</div>';

                    echo '<div style="text-align: center; margin-top: 20px;">';
                    echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
                    echo '</div>';
                } else {
                    echo '<div class="info-box">';
                    echo '<p>Pilih file backup yang ingin diimport:</p>';
                    echo '</div>';

                    echo '<form method="post" action="setupdb.php">'; // Mengubah action menjadi URL absolut tanpa parameter
                    echo '<table>';
                    echo '<tr><th>Pilih</th><th>Nama File</th><th>Ukuran</th><th>Tanggal</th></tr>';

                    foreach ($backup_files as $i => $file) {
                        echo '<tr>';
                        echo '<td style="text-align: center;"><input type="radio" name="backup_file" value="' . htmlspecialchars($file['path']) . '" ' . ($i === 0 ? 'checked' : '') . '></td>';
                        echo '<td>' . htmlspecialchars($file['name']) . '</td>';
                        echo '<td>' . htmlspecialchars($file['size']) . '</td>';
                        echo '<td>' . htmlspecialchars($file['date']) . '</td>';
                        echo '</tr>';
                    }

                    echo '</table>';

                    echo '<div class="warning-box" style="margin-top: 20px;">';
                    echo '<p><strong>Peringatan:</strong> Mengimport database akan menimpa data yang sudah ada.</p>';
                    echo '<p>Pastikan Anda telah membuat backup data penting sebelum melanjutkan.</p>';
                    echo '</div>';

                    echo '<div class="info-box">';
                    echo '<p><strong>Catatan:</strong> Jika Anda mengalami masalah <strong>foreign key constraint</strong> saat mengimpor database, gunakan <a href="import_fix.php" style="color: #2196f3; font-weight: bold;">Alat Perbaikan Import Database</a>.</p>';
                    echo '</div>';

                    echo '<div style="text-align: center; margin-top: 20px;">';
                    echo '<button type="submit" name="confirm_import" class="button" style="background-color: #ff9800;">Mulai Import</button> ';
                    echo '<a href="setupdb.php" class="button button-secondary">Batal</a> ';
                    echo '<a href="import_fix.php" class="button" style="background-color: #2196f3;">Alat Perbaikan Import</a>';
                    echo '</div>';
                    echo '</form>';
                }
            } else {
                echo '<div class="error-box">';
                echo '<p><strong>Status Koneksi:</strong> <span class="error">Gagal</span></p>';
                echo '<p><strong>Error:</strong> ' . htmlspecialchars($mysql_status['message']) . '</p>';
                echo '</div>';

                echo '<div style="text-align: center; margin-top: 20px;">';
                echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
                echo '</div>';
            }

            echo '</div>';
            } else if ($_GET['action'] === 'view_backup' && isset($_GET['file'])) {
                // View backup file details
                $file_path = $_GET['file'];

                // Security check - make sure file is in backups directory
                $backup_dir = realpath('backups');
                $file_real_path = realpath($file_path);

                if (!$file_real_path || strpos($file_real_path, $backup_dir) !== 0) {
                    echo '<div class="step">';
                    echo '<h2>Lihat Detail Backup</h2>';
                    echo '<div class="error-box">';
                    echo '<p><strong>Error:</strong> File tidak valid atau tidak berada di direktori backup.</p>';
                    echo '</div>';
                    echo '<div style="text-align: center; margin-top: 20px;">';
                    echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
                    echo '</div>';
                    echo '</div>';
                } else {
                    // Get file info
                    $file_name = basename($file_path);
                    $size_bytes = filesize($file_path);
                    $size_kb = round($size_bytes / 1024, 2);
                    $size_mb = round($size_kb / 1024, 2);
                    $size_str = $size_mb >= 1 ? "$size_mb MB" : "$size_kb KB";
                    $date = date('Y-m-d H:i:s', filemtime($file_path));

                    // Get database info
                    $db_info = getBackupFileInfo($file_path);

                    echo '<div class="step">';
                    echo '<h2>Detail File Backup</h2>';

                    echo '<div class="info-box">';
                    echo '<h3>Informasi File</h3>';
                    echo '<p><strong>Nama File:</strong> ' . htmlspecialchars($file_name) . '</p>';
                    echo '<p><strong>Ukuran:</strong> ' . htmlspecialchars($size_str) . ' (' . number_format($size_bytes) . ' bytes)</p>';
                    echo '<p><strong>Tanggal:</strong> ' . htmlspecialchars($date) . '</p>';
                    echo '<p><strong>Lokasi:</strong> ' . htmlspecialchars($file_path) . '</p>';
                    echo '</div>';

                    echo '<div class="info-box">';
                    echo '<h3>Informasi Database</h3>';
                    echo '<p><strong>Nama Database:</strong> ' . htmlspecialchars($db_info['db_name']) . '</p>';
                    echo '<p><strong>Jumlah Tabel:</strong> ' . htmlspecialchars($db_info['table_count']) . '</p>';

                    if (!empty($db_info['tables'])) {
                        echo '<button type="button" class="collapsible">Tampilkan Daftar Tabel</button>';
                        echo '<div class="content">';
                        echo '<table>';
                        echo '<tr><th>#</th><th>Nama Tabel</th></tr>';
                        foreach ($db_info['tables'] as $i => $table) {
                            echo '<tr><td>' . ($i + 1) . '</td><td>' . htmlspecialchars($table) . '</td></tr>';
                        }
                        echo '</table>';
                        echo '</div>';
                    }
                    echo '</div>';

                    // Show file content preview
                    echo '<div class="info-box">';
                    echo '<h3>Pratinjau Isi File</h3>';

                    // Read first 20 lines of the file
                    $file_handle = fopen($file_path, 'r');
                    $line_count = 0;
                    $max_lines = 20;
                    $file_preview = '';

                    if ($file_handle) {
                        while (($line = fgets($file_handle)) !== false && $line_count < $max_lines) {
                            $file_preview .= htmlspecialchars($line);
                            $line_count++;
                        }
                        fclose($file_handle);
                    }

                    echo '<div class="error-details" style="background-color: #f5f5f5; border: 1px solid #ddd;">';
                    echo $file_preview;
                    if ($line_count >= $max_lines) {
                        echo '<p style="color: #757575; font-style: italic;">... (file terlalu besar untuk ditampilkan seluruhnya)</p>';
                    }
                    echo '</div>';
                    echo '</div>';

                    echo '<div style="text-align: center; margin-top: 20px;">';
                    echo '<a href="setupdb.php?action=manage_backups" class="button">Kembali ke Kelola Database</a> ';
                    echo '</div>';
                    echo '</div>';
                }
            } else if ($_GET['action'] === 'manage_backups') {
                // Manage backup files
                echo '<div class="step">';
                echo '<h2>Kelola Database</h2>';

                // Check MySQL server status
                $mysql_status = checkMySqlStatus($db_host, $db_user, $db_pass);

                if (!$mysql_status['status']) {
                    echo '<div class="error-box">';
                    echo '<p><strong>Status Koneksi:</strong> <span class="error">Gagal</span></p>';
                    echo '<p><strong>Error:</strong> ' . htmlspecialchars($mysql_status['message']) . '</p>';
                    echo '</div>';

                    echo '<div style="text-align: center; margin-top: 20px;">';
                    echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
                    echo '</div>';
                } else {
                    // Check if database exists
                    $db_status = checkDatabaseExists($db_host, $db_user, $db_pass, $db_name);

                    if (!$db_status['status'] || !$db_status['exists']) {
                        echo '<div class="warning-box">';
                        echo '<p><strong>Database:</strong> <span class="error">Tidak Ditemukan</span></p>';
                        echo '<p>Database <span class="code">' . htmlspecialchars($db_name) . '</span> belum dibuat atau tidak dapat diakses.</p>';
                        echo '</div>';

                        echo '<div style="text-align: center; margin-top: 20px;">';
                        echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
                        echo '</div>';
                    } else {
                        // Database exists, show details
                        echo '<div class="info-box">';
                        echo '<h3>Informasi Database</h3>';
                        echo '<p><strong>Nama Database:</strong> <span class="code">' . htmlspecialchars($db_name) . '</span></p>';
                        echo '<p><strong>Ukuran Database:</strong> ' . htmlspecialchars($db_status['size']) . ' MB</p>';
                        echo '<p><strong>Jumlah Tabel:</strong> ' . htmlspecialchars($db_status['table_count']) . '</p>';
                        echo '</div>';

                        // Show tables
                        if (!empty($db_status['tables'])) {
                            echo '<h3>Daftar Tabel</h3>';
                            echo '<table>';
                            echo '<tr><th>#</th><th>Nama Tabel</th><th>Jumlah Baris</th><th>Ukuran</th></tr>';

                            // Connect to database to get table details
                            $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

                            foreach ($db_status['tables'] as $i => $table) {
                                // Get row count
                                $row_count_query = $conn->query("SELECT COUNT(*) as count FROM `$table`");
                                $row_count = $row_count_query ? $row_count_query->fetch_assoc()['count'] : 'N/A';

                                // Get table size
                                $size_query = $conn->query("SELECT
                                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                                    FROM information_schema.TABLES
                                    WHERE table_schema = '$db_name'
                                    AND table_name = '$table'");
                                $size = $size_query ? $size_query->fetch_assoc()['size_mb'] . ' MB' : 'N/A';

                                echo '<tr>';
                                echo '<td>' . ($i + 1) . '</td>';
                                echo '<td><span class="code">' . htmlspecialchars($table) . '</span></td>';
                                echo '<td>' . number_format($row_count) . '</td>';
                                echo '<td>' . htmlspecialchars($size) . '</td>';
                                echo '</tr>';
                            }

                            $conn->close();

                            echo '</table>';
                        }

                        // Get list of backup files
                        $backup_files = getBackupFiles();

                        echo '<h3>File Backup Tersedia</h3>';

                        if (empty($backup_files)) {
                            echo '<div class="warning-box">';
                            echo '<p><strong>Peringatan:</strong> Tidak ada file backup yang ditemukan.</p>';
                            echo '<p>Silakan buat backup terlebih dahulu.</p>';
                            echo '</div>';
                        } else {
                            echo '<div class="info-box">';
                            echo '<p>Klik pada nama file backup untuk melihat detail.</p>';
                            echo '</div>';

                            echo '<table>';
                            echo '<tr><th>Nama File</th><th>Database</th><th>Jumlah Tabel</th><th>Ukuran</th><th>Tanggal</th></tr>';

                            foreach ($backup_files as $file) {
                                echo '<tr class="backup-row" style="cursor: pointer;" onclick="window.location.href=\'setupdb.php?action=view_backup&file=' . urlencode($file['path']) . '\'">';
                                echo '<td><strong>' . htmlspecialchars($file['name']) . '</strong></td>';
                                echo '<td>' . htmlspecialchars($file['db_name']) . '</td>';
                                echo '<td>' . htmlspecialchars($file['table_count']) . '</td>';
                                echo '<td>' . htmlspecialchars($file['size']) . '</td>';
                                echo '<td>' . htmlspecialchars($file['date']) . '</td>';
                                echo '</tr>';
                            }

                            echo '</table>';

                            // Add some CSS for hover effect
                            echo '<style>
                                .backup-row:hover {
                                    background-color: #f5f5f5;
                                }
                            </style>';
                        }

                        // Backup button
                        echo '<div style="text-align: center; margin-top: 20px;">';
                        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
                        echo '</form>';

                        echo '<a href="setupdb.php" class="button button-secondary">Kembali ke Pengaturan Database</a>';
                        echo '</div>';
                    }
                }

                echo '</div>';
            }
        }
        // Check if form is submitted
        else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Check if backup request
            if (isset($_POST['backup_database'])) {
                // Check MySQL server status first
                $mysql_status = checkMySqlStatus($db_host, $db_user, $db_pass);

                if ($mysql_status['status']) {
                    // Perform database backup
                    $backup_result = backupDatabase($db_host, $db_user, $db_pass, $db_name);

                    echo '<div class="step">';
                    echo '<h2>Backup Database</h2>';

                    if ($backup_result['success']) {
                        echo '<div class="success-box">';
                        echo '<p><strong>Status:</strong> <span class="success">Berhasil</span></p>';
                        echo '<p><strong>File Backup:</strong> ' . htmlspecialchars($backup_result['file']) . '</p>';
                        echo '<p><strong>Ukuran:</strong> ' . htmlspecialchars($backup_result['size']) . '</p>';
                        echo '<p><strong>Waktu:</strong> ' . htmlspecialchars($backup_result['timestamp']) . '</p>';
                        echo '</div>';

                        echo '<div style="text-align: center; margin-top: 20px;">';
                        echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
                        echo '</div>';
                    } else {
                        echo '<div class="error-box">';
                        echo '<p><strong>Status:</strong> <span class="error">Gagal</span></p>';
                        echo '<p><strong>Pesan:</strong> ' . htmlspecialchars($backup_result['message']) . '</p>';

                        if (isset($backup_result['output'])) {
                            echo '<button type="button" class="collapsible">Tampilkan Detail Error</button>';
                            echo '<div class="content">';
                            echo '<div class="error-details">';
                            echo htmlspecialchars($backup_result['output']);
                            echo '</div>';
                            echo '</div>';
                        }

                        echo '</div>';

                        echo '<div style="text-align: center; margin-top: 20px;">';
                        echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
                        echo '</div>';
                    }

                    echo '</div>';

                } else {
                    echo '<div class="step">';
                    echo '<h2>Backup Database</h2>';
                    echo '<div class="error-box">';
                    echo '<p><strong>Status Koneksi:</strong> <span class="error">Gagal</span></p>';
                    echo '<p><strong>Error:</strong> ' . htmlspecialchars($mysql_status['message']) . '</p>';
                    echo '</div>';

                    echo '<div style="text-align: center; margin-top: 20px;">';
                    echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
                    echo '</div>';
                    echo '</div>';
                }
            }
            // Check if import request
            else if (isset($_POST['import_database'])) {
                // Check MySQL server status first
                $mysql_status = checkMySqlStatus($db_host, $db_user, $db_pass);

                if ($mysql_status['status']) {
                    echo '<div class="step">';
                    echo '<h2>Import Database</h2>';

                    // Get list of backup files
                    $backup_files = getBackupFiles();

                    if (empty($backup_files)) {
                        echo '<div class="warning-box">';
                        echo '<p><strong>Peringatan:</strong> Tidak ada file backup yang ditemukan.</p>';
                        echo '<p>Silakan buat backup terlebih dahulu atau upload file backup ke direktori <span class="code">backups/</span>.</p>';
                        echo '</div>';

                        echo '<div style="text-align: center; margin-top: 20px;">';
                        echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
                        echo '</div>';
                    } else {
                        echo '<div class="info-box">';
                        echo '<p>Pilih file backup yang ingin diimport:</p>';
                        echo '</div>';

                        echo '<form method="post" action="setupdb.php">'; // Mengubah action menjadi URL absolut tanpa parameter
                        echo '<table>';
                        echo '<tr><th>Pilih</th><th>Nama File</th><th>Ukuran</th><th>Tanggal</th></tr>';

                        foreach ($backup_files as $i => $file) {
                            echo '<tr>';
                            echo '<td style="text-align: center;"><input type="radio" name="backup_file" value="' . htmlspecialchars($file['path']) . '" ' . ($i === 0 ? 'checked' : '') . '></td>';
                            echo '<td>' . htmlspecialchars($file['name']) . '</td>';
                            echo '<td>' . htmlspecialchars($file['size']) . '</td>';
                            echo '<td>' . htmlspecialchars($file['date']) . '</td>';
                            echo '</tr>';
                        }

                        echo '</table>';

                        echo '<div class="warning-box" style="margin-top: 20px;">';
                        echo '<p><strong>Peringatan:</strong> Mengimport database akan menimpa data yang sudah ada.</p>';
                        echo '<p>Pastikan Anda telah membuat backup data penting sebelum melanjutkan.</p>';
                        echo '</div>';

                        echo '<div class="info-box">';
                        echo '<p><strong>Catatan:</strong> Jika Anda mengalami masalah <strong>foreign key constraint</strong> saat mengimpor database, gunakan <a href="import_fix.php" style="color: #2196f3; font-weight: bold;">Alat Perbaikan Import Database</a>.</p>';
                        echo '</div>';

                        echo '<div style="text-align: center; margin-top: 20px;">';
                        echo '<button type="submit" name="confirm_import" class="button" style="background-color: #ff9800;">Mulai Import</button> ';
                        echo '<a href="setupdb.php" class="button button-secondary">Batal</a> ';
                        echo '<a href="import_fix.php" class="button" style="background-color: #2196f3;">Alat Perbaikan Import</a>';
                        echo '</div>';
                        echo '</form>';
                    }

                    echo '</div>';
                } else {
                    echo '<div class="step">';
                    echo '<h2>Import Database</h2>';
                    echo '<div class="error-box">';
                    echo '<p><strong>Status Koneksi:</strong> <span class="error">Gagal</span></p>';
                    echo '<p><strong>Error:</strong> ' . htmlspecialchars($mysql_status['message']) . '</p>';
                    echo '</div>';

                    echo '<div style="text-align: center; margin-top: 20px;">';
                    echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
                    echo '</div>';
                    echo '</div>';
                }
            }
            // Check if delete backup request
            else if (isset($_POST['confirm_delete']) && isset($_POST['delete_file'])) {
                $file_path = $_POST['delete_file'];

                echo '<div class="step">';
                echo '<h2>Hapus File Backup</h2>';

                // Delete the file
                $delete_result = deleteBackupFile($file_path);

                if ($delete_result['success']) {
                    echo '<div class="success-box">';
                    echo '<p><strong>Status:</strong> <span class="success">Berhasil</span></p>';
                    echo '<p><strong>Pesan:</strong> ' . htmlspecialchars($delete_result['message']) . '</p>';
                    echo '</div>';
                } else {
                    echo '<div class="error-box">';
                    echo '<p><strong>Status:</strong> <span class="error">Gagal</span></p>';
                    echo '<p><strong>Pesan:</strong> ' . htmlspecialchars($delete_result['message']) . '</p>';

                    if (isset($delete_result['debug'])) {
                        echo '<button type="button" class="collapsible">Tampilkan Detail Debug</button>';
                        echo '<div class="content">';
                        echo '<div class="error-details">';
                        foreach ($delete_result['debug'] as $debug_line) {
                            echo htmlspecialchars($debug_line) . "\n";
                        }
                        echo '</div>';
                        echo '</div>';
                    }

                    echo '</div>';
                }

                echo '<div style="text-align: center; margin-top: 20px;">';
                echo '<a href="setupdb.php?action=manage_backups" class="button">Kembali ke Kelola Backup</a> ';
                echo '<a href="setupdb.php" class="button button-secondary">Kembali ke Pengaturan Database</a>';
                echo '</div>';

                echo '</div>';
            }
            // Check if confirm import request
            else if (isset($_POST['confirm_import']) && isset($_POST['backup_file'])) {
                $backup_file = $_POST['backup_file'];

                // Check MySQL server status first
                $mysql_status = checkMySqlStatus($db_host, $db_user, $db_pass);

                if ($mysql_status['status']) {
                    echo '<div class="step">';
                    echo '<h2>Import Database</h2>';

                    // Perform database import
                    $import_result = importDatabase($backup_file, $db_host, $db_user, $db_pass, $db_name);

                    if ($import_result['success']) {
                        echo '<div class="success-box">';
                        echo '<p><strong>Status:</strong> <span class="success">Berhasil</span></p>';
                        echo '<p><strong>Pesan:</strong> ' . htmlspecialchars($import_result['message']) . '</p>';
                        echo '<p><strong>Waktu Eksekusi:</strong> ' . htmlspecialchars($import_result['time']) . ' detik</p>';

                        if (isset($import_result['table_count'])) {
                            echo '<p><strong>Jumlah Tabel:</strong> ' . htmlspecialchars($import_result['table_count']) . '</p>';
                        }

                        if (isset($import_result['size'])) {
                            echo '<p><strong>Ukuran Database:</strong> ' . htmlspecialchars($import_result['size']) . ' MB</p>';
                        }

                        // Tampilkan metode yang digunakan
                        if (isset($import_result['method'])) {
                            $method_name = $import_result['method'] === 'command_line' ? 'Command Line MySQL' : 'PHP Line-by-Line Parser';
                            $method_icon = $import_result['method'] === 'command_line' ? '⚡' : '🔄';
                            echo '<p><strong>Metode Import:</strong> ' . $method_icon . ' ' . $method_name . '</p>';
                        }

                        echo '</div>';

                        echo '<div class="info-box">';
                        echo '<h3>Import Database Berhasil</h3>';
                        echo '<p>Database telah berhasil diimpor dan siap digunakan. Anda dapat:</p>';
                        echo '<ol>';
                        echo '<li>Kembali ke halaman pengaturan database untuk melihat informasi database</li>';
                        echo '<li>Langsung menuju ke halaman login untuk mengakses aplikasi</li>';
                        echo '</ol>';
                        echo '</div>';

                        echo '<div style="text-align: center; margin-top: 20px;">';
                        echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a> ';
                        echo '<a href="view/login.php" class="button button-secondary">Ke Halaman Login</a>';
                        echo '</div>';
                    } else {
                        echo '<div class="error-box">';
                        echo '<p><strong>Status:</strong> <span class="error">Gagal</span></p>';
                        echo '<p><strong>Pesan:</strong> ' . htmlspecialchars($import_result['message']) . '</p>';

                        if (isset($import_result['output'])) {
                            echo '<button type="button" class="collapsible">Tampilkan Detail Error</button>';
                            echo '<div class="content">';
                            echo '<div class="error-details">';
                            echo htmlspecialchars($import_result['output']);
                            echo '</div>';
                            echo '</div>';
                        }

                        echo '</div>';

                        // Check if the error message contains foreign key constraint error
                        $is_foreign_key_error = false;
                        if (isset($import_result['output']) && (
                            strpos($import_result['output'], 'foreign key constraint fails') !== false ||
                            strpos($import_result['output'], 'Failed to open the referenced table') !== false
                        )) {
                            $is_foreign_key_error = true;
                            echo '<div class="warning-box">';
                            echo '<p><strong>Terdeteksi masalah foreign key constraint!</strong></p>';
                            echo '<p>Gunakan <a href="import_fix.php" style="color: #ff8f00; font-weight: bold;">Alat Perbaikan Import Database</a> untuk mengatasi masalah ini.</p>';
                            echo '</div>';
                        }

                        // Tambahkan informasi tentang metode alternatif
                        echo '<div class="info-box">';
                        echo '<h3>Solusi Alternatif</h3>';
                        echo '<p>Jika Anda mengalami masalah saat mengimpor database, coba salah satu dari metode berikut:</p>';
                        echo '<ol>';
                        echo '<li><strong>Gunakan Alat Perbaikan Import Database</strong> - Alat ini dapat menonaktifkan atau menghapus foreign key constraints yang menyebabkan error.</li>';
                        echo '<li><strong>Gunakan Script Import Alternatif</strong> - Kami telah membuat script import alternatif yang dapat mengatasi berbagai masalah import.</li>';
                        echo '</ol>';
                        echo '</div>';

                        echo '<div style="text-align: center; margin-top: 20px;">';
                        echo '<a href="setupdb.php?action=import" class="button">Coba Lagi</a> ';

                        if ($is_foreign_key_error) {
                            echo '<a href="import_fix.php" class="button" style="background-color: #ff9800;">Perbaiki Masalah Foreign Key</a> ';
                        }

                        echo '<a href="import_db.php" class="button" style="background-color: #2196f3;">Gunakan Import Alternatif</a> ';
                        echo '<a href="setupdb.php" class="button button-secondary">Kembali ke Pengaturan Database</a>';
                        echo '</div>';
                    }

                    echo '</div>';
                } else {
                    echo '<div class="step">';
                    echo '<h2>Import Database</h2>';
                    echo '<div class="error-box">';
                    echo '<p><strong>Status Koneksi:</strong> <span class="error">Gagal</span></p>';
                    echo '<p><strong>Error:</strong> ' . htmlspecialchars($mysql_status['message']) . '</p>';
                    echo '</div>';

                    echo '<div style="text-align: center; margin-top: 20px;">';
                    echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
                    echo '</div>';
                    echo '</div>';
                }
            } else {
                // Regular setup process
                // Check MySQL server status first
                $mysql_status = checkMySqlStatus($db_host, $db_user, $db_pass);

                echo '<div class="step">';
                echo '<h2>Status Server MySQL</h2>';

                if ($mysql_status['status']) {
                    echo '<div class="success-box">';
                    echo '<p><strong>Status Koneksi:</strong> <span class="success">Terhubung</span></p>';
                    echo '<p><strong>Informasi Server:</strong> ' . htmlspecialchars($mysql_status['server_info']) . '</p>';
                    echo '<p><strong>Informasi Host:</strong> ' . htmlspecialchars($mysql_status['host_info']) . '</p>';
                    echo '<p><strong>Versi MySQL:</strong> ' . htmlspecialchars($mysql_status['version']) . '</p>';
                    echo '<p><strong>Dapat Membuat Database:</strong> ' . ($mysql_status['can_create_db'] ? '<span class="success">Ya</span>' : '<span class="error">Tidak</span>') . '</p>';
                    echo '</div>';

                    // Check if database exists
                    $db_status = checkDatabaseExists($db_host, $db_user, $db_pass, $db_name);

                    echo '<h2>Status Database</h2>';

                    if ($db_status['status']) {
                        if ($db_status['exists']) {
                            echo '<div class="warning-box">';
                            echo '<p><strong>Database:</strong> ' . htmlspecialchars($db_name) . ' <span class="badge badge-warning">Sudah Ada</span></p>';
                            echo '<p><strong>Ukuran:</strong> ' . htmlspecialchars($db_status['size']) . ' MB</p>';
                            echo '<p><strong>Jumlah Tabel:</strong> ' . htmlspecialchars($db_status['table_count']) . '</p>';

                            if (!empty($db_status['tables'])) {
                                echo '<button type="button" class="collapsible">Tampilkan Tabel yang Ada</button>';
                                echo '<div class="content">';
                                echo '<table>';
                                echo '<tr><th>#</th><th>Nama Tabel</th></tr>';
                                foreach ($db_status['tables'] as $i => $table) {
                                    echo '<tr><td>' . ($i + 1) . '</td><td>' . htmlspecialchars($table) . '</td></tr>';
                                }
                                echo '</table>';
                                echo '</div>';
                            }

                            echo '<p class="warning">Peringatan: Database sudah ada. Menjalankan pengaturan ini akan memodifikasi tabel yang sudah ada. Sebaiknya backup database Anda terlebih dahulu.</p>';

                            // Add backup button
                            echo '<form method="post" action="" style="margin-top: 15px;">';
                            echo '<button type="submit" name="backup_database" class="button" style="background-color: #ff9800;">Backup Database Sekarang</button>';
                            echo '</form>';

                            echo '</div>';
                        } else {
                            echo '<div class="info-box">';
                            echo '<p><strong>Database:</strong> ' . htmlspecialchars($db_name) . ' <span class="badge badge-info">Akan Dibuat</span></p>';
                            echo '</div>';
                        }
                    } else {
                        echo '<div class="error-box">';
                        echo '<p><strong>Error:</strong> ' . htmlspecialchars($db_status['message']) . '</p>';
                        echo '</div>';
                    }
                } else {
                    echo '<div class="error-box">';
                    echo '<p><strong>Status Koneksi:</strong> <span class="error">Gagal</span></p>';
                    echo '<p><strong>Error:</strong> ' . htmlspecialchars($mysql_status['message']) . '</p>';
                    echo '</div>';

                    echo '<div style="text-align: center; margin-top: 20px;">';
                    echo '<a href="setupdb.php" class="button">Coba Lagi</a>';
                    echo '</div>';

                    echo '</div>';
                    exit;
                }
                echo '</div>';

                // Execute SQL file
                echo '<div class="step">';
                echo '<h2>Mengeksekusi Perintah SQL</h2>';

                $result = executeSqlFile('setupdb.sql', $db_host, $db_user, $db_pass, $db_name);

                if ($result) {
                    // Calculate success percentage
                    $success_percent = ($result['success'] / $result['total']) * 100;
                    $error_percent = ($result['errors'] / $result['total']) * 100;

                    echo '<div class="info-box">';
                    echo '<p><strong>Total Perintah:</strong> ' . $result['total'] . '</p>';
                    echo '<p><strong>Berhasil Dieksekusi:</strong> ' . $result['success'] . ' <span class="badge badge-success">' . round($success_percent, 1) . '%</span></p>';
                    echo '<p><strong>Error:</strong> ' . $result['errors'] . ($result['errors'] > 0 ? ' <span class="badge badge-error">' . round($error_percent, 1) . '%</span>' : '') . '</p>';
                    echo '<p><strong>Total Waktu Eksekusi:</strong> ' . $result['total_time'] . ' detik</p>';
                    echo '</div>';

                    // Show progress bar
                    echo '<div class="progress-container">';
                    echo '<div class="progress-bar progress-success" style="width:' . $success_percent . '%">' . round($success_percent, 1) . '%</div>';
                    echo '</div>';

                    if ($result['errors'] > 0) {
                        echo '<button type="button" class="collapsible">Tampilkan Detail Error</button>';
                        echo '<div class="content">';
                        echo '<div class="error-details">';
                        foreach ($result['error_messages'] as $error) {
                            echo htmlspecialchars($error) . "\n\n";
                        }
                        echo '</div>';
                        echo '</div>';
                    } else {
                        echo '<div class="success-box">';
                        echo '<p><strong>Sukses:</strong> Pengaturan database berhasil diselesaikan!</p>';
                        echo '</div>';
                    }

                    // Show executed commands
                    echo '<button type="button" class="collapsible">Tampilkan Perintah yang Dieksekusi</button>';
                    echo '<div class="content">';
                    echo '<table>';
                    echo '<tr><th>#</th><th>Perintah</th><th>Status</th><th>Waktu</th><th>Baris Terpengaruh</th></tr>';
                    foreach ($result['executed_commands'] as $i => $cmd) {
                        $status_class = strpos($cmd['status'], 'Error') !== false ? 'error' : 'success';
                        echo '<tr>';
                        echo '<td>' . ($i + 1) . '</td>';
                        echo '<td><div class="code">' . htmlspecialchars($cmd['command']) . '</div></td>';
                        echo '<td class="' . $status_class . '">' . htmlspecialchars($cmd['status']) . '</td>';
                        echo '<td>' . htmlspecialchars($cmd['time']) . '</td>';
                        echo '<td>' . htmlspecialchars($cmd['affected_rows']) . '</td>';
                        echo '</tr>';
                    }
                    echo '</table>';
                    echo '</div>';

                    // Show profiling information
                    if (!empty($result['profiling'])) {
                        echo '<button type="button" class="collapsible">Tampilkan Profil Query</button>';
                        echo '<div class="content">';
                        echo '<table>';
                        echo '<tr><th>ID Query</th><th>Durasi</th><th>Query</th></tr>';
                        foreach ($result['profiling'] as $profile) {
                            echo '<tr>';
                            echo '<td>' . htmlspecialchars($profile['Query_ID']) . '</td>';
                            echo '<td>' . htmlspecialchars($profile['Duration']) . '</td>';
                            echo '<td><div class="code">' . htmlspecialchars($profile['Query']) . '</div></td>';
                            echo '</tr>';
                        }
                        echo '</table>';
                        echo '</div>';
                    }

                    // Check final database status
                    $final_db_status = checkDatabaseExists($db_host, $db_user, $db_pass, $db_name);

                    if ($final_db_status['status'] && $final_db_status['exists']) {
                        echo '<h3>Status Database Akhir</h3>';
                        echo '<div class="success-box">';
                        echo '<p><strong>Database:</strong> ' . htmlspecialchars($db_name) . ' <span class="badge badge-success">Berhasil Dibuat</span></p>';
                        echo '<p><strong>Ukuran:</strong> ' . htmlspecialchars($final_db_status['size']) . ' MB</p>';
                        echo '<p><strong>Jumlah Tabel:</strong> ' . htmlspecialchars($final_db_status['table_count']) . '</p>';

                        if (!empty($final_db_status['tables'])) {
                            echo '<button type="button" class="collapsible">Tampilkan Tabel yang Dibuat</button>';
                            echo '<div class="content">';
                            echo '<table>';
                            echo '<tr><th>#</th><th>Nama Tabel</th></tr>';
                            foreach ($final_db_status['tables'] as $i => $table) {
                                echo '<tr><td>' . ($i + 1) . '</td><td>' . htmlspecialchars($table) . '</td></tr>';
                            }
                            echo '</table>';
                            echo '</div>';
                        }
                        echo '</div>';
                    }

                    // Show links
                    echo '<div style="text-align: center; margin-top: 20px;">';
                    echo '<a href="view/login.php" class="button">Ke Halaman Login</a> ';
                    echo '<a href="setupdb.php" class="button button-secondary">Jalankan Pengaturan Lagi</a>';
                    echo '</div>';
                } else {
                    echo '<div class="error-box">';
                    echo '<p><strong>Error:</strong> Gagal mengeksekusi perintah SQL.</p>';
                    echo '</div>';

                    echo '<div style="text-align: center; margin-top: 20px;">';
                    echo '<a href="setupdb.php" class="button">Coba Lagi</a>';
                    echo '</div>';
                }

                echo '</div>';
            }
        } else {
            // Show setup form
            ?>
            <div class="step">
                <h2>Selamat Datang di Pengaturan Database Sistem Training</h2>
                <p>Script ini akan membuat database dan semua tabel yang diperlukan untuk Sistem Training.</p>

                <?php
                // Check MySQL server status
                $mysql_status = checkMySqlStatus($db_host, $db_user, $db_pass);

                if ($mysql_status['status']) {
                    echo '<div class="success-box">';
                    echo '<p><strong>Koneksi MySQL:</strong> <span class="success">Terhubung</span></p>';
                    echo '<p><strong>Server:</strong> ' . htmlspecialchars($mysql_status['server_info']) . '</p>';
                    echo '<p><strong>Versi:</strong> ' . htmlspecialchars($mysql_status['version']) . '</p>';
                    echo '</div>';

                    // Check if database exists
                    $db_status = checkDatabaseExists($db_host, $db_user, $db_pass, $db_name);

                    if ($db_status['status']) {
                        if ($db_status['exists']) {
                            echo '<div class="warning-box">';
                            echo '<p><strong>Status Database:</strong> Database <span class="code">' . htmlspecialchars($db_name) . '</span> sudah ada.</p>';
                            echo '<p><strong>Jumlah Tabel:</strong> ' . htmlspecialchars($db_status['table_count']) . ' tabel ditemukan.</p>';
                            echo '<p class="warning">Peringatan: Menjalankan pengaturan ini akan memodifikasi tabel yang sudah ada. Sebaiknya backup database Anda terlebih dahulu.</p>';

                            // Add backup button
                            echo '<form method="post" action="" style="margin-top: 15px;">';
                            echo '<button type="submit" name="backup_database" class="button" style="background-color: #ff9800;">Backup Database Sekarang</button>';
                            echo '</form>';

                            echo '</div>';
                        } else {
                            echo '<div class="info-box">';
                            echo '<p><strong>Status Database:</strong> Database <span class="code">' . htmlspecialchars($db_name) . '</span> akan dibuat.</p>';
                            echo '</div>';
                        }
                    } else {
                        echo '<div class="error-box">';
                        echo '<p><strong>Error Pemeriksaan Database:</strong> ' . htmlspecialchars($db_status['message']) . '</p>';
                        echo '</div>';
                    }
                } else {
                    echo '<div class="error-box">';
                    echo '<p><strong>Error Koneksi MySQL:</strong> ' . htmlspecialchars($mysql_status['message']) . '</p>';
                    echo '<p>Silakan periksa apakah server MySQL Anda berjalan dan kredensial sudah benar.</p>';
                    echo '</div>';
                }
                ?>

                <div class="info-box">
                    <h3>Informasi Pengaturan</h3>
                    <p><strong>Host Database:</strong> <span class="code"><?php echo htmlspecialchars($db_host); ?></span></p>
                    <p><strong>Pengguna Database:</strong> <span class="code"><?php echo htmlspecialchars($db_user); ?></span></p>
                    <p><strong>Password Database:</strong> <span class="code">[Tersembunyi]</span></p>
                    <p><strong>Nama Database:</strong> <span class="code"><?php echo htmlspecialchars($db_name); ?></span></p>
                    <p><strong>File SQL:</strong> <span class="code">setupdb.sql</span></p>
                </div>

                <div style="text-align: center;">
                    <form method="post" action="" style="margin-bottom: 15px;">
                        <button type="submit" class="button" <?php echo !$mysql_status['status'] ? 'disabled' : ''; ?>>
                            Mulai Pengaturan Database
                        </button>
                    </form>

                    <form action="run_dump.php" method="POST">
    <button type="submit" class="button" <?php echo !$mysql_status['status'] ? 'disabled' : ''; ?>>
        Atur ulang setupdb.sql
    </button>
</form>

                    <button class="button">
                        <a href="sync_database.php" style="text-decoration: none; color: white;">Sinkronkan database</a>
                    </button>
                    <?php if ($mysql_status['status']) { ?>
                        <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                            <form method="post" action="">
                                <button type="submit" name="backup_database" class="button" style="background-color: #ff9800;">
                                    Backup Database
                                </button>
                            </form>

                            <form method="post" action="">
                                <button type="submit" name="import_database" class="button" style="background-color: #2196f3;">
                                    Import Database
                                </button>
                            </form>

                            <a href="setupdb.php?action=manage_backups" class="button" style="background-color: #4caf50;">
                                Kelola Database
                            </a>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <?php
        }
        ?>
    </div>

    <script>
    // JavaScript for collapsible sections
    document.addEventListener('DOMContentLoaded', function() {
        var coll = document.getElementsByClassName("collapsible");
        for (var i = 0; i < coll.length; i++) {
            coll[i].addEventListener("click", function() {
                this.classList.toggle("active");
                var content = this.nextElementSibling;
                if (content.style.maxHeight) {
                    content.style.maxHeight = null;
                } else {
                    content.style.maxHeight = content.scrollHeight + "px";
                }
            });
        }
    });
    </script>
</body>
</html>
