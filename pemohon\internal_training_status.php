<?php
include 'security.php';
include '../config/config.php';

$user_id = $_SESSION['user_id'];

// Get user's internal training submissions
$query = "SELECT ot.*, 
                 dh.name as dept_head_name,
                 lnd.name as lnd_name
          FROM offline_training ot
          LEFT JOIN users dh ON ot.approved_by_dept_head = dh.id
          LEFT JOIN users lnd ON ot.approved_by_lnd = lnd.id
          WHERE ot.created_by = ?
          ORDER BY ot.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$trainings = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    .status-container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
    }

    .status-header {
        background: linear-gradient(135deg, #c40000 0%, #a50000 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 30px;
    }

    .training-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }

    .training-header {
        padding: 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .training-content {
        padding: 20px;
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .status-pending-dept {
        background: #fff3cd;
        color: #856404;
    }

    .status-pending-lnd {
        background: #d1ecf1;
        color: #0c5460;
    }

    .status-approved {
        background: #d4edda;
        color: #155724;
    }

    .status-rejected {
        background: #f8d7da;
        color: #721c24;
    }

    .approval-timeline {
        display: flex;
        align-items: center;
        margin: 20px 0;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
    }

    .timeline-step {
        flex: 1;
        text-align: center;
        position: relative;
    }

    .timeline-step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 20px;
        right: -50%;
        width: 100%;
        height: 2px;
        background: #ddd;
        z-index: 1;
    }

    .timeline-step.completed::after {
        background: #28a745;
    }

    .timeline-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
        position: relative;
        z-index: 2;
    }

    .timeline-icon.pending {
        background: #ffc107;
        color: white;
    }

    .timeline-icon.completed {
        background: #28a745;
        color: white;
    }

    .timeline-icon.rejected {
        background: #dc3545;
        color: white;
    }

    .timeline-icon.waiting {
        background: #6c757d;
        color: white;
    }

    .detail-row {
        display: flex;
        margin-bottom: 10px;
    }

    .detail-label {
        font-weight: 600;
        min-width: 150px;
        color: #555;
    }

    .detail-value {
        flex: 1;
        color: #333;
    }

    .comments-section {
        background: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
        border-left: 4px solid #2196f3;
    }

    .no-data {
        text-align: center;
        padding: 50px;
        color: #6c757d;
    }

    @media (max-width: 768px) {
        .status-container {
            margin: 10px;
            padding: 10px;
        }
        
        .training-header {
            flex-direction: column;
            gap: 15px;
        }
        
        .approval-timeline {
            flex-direction: column;
            gap: 20px;
        }
        
        .timeline-step::after {
            display: none;
        }
    }
</style>

<body>
    <?php include '../config/navbara.php'; ?>
    <div class="jarak"></div>

    <div class="status-container">
        <div class="welcome-section">
            <h1><i class="fas fa-list-alt"></i> Status Training Internal</h1>
            <p>Pantau status pengajuan training internal Anda</p>
            <a href="internal_training_form.php" class="btn btn-primary">Kembali ke Dashboard</a>
        </div>

        <?php if (empty($trainings)): ?>
            <div class="no-data">
                <i class="fas fa-clipboard-list" style="font-size: 4rem; color: #ccc; margin-bottom: 20px;"></i>
                <h3>Belum ada pengajuan training internal</h3>
                <p>Anda belum pernah mengajukan training internal.</p>
                <a href="internal_training_form.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Ajukan Training Internal
                </a>
            </div>
        <?php else: ?>
            <?php foreach ($trainings as $training): ?>
                <div class="training-card">
                    <div class="training-header">
                        <div>
                            <h3 style="margin: 0; color: #333;">
                                <i class="fas fa-building"></i> <?= htmlspecialchars($training['training_topic']) ?>
                            </h3>
                            <small style="color: #666;">ID: #<?= $training['id'] ?> | Diajukan: <?= date('d F Y H:i', strtotime($training['created_at'])) ?></small>
                        </div>
                        <div>
                            <?php
                            $status_class = '';
                            $status_text = '';
                            
                            if ($training['status'] === 'Pending Dept Head Approval') {
                                $status_class = 'status-pending-dept';
                                $status_text = 'Menunggu Dept Head';
                            } elseif ($training['status'] === 'Pending L&D Approval') {
                                $status_class = 'status-pending-lnd';
                                $status_text = 'Menunggu L&D';
                            } elseif ($training['status'] === 'Approved') {
                                $status_class = 'status-approved';
                                $status_text = 'Disetujui';
                            } elseif (strpos($training['status'], 'Rejected') !== false) {
                                $status_class = 'status-rejected';
                                $status_text = 'Ditolak';
                            } else {
                                $status_class = 'status-pending-dept';
                                $status_text = $training['status'];
                            }
                            ?>
                            <span class="status-badge <?= $status_class ?>">
                                <?= $status_text ?>
                            </span>
                        </div>
                    </div>

                    <div class="training-content">
                        <!-- Approval Timeline -->
                        <div class="approval-timeline">
                            <!-- Step 1: Submitted -->
                            <div class="timeline-step completed">
                                <div class="timeline-icon completed">
                                    <i class="fas fa-paper-plane"></i>
                                </div>
                                <div>
                                    <strong>Diajukan</strong><br>
                                    <small><?= date('d/m/Y H:i', strtotime($training['created_at'])) ?></small>
                                </div>
                            </div>

                            <!-- Step 2: Dept Head -->
                            <div class="timeline-step <?= $training['approved_dept_head'] === 'Approved' ? 'completed' : ($training['approved_dept_head'] === 'Rejected' ? 'rejected' : ($training['current_approver'] === 'dept_head' ? 'pending' : 'waiting')) ?>">
                                <div class="timeline-icon <?= $training['approved_dept_head'] === 'Approved' ? 'completed' : ($training['approved_dept_head'] === 'Rejected' ? 'rejected' : ($training['current_approver'] === 'dept_head' ? 'pending' : 'waiting')) ?>">
                                    <i class="fas <?= $training['approved_dept_head'] === 'Approved' ? 'fa-check' : ($training['approved_dept_head'] === 'Rejected' ? 'fa-times' : 'fa-user-tie') ?>"></i>
                                </div>
                                <div>
                                    <strong>Dept Head</strong><br>
                                    <?php if ($training['approved_dept_head'] === 'Approved'): ?>
                                        <small>✅ <?= date('d/m/Y H:i', strtotime($training['approved_at_dept_head'])) ?></small>
                                    <?php elseif ($training['approved_dept_head'] === 'Rejected'): ?>
                                        <small>❌ <?= date('d/m/Y H:i', strtotime($training['approved_at_dept_head'])) ?></small>
                                    <?php elseif ($training['current_approver'] === 'dept_head'): ?>
                                        <small>⏳ Menunggu</small>
                                    <?php else: ?>
                                        <small>⏸️ Belum</small>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Step 3: L&D -->
                            <div class="timeline-step <?= $training['approved_lnd'] === 'Approved' ? 'completed' : ($training['approved_lnd'] === 'Rejected' ? 'rejected' : ($training['current_approver'] === 'lnd' ? 'pending' : 'waiting')) ?>">
                                <div class="timeline-icon <?= $training['approved_lnd'] === 'Approved' ? 'completed' : ($training['approved_lnd'] === 'Rejected' ? 'rejected' : ($training['current_approver'] === 'lnd' ? 'pending' : 'waiting')) ?>">
                                    <i class="fas <?= $training['approved_lnd'] === 'Approved' ? 'fa-check' : ($training['approved_lnd'] === 'Rejected' ? 'fa-times' : 'fa-graduation-cap') ?>"></i>
                                </div>
                                <div>
                                    <strong>L&D</strong><br>
                                    <?php if ($training['approved_lnd'] === 'Approved'): ?>
                                        <small>✅ <?= date('d/m/Y H:i', strtotime($training['approved_at_lnd'])) ?></small>
                                    <?php elseif ($training['approved_lnd'] === 'Rejected'): ?>
                                        <small>❌ <?= date('d/m/Y H:i', strtotime($training['approved_at_lnd'])) ?></small>
                                    <?php elseif ($training['current_approver'] === 'lnd'): ?>
                                        <small>⏳ Menunggu</small>
                                    <?php else: ?>
                                        <small>⏸️ Belum</small>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Step 4: Completed -->
                            <div class="timeline-step <?= $training['status'] === 'Approved' ? 'completed' : (strpos($training['status'], 'Rejected') !== false ? 'rejected' : 'waiting') ?>">
                                <div class="timeline-icon <?= $training['status'] === 'Approved' ? 'completed' : (strpos($training['status'], 'Rejected') !== false ? 'rejected' : 'waiting') ?>">
                                    <i class="fas <?= $training['status'] === 'Approved' ? 'fa-flag-checkered' : (strpos($training['status'], 'Rejected') !== false ? 'fa-ban' : 'fa-clock') ?>"></i>
                                </div>
                                <div>
                                    <strong><?= $training['status'] === 'Approved' ? 'Selesai' : (strpos($training['status'], 'Rejected') !== false ? 'Ditolak' : 'Menunggu') ?></strong><br>
                                    <?php if ($training['status'] === 'Approved'): ?>
                                        <small>🎉 Siap dilaksanakan</small>
                                    <?php elseif (strpos($training['status'], 'Rejected') !== false): ?>
                                        <small>❌ Tidak disetujui</small>
                                    <?php else: ?>
                                        <small>⏳ Dalam proses</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Training Details -->
                        <div class="row" style="display: flex; gap: 30px;">
                            <div style="flex: 1;">
                                <h4>Detail Training</h4>
                                <div class="detail-row">
                                    <div class="detail-label">Tanggal:</div>
                                    <div class="detail-value">
                                        <?= date('d F Y', strtotime($training['start_date'])) ?>
                                        <?php if (!empty($training['end_date']) && $training['end_date'] !== $training['start_date']): ?>
                                            - <?= date('d F Y', strtotime($training['end_date'])) ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php if (!empty($training['training_time_start'])): ?>
                                <div class="detail-row">
                                    <div class="detail-label">Waktu:</div>
                                    <div class="detail-value">
                                        <?= date('H:i', strtotime($training['training_time_start'])) ?>
                                        <?php if (!empty($training['training_time_end'])): ?>
                                            - <?= date('H:i', strtotime($training['training_time_end'])) ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($training['location'])): ?>
                                <div class="detail-row">
                                    <div class="detail-label">Lokasi:</div>
                                    <div class="detail-value"><?= htmlspecialchars($training['location']) ?></div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($training['max_participants'])): ?>
                                <div class="detail-row">
                                    <div class="detail-label">Est. Peserta:</div>
                                    <div class="detail-value"><?= htmlspecialchars($training['max_participants']) ?> orang</div>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div style="flex: 1;">
                                <h4>Status Approval</h4>
                                <?php if (!empty($training['dept_head_name'])): ?>
                                <div class="detail-row">
                                    <div class="detail-label">Dept Head:</div>
                                    <div class="detail-value"><?= htmlspecialchars($training['dept_head_name']) ?></div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($training['lnd_name'])): ?>
                                <div class="detail-row">
                                    <div class="detail-label">L&D:</div>
                                    <div class="detail-value"><?= htmlspecialchars($training['lnd_name']) ?></div>
                                </div>
                                <?php endif; ?>
                                <div class="detail-row">
                                    <div class="detail-label">Status Saat Ini:</div>
                                    <div class="detail-value">
                                        <span class="status-badge <?= $status_class ?>" style="font-size: 0.8rem;">
                                            <?= $status_text ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Comments -->
                        <?php if (!empty($training['comments_dept_head']) || !empty($training['comments_lnd'])): ?>
                        <div style="margin-top: 20px;">
                            <h4>Komentar</h4>
                            <?php if (!empty($training['comments_dept_head'])): ?>
                            <div class="comments-section">
                                <strong><i class="fas fa-user-tie"></i> Department Head:</strong><br>
                                <?= htmlspecialchars($training['comments_dept_head']) ?>
                            </div>
                            <?php endif; ?>
                            <?php if (!empty($training['comments_lnd'])): ?>
                            <div class="comments-section">
                                <strong><i class="fas fa-graduation-cap"></i> L&D:</strong><br>
                                <?= htmlspecialchars($training['comments_lnd']) ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <footer>
        <?php include '../config/footer.php'; ?>
    </footer>
</body>
</html>
