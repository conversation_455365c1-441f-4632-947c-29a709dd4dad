<?php
require 'config.php';
// session_start() sudah dipanggil di config.php

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: ../view/form.php");
    exit();
}

// Rate Limiting
if (!isset($_SESSION['submit_count'])) {
    $_SESSION['submit_count'] = 1;
    $_SESSION['first_submit'] = time();
} else {
    if (time() - $_SESSION['first_submit'] < 60) {
        if ($_SESSION['submit_count'] >= 3) {
            $_SESSION['errors'] = ["Anda terlalu banyak melakukan submit! Coba lagi nanti."];
            header("Location: ../view/form.php");
            exit();
        }
        $_SESSION['submit_count']++;
    } else {
        $_SESSION['submit_count'] = 1;
        $_SESSION['first_submit'] = time();
    }
}

// CSRF Protection
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['token']) {
    $_SESSION['errors'] = ["Invalid CSRF token!"];
    header("Location: ../view/form.php");
    exit();
}

// Validasi Input
$errors = [];
$requiredFields = ['full_name', 'email', 'phone', 'training_topic', 'start_date', 'additional_info'];

foreach ($requiredFields as $field) {
    if (empty($_POST[$field])) {
        $errors[] = "Field " . ucfirst(str_replace('_', ' ', $field)) . " wajib diisi!";
    }
}

if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
    $errors[] = "Format email tidak valid!";
}

if (!DateTime::createFromFormat('Y-m-d', $_POST['start_date'])) {
    $errors[] = "Format tanggal tidak valid!";
}

if (!empty($errors)) {
    $_SESSION['errors'] = $errors;
    header("Location: ../view/form.php");
    exit();
}

// Sanitasi Input
$full_name = filter_input(INPUT_POST, 'full_name', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
$phone = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$training_topic = filter_input(INPUT_POST, 'training_topic', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$start_date = filter_input(INPUT_POST, 'start_date', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$additional_info = filter_input(INPUT_POST, 'additional_info', FILTER_SANITIZE_FULL_SPECIAL_CHARS);

// Simpan data ke training_submissions
$stmt = $conn->prepare("INSERT INTO training_submissions (full_name, email, phone, training_topic, start_date, additional_info) VALUES (?, ?, ?, ?, ?, ?)");
$stmt->bind_param("ssssss", $full_name, $email, $phone, $training_topic, $start_date, $additional_info);

if ($stmt->execute()) {
    $training_id = $stmt->insert_id; // Ambil ID terakhir setelah insert berhasil
    $stmt->close();

    // Simpan data ke participants jika ada data yang diinput
    if (!empty($_POST['nama']) && is_array($_POST['nama'])) {
        $stmt_participant = $conn->prepare("INSERT INTO participants (training_id, nama_participants, nik_participants, jabatan_participants, bagian_participants, departemen_participants) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt_participant->bind_param("isssss", $training_id, $nama, $nik, $jabatan, $bagian, $departemen);

        for ($i = 0; $i < count($_POST['nama']); $i++) {
            $nama = filter_var($_POST['nama'][$i], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            $nik = filter_var($_POST['nik'][$i], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            $jabatan = filter_var($_POST['jabatan'][$i], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            $bagian = filter_var($_POST['bagian'][$i], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            $departemen = filter_var($_POST['departemen'][$i], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            $stmt_participant->execute();
        }
        $stmt_participant->close();
    }


    $_SESSION['success'] = "Pengajuan training berhasil disimpan!";
} else {
    $_SESSION['errors'] = ["Terjadi kesalahan sistem. Silakan coba lagi."];
}

$conn->close();
header("Location: ../view/form.php");
exit();
