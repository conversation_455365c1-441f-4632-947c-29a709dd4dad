<?php
/**
 * Fallback navbar dengan notifikasi untuk digunakan ketika navbar utama tidak tersedia
 */

// Determine base URL
$base_url = '../';
$current_page = basename($_SERVER['PHP_SELF']);
$user_name = $_SESSION['full_name'] ?? $_SESSION['user_name'] ?? 'User';
$user_initial = strtoupper(substr($user_name, 0, 1));
?>

<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <div class="container-fluid">
        <a class="navbar-brand" href="<?= $base_url ?>pemohon/index.php">Training System</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?= ($current_page == 'index.php') ? 'active' : '' ?>" href="<?= $base_url ?>pemohon/index.php">
                        <i class="fas fa-home"></i> Home
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= ($current_page == 'classroom.php') ? 'active' : '' ?>" href="<?= $base_url ?>pemohon/classroom.php">
                        <i class="fas fa-chalkboard"></i> Kelas
                    </a>
                </li>
            </ul>

            <ul class="navbar-nav">
                <?php
                // Include notifications dropdown
                if (isset($_SESSION['user_id'])) {
                    try {
                        require_once __DIR__ . '/notification_helper.php';
                        $unread_notifications = getUnreadNotifications($_SESSION['user_id'], 5);
                        $notification_count = count($unread_notifications);
                        ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <?php if ($notification_count > 0): ?>
                                    <span class="position-absolute top-0 start-100 translate-middle rounded-pill bg-danger p-1">
                                        <?= $notification_count ?>
                                        <span class="visually-hidden">notifikasi belum dibaca</span>
                                    </span>
                                <?php endif; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationsDropdown" style="width: 300px; max-height: 400px; overflow-y: auto;">
                                <li>
                                    <h6 class="dropdown-header">Notifikasi</h6>
                                </li>

                                <?php if (empty($unread_notifications)): ?>
                                    <li>
                                        <span class="dropdown-item text-muted">Tidak ada notifikasi baru</span>
                                    </li>
                                <?php else: ?>
                                    <?php foreach ($unread_notifications as $notification): ?>
                                        <li>
                                            <a class="dropdown-item notification-item" href="mark_notification.php?id=<?= $notification['id'] ?>&redirect=<?= urlencode($_SERVER['REQUEST_URI']) ?>">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0">
                                                        <?php
                                                        $icon_class = 'fa-info-circle text-info';
                                                        switch ($notification['type']) {
                                                            case 'success':
                                                                $icon_class = 'fa-check-circle text-success';
                                                                break;
                                                            case 'warning':
                                                                $icon_class = 'fa-exclamation-triangle text-warning';
                                                                break;
                                                            case 'error':
                                                                $icon_class = 'fa-times-circle text-danger';
                                                                break;
                                                        }
                                                        ?>
                                                        <i class="fas <?= $icon_class ?> fa-lg"></i>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <h6 class="mb-0"><?= htmlspecialchars($notification['title']) ?></h6>
                                                        <p class="mb-0 small text-truncate"><?= htmlspecialchars($notification['message']) ?></p>
                                                        <small class="text-muted"><?= date('d M H:i', strtotime($notification['created_at'])) ?></small>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-center" href="all_notifications.php">
                                            Lihat Semua Notifikasi
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                        <?php
                    } catch (Exception $e) {
                        // Jika ada error, tampilkan bell icon tanpa badge
                        ?>
                        <li class="nav-item">
                            <a class="nav-link" href="all_notifications.php" title="Notifikasi">
                                <i class="fas fa-bell"></i>
                            </a>
                        </li>
                        <?php
                    }
                }
                ?>

                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="avatar-circle"><?= $user_initial ?></span>
                        <span class="d-none d-md-inline ms-2"><?= htmlspecialchars($user_name) ?></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="<?= $base_url ?>pemohon/profile.php"><i class="fas fa-user me-2"></i> Profil</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?= $base_url ?>view/logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
    .avatar-circle {
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 32px !important;
        height: 32px !important;
        border-radius: 50% !important;
        background-color: #007bff!important;
        color: white !important;
        font-weight: bold !important;
    }

    .navbar .dropdown-menu {
        min-width: 200px!important;
    }

    .navbar .dropdown-item {
        padding: 8px 16px !important;
    }

    .navbar .dropdown-item i {
        width: 20px !important;
        text-align: center !important;
    }

    .notification-item {
        padding: 10px 15px !important;
        border-bottom: 1px solid #f0f0f0 !important;
    }
    
    .notification-item:hover {
        background-color: #f8f9fa !important;
    }
    
    .notification-item:last-child {
        border-bottom: none !important;
    }
</style>
