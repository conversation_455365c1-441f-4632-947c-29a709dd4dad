-- ============================================================================
-- Script SQL untuk mengubah semua password menjadi "asdf"
-- HANY<PERSON> UNTUK TESTING - JANGAN GUNAKAN DI PRODUCTION!
-- ============================================================================

-- PERINGATAN: Script ini akan mengubah SEMUA password user menjadi "asdf"
-- Pastikan Anda memiliki backup database sebelum menjalankan!

-- ============================================================================
-- 1. BACKUP PASSWORD LAMA (OPSIONAL)
-- ============================================================================

-- Buat tabel backup jika belum ada
CREATE TABLE IF NOT EXISTS password_backup (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    old_password VARCHAR(255) NOT NULL,
    backup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    backup_reason VARCHAR(100) DEFAULT 'sql_mass_reset'
);

-- Backup semua password lama
INSERT INTO password_backup (user_id, old_password, backup_reason)
SELECT id, password, 'sql_mass_reset_to_asdf'
FROM users 
WHERE is_active = 1;

-- Verifikasi backup
SELECT COUNT(*) as 'Passwords Backed Up' FROM password_backup WHERE backup_reason = 'sql_mass_reset_to_asdf';

-- ============================================================================
-- 2. HASH PASSWORD "asdf"
-- ============================================================================

-- Password "asdf" yang sudah di-hash dengan PASSWORD_DEFAULT (bcrypt)
-- Hash ini dibuat dengan: password_hash('asdf', PASSWORD_DEFAULT)
-- Catatan: Hash ini mungkin berbeda di setiap environment, 
-- lebih baik gunakan script PHP untuk generate hash yang konsisten

SET @default_password_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

-- Verifikasi hash (ini adalah hash untuk "password", bukan "asdf")
-- Untuk "asdf", Anda perlu generate hash baru menggunakan PHP

-- ============================================================================
-- 3. PREVIEW USERS YANG AKAN DIUBAH
-- ============================================================================

-- Tampilkan semua user aktif
SELECT 
    id,
    name,
    nik,
    email,
    role_id,
    CASE 
        WHEN role_id = 99 THEN 'Admin' 
        ELSE 'Regular User' 
    END as user_type,
    is_active
FROM users 
WHERE is_active = 1
ORDER BY role_id, name;

-- Hitung total user
SELECT 
    COUNT(*) as 'Total Active Users',
    SUM(CASE WHEN role_id = 99 THEN 1 ELSE 0 END) as 'Admin Users',
    SUM(CASE WHEN role_id != 99 THEN 1 ELSE 0 END) as 'Regular Users'
FROM users 
WHERE is_active = 1;

-- ============================================================================
-- 4. GENERATE HASH UNTUK "asdf" (GUNAKAN PHP)
-- ============================================================================

/*
Untuk mendapatkan hash yang benar untuk "asdf", jalankan PHP code berikut:

<?php
echo "Hash untuk 'asdf': " . password_hash('asdf', PASSWORD_DEFAULT) . "\n";
?>

Atau gunakan script PHP yang sudah disediakan:
- reset_all_passwords.php (web interface)
- reset_passwords_cli.php (command line)
*/

-- ============================================================================
-- 5. UPDATE PASSWORD (GANTI HASH DENGAN YANG BENAR)
-- ============================================================================

-- PERINGATAN: Ganti hash di bawah dengan hash yang benar untuk "asdf"
-- Hash ini hanya contoh dan mungkin tidak bekerja!

-- Update semua user (termasuk admin)
-- UPDATE users 
-- SET 
--     password = '$2y$10$[GANTI_DENGAN_HASH_ASDF_YANG_BENAR]',
--     password_changed_at = NULL
-- WHERE is_active = 1;

-- Atau update semua user kecuali admin
-- UPDATE users 
-- SET 
--     password = '$2y$10$[GANTI_DENGAN_HASH_ASDF_YANG_BENAR]',
--     password_changed_at = NULL
-- WHERE is_active = 1 AND role_id != 99;

-- ============================================================================
-- 6. VERIFIKASI HASIL
-- ============================================================================

-- Cek user dengan password_changed_at = NULL (yang baru di-reset)
SELECT 
    id,
    name,
    nik,
    role_id,
    password_changed_at,
    CASE 
        WHEN password_changed_at IS NULL THEN 'Password Reset' 
        ELSE 'Custom Password' 
    END as password_status
FROM users 
WHERE is_active = 1
ORDER BY password_changed_at IS NULL DESC, role_id, name;

-- Hitung hasil
SELECT 
    COUNT(*) as 'Total Users',
    SUM(CASE WHEN password_changed_at IS NULL THEN 1 ELSE 0 END) as 'Reset Passwords',
    SUM(CASE WHEN password_changed_at IS NOT NULL THEN 1 ELSE 0 END) as 'Custom Passwords'
FROM users 
WHERE is_active = 1;

-- ============================================================================
-- 7. RESTORE DARI BACKUP (JIKA DIPERLUKAN)
-- ============================================================================

-- Jika perlu restore password lama:
-- UPDATE users u
-- INNER JOIN password_backup pb ON u.id = pb.user_id
-- SET u.password = pb.old_password
-- WHERE pb.backup_reason = 'sql_mass_reset_to_asdf';

-- ============================================================================
-- 8. CLEANUP
-- ============================================================================

-- Hapus backup setelah tidak diperlukan (HATI-HATI!)
-- DROP TABLE password_backup;

-- Atau hapus backup untuk session ini saja
-- DELETE FROM password_backup WHERE backup_reason = 'sql_mass_reset_to_asdf';

-- ============================================================================
-- 9. TESTING QUERIES
-- ============================================================================

-- Cek apakah ada user yang bisa login dengan "asdf"
-- (Jalankan setelah update password)
SELECT 
    id,
    name,
    nik,
    'Test login dengan password: asdf' as instruction
FROM users 
WHERE is_active = 1 
LIMIT 5;

-- Monitoring untuk development
SELECT 
    'Users with default password' as info,
    COUNT(*) as count
FROM users 
WHERE is_active = 1 
AND password_changed_at IS NULL;

-- ============================================================================
-- CATATAN PENTING
-- ============================================================================

/*
1. JANGAN GUNAKAN DI PRODUCTION!
2. Pastikan backup database sebelum menjalankan
3. Hash password harus di-generate dengan PHP, bukan hardcode
4. Gunakan script PHP yang disediakan untuk hasil yang lebih aman:
   - reset_all_passwords.php (web interface dengan konfirmasi)
   - reset_passwords_cli.php (command line dengan safety checks)

5. Setelah testing selesai:
   - Hapus script-script ini
   - Restore password dari backup jika diperlukan
   - Pastikan semua user mengubah password mereka

6. Untuk verifikasi fitur password default:
   - Login dengan user manapun menggunakan password "asdf"
   - User harus diarahkan ke change_password.php
   - Pesan warning harus muncul
   - Proses change password harus berfungsi normal
*/
