<?php
// <PERSON>lai output buffering untuk menangkap semua output
ob_start();

// Nonaktifkan error reporting yang bisa merusak JSON
error_reporting(0);
ini_set('display_errors', 0);

// Set header JSON di awal
header('Content-Type: application/json');

// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Include batch history recording
require_once 'record_batch_employee_history.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

try {
    // Validasi input
    if (empty($_GET['nik'])) {
        throw new Exception('NIK tidak boleh kosong');
    }

    $nik = $_GET['nik'];

    // Ambil data karyawan sebelum dihapus untuk log
    $query = "SELECT nama FROM karyawan WHERE nik = ?";
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Error preparing query: " . $conn->error);
    }

    $stmt->bind_param("s", $nik);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('Karyawan dengan NIK tersebut tidak ditemukan');
    }

    $employee = $result->fetch_assoc();
    $employee_name = $employee['nama'];

    // Mulai transaction
    $conn->begin_transaction();

    // Hapus data karyawan
    $delete_query = "DELETE FROM karyawan WHERE nik = ?";
    $delete_stmt = $conn->prepare($delete_query);
    if (!$delete_stmt) {
        throw new Exception("Error preparing delete query: " . $conn->error);
    }

    $delete_stmt->bind_param("s", $nik);
    if (!$delete_stmt->execute()) {
        throw new Exception("Error executing delete query: " . $delete_stmt->error);
    }

    if ($delete_stmt->affected_rows === 0) {
        throw new Exception('Tidak ada data yang dihapus');
    }

    // Log aktivitas
    $user_id = $_SESSION['user_id'];
    $log_query = "INSERT INTO activity_logs (user_id, action, category, timestamp)
                  VALUES (?, CONCAT('Menghapus karyawan: ', ?), 'employee', NOW())";

    $log_stmt = $conn->prepare($log_query);
    if (!$log_stmt) {
        throw new Exception("Error preparing log query: " . $conn->error);
    }

    $log_stmt->bind_param("is", $user_id, $employee_name);
    if (!$log_stmt->execute()) {
        throw new Exception("Error executing log query: " . $log_stmt->error);
    }

    // Record batch history for individual delete
    try {
        // Convert mysqli connection to PDO for batch history
        $pdo = new PDO("mysql:host={$GLOBALS['db_host']};dbname={$GLOBALS['db_name']};charset=utf8", $GLOBALS['db_user'], $GLOBALS['db_pass']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Record individual delete as a single-item batch
        $batch_id = recordBatchEmployeeHistory(
            $pdo,
            'INDIVIDUAL_DELETE',
            [$nik], // Single NIK in array
            'delete',
            1, // success_count
            0, // error_count
            0  // skipped_count
        );

        // Mark as rollback capable for individual deletes (requires enhanced history)
        if ($batch_id) {
            $update_stmt = $pdo->prepare("
                UPDATE karyawan_batch_history
                SET is_rollback_capable = FALSE, rollback_status = 'NONE'
                WHERE batch_id = ?
            ");
            $update_stmt->execute([$batch_id]);
        }

    } catch (Exception $batch_error) {
        // Don't fail the main operation if batch history fails
        error_log("Failed to record batch history for individual delete: " . $batch_error->getMessage());
    }

    // Commit transaction
    $conn->commit();

    // Kembalikan response sukses
    echo json_encode([
        'success' => true,
        'message' => 'Karyawan berhasil dihapus'
    ]);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn) && $conn->connect_errno === 0) {
        $conn->rollback();
    }

    // Kembalikan response error
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    // Bersihkan output buffer dan kirim respons
    $output = ob_get_clean();

    // Periksa apakah output adalah JSON yang valid
    json_decode($output);
    if (json_last_error() !== JSON_ERROR_NONE) {
        // Jika bukan JSON valid, ganti dengan pesan error JSON
        echo json_encode([
            'success' => false,
            'message' => 'Server error: Invalid JSON response',
            'debug_info' => 'Output contains non-JSON content'
        ]);
    } else {
        // Jika JSON valid, kirim output asli
        echo $output;
    }

    // Pastikan tidak ada output lain setelah ini
    exit();
}