<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

$error = "";

// Proses form submit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $nik = trim($_POST['nik']);
    $role_id = $_POST['role_id'];
    $dept = $_POST['dept'];
    $bagian = $_POST['bagian'];
    $jabatan = $_POST['jabatan'];

    // Cek apakah email, NIK, atau nama sudah digunakan
    $checkQuery = "SELECT * FROM users WHERE email = ? OR nik = ? OR name = ?";
    $stmtCheck = $conn->prepare($checkQuery);
    $stmtCheck->bind_param("sss", $email, $nik, $name);
    $stmtCheck->execute();
    $resultCheck = $stmtCheck->get_result();

    if ($resultCheck->num_rows > 0) {
        $row = $resultCheck->fetch_assoc();
        if ($row['email'] == $email) {
            $error = "Error: Email sudah digunakan!";
        } elseif ($row['nik'] == $nik) {
            $error = "Error: NIK sudah digunakan!";
        } elseif ($row['name'] == $name) {
            $error = "Error: Nama sudah digunakan!";
        }
    } else {
        // Generate verification code
        $verification_code = sprintf("%06d", mt_rand(100000, 999999));
        $verification_expires = date('Y-m-d H:i:s', strtotime('+15 minutes'));

        // Jika tidak ada duplikasi, tambahkan pengguna baru
        $query = "INSERT INTO users (name, email, nik, role_id, dept, bagian, jabatan, is_active, verification_code, verification_expires)
                  VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?)";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("sssisssss", $name, $email, $nik, $role_id, $dept, $bagian, $jabatan, $verification_code, $verification_expires);
        if ($stmt->execute()) {
            $user_id = $conn->insert_id;

            // Insert departemen-departemen yang dipilih
            if(isset($_POST['departments'])) {
                foreach($_POST['departments'] as $dept) {
                    $query = "INSERT INTO user_departments (user_id, dept) VALUES (?, ?)";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("is", $user_id, $dept);
                    $stmt->execute();
                }
            }

            // Kirim email aktivasi
            include_once '../config/mail.php';

            // Ambil konfigurasi email dari database
            $settingsQuery = "SELECT smtp_server, smtp_port, smtp_password, sender_email, sender_name, smtp_encryption FROM settings WHERE id = 1";
            $settingsResult = $conn->query($settingsQuery);

            if ($settingsResult && $settingsResult->num_rows > 0) {
                $settings = $settingsResult->fetch_assoc();

                // Kirim email aktivasi
                $activation_link = BASE_URL . "view/Aktivasi.php";

                $mail_result = send_mail(
                    $email,
                    'Aktivasi Akun Training Center',
                    "
                    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 2px solid #BF0000; border-radius: 5px; background-color: #f9f9f9;'>
                        <h2 style='color: #BF0000; text-align: center;'>Aktivasi Akun Training Center</h2>
                        <p style='color: #333; font-size: 16px;'>Halo <strong>{$name}</strong>,</p>
                        <p style='color: #333; font-size: 16px;'>Akun Anda telah dibuat di Training Center. Untuk mengaktifkan akun Anda, silakan gunakan informasi berikut:</p>
                        <div style='background-color: #ffffff; border: 2px solid #BF0000; border-radius: 5px; padding: 15px; margin: 20px 0;'>
                            <p style='margin: 5px 0;'><strong>NIK:</strong> {$nik}</p>
                            <p style='margin: 5px 0;'><strong>Departemen:</strong> {$dept}</p>
                            <p style='margin: 5px 0;'><strong>Kode Verifikasi:</strong> <span style='font-size: 18px; font-weight: bold; color: #BF0000;'>{$verification_code}</span></p>
                        </div>
                        <p style='color: #333; font-size: 16px;'>Untuk mengaktifkan akun Anda, kunjungi <a href='{$activation_link}' style='color: #BF0000; text-decoration: none; font-weight: bold;'>halaman aktivasi</a> dan masukkan NIK, Departemen, dan Kode Verifikasi Anda.</p>
                        <p style='color: #333; font-size: 14px;'>Kode verifikasi ini akan kadaluarsa dalam <strong>15 menit</strong>.</p>
                        <p style='color: #333; font-size: 14px;'>Jika Anda tidak meminta pembuatan akun ini, abaikan email ini.</p>
                        <div style='text-align: center; margin-top: 30px;'>
                            <p style='color: #333; font-size: 12px;'>Email ini dikirim secara otomatis. Mohon jangan membalas email ini.</p>
                        </div>
                    </div>
                    ",
                    $settings
                );
            }

            // Log aktivitas
            if (file_exists('../config/activity_logger.php')) {
                include_once '../config/activity_logger.php';
                if (function_exists('log_activity')) {
                    $role_names = [
                        1 => 'Pemohon',
                        2 => 'Dept Head',
                        3 => 'LnD',
                        4 => 'FM',
                        5 => 'Direktur',
                        6 => 'Kosong',
                        99 => 'Admin'
                    ];
                    $role_name = $role_names[$role_id] ?? 'Unknown';

                    log_activity($_SESSION['user_id'], "Menambahkan pengguna baru: {$name} (NIK: {$nik}) dengan role {$role_name}", "user", [
                        'user_id' => $user_id,
                        'name' => $name,
                        'nik' => $nik,
                        'role_id' => $role_id,
                        'dept' => $dept,
                        'bagian' => $bagian,
                        'jabatan' => $jabatan
                    ]);
                }
            }

            header('Location: index.php');
            exit();
        } else {
            $error = "Error: Gagal menambahkan pengguna.";
        }
    }
}

// Query untuk mengambil daftar role
$query = "SELECT * FROM roles";
$result = $conn->query($query);
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    html, body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 0;
        color: #333;
        height: 100%;
    }

    body {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
    }

    .content-wrapper {
        flex: 1;
        width: 100%;
        display: flex;
        flex-direction: column;
    }

    .container-form {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 150px 20px 40px 20px; /* Increased top padding to prevent navbar overlap */
        width: 100%;
    }

    .form-container {
        background: #fff;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        width: 100%;
        max-width: 550px;
        transition: all 0.3s ease;
    }

    .form-container:hover {
        box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
    }

    h2 {
        text-align: center;
        color: #BF0000;
        margin-bottom: 25px;
        font-weight: 600;
        position: relative;
        padding-bottom: 10px;
    }

    h2:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background-color: #BF0000;
        border-radius: 3px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    label {
        display: block;
        margin: 12px 0 8px;
        font-weight: 500;
        color: #444;
        font-size: 15px;
    }

    input, select {
        width: 100%;
        padding: 12px 15px;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-sizing: border-box;
        font-size: 15px;
        transition: all 0.3s ease;
        background-color: #f9f9f9;
    }

    input:focus, select:focus {
        outline: none;
        border-color: #BF0000;
        box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
        background-color: #fff;
    }

    button {
        width: 100%;
        padding: 14px;
        background-color: #BF0000;
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-top: 10px;
        box-shadow: 0 4px 6px rgba(191, 0, 0, 0.1);
    }

    button:hover {
        background-color: #a00000;
        transform: translateY(-2px);
        box-shadow: 0 6px 8px rgba(191, 0, 0, 0.15);
    }

    button:active {
        transform: translateY(0);
    }

    .error {
        color: #d32f2f;
        font-weight: 500;
        text-align: center;
        margin-bottom: 20px;
        padding: 10px;
        background-color: #ffebee;
        border-radius: 8px;
        border-left: 4px solid #d32f2f;
    }

    .back-link {
        display: block;
        text-align: center;
        margin-top: 20px;
        color: #BF0000;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .back-link:hover {
        color: #a00000;
        text-decoration: underline;
    }

    .search-container {
        margin-bottom: 25px;
        position: relative;
    }

    .search-input {
        width: 100%;
        padding: 12px 15px;
        padding-left: 40px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s ease;
        background-color: #f9f9f9;
    }

    .search-input:focus {
        outline: none;
        border-color: #BF0000;
        box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
        background-color: #fff;
    }

    .search-icon {
        position: absolute;
        left: 15px;
        top: 14px;
        color: #777;
    }

    .employee-results {
        max-height: 250px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 20px;
        display: none;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        scrollbar-width: thin;
        scrollbar-color: #BF0000 #f1f1f1;
    }

    .employee-results::-webkit-scrollbar {
        width: 8px;
    }

    .employee-results::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 8px;
    }

    .employee-results::-webkit-scrollbar-thumb {
        background-color: #BF0000;
        border-radius: 8px;
    }

    .employee-result {
        padding: 12px 15px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
        transition: all 0.2s ease;
    }

    .employee-result:last-child {
        border-bottom: none;
    }

    .employee-result:hover {
        background-color: #f5f5f5;
    }

    .selected-employee {
        background-color: #f8f0f0;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 25px;
        display: none;
        border-left: 4px solid #BF0000;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        line-height: 1.6;
    }

    .checkbox-group {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;
        max-height: 150px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 8px;
        background-color: #f9f9f9;
    }

    .checkbox-item {
        display: flex;
        align-items: center;
        margin-right: 15px;
        margin-bottom: 8px;
    }

    .checkbox-item input[type="checkbox"] {
        width: auto;
        margin-right: 5px;
        margin-bottom: 0;
    }

    .checkbox-item label {
        margin: 0;
        font-weight: normal;
        cursor: pointer;
    }

    .info-text {
        display: block;
        font-size: 13px;
        color: #666;
        margin-top: 5px;
        font-style: italic;
    }

    /* Notification styles */
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px;
        border-radius: 8px;
        background-color: #f8f9fa;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-width: 300px;
        max-width: 400px;
        animation: slideIn 0.3s ease-out forwards;
    }

    .notification.error {
        background-color: #f8d7da;
        border-left: 4px solid #dc3545;
    }

    .notification.success {
        background-color: #d4edda;
        border-left: 4px solid #28a745;
    }

    .notification.info {
        background-color: #d1ecf1;
        border-left: 4px solid #17a2b8;
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .notification-content i {
        font-size: 20px;
    }

    .notification-content span {
        font-size: 14px;
    }

    .notification-close {
        background: none;
        border: none;
        color: #666;
        cursor: pointer;
        font-size: 16px;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .notification-close:hover {
        color: #333;
    }

    .notification-hide {
        animation: slideOut 0.3s ease-in forwards;
    }

    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .form-container {
            padding: 20px;
        }

        input, select, button {
            padding: 10px;
        }

        .notification {
            left: 20px;
            right: 20px;
            max-width: none;
        }
    }
</style>
<body>
<?php include '../config/navbarb.php'; ?>
<div class="content-wrapper">
    <div class="container-form">
        <div class="form-container">
            <h2>Create New User</h2>

            <?php if (!empty($error)) { ?>
                <p class="error"><?php echo $error; ?></p>
            <?php } ?>

            <div class="search-container">
                <input type="text"
                       id="searchEmployee"
                       class="search-input"
                       placeholder="Cari karyawan berdasarkan nama atau NIK...">
                <div id="employeeResults" class="employee-results"></div>
            </div>

            <div id="selectedEmployee" class="selected-employee"></div>

            <form method="POST" action="create_user.php" onsubmit="return validateForm()">
                <input type="hidden" id="nik" name="nik">
                <input type="hidden" id="dept" name="dept">
                <input type="hidden" id="bagian" name="bagian">
                <input type="hidden" id="jabatan" name="jabatan">

                <div class="form-group">
                    <label for="name">Nama Lengkap</label>
                <input type="text" name="name" id="name" placeholder="Masukkan Nama atau cari Nama/NIK" oninput="generateEmail()">
                <small class="info-text">Nama lengkap karyawan yang akan ditambahkan</small>
            </div>

            <div class="form-group">
                <label for="email">Email</label>
                <div style="position: relative;">
                    <input type="email" name="email" id="email" required placeholder="Masukkan email">
                    <button type="button" id="generateEmailBtn" style="position: absolute; right: 0; top: 0; width: auto; padding: 12px 15px; margin: 0; background-color: #f0f0f0; color: #333; border-radius: 0 8px 8px 0; box-shadow: none;">
                        <i class="fas fa-magic"></i> Generate
                    </button>
                </div>
                <small class="info-text">Format email otomatis: <EMAIL></small>
            </div>

            <div class="form-group">
                <label for="role_id">Role</label>
                <select name="role_id" id="role_id" required>
                    <option value="">Pilih Role</option>
                    <?php while ($row = $result->fetch_assoc()) { ?>
                        <option value="<?php echo $row['id']; ?>"><?php echo $row['role_name']; ?></option>
                    <?php } ?>
            </select>

            <div class="form-group">
                <label>Akses Multiple Departemen</label>
                <div class="checkbox-group">
                    <div class="checkbox-container">
                        <?php
                        // Ambil daftar departemen dari database
                        $dept_query = "SELECT DISTINCT dept FROM karyawan ORDER BY dept";
                        $dept_result = $conn->query($dept_query);
                        while ($dept_row = $dept_result->fetch_assoc()) {
                            $dept_code = $dept_row['dept'];
                            echo "<div class='checkbox-item'>";
                            echo "<input type='checkbox' id='dept_{$dept_code}' name='departments[]' value='{$dept_code}'>";
                            echo "<label for='dept_{$dept_code}'>{$dept_code}</label>";
                            echo "</div>";
                        }
                        ?>
                    </div>
                </div>
                <span class="info-text">Pilih departemen tambahan jika user memiliki akses ke multiple departemen</span>
            </div>

            <button type="submit">Add User</button>
        </form>
        <a href="index.php" class="back-link">Back to List</a>
        </div>
    </div>
</div>
<?php include '../config/footer.php'; ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchEmployee');
    const resultsDiv = document.getElementById('employeeResults');
    const generateEmailBtn = document.getElementById('generateEmailBtn');
    let searchTimeout;

    // Event listener untuk tombol generate email
    if (generateEmailBtn) {
        generateEmailBtn.addEventListener('click', function() {
            generateEmail();
        });
    }

    // Fungsi untuk menghasilkan email otomatis
    window.generateEmail = function() {
        const nameInput = document.getElementById('name');
        const emailInput = document.getElementById('email');
        const nikInput = document.getElementById('nik');

        if (nameInput && nameInput.value.trim() !== '') {
            const name = nameInput.value.trim();

            // Gunakan seluruh nama, hapus spasi dan karakter khusus
            // Hapus karakter non-alphanumeric dan ubah ke lowercase
            const cleanName = name.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();

            // Tambahkan angka 1 di belakang nama jika ada, jika tidak gunakan nama asli tanpa angka di belakangnya
            const email = `${cleanName}@example.com`;

            // Cek apakah email sudah ada di database
            checkEmailExists(email, function(exists) {
                if (exists) {
                    // Jika email sudah ada, tambahkan NIK atau angka random
                    const nik = nikInput.value.trim();
                    let uniqueIdentifier = '';

                    if (nik && nik !== '') {
                        // Ambil 4 digit terakhir dari NIK jika ada
                        uniqueIdentifier = nik.replace(/[^0-9]/g, '').slice(-4);
                    } else {
                        // Jika tidak ada NIK, gunakan angka random 4 digit
                        uniqueIdentifier = Math.floor(1000 + Math.random() * 9000);
                    }

                    const uniqueEmail = `${cleanName}${uniqueIdentifier}@example.com`;
                    emailInput.value = uniqueEmail;

                    // Tampilkan notifikasi
                    showNotification(`Email ${email} sudah digunakan. Menggunakan ${uniqueEmail} sebagai gantinya.`, 'info');
                } else {
                    // Jika email belum ada, gunakan email yang sudah dibuat
                    emailInput.value = email;
                }

                // Animasi untuk menunjukkan perubahan
                emailInput.style.backgroundColor = '#e8f5e9';
                setTimeout(() => {
                    emailInput.style.backgroundColor = '';
                }, 1000);
            });
        }
    };

    // Fungsi untuk memeriksa apakah email sudah ada di database
    function checkEmailExists(email, callback) {
        fetch(`check_email.php?email=${encodeURIComponent(email)}`)
            .then(response => response.json())
            .then(data => {
                callback(data.exists);
            })
            .catch(error => {
                console.error('Error checking email:', error);
                // Asumsikan email tidak ada jika terjadi error
                callback(false);
            });
    }

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length < 3) {
            resultsDiv.style.display = 'none';
            return;
        }

        searchTimeout = setTimeout(() => {
            // Menggunakan parameter yang benar dan menangani response dengan tepat
            fetch(`search_handler.php?search=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    resultsDiv.innerHTML = '';
                    // Periksa apakah data.data ada dan memiliki item
                    if (data.data && data.data.length > 0) {
                        data.data.forEach(employee => {
                            const div = document.createElement('div');
                            div.className = 'employee-result';
                            div.innerHTML = `${employee.nama} (${employee.nik}) - ${employee.dept}`;
                            div.onclick = () => selectEmployee({
                                nama: employee.nama,
                                nik: employee.nik,
                                dept: employee.dept,
                                bagian: employee.bagian,
                                jabatan: employee.jabatan
                            });
                            resultsDiv.appendChild(div);
                        });
                        resultsDiv.style.display = 'block';
                    } else {
                        resultsDiv.innerHTML = '<div class="employee-result">Tidak ada hasil ditemukan</div>';
                        resultsDiv.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    resultsDiv.innerHTML = '<div class="employee-result">Terjadi kesalahan saat mencari</div>';
                    resultsDiv.style.display = 'block';
                });
        }, 300);
    });

    function selectEmployee(employee) {
        document.getElementById('name').value = employee.nama;
        document.getElementById('nik').value = employee.nik;
        document.getElementById('dept').value = employee.dept;
        document.getElementById('bagian').value = employee.bagian;
        document.getElementById('jabatan').value = employee.jabatan;

        // Generate email otomatis saat memilih karyawan
        generateEmail();

        const selectedDiv = document.getElementById('selectedEmployee');
        selectedDiv.innerHTML = `
            <strong>Karyawan Terpilih:</strong><br>
            Nama: ${employee.nama}<br>
            NIK: ${employee.nik}<br>
            Departemen: ${employee.dept}<br>
            Bagian: ${employee.bagian}<br>
            Jabatan: ${employee.jabatan}
        `;
        selectedDiv.style.display = 'block';

        resultsDiv.style.display = 'none';
        searchInput.value = '';

        // Set checkbox for department
        const deptCheckbox = document.querySelector(`input[name="departments[]"][value="${employee.dept}"]`);
        if (deptCheckbox) {
            deptCheckbox.checked = true;
        }
    }

    window.validateForm = function() {
        const nameInput = document.getElementById('name');
        const emailInput = document.getElementById('email');
        const roleInput = document.getElementById('role_id');

        if (!nameInput.value.trim()) {
            showNotification('Mohon isi nama karyawan', 'error');
            nameInput.focus();
            return false;
        }

        if (!emailInput.value.trim()) {
            showNotification('Mohon isi email karyawan', 'error');
            emailInput.focus();
            return false;
        }

        if (!roleInput.value) {
            showNotification('Mohon pilih role untuk karyawan', 'error');
            roleInput.focus();
            return false;
        }

        return true;
    };

    // Fungsi untuk menampilkan notifikasi
    function showNotification(message, type = 'info') {
        // Cek apakah sudah ada notifikasi
        let notification = document.querySelector('.notification');
        if (notification) {
            notification.remove();
        }

        // Buat notifikasi baru
        notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close"><i class="fas fa-times"></i></button>
        `;

        document.body.appendChild(notification);

        // Tambahkan event listener untuk tombol close
        notification.querySelector('.notification-close').addEventListener('click', function() {
            notification.classList.add('notification-hide');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });

        // Auto-hide setelah 5 detik
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.add('notification-hide');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 5000);
    }
});
</script>
</body>
</html>
