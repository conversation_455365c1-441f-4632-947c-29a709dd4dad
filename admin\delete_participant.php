<?php
// Include database connection
$root_dir = dirname(__DIR__);
require_once $root_dir . '/config/config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Check if participant_id is provided
if (!isset($_POST['participant_id']) || empty($_POST['participant_id'])) {
    echo json_encode(['success' => false, 'message' => 'Participant ID is required']);
    exit;
}

// Get participant ID
$participant_id = intval($_POST['participant_id']);

try {
    // Log the participant ID for debugging
    error_log("Attempting to delete participant with ID: " . $participant_id);

    // Prepare and execute the delete query
    $query = "DELETE FROM participants WHERE id = ?";
    $stmt = $conn->prepare($query);

    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }

    $bind_result = $stmt->bind_param("i", $participant_id);
    if (!$bind_result) {
        throw new Exception("Binding parameters failed: " . $stmt->error);
    }

    $result = $stmt->execute();

    if ($result) {
        // Check if any rows were affected
        if ($stmt->affected_rows > 0) {
            error_log("Successfully deleted participant with ID: " . $participant_id);
            echo json_encode(['success' => true, 'message' => 'Participant deleted successfully']);
        } else {
            error_log("No participant found with ID: " . $participant_id);
            echo json_encode(['success' => false, 'message' => 'Participant not found']);
        }
    } else {
        error_log("Failed to delete participant: " . $conn->error);
        echo json_encode(['success' => false, 'message' => 'Failed to delete participant: ' . $conn->error]);
    }
} catch (Exception $e) {
    error_log("Exception when deleting participant: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}

// Close the connection
$conn->close();
?>
