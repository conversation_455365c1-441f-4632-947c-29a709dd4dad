<?php
/**
 * Training Notification Manager Class
 * Mengelola notifikasi untuk training system
 */

class TrainingNotificationManager {
    private $conn;
    private $activityLogger;
    
    // Notification types
    const TYPE_INFO = 'info';
    const TYPE_SUCCESS = 'success';
    const TYPE_WARNING = 'warning';
    const TYPE_ERROR = 'error';
    const TYPE_ASSIGNMENT = 'assignment';
    const TYPE_ANNOUNCEMENT = 'announcement';
    const TYPE_REMINDER = 'reminder';
    
    public function __construct($conn, $activityLogger = null) {
        $this->conn = $conn;
        $this->activityLogger = $activityLogger;
    }
    
    /**
     * Create notification
     */
    public function createNotification($user_id, $class_id, $title, $message, $type = self::TYPE_INFO) {
        $query = "INSERT INTO training_notifications (user_id, class_id, title, message, type, created_at) 
                  VALUES (?, ?, ?, ?, ?, NOW())";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("iisss", $user_id, $class_id, $title, $message, $type);
        
        if ($stmt->execute()) {
            $notification_id = $this->conn->insert_id;
            
            // Log activity
            if ($this->activityLogger) {
                $this->activityLogger->log($user_id, "Notification created: $title", 'notification');
            }
            
            return $notification_id;
        }
        
        return false;
    }
    
    /**
     * Send notification to multiple users
     */
    public function sendToMultipleUsers($user_ids, $class_id, $title, $message, $type = self::TYPE_INFO) {
        $notification_ids = [];
        
        foreach ($user_ids as $user_id) {
            $id = $this->createNotification($user_id, $class_id, $title, $message, $type);
            if ($id) {
                $notification_ids[] = $id;
            }
        }
        
        return $notification_ids;
    }
    
    /**
     * Send notification to all class participants
     */
    public function sendToClassParticipants($class_id, $title, $message, $type = self::TYPE_INFO, $include_instructor = true) {
        // Get all participants
        $query = "SELECT user_id FROM class_participants WHERE class_id = ?";
        
        if ($include_instructor) {
            $query .= " UNION SELECT instructor_id as user_id FROM classes WHERE id = ?
                       UNION SELECT user_id FROM class_assistants WHERE class_id = ?";
        }
        
        $stmt = $this->conn->prepare($query);
        if ($include_instructor) {
            $stmt->bind_param("iii", $class_id, $class_id, $class_id);
        } else {
            $stmt->bind_param("i", $class_id);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        $user_ids = [];
        while ($row = $result->fetch_assoc()) {
            $user_ids[] = $row['user_id'];
        }
        
        return $this->sendToMultipleUsers($user_ids, $class_id, $title, $message, $type);
    }
    
    /**
     * Get user notifications
     */
    public function getUserNotifications($user_id, $limit = 50, $unread_only = false) {
        $query = "SELECT tn.*, c.class_name 
                  FROM training_notifications tn
                  LEFT JOIN classes c ON tn.class_id = c.id
                  WHERE tn.user_id = ?";
        
        $params = [$user_id];
        $types = "i";
        
        if ($unread_only) {
            $query .= " AND tn.is_read = 0";
        }
        
        $query .= " ORDER BY tn.created_at DESC LIMIT ?";
        $params[] = $limit;
        $types .= "i";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        
        return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    }
    
    /**
     * Get class notifications
     */
    public function getClassNotifications($class_id, $limit = 50) {
        $query = "SELECT tn.*, u.name as user_name 
                  FROM training_notifications tn
                  LEFT JOIN users u ON tn.user_id = u.id
                  WHERE tn.class_id = ?
                  ORDER BY tn.created_at DESC LIMIT ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("ii", $class_id, $limit);
        $stmt->execute();
        
        return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    }
    
    /**
     * Mark notification as read
     */
    public function markAsRead($notification_id, $user_id = null) {
        $query = "UPDATE training_notifications SET is_read = 1 WHERE id = ?";
        $params = [$notification_id];
        $types = "i";
        
        if ($user_id) {
            $query .= " AND user_id = ?";
            $params[] = $user_id;
            $types .= "i";
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param($types, ...$params);
        
        return $stmt->execute();
    }
    
    /**
     * Mark all user notifications as read
     */
    public function markAllAsRead($user_id) {
        $query = "UPDATE training_notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $user_id);
        
        return $stmt->execute();
    }
    
    /**
     * Get unread notification count
     */
    public function getUnreadCount($user_id) {
        $query = "SELECT COUNT(*) as count FROM training_notifications WHERE user_id = ? AND is_read = 0";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        
        $result = $stmt->get_result()->fetch_assoc();
        return $result['count'];
    }
    
    /**
     * Delete notification
     */
    public function deleteNotification($notification_id, $user_id = null) {
        $query = "DELETE FROM training_notifications WHERE id = ?";
        $params = [$notification_id];
        $types = "i";
        
        if ($user_id) {
            $query .= " AND user_id = ?";
            $params[] = $user_id;
            $types .= "i";
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param($types, ...$params);
        
        return $stmt->execute();
    }
    
    /**
     * Delete old notifications
     */
    public function deleteOldNotifications($days = 30) {
        $query = "DELETE FROM training_notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $days);
        
        return $stmt->execute();
    }
    
    /**
     * Send assignment notification
     */
    public function sendAssignmentNotification($class_id, $assignment_title, $due_date = null) {
        $title = "New Assignment: " . $assignment_title;
        $message = "A new assignment has been posted: " . $assignment_title;
        
        if ($due_date) {
            $message .= "\nDue date: " . date('Y-m-d H:i', strtotime($due_date));
        }
        
        return $this->sendToClassParticipants($class_id, $title, $message, self::TYPE_ASSIGNMENT, false);
    }
    
    /**
     * Send announcement notification
     */
    public function sendAnnouncementNotification($class_id, $announcement_title, $announcement_content) {
        $title = "Announcement: " . $announcement_title;
        $message = substr($announcement_content, 0, 200) . (strlen($announcement_content) > 200 ? '...' : '');
        
        return $this->sendToClassParticipants($class_id, $title, $message, self::TYPE_ANNOUNCEMENT);
    }
    
    /**
     * Send reminder notification
     */
    public function sendReminderNotification($user_ids, $class_id, $reminder_text) {
        $title = "Reminder";
        return $this->sendToMultipleUsers($user_ids, $class_id, $title, $reminder_text, self::TYPE_REMINDER);
    }
    
    /**
     * Get notification statistics
     */
    public function getNotificationStats($days = 30) {
        $query = "SELECT 
                    type,
                    COUNT(*) as count,
                    COUNT(CASE WHEN is_read = 1 THEN 1 END) as read_count,
                    COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_count
                  FROM training_notifications 
                  WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                  GROUP BY type
                  ORDER BY count DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $days);
        $stmt->execute();
        
        return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    }
    
    /**
     * Get notification by ID
     */
    public function getNotificationById($notification_id) {
        $query = "SELECT tn.*, c.class_name, u.name as user_name
                  FROM training_notifications tn
                  LEFT JOIN classes c ON tn.class_id = c.id
                  LEFT JOIN users u ON tn.user_id = u.id
                  WHERE tn.id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $notification_id);
        $stmt->execute();
        
        return $stmt->get_result()->fetch_assoc();
    }
}
