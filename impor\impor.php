<?php
include '../config/config.php';

$file = fopen('Karyawan.csv', 'r');
fgetcsv($file, 0, ',', '"', '\\'); // <PERSON>wati header dengan parameter lengkap

while (($data = fgetcsv($file, 0, ',', '"', '\\')) !== false) {
    $nik = $data[0];
    $nama = $data[1];
    $tgl_masuk = $data[2];
    $jk = $data[3];
    $level_karyawan = (int)$data[4];
    $tgl_lahir = $data[5];
    $agama = $data[6];
    $pendidikan_akhir = $data[7];
    $no_telp = $data[8];
    $dept = $data[9];
    $bagian = $data[10];
    $jabatan = $data[11];
    $group = $data[12];
    $status = $data[13];
    $pt = $data[14];

    // Gunakan INSERT ... ON DUPLICATE KEY UPDATE untuk menangani duplikat
    $query = "INSERT INTO karyawan (nik, nama, tgl_masuk, jk, level_karyawan, tgl_lahir, agama, pendidikan_akhir, no_telp, dept, bagian, jabatan, `group`, status, pt) 
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              ON DUPLICATE KEY UPDATE 
              nama = VALUES(nama), tgl_masuk = VALUES(tgl_masuk), jk = VALUES(jk), level_karyawan = VALUES(level_karyawan), 
              tgl_lahir = VALUES(tgl_lahir), agama = VALUES(agama), pendidikan_akhir = VALUES(pendidikan_akhir), 
              no_telp = VALUES(no_telp), dept = VALUES(dept), bagian = VALUES(bagian), jabatan = VALUES(jabatan), 
              `group` = VALUES(`group`), status = VALUES(status), pt = VALUES(pt)";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ssssissssssssss", $nik, $nama, $tgl_masuk, $jk, $level_karyawan, $tgl_lahir, $agama, $pendidikan_akhir, $no_telp, $dept, $bagian, $jabatan, $group, $status, $pt);
    
    if (!$stmt->execute()) {
        echo "Error untuk NIK $nik: " . $conn->error . "<br>";
    } else {
        echo "Data untuk NIK $nik berhasil diproses.<br>";
    }
}

fclose($file);
echo "Proses impor selesai!";
?>