<?php
/**
 * <PERSON>ript untuk test logika penentuan dept head yang tepat
 */

// Disable session untuk testing
define('NO_SESSION', true);

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/notification_helper.php';

echo "🧪 Testing Department Head Logic\n";
echo "================================\n\n";

// Test cases dengan berbagai departemen
$test_cases = [
    [
        'dept' => 'HRGA',
        'expected_dept_head' => 'Paulus Wikatmo',
        'description' => 'HRGA training should go to Manager HRGA'
    ],
    [
        'dept' => 'WRH02',
        'expected_dept_head' => 'Rahmat Hidayat',
        'description' => 'WRH02 training should go to Manager Warehouse 2'
    ],
    [
        'dept' => 'ENG',
        'expected_dept_head' => 'Hartanto.S.T,MBA',
        'description' => 'ENG training should go to Manager Engineering'
    ]
];

foreach ($test_cases as $i => $test_case) {
    echo "📋 Test Case " . ($i + 1) . ": {$test_case['description']}\n";
    echo "   🏢 Department: {$test_case['dept']}\n";
    echo "   🎯 Expected: {$test_case['expected_dept_head']}\n";
    
    // Cari dept head untuk departemen ini
    $dept_head_query = "SELECT u.id, u.name, u.email 
                        FROM users u 
                        INNER JOIN user_departments ud ON u.id = ud.user_id 
                        WHERE ud.dept = ? AND u.role_id = 2 
                        ORDER BY u.id LIMIT 1";
    $dept_head_stmt = $conn->prepare($dept_head_query);
    $dept_head_stmt->bind_param("s", $test_case['dept']);
    $dept_head_stmt->execute();
    $dept_head_result = $dept_head_stmt->get_result();
    
    if ($dept_head_result->num_rows > 0) {
        $dept_head = $dept_head_result->fetch_assoc();
        $actual_name = $dept_head['name'];
        
        if ($actual_name == $test_case['expected_dept_head']) {
            echo "   ✅ PASS: Found correct dept head: $actual_name\n";
        } else {
            echo "   ❌ FAIL: Expected {$test_case['expected_dept_head']}, got $actual_name\n";
        }
        echo "   📧 Email: {$dept_head['email']}\n";
    } else {
        echo "   ❌ FAIL: No dept head found for department {$test_case['dept']}\n";
    }
    echo "\n";
}

// Test dengan training ID 146 (HRGA department)
echo "🎯 Testing with actual training data (ID: 146)\n";
echo "==============================================\n";

$training_query = "SELECT ts.*, u.name as requester_name 
                   FROM training_submissions ts 
                   LEFT JOIN users u ON ts.user_id = u.id 
                   WHERE ts.id = 146";
$training_result = $conn->query($training_query);

if ($training_result && $training_result->num_rows > 0) {
    $training = $training_result->fetch_assoc();
    echo "📚 Training: {$training['training_topic']}\n";
    echo "👤 Requester: {$training['requester_name']}\n";
    echo "🏢 Department: {$training['departemen']}\n\n";
    
    // Test logika notification_helper
    echo "🔍 Testing notification logic...\n";
    
    // Simulate calling the notification function with correct logic
    $dept_head_query = "SELECT u.id, u.name, u.email 
                        FROM users u 
                        INNER JOIN user_departments ud ON u.id = ud.user_id 
                        WHERE ud.dept = ? AND u.role_id = 2 
                        ORDER BY u.id LIMIT 1";
    $dept_head_stmt = $conn->prepare($dept_head_query);
    $dept_head_stmt->bind_param("s", $training['departemen']);
    $dept_head_stmt->execute();
    $dept_head_result = $dept_head_stmt->get_result();
    
    if ($dept_head_result->num_rows > 0) {
        $correct_dept_head = $dept_head_result->fetch_assoc();
        echo "   ✅ Correct dept head found: {$correct_dept_head['name']}\n";
        echo "   📧 Email: {$correct_dept_head['email']}\n";
        
        // Test dengan next_approver_id yang salah (Rahmat Hidayat)
        $wrong_approver_query = "SELECT id, name FROM users WHERE name = 'Rahmat Hidayat'";
        $wrong_approver_result = $conn->query($wrong_approver_query);
        
        if ($wrong_approver_result && $wrong_approver_result->num_rows > 0) {
            $wrong_approver = $wrong_approver_result->fetch_assoc();
            echo "\n   🧪 Testing with wrong approver (Rahmat Hidayat):\n";
            
            // Simulate the corrected logic
            $approver_query = "SELECT u.id, u.name, u.email, ud.dept as responsible_dept 
                              FROM users u 
                              LEFT JOIN user_departments ud ON u.id = ud.user_id 
                              WHERE u.id = ? AND u.role_id = 2";
            $approver_stmt = $conn->prepare($approver_query);
            $approver_stmt->bind_param("i", $wrong_approver['id']);
            $approver_stmt->execute();
            $approver_result = $approver_stmt->get_result();
            
            if ($approver_result->num_rows > 0) {
                $approver = $approver_result->fetch_assoc();
                echo "      👤 Wrong approver dept: {$approver['responsible_dept']}\n";
                echo "      🏢 Training dept: {$training['departemen']}\n";
                
                if ($approver['responsible_dept'] != $training['departemen']) {
                    echo "      ⚠️  Mismatch detected! System should find correct dept head.\n";
                    echo "      ✅ Correct dept head: {$correct_dept_head['name']}\n";
                } else {
                    echo "      ✅ Departments match\n";
                }
            }
        }
    } else {
        echo "   ❌ No dept head found for {$training['departemen']}\n";
    }
} else {
    echo "❌ Training ID 146 not found\n";
}

echo "\n🏁 Department head logic test completed!\n";

$conn->close();
?>
