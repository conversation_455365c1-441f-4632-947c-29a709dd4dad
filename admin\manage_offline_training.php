<?php
// File: admin/manage_offline_training.php
// Deskripsi: Halaman untuk mengelola Training Internal

include '../config/config.php';
include 'security.php';

// Inisialisasi variabel
$error = '';
$success = '';

// Get date from URL parameter if coming from calendar
$selected_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

// Proses form tambah/edit training
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Tambah training baru
        if ($_POST['action'] === 'add') {
            $training_topic = isset($_POST['training_topic']) ? $_POST['training_topic'] : ''; // Assuming topic cannot be null based on fatal error
            $training_description = isset($_POST['training_description']) ? $_POST['training_description'] : '';
            $start_date = isset($_POST['start_date']) ? $_POST['start_date'] : null;
            $end_date = isset($_POST['end_date']) ? $_POST['end_date'] : null;
            $training_time_start = !empty($_POST['training_time_start']) ? $_POST['training_time_start'] : null;
            $training_time_end = !empty($_POST['training_time_end']) ? $_POST['training_time_end'] : null;
            $location = isset($_POST['location']) ? trim($_POST['location']) : '';
            $trainer_name = isset($_POST['trainer_name']) ? trim($_POST['trainer_name']) : '';
            $max_participants = !empty($_POST['max_participants']) ? (int)$_POST['max_participants'] : null;
            $status = isset($_POST['status']) ? $_POST['status'] : 'Active'; // Assuming a default status

            // Handle empty end_date
            if (empty($end_date) || $end_date === $start_date) {
                $end_date = null;
            }

            // Debug: Log received values
            error_log("Form values received:");
            error_log("trainer_name: " . $trainer_name);
            error_log("max_participants: " . $max_participants);
            error_log("location: " . $location);

            $query = "INSERT INTO offline_training
                      (training_topic, training_description, start_date, end_date, training_time_start, training_time_end,
                       location, trainer_name, max_participants, status, created_by)
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("ssssssssisi", $training_topic, $training_description, $start_date, $end_date,
                              $training_time_start, $training_time_end, $location, $trainer_name,
                              $max_participants, $status, $_SESSION['user_id']);

            if ($stmt->execute()) {
                $success = "Training Internal berhasil ditambahkan";
            } else {
                $error = "Error: " . $stmt->error;
                // Debug information
                error_log("INSERT Error: " . $stmt->error);
                error_log("Trainer name value: " . $trainer_name);
                error_log("Max participants value: " . $max_participants);
            }
            $stmt->close();
        }
        // Edit training
        elseif ($_POST['action'] === 'edit') {
            $training_id = $_POST['training_id']; // Assuming training_id is always set for edit action
            $training_topic = isset($_POST['training_topic']) ? $_POST['training_topic'] : '';
            $training_description = isset($_POST['training_description']) ? $_POST['training_description'] : '';
            $start_date = isset($_POST['start_date']) ? $_POST['start_date'] : null;
            $end_date = isset($_POST['end_date']) ? $_POST['end_date'] : null;
            $training_time_start = !empty($_POST['training_time_start']) ? $_POST['training_time_start'] : null;
            $training_time_end = !empty($_POST['training_time_end']) ? $_POST['training_time_end'] : null;
            $location = isset($_POST['location']) ? trim($_POST['location']) : '';
            $trainer_name = isset($_POST['trainer_name']) ? trim($_POST['trainer_name']) : '';
            $max_participants = !empty($_POST['max_participants']) ? (int)$_POST['max_participants'] : null;
            $status = isset($_POST['status']) ? $_POST['status'] : 'Active';

            // Handle empty end_date
            if (empty($end_date) || $end_date === $start_date) {
                $end_date = null;
            }

            $query = "UPDATE offline_training
                      SET training_topic = ?, training_description = ?, start_date = ?, end_date = ?,
                          training_time_start = ?, training_time_end = ?, location = ?,
                          trainer_name = ?, max_participants = ?, status = ?
                      WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("ssssssssisi", $training_topic, $training_description, $start_date, $end_date,
                              $training_time_start, $training_time_end, $location, $trainer_name,
                              $max_participants, $status, $training_id);

            if ($stmt->execute()) {
                $success = "Training Internal berhasil diperbarui";
            } else {
                $error = "Error: " . $stmt->error;
            }
            $stmt->close();
        }
        // Hapus training
        elseif ($_POST['action'] === 'delete') {
            $training_id = $_POST['training_id']; // Assuming training_id is always set for delete action

            // Cek apakah ada data absensi
            $check_query = "SELECT COUNT(*) as count FROM offline_training_attendance WHERE offline_training_id = ?";
            $check_stmt = $conn->prepare($check_query);
            $check_stmt->bind_param("i", $training_id);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            $row = $result->fetch_assoc();
            $check_stmt->close();

            if ($row['count'] > 0) {
                $error = "Tidak dapat menghapus training karena sudah ada data absensi";
            } else {
                $query = "DELETE FROM offline_training WHERE id = ?";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("i", $training_id);

                if ($stmt->execute()) {
                    $success = "Training Internal berhasil dihapus";
                } else {
                    $error = "Error: " . $stmt->error;
                }
                $stmt->close();
            }
        }
    }
}

// Ambil daftar Training Internal
$query = "SELECT * FROM offline_training ORDER BY start_date DESC";
$result = $conn->query($query);

// Judul halaman
$page_title = "Manajemen Training Internal";
include 'header.php';

// Tambahkan CSS dan JS khusus untuk halaman ini
echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">';
echo '<link rel="stylesheet" href="assets/css/offline-training.css">';
?>

<div class="container-fluid">
    <h1 class="welcome-section">Manajemen Training Internal</h1>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_GET['date'])): ?>
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-calendar-plus"></i> <strong>Tambah Training untuk tanggal:</strong> <?= date('d F Y', strtotime($selected_date)) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
        <div class="mb-3">
            <?php if (isset($_GET['date'])): ?>
                <a href="calendar_management.php" class="btn btn-secondary">
                    <i class="fas fa-calendar-alt"></i> Kembali ke Kalender
                </a>
            <?php endif; ?>
            <a href="dashboard.php" class="btn btn-primary">
                <i class="fas fa-home"></i> Dashboard
            </a>
        </div>
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="text-white">Daftar Training Internal</h6>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTrainingModal">
                <i class="fas fa-plus"></i> Tambah Training
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="trainingTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Topik Training</th>
                            <th>Tanggal</th>
                            <th>Waktu</th>
                            <th>Lokasi</th>
                            <th>Trainer</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
    <?php if ($result->num_rows > 0): ?>
        <?php while ($row = $result->fetch_assoc()): ?>
            <tr>
                <td><?= htmlspecialchars($row['training_topic']) ?></td>
                <td>
                    <?php
                    if (!empty($row['start_date'])) {
                        $start_date_formatted = date('d-m-Y', strtotime($row['start_date']));
                        if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                            $end_date_formatted = date('d-m-Y', strtotime($row['end_date']));
                            echo $start_date_formatted . ' - ' . $end_date_formatted;
                        } else {
                            echo $start_date_formatted;
                        }
                    } else {
                        echo '-';
                    }
                    ?>
                </td>
                <td>
                    <?= !empty($row['training_time_start']) ? date('H:i', strtotime($row['training_time_start'])) : '-' ?>
                    s/d
                    <?= !empty($row['training_time_end']) ? date('H:i', strtotime($row['training_time_end'])) : '-' ?>
                </td>
                <td><?= htmlspecialchars($row['location']) ?></td>
                <td><?= htmlspecialchars($row['trainer_name']) ?></td>
                <td>
                    <span class="badge bg-<?=
                        $row['status'] == 'Active' ? 'success' :
                        ($row['status'] == 'Completed' ? 'primary' : 'danger') ?>">
                        <?= htmlspecialchars($row['status']) ?>
                    </span>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="view_offline_attendance.php?id=<?= $row['id'] ?>"
                           class="btn btn-sm btn-secondary" title="Lihat Absensi">
                            <i class="fas fa-list"></i>
                        </a>
                        <a href="rfid_attendance_offline.php?training_id=<?= $row['id'] ?>"
                           class="btn btn-sm btn-warning" title="Absensi RFID">
                            <i class="fas fa-id-card"></i>
                        </a>
                        <button type="button" class="btn btn-sm btn-primary edit-training"
                            data-id="<?= $row['id'] ?>"
                            data-topic="<?= htmlspecialchars($row['training_topic']) ?>"
                            data-description="<?= htmlspecialchars($row['training_description']) ?>"
                            data-start-date="<?= $row['start_date'] ?>"
                            data-end-date="<?= $row['end_date'] ?>"
                            data-time-start="<?= $row['training_time_start'] ?>"
                            data-time-end="<?= $row['training_time_end'] ?>"
                            data-location="<?= htmlspecialchars($row['location']) ?>"
                            data-trainer="<?= htmlspecialchars($row['trainer_name']) ?>"
                            data-max="<?= $row['max_participants'] ?>"
                            data-status="<?= $row['status'] ?>"
                            title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-training"
                            data-id="<?= $row['id'] ?>"
                            data-topic="<?= htmlspecialchars($row['training_topic']) ?>"
                            title="Hapus">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        <?php endwhile; ?>
    <?php else: ?>
        <tr>
            <td colspan="7" class="text-center">Tidak ada data Training Internal</td>
        </tr>
    <?php endif; ?>
</tbody>

                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Training -->
<div class="modal fade" id="addTrainingModal" tabindex="-1" aria-labelledby="addTrainingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTrainingModalLabel">Tambah Training Internal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="training_topic" class="form-label">Topik Training <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="training_topic" name="training_topic" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="training_description" class="form-label">Deskripsi</label>
                        <textarea class="form-control" id="training_description" name="training_description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">Tanggal Mulai <span class="text-danger">*</span></label>
                            <input type="date" class="form-control datepicker" id="start_date" name="start_date" value="<?= htmlspecialchars($selected_date) ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">Tanggal Selesai</label>
                            <input type="date" class="form-control datepicker" id="end_date" name="end_date" value="<?= htmlspecialchars($selected_date) ?>">
                            <small class="form-text text-muted">Kosongkan jika training hanya 1 hari</small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="training_time_start" class="form-label">Waktu Mulai</label>
                            <input type="time" class="form-control timepicker" id="training_time_start" name="training_time_start">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="training_time_end" class="form-label">Waktu Selesai</label>
                            <input type="time" class="form-control timepicker" id="training_time_end" name="training_time_end">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="location" class="form-label">Lokasi</label>
                            <input type="text" class="form-control" id="location" name="location">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="trainer_name" class="form-label">Nama Trainer</label>
                            <input type="text" class="form-control" id="trainer_name" name="trainer_name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="max_participants" class="form-label">Maksimal Peserta</label>
                            <input type="number" class="form-control" id="max_participants" name="max_participants" min="1">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select class="form-control" id="status" name="status" required>
                                <option value="Active">Active</option>
                                <option value="Completed">Completed</option>
                                <option value="Cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit Training -->
<div class="modal fade" id="editTrainingModal" tabindex="-1" aria-labelledby="editTrainingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTrainingModalLabel">Edit Training Internal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="training_id" id="edit_training_id">

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="edit_training_topic" class="form-label">Topik Training <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_training_topic" name="training_topic" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_training_description" class="form-label">Deskripsi</label>
                        <textarea class="form-control" id="edit_training_description" name="training_description" rows="3"></textarea>
                    </div>

                    <div class="row">
                         <div class="col-md-6 mb-3">
                            <label for="edit_start_date" class="form-label">Tanggal Mulai <span class="text-danger">*</span></label>
                            <input type="date" class="form-control datepicker" id="edit_start_date" name="start_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_end_date" class="form-label">Tanggal Selesai</label>
                            <input type="date" class="form-control datepicker" id="edit_end_date" name="end_date">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="edit_training_time_start" class="form-label">Waktu Mulai</label>
                            <input type="time" class="form-control timepicker" id="edit_training_time_start" name="training_time_start">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="edit_training_time_end" class="form-label">Waktu Selesai</label>
                            <input type="time" class="form-control timepicker" id="edit_training_time_end" name="training_time_end">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="edit_location" class="form-label">Lokasi</label>
                            <input type="text" class="form-control" id="edit_location" name="location">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_trainer_name" class="form-label">Nama Trainer</label>
                            <input type="text" class="form-control" id="edit_trainer_name" name="trainer_name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_max_participants" class="form-label">Maksimal Peserta</label>
                            <input type="number" class="form-control" id="edit_max_participants" name="max_participants" min="1">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select class="form-control" id="edit_status" name="status" required>
                                <option value="Active">Active</option>
                                <option value="Completed">Completed</option>
                                <option value="Cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Hapus Training -->
<div class="modal fade" id="deleteTrainingModal" tabindex="-1" aria-labelledby="deleteTrainingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteTrainingModalLabel">Konfirmasi Hapus</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus training <strong id="delete_training_topic"></strong>?</p>
                <p class="text-danger">Perhatian: Tindakan ini tidak dapat dibatalkan!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <form method="post" action="">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="training_id" id="delete_training_id">
                    <button type="submit" class="btn btn-danger">Hapus</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Include Flatpickr JS -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Flatpickr for date pickers
        flatpickr(".datepicker", {
            dateFormat: "Y-m-d",
            allowInput: true
        });

        // Initialize Flatpickr for time pickers
        flatpickr(".timepicker", {
            enableTime: true,
            noCalendar: true,
            dateFormat: "H:i",
            time_24hr: true,
            allowInput: true
        });

        // Add helpful behavior for date fields
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');

        if (startDateInput && endDateInput) {
            // When start date changes, update end date if it's earlier than start date
            startDateInput.addEventListener('change', function() {
                if (endDateInput.value && endDateInput.value < this.value) {
                    endDateInput.value = this.value;
                }
            });

            // When end date changes, clear it if it's the same as start date
            endDateInput.addEventListener('change', function() {
                if (this.value === startDateInput.value) {
                    this.value = '';
                }
            });
        }

        // Handle edit button click
        document.querySelectorAll('.edit-training').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const topic = this.getAttribute('data-topic');
                const description = this.getAttribute('data-description');
                const startDate = this.getAttribute('data-start-date');
                const endDate = this.getAttribute('data-end-date');
                const timeStart = this.getAttribute('data-time-start');
                const timeEnd = this.getAttribute('data-time-end');
                const location = this.getAttribute('data-location');
                const trainer = this.getAttribute('data-trainer');
                const max = this.getAttribute('data-max');
                const status = this.getAttribute('data-status');

                const editTrainingIdInput = document.getElementById('edit_training_id');
                if (editTrainingIdInput) editTrainingIdInput.value = id;

                const editTrainingTopicInput = document.getElementById('edit_training_topic');
                if (editTrainingTopicInput) editTrainingTopicInput.value = topic;

                const editTrainingDescriptionTextarea = document.getElementById('edit_training_description');
                if (editTrainingDescriptionTextarea) editTrainingDescriptionTextarea.value = description;

                const editStartDateInput = document.getElementById('edit_start_date');
                if (editStartDateInput) editStartDateInput.value = startDate;

                const editEndDateInput = document.getElementById('edit_end_date');
                if (editEndDateInput) editEndDateInput.value = endDate;

                const editTrainingTimeStartInput = document.getElementById('edit_training_time_start');
                if (editTrainingTimeStartInput) editTrainingTimeStartInput.value = timeStart;

                const editTrainingTimeEndInput = document.getElementById('edit_training_time_end');
                if (editTrainingTimeEndInput) editTrainingTimeEndInput.value = timeEnd;

                const editLocationInput = document.getElementById('edit_location');
                if (editLocationInput) editLocationInput.value = location;

                const editTrainerNameInput = document.getElementById('edit_trainer_name');
                if (editTrainerNameInput) editTrainerNameInput.value = trainer;

                const editMaxParticipantsInput = document.getElementById('edit_max_participants');
                if (editMaxParticipantsInput) editMaxParticipantsInput.value = max;

                const editStatusSelect = document.getElementById('edit_status');
                if (editStatusSelect) editStatusSelect.value = status;

                const editModal = new bootstrap.Modal(document.getElementById('editTrainingModal'));
                editModal.show();
            });
        });

        // Handle delete button click
        document.querySelectorAll('.delete-training').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const topic = this.getAttribute('data-topic');

                document.getElementById('delete_training_id').value = id;
                document.getElementById('delete_training_topic').textContent = topic;

                const deleteModal = new bootstrap.Modal(document.getElementById('deleteTrainingModal'));
                deleteModal.show();
            });
        });
    });

    // Auto-open edit modal if hash contains edit-{id}
    if (window.location.hash.startsWith('#edit-')) {
        const trainingId = window.location.hash.replace('#edit-', '');
        const editButton = document.querySelector(`button[data-id="${trainingId}"].edit-training`);
        if (editButton) {
            editButton.click();
            // Clear the hash after opening modal
            history.replaceState(null, null, window.location.pathname + window.location.search);
        }
    }
</script>

<?php include 'footer.php'; ?>
