<?php
include 'security.php';
include '../config/config.php';
include '../config/access_control.php';

// ===== GET USER DATA =====
// Semua user dapat mengajukan training internal, tidak ada batasan level
$user_id = $_SESSION['user_id'];

// Ambil data user dari database
$user_data = getUserLevel($user_id);
if (!$user_data) {
    $_SESSION['error_message'] = "Data user tidak ditemukan. Silakan login ulang.";
    header("Location: ../index.php");
    exit();
}

// Set session variables dengan fallback values
$_SESSION['full_name'] = $user_data['name'] ?? 'Pengguna';
$_SESSION['user_nik'] = $user_data['nik'] ?? '0000000000000000';
$_SESSION['user_email'] = $user_data['email'] ?? '';
$user_dept = $user_data['dept'] ?? 'Tidak tersedia';
$user_bagian = $user_data['bagian'] ?? 'Tidak tersedia';
$user_jabatan = $user_data['jabatan'] ?? 'Tidak tersedia';

// Generate token unik untuk form submission dan mencegah duplikasi
if (!isset($_SESSION['token'])) {
    $_SESSION['token'] = bin2hex(random_bytes(32));
}

// Generate token unik untuk mencegah duplikasi submit
$form_token = md5(uniqid(mt_rand(), true));
$_SESSION['form_token'] = $form_token;
$_SESSION['form_token_time'] = time();
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<link rel="stylesheet" href="../assets/css/form-style.css">
<style>
    .form-container {
        max-width: 900px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-top: 20px;
    }

    .form-header {
        background: linear-gradient(135deg, #c40000 0%, #dc3545 100%);
        color: white !important;
        padding: 30px;
        text-align: center;
    }

    .form-header h1 {
        color: white !important;

        margin: 0;
        font-size: 2rem;
        font-weight: 600;
    }

    .form-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
    }

    .form-section {
        padding: 30px;
        border-bottom: 1px solid #eee;
    }

    .form-section:last-child {
        border-bottom: none;
    }

    .form-section h2 {
        color: #333;
        margin-bottom: 20px;
        font-size: 1.3rem;
        font-weight: 600;
        border-left: 4px solid #c40000;
        padding-left: 15px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #555;
    }

    .form-group label.required::after {
        content: " *";
        color: #e74c3c;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.3s ease;
        background-color: #fff;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #c40000;
        box-shadow: 0 0 0 3px rgba(196, 0, 0, 0.1);
    }

    .form-group input[readonly] {
        background-color: #f8f9fa;
        color: #6c757d;
    }

    .form-text {
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
    }

    .btn-submit {
        background: linear-gradient(135deg, #c40000 0%, #dc3545 100%);
        color: white;
        border: none;
        padding: 15px 40px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        margin-top: 20px;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(196, 0, 0, 0.4);
    }

    .btn-submit:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .row {
        display: flex;
        gap: 20px;
        margin: -10px;
    }

    .col-md-6 {
        flex: 1;
        padding: 10px;
    }

    .col-md-4 {
        flex: 0 0 33.333%;
        padding: 10px;
    }

    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        border: none;
    }

    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border-left: 4px solid #28a745;
    }

    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #c40000;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 16px;
    }

    @media (max-width: 768px) {
        .row {
            flex-direction: column;
        }

        .col-md-6,
        .col-md-4 {
            flex: none;
        }

        .form-container {
            margin: 10px;
            border-radius: 10px;
        }

        .form-header {
            padding: 20px;
        }

        .form-section {
            padding: 20px;
        }
    }
</style>

<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">Memproses pengajuan training internal...</div>
        </div>
    </div>

    <?php include '../config/navbara.php'; ?>
    <div class="jarak-form"></div>
<!-- 
    <div class="container-form">
        <!-- Notifications will be handled by JavaScript -->
        <?php if (isset($_SESSION['errors'])): ?>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    <?php foreach ($_SESSION['errors'] as $error): ?>
                        alert("<?php echo addslashes(htmlspecialchars($error)); ?>");
                    <?php endforeach; ?>
                });
            </script>
            <?php unset($_SESSION['errors']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['success'])): ?>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    alert("<?php echo addslashes(htmlspecialchars($_SESSION['success'])); ?>");
                });
            </script>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>
<!-- 
        <div class="form-container">
            <div class="form-header">
                <h1><i class="fas fa-building"></i> Form Training Internal</h1>
                <p>Terbuka untuk semua karyawan - Silakan lengkapi informasi di bawah ini untuk mengajukan training internal</p>
            </div>

            <form action="submit_internal_training.php" method="POST" id="internalTrainingForm">
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['token']); ?>">
                <input type="hidden" name="form_token" value="<?php echo htmlspecialchars($form_token); ?>">

                Informasi Pemohon
                <div class="form-section">
                    <h2><i class="fas fa-user"></i> Informasi Pemohon</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required" for="full_name">Nama Pemohon</label>
                                <input type="text" id="full_name" name="full_name" required readonly
                                       value="<?= htmlspecialchars($_SESSION['full_name'] ?? 'Pengguna') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required" for="nik">NIK</label>
                                <input type="text" id="nik" name="nik" required readonly
                                       value="<?= htmlspecialchars($_SESSION['user_nik'] ?? '0000000000000000') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="required" for="department">Departemen</label>
                                <input type="text" id="department" name="department" required readonly
                                       value="<?= htmlspecialchars($user_dept ?? 'Tidak tersedia') ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="required" for="bagian">Bagian</label>
                                <input type="text" id="bagian" name="bagian" required readonly
                                       value="<?= htmlspecialchars($user_bagian ?? 'Tidak tersedia') ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="required" for="jabatan">Jabatan</label>
                                <input type="text" id="jabatan" name="jabatan" required readonly
                                       value="<?= htmlspecialchars($user_jabatan ?? 'Tidak tersedia') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="required" for="email">Email</label>
                        <input type="email" id="email" name="email" required
                               value="<?= htmlspecialchars($_SESSION['user_email'] ?? '') ?>"
                               placeholder="<EMAIL>">
                        <small class="form-text">Email dapat diubah jika diperlukan</small>
                    </div>
                </div>

                <!-- Informasi Training Internal -->
                <!-- <div class="form-section">
                    <h2><i class="fas fa-chalkboard-teacher"></i> Informasi Training Internal</h2>

                    <div class="form-group">
                        <label class="required" for="training_topic">Nama/Topik Training</label>
                        <input type="text" id="training_topic" name="training_topic" required
                               placeholder="Masukkan nama/topik training internal yang diinginkan">
                        <small class="form-text">Contoh: Leadership Development, Team Building, Technical Skills, dll</small>
                    </div>

                    <div class="form-group">
                        <label class="required" for="training_description">Deskripsi Training</label>
                        <textarea id="training_description" name="training_description" rows="4" required
                                  placeholder="Jelaskan detail training yang diinginkan, tujuan, dan materi yang diharapkan"></textarea>
                        <small class="form-text">Berikan deskripsi lengkap tentang training yang diinginkan</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required" for="start_date">Tanggal Mulai Training</label>
                                <input type="date" id="start_date" name="start_date" required
                                       min="<?= date('Y-m-d', strtotime('+1 week')); ?>">
                                <small class="form-text">Tanggal training minimal 1 minggu dari hari ini</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="end_date">Tanggal Selesai Training</label>
                                <input type="date" id="end_date" name="end_date">
                                <small class="form-text">Kosongkan jika training hanya 1 hari</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="training_time_start">Waktu Mulai</label>
                                <input type="time" id="training_time_start" name="training_time_start" value="08:00">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="training_time_end">Waktu Selesai</label>
                                <input type="time" id="training_time_end" name="training_time_end" value="17:00">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="location">Lokasi Training</label>
                                <input type="text" id="location" name="location"
                                       placeholder="Contoh: Ruang Meeting A, Training Room, dll">
                                <small class="form-text">Lokasi yang diinginkan untuk pelaksanaan training</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="max_participants">Estimasi Jumlah Peserta</label>
                                <input type="number" id="max_participants" name="max_participants" min="1" max="100"
                                       placeholder="Contoh: 20">
                                <small class="form-text">Perkiraan jumlah peserta yang akan mengikuti training</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="trainer_preference">Preferensi Trainer</label>
                        <textarea id="trainer_preference" name="trainer_preference" rows="3"
                                  placeholder="Jelaskan preferensi trainer (internal/eksternal, keahlian yang dibutuhkan, dll)"></textarea>
                        <small class="form-text">Opsional: Jelaskan jenis trainer yang diinginkan</small>
                    </div>

                    <div class="form-group">
                        <label class="required" for="training_skill_type">Jenis Kompetensi Training</label>
                        <select name="training_skill_type" id="training_skill_type" required>
                            <option value="" disabled selected>Pilih jenis kompetensi</option>
                            <option value="Soft Skill">Soft Skill</option>
                            <option value="Technical Skill">Technical Skill</option>
                            <option value="Compliance">Compliance</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="required" for="urgency_reason">Alasan & Urgensi Training</label>
                        <textarea id="urgency_reason" name="urgency_reason" rows="4" required
                                  placeholder="Jelaskan mengapa training ini dibutuhkan, urgensi pelaksanaan, dan manfaat yang diharapkan"></textarea>
                        <small class="form-text">Jelaskan latar belakang kebutuhan training, urgensi, dan dampak yang diharapkan</small>
                    </div>
                </div>

                <div class="form-section">
                    <button type="submit" class="btn-submit" id="submitButton">
                        <i class="fas fa-paper-plane"></i> Kirim Pengajuan Training Internal
                    </button>
                </div>
            </form>
        </div>
    </div>  -->

    <div class="form-container">
        <br>
        <br>
        <h1><i class="fas fa-wrench"></i> Halaman sedang dikembangkan</h1>
        <p>Maaf, fitur ini sedang dalam proses pengembangan. Terima kasih atas kesabaran Anda.</p>
        <a href="dashboard.php" class="btn btn-primary">Kembali ke Dashboard</a>
        
        <br>
        <br>
        <br>

    </div>
    <footer>
        <?php include '../config/footer.php'; ?>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('internalTrainingForm');
            const submitButton = document.getElementById('submitButton');
            const loadingOverlay = document.getElementById('loadingOverlay');

            // Set minimum date untuk input tanggal training (1 minggu dari hari ini)
            const startDateInput = document.getElementById('start_date');
            const endDateInput = document.getElementById('end_date');

            if (startDateInput) {
                const today = new Date();
                const minDate = new Date(today.setDate(today.getDate() + 7));
                startDateInput.min = minDate.toISOString().split('T')[0];

                // Validasi saat input berubah
                startDateInput.addEventListener('change', function() {
                    const selectedDate = new Date(this.value);
                    const minDate = new Date();
                    minDate.setDate(minDate.getDate() + 7);

                    if (selectedDate < minDate) {
                        alert('Tanggal training harus minimal 1 minggu dari hari ini');
                        this.value = '';
                        return;
                    }

                    // Update end date minimum
                    if (endDateInput) {
                        endDateInput.min = this.value;
                        if (endDateInput.value && endDateInput.value < this.value) {
                            endDateInput.value = this.value;
                        }
                    }
                });
            }

            // Validasi end date
            if (endDateInput) {
                endDateInput.addEventListener('change', function() {
                    if (startDateInput.value && this.value < startDateInput.value) {
                        alert('Tanggal selesai tidak boleh lebih awal dari tanggal mulai');
                        this.value = startDateInput.value;
                    }
                });
            }

            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // Validasi form
                if (!validateForm()) {
                    return false;
                }

                // Show loading
                loadingOverlay.style.display = 'flex';
                submitButton.disabled = true;

                // Submit form
                this.submit();
            });

            function validateForm() {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.focus();
                        alert('Mohon lengkapi semua field yang wajib diisi');
                        isValid = false;
                        return false;
                    }
                });

                return isValid;
            }
        });
    </script>
</body>
</html>
