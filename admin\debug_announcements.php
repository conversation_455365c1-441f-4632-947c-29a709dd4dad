<?php
/**
 * Debug Announcements - Check announcement recipients data
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header("Location: ../login.php");
    exit();
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Announcements - Training Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-bug"></i> Debug Announcements & Recipients</h2>
                <p class="text-muted">Halaman ini untuk debugging data pengumuman dan penerima.</p>

                <div class="row">
                    <!-- Announcements Table -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-bullhorn"></i> Announcements Table</h5>
                            </div>
                            <div class="card-body">
                                <?php
                                $announcements_query = "SELECT * FROM announcements ORDER BY created_at DESC";
                                $result = $conn->query($announcements_query);

                                if ($result && $result->num_rows > 0) {
                                    echo '<div class="table-responsive">';
                                    echo '<table class="table table-sm table-bordered">';
                                    echo '<thead class="table-dark">';
                                    echo '<tr><th>ID</th><th>Title</th><th>Target Role</th><th>Active</th><th>Created</th></tr>';
                                    echo '</thead><tbody>';

                                    while ($row = $result->fetch_assoc()) {
                                        echo '<tr>';
                                        echo '<td>' . $row['id'] . '</td>';
                                        echo '<td>' . htmlspecialchars(substr($row['title'], 0, 30)) . '...</td>';
                                        echo '<td>' . ($row['target_role'] ?: 'NULL') . '</td>';
                                        echo '<td>' . ($row['active'] ? 'Yes' : 'No') . '</td>';
                                        echo '<td>' . date('d/m H:i', strtotime($row['created_at'])) . '</td>';
                                        echo '</tr>';
                                    }

                                    echo '</tbody></table>';
                                    echo '</div>';
                                } else {
                                    echo '<div class="alert alert-info">No announcements found.</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>

                    <!-- Recipients Table -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-users"></i> Announcement Recipients Table</h5>
                            </div>
                            <div class="card-body">
                                <?php
                                $recipients_query = "SELECT ar.*, a.title, u.name as user_name, u.email
                                                   FROM announcement_recipients ar
                                                   LEFT JOIN announcements a ON ar.announcement_id = a.id
                                                   LEFT JOIN users u ON ar.user_id = u.id
                                                   ORDER BY ar.announcement_id DESC, u.name";
                                $result = $conn->query($recipients_query);

                                if ($result && $result->num_rows > 0) {
                                    echo '<div class="table-responsive">';
                                    echo '<table class="table table-sm table-bordered">';
                                    echo '<thead class="table-dark">';
                                    echo '<tr><th>Ann ID</th><th>User</th><th>Email</th><th>Assigned</th></tr>';
                                    echo '</thead><tbody>';

                                    while ($row = $result->fetch_assoc()) {
                                        echo '<tr>';
                                        echo '<td>' . $row['announcement_id'] . '</td>';
                                        echo '<td>' . htmlspecialchars($row['user_name'] ?: 'Unknown') . '</td>';
                                        echo '<td>' . htmlspecialchars($row['email'] ?: 'N/A') . '</td>';
                                        echo '<td>' . date('d/m H:i', strtotime($row['assigned_at'])) . '</td>';
                                        echo '</tr>';
                                    }

                                    echo '</tbody></table>';
                                    echo '</div>';
                                } else {
                                    echo '<div class="alert alert-info">No announcement recipients found.</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed View -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list-alt"></i> Detailed Announcement Recipients</h5>
                            </div>
                            <div class="card-body">
                                <?php
                                $detailed_query = "SELECT a.id, a.title, a.target_role, a.active,
                                                         GROUP_CONCAT(CONCAT(u.name, ' (', u.email, ' - ', r.role_name, ')') SEPARATOR ', ') as recipients,
                                                         COUNT(ar.user_id) as recipient_count,
                                                         GROUP_CONCAT(DISTINCT u.role_id) as recipient_roles
                                                  FROM announcements a
                                                  LEFT JOIN announcement_recipients ar ON a.id = ar.announcement_id
                                                  LEFT JOIN users u ON ar.user_id = u.id
                                                  LEFT JOIN roles r ON u.role_id = r.id
                                                  GROUP BY a.id
                                                  ORDER BY a.created_at DESC";
                                $result = $conn->query($detailed_query);

                                if ($result && $result->num_rows > 0) {
                                    while ($row = $result->fetch_assoc()) {
                                        $status_class = $row['active'] ? 'success' : 'secondary';

                                        // Determine recipient type
                                        $recipient_type = 'Unknown';
                                        $recipient_info = '';

                                        if ($row['recipient_count'] > 0) {
                                            // Count total active users
                                            $total_users_query = "SELECT COUNT(*) as total FROM users WHERE is_active = 1";
                                            $total_result = $conn->query($total_users_query);
                                            $total_users = $total_result->fetch_assoc()['total'];

                                            if ($row['recipient_count'] == $total_users) {
                                                $recipient_type = 'All Users';
                                                $recipient_info = "Sent to all {$row['recipient_count']} active users";
                                            } else {
                                                // Check if single role
                                                $roles = explode(',', $row['recipient_roles']);
                                                if (count($roles) == 1 && $roles[0]) {
                                                    $role_id = $roles[0];
                                                    $role_users_query = "SELECT COUNT(*) as total FROM users WHERE is_active = 1 AND role_id = ?";
                                                    $stmt = $conn->prepare($role_users_query);
                                                    $stmt->bind_param("s", $role_id);
                                                    $stmt->execute();
                                                    $role_total = $stmt->get_result()->fetch_assoc()['total'];
                                                    $stmt->close();

                                                    if ($row['recipient_count'] == $role_total) {
                                                        $recipient_type = 'Role-based';
                                                        $role_names = ['1' => 'Pemohon', '2' => 'Dept Head', '3' => 'HRD', '4' => 'GA', '5' => 'Factory Manager', '6' => 'Direktur', '99' => 'Admin'];
                                                        $role_name = $role_names[$role_id] ?? "Role $role_id";
                                                        $recipient_info = "Sent to all {$row['recipient_count']} users with role: $role_name";
                                                    } else {
                                                        $recipient_type = 'Specific Users';
                                                        $recipient_info = "Sent to {$row['recipient_count']} specific users";
                                                    }
                                                } else {
                                                    $recipient_type = 'Specific Users';
                                                    $recipient_info = "Sent to {$row['recipient_count']} specific users";
                                                }
                                            }
                                        } else {
                                            $recipient_type = 'No Recipients';
                                            $recipient_info = "No recipients found";
                                        }

                                        echo '<div class="card mb-3">';
                                        echo '<div class="card-header d-flex justify-content-between align-items-center">';
                                        echo '<h6 class="mb-0">' . htmlspecialchars($row['title']) . '</h6>';
                                        echo '<span class="badge bg-' . $status_class . '">' . ($row['active'] ? 'Active' : 'Inactive') . '</span>';
                                        echo '</div>';
                                        echo '<div class="card-body">';
                                        echo '<p><strong>Announcement ID:</strong> ' . $row['id'] . '</p>';
                                        echo '<p><strong>Recipient Type:</strong> <span class="badge bg-info">' . $recipient_type . '</span></p>';
                                        echo '<p><strong>Recipients:</strong> ' . $recipient_info . '</p>';

                                        if ($row['recipients']) {
                                            echo '<div class="mt-2">';
                                            echo '<strong>Recipient Details:</strong><br>';
                                            echo '<small class="text-muted">' . htmlspecialchars($row['recipients']) . '</small>';
                                            echo '</div>';
                                        }
                                        echo '</div>';
                                        echo '</div>';
                                    }
                                } else {
                                    echo '<div class="alert alert-info">No announcements found.</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test User View -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-eye"></i> Test User View</h5>
                                <p class="mb-0 text-muted">Simulasi pengumuman yang akan dilihat oleh user tertentu</p>
                            </div>
                            <div class="card-body">
                                <form method="GET" class="mb-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">Pilih User untuk Test:</label>
                                            <select name="test_user_id" class="form-select">
                                                <option value="">-- Pilih User --</option>
                                                <?php
                                                $users_query = "SELECT id, name, email, role_id FROM users WHERE is_active = 1 ORDER BY name";
                                                $users_result = $conn->query($users_query);
                                                while ($user = $users_result->fetch_assoc()) {
                                                    $selected = (isset($_GET['test_user_id']) && $_GET['test_user_id'] == $user['id']) ? 'selected' : '';
                                                    echo '<option value="' . $user['id'] . '" ' . $selected . '>' .
                                                         htmlspecialchars($user['name']) . ' (' . $user['email'] . ') - Role ' . $user['role_id'] . '</option>';
                                                }
                                                ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 d-flex align-items-end">
                                            <button type="submit" class="btn btn-primary">Test View</button>
                                        </div>
                                    </div>
                                </form>

                                <?php if (isset($_GET['test_user_id']) && !empty($_GET['test_user_id'])): ?>
                                    <?php
                                    $test_user_id = intval($_GET['test_user_id']);
                                    $current_date = date('Y-m-d');

                                    // Get user info
                                    $user_info_query = "SELECT name, email, role_id FROM users WHERE id = ?";
                                    $stmt = $conn->prepare($user_info_query);
                                    $stmt->bind_param("i", $test_user_id);
                                    $stmt->execute();
                                    $user_info = $stmt->get_result()->fetch_assoc();
                                    $stmt->close();

                                    if ($user_info) {
                                        echo '<div class="alert alert-info">';
                                        echo '<strong>Testing for:</strong> ' . htmlspecialchars($user_info['name']) . ' (' . $user_info['email'] . ') - Role ' . $user_info['role_id'];
                                        echo '</div>';

                                        // Test the query from dashboard
                                        $test_query = "SELECT a.*, u.name as creator_name
                                                      FROM announcements a
                                                      LEFT JOIN users u ON a.created_by = u.id
                                                      LEFT JOIN announcement_recipients ar ON a.id = ar.announcement_id
                                                      WHERE a.active = 1
                                                      AND (a.expiry_date IS NULL OR a.expiry_date >= ?)
                                                      AND (
                                                          (ar.user_id = ?)
                                                          OR (NOT EXISTS (SELECT 1 FROM announcement_recipients WHERE announcement_id = a.id)
                                                              AND (a.target_role IS NULL OR a.target_role = '' OR a.target_role = ?))
                                                      )
                                                      GROUP BY a.id
                                                      ORDER BY a.created_at DESC";

                                        $stmt = $conn->prepare($test_query);
                                        $stmt->bind_param("sis", $current_date, $test_user_id, $user_info['role_id']);
                                        $stmt->execute();
                                        $test_result = $stmt->get_result();

                                        if ($test_result->num_rows > 0) {
                                            echo '<h6>Announcements visible to this user:</h6>';
                                            while ($announcement = $test_result->fetch_assoc()) {
                                                echo '<div class="border p-2 mb-2 rounded">';
                                                echo '<strong>' . htmlspecialchars($announcement['title']) . '</strong><br>';
                                                echo '<small class="text-muted">Created by: ' . htmlspecialchars($announcement['creator_name']) . '</small>';
                                                echo '</div>';
                                            }
                                        } else {
                                            echo '<div class="alert alert-warning">No announcements visible to this user.</div>';
                                        }
                                        $stmt->close();
                                    }
                                    ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <a href="manage_announcements.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Manage Announcements
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
