<?php
// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Set header untuk download file Excel
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="Data_Karyawan_Untuk_Dihapus.xlsx"');
header('Cache-Control: max-age=0');

// Require library PhpSpreadsheet
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// Buat spreadsheet baru
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('Data Karyawan');

// Definisikan header kolom
$columns = [
    'A' => 'NIK*',
    'B' => 'Nama',
    'C' => 'Tgl Masuk',
    'D' => 'JK',
    'E' => 'Level Karyawan',
    'F' => 'Tgl Lahir',
    'G' => 'Agama',
    'H' => 'Pendidikan Akhir',
    'I' => 'No. Telp',
    'J' => 'Dept',
    'K' => 'Bagian',
    'L' => 'Jabatan',
    'M' => 'Group',
    'N' => 'Status',
    'O' => 'PT'
];

// Set header kolom
foreach ($columns as $col => $value) {
    $sheet->setCellValue($col . '1', $value);
}

// Ambil data karyawan dari database
$query = "SELECT * FROM karyawan ORDER BY nik";
$result = $conn->query($query);

// Tambahkan data karyawan ke sheet
$row = 2;
while ($data = $result->fetch_assoc()) {
    $sheet->setCellValue('A' . $row, $data['nik']);
    $sheet->setCellValue('B' . $row, $data['nama']);
    $sheet->setCellValue('C' . $row, $data['tgl_masuk']);
    $sheet->setCellValue('D' . $row, $data['jk']);
    $sheet->setCellValue('E' . $row, $data['level_karyawan']);
    $sheet->setCellValue('F' . $row, $data['tgl_lahir']);
    $sheet->setCellValue('G' . $row, $data['agama']);
    $sheet->setCellValue('H' . $row, $data['pendidikan_akhir']);
    $sheet->setCellValue('I' . $row, $data['no_telp']);
    $sheet->setCellValue('J' . $row, $data['dept']);
    $sheet->setCellValue('K' . $row, $data['bagian']);
    $sheet->setCellValue('L' . $row, $data['jabatan']);
    $sheet->setCellValue('M' . $row, $data['group']);
    $sheet->setCellValue('N' . $row, $data['status']);
    $sheet->setCellValue('O' . $row, $data['pt']);
    $row++;
}

// Tambahkan sheet kedua untuk petunjuk
$instructionSheet = $spreadsheet->createSheet();
$instructionSheet->setTitle('Petunjuk');

// Tambahkan petunjuk
$instructions = [
    ['Petunjuk Penghapusan Masal Karyawan'],
    [''],
    ['1. File ini berisi data semua karyawan yang ada di sistem.'],
    ['2. Untuk menghapus karyawan, PERTAHANKAN data karyawan yang ingin dihapus dalam file ini.'],
    ['3. HAPUS baris data karyawan yang TIDAK ingin dihapus dari sistem.'],
    ['4. Kolom NIK (kolom A) adalah wajib dan digunakan sebagai kunci untuk menghapus data.'],
    ['5. Jangan mengubah nilai NIK, karena akan digunakan sebagai referensi untuk penghapusan.'],
    ['6. Jangan mengubah nama kolom atau urutan kolom.'],
    ['7. Setelah selesai mengedit, simpan file dan upload melalui menu "Hapus Karyawan Masal".'],
    ['8. PERHATIAN: Tindakan penghapusan tidak dapat dibatalkan. Pastikan data yang akan dihapus sudah benar.'],
    [''],
    ['Contoh:'],
    ['- Jika ada 100 karyawan dalam file ini dan Anda ingin menghapus 10 karyawan,'],
    ['- Hapus 90 baris karyawan yang TIDAK ingin dihapus, sehingga hanya tersisa 10 baris karyawan yang ingin dihapus.'],
    ['- Upload file yang sudah diedit, dan sistem akan menghapus 10 karyawan tersebut.'],
];

// Tambahkan petunjuk ke sheet
$row = 1;
foreach ($instructions as $instruction) {
    if (count($instruction) == 1) {
        $instructionSheet->setCellValue('A' . $row, $instruction[0]);
        if ($row == 1) {
            // Judul petunjuk
            $instructionSheet->mergeCells('A1:B1');
            $instructionSheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        }
    } else if (count($instruction) == 2) {
        $instructionSheet->setCellValue('A' . $row, $instruction[0]);
        $instructionSheet->setCellValue('B' . $row, $instruction[1]);
    }
    $row++;
}

// Set lebar kolom
foreach (range('A', 'O') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// Set style untuk header
$headerStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FFFFFF'],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'BF0000'],
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['rgb' => '000000'],
        ],
    ],
];

$sheet->getStyle('A1:O1')->applyFromArray($headerStyle);
$sheet->getRowDimension(1)->setRowHeight(30);

// Set style untuk data
$dataStyle = [
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['rgb' => '000000'],
        ],
    ],
    'alignment' => [
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
];

$sheet->getStyle('A2:O' . ($row - 1))->applyFromArray($dataStyle);

// Set style untuk petunjuk
$instructionSheet->getColumnDimension('A')->setWidth(15);
$instructionSheet->getColumnDimension('B')->setWidth(80);

// Freeze pane pada header
$sheet->freezePane('A2');

// Aktifkan filter
$sheet->setAutoFilter('A1:O1');

// Set sheet pertama sebagai active sheet
$spreadsheet->setActiveSheetIndex(0);

// Tulis ke output
$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit;
