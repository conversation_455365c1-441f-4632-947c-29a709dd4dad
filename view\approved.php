<?php
session_start();

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Include database connection file
include '../config/config.php';

$query = "SELECT ts.id, ts.full_name, ts.email, ts.phone, ts.training_topic, ts.start_date, ts.status, u.name AS approved_by_name
          FROM training_submissions ts
          LEFT JOIN users u ON ts.approved_by = u.id
          WHERE ts.status = 'pending' AND ts.start_date >= CURDATE()";

$query_completed = "SELECT ts.id, ts.full_name, ts.email, ts.phone, ts.training_topic, ts.start_date, ts.status, u.name AS approved_by_name
                   FROM training_submissions ts
                   LEFT JOIN users u ON ts.approved_by = u.id
                   WHERE ts.status = 'approved' AND ts.start_date >= CURDATE()";

$query_canceled = "SELECT ts.id, ts.full_name, ts.email, ts.phone, ts.training_topic, ts.start_date, ts.status,
u.name AS approved_by_name, c.name AS canceled_by_name
FROM training_submissions ts
LEFT JOIN users u ON ts.approved_by = u.id
LEFT JOIN users c ON ts.canceled_by = c.id
WHERE ts.status = 'canceled' AND ts.start_date >= CURDATE()";

// Fetch training submissions (pending)
$result = mysqli_query($conn, $query);
$submissions = [];
while ($row = mysqli_fetch_assoc($result)) {
    $submissions[] = $row;
}

// Fetch completed training data (approved)
$result_completed = mysqli_query($conn, $query_completed);
$submissions_completed = mysqli_fetch_all($result_completed, MYSQLI_ASSOC);

// Fetch canceled training data
$result_canceled = mysqli_query($conn, $query_canceled);
$submissions_canceled = mysqli_fetch_all($result_canceled, MYSQLI_ASSOC);

// Membatalkan training yang sudah lewat tanggal
foreach ($submissions as $row) {
    if ($row['start_date'] < date('Y-m-d') && $row['status'] === 'pending') {
        $stmt = $conn->prepare("UPDATE training_submissions SET status='canceled', canceled_by=?, canceled_at=? WHERE id=?");
        $current_datetime = date('Y-m-d H:i:s');
        $stmt->bind_param("isi", $_SESSION['user_id'], $current_datetime, $row['id']);
        $stmt->execute();
    }
}

// Proses Approve Training
if (isset($_GET['approve_id'])) {
    $id = $_GET['approve_id'];
    $approved_by = $_SESSION['user_id'];

    // Validasi ID yang diminta ada dalam database
    $stmt = $conn->prepare("SELECT id FROM training_submissions WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Update status menjadi approved
        $stmt = $conn->prepare("UPDATE training_submissions SET status='approved', approved_by=? WHERE id=?");
        $stmt->bind_param("ii", $approved_by, $id); // approved_by berisi ID user yang meng-approve

        if ($stmt->execute()) {
            header("Location: approved.php");
            exit();
        }
    } else {
        echo "ID tidak valid.";
    }
}

// Proses Hapus Training (Membatalkan)
if (isset($_GET['delete_id'])) {
    $delete_id = $_GET['delete_id'];
    $canceled_by = $_SESSION['user_id']; // ID pengguna yang membatalkan
    $canceled_at = date('Y-m-d H:i:s'); // Waktu pembatalan

    // Validasi ID yang diminta ada dalam database
    $stmt = $conn->prepare("SELECT id FROM training_submissions WHERE id = ?");
    $stmt->bind_param("i", $delete_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Update status menjadi canceled
        $stmt = $conn->prepare("UPDATE training_submissions SET status='canceled', canceled_by=?, canceled_at=? WHERE id=?");
        $stmt->bind_param("isi", $canceled_by, $canceled_at, $delete_id); // canceled_by berisi ID user yang membatalkan

        if ($stmt->execute()) {
            header("Location: approved.php");
            exit();
        }
    } else {
        echo "ID tidak valid.";
    }
}
?>

<!DOCTYPE html>
<html lang="id">
    <?php include '../config/head.php'; ?>
    <style>
        .form-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .form-header {
            text-align: center;
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 10px;
            text-align: left;
            border: 1px solid #ddd;
        }

        th {
            background-color: #c40000;
            color: white;
        }

        td {
            background-color: #f9f9f9;
        }

        tr:nth-child(even) td {
            background-color: #f2f2f2;
        }

        tr:hover td {
            background-color: #e0e0e0;
        }

        .error-message {
            text-align: center;
            color:rgb(0, 0, 0);
        }

        .btn-approve, .btn-delete, .btn-detail {
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            display: inline-block;
            margin-right: 10px;
        }

        .btn-detail {
            background-color: #2196F3;
            color: white;
        }

        .btn-detail:hover {
            background-color: #03a9f4;
        }

        .btn-approve {
            background-color: #4CAF50;
            color: white;
        }

        .btn-approve:hover {
            background-color: #45a049;
        }

        .btn-delete {
            background-color: #f44336;
            color: white;
        }

        .btn-delete:hover {
            background-color: #d32f2f;
        }
    </style>
<body>

<?php include '../config/navbar.php';?>
<div class="container-form">
    <div class="form-container">
        <div class="form-header">
            <h1>Training yang Belum di-Approve</h1>
            <p>Daftar training yang belum disetujui.</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>No</th>
                    <th>Nama</th>
                    <th>Email</th>
                    <th>Telepon</th>
                    <th>Jenis Training</th>
                    <th>Tanggal Pengajuan</th>
                    <th>Detail</th>
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
    <?php
        $no = 1;
        $hasPending = false; // Flag untuk mengecek apakah ada data pending

        foreach ($submissions as $row) {
            if ($row['status'] === 'pending') {
                $hasPending = true;
                echo "<tr>";
                echo "<td>{$no}</td>";
                echo "<td>" . htmlspecialchars($row['full_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['email']) . "</td>";
                echo "<td>" . htmlspecialchars($row['phone']) . "</td>";
                echo "<td>" . htmlspecialchars($row['training_topic']) . "</td>";
                echo "<td>" . htmlspecialchars($row['start_date']) . "</td>";
                echo "<td><a class='btn-detail' href='training_detail.php?id={$row['id']}'>Detail</a></td>";
                echo "<td><a class='btn-approve' href='approved.php?approve_id={$row['id']}' onclick='return confirm(\"Apakah Anda yakin ingin menyetujui training ini?\")'>Approve</a></td>";
                echo "</tr>";
                $no++;
            }
        }

        // Jika tidak ada data pending, tampilkan pesan
        if (!$hasPending) {
            echo "<tr><td colspan='8' class='error-message'>Tidak ada training yang perlu di-approve.</td></tr>";
        }
    ?>
</tbody>
        </table>
    </div>

    <div class="form-container">
        <div class="form-header">
            <h1>Training yang Sudah di-Approve</h1>
            <p>Daftar training yang sudah disetujui.</p>
        </div>
        <div style="overflow-x: auto;">
            <table>
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Nama</th>
                        <th>Email</th>
                        <th>Telepon</th>
                        <th>Jenis Training</th>
                        <th>Tanggal Pengajuan</th>
                        <th>Approved By</th>
                    <th>Detail</th>
                        <th>Cancel</th>
                    </tr>
                </thead>
                <tbody>
                <?php
    $no = 1;
    $hasApproved = false; // Flag to check if there are any approved submissions
    foreach ($submissions_completed as $row) {
        echo "<tr>";
        echo "<td>{$no}</td>";
        echo "<td>" . htmlspecialchars($row['full_name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['email']) . "</td>";
        echo "<td>" . htmlspecialchars($row['phone']) . "</td>";
        echo "<td>" . htmlspecialchars($row['training_topic']) . "</td>";
        echo "<td>" . htmlspecialchars($row['start_date']) . "</td>";
        echo "<td>" . htmlspecialchars($row['approved_by_name'] ?? 'Admin') . "</td>";
        echo "<td><a class='btn-detail' href='training_detail.php?id={$row['id']}'>Detail</a></td>";
        echo "<td><a class='btn-delete' href='approved.php?delete_id={$row['id']}' onclick='return confirm(\"Apakah Anda yakin ingin membatalkan training ini?\")'>Cancel</a></td>";
        echo "</tr>";
        $no++;

        // Ubah flag menjadi true jika ada data yang approved
        $hasApproved = true;
    }

    // Show a message if no approved submissions are found
    if (!$hasApproved) {
        echo "<tr><td colspan='9' class='error-message'>Tidak ada training yang perlu di-approve.</td></tr>";
    }
?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="form-container">
    <div class="form-header">
        <h1>Training yang Dibatalkan</h1>
        <p>Daftar training yang telah dibatalkan.</p>
    </div>
    <div style="overflow-x: auto;">
        <table>
            <thead>
                <tr>
                    <th>No</th>
                    <th>Nama</th>
                    <th>Email</th>
                    <th>Telepon</th>
                    <th>Jenis Training</th>
                    <th>Tanggal Pengajuan</th>
                    <th>Canceled by</th>
                    <th>Detail</th>

                </tr>
            </thead>
            <tbody>
            <?php
        $no = 1;
        $hascancelled = false; // Initialize the variable
            foreach ($submissions_canceled as $row) {
                echo "<tr>";
                echo "<td>{$no}</td>";
                echo "<td>" . htmlspecialchars($row['full_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['email']) . "</td>";
                echo "<td>" . htmlspecialchars($row['phone']) . "</td>";
                echo "<td>" . htmlspecialchars($row['training_topic']) . "</td>";
                echo "<td>" . htmlspecialchars($row['start_date']) . "</td>";
        echo "<td><a class='btn-detail' href='training_detail.php?id={$row['id']}'>Detail</a></td>";
                echo "</tr>";
                $no++;



            // Set flag to true if there are canceled records
            $hascancelled = true;
        }
        if (!$hascancelled) {
            echo "<tr><td colspan='8' class='error-message'>Tidak ada training yang di-Batalkan.</td></tr>";
        };
        ?>
            </tbody>
        </table>
    </div>
</div>
</div>
<?php include '../config/footer.php';?>
</body>
</html>
