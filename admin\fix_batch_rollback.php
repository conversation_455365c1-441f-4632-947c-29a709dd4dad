<?php
/**
 * Fix Batch Rollback Capability
 * This file fixes rollback capability for existing batch records
 */

session_start();
require_once '../config/config.php';

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo 'Unauthorized access';
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        if (isset($_POST['fix_all'])) {
            // Fix all rollback-capable batch types
            $stmt = $pdo->prepare("
                UPDATE karyawan_batch_history
                SET is_rollback_capable = TRUE, rollback_status = 'AVAILABLE'
                WHERE action_type IN ('BATCH_INSERT', 'BATCH_UPDATE', 'BATCH_DELETE')
                AND (is_rollback_capable = FALSE OR rollback_status = 'NONE')
            ");
            $result = $stmt->execute();
            $updated_count = $stmt->rowCount();

            echo "Fixed {$updated_count} batch records (INSERT/UPDATE/DELETE)";

        } elseif (isset($_POST['batch_id'])) {
            // Fix specific batch
            $batch_id = intval($_POST['batch_id']);

            // Get batch info first
            $check_stmt = $pdo->prepare("SELECT action_type FROM karyawan_batch_history WHERE batch_id = ?");
            $check_stmt->execute([$batch_id]);
            $batch_info = $check_stmt->fetch(PDO::FETCH_ASSOC);

            if ($batch_info) {
                $stmt = $pdo->prepare("
                    UPDATE karyawan_batch_history
                    SET is_rollback_capable = TRUE, rollback_status = 'AVAILABLE'
                    WHERE batch_id = ? AND action_type IN ('BATCH_INSERT', 'BATCH_UPDATE', 'BATCH_DELETE')
                ");
                $result = $stmt->execute([$batch_id]);
                $updated_count = $stmt->rowCount();

                if ($updated_count > 0) {
                    echo "Fixed batch ID {$batch_id} ({$batch_info['action_type']})";
                } else {
                    echo "No changes made to batch ID {$batch_id} (action type: {$batch_info['action_type']}, may already be fixed)";
                }
            } else {
                echo "Batch ID {$batch_id} not found";
            }
        } else {
            echo "Invalid request";
        }
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage();
    }
} else {
    echo "Invalid request method";
}
?>
