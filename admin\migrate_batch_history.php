<?php
/**
 * Migrate Batch History Database
 * This file updates the database schema for rollback functionality
 */

session_start();
require_once '../config/config.php';

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo 'Unauthorized access';
    exit();
}

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Batch History Database Migration</h2>";
    
    // Check current schema
    echo "<h3>1. Checking Current Schema...</h3>";
    $stmt = $pdo->prepare("DESCRIBE karyawan_batch_history");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $existing_columns = array_column($columns, 'Field');
    echo "<p>Existing columns: " . implode(', ', $existing_columns) . "</p>";
    
    // Migration steps
    $migrations = [];
    
    // Step 1: Update action_type column to VARCHAR
    if (in_array('action_type', $existing_columns)) {
        $stmt = $pdo->prepare("SHOW COLUMNS FROM karyawan_batch_history LIKE 'action_type'");
        $stmt->execute();
        $action_type_info = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (strpos($action_type_info['Type'], 'enum') !== false) {
            $migrations[] = "ALTER TABLE karyawan_batch_history MODIFY action_type VARCHAR(50) NOT NULL COMMENT 'BATCH_INSERT, BATCH_UPDATE, BATCH_DELETE, BATCH_ROLLBACK, etc.'";
            echo "<p>✓ Need to update action_type from ENUM to VARCHAR</p>";
        } else {
            echo "<p>✓ action_type already VARCHAR</p>";
        }
    }
    
    // Step 2: Add rollback capability columns
    if (!in_array('is_rollback_capable', $existing_columns)) {
        $migrations[] = "ALTER TABLE karyawan_batch_history ADD COLUMN is_rollback_capable BOOLEAN DEFAULT FALSE COMMENT 'Whether this batch can be rolled back'";
        echo "<p>✓ Need to add is_rollback_capable column</p>";
    } else {
        echo "<p>✓ is_rollback_capable column already exists</p>";
    }
    
    if (!in_array('rollback_status', $existing_columns)) {
        $migrations[] = "ALTER TABLE karyawan_batch_history ADD COLUMN rollback_status ENUM('NONE', 'AVAILABLE', 'ROLLED_BACK', 'PARTIAL') DEFAULT 'NONE'";
        echo "<p>✓ Need to add rollback_status column</p>";
    } else {
        echo "<p>✓ rollback_status column already exists</p>";
    }
    
    // Execute migrations
    if (!empty($migrations)) {
        echo "<h3>2. Executing Migrations...</h3>";
        foreach ($migrations as $migration) {
            try {
                $pdo->exec($migration);
                echo "<p>✓ Executed: " . htmlspecialchars($migration) . "</p>";
            } catch (Exception $e) {
                echo "<p>✗ Failed: " . htmlspecialchars($migration) . " - " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<h3>2. No migrations needed - schema is up to date</h3>";
    }
    
    // Step 3: Update existing BATCH_INSERT records to be rollback capable
    echo "<h3>3. Updating Existing Records...</h3>";
    
    $stmt = $pdo->prepare("UPDATE karyawan_batch_history SET is_rollback_capable = TRUE, rollback_status = 'AVAILABLE' WHERE action_type = 'BATCH_INSERT' AND rollback_status = 'NONE'");
    $result = $stmt->execute();
    $updated_count = $stmt->rowCount();
    
    echo "<p>✓ Updated {$updated_count} BATCH_INSERT records to be rollback capable</p>";
    
    // Step 4: Show final schema
    echo "<h3>4. Final Schema:</h3>";
    $stmt = $pdo->prepare("DESCRIBE karyawan_batch_history");
    $stmt->execute();
    $final_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($final_columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 5: Test rollback capability
    echo "<h3>5. Testing Rollback Capability...</h3>";
    
    $stmt = $pdo->prepare("
        SELECT batch_id, action_type, batch_data, is_rollback_capable, rollback_status 
        FROM karyawan_batch_history 
        WHERE action_type = 'BATCH_INSERT' 
        ORDER BY change_timestamp DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $test_batches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($test_batches)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Batch ID</th><th>Action Type</th><th>Rollback Capable</th><th>Rollback Status</th><th>Can Rollback</th></tr>";
        
        require_once 'record_batch_employee_history.php';
        
        foreach ($test_batches as $batch) {
            $batch_data = json_decode($batch['batch_data'], true);
            $can_rollback = canRollback($batch_data, $batch['action_type']);
            
            echo "<tr>";
            echo "<td>" . $batch['batch_id'] . "</td>";
            echo "<td>" . $batch['action_type'] . "</td>";
            echo "<td>" . ($batch['is_rollback_capable'] ? 'Yes' : 'No') . "</td>";
            echo "<td>" . $batch['rollback_status'] . "</td>";
            echo "<td style='color: " . ($can_rollback ? 'green' : 'red') . ";'>" . ($can_rollback ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No BATCH_INSERT records found for testing</p>";
    }
    
    echo "<h3>6. Migration Complete!</h3>";
    echo "<p>✓ Database schema updated for rollback functionality</p>";
    echo "<p>✓ Existing BATCH_INSERT records marked as rollback capable</p>";
    echo "<p>✓ Ready to use rollback feature</p>";
    
    echo "<p><a href='batch_employee_history.php'>← Back to Batch History</a></p>";
    
} catch (Exception $e) {
    echo "<h3>Migration Error:</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
