<?php
session_start();
include '../config/config.php';
require_once '../includes/SystemManager.php';

// Initialize System Manager
$systemManager = new SystemManager($conn);

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi pesan
$success_message = '';
$error_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'delete_category':
                $category = trim($_POST['category']);

                // Cek apakah ada FAQ dalam kategori ini
                $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM faq WHERE category = ?");
                $check_stmt->bind_param("s", $category);
                $check_stmt->execute();
                $result = $check_stmt->get_result();
                $count = $result->fetch_assoc()['count'];

                if ($count > 0) {
                    // Jika ada FAQ dalam kategori ini, hapus semua FAQ dalam kategori tersebut
                    $delete_faqs = $conn->prepare("DELETE FROM faq WHERE category = ?");
                    $delete_faqs->bind_param("s", $category);

                    if ($delete_faqs->execute()) {
                        $success_message = 'Kategori dan semua FAQ terkait berhasil dihapus.';
                    } else {
                        $error_message = 'Gagal menghapus kategori dan FAQ terkait.';
                    }
                } else {
                    $success_message = 'Kategori berhasil dihapus.';
                }
                break;

            case 'create':
                $question = trim($_POST['question']);
                $answer = trim($_POST['answer']);
                $category = trim($_POST['category']);

                // Cek jika kategori baru dipilih
                if ($category === 'new_category' && isset($_POST['new_category'])) {
                    $category = trim($_POST['new_category']);
                }

                if (!empty($question) && !empty($answer) && !empty($category)) {
                    $stmt = $conn->prepare("INSERT INTO faq (question, answer, category) VALUES (?, ?, ?)");
                    $stmt->bind_param("sss", $question, $answer, $category);

                    if ($stmt->execute()) {
                        $success_message = 'FAQ berhasil ditambahkan.';
                    } else {
                        $error_message = 'Gagal menambahkan FAQ.';
                    }
                } else {
                    $error_message = 'Semua field harus diisi.';
                }
                break;

            case 'update':
                $id = $_POST['faq_id'];
                $question = trim($_POST['question']);
                $answer = trim($_POST['answer']);
                $category = trim($_POST['category']);

                // Cek jika kategori baru dipilih
                if ($category === 'new_category' && isset($_POST['new_category'])) {
                    $category = trim($_POST['new_category']);
                }

                if (!empty($question) && !empty($answer) && !empty($category)) {
                    $stmt = $conn->prepare("UPDATE faq SET question = ?, answer = ?, category = ? WHERE id = ?");
                    $stmt->bind_param("sssi", $question, $answer, $category, $id);

                    if ($stmt->execute()) {
                        $success_message = 'FAQ berhasil diperbarui.';
                    } else {
                        $error_message = 'Gagal memperbarui FAQ.';
                    }
                } else {
                    $error_message = 'Semua field harus diisi.';
                }
                break;

            case 'delete':
                $id = $_POST['faq_id'];
                $stmt = $conn->prepare("DELETE FROM faq WHERE id = ?");
                $stmt->bind_param("i", $id);

                if ($stmt->execute()) {
                    $success_message = 'FAQ berhasil dihapus.';
                } else {
                    $error_message = 'Gagal menghapus FAQ.';
                }
                break;

            case 'answer':
                $submission_id = intval($_POST['submission_id'] ?? 0);
                $answer = trim($_POST['answer'] ?? '');

                if ($submission_id > 0 && !empty($answer)) {
                    try {
                        $faqManager = $systemManager->getFAQManager();
                        if ($faqManager && $faqManager->answerSubmission($submission_id, $answer, $_SESSION['user_id'])) {
                            $success_message = 'FAQ submission berhasil dijawab.';
                        } else {
                            $error_message = 'Gagal menjawab FAQ submission.';
                        }
                    } catch (Exception $e) {
                        $error_message = 'Error: ' . $e->getMessage();
                    }
                } else {
                    $error_message = 'Data tidak valid untuk menjawab FAQ.';
                }
                break;

            case 'reject':
                $submission_id = intval($_POST['submission_id'] ?? 0);
                $reason = trim($_POST['reason'] ?? '');

                if ($submission_id > 0) {
                    try {
                        $faqManager = $systemManager->getFAQManager();
                        if ($faqManager && $faqManager->rejectSubmission($submission_id, $_SESSION['user_id'], $reason)) {
                            $success_message = 'FAQ submission berhasil ditolak.';
                        } else {
                            $error_message = 'Gagal menolak FAQ submission.';
                        }
                    } catch (Exception $e) {
                        $error_message = 'Error: ' . $e->getMessage();
                    }
                } else {
                    $error_message = 'Data tidak valid untuk menolak FAQ.';
                }
                break;

            case 'approve_and_add':
                $submission_id = intval($_POST['submission_id'] ?? 0);
                $answer = trim($_POST['answer'] ?? '');

                if ($submission_id > 0 && !empty($answer)) {
                    try {
                        $faqManager = $systemManager->getFAQManager();
                        if ($faqManager && $faqManager->approveAndAddToFAQ($submission_id, $answer, $_SESSION['user_id'])) {
                            $success_message = 'FAQ submission disetujui dan ditambahkan ke FAQ publik.';
                        } else {
                            $error_message = 'Gagal menyetujui dan menambahkan FAQ.';
                        }
                    } catch (Exception $e) {
                        $error_message = 'Error: ' . $e->getMessage();
                    }
                } else {
                    $error_message = 'Data tidak valid untuk menyetujui FAQ.';
                }
                break;

            case 'delete_submission':
                error_log("Delete submission action triggered");
                $submission_id = intval($_POST['submission_id'] ?? 0);
                error_log("Submission ID: " . $submission_id);

                if ($submission_id > 0) {
                    try {
                        error_log("Getting FAQManager...");
                        $faqManager = $systemManager->getFAQManager();

                        if ($faqManager) {
                            error_log("FAQManager obtained, attempting delete...");
                            $result = $faqManager->deleteSubmission($submission_id, $_SESSION['user_id']);
                            error_log("Delete result: " . ($result ? 'true' : 'false'));

                            if ($result) {
                                $success_message = 'FAQ submission berhasil dihapus.';
                                error_log("Delete successful");
                            } else {
                                $error_message = 'Gagal menghapus FAQ submission.';
                                error_log("Delete failed");
                            }
                        } else {
                            $error_message = 'FAQManager tidak tersedia.';
                            error_log("FAQManager not available");
                        }
                    } catch (Exception $e) {
                        $error_message = 'Error: ' . $e->getMessage();
                        error_log("Delete exception: " . $e->getMessage());
                    }
                } else {
                    $error_message = 'Data tidak valid untuk menghapus submission.';
                    error_log("Invalid submission ID");
                }
                break;

            case 'edit_submission':
                $submission_id = intval($_POST['submission_id'] ?? 0);
                $question = trim($_POST['question'] ?? '');
                $answer = trim($_POST['answer'] ?? '');
                $category = trim($_POST['category'] ?? '');
                $status = trim($_POST['status'] ?? '');

                if ($submission_id > 0 && !empty($question) && !empty($category) && !empty($status)) {
                    try {
                        $faqManager = $systemManager->getFAQManager();
                        if ($faqManager && $faqManager->updateSubmission($submission_id, $question, $answer, $category, $status, $_SESSION['user_id'])) {
                            $success_message = 'FAQ submission berhasil diperbarui.';
                        } else {
                            $error_message = 'Gagal memperbarui FAQ submission.';
                        }
                    } catch (Exception $e) {
                        $error_message = 'Error: ' . $e->getMessage();
                    }
                } else {
                    $error_message = 'Data tidak valid untuk memperbarui submission.';
                }
                break;

            case 'toggle_faq_visibility':
                $faq_id = intval($_POST['faq_id'] ?? 0);
                $is_hidden = intval($_POST['is_hidden'] ?? 0);

                if ($faq_id > 0) {
                    try {
                        $faqManager = $systemManager->getFAQManager();
                        if ($faqManager && $faqManager->toggleFAQVisibility($faq_id, $is_hidden, $_SESSION['user_id'])) {
                            $action = $is_hidden ? 'disembunyikan' : 'ditampilkan';
                            $success_message = "FAQ berhasil $action di halaman publik.";
                        } else {
                            $error_message = 'Gagal mengubah visibilitas FAQ.';
                        }
                    } catch (Exception $e) {
                        $error_message = 'Error: ' . $e->getMessage();
                    }
                } else {
                    $error_message = 'Data tidak valid untuk mengubah visibilitas.';
                }
                break;
        }
    }
}

// Pastikan kolom is_hidden ada di tabel faq
try {
    // Cek apakah kolom is_hidden sudah ada
    $check_column = "SHOW COLUMNS FROM faq LIKE 'is_hidden'";
    $column_result = $conn->query($check_column);

    if ($column_result->num_rows === 0) {
        // Tambahkan kolom is_hidden jika belum ada
        $add_column = "ALTER TABLE faq ADD COLUMN is_hidden TINYINT(1) DEFAULT 0";
        $conn->query($add_column);
    }
} catch (Exception $e) {
    error_log("Error adding is_hidden column: " . $e->getMessage());
}

// Ambil semua FAQ dengan visibility status
try {
    $query = "SELECT *, COALESCE(is_hidden, 0) as is_hidden FROM faq ORDER BY category, id";
    $result = $conn->query($query);
    $faqs = $result->fetch_all(MYSQLI_ASSOC);
} catch (Exception $e) {
    // Fallback jika kolom is_hidden masih bermasalah
    error_log("Error with is_hidden column: " . $e->getMessage());
    $query = "SELECT *, 0 as is_hidden FROM faq ORDER BY category, id";
    $result = $conn->query($query);
    $faqs = $result->fetch_all(MYSQLI_ASSOC);
}

// Ambil kategori unik
$categories = array_unique(array_column($faqs, 'category'));

// Initialize FAQ Manager dan ambil submissions
$submissions = [];
$submission_stats = [];
$faqManager = null;

try {
    // Debug: Check if SystemManager is properly initialized
    if (!isset($systemManager) || !is_object($systemManager)) {
        throw new Exception("SystemManager not properly initialized");
    }

    $faqManager = $systemManager->getFAQManager();

    // Debug: Check if FAQManager is properly returned
    if (!$faqManager || !is_object($faqManager)) {
        throw new Exception("FAQManager not properly returned from SystemManager");
    }

    $submissions = $faqManager->getSubmissions(null, 50);
    $submission_stats = $faqManager->getSubmissionStats();

} catch (Exception $e) {
    $error_message = 'Gagal memuat FAQ submissions: ' . $e->getMessage();
    error_log("FAQ Management Error: " . $e->getMessage());

    // Fallback: Try to get submissions directly from database
    try {
        $query = "SELECT fs.*, u.name as submitter_name, u.email as submitter_email,
                         answerer.name as answerer_name
                  FROM faq_submissions fs
                  LEFT JOIN users u ON fs.user_id = u.id
                  LEFT JOIN users answerer ON fs.answered_by = answerer.id
                  ORDER BY fs.created_at DESC LIMIT 50";

        $result = $conn->query($query);
        if ($result) {
            $submissions = $result->fetch_all(MYSQLI_ASSOC);
            $error_message = 'FAQ submissions loaded with fallback method.';
        }
    } catch (Exception $fallbackError) {
        error_log("FAQ Fallback Error: " . $fallbackError->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap 4 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
    <!-- Bootstrap 4 JS dan Popper -->
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.min.js"></script>
    <!-- Custom Modal JS -->
<style>
    .faq-manager {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    .faq-form {
        background: #f9f9f9;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
    }
    .faq-list {
        margin-top: 30px;
    }
    .faq-item {
        background: white;
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .faq-actions {
        margin-top: 10px;
    }
    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 4px;
    }
    .alert-success {
        background-color: #d4edda;
        color: #155724;
    }
    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
    }

    /* Tab Navigation Styles */
    .tab-navigation {
        display: flex;
        border-bottom: 2px solid #e9ecef;
        margin-bottom: 30px;
    }

    .tab-button {
        background: none;
        border: none;
        padding: 15px 25px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        color: #6c757d;
        border-bottom: 3px solid transparent;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .tab-button:hover {
        color: #BF0000;
        background-color: #f8f9fa;
    }

    .tab-button.active {
        color: #BF0000;
        border-bottom-color: #BF0000;
        background-color: #fff;
    }

    .badge {
        background-color: #dc3545;
        color: white;
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 10px;
        margin-left: 5px;
    }

    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    .submission-item {
        transition: box-shadow 0.3s;
    }

    .submission-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .submission-actions {
        margin-top: 15px;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    /* FAQ Item Enhancements */
    .faq-header-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .faq-header-row h4 {
        margin: 0;
        flex: 1;
        margin-right: 15px;
    }

    .faq-status {
        flex-shrink: 0;
    }

    .faq-hidden {
        opacity: 0.6;
        border-left: 4px solid #6c757d;
    }

    .badge {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 600;
    }

    .badge-secondary {
        background-color: #6c757d;
        color: white;
    }

    .badge-success {
        background-color: #28a745;
        color: white;
    }

    .badge-warning {
        background-color: #ffc107;
        color: #212529;
    }

    .badge-danger {
        background-color: #dc3545;
        color: white;
    }

    /* Enhanced Button Styles */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .btn-sm {
        padding: 4px 8px;
        font-size: 12px;
    }

    .btn-primary {
        background-color: #007bff;
        color: white;
    }

    .btn-success {
        background-color: #28a745;
        color: white;
    }

    .btn-warning {
        background-color: #ffc107;
        color: #212529;
    }

    .btn-danger {
        background-color: #dc3545;
        color: white;
    }

    .btn-info {
        background-color: #17a2b8;
        color: white;
    }

    /* Modal Enhancements */
    .modal-lg {
        max-width: 800px;
    }

    .form-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
    }

    .form-col {
        flex: 1;
    }

    .form-col-auto {
        flex: 0 0 auto;
    }
</style>
<body>
    <?php include '../config/navbarb.php'; ?>
<div class="jarak"></div>
    <div class="faq-manager">
        <h2 class="welcome-section">Kelola FAQ</h2>

        <?php if ($success_message): ?>
            <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <!-- Tab Navigation -->
        <div class="tab-navigation mb-4">
            <button class="tab-button active" onclick="showTab('faq-management')">
                <i class="fas fa-list"></i> Kelola FAQ
            </button>
            <button class="tab-button" onclick="showTab('faq-submissions')">
                <i class="fas fa-inbox"></i> FAQ Submissions
                <?php
                $pending_count = 0;
                foreach ($submission_stats as $stat) {
                    if ($stat['status'] === 'pending') $pending_count += $stat['count'];
                }
                if ($pending_count > 0):
                ?>
                    <span class="badge"><?= $pending_count ?></span>
                <?php endif; ?>
            </button>
        </div>

        <!-- FAQ Management Tab -->
        <div id="faq-management" class="tab-content active">

        <!-- Form Tambah FAQ -->
        <div class="faq-form">
            <h3>Tambah FAQ Baru</h3>
            <form method="POST" action="">
                <input type="hidden" name="action" value="create">
                <div class="form-group">
                    <label>Pertanyaan:</label>
                    <input type="text" name="question" class="form-control" required>
                </div>
                <div class="form-group">
                    <label>Jawaban:</label>
                    <textarea name="answer" class="form-control" rows="4" required></textarea>
                </div>
                <div class="form-group">
                    <label>Kategori:</label>
                    <select name="category" class="form-control" required>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo htmlspecialchars($category); ?>">
                                <?php echo htmlspecialchars($category); ?>
                            </option>
                        <?php endforeach; ?>
                        <option value="new_category">Kategori Baru</option>
                    </select>
                </div>
                <div class="form-group" id="new-category-input" style="display: none;">
                    <label>Kategori Baru:</label>
                    <input type="text" name="new_category" class="form-control">
                </div>
                <button type="submit" class="btn btn-primary">Tambah FAQ</button>
            </form>
        </div>

        <!-- Daftar Kategori -->
        <div class="category-list mb-4">
            <h3>Daftar Kategori</h3>
            <?php foreach ($categories as $category): ?>
                <div class="category-item d-flex justify-content-between align-items-center bg-light p-3 mb-2 rounded">
                    <span><?php echo htmlspecialchars($category); ?></span>
                    <form method="POST" action="" class="d-inline">
                        <input type="hidden" name="action" value="delete_category">
                        <input type="hidden" name="category" value="<?php echo htmlspecialchars($category); ?>">
                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirmaction('Apakah Anda yakin ingin menghapus kategori ini? Semua FAQ dalam kategori ini juga akan dihapus.')">Hapus Kategori</button>
                    </form>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Daftar FAQ -->
        <div class="faq-list">
            <h3>Daftar FAQ</h3>
            <?php foreach ($faqs as $faq): ?>
                <div class="faq-item <?= $faq['is_hidden'] ? 'faq-hidden' : '' ?>">
                    <div class="faq-header-row">
                        <h4><?php echo htmlspecialchars($faq['question']); ?></h4>
                        <div class="faq-status">
                            <?php if ($faq['is_hidden']): ?>
                                <span class="badge badge-secondary">
                                    <i class="fas fa-eye-slash"></i> Disembunyikan
                                </span>
                            <?php else: ?>
                                <span class="badge badge-success">
                                    <i class="fas fa-eye"></i> Ditampilkan
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <p><?php echo htmlspecialchars($faq['answer']); ?></p>
                    <small>Kategori: <?php echo htmlspecialchars($faq['category']); ?></small>
                    <div class="faq-actions">
                        <button class="btn btn-sm btn-primary" onclick="editFaq(<?php echo $faq['id']; ?>)">
                            <i class="fas fa-edit"></i> Edit
                        </button>

                        <?php if ($faq['is_hidden']): ?>
                            <button class="btn btn-sm btn-success" onclick="toggleFAQVisibility(<?= $faq['id'] ?>, 0)">
                                <i class="fas fa-eye"></i> Tampilkan
                            </button>
                        <?php else: ?>
                            <button class="btn btn-sm btn-warning" onclick="toggleFAQVisibility(<?= $faq['id'] ?>, 1)">
                                <i class="fas fa-eye-slash"></i> Sembunyikan
                            </button>
                        <?php endif; ?>

                        <button type="button" class="btn btn-sm btn-danger" onclick="deleteFAQ(<?php echo $faq['id']; ?>)">
                            <i class="fas fa-trash"></i> Hapus
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        </div> <!-- End FAQ Management Tab -->

        <!-- FAQ Submissions Tab -->
        <div id="faq-submissions" class="tab-content">
            <h3>FAQ Submissions dari User</h3>

            <?php if (empty($submissions)): ?>
                <div class="text-center p-4">
                    <i class="fas fa-inbox" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                    <p>Belum ada submission FAQ dari user.</p>
                </div>
            <?php else: ?>
                <?php foreach ($submissions as $submission): ?>
                    <div class="submission-item mb-3 p-3 border rounded">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <h5><?= htmlspecialchars($submission['submitter_name']) ?></h5>
                                <small class="text-muted">
                                    <?= htmlspecialchars($submission['submitter_email']) ?> •
                                    <?= htmlspecialchars($submission['category']) ?> •
                                    <?= date('d M Y H:i', strtotime($submission['created_at'])) ?>
                                </small>
                            </div>
                            <span class="badge badge-<?= $submission['status'] === 'pending' ? 'warning' : ($submission['status'] === 'answered' ? 'success' : 'danger') ?>">
                                <?= ucfirst($submission['status']) ?>
                            </span>
                        </div>

                        <div class="submission-question mb-2">
                            <strong>Pertanyaan:</strong><br>
                            <?= nl2br(htmlspecialchars($submission['question'])) ?>
                        </div>

                        <?php if ($submission['answer']): ?>
                            <div class="submission-answer mb-2 p-2 bg-light rounded">
                                <strong>Jawaban:</strong><br>
                                <?= nl2br(htmlspecialchars($submission['answer'])) ?>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        Dijawab oleh: <?= htmlspecialchars($submission['answerer_name']) ?> pada
                                        <?= date('d M Y H:i', strtotime($submission['answered_at'])) ?>
                                    </small>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="submission-actions">
                            <?php if ($submission['status'] === 'pending'): ?>
                                <button class="btn btn-sm btn-success" onclick="answerSubmission(<?= $submission['id'] ?>, 'answer')">
                                    <i class="fas fa-reply"></i> Jawab
                                </button>
                                <button class="btn btn-sm btn-primary" onclick="answerSubmission(<?= $submission['id'] ?>, 'approve')">
                                    <i class="fas fa-check"></i> Setujui & Tambah ke FAQ
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="rejectSubmission(<?= $submission['id'] ?>)">
                                    <i class="fas fa-times"></i> Tolak
                                </button>
                            <?php endif; ?>

                            <!-- Universal Actions -->
                            <button class="btn btn-sm btn-info" onclick="editSubmission(<?= $submission['id'] ?>)">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="deleteSubmission(<?= $submission['id'] ?>)">
                                <i class="fas fa-trash"></i> Hapus
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div> <!-- End FAQ Submissions Tab -->
    </div>

    <!-- Modal Edit FAQ -->
    <div class="modal fade" id="editFaqModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit FAQ</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="editFaqForm" method="POST" action="">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="faq_id" id="edit_faq_id">
                        <div class="form-group">
                            <label>Pertanyaan:</label>
                            <input type="text" name="question" id="edit_question" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>Jawaban:</label>
                            <textarea name="answer" id="edit_answer" class="form-control" rows="4" required></textarea>
                        </div>
                        <div class="form-group">
                            <label>Kategori:</label>
                            <select name="category" id="edit_category" class="form-control" required>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo htmlspecialchars($category); ?>">
                                        <?php echo htmlspecialchars($category); ?>
                                    </option>
                                <?php endforeach; ?>
                                <option value="new_category">Kategori Baru</option>
                            </select>
                        </div>
                        <div class="form-group" id="edit-new-category-input" style="display: none;">
                            <label>Kategori Baru:</label>
                            <input type="text" name="new_category" class="form-control">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="submit" form="editFaqForm" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Edit Submission -->
    <div class="modal fade" id="editSubmissionModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit FAQ Submission</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="editSubmissionForm" method="POST" action="">
                        <input type="hidden" name="action" value="edit_submission">
                        <input type="hidden" name="submission_id" id="edit_submission_id">

                        <div class="form-row">
                            <div class="form-col">
                                <label>Kategori:</label>
                                <select name="category" id="edit_submission_category" class="form-control" required>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category); ?>">
                                            <?php echo htmlspecialchars($category); ?>
                                        </option>
                                    <?php endforeach; ?>
                                    <option value="new_category">Kategori Baru</option>
                                </select>
                            </div>
                            <div class="form-col">
                                <label>Status:</label>
                                <select name="status" id="edit_submission_status" class="form-control" required>
                                    <option value="pending">Pending</option>
                                    <option value="answered">Answered</option>
                                    <option value="rejected">Rejected</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Pertanyaan:</label>
                            <textarea name="question" id="edit_submission_question" class="form-control" rows="3" required></textarea>
                        </div>

                        <div class="form-group">
                            <label>Jawaban:</label>
                            <textarea name="answer" id="edit_submission_answer" class="form-control" rows="4"></textarea>
                        </div>

                        <div class="form-group" id="edit-submission-new-category-input" style="display: none;">
                            <label>Kategori Baru:</label>
                            <input type="text" name="new_category" class="form-control">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="submit" form="editSubmissionForm" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </div>
        </div>
    </div>

    <?php include '../config/footer.php'; ?>

    <script>
    // Tab Navigation Functions
    function showTab(tabId) {
        // Hide all tab contents
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.remove('active');
        });

        // Remove active class from all tab buttons
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(button => {
            button.classList.remove('active');
        });

        // Show selected tab content
        document.getElementById(tabId).classList.add('active');

        // Add active class to clicked button
        event.target.classList.add('active');
    }

    // FAQ Submission Management Functions
    function answerSubmission(submissionId, action) {
        const title = action === 'approve' ? 'Setujui & Tambah ke FAQ' : 'Jawab Submission';
        const confirmMessage = action === 'approve' ?
            'Apakah Anda yakin ingin menyetujui submission ini dan menambahkannya ke FAQ publik?' :
            'Apakah Anda yakin ingin menjawab submission ini?';

        confirmaction(confirmMessage, function() {
            const answer = prompt(`${title}\n\nMasukkan jawaban:`);

            if (answer && answer.trim()) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = action === 'approve' ? 'approve_and_add' : 'answer';

                const submissionInput = document.createElement('input');
                submissionInput.type = 'hidden';
                submissionInput.name = 'submission_id';
                submissionInput.value = submissionId;

                const answerInput = document.createElement('input');
                answerInput.type = 'hidden';
                answerInput.name = 'answer';
                answerInput.value = answer.trim();

                form.appendChild(actionInput);
                form.appendChild(submissionInput);
                form.appendChild(answerInput);

                document.body.appendChild(form);
                form.submit();
            }
        });
    }

    function rejectSubmission(submissionId) {
        const confirmMessage = 'Apakah Anda yakin ingin menolak submission ini?\n\n' +
                              'Submission akan ditandai sebagai ditolak.\n\n' +
                              'Klik OK untuk melanjutkan atau Cancel untuk membatalkan.';

        confirmaction(confirmMessage, function() {
            const reason = prompt('Alasan penolakan (opsional):');

            if (reason !== null) { // User didn't cancel
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'reject';

                const submissionInput = document.createElement('input');
                submissionInput.type = 'hidden';
                submissionInput.name = 'submission_id';
                submissionInput.value = submissionId;

                const reasonInput = document.createElement('input');
                reasonInput.type = 'hidden';
                reasonInput.name = 'reason';
                reasonInput.value = reason || '';

                form.appendChild(actionInput);
                form.appendChild(submissionInput);
                form.appendChild(reasonInput);

                document.body.appendChild(form);
                form.submit();
            }
        });
    }

    // Edit Submission Function
    function editSubmission(submissionId) {
        // Get submission data via AJAX or from existing data
        fetch(`get_submission.php?id=${submissionId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const submission = data.submission;

                    document.getElementById('edit_submission_id').value = submission.id;
                    document.getElementById('edit_submission_question').value = submission.question;
                    document.getElementById('edit_submission_answer').value = submission.answer || '';
                    document.getElementById('edit_submission_category').value = submission.category;
                    document.getElementById('edit_submission_status').value = submission.status;

                    $('#editSubmissionModal').modal('show');
                } else {
                    alert('Gagal memuat data submission: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Terjadi kesalahan saat memuat data submission');
            });
    }

    // Delete Submission Function
    function deleteSubmission(submissionId) {
        console.log('deleteSubmission called with ID:', submissionId);

        // Try simple confirm first
        if (typeof window.originalConfirm === 'function') {
            var confirmed = window.originalConfirm('Apakah Anda yakin ingin menghapus submission ini?\n\nTindakan ini tidak dapat dibatalkan.');
        } else {
            var confirmed = confirm('Apakah Anda yakin ingin menghapus submission ini?\n\nTindakan ini tidak dapat dibatalkan.');
        }

        if (confirmed) {
            console.log('User confirmed deletion, creating form...');

            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'delete_submission';

            const submissionInput = document.createElement('input');
            submissionInput.type = 'hidden';
            submissionInput.name = 'submission_id';
            submissionInput.value = submissionId;

            form.appendChild(actionInput);
            form.appendChild(submissionInput);

            document.body.appendChild(form);

            console.log('Form created, submitting...', {
                action: actionInput.value,
                submission_id: submissionInput.value
            });

            form.submit();
        } else {
            console.log('User cancelled deletion');
        }
    }

    // Alternative delete function using custom modal
    function deleteSubmissionWithModal(submissionId) {
        console.log('deleteSubmissionWithModal called with ID:', submissionId);

        const message = 'Apakah Anda yakin ingin menghapus submission ini?\n\n' +
                       'Tindakan ini akan:\n' +
                       '• Menghapus submission secara permanen\n' +
                       '• Tidak dapat dibatalkan\n\n' +
                       'Klik OK untuk melanjutkan atau Cancel untuk membatalkan.';

        console.log('Showing confirmation dialog...');

        confirmDangerousAction(message, function() {
            console.log('User confirmed deletion, creating form...');

            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'delete_submission';

            const submissionInput = document.createElement('input');
            submissionInput.type = 'hidden';
            submissionInput.name = 'submission_id';
            submissionInput.value = submissionId;

            form.appendChild(actionInput);
            form.appendChild(submissionInput);

            document.body.appendChild(form);

            console.log('Form created, submitting...', {
                action: actionInput.value,
                submission_id: submissionInput.value
            });

            form.submit();
        });
    }

    // Toggle FAQ Visibility Function
    function toggleFAQVisibility(faqId, isHidden) {
        const action = isHidden ? 'menyembunyikan' : 'menampilkan';
        const message = `Apakah Anda yakin ingin ${action} FAQ ini di halaman publik?\n\n` +
                       `FAQ akan ${isHidden ? 'disembunyikan' : 'ditampilkan'} untuk semua pengunjung.\n\n` +
                       'Klik OK untuk melanjutkan atau Cancel untuk membatalkan.';

        confirmaction(message, function() {
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'toggle_faq_visibility';

            const faqInput = document.createElement('input');
            faqInput.type = 'hidden';
            faqInput.name = 'faq_id';
            faqInput.value = faqId;

            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'is_hidden';
            hiddenInput.value = isHidden;

            form.appendChild(actionInput);
            form.appendChild(faqInput);
            form.appendChild(hiddenInput);

            document.body.appendChild(form);
            form.submit();
        });
    }

    // Global confirmation function - Compatible with custom-modal.js
    function confirmaction(message, callback) {
        if (typeof callback === 'function') {
            // Use custom modal with callback
            if (typeof window.confirmAction === 'function') {
                window.confirmAction(message, callback);
            } else if (typeof window.CustomModal !== 'undefined') {
                CustomModal.confirm(message, 'Konfirmasi', {
                    onConfirm: callback
                });
            } else {
                // Fallback to native confirm
                if (confirm(message)) {
                    callback();
                }
            }
        } else {
            // For backward compatibility, try native confirm first
            if (typeof window.originalConfirm === 'function') {
                return window.originalConfirm(message);
            } else {
                return confirm(message);
            }
        }
    }

    // Enhanced confirmation for dangerous actions
    function confirmDangerousAction(message, callback) {
        const enhancedMessage = (message || 'Tindakan ini tidak dapat dibatalkan!') +
                               '\n\nKlik OK untuk melanjutkan atau Cancel untuk membatalkan.';

        if (typeof callback === 'function') {
            confirmaction(enhancedMessage, callback);
        } else {
            // Return a promise-like behavior
            return new Promise((resolve) => {
                confirmaction(enhancedMessage, () => resolve(true));
            });
        }
    }

    // Alias functions for backward compatibility
    function confirmAction(message, callback) {
        return confirmaction(message, callback);
    }

    function confirmdelete(message, callback) {
        return confirmDangerousAction(message, callback);
    }



    // Delete FAQ Function
    function deleteFAQ(faqId) {
        const message = 'Apakah Anda yakin ingin menghapus FAQ ini?\n\n' +
                       'Tindakan ini akan:\n' +
                       '• Menghapus FAQ secara permanen\n' +
                       '• Tidak dapat dibatalkan\n\n' +
                       'Klik OK untuk melanjutkan atau Cancel untuk membatalkan.';

        confirmDangerousAction(message, function() {
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'delete';

            const faqInput = document.createElement('input');
            faqInput.type = 'hidden';
            faqInput.name = 'faq_id';
            faqInput.value = faqId;

            form.appendChild(actionInput);
            form.appendChild(faqInput);

            document.body.appendChild(form);
            form.submit();
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Handle kategori baru di form tambah
        const categorySelect = document.querySelector('select[name="category"]');
        const newCategoryInput = document.getElementById('new-category-input');

        categorySelect.addEventListener('change', function() {
            if (this.value === 'new_category') {
                newCategoryInput.style.display = 'block';
            } else {
                newCategoryInput.style.display = 'none';
            }
        });

        // Handle kategori baru di form edit
        const editCategorySelect = document.getElementById('edit_category');
        const editNewCategoryInput = document.getElementById('edit-new-category-input');

        editCategorySelect.addEventListener('change', function() {
            if (this.value === 'new_category') {
                editNewCategoryInput.style.display = 'block';
            } else {
                editNewCategoryInput.style.display = 'none';
            }
        });

        // Handle kategori baru di edit submission modal
        const editSubmissionCategorySelect = document.getElementById('edit_submission_category');
        const editSubmissionNewCategoryInput = document.getElementById('edit-submission-new-category-input');

        if (editSubmissionCategorySelect) {
            editSubmissionCategorySelect.addEventListener('change', function() {
                if (this.value === 'new_category') {
                    editSubmissionNewCategoryInput.style.display = 'block';
                } else {
                    editSubmissionNewCategoryInput.style.display = 'none';
                }
            });
        }
    });

    function editFaq(faqId) {
        // Cari FAQ yang sesuai dari data yang ada
        const faqs = <?php echo json_encode($faqs); ?>;
        console.log('Data FAQ:', faqs);
        console.log('ID yang dicari:', faqId);

        // Konversi faqId ke number karena data dari PHP mungkin dalam bentuk string
        const faqIdNumber = parseInt(faqId);
        const faq = faqs.find(f => parseInt(f.id) === faqIdNumber);
        console.log('FAQ yang ditemukan:', faq);

        if (faq) {
            document.getElementById('edit_faq_id').value = faq.id;
            document.getElementById('edit_question').value = faq.question;
            document.getElementById('edit_answer').value = faq.answer;
            document.getElementById('edit_category').value = faq.category;

            // Pastikan jQuery dan Bootstrap tersedia
            if (typeof $ === 'undefined') {
                console.error('jQuery tidak tersedia');
                return;
            }

            if (typeof $.fn.modal === 'undefined') {
                console.error('Bootstrap modal tidak tersedia');
                return;
            }

            // Tampilkan modal dengan menambahkan penanganan error
            try {
                $('#editFaqModal').modal('show');
            } catch (error) {
                console.error('Error saat menampilkan modal:', error);
            }
        } else {
            console.error('FAQ dengan ID', faqId, 'tidak ditemukan');
        }
    }
    </script>
</body>
</html>