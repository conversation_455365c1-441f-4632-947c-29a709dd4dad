<?php
/**
 * Helper file untuk fungsi-fungsi keamanan
 * File ini harus di-include setelah config.php
 */

// Fungsi untuk mendapatkan pengaturan keamanan
if (!function_exists('get_security_settings')) {
    function get_security_settings() {
        global $conn;
        static $security_settings = null;
        
        // Jika settings sudah di-cache, langsung kembalikan
        if ($security_settings !== null) {
            return $security_settings;
        }
        
        // Jika koneksi database tersedia, ambil dari database
        if (isset($conn) && $conn instanceof mysqli && !$conn->connect_error) {
            try {
                $query = "SELECT encrypt_password, password_reset_days, login_attempts, block_time, password_expiry, 
                          require_uppercase, require_number, require_special, min_password_length 
                          FROM settings WHERE id = 1 LIMIT 1"; // Pastikan hanya mengambil satu baris data.
                $result = $conn->query($query);
                
                if ($result && $result->num_rows > 0) {
                    $security_settings = $result->fetch_assoc();
                    return $security_settings;
                }
            } catch (Exception $e) {
                // Jika terjadi error, gunakan default
            }
        }
        
        // Default settings jika tidak bisa mengambil dari database
        return [
            'encrypt_password' => 1, // Default encrypt_password = 1 (true) untuk mengaktifkan enkripsi password.
            'password_reset_days' => 90, // Default password_reset_days = 90 hari untuk mengaktifkan reset password.
            'login_attempts' => 3,
            'block_time' => 15,
            'password_expiry' => 30,
            'require_uppercase' => 1,
            'require_number' => 1,
            'require_special' => 1,
            'min_password_length' => 8
        ];
    }
}

// Fungsi untuk validasi password berdasarkan pengaturan keamanan
if (!function_exists('validate_password_strength')) {
    function validate_password_strength($password) {
        $settings = get_security_settings();
        $errors = [];
        
        // Cek panjang minimum
        if (strlen($password) < $settings['min_password_length']) {
            $errors[] = "Password harus minimal {$settings['min_password_length']} karakter";
        }
        
        // Cek huruf besar
        if ($settings['require_uppercase'] && !preg_match('/[A-Z]/', $password)) {
            $errors[] = "Password harus mengandung minimal satu huruf besar";
        }
        
        // Cek angka
        if ($settings['require_number'] && !preg_match('/[0-9]/', $password)) {
            $errors[] = "Password harus mengandung minimal satu angka";
        }
        
        // Cek karakter khusus
        if ($settings['require_special'] && !preg_match('/[^a-zA-Z0-9]/', $password)) {
            $errors[] = "Password harus mengandung minimal satu karakter khusus";
        }
        
        return $errors;
    }
}

// Fungsi untuk memeriksa apakah akun diblokir
if (!function_exists('is_account_locked')) {
    function is_account_locked($user_id) {
        global $conn;
        
        if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
            return false; // Jika tidak bisa mengakses database, anggap tidak terkunci
        }
        
        try {
            $query = "SELECT failed_attempts, last_failed_attempt FROM users WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result && $result->num_rows > 0) {
                $user = $result->fetch_assoc();
                $settings = get_security_settings();
                
                // Jika jumlah percobaan gagal melebihi batas
                if ($user['failed_attempts'] >= $settings['login_attempts']) {
                    // Cek apakah masih dalam periode blokir
                    $block_time_seconds = $settings['block_time'] * 60; // Konversi menit ke detik
                    $last_failed = strtotime($user['last_failed_attempt']);
                    $current_time = time();
                    
                    // Jika masih dalam periode blokir
                    if (($current_time - $last_failed) < $block_time_seconds) {
                        $remaining_time = $block_time_seconds - ($current_time - $last_failed);
                        $remaining_minutes = ceil($remaining_time / 60);
                        return [
                            'locked' => true,
                            'remaining_minutes' => $remaining_minutes,
                            'message' => "Akun Anda terkunci karena terlalu banyak percobaan gagal. Silakan coba lagi dalam {$remaining_minutes} menit."
                        ];
                    } else {
                        // Jika sudah melewati periode blokir, reset counter
                        $reset_query = "UPDATE users SET failed_attempts = 0 WHERE id = ?";
                        $reset_stmt = $conn->prepare($reset_query);
                        $reset_stmt->bind_param("i", $user_id);
                        $reset_stmt->execute();
                    }
                }
            }
            
            return ['locked' => false];
        } catch (Exception $e) {
            return ['locked' => false]; // Jika terjadi error, anggap tidak terkunci
        }
    }
}

// Fungsi untuk mencatat percobaan login gagal
if (!function_exists('record_failed_login')) {
    function record_failed_login($user_id) {
        global $conn;
        
        if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
            return false;
        }
        
        try {
            // Update jumlah percobaan gagal dan waktu terakhir gagal
            $query = "UPDATE users SET 
                      failed_attempts = failed_attempts + 1,
                      last_failed_attempt = NOW()
                      WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $user_id);
            return $stmt->execute();
        } catch (Exception $e) {
            return false;
        }
    }
}

// Fungsi untuk reset counter percobaan login gagal
if (!function_exists('reset_failed_login')) {
    function reset_failed_login($user_id) {
        global $conn;
        
        if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
            return false;
        }
        
        try {
            $query = "UPDATE users SET failed_attempts = 0 WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $user_id);
            return $stmt->execute();
        } catch (Exception $e) {
            return false;
        }
    }
}

// Fungsi untuk memeriksa apakah password perlu diperbarui
if (!function_exists('is_password_expired')) {
    function is_password_expired($user_id) {
        global $conn;
        
        if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
            return false;
        }
        
        try {
            $query = "SELECT password_changed_at FROM users WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result && $result->num_rows > 0) {
                $user = $result->fetch_assoc();
                $settings = get_security_settings();
                
                // Jika password_changed_at adalah NULL, anggap password belum pernah diubah
                if ($user['password_changed_at'] === null) {
                    return true;
                }
                
                // Hitung selisih hari
                $password_changed = strtotime($user['password_changed_at']);
                $current_time = time();
                $days_diff = floor(($current_time - $password_changed) / (60 * 60 * 24));
                
                // Jika selisih hari lebih dari masa berlaku password
                if ($days_diff > $settings['password_expiry']) {
                    return true;
                }
            }
            
            return false;
        } catch (Exception $e) {
            return false;
        }
    }
}
