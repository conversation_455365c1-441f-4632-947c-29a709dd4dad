/* Custom Notifications System Styles */

/* Container for all notifications */
.custom-notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Base notification style */
.custom-notification {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 10px;
    position: relative;
    overflow: hidden;
}

/* Notification types */
.custom-notification-success {
    background-color: #e7f7ee;
    border-left: 4px solid #28a745;
    color: #155724;
}

.custom-notification-error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.custom-notification-warning {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    color: #856404;
}

.custom-notification-info {
    background-color: #d1ecf1;
    border-left: 4px solid #17a2b8;
    color: #0c5460;
}

/* Notification parts */
.notification-icon {
    margin-right: 15px;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-content {
    flex: 1;
    padding-right: 30px;
    font-size: 14px;
    line-height: 1.5;
}

.notification-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    color: inherit;
    opacity: 0.7;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s;
}

.notification-close:hover {
    opacity: 1;
}

/* Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification-slide-in {
    animation: slideInRight 0.3s forwards;
}

.notification-slide-out {
    animation: slideOutRight 0.3s forwards;
}

/* Custom Dialog Styles */
.custom-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.custom-dialog {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 400px;
    padding: 0;
    opacity: 0;
    transform: scale(0.9);
    transition: opacity 0.3s, transform 0.3s;
}

.custom-dialog.dialog-show {
    opacity: 1;
    transform: scale(1);
}

.custom-dialog.dialog-hide {
    opacity: 0;
    transform: scale(0.9);
}

.custom-dialog-content {
    padding: 20px;
}

.custom-dialog-message {
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.5;
    color: #333;
}

.custom-dialog-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.custom-dialog-button {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.custom-dialog-cancel {
    background-color: #f1f1f1;
    border: 1px solid #ddd;
    color: #333;
}

.custom-dialog-cancel:hover {
    background-color: #e5e5e5;
}

.custom-dialog-confirm {
    background-color: #BF0000;
    border: 1px solid #BF0000;
    color: white;
}

.custom-dialog-confirm:hover {
    background-color: #a00000;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .custom-notifications-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .custom-notification {
        padding: 12px;
    }
    
    .notification-icon {
        font-size: 18px;
    }
    
    .notification-content {
        font-size: 13px;
    }
}
