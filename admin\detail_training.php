
<?php
include '../config/config.php';
include 'security.php';  // Ensure security.php is included

$id = $_GET['id'];

$query = "SELECT ts.id,ts.assignment, ts.full_name, ts.status, ts.training_topic, ts.nik, ts.departemen, ts.bagian, ts.jabatan, ts.internal_memo_image,
                 ts.training_type, ts.training_skill_type, ts.start_date, ts.end_date, ts.is_confirmed, ts.training_place, ts.training_cost,
                 ts.contact_person, ts.contact_number, ts.sharing_knowledge, ts.additional_info, ts.approved_hrd,
                 ts.email, ts.phone, ts.comments_dept_head, ts.comments_hrd, ts.comments_ga, ts.comments_fm, ts.comments_dir,
                 ts.training_time_start, ts.training_time_end, ts.provider_name, ts.provider_address, ts.trainer_name_internal, ts.trainer_nik_internal, ts.trainer_department_internal, ts.trainer_sub_department_internal, ts.trainer_position_internal, ts.trainer_name_external, ts.additional_info_provider,
                 ts.provider_type,
                 ts.approved_fm, ts.approved_dir,
                 r.role_name AS current_approver,
                 GROUP_CONCAT(p.nama_participants SEPARATOR ', ') AS participant_names,
                 GROUP_CONCAT(p.nik_participants SEPARATOR ', ') AS participant_niks,
                 GROUP_CONCAT(p.jabatan_participants SEPARATOR ', ') AS participant_jabatans,
                 GROUP_CONCAT(p.bagian_participants SEPARATOR ', ') AS participant_bagians,
                 GROUP_CONCAT(p.departemen_participants SEPARATOR ', ') AS participant_departemens
          FROM training_submissions ts
          LEFT JOIN roles r ON ts.current_approver_role_id = r.id
          LEFT JOIN participants p ON ts.id = p.training_id
          WHERE ts.id = ?
          GROUP BY ts.id";
$stmt = $conn->prepare($query);
if ($stmt === false) {
    die("Error preparing query.");
}

$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();

// Check if submission data is found
if (!$row) {
    die("Training submission not found.");
}
// Process form submission if any
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $status = $_POST['status'];
    $comments = $_POST['comments'];

    // Validate status
    if (!in_array($status, ['Approved', 'Rejected'])) {
        die("Invalid status.");
    }

    // Update status and comments
    if ($status == 'Approved') {
        $updateQuery = "UPDATE training_submissions
                        SET status = 'Approved',
                            comments_dir = ?,
                            approved_dir = 'Approved',
                            approved_by = ?,
                            approved_at = CURRENT_TIMESTAMP
                        WHERE id = ?";

        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param("sii", $comments, $_SESSION['user_id'], $id);
    } else {
        $updateQuery = "UPDATE training_submissions
                        SET status = 'Rejected',
                            comments_dir = ?,
                            approved_dir = 'Rejected',
                            rejected_at = CURRENT_TIMESTAMP,
                            rejected_by = ?
                        WHERE id = ?";

        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param("ssii", $comments, $_SESSION['user_id'], $id);
    }

    if ($updateStmt->execute()) {
        if ($status == 'Approved') {
            $_SESSION['success_message'] = "Training telah disetujui dan siap dilaksanakan.";
        } else {
            $_SESSION['error_message'] = "Training telah ditolak.";
        }

        header("Location: index.php");
        exit;
    } else {
        $_SESSION['error_message'] = "Terjadi kesalahan dalam memproses persetujuan.";
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<head>
    <script>
        // Fungsi untuk menampilkan toast notification
        function showToast(message, type = 'success', duration = 3000) {
            // Hapus toast yang sudah ada jika ada
            const existingToast = document.querySelector('.toast-notification');
            if (existingToast) {
                document.body.removeChild(existingToast);
            }

            // Buat toast baru
            const toast = document.createElement('div');
            toast.className = `toast-notification toast-${type}`;

            // Set icon berdasarkan type
            let icon = 'check';
            if (type === 'error') icon = 'exclamation-circle';
            if (type === 'warning') icon = 'exclamation-triangle';
            if (type === 'info') icon = 'info-circle';

            toast.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;
            document.body.appendChild(toast);

            // Animasi toast
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);

            // Hapus toast setelah durasi tertentu
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, duration);
        }

        // Alias untuk kompatibilitas dengan kode lama
        function showAlert(message) {
            showToast(message, 'success');
        }

        // Tambahkan CSS untuk toast notification
        document.head.insertAdjacentHTML('beforeend', `
        <style>
        .toast-notification {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(100px);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            opacity: 0;
            transition: transform 0.3s ease, opacity 0.3s ease;
        }

        .toast-notification.show {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }

        .toast-success i {
            color: #4CAF50;
        }

        .toast-error i {
            color: #F44336;
        }

        .toast-warning i {
            color: #FF9800;
        }

        .toast-info i {
            color: #2196F3;
        }
        </style>
        `);
    </script>
</head>
<style>
    .alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}
</style>
<body>
<?php include '../config/navbar.php'; ?>

<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success">
        <?= htmlspecialchars($_SESSION['success_message']) ?>
    </div>
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger">
        <?= htmlspecialchars($_SESSION['error_message']) ?>
    </div>
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

<div class="container-form">
    <div class="form-container">
        <div class="latest-submissions">
            <h1>Detail Pengajuan Training</h1>
            <?php include '../config/tablea.php'; ?>
            </form>
            <button><a style="color: white; text-decoration: none;" href="training_management.php">Kembali ke Daftar Pengajuan</a></button>
        </div>
    </div>
</div>
<?php include '../config/footer.php'; ?>
</body>
</html>

<?php $conn->close(); ?>
