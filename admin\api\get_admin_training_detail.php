<?php
/**
 * API untuk mengambil detail training dan daftar peserta untuk admin
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Set timezone for consistent date handling
date_default_timezone_set('Asia/Jakarta');

// Include necessary files
require_once '../../config/config.php';
require_once '../security.php';

// Check if user is admin (same validation as security.php)
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role_id'], [2, 3, 4, 5, 99])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized - Admin access required']);
    exit;
}

try {
    // Get parameters
    $training_id = $_GET['id'] ?? '';
    $training_type = $_GET['type'] ?? '';

    // Debug logging
    error_log("get_admin_training_detail.php called with id=$training_id, type=$training_type");

    if (empty($training_id) || empty($training_type)) {
        throw new Exception('Missing required parameters: id=' . $training_id . ', type=' . $training_type);
    }

    if (!is_numeric($training_id)) {
        throw new Exception('Invalid training ID: must be numeric');
    }

    // Convert to integer
    $training_id = (int)$training_id;

    $training_detail = null;
    $participants = [];

    switch ($training_type) {
        case 'offline':
            // Get offline training detail
            error_log("Fetching offline training with ID: $training_id");

            // Simplified query for better performance
            $query = "SELECT
                        id,
                        training_topic as title,
                        training_description as description,
                        start_date as date,
                        end_date,
                        training_time_start as time_start,
                        training_time_end as time_end,
                        location,
                        trainer_name as trainer,
                        max_participants,
                        status,
                        is_hidden
                      FROM offline_training
                      WHERE id = ?";

            $stmt = $conn->prepare($query);
            if (!$stmt) {
                throw new Exception('Database prepare error for offline training: ' . $conn->error);
            }

            $stmt->bind_param("i", $training_id);
            if (!$stmt->execute()) {
                throw new Exception('Database execute error for offline training: ' . $stmt->error);
            }

            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $training_detail = $result->fetch_assoc();

                // Process and format data safely
                $training_detail['type'] = 'offline';
                $training_detail['is_confirmed'] = $training_detail['is_confirmed'] ?? 1;
                $training_detail['is_hidden'] = $training_detail['is_hidden'] ?? 0;
                $training_detail['status'] = $training_detail['status'] ?? 'Active';
                $training_detail['raw_date'] = $training_detail['date'];

                // Format time display
                $time_parts = [];
                if (!empty($training_detail['time_start'])) {
                    $time_parts[] = $training_detail['time_start'];
                }
                if (!empty($training_detail['time_end'])) {
                    $time_parts[] = $training_detail['time_end'];
                }
                $training_detail['time'] = implode(' - ', $time_parts);

                // Get participants from training_attendance table (simplified)
                $participants_query = "SELECT
                                        nama as name,
                                        nik,
                                        status as attendance_status
                                      FROM training_attendance
                                      WHERE training_id = ?
                                      ORDER BY nama
                                      LIMIT 50";

                $stmt2 = $conn->prepare($participants_query);
                if (!$stmt2) {
                    error_log("Participants query prepare error for offline training: " . $conn->error);
                } else {
                    $stmt2->bind_param("i", $training_id);
                    if (!$stmt2->execute()) {
                        error_log("Participants query execute error for offline training: " . $stmt2->error);
                    } else {
                        $participants_result = $stmt2->get_result();

                        while ($participant = $participants_result->fetch_assoc()) {
                            $participants[] = [
                                'name' => $participant['name'] ?? 'N/A',
                                'nik' => $participant['nik'] ?? 'N/A',
                                'dept' => 'N/A', // Simplified - no JOIN for better performance
                                'jabatan' => 'N/A', // Simplified - no JOIN for better performance
                                'status' => $participant['attendance_status'] ?: 'tidak hadir'
                            ];
                        }
                        error_log("Offline training participants found: " . $participants_result->num_rows);
                    }
                    $stmt2->close();
                }
            }
            $stmt->close();
            break;

        case 'online':
            // Get online training (training submission) detail
            error_log("Fetching online training with ID: $training_id");

            // Simplified query for online training
            $query = "SELECT
                        id,
                        training_topic as title,
                        additional_info as description,
                        start_date as date,
                        end_date,
                        training_time_start as time_start,
                        training_time_end as time_end,
                        training_place as location,
                        contact_person as trainer,
                        status,
                        is_hidden,
                        user_id
                      FROM training_submissions
                      WHERE id = ?";

            $stmt = $conn->prepare($query);
            if (!$stmt) {
                throw new Exception('Database prepare error for online training: ' . $conn->error);
            }

            $stmt->bind_param("i", $training_id);
            if (!$stmt->execute()) {
                throw new Exception('Database execute error for online training: ' . $stmt->error);
            }

            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $training_detail = $result->fetch_assoc();

                // Process and format data safely
                $training_detail['type'] = 'online';
                $training_detail['is_confirmed'] = $training_detail['is_confirmed'] ?? 0;
                $training_detail['is_hidden'] = $training_detail['is_hidden'] ?? 0;
                $training_detail['location'] = $training_detail['location'] ?? 'Online';
                $training_detail['max_participants'] = null;
                $training_detail['raw_date'] = $training_detail['date'];

                // Format time display
                $time_parts = [];
                if (!empty($training_detail['time_start'])) {
                    $time_parts[] = $training_detail['time_start'];
                }
                if (!empty($training_detail['time_end'])) {
                    $time_parts[] = $training_detail['time_end'];
                }
                $training_detail['time'] = implode(' - ', $time_parts);

                error_log("Online training detail found: " . $training_detail['title']);

                // Simplified participant handling - just add submitter
                $participants[] = [
                    'name' => 'Pemohon Training',
                    'nik' => 'N/A',
                    'dept' => 'N/A',
                    'jabatan' => 'N/A',
                    'status' => 'Pemohon'
                ];

                // Simplified - no complex queries for better performance
                error_log("Online training participants simplified for performance");
            }
            $stmt->close();
            break;

        default:
            throw new Exception('Invalid training type');
    }

    if (!$training_detail) {
        throw new Exception('Training not found');
    }

    // Format dates for display
    if ($training_detail['date']) {
        $raw_date = $training_detail['date'];
        $date = new DateTime($training_detail['date']);
        $formatted_date = $date->format('d M Y');

        // Handle end_date if exists and different from start_date
        if (!empty($training_detail['end_date']) && $training_detail['end_date'] !== $training_detail['date']) {
            $end_date = new DateTime($training_detail['end_date']);
            $formatted_date .= ' - ' . $end_date->format('d M Y');
        }

        $training_detail['date'] = $formatted_date;
        $training_detail['raw_date'] = $raw_date; // Keep original date for calendar consistency
    }

    // Add participants to training detail
    $training_detail['participants'] = $participants;

    echo json_encode([
        'success' => true,
        'training' => $training_detail
    ]);

} catch (Exception $e) {
    error_log("Error in get_admin_training_detail.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error loading training detail: ' . $e->getMessage()
    ]);
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
