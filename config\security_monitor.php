<?php
/**
 * File untuk mendeteksi dan mencatat serangan
 * File ini harus di-include di awal setiap halaman
 */

// Pastikan session sudah dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Buat direktori logs jika belum ada
$log_dir = __DIR__ . '/../logs';
if (!file_exists($log_dir)) {
    mkdir($log_dir, 0755, true);
}

// Fungsi untuk mencatat serangan
function log_attack($attack_type, $details, $user_id = 0) {
    global $conn;
    
    // Log ke file
    $log_dir = __DIR__ . '/../logs';
    $log_file = $log_dir . '/security.log';
    
    $log_message = date('Y-m-d H:i:s') . " | Attack: $attack_type | " .
                  "IP: " . $_SERVER['REMOTE_ADDR'] . " | " .
                  "User-Agent: " . $_SERVER['HTTP_USER_AGENT'] . " | " .
                  "URI: " . $_SERVER['REQUEST_URI'] . " | " .
                  "User ID: " . ($user_id ?: 'Not logged in') . " | " .
                  "Details: " . json_encode($details) . "\n";
    
    error_log($log_message, 3, $log_file);
    
    // Log ke database jika koneksi tersedia
    if (isset($conn) && $conn instanceof mysqli && !$conn->connect_error) {
        try {
            $query = "INSERT INTO security_logs (user_id, event_type, description, ip_address, user_agent, created_at) 
                      VALUES (?, ?, ?, ?, ?, NOW())";
            $stmt = $conn->prepare($query);
            
            $event_type = "ATTACK_" . strtoupper($attack_type);
            $description = json_encode($details);
            $ip_address = $_SERVER['REMOTE_ADDR'];
            $user_agent = $_SERVER['HTTP_USER_AGENT'];
            
            $stmt->bind_param("issss", $user_id, $event_type, $description, $ip_address, $user_agent);
            $stmt->execute();
        } catch (Exception $e) {
            error_log("Error logging attack to database: " . $e->getMessage(), 3, $log_file);
        }
    }
}

// Deteksi SQL Injection
function detect_sql_injection() {
    $patterns = array(
        '/(\%27)|(\')|(\-\-)|(\%23)|(#)/i',
        '/((\%3D)|(=))[^\n]*((\%27)|(\')|(\-\-)|(\%3B)|(;))/i',
        '/\w*((\%27)|(\'))((\%6F)|o|(\%4F))((\%72)|r|(\%52))/i',
        '/(union).*(select)/i',
        '/(select|update|delete|insert|drop|alter|truncate).*(from|into|table)/i'
    );
    
    $input_sources = array_merge($_GET, $_POST, $_COOKIE);
    
    foreach ($input_sources as $key => $value) {
        if (is_string($value)) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $value)) {
                    log_attack('sql_injection', [
                        'parameter' => $key,
                        'value' => $value,
                        'pattern' => $pattern
                    ], $_SESSION['user_id'] ?? 0);
                    
                    // Redirect ke halaman error
                    header('Location: /training/view/error.php?error=security');
                    exit();
                }
            }
        }
    }
}

// Deteksi XSS
function detect_xss() {
    $patterns = array(
        '/<script[^>]*>.*?<\/script>/i',
        '/javascript:[^"]*/i',
        '/<iframe[^>]*>.*?<\/iframe>/i',
        '/onload=|onerror=|onclick=|onmouseover=|onmouseout=|onkeydown=|onkeypress=/i'
    );
    
    $input_sources = array_merge($_GET, $_POST, $_COOKIE);
    
    foreach ($input_sources as $key => $value) {
        if (is_string($value)) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $value)) {
                    log_attack('xss', [
                        'parameter' => $key,
                        'value' => $value,
                        'pattern' => $pattern
                    ], $_SESSION['user_id'] ?? 0);
                    
                    // Redirect ke halaman error
                    header('Location: /training/view/error.php?error=security');
                    exit();
                }
            }
        }
    }
}

// Deteksi Path Traversal
function detect_path_traversal() {
    $patterns = array(
        '/\.\.\//',
        '/\.\.\\\\/'
    );
    
    $input_sources = array_merge($_GET, $_POST, $_COOKIE);
    
    foreach ($input_sources as $key => $value) {
        if (is_string($value)) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $value)) {
                    log_attack('path_traversal', [
                        'parameter' => $key,
                        'value' => $value,
                        'pattern' => $pattern
                    ], $_SESSION['user_id'] ?? 0);
                    
                    // Redirect ke halaman error
                    header('Location: /training/view/error.php?error=security');
                    exit();
                }
            }
        }
    }
}

// Deteksi CSRF
function detect_csrf() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token']) || 
            !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            
            log_attack('csrf', [
                'post_token' => $_POST['csrf_token'] ?? 'Not set',
                'session_token' => $_SESSION['csrf_token'] ?? 'Not set',
                'referer' => $_SERVER['HTTP_REFERER'] ?? 'Not set'
            ], $_SESSION['user_id'] ?? 0);
            
            // Redirect ke halaman error
            header('Location: /training/view/error.php?error=security');
            exit();
        }
    }
}

// Deteksi Brute Force
function detect_brute_force() {
    global $conn;
    
    if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
        return;
    }
    
    $ip_address = $_SERVER['REMOTE_ADDR'];
    $time_window = 300; // 5 menit
    $max_attempts = 10;
    
    try {
        $query = "SELECT COUNT(*) as attempts FROM login_attempts 
                  WHERE ip_address = ? AND success = 0 AND 
                  attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("si", $ip_address, $time_window);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        if ($row['attempts'] >= $max_attempts) {
            log_attack('brute_force', [
                'ip_address' => $ip_address,
                'attempts' => $row['attempts'],
                'time_window' => $time_window
            ], $_SESSION['user_id'] ?? 0);
            
            // Redirect ke halaman error
            header('Location: /training/view/error.php?error=security');
            exit();
        }
    } catch (Exception $e) {
        error_log("Error detecting brute force: " . $e->getMessage());
    }
}

// Deteksi User Agent Mencurigakan
function detect_suspicious_user_agent() {
    $suspicious_patterns = array(
        '/bot/i',
        '/spider/i',
        '/crawl/i',
        '/scrape/i',
        '/curl/i',
        '/wget/i',
        '/nikto/i',
        '/nmap/i',
        '/acunetix/i',
        '/sqlmap/i',
        '/dirbuster/i',
        '/havij/i',
        '/nessus/i'
    );
    
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    foreach ($suspicious_patterns as $pattern) {
        if (preg_match($pattern, $user_agent)) {
            log_attack('suspicious_user_agent', [
                'user_agent' => $user_agent,
                'pattern' => $pattern
            ], $_SESSION['user_id'] ?? 0);
            
            // Redirect ke halaman error
            header('Location: /training/view/error.php?error=security');
            exit();
        }
    }
}

// Deteksi HTTP Method yang Tidak Valid
function detect_invalid_http_method() {
    $allowed_methods = array('GET', 'POST', 'HEAD');
    
    if (!in_array($_SERVER['REQUEST_METHOD'], $allowed_methods)) {
        log_attack('invalid_http_method', [
            'method' => $_SERVER['REQUEST_METHOD']
        ], $_SESSION['user_id'] ?? 0);
        
        // Redirect ke halaman error
        header('Location: /training/view/error.php?error=security');
        exit();
    }
}

// Jalankan semua deteksi
detect_sql_injection();
detect_xss();
detect_path_traversal();
detect_suspicious_user_agent();
detect_invalid_http_method();

// Deteksi CSRF hanya untuk request POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    detect_csrf();
}

// Deteksi Brute Force hanya untuk halaman login
if (strpos($_SERVER['REQUEST_URI'], 'login.php') !== false) {
    detect_brute_force();
}
?>
