<?php
// <PERSON><PERSON> output buffering untuk menangkap semua output
ob_start();

// Nonaktifkan error reporting yang bisa merusak JSON
error_reporting(0);
ini_set('display_errors', 0);

// Set header JSON di awal
header('Content-Type: application/json');

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Get search parameter
$search = isset($_GET['search']) ? $_GET['search'] : '';
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $limit;

// Get selected NIKs if any
$selectedNIKs = isset($_GET['selected_niks']) ? json_decode($_GET['selected_niks'], true) : [];



// Get column filters
$columnFilters = isset($_GET['column_filters']) ? json_decode($_GET['column_filters'], true) : [];

// Get sort parameters
$sortColumn = isset($_GET['sort_column']) ? $_GET['sort_column'] : '';
$sortDirection = isset($_GET['sort_direction']) ? $_GET['sort_direction'] : 'asc';

try {
    // Build query
    $query = "SELECT * FROM karyawan";
    $params = [];
    $types = "";
    $searchCondition = "";

    // Initialize WHERE clauses array
    $whereClauses = [];

    // Add search conditions if search parameter is provided
    if (!empty($search)) {
        // Split search terms by space
        $searchTerms = explode(' ', $search);

        // Build WHERE clause for each term
        $searchClauses = [];
        foreach ($searchTerms as $term) {
            $term = trim($term);
            if (!empty($term)) {
                $searchClauses[] = "(
                    nik LIKE ? OR
                    nama LIKE ? OR
                    DATE_FORMAT(tgl_masuk, '%d-%m-%Y') LIKE ? OR
                    jk LIKE ? OR
                    level_karyawan LIKE ? OR
                    DATE_FORMAT(tgl_lahir, '%d-%m-%Y') LIKE ? OR
                    agama LIKE ? OR
                    pendidikan_akhir LIKE ? OR
                    no_telp LIKE ? OR
                    dept LIKE ? OR
                    bagian LIKE ? OR
                    jabatan LIKE ?
                )";

                // Add parameters for each field
                $searchParam = "%$term%";
                $params = array_merge($params, array_fill(0, 12, $searchParam));
                $types .= str_repeat("s", 12);
            }
        }

        if (!empty($searchClauses)) {
            $whereClauses[] = '(' . implode(" AND ", $searchClauses) . ')';
        }
    }



    // Tambahkan filter kolom jika ada
    if (!empty($columnFilters) && is_array($columnFilters)) {
        // Debug: Log filter yang diterima
        error_log("Column Filters: " . json_encode($columnFilters));

        // Urutkan filter untuk memastikan hierarki filter diproses dengan benar
        $columnOrder = ['dept', 'bagian', 'jabatan', 'group', 'nama', 'nik'];
        $orderedFilters = [];

        // Tambahkan filter yang diurutkan terlebih dahulu
        foreach ($columnOrder as $column) {
            if (isset($columnFilters[$column])) {
                $orderedFilters[$column] = $columnFilters[$column];
            }
        }

        // Tambahkan filter lainnya
        foreach ($columnFilters as $column => $values) {
            if (!in_array($column, $columnOrder)) {
                $orderedFilters[$column] = $values;
            }
        }

        // Debug: Log filter yang sudah diurutkan
        error_log("Ordered Filters: " . json_encode($orderedFilters));

        // Proses filter yang sudah diurutkan
        foreach ($orderedFilters as $column => $values) {
            // Pastikan kolom valid untuk mencegah SQL injection
            $allowedColumns = [
                'nik', 'nama', 'tgl_masuk', 'jk', 'level_karyawan', 'tgl_lahir',
                'agama', 'pendidikan_akhir', 'no_telp', 'dept', 'bagian', 'jabatan',
                'group', 'status', 'pt'
            ];

            if (!in_array($column, $allowedColumns)) {
                continue; // Skip kolom yang tidak valid
            }

            // Escape kolom untuk keamanan
            $columnEscaped = "`" . str_replace("`", "``", $column) . "`";

            if (!empty($values) && is_array($values)) {
                $columnClauses = [];
                foreach ($values as $value) {
                    // Gunakan LIKE untuk pencarian yang lebih fleksibel
                    $columnClauses[] = "$columnEscaped = ?";
                    $params[] = $value;
                    $types .= "s";
                }
                if (!empty($columnClauses)) {
                    $whereClauses[] = '(' . implode(' OR ', $columnClauses) . ')';
                }
            }
        }

        // Debug: Log WHERE clauses yang dihasilkan
        error_log("WHERE Clauses: " . json_encode($whereClauses));
    }

    // Combine all WHERE conditions
    if (!empty($whereClauses)) {
        $searchCondition = " WHERE " . implode(" AND ", $whereClauses);
        $query .= $searchCondition;
    }

    // Get total records
    $countQuery = "SELECT COUNT(*) as count FROM karyawan" . $searchCondition;

    if (!empty($params)) {
        $stmt = $conn->prepare($countQuery);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $total_result = $stmt->get_result();
    } else {
        $total_result = $conn->query($countQuery);
    }

    if (!$total_result) {
        throw new Exception("Error executing count query: " . $conn->error);
    }

    $total_records = $total_result->fetch_assoc()['count'];

    // Add ORDER BY clause if sort column is specified
    if (!empty($sortColumn)) {
        // Validate sort column to prevent SQL injection
        $allowedColumns = [
            'nik', 'nama', 'tgl_masuk', 'jk', 'level_karyawan', 'tgl_lahir',
            'agama', 'pendidikan_akhir', 'no_telp', 'dept', 'bagian', 'jabatan',
            'group', 'status', 'pt'
        ];

        if (in_array($sortColumn, $allowedColumns)) {
            $sortDirection = strtoupper($sortDirection) === 'DESC' ? 'DESC' : 'ASC';
            $query .= " ORDER BY `$sortColumn` $sortDirection";
        } else {
            // Default sorting if no valid sort column specified
            $query .= " ORDER BY nik ASC";
        }
    } else {
        // Default sorting if no sort column specified
        $query .= " ORDER BY nik ASC";
    }

    // Debug: Log query sebelum pagination
    error_log("search_employees.php - Query before pagination: " . $query);

    // Add pagination to the main query
    $query .= " LIMIT ? OFFSET ?";

    // Add limit and offset parameters
    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';

    // Debug: Log final query dan parameter
    error_log("search_employees.php - Final query: " . $query);
    error_log("search_employees.php - Parameters: " . json_encode($params));

    // Prepare and execute the main query
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Error preparing query: " . $conn->error);
    }

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    if (!$stmt->execute()) {
        throw new Exception("Error executing query: " . $stmt->error);
    }

    $result = $stmt->get_result();

    // Fetch results
    $employees = [];
    while ($row = $result->fetch_assoc()) {
        $employees[] = $row;
    }

    // Return success response
    echo json_encode([
        'success' => true,
        'employees' => $employees,
        'total_records' => $total_records,
        'current_page' => $page,
        'limit' => $limit
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($stmt)) {
        $stmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }

    // Flush output buffer and end script
    ob_end_flush();
}

