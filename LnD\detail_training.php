<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

try {
    include '../config/config.php';
    include 'security.php';
    include '../config/notification_helper.php';
} catch (Exception $e) {
    error_log("Error including required files: " . $e->getMessage());
    die("Terjadi kesalahan saat memuat file yang diperlukan. Silakan coba lagi atau hubungi administrator.");
}

$id = $_GET['id'] ?? null;
if (!$id || !is_numeric($id)) {
    $_SESSION['error'] = "ID training tidak valid.";
    header("Location: dashboard.php");
    exit;
}

try {
    $query = "SELECT ts.id, ts.assignment, ts.full_name, ts.status, ts.training_topic, ts.nik, ts.departemen, ts.bagian, ts.jabatan,
                    ts.training_type, ts.training_skill_type, ts.start_date, ts.end_date, ts.is_confirmed, ts.training_place, ts.training_cost,
                    ts.contact_person, ts.contact_number, ts.sharing_knowledge, ts.additional_info, ts.approved_hrd, ts.internal_memo_image,
                    ts.email, ts.phone, ts.comments_dept_head, ts.comments_hrd, ts.comments_ga,
                    ts.training_time_start, ts.training_time_end, ts.provider_name, ts.provider_address,
                    ts.provider_type, ts.trainer_name_internal, ts.trainer_nik_internal, ts.trainer_department_internal,
                    ts.trainer_sub_department_internal, ts.trainer_position_internal, ts.trainer_name_external, ts.additional_info_provider,
                    r.role_name AS current_approver,
                    GROUP_CONCAT(p.nama_participants SEPARATOR ', ') AS participant_names,
                    GROUP_CONCAT(p.nik_participants SEPARATOR ', ') AS participant_niks,
                    GROUP_CONCAT(p.jabatan_participants SEPARATOR ', ') AS participant_jabatans,
                    GROUP_CONCAT(p.bagian_participants SEPARATOR ', ') AS participant_bagians,
                    GROUP_CONCAT(p.departemen_participants SEPARATOR ', ') AS participant_departemens
            FROM training_submissions ts
            LEFT JOIN roles r ON ts.current_approver_role_id = r.id
            LEFT JOIN participants p ON ts.id = p.training_id
            WHERE ts.id = ?
            GROUP BY ts.id";
    $stmt = $conn->prepare($query);
    if ($stmt === false) {
        throw new Exception("Error preparing query: " . $conn->error);
    }
    $stmt->bind_param("i", $id);
    if (!$stmt->execute()) {
        throw new Exception("Error executing query: " . $stmt->error);
    }
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    if (!$row) {
        throw new Exception("Training submission dengan ID $id tidak ditemukan.");
    }
} catch (Exception $e) {
    error_log("Error fetching training data: " . $e->getMessage());
    $_SESSION['error'] = "Terjadi kesalahan saat mengambil data training: " . $e->getMessage();
    header("Location: dashboard.php");
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        error_log("POST data received: " . json_encode($_POST));
        error_log("FILES data received: " . json_encode($_FILES));

        $status = $_POST['status'] ?? '';
        $comments = $_POST['comments'] ?? '';

        // Basic validation
        if (!in_array($status, ['Approved', 'Rejected', 'Revise'])) {
            throw new Exception("Status tidak valid: " . $status);
        }

        // For testing - if status is Rejected or Revise, do simple update
        if ($status === 'Rejected' || $status === 'Revise') {
            $updateQuery = "UPDATE training_submissions SET status = ?, comments_hrd = ?, current_approver_role_id = NULL, next_approver_id = NULL, approved_dept_head = 'Pending', approved_hrd = 'Pending', approved_ga = 'Pending', approved_fm = 'Pending', approved_dir = 'Pending', is_confirmed = 0 WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            if ($updateStmt === false) {
                throw new Exception("Error preparing simple update query: " . $conn->error);
            }
            $updateStmt->bind_param("ssi", $status, $comments, $id);

            if ($updateStmt->execute()) {
                error_log("Training ID $id updated successfully with status: $status");
                $_SESSION['success'] = "Training berhasil diperbarui dengan status: $status";
                header("Location: dashboard.php");
                exit;
            } else {
                throw new Exception("Gagal memperbarui data: " . $updateStmt->error);
            }
        }

        $internal_memo_image_path = $row['internal_memo_image'] ?? null;
        if (isset($_FILES['internal_memo_image']) && $_FILES['internal_memo_image']['error'] === UPLOAD_ERR_OK) {
            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
            $file_type = $_FILES['internal_memo_image']['type'];
            $file_extension = strtolower(pathinfo($_FILES['internal_memo_image']['name'], PATHINFO_EXTENSION));
            if (!in_array($file_type, $allowed_types) || !in_array($file_extension, ['jpg', 'jpeg', 'png', 'pdf'])) {
                throw new Exception("Format file tidak valid. Hanya file JPG, JPEG, PNG, dan PDF yang diperbolehkan.");
            }
            if ($_FILES['internal_memo_image']['size'] > 2 * 1024 * 1024) {
                throw new Exception("Ukuran file terlalu besar. Maksimal 2MB.");
            }
            $upload_dir = '../Uploads/internal_memo/';
            if (!file_exists($upload_dir)) {
                if (!mkdir($upload_dir, 0755, true)) {
                    throw new Exception("Gagal membuat direktori upload.");
                }
            }
            $unique_filename = 'internal_memo_' . $id . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $unique_filename;
            if (move_uploaded_file($_FILES['internal_memo_image']['tmp_name'], $upload_path)) {
                $internal_memo_image_path = 'Uploads/internal_memo/' . $unique_filename;
                error_log("Internal memo image uploaded successfully: " . $internal_memo_image_path);
            } else {
                throw new Exception("Gagal mengunggah file. Silakan coba lagi.");
            }
        }

        $training_type = '';
        $training_skill_type = '';
        $training_provider = '';
        $trainer_name_internal = [];
        $trainer_nik_internal = [];
        $trainer_department_internal = [];
        $trainer_sub_department_internal = [];
        $trainer_position_internal = [];
        $provider_name = '';
        $provider_address = '';
        $trainer_name_external = '';
        $additional_info_provider = '';
        $multi_day = 'Tidak';
        $start_date = null;
        $end_date = null;
        $training_place = '';
        $training_cost = '';
        $contact_person = '';
        $contact_number = '';
        $sharing_knowledge = 'Tidak';
        $training_time_start = '';
        $training_time_end = '';
        $is_confirmed = '0';

        if ($status === 'Approved') {
            $training_type = $_POST['training_type'] ?? '';
            $training_skill_type = $_POST['training_skill_type'] ?? '';
            $training_provider = $_POST['provider_type'] ?? '';
            $trainer_name_internal = $_POST['trainer_name_internal'] ?? [];
            $trainer_nik_internal = $_POST['trainer_nik_internal'] ?? [];
            $trainer_department_internal = $_POST['trainer_department_internal'] ?? [];
            $trainer_sub_department_internal = $_POST['trainer_sub_department_internal'] ?? [];
            $trainer_position_internal = $_POST['trainer_position_internal'] ?? [];
            $provider_name = $_POST['provider_name'] ?? '';
            $provider_address = $_POST['provider_address'] ?? '';
            $trainer_name_external = $_POST['trainer_name_external'] ?? '';
            $additional_info_provider = $_POST['additional_info_provider'] ?? '';
            $multi_day = $_POST['multi_day'] ?? 'Tidak';
            if ($multi_day === 'Ya') {
                $start_date = $_POST['start_date'] ?? '';
                $end_date = $_POST['end_date'] ?? '';
            } else {
                $start_date = $_POST['fixed_date'] ?? '';
                $end_date = $start_date;
            }
            $training_place = $_POST['training_place'] ?? 'TBD'; // Default value since field not in form
            $training_cost = str_replace(".", "", $_POST['training_cost'] ?? '');
            $contact_person = $_POST['contact_person'] ?? '';
            $contact_number = $_POST['contact_number'] ?? '';
            $sharing_knowledge = $_POST['sharing_knowledge'] ?? 'Tidak';
            $training_time_start = $_POST['training_time_start'] ?? '';
            $training_time_end = $_POST['training_time_end'] ?? '';
            $is_confirmed = '1';

            if ($multi_day === 'Ya' && (empty($start_date) || empty($end_date))) {
                throw new Exception("Tanggal mulai dan tanggal selesai harus diisi untuk training multi-hari.");
            }
            if ($multi_day === 'Tidak' && empty($start_date)) {
                throw new Exception("Tanggal pelaksanaan harus diisi untuk training satu hari.");
            }
            if ($start_date && $end_date && $start_date > $end_date) {
                throw new Exception("Tanggal Berakhir tidak boleh sebelum Tanggal Mulai.");
            }

            // Trainer validation for internal provider

            // Improved validation for internal trainer
            if ($training_provider === 'Internal') {
                $has_trainer = false;
                if (is_array($trainer_name_internal) && count($trainer_name_internal) > 0) {
                    // Check if at least one trainer name is not empty
                    foreach ($trainer_name_internal as $name) {
                        if (!empty(trim($name))) {
                            $has_trainer = true;
                            break;
                        }
                    }
                }

                if (!$has_trainer) {
                    throw new Exception("Setidaknya satu trainer harus ditambahkan untuk provider internal.");
                }
            }
        }

        $trainer_names = json_encode($trainer_name_internal);
        $trainer_niks = json_encode($trainer_nik_internal);
        $trainer_departments = json_encode($trainer_department_internal);
        $trainer_sub_departments = json_encode($trainer_sub_department_internal);
        $trainer_positions = json_encode($trainer_position_internal);

        $queryRole = "SELECT current_approver_role_id FROM training_submissions WHERE id = ?";
        $stmtRole = $conn->prepare($queryRole);
        $stmtRole->bind_param("i", $id);
        $stmtRole->execute();
        $resultRole = $stmtRole->get_result();
        $currentApprover = $resultRole->fetch_assoc();
        if (!$currentApprover) {
            throw new Exception("Training submission tidak ditemukan.");
        }

        $currentUserQuery = "SELECT u.jabatan FROM users u WHERE u.id = ?";
        $currentUserStmt = $conn->prepare($currentUserQuery);
        $currentUserStmt->bind_param("i", $_SESSION['user_id']);
        $currentUserStmt->execute();
        $currentUserResult = $currentUserStmt->get_result();
        $currentUser = $currentUserResult->fetch_assoc();
        $jabatan = $currentUser['jabatan'] ?? '';
        $currentRoleId = $currentApprover['current_approver_role_id'];

        $roleHierarchy = [
            1 => 2, // Admin to Dept Head
            2 => 3, // Dept Head to HRD
            3 => 2, // HRD to Manager HRGA
            5 => 6  // Factory Manager to Direktur
        ];

        if ($currentRoleId == 3) {
            $queryHRGAManager = "SELECT id FROM users WHERE jabatan = 'Manager HRGA' AND role_id = 2 LIMIT 1";
            $resultHRGAManager = $conn->query($queryHRGAManager);
            $hrgaManager = $resultHRGAManager->fetch_assoc();
            if ($hrgaManager) {
                $nextRoleId = 2;
                $updateNextApproverQuery = "UPDATE training_submissions SET next_approver_id = ? WHERE id = ?";
                $updateNextApproverStmt = $conn->prepare($updateNextApproverQuery);
                $updateNextApproverStmt->bind_param("ii", $hrgaManager['id'], $id);
                $updateNextApproverStmt->execute();
                $updateNextApproverStmt->close();
                error_log("Training ID $id: Next approver set to Manager HRGA (ID: " . $hrgaManager['id'] . ")");
            } else {
                $nextRoleId = 4;
                error_log("Training ID $id: No HRGA Manager found, proceeding to Factory Manager (role_id 4)");
            }
        } elseif ($currentRoleId == 2 && $jabatan == 'Manager HRGA') {
            $nextRoleId = 4;
        } elseif ($currentRoleId == 4) {
            $nextRoleId = 5;
        } else {
            $nextRoleId = $roleHierarchy[$currentRoleId] ?? 1;
        }

        if ($status == 'Approved') {
            $updateQuery = "UPDATE training_submissions
                            SET status = ?, comments_hrd = ?, training_type = ?, training_skill_type = ?,
                                start_date = ?, end_date = ?, training_place = ?,
                                training_cost = ?, contact_person = ?, contact_number = ?, sharing_knowledge = ?,
                                current_approver_role_id = ?, approved_hrd = 'Approved',
                                provider_type = ?, trainer_name_internal = ?, trainer_nik_internal = ?,
                                trainer_department_internal = ?, trainer_sub_department_internal = ?,
                                trainer_position_internal = ?, provider_name = ?, provider_address = ?,
                                trainer_name_external = ?, additional_info_provider = ?,
                                training_time_start = ?, training_time_end = ?, internal_memo_image = ?, is_confirmed = ?
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            if ($updateStmt === false) {
                throw new Exception("Error preparing update query: " . $conn->error);
            }
            $updateStmt->bind_param("sssssssssssissssssssssssssi",
                $status, $comments, $training_type, $training_skill_type,
                $start_date, $end_date, $training_place, $training_cost,
                $contact_person, $contact_number, $sharing_knowledge,
                $nextRoleId, $training_provider, $trainer_names, $trainer_niks,
                $trainer_departments, $trainer_sub_departments, $trainer_positions,
                $provider_name, $provider_address, $trainer_name_external,
                $additional_info_provider, $training_time_start, $training_time_end,
                $internal_memo_image_path, $is_confirmed, $id
            );
        } elseif ($status == 'Revise') {
            $updateQuery = "UPDATE training_submissions
                            SET status = ?, comments_hrd = ?, approved_hrd = 'Revise', is_confirmed = 0,
                                current_approver_role_id = NULL, next_approver_id = NULL
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("ssi", $status, $comments, $id);
        } else {
            $updateQuery = "UPDATE training_submissions
                            SET status = 'Rejected',
                                comments_hrd = ?,
                                assignment = ?,
                                approved_hrd = 'Rejected',
                                rejected_at = CURRENT_TIMESTAMP,
                                rejected_by = ?
                            WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("ssii", $comments, $assignment, $_SESSION['user_id'], $id);
        }

        if ($updateStmt->execute()) {
            error_log("Training ID $id updated successfully with status: $status");
            $_SESSION['success'] = "Training berhasil diperbarui dengan status: $status";
            header("Location: dashboard.php");
            exit;
        } else {
            throw new Exception("Gagal memperbarui data: " . $updateStmt->error);
        }
    } catch (Exception $e) {
        error_log("Error in detail_training.php: " . $e->getMessage());
        error_log("POST data: " . json_encode($_POST));
        error_log("FILES data: " . json_encode($_FILES));
        $_SESSION['error'] = "Terjadi kesalahan saat memproses form: " . $e->getMessage();
        header("Location: detail_training.php?id=$id&error=1");
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<link rel="stylesheet" href="../assets/css/form-style.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<head>
    <script>
        function showAlert(message) {
            let alertBox = document.createElement("div");
            alertBox.innerText = message;
            alertBox.style.position = "fixed";
            alertBox.style.top = "20px";
            alertBox.style.left = "50%";
            alertBox.style.transform = "translateX(-50%)";
            alertBox.style.backgroundColor = "#28a745";
            alertBox.style.color = "white";
            alertBox.style.padding = "10px 20px";
            alertBox.style.borderRadius = "5px";
            alertBox.style.boxShadow = "0px 4px 6px rgba(0,0,0,0.1)";
            document.body.appendChild(alertBox);
            setTimeout(() => { alertBox.remove(); }, 3000);
        }
    </script>
</head>
<style>
    .full-width {
        width: 100%;
        height: 30px;
        margin-bottom: 20px;
    }
    input[type="date"] {
        padding: 10px;
        font-size: 12px;
        font-weight: bold;
        border: 1px solid #ccc;
        border-radius: 5px;
        background-color: white;
        color: black;
    }
    input[type="time"]::-webkit-calendar-picker-indicator {
        display: none;
    }
    .expanded {
        display: flex;
        gap: 20px;
        align-items: center;
        flex-wrap: wrap;
    }
    .expanded label {
        flex: 1;
    }
    #search_trainer {
        width: 100%;
        padding: 12px;
        border: 1px solid var(--border-color-);
        border-radius: var(--border-radius);
        margin-bottom: 10px;
    }

    #trainer_results {
        background: white;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        max-height: 200px;
        overflow-y: auto;
    }

    .trainer-result {
        padding: 12px;
        border-bottom: 1px solid var(--border-color);
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .trainer-result:hover {
        background-color: var(--secondary-color);
    }

    /* Participant Table */
    .trainer-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
        background: white;
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    .flatpickr-time input {
        pointer-events: none; /* Mencegah klik pada input */
        color: white;
        background-color: #BF0000; /* Warna latar belakang untuk menunjukkan readonly */
    }
    .flatpickr-time .numInputWrapper span.arrowUp,
    .flatpickr-time .numInputWrapper span.arrowDown {
        cursor: pointer; /* Pastikan tombol panah terlihat interaktif */
    }
    .flatpickr-time .numInputWrapper span.arrowUp::after {
        border-bottom-color: white; /* Warna panah atas */
    }
    .flatpickr-time .numInputWrapper span.arrowDown::after {
        border-top-color: white; /* Warna panah bawah */
    }
    .full-width {
    width: 100%;
    height: 30px; /* Untuk input */
    margin-bottom: 10px; /* Jarak antar elemen */
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
}
textarea.full-width {
    height: auto; /* Override untuk textarea */
    resize: vertical; /* Hanya bisa di-resize vertikal */
}
small {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

                            .form-group {
                                margin-bottom: 1.5rem;
                            }

                            .form-control {
                                width: 100%;
                                padding: 0.375rem 0.75rem;
                                font-size: 1rem;
                                line-height: 1.5;
                                color: #495057;
                                background-color: #fff;
                                border: 1px solid #ced4da;
                                border-radius: 0.25rem;
                                transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                            }

                            .date-inputs {
                                display: flex;
                                gap: 20px;
                                flex-wrap: wrap;
                            }

                            .date-inputs > div {
                                flex: 1;
                                min-width: 200px;
                                margin: 10px;
                            }

                            .form-control:focus {
                                color: #495057;
                                background-color: #fff;
                                border-color: #ff0000;
                                outline: 0;
                                box-shadow: 0 0 0 0.2rem rgba(255, 0, 0, 0.25);
                            }

                            .required:after {
                                content: " *";
                                color: red;
                            }

                            .form-text {
                                display: block;
                                margin-top: 0.25rem;
                                font-size: 80%;
                                color: #6c757d;
                            }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container-form">
    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger" style="background-color: #f8d7da; color: #721c24; padding: 15px; margin-bottom: 20px; border: 1px solid #f5c6cb; border-radius: 4px;">
            <?= htmlspecialchars($_SESSION['error']) ?>
            <?php unset($_SESSION['error']); // Hapus pesan setelah ditampilkan ?>
        </div>
    <?php endif; ?>

    <div class="form-container">
        <div class="latest-submissions">
            <h1>Detail Pengajuan Training</h1>
            <?php include '../config/table.php'; ?>

            <h3>Berikan Keputusan</h3>
            <form method="POST" action="" enctype="multipart/form-data">
                <label class="required" for="status_select">Status</label>
                <select name="status" id="status_select" required onchange="toggleFormFields()">
                    <option value="Approved" <?= ($row['status'] ?? '') == 'Approved' ? 'selected' : '' ?>>Approved</option>
                    <option value="Rejected" <?= ($row['status'] ?? '') == 'Rejected' ? 'selected' : '' ?>>Rejected</option>
                    <option value="Revise" <?= ($row['status'] ?? '') == 'Revise' ? 'selected' : '' ?>>Revise</option>
                </select><br><br>

                <!-- Kolom komentar untuk semua status -->
                <div id="comments_section" class="form-group">
                    <h3 id="comments_heading">Komentar</h3>
                    <label for="comments">Komentar</label>
                    <textarea name="comments" id="comments" class="full-width" rows="4" placeholder="Tambahkan komentar"><?= htmlspecialchars($row['comments_hrd'] ?? '') ?></textarea>
                    <small id="comments_help" class="form-text text-muted">
                        <?php if ($row['status'] == 'Revise'): ?>
                            Berikan alasan dan petunjuk yang jelas untuk revisi yang diperlukan
                        <?php elseif ($row['status'] == 'Rejected'): ?>
                            Berikan alasan yang jelas mengapa pengajuan ini ditolak
                        <?php else: ?>
                            Berikan komentar tambahan jika diperlukan (opsional)
                        <?php endif; ?>
                    </small>
                </div>

                <!-- Bagian form untuk status Approved dan Rejected -->
                <div id="approval_fields_section">
                    <div class="form-group">
                        <h3>Deskripsi Training</h3>
                    </div>
                    <div class="form-group">
                        <label class="required" for="training_type_select">Jenis Training</label>
                        <small class="form-text text-muted">Pilih jenis training yang sesuai</small>
                        <select name="training_type" id="training_type_select" required>
                            <option value="" disabled selected>Tentukan jenis training</option>
                            <option value="Training Inhouse">Training Inhouse</option>
                            <option value="Training Outhouse">Training Outhouse</option>
                            <option value="Training Hybrid">Training Hybrid</option>
                        </select>
                    </div>
                <div class="form-group">
                    <label class="required" for="training_skill_type">Jenis Skill Training</label>
                    <small class="form-text text-muted">Pilih jenis kompetensi training yang sesuai (jika pilihan benar maka abaikan)</small>
                    <select name="training_skill_type" id="training_skill_type" required>
                        <option value="" disabled selected>Tentukan jenis skill training</option>
                        <option value="Soft Skill" <?= ($row['training_skill_type'] ?? '') == 'Soft Skill' ? 'selected' : '' ?>>Soft Skill</option>
                        <option value="Technical Skill" <?= ($row['training_skill_type'] ?? '') == 'Technical Skill' ? 'selected' : '' ?>>Technical Skill</option>
                        <option value="Compliance" <?= ($row['training_skill_type'] ?? '') == 'Compliance' ? 'selected' : '' ?>>Compliance</option>
                    </select>
                </div>
                <div class="form-group">
                    <h3>Provider Training</h3>
                </div>
                <div class="form-group">
                    <select name="provider_type" id="provider_type_select" required onchange="updateTrainingProvider()">
                        <option value="" disabled selected>Tentukan jenis provider</option>
                        <option value="Internal">Internal</option>
                        <option value="External">External</option>
                    </select>

                    <div class="form-section">
                        <h3>Daftar Trainer</h3>
                    </div>
                    <div id="internalFields" style="display: none;">
                        <div class="search-section">
                            <input type="text" id="search_trainer" placeholder="Cari trainer (minimal 3 karakter)...">
                            <div id="trainer_results"></div>
                        </div>
                        <div class="trainer-controls">
                            <button type="button" class="btn-add-trainer" onclick="addTrainerManual()">
                                Tambah Trainer Manual
                            </button>
                        </div>
                        <table class="trainer-table" id="trainer_table">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Nama</th>
                                    <th>NIK</th>
                                    <th>Departemen</th>
                                    <th>Bagian</th>
                                    <th>Jabatan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="trainer_list">
                            </tbody>
                        </table>
                    </div>
                </div>
                <div id="externalFields" style="display: none;">
                    <div class="form-group">
                        <label class="required" for="provider_name">Nama Provider</label>
                        <input type="text" name="provider_name" class="full-width" placeholder="Masukkan nama provider" required>
                    </div>
                    <div class="form-group">
                        <label class="required" for="provider_address">Alamat</label>
                        <input type="text" name="provider_address" class="full-width" placeholder="Masukkan alamat provider" required>
                    </div>
                    <div class="form-group">
                        <label class="" for="trainer_name_external">Nama Trainer</label>
                        <small style="color:black;">Kosongkan jika trainer belum di ketahui</small>
                        <input type="text" name="trainer_name_external" class="full-width" placeholder="Masukkan nama trainer eksternal">
                    </div>
                    <!-- <div class="form-group"> -->
                        <label class="" for="additional_info_provider">Informasi Tambahan mengenai Vendor/Trainer</label>
                        <small style="color:black;">Kosongkan jika tidak ada informasi tambahan</small>
                        <textarea name="additional_info_provider" class="full-width" rows="4" placeholder="Tambahkan informasi tambahan mengenai Vendor"></textarea>
                    </div>
                </div>
                    <div class="form-group" id="training_date_section">
                        <h3>Tempat dan Waktu</h3>

                    <div class="form-group">
                        <label class="required" for="training_place">Tempat Training Dilaksanakan</label>
                        <input type="text" name="training_place" class="full-width" placeholder="Masukkan tempat training dilaksanakan" required>
                    </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Estimasi dari pemohon:</strong> <?php echo htmlspecialchars($row['start_date'] ?? 'Belum ditentukan'); ?>
                        </div>

                        <!-- Durasi Training Selection -->
                        <div class="mb-3">
                            <label for="multi_day_select" class="form-label"><strong>Durasi Training</strong></label>
                            <select id="multi_day_select" name="multi_day" class="form-control" onchange="toggleDateFields()" required>
                                <option value="" disabled selected>Pilih Durasi Training</option>
                                <option value="Tidak">Training 1 Hari</option>
                                <option value="Ya">Training Multi-Hari</option>
                            </select>
                            <small class="form-text text-muted">Tentukan apakah training dilaksanakan dalam 1 hari atau beberapa hari</small>
                        </div>

                        <!-- Date Fields Container -->
                        <div id="date_fields_container">
                            <!-- Single Date Field -->
<div id="single_date_field" class="date-field-group" style="display: none;">
    <div class="card border-primary">
        <div class="card-body">
            <h5 class="card-title text-primary">Tanggal Pelaksanaan</h5>
            <div class="form-group">
                <label for="fixed_date" class="form-label required">Tanggal Training</label>
                <input type="date"
                       name="fixed_date"
                       id="fixed_date"
                       class="form-control form-control-lg"
                       min="<?= date('Y-m-d'); ?>"
                       value="<?= htmlspecialchars($row['start_date'] ?? '') ?>">
                <small class="form-text text-success">
                    <i class="fas fa-check-circle"></i> Training akan dilaksanakan dalam 1 hari
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Multi Date Fields -->
<div id="multi_date_fields" class="date-field-group" style="display: none;">
    <div class="card border-success">
        <div class="card-body">
            <h5 class="card-title text-success">Periode Training</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="training_start_date" class="form-label required">
                            <i class="fas fa-play-circle text-success"></i> Tanggal Mulai
                        </label>
                        <input type="date"
                               name="start_date"
                               id="training_start_date"
                               class="form-control form-control-lg"
                               min="<?= date('Y-m-d'); ?>"
                               value="<?= htmlspecialchars($row['start_date'] ?? '') ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="training_end_date" class="form-label required">
                            <i class="fas fa-stop-circle text-danger"></i> Tanggal Berakhir
                        </label>
                        <input type="date"
                               name="end_date"
                               id="training_end_date"
                               class="form-control form-control-lg"
                               min="<?= date('Y-m-d'); ?>"
                               value="<?= htmlspecialchars($row['end_date'] ?? '') ?>">
                    </div>
                </div>
            </div>
            <small class="form-text text-success">
                <i class="fas fa-calendar-alt"></i> Training akan dilaksanakan dalam beberapa hari berturut-turut
            </small>
        </div>
    </div>
</div>

                <div class="form-group time-section">
                <label class="required" for="training_time_start_input">Waktu</label>
                <div class="time-inputs" style="display: flex; gap: 20px; flex-wrap: wrap;">
                    <div style="flex: 1;">
                        <label class="required" for="training_time_start_input">Dari</label>
                        <input type="text"
                               id="training_time_start_input"
                               name="training_time_start"
                               class="full-width"
                               placeholder="08:00"
                               value="<?= htmlspecialchars($row['training_time_start'] ?? '08:00') ?>"
                               required>
                        <small>Contoh: 08:00 (24 jam)</small>
                    </div>
                    <div style="flex: 1;">
                        <label class="required" for="training_time_end_input">Sampai</label>
                        <input type="text"
                               id="training_time_end_input"
                               name="training_time_end"
                               class="full-width"
                               placeholder="17:00"
                               value="<?= htmlspecialchars($row['training_time_end'] ?? '17:00') ?>"
                               required>
                        <small>Contoh: 17:00 (24 jam)</small>
                    </div>
                </div>
                <p style="font-size: 13px; margin-top: 10px;">
                    <strong>Penjelasan:</strong> Gunakan tombol panah untuk mengatur waktu dalam format 24 jam (00:00 - 23:59).
                </p>
            </div>
        <div class="form-group">
            <h3>Biaya dan Kontak</h3>
            <label class="required" for="training_cost">Biaya Training</label>
            <input type="text" name="training_cost" id="training_cost" class="full-width" placeholder="Contoh: 1.000.000" required>
            <small style="color:black;">Gunakan format xxx.xxx.xxx.xxx (misalnya, 1.000.000 untuk 1 Juta).</small>
        </div>
        <div class="form-group">
            <label class="required" for="contact_person">Contact Person Trainer/Provider</label>
            <input type="text" name="contact_person" id="contact_person" class="full-width" placeholder="Nama lengkap" required maxlength="100">
            <small style="color:black;">Masukkan nama lengkap orang yang bisa dihubungi dari pihak trainer/Provider.</small>
        </div>
        <div class="form-group">
            <label class="required" for="contact_number">No. Telpon Trainer/Provider</label>
            <input type="tel" name="contact_number" id="contact_number" class="full-width" placeholder="Contoh: 081234567890" required pattern="[0-9]{10,15}" title="Masukkan 10-15 digit angka">
            <small style="color:black;">Gunakan angka tanpa spasi atau tanda (misalnya, 081234567890).</small>
        </div>
        <div class="form-group">
            <h3>Sharing Knowledge</h3>
            <label class="required" for="sharing_knowledge">Perlu Sharing Knowledge?</label>
            <small class="form-text text-muted">Sharing Knowledge adalah kegiatan untuk menyebarkan pengetahuan yang telah didapatkan dari training kepada rekan kerja lainnya</small>
            <select name="sharing_knowledge" id="sharing_knowledge" required>
                <option value="" disabled selected>Pilih opsi</option>
                <option value="Ya">Ya</option>
                <option value="Tidak">Tidak</option>
            </select>
        </div>

        <div class="form-group">
            <h3>Internal Memo</h3>
            <label for="internal_memo_image">Upload Gambar Internal Memo</label>
            <small class="form-text text-muted">Upload gambar internal memo dalam format JPG, JPEG, PNG, atau PDF (Maksimal 2MB)</small>

            <?php if (!empty($row['internal_memo_image'])): ?>
                <div class="existing-image" style="margin: 10px 0;">
                    <p><strong>Gambar Internal Memo yang sudah diupload:</strong></p>
                    <img src="../<?= htmlspecialchars($row['internal_memo_image']) ?>" alt="Internal Memo" style="max-width: 100%; max-height: 300px; border: 1px solid #ddd; padding: 5px;">
                    <p class="mt-2">Upload gambar baru untuk mengganti gambar yang sudah ada.</p>
                </div>
            <?php endif; ?>

            <input type="file" name="internal_memo_image" id="internal_memo_image" class="form-control" accept="image/jpeg,image/jpg,image/png,application/pdf">
            <div id="loading" style="display: none;">Mengunggah gambar...</div>
            <div id="image_preview" style="margin-top: 10px; display: none;">
                <img id="preview_img" src="#" alt="Preview" style="max-width: 100%; max-height: 300px;">
                <button type="button" id="remove_image" class="btn btn-sm btn-danger" style="margin-top: 5px;">Hapus Gambar</button>
            </div>
        </div>

        <style>
            #loading {
                color: black;
                display: none;
            }

            /* Pastikan tombol submit selalu terlihat dengan prioritas tertinggi */
            button[type="submit"] {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                background-color: #007bff;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                margin: 10px 0;
                position: relative !important;
                z-index: 9999 !important;
            }

            button[type="submit"]:hover {
                background-color: #0056b3;
            }

            /* Pastikan parent container dari submit button juga terlihat */
            button[type="submit"]:parent,
            .form-group:has(button[type="submit"]) {
                display: block !important;
                visibility: visible !important;
            }
        </style>

        <!-- Komentar sudah dipindahkan ke bagian revisi -->
        <button type="submit">Submit</button>
    </form>
    <button><a style="color: white; text-decoration: none;" href="dashboard.php">Kembali ke Daftar Pengajuan</a></button>
    </div>
</div>
        </div>
    </div>
</div>
<?php include '../config/footer.php'; ?>

<script>
    document.querySelector('form').addEventListener('submit', function() {
        document.getElementById('loading').style.display = 'block';
    });
    function toggleDateFields() {
    const multiDaySelect = document.getElementById('multi_day_select');
    const singleDayField = document.getElementById('single_day_field');
    const multiDayFields = document.getElementById('multi_day_fields');
    const trainingDateFixed = document.getElementById('fixed_date'); // Changed from start_date
    const trainingDateStart = document.getElementById('start_date');
    const trainingDateEnd = document.getElementById('end_date');

    // Set today's date as min for date inputs
    const today = new Date().toISOString().split('T')[0];

    // Hide all fields initially
    if (singleDayField) singleDayField.style.display = "none";
    if (multiDayFields) multiDayFields.style.display = "none";

    // Remove required attributes
    if (trainingDateFixed) trainingDateFixed.removeAttribute("required");
    if (trainingDateStart) trainingDateStart.removeAttribute("required");
    if (trainingDateEnd) trainingDateEnd.removeAttribute("required");

    // Clear all date inputs
    if (trainingDateFixed) trainingDateFixed.value = "";
    if (trainingDateStart) trainingDateStart.value = "";
    if (trainingDateEnd) trainingDateEnd.value = "";

    // Show fields based on dropdown selection
    if (multiDaySelect && multiDaySelect.value === "Ya") {
        if (multiDayFields) {
            multiDayFields.style.display = "block";
            if (trainingDateStart) {
                trainingDateStart.setAttribute("required", "required");
                trainingDateStart.setAttribute("min", today); // Prevent past dates
            }
            if (trainingDateEnd) {
                trainingDateEnd.setAttribute("required", "required");
                trainingDateEnd.setAttribute("min", today); // Prevent past dates
            }
        }
    } else if (multiDaySelect && multiDaySelect.value === "Tidak") {
        if (singleDayField) {
            singleDayField.style.display = "block";
            if (trainingDateFixed) {
                trainingDateFixed.setAttribute("required", "required");
                trainingDateFixed.setAttribute("min", today); // Prevent past dates
            }
        }
    }
}

document.addEventListener('DOMContentLoaded', function () {
    const trainingDateStart = document.getElementById('start_date');
    const trainingDateEnd = document.getElementById('end_date');

    // Real-time validation for multi-day dates
    if (trainingDateStart && trainingDateEnd) {
        trainingDateStart.addEventListener('change', function () {
            const startDate = new Date(trainingDateStart.value);
            const endDate = new Date(trainingDateEnd.value);

            if (!isNaN(startDate)) {
                trainingDateEnd.setAttribute('min', trainingDateStart.value);
                if (!isNaN(endDate) && endDate < startDate) {
                    // Allow flexible date selection - just show console warning
                    console.warn("Note: End date is before start date");
                    // Don't reset the value for flexibility
                }
            }
        });

        trainingDateEnd.addEventListener('change', function () {
            const startDate = new Date(trainingDateStart.value);
            const endDate = new Date(trainingDateEnd.value);

            if (!isNaN(startDate) && !isNaN(endDate) && endDate < startDate) {
                alert("Tanggal berakhir harus lebih besar dari tanggal mulai!");
                trainingDateEnd.value = "";
            }
        });
    }

    // Call toggle on page load
    toggleDateFields();
});

// Form submission validation
const trainingForm = document.querySelector('#training_form');
if (trainingForm) {
    trainingForm.addEventListener('submit', function(e) {
    const statusSelect = document.getElementById('status_select');
    const multiDaySelect = document.getElementById('multi_day_select');
    const trainingDateFixed = document.getElementById('fixed_date'); // Changed from start_date
    const trainingDateStart = document.getElementById('start_date');
    const trainingDateEnd = document.getElementById('end_date');
    const providerSelect = document.getElementById('provider_type_select');
    const trainerList = document.getElementById('trainer_list');

    // Skip validation for Rejected or Revise status
    if (statusSelect && (statusSelect.value === "Rejected" || statusSelect.value === "Revise")) {
        return;
    }

    // Validate provider type (from previous context)
    if (!providerSelect || providerSelect.value === "") {
        e.preventDefault();
        alert("Silakan pilih jenis provider terlebih dahulu.");
        return;
    }

    // Validate internal provider trainer list
    if (providerSelect && providerSelect.value === "Internal" && trainerList && trainerList.children.length === 0) {
        e.preventDefault();
        alert("Silakan tambahkan setidaknya satu trainer untuk provider internal.");
        return;
    }

    // Validate duration dropdown
    if (!multiDaySelect || multiDaySelect.value === "") {
        e.preventDefault();
        alert("Silakan pilih durasi training terlebih dahulu.");
        return;
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Validate multi-day training
    if (multiDaySelect.value === "Ya") {
        if (!trainingDateStart.value || !trainingDateEnd.value) {
            e.preventDefault();
            alert("Tanggal mulai dan tanggal berakhir harus diisi untuk training lebih dari satu hari.");
            return;
        }

        const startDate = new Date(trainingDateStart.value);
        const endDate = new Date(trainingDateEnd.value);

        if (isNaN(startDate) || isNaN(endDate)) {
            e.preventDefault();
            alert("Tanggal mulai atau tanggal berakhir tidak valid.");
            return;
        }

        if (startDate > endDate) {
            // Allow flexible date selection - just show console warning
            console.warn("Note: End date is before start date");
            // Don't block submission for flexibility
        }

        if (startDate < today) {
            e.preventDefault();
            alert("Tanggal mulai tidak boleh di masa lalu!");
            return;
        }
    } else if (multiDaySelect.value === "Tidak") {
        if (!trainingDateFixed.value) {
            e.preventDefault();
            alert("Tanggal pelaksanaan harus diisi untuk training satu hari.");
            return;
        }

        const fixedDate = new Date(trainingDateFixed.value);
        if (isNaN(fixedDate)) {
            e.preventDefault();
            alert("Tanggal pelaksanaan tidak valid.");
            return;
        }

        if (fixedDate < today) {
            e.preventDefault();
            alert("Tanggal pelaksanaan tidak boleh di masa lalu!");
            return;
        }
    }
    });
}
// Global array to store trainers
let trainers = [];

// Define all trainer-related functions in global scope
function addTrainer(trainer) {
    if (trainers.some(t => t.nik === trainer.nik)) {
        alert('Trainer ini sudah ditambahkan!');
        return;
    }
    trainers.push(trainer);
    updateTrainerTable();
    document.getElementById('trainer_results').innerHTML = '';
    document.getElementById('search_trainer').value = '';
}

function addTrainerManual() {
    let tbody = document.getElementById("trainer_list");
    let index = tbody.getElementsByTagName("tr").length + 1;
    let row = document.createElement("tr");
    row.innerHTML = `
        <td>${index}</td>
        <td><input type="text" name="trainer_name_internal[]" required></td>
        <td><input type="text" name="trainer_nik_internal[]" required></td>
        <td><input type="text" name="trainer_department_internal[]" required></td>
        <td><input type="text" name="trainer_sub_department_internal[]" required></td>
        <td><input type="text" name="trainer_position_internal[]" required></td>
        <td><button type="button" onclick="removeTrainer(this)">Hapus</button></td>
    `;
    tbody.appendChild(row);
}

function removeTrainer(button) {
    if (typeof button === "number") {
        trainers.splice(button, 1);
        updateTrainerTable();
    } else {
        const row = button.closest('tr');
        if (row) {
            const index = Array.from(row.parentElement.children).indexOf(row);
            trainers.splice(index, 1);
            row.remove();
            updateTrainerTable();
        }
    }
}

function updateTrainerTable() {
    const tbody = document.getElementById('trainer_list');
    tbody.innerHTML = '';
    trainers.forEach((trainer, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${trainer.nama}</td>
            <td>${trainer.nik}</td>
            <td>${trainer.departemen}</td>
            <td>${trainer.bagian}</td>
            <td>${trainer.jabatan}</td>
            <td><button type="button" onclick="removeTrainer(${index})">Hapus</button></td>
        `;
        tbody.appendChild(row);
    });
    updateHiddenInputs(); // Call to ensure hidden inputs are updated
}

function updateHiddenInputs() {
    const form = document.querySelector('form');
    // Remove only hidden inputs that are not in the table (to avoid removing manual inputs)
    const existingHiddenInputs = form.querySelectorAll('input[type="hidden"][name^="trainer_"]');
    existingHiddenInputs.forEach(input => {
        // Only remove if it's not inside the trainer table
        if (!input.closest('#trainer_table')) {
            input.remove();
        }
    });

    // Create new hidden inputs with correct names for search-added trainers
    trainers.forEach((trainer, index) => {
        // Create individual hidden inputs for each trainer
        const nameInput = document.createElement('input');
        nameInput.type = 'hidden';
        nameInput.name = 'trainer_name_internal[]';
        nameInput.value = trainer.nama;
        nameInput.className = 'search-trainer-input';
        form.appendChild(nameInput);

        const nikInput = document.createElement('input');
        nikInput.type = 'hidden';
        nikInput.name = 'trainer_nik_internal[]';
        nikInput.value = trainer.nik;
        nikInput.className = 'search-trainer-input';
        form.appendChild(nikInput);

        const deptInput = document.createElement('input');
        deptInput.type = 'hidden';
        deptInput.name = 'trainer_department_internal[]';
        deptInput.value = trainer.departemen;
        deptInput.className = 'search-trainer-input';
        form.appendChild(deptInput);

        const subDeptInput = document.createElement('input');
        subDeptInput.type = 'hidden';
        subDeptInput.name = 'trainer_sub_department_internal[]';
        subDeptInput.value = trainer.bagian;
        subDeptInput.className = 'search-trainer-input';
        form.appendChild(subDeptInput);

        const posInput = document.createElement('input');
        posInput.type = 'hidden';
        posInput.name = 'trainer_position_internal[]';
        posInput.value = trainer.jabatan;
        posInput.className = 'search-trainer-input';
        form.appendChild(posInput);
    });
}

function updateTrainingProvider() {
    const providerSelect = document.getElementById("provider_type_select");
    const internalFields = document.getElementById("internalFields");
    const externalFields = document.getElementById("externalFields");
    const providerName = document.getElementsByName("provider_name")[0];
    const providerAddress = document.getElementsByName("provider_address")[0];
    const trainerNameExternal = document.getElementsByName("trainer_name_external")[0];
    const searchTrainer = document.getElementById("search_trainer");
    const trainerList = document.getElementById("trainer_list");
    const trainerResults = document.getElementById("trainer_results");

    if (providerSelect.value === "Internal") {
        internalFields.style.display = "block";
        externalFields.style.display = "none";
        providerName.removeAttribute("required");
        providerAddress.removeAttribute("required");
        trainerNameExternal.removeAttribute("required");
        providerName.value = "";
        providerAddress.value = "";
        trainerNameExternal.value = "";
    } else if (providerSelect.value === "External") {
        internalFields.style.display = "none";
        externalFields.style.display = "block";
        providerName.setAttribute("required", "required");
        providerAddress.setAttribute("required", "required");
        trainerNameExternal.removeAttribute("required");
        searchTrainer.value = "";
        trainerList.innerHTML = "";
        trainerResults.innerHTML = "";
        trainers = []; // Clear trainers array
        updateHiddenInputs();
    }
}

function toggleDateFields() {
    const multiDaySelect = document.getElementById('multi_day_select');
    const singleDateField = document.getElementById('single_date_field');
    const multiDateFields = document.getElementById('multi_date_fields');
    const trainingDateFixed = document.getElementById('training_date');
    const trainingDateStart = document.getElementById('training_start_date');
    const trainingDateEnd = document.getElementById('training_end_date');
    const today = new Date().toISOString().split('T')[0];

    if (!multiDaySelect || !singleDateField || !multiDateFields) {
        console.error('Required elements not found for toggleDateFields');
        return;
    }

    singleDateField.style.display = "none";
    multiDateFields.style.display = "none";
    if (trainingDateFixed) trainingDateFixed.removeAttribute("required");
    if (trainingDateStart) trainingDateStart.removeAttribute("required");
    if (trainingDateEnd) trainingDateEnd.removeAttribute("required");
    if (trainingDateFixed) trainingDateFixed.value = "";
    if (trainingDateStart) trainingDateStart.value = "";
    if (trainingDateEnd) trainingDateEnd.value = "";

    if (multiDaySelect.value === "Ya") {
        multiDateFields.style.display = "block";
        if (trainingDateStart) {
            trainingDateStart.setAttribute("required", "required");
            trainingDateStart.setAttribute("min", today);
        }
        if (trainingDateEnd) {
            trainingDateEnd.setAttribute("required", "required");
            trainingDateEnd.setAttribute("min", today);
        }
    } else if (multiDaySelect.value === "Tidak") {
        singleDateField.style.display = "block";
        if (trainingDateFixed) {
            trainingDateFixed.setAttribute("required", "required");
            trainingDateFixed.setAttribute("min", today);
        }
    }
}

document.addEventListener('DOMContentLoaded', function () {
    const trainingDateStart = document.getElementById('training_start_date');
    const trainingDateEnd = document.getElementById('training_end_date');

    if (trainingDateStart && trainingDateEnd) {
        trainingDateStart.addEventListener('change', function () {
            const startDate = new Date(trainingDateStart.value);
            const endDate = new Date(trainingDateEnd.value);
            if (!isNaN(startDate)) {
                trainingDateEnd.setAttribute('min', trainingDateStart.value);
                if (!isNaN(endDate) && endDate < startDate) {
                    alert("Tanggal berakhir tidak boleh lebih awal dari tanggal mulai.");
                    trainingDateEnd.value = "";
                }
            }
        });

        trainingDateEnd.addEventListener('change', function () {
            const startDate = new Date(trainingDateStart.value);
            const endDate = new Date(trainingDateEnd.value);
            if (!isNaN(startDate) && !isNaN(endDate) && endDate < startDate) {
                // Allow flexible date selection - just show console warning
                console.warn("Note: End date is before start date");
                // Don't reset the value for flexibility
            }
        });
    }

    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            updateHiddenInputs(); // Ensure trainer data is included
            const statusSelect = document.getElementById('status_select');
            const multiDaySelect = document.getElementById('multi_day_select');
            const trainingDateFixed = document.getElementById('training_date');
            const trainingDateStart = document.getElementById('training_start_date');
            const trainingDateEnd = document.getElementById('training_end_date');
            const providerSelect = document.getElementById('provider_type_select');
            const trainerList = document.getElementById('trainer_list');

            if (statusSelect && (statusSelect.value === "Rejected" || statusSelect.value === "Revise")) {
                return; // Skip validation for Rejected or Revise
            }

            if (!providerSelect || providerSelect.value === "") {
                e.preventDefault();
                alert("Silakan pilih jenis provider terlebih dahulu.");
                return;
            }

            if (providerSelect.value === "Internal" && trainerList && trainerList.children.length === 0) {
                e.preventDefault();
                alert("Silakan tambahkan setidaknya satu trainer untuk provider internal.");
                return;
            }

            if (!multiDaySelect || multiDaySelect.value === "") {
                e.preventDefault();
                alert("Silakan pilih durasi training terlebih dahulu.");
                return;
            }

            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (multiDaySelect.value === "Ya") {
                if (!trainingDateStart.value || !trainingDateEnd.value) {
                    e.preventDefault();
                    alert("Tanggal mulai dan tanggal berakhir harus diisi untuk training lebih dari satu hari.");
                    return;
                }
                const startDate = new Date(trainingDateStart.value);
                const endDate = new Date(trainingDateEnd.value);
                if (isNaN(startDate) || isNaN(endDate)) {
                    e.preventDefault();
                    alert("Tanggal mulai atau tanggal berakhir tidak valid.");
                    return;
                }
                if (startDate > endDate) {
                    e.preventDefault();
                    alert("Tanggal berakhir harus lebih besar dari tanggal mulai!");
                    return;
                }
                if (startDate < today) {
                    e.preventDefault();
                    alert("Tanggal mulai tidak boleh di masa lalu!");
                    return;
                }
            } else if (multiDaySelect.value === "Tidak") {
                if (!trainingDateFixed.value) {
                    e.preventDefault();
                    alert("Tanggal pelaksanaan harus diisi untuk training satu hari.");
                    return;
                }
                const fixedDate = new Date(trainingDateFixed.value);
                if (isNaN(fixedDate)) {
                    e.preventDefault();
                    alert("Tanggal pelaksanaan tidak valid.");
                    return;
                }
                if (fixedDate < today) {
                    e.preventDefault();
                    alert("Tanggal pelaksanaan tidak boleh di masa lalu!");
                    return;
                }
            }
        });
    }

    updateTrainingProvider();
    toggleDateFields();
    toggleFormFields();

    const imageInput = document.getElementById("internal_memo_image");
    const imagePreview = document.getElementById("image_preview");
    const previewImg = document.getElementById("preview_img");
    const removeImageBtn = document.getElementById("remove_image");

    if (imageInput) {
        imageInput.addEventListener("change", function() {
            if (this.files && this.files[0]) {
                if (this.files[0].size > 2 * 1024 * 1024) {
                    alert("Ukuran file terlalu besar. Maksimal 2MB.");
                    this.value = "";
                    imagePreview.style.display = "none";
                    return;
                }
                const fileType = this.files[0].type;
                if (fileType !== "image/jpeg" && fileType !== "image/jpg" && fileType !== "image/png" && fileType !== "application/pdf") {
                    alert("Format file tidak valid. Hanya file JPG, JPEG, PNG, dan PDF yang diperbolehkan.");
                    this.value = "";
                    imagePreview.style.display = "none";
                    return;
                }
                if (fileType === "application/pdf") {
                    imagePreview.style.display = "none";
                    return;
                }
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    imagePreview.style.display = "block";
                };
                reader.readAsDataURL(this.files[0]);
            }
        });

        if (removeImageBtn) {
            removeImageBtn.addEventListener("click", function() {
                imageInput.value = "";
                imagePreview.style.display = "none";
            });
        }
    }

    const startTimePicker = flatpickr("#training_time_start_input", {
        enableTime: true,
        noCalendar: true,
        dateFormat: "H:i",
        time_24hr: true,
        minuteIncrement: 15,
        defaultHour: 8,
        defaultMinute: 0,
        clickOpens: true,
        allowInput: true
    });

    const endTimePicker = flatpickr("#training_time_end_input", {
        enableTime: true,
        noCalendar: true,
        dateFormat: "H:i",
        time_24hr: true,
        minuteIncrement: 15,
        defaultHour: 17,
        defaultMinute: 0,
        clickOpens: true,
        allowInput: true
    });

    try {
        const singleDateElement = document.getElementById("training_date");
        if (singleDateElement) {
            flatpickr("#training_date", {
                dateFormat: "Y-m-d",
                minDate: "today",
                allowInput: true
            });
        }

        const startDateElement = document.getElementById("training_start_date");
        if (startDateElement) {
            flatpickr("#training_start_date", {
                dateFormat: "Y-m-d",
                minDate: "today",
                allowInput: true
            });
        }

        const endDateElement = document.getElementById("training_end_date");
        if (endDateElement) {
            flatpickr("#training_end_date", {
                dateFormat: "Y-m-d",
                minDate: "today",
                allowInput: true
            });
        }
    } catch (error) {
        console.error('Flatpickr initialization error:', error);
    }
});

    // Helper function to validate date
    function isValidDate(dateString) {
        const date = new Date(dateString);
        return date instanceof Date && !isNaN(date) && dateString.match(/^\d{4}-\d{2}-\d{2}$/);
    }

    // Call toggleDateFields when the page loads to initialize the date fields
    toggleDateFields();

// GLOBAL FUNCTIONS - Dipindahkan ke luar DOMContentLoaded agar bisa diakses dari HTML

// IMPROVED: Function to toggle date fields based on multi-day selection
function toggleDateFields() {
    const multiDaySelect = document.getElementById('multi_day_select');
    const singleDateField = document.getElementById('single_date_field');
    const multiDateFields = document.getElementById('multi_date_fields');

    console.log('toggleDateFields called, value:', multiDaySelect ? multiDaySelect.value : 'null');

    if (!multiDaySelect || !singleDateField || !multiDateFields) {
        console.error('Required elements not found for toggleDateFields');
        return;
    }

    // Get all date input elements
    const singleDateInput = document.getElementById('training_date');
    const startDateInput = document.getElementById('training_start_date');
    const endDateInput = document.getElementById('training_end_date');

    if (multiDaySelect.value === 'Tidak') {
        // Check if multi-day inputs have values and confirm before clearing
        const hasMultiDayValues = (startDateInput && startDateInput.value) || (endDateInput && endDateInput.value);

        if (hasMultiDayValues) {
            // Use confirmAction with proper callbacks for custom-modal.js
            confirmAction(
                'Anda akan beralih ke mode "Training 1 Hari".<br><br>Tanggal yang sudah diisi untuk training multi-hari akan dihapus.<br><br>Lanjutkan?',
                function() {
                    // User clicked "Ya" - proceed with clearing
                    console.log('User confirmed switch to single day mode');

                    // Show single day field, hide multi-day fields
                    singleDateField.style.display = 'block';
                    multiDateFields.style.display = 'none';

                    // CLEAR multi-day inputs when switching to single day
                    if (startDateInput) {
                        startDateInput.value = '';
                        startDateInput.removeAttribute('required');
                        console.log('Cleared multi-day start date input');
                    }
                    if (endDateInput) {
                        endDateInput.value = '';
                        endDateInput.removeAttribute('required');
                        console.log('Cleared multi-day end date input');
                    }

                    // Set required for single date
                    if (singleDateInput) {
                        singleDateInput.setAttribute('required', 'required');
                    }

                    // Add visual feedback
                    addVisualFeedback();

                    console.log('Single day mode activated - multi-day inputs cleared');
                },
                function() {
                    // User clicked "Tidak" - revert dropdown
                    console.log('User cancelled switch to single day mode');
                    multiDaySelect.value = 'Ya';
                },
                'Konfirmasi Perubahan Mode Training'
            );
            return; // Exit function, let confirmAction handle the rest
        }

        // No data to lose, proceed directly
        singleDateField.style.display = 'block';
        multiDateFields.style.display = 'none';

        // Set required for single date
        if (singleDateInput) {
            singleDateInput.setAttribute('required', 'required');
        }
        if (startDateInput) startDateInput.removeAttribute('required');
        if (endDateInput) endDateInput.removeAttribute('required');

        // Add visual feedback
        addVisualFeedback();

        console.log('Single day mode activated - no data to clear');

    } else if (multiDaySelect.value === 'Ya') {
        // Check if single day input has value and confirm before clearing
        const hasSingleDayValue = singleDateInput && singleDateInput.value;

        if (hasSingleDayValue) {
            // Use confirmAction with proper callbacks for custom-modal.js
            confirmAction(
                'Anda akan beralih ke mode "Training Multi-Hari".<br><br>Tanggal yang sudah diisi untuk training 1 hari akan dihapus.<br><br>Lanjutkan?',
                function() {
                    // User clicked "Ya" - proceed with clearing
                    console.log('User confirmed switch to multi-day mode');

                    // Show multi-day fields, hide single day field
                    singleDateField.style.display = 'none';
                    multiDateFields.style.display = 'block';

                    // CLEAR single day input when switching to multi-day
                    if (singleDateInput) {
                        singleDateInput.value = '';
                        singleDateInput.removeAttribute('required');
                        console.log('Cleared single day input');
                    }

                    // Set required for multi-day dates
                    if (startDateInput) startDateInput.setAttribute('required', 'required');
                    if (endDateInput) endDateInput.setAttribute('required', 'required');

                    // Add visual feedback
                    addVisualFeedback();

                    console.log('Multi-day mode activated - single day input cleared');
                },
                function() {
                    // User clicked "Tidak" - revert dropdown
                    console.log('User cancelled switch to multi-day mode');
                    multiDaySelect.value = 'Tidak';
                },
                'Konfirmasi Perubahan Mode Training'
            );
            return; // Exit function, let confirmAction handle the rest
        }

        // No data to lose, proceed directly
        singleDateField.style.display = 'none';
        multiDateFields.style.display = 'block';

        // Set required for multi-day dates
        if (startDateInput) startDateInput.setAttribute('required', 'required');
        if (endDateInput) endDateInput.setAttribute('required', 'required');
        if (singleDateInput) singleDateInput.removeAttribute('required');

        // Add visual feedback
        addVisualFeedback();

        console.log('Multi-day mode activated - no data to clear');

    } else {
        // Hide both if no selection
        singleDateField.style.display = 'none';
        multiDateFields.style.display = 'none';

        // CLEAR ALL inputs when no selection
        if (singleDateInput) {
            singleDateInput.value = '';
            singleDateInput.removeAttribute('required');
            console.log('Cleared single day input');
        }
        if (startDateInput) {
            startDateInput.value = '';
            startDateInput.removeAttribute('required');
            console.log('Cleared multi-day start date input');
        }
        if (endDateInput) {
            endDateInput.value = '';
            endDateInput.removeAttribute('required');
            console.log('Cleared multi-day end date input');
        }

        console.log('No selection - all fields hidden and cleared');
    }

    // Helper function for visual feedback
    function addVisualFeedback() {
        const container = document.getElementById('date_fields_container');
        if (container) {
            // Add a subtle animation effect
            container.style.transition = 'opacity 0.3s ease';
            container.style.opacity = '0.7';
            setTimeout(() => {
                container.style.opacity = '1';
            }, 150);
        }
    }

    // Add visual feedback for user (fallback for direct calls)
    addVisualFeedback();
}

function toggleFormFields() {
        const statusSelect = document.getElementById('status_select');
        const commentsSection = document.getElementById('comments_section');
        const commentsHeading = document.getElementById('comments_heading');
        const commentsHelp = document.getElementById('comments_help');
        const approvalFieldsSection = document.getElementById('approval_fields_section');

        // Cek apakah status adalah Revise atau Rejected
        const isReviseOrRejected = statusSelect.value === 'Revise' || statusSelect.value === 'Rejected';

        if (isReviseOrRejected) {
            // Jika status Revise atau Rejected, tampilkan hanya kolom komentar
            if (commentsSection) commentsSection.style.display = 'block';
            if (approvalFieldsSection) approvalFieldsSection.style.display = 'none';

            // Sesuaikan teks berdasarkan status
            if (statusSelect.value === 'Revise') {
                if (commentsHeading) commentsHeading.textContent = 'Komentar Revisi';
                if (commentsHelp) commentsHelp.textContent = 'Berikan alasan dan petunjuk yang jelas untuk revisi yang diperlukan';
            } else {
                if (commentsHeading) commentsHeading.textContent = 'Alasan Penolakan';
                if (commentsHelp) commentsHelp.textContent = 'Berikan alasan yang jelas mengapa pengajuan ini ditolak';
            }

            // Hapus atribut required dari field yang disembunyikan
            const requiredFields = approvalFieldsSection.querySelectorAll('[required]');
            requiredFields.forEach(field => {
                field.removeAttribute('required');
                field.dataset.wasRequired = 'true'; // Simpan informasi bahwa field ini sebelumnya required
            });
        } else {
            // Jika status Approved, tampilkan semua field
            if (commentsSection) commentsSection.style.display = 'block';
            if (approvalFieldsSection) approvalFieldsSection.style.display = 'block';

            // Sesuaikan teks untuk komentar
            if (commentsHeading) commentsHeading.textContent = 'Komentar (Opsional)';
            if (commentsHelp) commentsHelp.textContent = 'Berikan komentar tambahan jika diperlukan';

            // Kembalikan atribut required ke field yang sebelumnya required
            const previouslyRequiredFields = approvalFieldsSection.querySelectorAll('[data-was-required="true"]');
            previouslyRequiredFields.forEach(field => {
                field.setAttribute('required', 'required');
            });
        }
    }

    // Panggil fungsi saat halaman dimuat untuk mengatur tampilan awal
    toggleFormFields();

    // Event listener untuk search trainer
    const searchTrainerElement = document.getElementById('search_trainer');
    if (searchTrainerElement) {
        searchTrainerElement.addEventListener('input', function(e) {
        const query = this.value.trim();
        if (query.length < 3) {
            document.getElementById('trainer_results').innerHTML = '';
            return;
        }

        fetch(`search_employee.php?query=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('trainer_results');
                resultsDiv.innerHTML = '';
                data.forEach(employee => {
                    const div = document.createElement('div');
                    div.className = 'trainer-result';
                    div.innerHTML = `${employee.nama} (${employee.nik}) - ${employee.dept}`;
                    div.onclick = () => addTrainer({
                        nik: employee.nik,
                        nama: employee.nama,
                        departemen: employee.dept,
                        bagian: employee.bagian,
                        jabatan: employee.jabatan
                    });
                    resultsDiv.appendChild(div);
                });
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('trainer_results').innerHTML =
                    '<div class="error">Terjadi kesalahan saat mencari karyawan</div>';
            });
        });
    }

    // Toggle fields berdasarkan status
    function toggleFields() {
        // Define variables inside function to avoid ReferenceError
        const statusSelect = document.getElementById("status_select");
        const formGroups = document.querySelectorAll(".form-group");
        const commentsTextArea = document.getElementById("comments");
        const submitButton = document.querySelector("button[type='submit']");
        const trainingDateSection = document.getElementById('start_date_section');

        if (!statusSelect) return; // Exit if statusSelect not found

        if (statusSelect.value === "Rejected" || statusSelect.value === "Revise") {
            console.log("Status Rejected/Revise - Hiding all training details");

            // APPROACH: Sembunyikan SEMUA elements, lalu tampilkan hanya yang diperlukan

            // 1. Sembunyikan SEMUA form groups terlebih dahulu
            formGroups.forEach(group => {
                group.style.display = "none";
                const inputs = group.querySelectorAll("input, select, textarea");
                inputs.forEach(input => input.removeAttribute("required"));
            });

            // 2. Sembunyikan SEMUA h3 headers
            const allH3 = document.querySelectorAll('h3');
            allH3.forEach(h3 => {
                h3.style.display = "none";
            });

            // 3. Sembunyikan SEMUA divs dan elements yang mungkin terlewat
            const hideSelectors = [
                '.alert-info',                 // Estimasi dari pemohon
                '.mb-3',                       // Durasi Training section
                '#multi_day_select',           // Durasi Training dropdown
                '#date_fields_container',      // Date fields container
                '#single_date_field',          // Single date field
                '#multi_date_fields',          // Multi date fields
                '.time-section',               // Time section
                '.form-group:has(input[name="training_place"])',  // Tempat training
                '.form-group:has(input[name="training_cost"])',   // Biaya training
                '.form-group:has(input[name="contact_person"])',  // Contact person
                '.form-group:has(input[name="contact_number"])',  // Contact number
                '.form-group:has(select[name="sharing_knowledge"])', // Sharing knowledge
                '.form-group:has(input[name="internal_memo_image"])'  // Internal memo
            ];

            hideSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element) {
                        element.style.display = "none";
                    }
                });
            });

            // 4. HANYA tampilkan elements yang benar-benar diperlukan

            // Tampilkan form group yang berisi status select
            const statusGroup = statusSelect.closest('.form-group');
            if (statusGroup) {
                statusGroup.style.display = "block";
                console.log("Status group displayed");
            }

            // Tampilkan form group yang berisi comments textarea
            const commentsGroup = commentsTextArea.closest('.form-group');
            if (commentsGroup) {
                commentsGroup.style.display = "block";
                console.log("Comments group displayed");
            }

            // Pastikan submit button dan parent-nya terlihat
            if (submitButton) {
                submitButton.style.display = "block";
                let parent = submitButton.parentElement;
                while (parent && parent !== document.body) {
                    parent.style.display = "block";
                    parent = parent.parentElement;
                }
                console.log("Submit button and parents displayed");
            }

            // Pastikan form status tetap terlihat
            if (statusGroup) {
                statusGroup.style.display = "block";
                console.log("Status group displayed");
            }
        } else {
            console.log("Status Approved - Showing all training details");

            // Jika status Approved, tampilkan SEMUA elements kembali

            // 1. Tampilkan semua form groups
            formGroups.forEach(group => {
                group.style.display = "block";
                let inputs = group.querySelectorAll("input, select, textarea");
                inputs.forEach(input => {
                    if (input.hasAttribute("data-original-required")) {
                        input.setAttribute("required", "required");
                    }
                });
            });

            // 2. Tampilkan semua h3 headers
            const allH3 = document.querySelectorAll('h3');
            allH3.forEach(h3 => {
                h3.style.display = "block";
            });

            // 3. Tampilkan semua elements yang mungkin disembunyikan
            const showSelectors = [
                '.alert-info',                 // Estimasi dari pemohon
                '.mb-3',                       // Durasi Training section
                '#multi_day_select',           // Durasi Training dropdown
                '#date_fields_container',      // Date fields container
                '#single_date_field',          // Single date field
                '#multi_date_fields',          // Multi date fields
                '.time-section'                // Time section
            ];

            showSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element) {
                        element.style.display = "block";
                        // Restore required attributes
                        const inputs = element.querySelectorAll("input, select, textarea");
                        inputs.forEach(input => {
                            if (input.hasAttribute("data-original-required")) {
                                input.setAttribute("required", "required");
                            }
                        });
                    }
                });
            });

            console.log("All elements restored for Approved status");
        }

        // Pastikan textarea komentar selalu terlihat
        const commentsGroup = commentsTextArea.closest('.form-group');
        if (commentsGroup) {
            commentsGroup.style.display = "block";
        }

        // Pastikan tombol submit dan parent container selalu terlihat
        if (submitButton) {
            // Pastikan tombol submit terlihat
            submitButton.style.display = "block";
            submitButton.style.visibility = "visible";
            submitButton.style.opacity = "1";
            submitButton.style.position = "relative";
            submitButton.style.zIndex = "9999";

            // Pastikan parent container juga terlihat
            let parent = submitButton.parentElement;
            while (parent && parent !== document.body) {
                parent.style.display = "block";
                parent.style.visibility = "visible";
                parent = parent.parentElement;
            }

            console.log("Submit button and parents visibility ensured in toggleFields()");
        } else {
            console.error("Submit button not found in toggleFields()!");
        }
    }

    document.querySelectorAll("input, select, textarea").forEach(input => {
        if (input.hasAttribute("required")) {
            input.setAttribute("data-original-required", "true");
        }
    });

    // Add event listener with proper variable definition
    const statusSelectElement = document.getElementById("status_select");
    if (statusSelectElement) {
        statusSelectElement.addEventListener("change", function() {
            console.log("Status changed to:", statusSelectElement.value);
            toggleFields();
        });
        toggleFields(); // Call on page load
    } else {
        console.error("Status select element not found!");
    }

    // SIMPLIFIED VALIDATION - Hanya validasi yang essential
    const form = document.querySelector("form");
    if (form) {
        form.addEventListener("submit", function(e) {
            const statusSelect = document.getElementById("status_select");
            const commentsField = document.getElementById("comments");

            console.log("Form submit - Status:", statusSelect ? statusSelect.value : "null");

            // EMERGENCY FIX: Remove required dari semua hidden fields sebelum submit
            const hiddenRequiredFields = form.querySelectorAll('input[required], select[required], textarea[required]');
            hiddenRequiredFields.forEach(field => {
                const fieldGroup = field.closest('.form-group');
                if (fieldGroup && fieldGroup.style.display === 'none') {
                    field.removeAttribute('required');
                    console.log('Removed required from hidden field:', field.name);
                }
            });

            // Validasi komentar wajib untuk status Revise
            if (statusSelect && statusSelect.value === "Revise") {
                console.log("Status Revise - Checking comments");
                if (!commentsField || !commentsField.value.trim()) {
                    e.preventDefault();
                    alert("Komentar wajib diisi untuk status Revise. Berikan alasan dan petunjuk yang jelas untuk revisi yang diperlukan.");
                    if (commentsField) commentsField.focus();
                    return;
                }
                console.log("Status Revise - Comments OK, submitting");
                return; // Tidak perlu validasi lainnya untuk Revise
            }

            // Jika status adalah Rejected, lewati validasi training details
            if (statusSelect && statusSelect.value === "Rejected") {
                console.log("Status Rejected - No validation needed");
                return; // Tidak perlu validasi lainnya
            }

            // Untuk status Approved, biarkan form submit tanpa validasi JavaScript
            // Validasi akan dilakukan di server-side
            if (statusSelect && statusSelect.value === "Approved") {
                console.log("Status Approved - Allowing form submit");
                return; // Biarkan form submit
            }

            console.log("Unknown status or no status selected");
        });
    }

    // Update required attributes based on status
    const statusSelectForRequired = document.getElementById("status_select");
    if (statusSelectForRequired) {
        statusSelectForRequired.addEventListener("change", function() {
            const isApproved = this.value === "Approved";
            const requiredFields = [
                "start_date",
                "start_date",
                "end_date",
                "training_time_start_input",
                "training_time_end_input",
                "training_place",
                "training_cost",
                "contact_person",
                "contact_number"
            ];

            requiredFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    if (isApproved) {
                        element.setAttribute("required", "required");
                    } else {
                        element.removeAttribute("required");
                    }
                }
            });
        });
    }
// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    toggleFields(); // Fix: gunakan nama function yang benar
    updateTrainingProvider(); // Ensure provider type is set correctly on page load

    // Pastikan tombol submit dan parent container selalu terlihat
    const submitButton = document.querySelector("button[type='submit']");
    if (submitButton) {
        // Pastikan tombol submit terlihat
        submitButton.style.display = "block";
        submitButton.style.visibility = "visible";
        submitButton.style.opacity = "1";
        submitButton.style.position = "relative";
        submitButton.style.zIndex = "9999";

        // Pastikan parent container juga terlihat
        let parent = submitButton.parentElement;
        while (parent && parent !== document.body) {
            parent.style.display = "block";
            parent.style.visibility = "visible";
            parent = parent.parentElement;
        }

        console.log("Submit button and parents made visible on page load");
    } else {
        console.error("Submit button not found on page load!");
    }
});
</script>
</body>
</html>
