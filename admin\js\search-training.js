/**
 * JavaScript untuk Search Training Dropdown dengan Suggestions
 */

class TrainingSearchDropdown {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            placeholder: 'Cari training berda<PERSON><PERSON> judul, trainer, atau lokasi...',
            minLength: 2,
            maxResults: 10,
            debounceDelay: 300,
            showFilters: true,
            onSelect: null,
            ...options
        };

        this.currentQuery = '';
        this.currentType = 'all';
        this.selectedIndex = -1;
        this.suggestions = [];
        this.debounceTimer = null;
        this.isLoading = false;

        this.init();
    }

    init() {
        this.createHTML();
        this.bindEvents();
    }

    createHTML() {
        this.container.innerHTML = `
            <div class="search-training-container">
                <div class="position-relative">
                    <input type="text"
                           class="search-training-input"
                           placeholder="${this.options.placeholder}"
                           autocomplete="off">
                    <i class="fas fa-search search-icon"></i>
                    <div class="search-loading d-none">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>

                ${this.options.showFilters ? `
                <div class="search-filters">
                    <button type="button" class="filter-btn active" data-type="all">
                        <i class="fas fa-list"></i> Semua
                    </button>
                    <button type="button" class="filter-btn" data-type="offline">
                        <i class="fas fa-building"></i> Internal
                    </button>
                    <button type="button" class="filter-btn" data-type="online">
                        <i class="fas fa-laptop"></i> Eksternal
                    </button>
                </div>
                ` : ''}

                <div class="search-suggestions">
                    <!-- Suggestions will be populated here -->
                </div>
            </div>
        `;

        // Get elements
        this.input = this.container.querySelector('.search-training-input');
        this.searchIcon = this.container.querySelector('.search-icon');
        this.loadingSpinner = this.container.querySelector('.search-loading');
        this.suggestionsContainer = this.container.querySelector('.search-suggestions');
        this.filterButtons = this.container.querySelectorAll('.filter-btn');
    }

    bindEvents() {
        // Input events
        this.input.addEventListener('input', (e) => {
            this.handleInput(e.target.value);
        });

        this.input.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });

        this.input.addEventListener('focus', () => {
            if (this.suggestions.length > 0) {
                this.showSuggestions();
            }
        });

        // Filter button events
        this.filterButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleFilterChange(e.target.closest('.filter-btn').dataset.type);
            });
        });

        // Click outside to close
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.hideSuggestions();
            }
        });
    }

    handleInput(value) {
        this.currentQuery = value.trim();

        if (this.currentQuery.length < this.options.minLength) {
            this.hideSuggestions();
            return;
        }

        // Debounce search
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
            this.search();
        }, this.options.debounceDelay);
    }

    handleKeydown(e) {
        if (!this.suggestionsContainer.classList.contains('show')) {
            return;
        }

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.navigateDown();
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.navigateUp();
                break;
            case 'Enter':
                e.preventDefault();
                this.selectCurrent();
                break;
            case 'Escape':
                this.hideSuggestions();
                this.input.blur();
                break;
        }
    }

    handleFilterChange(type) {
        this.currentType = type;

        // Update active filter button
        this.filterButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.type === type);
        });

        // Re-search with new filter
        if (this.currentQuery.length >= this.options.minLength) {
            this.search();
        }
    }

    async search() {
        if (this.isLoading) return;

        this.setLoading(true);

        try {
            const params = new URLSearchParams({
                q: this.currentQuery,
                type: this.currentType,
                limit: this.options.maxResults
            });

            const response = await fetch(`api/search_training_suggestions.php?${params}`);

            // Check if response is successful
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Parse JSON response
            const data = await response.json();

            if (data.success) {
                this.suggestions = data.suggestions;
                this.renderSuggestions();
                this.showSuggestions();
            } else {
                console.error('Search error:', data.message);
                this.showError(data.message || 'Terjadi kesalahan saat mencari training');
            }
        } catch (error) {
            console.error('Search request failed:', error);
            this.showError('Terjadi kesalahan saat mencari training: ' + error.message);
        } finally {
            this.setLoading(false);
        }
    }

    renderSuggestions() {
        if (this.suggestions.length === 0) {
            this.suggestionsContainer.innerHTML = `
                <div class="no-suggestions">
                    <i class="fas fa-search"></i>
                    <div>Tidak ada training ditemukan untuk "${this.currentQuery}"</div>
                </div>
            `;
            return;
        }

        const html = `
            <div class="search-info">
                <span>Ditemukan ${this.suggestions.length} training</span>
                <span>Ketik untuk mencari lebih spesifik</span>
            </div>
            ${this.suggestions.map((suggestion, index) => `
                <div class="suggestion-item" data-index="${index}" data-id="${suggestion.id}" data-type="${suggestion.type}">
                    <div class="suggestion-icon ${suggestion.type}">
                        <i class="${suggestion.icon}"></i>
                    </div>
                    <div class="suggestion-content">
                        <div class="suggestion-title">${suggestion.highlighted_title}</div>
                        <div class="suggestion-meta">
                            ${suggestion.date ? `<div class="suggestion-meta-item"><i class="fas fa-calendar"></i> ${suggestion.date}</div>` : ''}
                            ${suggestion.location ? `<div class="suggestion-meta-item"><i class="fas fa-map-marker-alt"></i> ${suggestion.highlighted_location}</div>` : ''}
                            ${suggestion.trainer ? `<div class="suggestion-meta-item"><i class="fas fa-user"></i> ${suggestion.highlighted_trainer}</div>` : ''}
                        </div>
                        ${suggestion.description ? `<div class="suggestion-description">${suggestion.description}</div>` : ''}
                    </div>
                    <div class="suggestion-type-badge ${suggestion.type}">${suggestion.type}</div>
                </div>
            `).join('')}
        `;

        this.suggestionsContainer.innerHTML = html;

        // Bind click events to suggestions
        this.suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const index = parseInt(item.dataset.index);
                this.selectSuggestion(index);
            });
        });

        this.selectedIndex = -1;
    }

    showSuggestions() {
        this.suggestionsContainer.classList.add('show');
    }

    hideSuggestions() {
        this.suggestionsContainer.classList.remove('show');
        this.selectedIndex = -1;
    }

    navigateDown() {
        const items = this.suggestionsContainer.querySelectorAll('.suggestion-item');
        if (items.length === 0) return;

        this.selectedIndex = Math.min(this.selectedIndex + 1, items.length - 1);
        this.updateSelection();
    }

    navigateUp() {
        const items = this.suggestionsContainer.querySelectorAll('.suggestion-item');
        if (items.length === 0) return;

        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
        this.updateSelection();
    }

    updateSelection() {
        const items = this.suggestionsContainer.querySelectorAll('.suggestion-item');
        items.forEach((item, index) => {
            item.classList.toggle('selected', index === this.selectedIndex);
        });

        // Scroll selected item into view
        if (this.selectedIndex >= 0) {
            items[this.selectedIndex].scrollIntoView({
                block: 'nearest',
                behavior: 'smooth'
            });
        }
    }

    selectCurrent() {
        if (this.selectedIndex >= 0 && this.selectedIndex < this.suggestions.length) {
            this.selectSuggestion(this.selectedIndex);
        }
    }

    selectSuggestion(index) {
        const suggestion = this.suggestions[index];
        if (!suggestion) return;

        // Update input value
        this.input.value = suggestion.title;
        this.hideSuggestions();

        // Call callback if provided
        if (this.options.onSelect && typeof this.options.onSelect === 'function') {
            this.options.onSelect(suggestion);
        }

        // Trigger custom event
        this.container.dispatchEvent(new CustomEvent('trainingSelected', {
            detail: suggestion
        }));
    }

    setLoading(loading) {
        this.isLoading = loading;
        this.searchIcon.style.display = loading ? 'none' : 'block';
        this.loadingSpinner.classList.toggle('d-none', !loading);
    }

    showError(message) {
        this.suggestionsContainer.innerHTML = `
            <div class="no-suggestions">
                <i class="fas fa-exclamation-triangle text-warning"></i>
                <div>${message}</div>
            </div>
        `;
        this.showSuggestions();
    }

    // Public methods
    clear() {
        this.input.value = '';
        this.currentQuery = '';
        this.hideSuggestions();
    }

    setValue(value) {
        this.input.value = value;
        this.currentQuery = value;
    }

    focus() {
        this.input.focus();
    }
}
