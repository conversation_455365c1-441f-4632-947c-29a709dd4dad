<?php
// <PERSON>lai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Include batch history recording
require_once 'record_batch_employee_history.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi variabel
$success_message = '';
$warning_message = '';
$error_message = '';
$preview_data = [];
$total_success = 0;
$total_error = 0;
$error_details = [];

// Proses upload file
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && ($_POST['action'] === 'upload' || $_POST['action'] === 'preview')) {
    // Cek apakah file diupload atau ada file temporary dari preview
    $file_to_process = null;
    $is_temp_file = false;

    if (isset($_POST['temp_file']) && file_exists($_POST['temp_file'])) {
        // Gunakan file temporary dari preview
        $file_to_process = $_POST['temp_file'];
        $file_name = isset($_POST['original_filename']) ? $_POST['original_filename'] : 'uploaded_file.xlsx';
        $is_temp_file = true;
    } elseif (isset($_FILES['excel_file']) && $_FILES['excel_file']['error'] === UPLOAD_ERR_OK) {
        // Gunakan file yang baru diupload
        $file_to_process = $_FILES['excel_file']['tmp_name'];
        $file_name = $_FILES['excel_file']['name'];
    } else {
        $error_message = 'Terjadi kesalahan saat upload file. Silakan coba lagi.';
    }

    if ($file_to_process) {
        // Validasi tipe file
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        if ($file_ext !== 'xlsx' && $file_ext !== 'xls') {
            $error_message = 'File harus berformat Excel (.xlsx atau .xls).';
        } else {
            // Proses file Excel
            require '../vendor/autoload.php';

            try {
                // Load file Excel
                $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file_to_process);
                $reader->setReadDataOnly(true);
                $spreadsheet = $reader->load($file_to_process);
                $worksheet = $spreadsheet->getActiveSheet();

                // Ambil data dari Excel
                $rows = [];
                $highestRow = $worksheet->getHighestRow();
                $highestColumn = $worksheet->getHighestColumn();
                $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

                for ($row = 1; $row <= $highestRow; $row++) {
                    $rowData = [];
                    for ($col = 1; $col <= $highestColumnIndex; $col++) {
                        $cell = $worksheet->getCell(\PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($col - 1) . $row);
                        $value = $cell->getValue();
                        $rowData[] = $value;
                    }
                    $rows[] = $rowData;
                }

                // Validasi header - cek apakah ada kolom NIK di baris header
                $header = $rows[0];

                // Debug info
                $debug_header = '';
                if (!empty($header) && isset($header[0])) {
                    $debug_header = 'Header pertama: "' . $header[0] . '"';
                } else {
                    $debug_header = 'Header kosong atau tidak valid';
                }

                // Cari kolom NIK di header (bisa di posisi mana saja)
                $nik_column_index = -1;
                foreach ($header as $index => $column) {
                    // Konversi nilai ke string dan cek apakah mengandung 'nik'
                    $column_str = is_string($column) ? $column : (string)$column;
                    if (stripos($column_str, 'nik') !== false) {
                        $nik_column_index = $index;
                        break;
                    }
                }

                if ($nik_column_index === -1) {
                    $error_message = 'Format header tidak sesuai. Tidak ditemukan kolom NIK di header. ' . $debug_header;
                } else {
                    // Jika mode preview, tampilkan data
                    if ($_POST['action'] === 'preview') {
                        // Simpan header untuk ditampilkan di preview
                        $header_row = $rows[0];

                        // Hapus header untuk memproses data
                        $data_rows = array_slice($rows, 1);

                        // Filter baris kosong
                        $filtered_rows = array_filter($data_rows, function($row) use ($nik_column_index) {
                            // Cek apakah kolom NIK memiliki nilai
                            return isset($row[$nik_column_index]) && $row[$nik_column_index] !== null && $row[$nik_column_index] !== '';
                        });

                        // Ambil 10 baris pertama untuk preview
                        $data_preview = array_slice($filtered_rows, 0, 10);

                        // Tambahkan header ke preview data
                        $preview_data = array_merge([$header_row], $data_preview);

                        // Jika tidak ada data untuk preview
                        if (count($preview_data) <= 1) { // Hanya header
                            $error_message = 'Tidak ada data yang dapat ditampilkan. File mungkin kosong atau hanya berisi header.';
                        }
                    } else {
                        // Hapus header untuk mode upload
                        array_shift($rows);

                        // Jika mode delete, proses data
                        $total_rows = count($rows);
                        $total_success = 0;
                        $total_error = 0;
                        $error_details = [];

                        foreach ($rows as $index => $row) {
                            // Skip baris kosong
                            if (empty(array_filter($row))) {
                                continue;
                            }

                            // Ambil NIK dari baris (menggunakan indeks kolom NIK yang terdeteksi)
                            $nik = isset($row[$nik_column_index]) ? trim($row[$nik_column_index]) : '';

                            // Validasi NIK
                            if (empty($nik)) {
                                $total_error++;
                                $error_details[] = "Baris " . ($index + 2) . ": NIK tidak boleh kosong.";
                                continue;
                            }

                            // Cek apakah NIK ada di database
                            $stmt = $conn->prepare("SELECT id FROM karyawan WHERE nik = ?");
                            $stmt->bind_param("s", $nik);
                            $stmt->execute();
                            $result = $stmt->get_result();

                            if ($result->num_rows === 0) {
                                $total_error++;
                                $error_details[] = "Baris " . ($index + 2) . ": NIK $nik tidak ditemukan dalam database.";
                                continue;
                            }

                            // Hapus karyawan
                            $delete_query = "DELETE FROM karyawan WHERE nik = ?";
                            $stmt = $conn->prepare($delete_query);
                            $stmt->bind_param("s", $nik);

                            if ($stmt->execute()) {
                                $total_success++;
                            } else {
                                $total_error++;
                                $error_details[] = "Baris " . ($index + 2) . ": Gagal menghapus karyawan dengan NIK $nik. Error: " . $stmt->error;
                            }
                        }

                        // Log aktivitas
                        $user_id = $_SESSION['user_id'];
                        $log_query = "INSERT INTO activity_logs (user_id, action, category, timestamp)
                                      VALUES (?, CONCAT('Hapus karyawan masal: ', ? ,' berhasil, ', ?, ' gagal'), 'employee', NOW())";

                        $log_stmt = $conn->prepare($log_query);
                        $log_stmt->bind_param("iii", $user_id, $total_success, $total_error);
                        $log_stmt->execute();

                        // Collect all NIKs processed (successful and failed)
                        $all_niks = [];
                        foreach ($rows as $index => $row) {
                            if (!empty(array_filter($row))) {
                                $nik = isset($row[$nik_column_index]) ? trim($row[$nik_column_index]) : '';
                                if (!empty($nik)) {
                                    $all_niks[] = $nik;
                                }
                            }
                        }

                        // Record batch history
                        $batch_id = recordBatchEmployeeHistory(
                            $conn,
                            'BATCH_DELETE',
                            $all_niks,
                            'delete',
                            $total_success,
                            $total_error,
                            0 // No skipped records in deletion
                        );

                        if ($batch_id) {
                            error_log("Batch deletion history recorded with ID: $batch_id");
                        } else {
                            error_log("Failed to record batch deletion history");
                        }

                        // Set pesan sukses
                        if ($total_success > 0) {
                            $success_message = "Berhasil menghapus $total_success data karyawan.";
                            if ($total_error > 0) {
                                $success_message .= " $total_error data gagal dihapus.";
                            }
                        } else {
                            $error_message = "Gagal menghapus data karyawan. $total_error data gagal dihapus.";
                        }

                        // Hapus file temporary jika ada
                        if ($is_temp_file && file_exists($file_to_process)) {
                            @unlink($file_to_process);
                        }
                    }
                }
            } catch (Exception $e) {
                $error_message = 'Terjadi kesalahan: ' . $e->getMessage();

                // Hapus file temporary jika ada
                if (isset($is_temp_file) && $is_temp_file && isset($file_to_process) && file_exists($file_to_process)) {
                    @unlink($file_to_process);
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    body {
        background-color: #f5f5f5;
        overflow-x: hidden;
    }

    .container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
    }

    .card {
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 20px;
    }

    .card-header {
        background-color: #BF0000 !important;
        color: white;
        padding: 15px 20px;
    }

    .card-header h3 {
        margin: 0;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
    }

    .card-header h3 i {
        margin-right: 10px;
    }

    .card-body {
        padding: 20px;
        background-color: white;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 5px;
        display: block;
    }

    .form-control {
        width: 100%;
        padding: 10px;
        border: 1px solid #ced4da;
        border-radius: 5px;
    }

    .btn-primary {
        background-color: #BF0000;
        border-color: #BF0000;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #a00000;
        border-color: #a00000;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #5a6268;
    }

    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    .alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }

    .alert-warning {
        background-color: #fff3cd;
        border-color: #ffeeba;
        color: #856404;
    }

    .table-container {
        overflow-x: auto;
        margin-top: 20px;
    }

    .table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .table th {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 600;
        text-align: left;
        padding: 12px 15px;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        padding: 12px 15px;
        border-bottom: 1px solid #e9ecef;
        color: #495057;
    }

    .table tr:hover {
        background-color: #f8f9fa;
    }

    .steps {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20px;
        gap: 20px;
    }

    .step {
        flex: 1;
        min-width: 200px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
        border-left: 4px solid #BF0000;
    }

    .step-number {
        display: inline-block;
        width: 30px;
        height: 30px;
        background-color: #BF0000;
        color: white;
        border-radius: 50%;
        text-align: center;
        line-height: 30px;
        margin-right: 10px;
        font-weight: bold;
    }

    .step-title {
        font-weight: 600;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .step-description {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .error-details {
        max-height: 200px;
        overflow-y: auto;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-top: 10px;
    }

    .error-details ul {
        margin: 0;
        padding-left: 20px;
    }

    .error-details li {
        margin-bottom: 5px;
    }

    .jarak {
        height: 100px;
    }

    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeeba;
        border-left: 4px solid #ffc107;
        color: #856404;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .warning-box h4 {
        color: #856404;
        margin-top: 0;
        margin-bottom: 10px;
    }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container">
    <div class="card">
        <div class="card-header">
            <h3><i class="fas fa-trash"></i> Hapus Data Karyawan Masal</h3>
        </div>
        <div class="card-body">
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($warning_message)): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $warning_message; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <div class="warning-box">
                <h4><i class="fas fa-exclamation-triangle"></i> Perhatian!</h4>
                <p>Fitur ini digunakan untuk menghapus data karyawan secara masal. Tindakan ini tidak dapat dibatalkan. Pastikan Anda telah memilih data yang benar sebelum melanjutkan.</p>
            </div>

            <div class="steps">
                <div class="step">
                    <div class="step-title">
                        <span class="step-number">1</span> Download Data Karyawan
                    </div>
                    <div class="step-description">
                        Download data karyawan dalam format Excel. File ini berisi semua data karyawan yang ada di sistem.
                    </div>
                    <div class="mt-3">
                        <a href="download_employees_for_deletion.php" class="btn btn-primary">
                            <i class="fas fa-download"></i> Download Data Karyawan
                        </a>
                    </div>
                </div>

                <div class="step">
                    <div class="step-title">
                        <span class="step-number">2</span> Edit File Excel
                    </div>
                    <div class="step-description">
                        Buka file Excel yang telah didownload. <strong>HAPUS</strong> baris data karyawan yang <strong>TIDAK</strong> ingin dihapus. <strong>PERTAHANKAN</strong> baris data karyawan yang ingin dihapus.
                    </div>
                </div>

                <div class="step">
                    <div class="step-title">
                        <span class="step-number">3</span> Upload File
                    </div>
                    <div class="step-description">
                        Upload file Excel yang sudah diedit. Sistem akan menghapus data karyawan yang ada dalam file tersebut.
                    </div>
                </div>
            </div>

            <form method="post" enctype="multipart/form-data" id="uploadForm">
                <div class="form-group">
                    <label class="form-label">Pilih File Excel</label>
                    <input type="file" name="excel_file" id="excelFile" class="form-control" accept=".xlsx, .xls" required>
                    <small class="text-muted">File harus berformat Excel (.xlsx atau .xls)</small>
                </div>

                <div class="form-group">
                    <button type="button" class="btn btn-secondary" id="previewBtn">
                        <i class="fas fa-eye"></i> Preview Data
                    </button>
                    <button type="button" class="btn btn-danger" id="uploadBtn">
                        <i class="fas fa-trash"></i> Hapus Data
                    </button>
                    <a href="employee_management.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>

                <!-- Hidden inputs for form submission -->
                <input type="hidden" name="action" id="formAction" value="">
            </form>

            <!-- Loading indicator -->
            <div id="loadingIndicator" style="display:none;" class="text-center mt-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2">Sedang memproses file, mohon tunggu...</p>
            </div>

            <?php if (!empty($preview_data)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h3><i class="fas fa-eye"></i> Preview Data yang Akan Dihapus</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table">
                                <!-- Header akan diambil dari data Excel -->
                                <tbody>
                                    <?php
                                    // Tampilkan header dengan style berbeda
                                    if (!empty($preview_data) && count($preview_data) > 0):
                                        $header = $preview_data[0];
                                    ?>
                                        <tr class="table-primary">
                                            <?php foreach ($header as $cell): ?>
                                                <th><?php echo ($cell !== null) ? htmlspecialchars($cell) : ''; ?></th>
                                            <?php endforeach; ?>
                                        </tr>
                                    <?php endif; ?>

                                    <?php
                                    // Tampilkan data (mulai dari indeks 1 untuk melewati header)
                                    for ($i = 1; $i < count($preview_data); $i++):
                                        $row = $preview_data[$i];
                                    ?>
                                        <tr>
                                            <?php foreach ($row as $cell): ?>
                                                <td><?php echo ($cell !== null) ? htmlspecialchars($cell) : ''; ?></td>
                                            <?php endforeach; ?>
                                        </tr>
                                    <?php endfor; ?>

                                    <?php
                                    // Jika tidak ada data, tampilkan pesan
                                    if (count($preview_data) <= 1):
                                    ?>
                                        <tr>
                                            <td colspan="15" class="text-center">Tidak ada data untuk ditampilkan</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <p class="text-muted">Menampilkan <?php echo count($preview_data) - 1; ?> baris data yang akan dihapus dari file Excel.</p>
                            <form method="post" enctype="multipart/form-data">
                                <!-- Simpan file yang diupload ke temporary file dan gunakan path sebagai referensi -->
                                <?php
                                    // Cek apakah file sudah diupload
                                    if (isset($_FILES['excel_file']) && !empty($_FILES['excel_file']['tmp_name'])) {
                                        $temp_file = tempnam(sys_get_temp_dir(), 'excel_');
                                        move_uploaded_file($_FILES['excel_file']['tmp_name'], $temp_file);
                                    } else {
                                        // Jika tidak ada file baru, gunakan file yang sama dari preview
                                        $temp_file = $_POST['temp_file'] ?? tempnam(sys_get_temp_dir(), 'excel_');
                                    }
                                ?>
                                <input type="hidden" name="temp_file" value="<?php echo $temp_file; ?>">
                                <input type="hidden" name="original_filename" value="<?php echo isset($_FILES['excel_file']) ? htmlspecialchars($_FILES['excel_file']['name']) : (isset($_POST['original_filename']) ? htmlspecialchars($_POST['original_filename']) : 'uploaded_file.xlsx'); ?>">
                                <input type="hidden" name="action" value="upload">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Hapus Data
                                </button>
                                <a href="delete_karyawan.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Batal
                                </a>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($total_error > 0 && !empty($error_details)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h3><i class="fas fa-exclamation-triangle"></i> Detail Error</h3>
                    </div>
                    <div class="card-body">
                        <div class="error-details">
                            <ul>
                                <?php foreach ($error_details as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
// Menangani error runtime.lastError
window.addEventListener('error', function(e) {
    // Cek apakah error adalah runtime.lastError
    if (e && e.message && e.message.includes('runtime.lastError')) {
        console.log('Caught runtime.lastError:', e.message);
        // Mencegah error ditampilkan di konsol browser
        e.preventDefault();
        e.stopPropagation();
        return true;
    }
});

// Menangani unhandled promise rejection
window.addEventListener('unhandledrejection', function(e) {
    // Cek apakah rejection adalah runtime.lastError
    if (e && e.reason && e.reason.message && e.reason.message.includes('runtime.lastError')) {
        console.log('Caught unhandled promise rejection with runtime.lastError:', e.reason.message);
        // Mencegah error ditampilkan di konsol browser
        e.preventDefault();
        e.stopPropagation();
        return true;
    }
});

// Menangani submit form untuk mencegah multiple submission
document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const formAction = document.getElementById('formAction');

    if (uploadForm) {
        const excelFileInput = document.getElementById('excelFile');
        const previewBtn = document.getElementById('previewBtn');
        const uploadBtn = document.getElementById('uploadBtn');

        // Validasi file sebelum submit
        excelFileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                // Validasi tipe file
                const fileType = file.name.split('.').pop().toLowerCase();
                if (fileType !== 'xlsx' && fileType !== 'xls') {
                    alert('File harus berformat Excel (.xlsx atau .xls)');
                    this.value = ''; // Reset input file
                    return;
                }

                // Validasi ukuran file (max 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('Ukuran file terlalu besar. Maksimal 5MB.');
                    this.value = ''; // Reset input file
                    return;
                }

                // Enable tombol jika file valid
                previewBtn.disabled = false;
                uploadBtn.disabled = false;
            } else {
                // Disable tombol jika tidak ada file
                previewBtn.disabled = true;
                uploadBtn.disabled = true;
            }
        });

        // Tangani klik tombol preview
        previewBtn.addEventListener('click', function() {
            // Validasi file sebelum submit
            if (!excelFileInput.files || excelFileInput.files.length === 0) {
                alert('Silakan pilih file Excel terlebih dahulu.');
                return;
            }

            // Set action dan submit form
            formAction.value = 'preview';

            // Tampilkan loading indicator
            loadingIndicator.style.display = 'block';

            // Disable tombol
            previewBtn.disabled = true;
            uploadBtn.disabled = true;

            // Submit form
            uploadForm.submit();
        });

        // Tangani klik tombol upload
        uploadBtn.addEventListener('click', function() {
            // Validasi file sebelum submit
            if (!excelFileInput.files || excelFileInput.files.length === 0) {
                alert('Silakan pilih file Excel terlebih dahulu.');
                return;
            }

            // Gunakan confirmAction sebagai pengganti confirm
            confirmAction(
                'PERHATIAN: Tindakan ini akan menghapus data karyawan yang ada dalam file Excel. Tindakan ini tidak dapat dibatalkan. Apakah Anda yakin ingin melanjutkan?',
                function() {
                    // Kode ini akan dijalankan ketika user mengklik "Ya"

                    // Set action dan submit form
                    formAction.value = 'upload';

                    // Tampilkan loading indicator
                    loadingIndicator.style.display = 'block';

                    // Disable tombol
                    previewBtn.disabled = true;
                    uploadBtn.disabled = true;

                    // Submit form
                    uploadForm.submit();
                },
                function() {
                    // Kode ini akan dijalankan ketika user mengklik "Tidak"
                    console.log("Penghapusan dibatalkan oleh user");
                },
                "Konfirmasi Penghapusan"
            );
        });
    }
});
</script>
</body>
</html>

