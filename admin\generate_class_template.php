<?php
/**
 * Generate Excel Template untuk Import Kelas Training
 * File ini menghasilkan template Excel untuk mengimpor data kelas training
 */

// <PERSON><PERSON> sesi jika belum dimulai
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include file konfigurasi
include_once '../config/config.php';
include '../includes/functions.php';

// Cek apakah user sudah login dan memiliki role admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Require library PhpSpreadsheet
// Anda perlu menginstal ini melalui Composer: composer require phpoffice/phpspreadsheet
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;

// Buat spreadsheet baru
$spreadsheet = new Spreadsheet();

// Set properti dokumen
$spreadsheet->getProperties()
    ->setCreator('Sistem Training')
    ->setLastModifiedBy('Sistem Training')
    ->setTitle('Template Import Kelas Training')
    ->setSubject('Template Import Kelas Training')
    ->setDescription('Template untuk mengimpor data kelas training')
    ->setKeywords('training import template')
    ->setCategory('Templates');

// Buat sheet Kelas
$classesSheet = $spreadsheet->getActiveSheet();
$classesSheet->setTitle('Kelas');

// Set style header
$headerStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FFFFFF'],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => '4472C4'],
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
        ],
    ],
];

// Set style instruksi
$instructionStyle = [
    'font' => [
        'italic' => true,
        'color' => ['rgb' => '808080'],
    ],
];

// Set style kolom wajib
$requiredStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FF0000'],
    ],
];

// Tambahkan instruksi ke sheet Kelas
$classesSheet->setCellValue('A1', 'PETUNJUK:');
$classesSheet->mergeCells('A1:G1');
$classesSheet->setCellValue('A2', '1. Kolom bertanda * wajib diisi');
$classesSheet->mergeCells('A2:G2');
$classesSheet->setCellValue('A3', '2. Topik Training harus sesuai dengan topik training yang sudah disetujui dan dibuatkan');
$classesSheet->mergeCells('A3:G3');
$classesSheet->setCellValue('A4', '3. Status harus salah satu dari: aktif, tidak aktif, selesai');
$classesSheet->mergeCells('A4:G4');
$classesSheet->setCellValue('A5', '4. Format tanggal: YYYY-MM-DD (contoh: 2023-12-31)');
$classesSheet->mergeCells('A5:G5');

// Terapkan style instruksi
$classesSheet->getStyle('A1:G5')->applyFromArray($instructionStyle);

// Tambahkan header ke sheet Kelas
$classesHeaders = [
    'A7' => 'Topik Training*',
    'B7' => 'Judul Kelas*',
    'C7' => 'Deskripsi',
    'D7' => 'Tanggal Mulai',
    'E7' => 'Tanggal Selesai',
    'F7' => 'Status*',
    'G7' => 'Jumlah Peserta Maksimal'
];

foreach ($classesHeaders as $cell => $value) {
    $classesSheet->setCellValue($cell, $value);
}

// Terapkan style header
$classesSheet->getStyle('A7:G7')->applyFromArray($headerStyle);

// Tambahkan data contoh
$classesSheet->setCellValue('A8', 'Learning & Development');
$classesSheet->setCellValue('B8', 'Kelas Learning & Development');
$classesSheet->setCellValue('C8', 'Deskripsi Learning & Development');
$classesSheet->setCellValue('D8', '2025-01-01');
$classesSheet->setCellValue('E8', '2100-01-01');
$classesSheet->setCellValue('F8', 'aktif');
$classesSheet->setCellValue('G8', '1000');

// Set lebar kolom
$classesSheet->getColumnDimension('A')->setWidth(30);
$classesSheet->getColumnDimension('B')->setWidth(30);
$classesSheet->getColumnDimension('C')->setWidth(40);
$classesSheet->getColumnDimension('D')->setWidth(15);
$classesSheet->getColumnDimension('E')->setWidth(15);
$classesSheet->getColumnDimension('F')->setWidth(15);
$classesSheet->getColumnDimension('G')->setWidth(25);

// Tambahkan validasi data untuk status
$statusValidation = $classesSheet->getCell('F8')->getDataValidation();
$statusValidation->setType(DataValidation::TYPE_LIST);
$statusValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
$statusValidation->setAllowBlank(false);
$statusValidation->setShowInputMessage(true);
$statusValidation->setShowErrorMessage(true);
$statusValidation->setShowDropDown(true);
$statusValidation->setFormula1('"aktif,tidak aktif,selesai"');

// Salin validasi ke range
$classesSheet->setDataValidation('F8:F100', $statusValidation);

// Dapatkan daftar training yang sudah disetujui
$trainings = [];
$trainings_query = "SELECT id, training_topic FROM training_submissions 
                   WHERE status = 'Approved' OR status = 'Completed'
                   ORDER BY id DESC";
$result = $conn->query($trainings_query);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $trainings[] = $row['training_topic'];
    }
}

// Tambahkan validasi data untuk topik training
if (!empty($trainings)) {
    $trainingList = '"' . implode(',', $trainings) . '"';
    $topicValidation = $classesSheet->getCell('A8')->getDataValidation();
    $topicValidation->setType(DataValidation::TYPE_LIST);
    $topicValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
    $topicValidation->setAllowBlank(false);
    $topicValidation->setShowInputMessage(true);
    $topicValidation->setShowErrorMessage(true);
    $topicValidation->setShowDropDown(true);
    $topicValidation->setFormula1($trainingList);
    
    // Salin validasi ke range
    $classesSheet->setDataValidation('A8:A100', $topicValidation);
}

// Set sheet pertama sebagai aktif
$spreadsheet->setActiveSheetIndex(0);

// Buat writer
$writer = new Xlsx($spreadsheet);

// Set header untuk download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="template_import_kelas.xlsx"');
header('Cache-Control: max-age=0');

// Simpan ke output
$writer->save('php://output');
exit;
?>
