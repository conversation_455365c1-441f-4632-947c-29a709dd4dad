<?php
/**
 * Check table structure untuk training_participants
 */

include '../config/config.php';
include 'security.php';

echo "<h2>🔍 Check Table Structure</h2>";

// Check training_participants table structure
echo "<h3>training_participants Table Structure</h3>";
$query = "DESCRIBE training_participants";
$result = $conn->query($query);

if ($result) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Field</th>";
    echo "<th style='padding: 8px;'>Type</th>";
    echo "<th style='padding: 8px;'>Null</th>";
    echo "<th style='padding: 8px;'>Key</th>";
    echo "<th style='padding: 8px;'>Default</th>";
    echo "<th style='padding: 8px;'>Extra</th>";
    echo "</tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td style='padding: 8px;'><strong>{$row['Field']}</strong></td>";
        echo "<td style='padding: 8px;'>{$row['Type']}</td>";
        echo "<td style='padding: 8px;'>{$row['Null']}</td>";
        echo "<td style='padding: 8px;'>{$row['Key']}</td>";
        echo "<td style='padding: 8px;'>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "<td style='padding: 8px;'>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>Error: " . $conn->error . "</p>";
}

// Check sample data
echo "<h3>Sample Data from training_participants</h3>";
$query = "SELECT * FROM training_participants LIMIT 10";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    
    // Get column names
    $fields = $result->fetch_fields();
    echo "<tr style='background: #f8f9fa;'>";
    foreach ($fields as $field) {
        echo "<th style='padding: 8px;'>{$field->name}</th>";
    }
    echo "</tr>";
    
    // Reset result pointer
    $result->data_seek(0);
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td style='padding: 8px;'>" . htmlspecialchars($value ?? 'NULL') . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p><em>No data found in training_participants table</em></p>";
}

// Check training_attendance table structure (for comparison)
echo "<h3>training_attendance Table Structure (for comparison)</h3>";
$query = "DESCRIBE training_attendance";
$result = $conn->query($query);

if ($result) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Field</th>";
    echo "<th style='padding: 8px;'>Type</th>";
    echo "<th style='padding: 8px;'>Null</th>";
    echo "<th style='padding: 8px;'>Key</th>";
    echo "<th style='padding: 8px;'>Default</th>";
    echo "<th style='padding: 8px;'>Extra</th>";
    echo "</tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td style='padding: 8px;'><strong>{$row['Field']}</strong></td>";
        echo "<td style='padding: 8px;'>{$row['Type']}</td>";
        echo "<td style='padding: 8px;'>{$row['Null']}</td>";
        echo "<td style='padding: 8px;'>{$row['Key']}</td>";
        echo "<td style='padding: 8px;'>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "<td style='padding: 8px;'>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>Error: " . $conn->error . "</p>";
}

// Show all tables
echo "<h3>All Tables in Database</h3>";
$query = "SHOW TABLES";
$result = $conn->query($query);

if ($result) {
    echo "<ul>";
    while ($row = $result->fetch_array()) {
        echo "<li><strong>{$row[0]}</strong></li>";
    }
    echo "</ul>";
}

$conn->close();
?>
