<?php
/**
 * CSRF Protection Class
 * Mengelola token CSRF untuk keamanan form
 */

class CSRFProtection {
    private $conn;
    private $token_lifetime = 3600; // 1 hour
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->createTableIfNotExists();
    }
    
    /**
     * Create CSRF tokens table if not exists
     */
    private function createTableIfNotExists() {
        $query = "CREATE TABLE IF NOT EXISTS csrf_tokens (
            id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            token VARCHAR(64) NOT NULL,
            session_id VARCHAR(128) NOT NULL,
            created_at DATETIME NOT NULL,
            expires_at DATETIME NOT NULL,
            used TINYINT(1) NOT NULL DEFAULT 0,
            UNIQUE KEY (token),
            INDEX (session_id),
            INDEX (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->conn->query($query);
    }
    
    /**
     * Generate new CSRF token
     */
    public function generateToken() {
        $token = bin2hex(random_bytes(32));
        $session_id = session_id();
        $created_at = date('Y-m-d H:i:s');
        $expires_at = date('Y-m-d H:i:s', time() + $this->token_lifetime);
        
        // Clean old tokens first
        $this->cleanExpiredTokens();
        
        // Insert new token
        $query = "INSERT INTO csrf_tokens (token, session_id, created_at, expires_at) 
                  VALUES (?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("ssss", $token, $session_id, $created_at, $expires_at);
        
        if ($stmt->execute()) {
            // Store in session as backup
            $_SESSION['csrf_token'] = $token;
            return $token;
        }
        
        return false;
    }
    
    /**
     * Validate CSRF token
     */
    public function validateToken($token) {
        if (empty($token)) {
            return false;
        }
        
        $session_id = session_id();
        
        // Check token in database
        $query = "SELECT id FROM csrf_tokens 
                  WHERE token = ? 
                  AND session_id = ? 
                  AND expires_at > NOW() 
                  AND used = 0";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("ss", $token, $session_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            // Mark token as used
            $this->markTokenAsUsed($token);
            return true;
        }
        
        // Fallback to session token (for backward compatibility)
        if (isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token)) {
            unset($_SESSION['csrf_token']);
            return true;
        }
        
        return false;
    }
    
    /**
     * Mark token as used
     */
    private function markTokenAsUsed($token) {
        $query = "UPDATE csrf_tokens SET used = 1 WHERE token = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("s", $token);
        $stmt->execute();
    }
    
    /**
     * Get current token (generate if not exists)
     */
    public function getToken() {
        // Check if we have a valid token in session
        if (isset($_SESSION['csrf_token'])) {
            $token = $_SESSION['csrf_token'];
            
            // Verify token is still valid in database
            $query = "SELECT id FROM csrf_tokens 
                      WHERE token = ? 
                      AND session_id = ? 
                      AND expires_at > NOW() 
                      AND used = 0";
            
            $stmt = $this->conn->prepare($query);
            $session_id = session_id();
            $stmt->bind_param("ss", $token, $session_id);
            $stmt->execute();
            
            if ($stmt->get_result()->num_rows > 0) {
                return $token;
            }
        }
        
        // Generate new token
        return $this->generateToken();
    }
    
    /**
     * Generate hidden input field for forms
     */
    public function getHiddenInput() {
        $token = $this->getToken();
        return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
    }
    
    /**
     * Clean expired tokens
     */
    public function cleanExpiredTokens() {
        $query = "DELETE FROM csrf_tokens WHERE expires_at < NOW() OR used = 1";
        $this->conn->query($query);
    }
    
    /**
     * Clean all tokens for current session
     */
    public function cleanSessionTokens() {
        $session_id = session_id();
        $query = "DELETE FROM csrf_tokens WHERE session_id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("s", $session_id);
        $stmt->execute();
        
        unset($_SESSION['csrf_token']);
    }
    
    /**
     * Validate token from request
     */
    public function validateRequest() {
        $token = $_POST['csrf_token'] ?? $_GET['csrf_token'] ?? null;
        return $this->validateToken($token);
    }
    
    /**
     * Middleware function to protect routes
     */
    public function protect() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (!$this->validateRequest()) {
                http_response_code(403);
                die('CSRF token validation failed. Please refresh the page and try again.');
            }
        }
    }
    
    /**
     * Get token statistics
     */
    public function getTokenStats() {
        $query = "SELECT 
                    COUNT(*) as total_tokens,
                    COUNT(CASE WHEN used = 1 THEN 1 END) as used_tokens,
                    COUNT(CASE WHEN expires_at < NOW() THEN 1 END) as expired_tokens,
                    COUNT(CASE WHEN expires_at > NOW() AND used = 0 THEN 1 END) as active_tokens
                  FROM csrf_tokens";
        
        $result = $this->conn->query($query);
        return $result->fetch_assoc();
    }
}
