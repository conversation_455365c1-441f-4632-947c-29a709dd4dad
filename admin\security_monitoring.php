<?php
/**
 * Halaman Monitoring Keamanan
 * Halaman ini menampilkan statistik dan log keamanan website
 */

// <PERSON><PERSON> session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include konfigurasi database
require_once '../config/config.php';

// Pastikan hanya admin yang bisa akses
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Fungsi untuk format tanggal
function format_date($date) {
    return date('d M Y H:i:s', strtotime($date));
}

// Fungsi untuk mendapatkan nama user
function get_user_name($conn, $user_id) {
    if ($user_id == 0) {
        return 'Guest/Unknown';
    }

    $query = "SELECT name FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc()['name'];
    }

    return "User #$user_id";
}

// Ambil statistik keamanan
$stats = [];

// 1. Total percobaan login gagal
$query = "SELECT COUNT(*) as count FROM login_attempts WHERE success = 0";
$result = $conn->query($query);
$stats['failed_logins'] = $result->fetch_assoc()['count'];

// 2. Total akun terkunci
$query = "SELECT COUNT(*) as count FROM users WHERE account_locked = 1";
$result = $conn->query($query);
$stats['locked_accounts'] = $result->fetch_assoc()['count'];

// 3. Total serangan terdeteksi
$query = "SELECT COUNT(*) as count FROM security_logs WHERE event_type LIKE 'ATTACK%'";
$result = $conn->query($query);
$stats['detected_attacks'] = $result->fetch_assoc()['count'];

// 3a. Total logout hari ini
$query = "SELECT COUNT(*) as count FROM security_logs WHERE event_type = 'LOGOUT' AND DATE(created_at) = CURDATE()";
$result = $conn->query($query);
$stats['today_logouts'] = $result->fetch_assoc()['count'];

// 4. Serangan per jenis
$query = "SELECT event_type, COUNT(*) as count FROM security_logs WHERE event_type LIKE 'ATTACK%' GROUP BY event_type";
$result = $conn->query($query);
$stats['attack_types'] = [];
while ($row = $result->fetch_assoc()) {
    $stats['attack_types'][$row['event_type']] = $row['count'];
}

// 5. Aktivitas login terbaru
$query = "SELECT la.*, u.name as user_name
          FROM login_attempts la
          LEFT JOIN users u ON la.user_id = u.id
          ORDER BY la.attempt_time DESC LIMIT 20";
$result = $conn->query($query);
$recent_logins = [];
while ($row = $result->fetch_assoc()) {
    $recent_logins[] = $row;
}

// 6. Aktivitas keamanan terbaru
$query = "SELECT * FROM security_logs ORDER BY created_at DESC LIMIT 20";
$result = $conn->query($query);
$recent_activities = [];
while ($row = $result->fetch_assoc()) {
    $recent_activities[] = $row;
}

// 7. Rate limits terbaru
$query = "SELECT * FROM rate_limits ORDER BY attempt_time DESC LIMIT 20";
$result = $conn->query($query);
$recent_rate_limits = [];
while ($row = $result->fetch_assoc()) {
    $recent_rate_limits[] = $row;
}

// 8. Statistik per hari (7 hari terakhir)
$query = "SELECT DATE(attempt_time) as date, COUNT(*) as count, SUM(success) as success_count
          FROM login_attempts
          WHERE attempt_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
          GROUP BY DATE(attempt_time)
          ORDER BY date DESC";
$result = $conn->query($query);
$daily_stats = [];
while ($row = $result->fetch_assoc()) {
    $daily_stats[] = $row;
}

// 8a. Statistik logout per hari (7 hari terakhir)
$query = "SELECT DATE(created_at) as date, COUNT(*) as logout_count
          FROM security_logs
          WHERE event_type = 'LOGOUT' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
          GROUP BY DATE(created_at)
          ORDER BY date DESC";
$result = $conn->query($query);
$daily_logout_stats = [];
while ($row = $result->fetch_assoc()) {
    $daily_logout_stats[] = $row;
}

// 9. IP Address dengan percobaan login gagal terbanyak
$query = "SELECT ip_address, COUNT(*) as count
          FROM login_attempts
          WHERE success = 0
          GROUP BY ip_address
          ORDER BY count DESC
          LIMIT 10";
$result = $conn->query($query);
$top_failed_ips = [];
while ($row = $result->fetch_assoc()) {
    $top_failed_ips[] = $row;
}

// 10. User dengan percobaan login gagal terbanyak
$query = "SELECT user_id, COUNT(*) as count
          FROM login_attempts
          WHERE success = 0 AND user_id > 0
          GROUP BY user_id
          ORDER BY count DESC
          LIMIT 10";
$result = $conn->query($query);
$top_failed_users = [];
while ($row = $result->fetch_assoc()) {
    $row['user_name'] = get_user_name($conn, $row['user_id']);
    $top_failed_users[] = $row;
}

// 11. Log login dan logout (untuk tab baru)
// Fungsi untuk mendapatkan total log login/logout
function getTotalLoginLogs($conn, $filter_user, $filter_date, $filter_event, $filter_search = '') {
    $where_clauses = [];
    $params = [];
    $types = "";

    if ($filter_user > 0) {
        $where_clauses[] = "user_id = ?";
        $params[] = $filter_user;
        $types .= "i";
    }

    if (!empty($filter_date)) {
        $where_clauses[] = "DATE(created_at) = ?";
        $params[] = $filter_date;
        $types .= "s";
    }

    if (!empty($filter_event)) {
        // Handle different event type formats
        if ($filter_event == 'LOGIN_SUCCESS') {
            $where_clauses[] = "(event_type = 'LOGIN_SUCCESS' OR event_type = 'login_success')";
        } elseif ($filter_event == 'LOGIN_FAILED') {
            $where_clauses[] = "(event_type = 'LOGIN_FAILED' OR event_type = 'login_failed')";
        } elseif ($filter_event == 'LOGOUT') {
            $where_clauses[] = "(event_type = 'LOGOUT' OR event_type = 'logout')";
        } elseif ($filter_event == 'LOGIN_UNKNOWN_USER') {
            $where_clauses[] = "event_type LIKE '%LOGIN_UNKNOWN_USER%'";
        } else {
            $where_clauses[] = "event_type = ?";
            $params[] = $filter_event;
            $types .= "s";
        }
    }

    // Tambahkan filter pencarian jika ada
    if (!empty($filter_search)) {
        $where_clauses[] = "(description LIKE ? OR event_type LIKE ?)";
        $search_term = "%" . $filter_search . "%";
        $params[] = $search_term;
        $params[] = $search_term;
        $types .= "ss";
    }

    // Tambahkan kondisi untuk hanya menampilkan log login/logout
    if (empty($filter_event)) {
        $where_clauses[] = "(event_type IN ('LOGIN_SUCCESS', 'login_success', 'LOGIN_FAILED', 'login_failed', 'LOGOUT', 'logout') OR event_type LIKE '%LOGIN_UNKNOWN_USER%')";
    }

    $where_sql = !empty($where_clauses) ? "WHERE " . implode(" AND ", $where_clauses) : "";

    $query = "SELECT COUNT(*) as total FROM security_logs sl
              LEFT JOIN users u ON sl.user_id = u.id
              $where_sql";
    $stmt = $conn->prepare($query);

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();

    return $row['total'];
}

// Fungsi untuk mendapatkan log login dan logout
function getLoginLogs($conn, $filter_user, $filter_date, $filter_event, $limit, $offset, $filter_search = '') {
    $where_clauses = [];
    $params = [];
    $types = "";

    if ($filter_user > 0) {
        $where_clauses[] = "sl.user_id = ?";
        $params[] = $filter_user;
        $types .= "i";
    }

    if (!empty($filter_date)) {
        $where_clauses[] = "DATE(sl.created_at) = ?";
        $params[] = $filter_date;
        $types .= "s";
    }

    if (!empty($filter_event)) {
        // Handle different event type formats
        if ($filter_event == 'LOGIN_SUCCESS') {
            $where_clauses[] = "(sl.event_type = 'LOGIN_SUCCESS' OR sl.event_type = 'login_success')";
        } elseif ($filter_event == 'LOGIN_FAILED') {
            $where_clauses[] = "(sl.event_type = 'LOGIN_FAILED' OR sl.event_type = 'login_failed')";
        } elseif ($filter_event == 'LOGOUT') {
            $where_clauses[] = "(sl.event_type = 'LOGOUT' OR sl.event_type = 'logout')";
        } elseif ($filter_event == 'LOGIN_UNKNOWN_USER') {
            $where_clauses[] = "sl.event_type LIKE '%LOGIN_UNKNOWN_USER%'";
        } else {
            $where_clauses[] = "sl.event_type = ?";
            $params[] = $filter_event;
            $types .= "s";
        }
    }

    // Tambahkan filter pencarian jika ada
    if (!empty($filter_search)) {
        $where_clauses[] = "(sl.description LIKE ? OR sl.event_type LIKE ? OR u.name LIKE ? OR u.nik LIKE ?)";
        $search_term = "%" . $filter_search . "%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
        $types .= "ssss";
    }

    // Tambahkan kondisi untuk hanya menampilkan log login/logout
    if (empty($filter_event)) {
        $where_clauses[] = "(sl.event_type IN ('LOGIN_SUCCESS', 'login_success', 'LOGIN_FAILED', 'login_failed', 'LOGOUT', 'logout') OR sl.event_type LIKE '%LOGIN_UNKNOWN_USER%')";
    }

    $where_sql = !empty($where_clauses) ? "WHERE " . implode(" AND ", $where_clauses) : "";

    $query = "SELECT sl.*, u.name as user_name, u.nik as user_nik
              FROM security_logs sl
              LEFT JOIN users u ON sl.user_id = u.id
              $where_sql
              ORDER BY sl.created_at DESC
              LIMIT ? OFFSET ?";

    $stmt = $conn->prepare($query);

    if (!empty($params)) {
        $params[] = $limit;
        $params[] = $offset;
        $types .= "ii";
        $stmt->bind_param($types, ...$params);
    } else {
        $stmt->bind_param("ii", $limit, $offset);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    $logs = [];
    while ($row = $result->fetch_assoc()) {
        $logs[] = $row;
    }

    return $logs;
}

// Ambil filter dari query string jika ada
$filter_user = isset($_GET['user']) ? intval($_GET['user']) : 0;
$filter_date = isset($_GET['date']) ? $_GET['date'] : '';
$filter_event = isset($_GET['event']) ? $_GET['event'] : '';
$filter_search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 20; // Jumlah log per halaman
$offset = ($page - 1) * $limit;

// Ambil daftar pengguna untuk filter
$users_query = "SELECT id, name, nik FROM users ORDER BY name";
$users_result = $conn->query($users_query);
$users = [];
while ($user = $users_result->fetch_assoc()) {
    $users[$user['id']] = $user;
}

// Ambil total log
$total_logs = getTotalLoginLogs($conn, $filter_user, $filter_date, $filter_event, $filter_search);
$total_pages = ceil($total_logs / $limit);

// Ambil log login dan logout
$login_logs = getLoginLogs($conn, $filter_user, $filter_date, $filter_event, $limit, $offset, $filter_search);

// Cek apakah ada file log keamanan
$security_log_file = '../logs/security.log';
$security_log_content = '';
if (file_exists($security_log_file)) {
    // Ambil 50 baris terakhir dari file log
    $security_log_lines = file($security_log_file);
    $security_log_lines = array_slice($security_log_lines, -50);
    $security_log_content = implode('', $security_log_lines);
}

// Cek apakah ada file log error
$error_log_file = '../logs/error.log';
$error_log_content = '';
if (file_exists($error_log_file)) {
    // Ambil 50 baris terakhir dari file log
    $error_log_lines = file($error_log_file);
    $error_log_lines = array_slice($error_log_lines, -50);
    $error_log_content = implode('', $error_log_lines);
}

// Tampilkan halaman
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    :root {
        --primary-color: #BF0000;
        --primary-color-dark: #900000;
        --primary-color-light: rgba(191, 0, 0, 0.1);
        --primary-color-lighter: rgba(191, 0, 0, 0.05);
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --info-color: #17a2b8;
        --dark-color: #343a40;
        --light-color: #f8f9fa;
        --border-radius: 10px;
        --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        --transition: all 0.3s ease;
    }

    /* Dashboard Container */
    .dashboard-container {
        padding: 20px;
        background-color: #f5f5f5;
        min-height: calc(100vh - 60px);
    }

    /* Stats Cards */
    .stats-card {
        background-color: #fff;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 20px;
        margin-bottom: 20px;
        transition: var(--transition);
        border-left: 4px solid var(--primary-color);
        position: relative;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: radial-gradient(circle, var(--primary-color-light) 0%, transparent 70%);
        border-radius: 50%;
        opacity: 0.5;
        z-index: 0;
    }

    .stats-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        position: relative;
        z-index: 1;
    }

    .stats-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }

    .stats-value {
        font-size: 28px;
        font-weight: 700;
        color: var(--primary-color);
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        transition: var(--transition);
    }

    .stats-card:hover .stats-value {
        transform: scale(1.1);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    /* Action Buttons */
    .action-buttons {
        margin-bottom: 20px;
    }

    .action-buttons .btn {
        padding: 10px 20px;
        border-radius: 30px;
        font-weight: 600;
        transition: var(--transition);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .action-buttons .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
    }

    .action-buttons .btn i {
        margin-right: 8px;
    }

    /* Tables */
    .table-responsive {
        overflow-x: auto;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        background-color: #fff;
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        background-color: var(--primary-color-light);
        color: var(--dark-color);
        font-weight: 600;
        border-bottom: 2px solid var(--primary-color);
        padding: 12px 15px;
    }

    .table tbody tr {
        transition: var(--transition);
    }

    .table tbody tr:hover {
        background-color: var(--primary-color-lighter);
    }

    .table tbody td {
        padding: 12px 15px;
        vertical-align: middle;
    }

    /* Log Container */
    .log-container {
        background-color: #2b2b2b;
        color: #f8f8f8;
        border-radius: var(--border-radius);
        padding: 15px;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        overflow-x: auto;
        white-space: pre-wrap;
        max-height: 300px;
        overflow-y: auto;
        box-shadow: var(--box-shadow);
        border: 1px solid #444;
    }

    .log-container::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    .log-container::-webkit-scrollbar-thumb {
        background-color: #666;
        border-radius: 4px;
    }

    .log-container::-webkit-scrollbar-track {
        background-color: #333;
    }

    /* Status Colors */
    .status-success {
        color: var(--success-color);
        font-weight: 600;
    }

    .status-failed {
        color: var(--danger-color);
        font-weight: 600;
    }

    .status-warning {
        color: var(--warning-color);
        font-weight: 600;
    }

    /* Tabs */
    .nav-tabs {
        border-bottom: none;
        margin-bottom: 0;
    }

    .nav-tabs .nav-item {
        margin-bottom: 0;
    }

    .nav-tabs .nav-link {
        border: none;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        padding: 12px 20px;
        font-weight: 600;
        color: #555;
        transition: var(--transition);
        position: relative;
    }

    .nav-tabs .nav-link i {
        margin-right: 8px;
    }

    .nav-tabs .nav-link:hover {
        background-color: var(--primary-color-lighter);
        color: var(--primary-color);
    }

    .nav-tabs .nav-link.active {
        background-color: #fff;
        color: var(--primary-color);
        border-top: 3px solid var(--primary-color);
    }

    .tab-content {
        padding: 25px;
        background-color: #fff;
        border-radius: 0 0 var(--border-radius) var(--border-radius);
        box-shadow: var(--box-shadow);
    }

    /* Badges */
    .badge {
        padding: 6px 10px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 12px;
    }

    /* Log Entry Styles */
    .log-entry {
        transition: all 0.2s ease;
    }

    .log-entry:hover {
        background-color: #f8f9fa;
    }

    .badge i {
        margin-right: 3px;
    }

    .badge-success {
        background-color: #28a745;
    }

    .badge-danger {
        background-color: #dc3545;
    }

    .badge-info {
        background-color: #17a2b8;
    }

    .badge-warning {
        background-color: #ffc107;
        color: #212529;
    }

    .badge-secondary {
        background-color: #6c757d;
    }

    .user-info strong {
        display: block;
        margin-bottom: 2px;
    }

    code {
        background-color: #f8f9fa;
        color: #e83e8c;
        padding: 2px 4px;
        border-radius: 3px;
    }

    .d-flex {
        display: flex;
    }

    .flex-column {
        flex-direction: column;
    }

    .fw-bold {
        font-weight: bold;
    }

    .py-4 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem;
    }

    .mt-1 {
        margin-top: 0.25rem;
    }

    .mb-0 {
        margin-bottom: 0;
    }

    .mb-2 {
        margin-bottom: 0.5rem;
    }

    .me-1 {
        margin-right: 0.25rem;
    }

    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .fade-in {
        animation: fadeIn 0.5s ease forwards;
    }

    /* Responsive Adjustments */
    @media (max-width: 992px) {
        .stats-value {
            font-size: 24px;
        }

        .nav-tabs .nav-link {
            padding: 10px 15px;
        }
    }

    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .stats-card {
            margin-bottom: 15px;
        }

        .nav-tabs .nav-link {
            padding: 8px 12px;
            font-size: 14px;
        }

        .nav-tabs .nav-link i {
            margin-right: 5px;
        }

        .tab-content {
            padding: 15px;
        }

        .table thead th, .table tbody td {
            padding: 10px;
        }
    }

    @media (max-width: 576px) {
        .dashboard-container {
            padding: 10px;
        }

        .stats-card {
            padding: 15px;
        }

        .stats-title {
            font-size: 16px;
        }

        .stats-value {
            font-size: 20px;
        }

        .nav-tabs {
            display: flex;
            overflow-x: auto;
            white-space: nowrap;
            flex-wrap: nowrap;
        }

        .nav-tabs .nav-item {
            flex: 0 0 auto;
        }

        .nav-tabs .nav-link {
            padding: 8px 10px;
            font-size: 13px;
        }

        .nav-tabs .nav-link i {
            margin-right: 3px;
        }
    }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container-fluid dashboard-container">
    <h1 class="mb-4"><i class="fas fa-shield-alt"></i> Monitoring Keamanan</h1>

    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">Percobaan Login Gagal</div>
                    <i class="fas fa-sign-in-alt text-danger"></i>
                </div>
                <div class="stats-value"><?= $stats['failed_logins'] ?></div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">Akun Terkunci</div>
                    <i class="fas fa-user-lock text-warning"></i>
                </div>
                <div class="stats-value"><?= $stats['locked_accounts'] ?></div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">Serangan Terdeteksi</div>
                    <i class="fas fa-bug text-danger"></i>
                </div>
                <div class="stats-value"><?= $stats['detected_attacks'] ?></div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">Logout Hari Ini</div>
                    <i class="fas fa-sign-out-alt text-info"></i>
                </div>
                <div class="stats-value"><?= $stats['today_logouts'] ?></div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">Jenis Serangan</div>
                    <i class="fas fa-chart-pie text-primary"></i>
                </div>
                <div class="stats-value"><?= count($stats['attack_types']) ?></div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12 action-buttons">
            <a href="unknown_login_attempts.php" class="btn btn-warning">
                <i class="fas fa-user-secret"></i> Lihat Percobaan Login
            </a>
            <a href="security_check.php" class="btn btn-primary ms-2">
                <i class="fas fa-shield-alt"></i> Periksa Keamanan
            </a>
            <button type="button" class="btn btn-info ms-2" id="refreshData">
                <i class="fas fa-sync-alt"></i> Refresh Data
            </button>
            <button type="button" class="btn btn-success ms-2" id="exportData">
                <i class="fas fa-file-export"></i> Export Data
            </button>
        </div>
    </div>

    <!-- Alert untuk notifikasi -->
    <div class="alert alert-success alert-dismissible fade show d-none" role="alert" id="notificationAlert">
        <i class="fas fa-info-circle me-2"></i> <span id="notificationMessage"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <ul class="nav nav-tabs" id="securityTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="logins-tab" data-bs-toggle="tab" data-bs-target="#logins" type="button" role="tab" aria-controls="logins" aria-selected="true">
                <i class="fas fa-sign-in-alt"></i> Login
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="login-logs-tab" data-bs-toggle="tab" data-bs-target="#login-logs" type="button" role="tab" aria-controls="login-logs" aria-selected="false">
                <i class="fas fa-history"></i> Log Login & Logout
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="activities-tab" data-bs-toggle="tab" data-bs-target="#activities" type="button" role="tab" aria-controls="activities" aria-selected="false">
                <i class="fas fa-shield-alt"></i> Aktivitas Keamanan
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="rate-limits-tab" data-bs-toggle="tab" data-bs-target="#rate-limits" type="button" role="tab" aria-controls="rate-limits" aria-selected="false">
                <i class="fas fa-tachometer-alt"></i> Rate Limits
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats" type="button" role="tab" aria-controls="stats" aria-selected="false">
                <i class="fas fa-chart-bar"></i> Statistik
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab" aria-controls="logs" aria-selected="false">
                <i class="fas fa-file-alt"></i> Log Files
            </button>
        </li>
    </ul>

    <div class="tab-content" id="securityTabsContent">
        <!-- Tab Login Logs -->
        <div class="tab-pane fade" id="login-logs" role="tabpanel" aria-labelledby="login-logs-tab">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Tentang Log Login & Logout</strong>
                        <p>Halaman ini menampilkan riwayat aktivitas login dan logout pengguna. Anda dapat memfilter berdasarkan pengguna, tanggal, atau jenis aktivitas.</p>
                    </div>
                </div>
            </div>

            <!-- Filter Form -->
            <form method="GET" action="" class="mb-4">
                <input type="hidden" name="tab" value="login-logs">
                <div class="row">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Pengguna</label>
                            <select class="form-control" name="user">
                                <option value="0">Semua Pengguna</option>
                                <?php foreach ($users as $user_id => $user): ?>
                                    <option value="<?= $user_id ?>" <?= $filter_user == $user_id ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($user['name']) ?> (<?= htmlspecialchars($user['nik']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Tanggal</label>
                            <input type="date" class="form-control" name="date" value="<?= htmlspecialchars($filter_date) ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Jenis Aktivitas</label>
                            <select class="form-control" name="event">
                                <option value="">Semua Aktivitas</option>
                                <option value="LOGIN_SUCCESS" <?= in_array($filter_event, ['login_success', 'LOGIN_SUCCESS']) ? 'selected' : '' ?>>Login Berhasil</option>
                                <option value="LOGIN_FAILED" <?= in_array($filter_event, ['login_failed', 'LOGIN_FAILED']) ? 'selected' : '' ?>>Login Gagal</option>
                                <option value="LOGOUT" <?= in_array($filter_event, ['logout', 'LOGOUT']) ? 'selected' : '' ?>>Logout</option>
                                <option value="LOGIN_UNKNOWN_USER" <?= $filter_event == 'LOGIN_UNKNOWN_USER' ? 'selected' : '' ?>>Login Tidak Dikenal</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Pencarian</label>
                            <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($filter_search) ?>" placeholder="Cari nama, NIK, deskripsi, atau event...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Cari & Filter
                                </button>
                                <a href="security_monitoring.php?tab=login-logs" class="btn btn-secondary">
                                    <i class="fas fa-sync"></i> Reset
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Log Table -->
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="bg-light">
                        <tr>
                            <th style="width: 18%">Waktu</th>
                            <th style="width: 20%">Pengguna</th>
                            <th style="width: 25%">Aktivitas</th>
                            <th style="width: 12%">IP Address</th>
                            <th style="width: 25%">Perangkat</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($login_logs)): ?>
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <i class="fas fa-info-circle text-muted mb-2" style="font-size: 2rem;"></i>
                                    <p class="mb-0">Tidak ada data log yang ditemukan</p>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($login_logs as $log): ?>
                                <tr class="log-entry">
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold"><?= date('d-m-Y', strtotime($log['created_at'])) ?></span>
                                            <small class="text-muted"><?= date('H:i:s', strtotime($log['created_at'])) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($log['user_id'] > 0 && isset($log['user_name'])): ?>
                                            <div class="user-info">
                                                <strong><?= htmlspecialchars($log['user_name']) ?></strong>
                                                <?php if (isset($log['user_nik'])): ?>
                                                    <small class="text-muted">NIK: <?= htmlspecialchars($log['user_nik']) ?></small>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted"><i class="fas fa-user-slash me-1"></i> Pengguna tidak dikenal</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $badge_class = 'badge-secondary';
                                        $event_text = $log['event_type'];
                                        $icon = 'fa-info-circle';

                                        if ($log['event_type'] == 'login_success' || $log['event_type'] == 'LOGIN_SUCCESS') {
                                            $badge_class = 'badge-success';
                                            $event_text = 'Login Berhasil';
                                            $icon = 'fa-check-circle';
                                        } elseif ($log['event_type'] == 'login_failed' || $log['event_type'] == 'LOGIN_FAILED') {
                                            $badge_class = 'badge-danger';
                                            $event_text = 'Login Gagal';
                                            $icon = 'fa-times-circle';
                                        } elseif ($log['event_type'] == 'logout' || $log['event_type'] == 'LOGOUT') {
                                            $badge_class = 'badge-info';
                                            $event_text = 'Logout';
                                            $icon = 'fa-sign-out-alt';
                                        } elseif (strpos($log['event_type'], 'LOGIN_UNKNOWN_USER') !== false) {
                                            $badge_class = 'badge-warning';
                                            $event_text = 'Login Tidak Dikenal';
                                            $icon = 'fa-question-circle';
                                        }
                                        ?>
                                        <span class="badge <?= $badge_class ?>"><i class="fas <?= $icon ?>"></i> <?= $event_text ?></span>
                                        <?php if (!empty($log['description'])): ?>
                                            <div class="mt-1">
                                                <small class="text-muted"><?= htmlspecialchars($log['description']) ?></small>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <code><?= htmlspecialchars($log['ip_address']) ?></code>
                                    </td>
                                    <td>
                                        <?php
                                        $user_agent = $log['user_agent'];
                                        $browser_icon = 'fa-globe';

                                        if (strpos($user_agent, 'Chrome') !== false) {
                                            $browser_icon = 'fa-chrome';
                                        } elseif (strpos($user_agent, 'Firefox') !== false) {
                                            $browser_icon = 'fa-firefox';
                                        } elseif (strpos($user_agent, 'Safari') !== false) {
                                            $browser_icon = 'fa-safari';
                                        } elseif (strpos($user_agent, 'Edge') !== false) {
                                            $browser_icon = 'fa-edge';
                                        } elseif (strpos($user_agent, 'MSIE') !== false || strpos($user_agent, 'Trident') !== false) {
                                            $browser_icon = 'fa-internet-explorer';
                                        }
                                        ?>
                                        <div>
                                            <i class="fab <?= $browser_icon ?>"></i>
                                            <small class="text-muted" style="max-width: 250px;" title="<?= htmlspecialchars($user_agent) ?>">
                                                <?= htmlspecialchars($user_agent) ?>
                                            </small>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="mt-3">
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <?php
                            // Build query string for pagination
                            $query_params = [];
                            if ($filter_user) $query_params[] = "user=$filter_user";
                            if ($filter_date) $query_params[] = "date=$filter_date";
                            if ($filter_event) $query_params[] = "event=$filter_event";
                            if ($filter_search) $query_params[] = "search=" . urlencode($filter_search);
                            $query_string = !empty($query_params) ? "&" . implode("&", $query_params) : "";
                            ?>

                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?tab=login-logs&page=1<?= $query_string ?>">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?tab=login-logs&page=<?= $page - 1 ?><?= $query_string ?>">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            for ($i = $start_page; $i <= $end_page; $i++):
                            ?>
                                <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?tab=login-logs&page=<?= $i ?><?= $query_string ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?tab=login-logs&page=<?= $page + 1 ?><?= $query_string ?>">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?tab=login-logs&page=<?= $total_pages ?><?= $query_string ?>">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <div class="text-center text-muted">
                        Menampilkan <?= count($login_logs) ?> dari <?= $total_logs ?> log
                        (Halaman <?= $page ?> dari <?= $total_pages ?>)
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Tab Login -->
        <div class="tab-pane fade show active" id="logins" role="tabpanel" aria-labelledby="logins-tab">
            <h3 class="mb-3">Percobaan Login Terbaru</h3>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>IP Address</th>
                            <th>User Agent</th>
                            <th>Waktu</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_logins as $login): ?>
                        <tr>
                            <td><?= $login['id'] ?></td>
                            <td><?= $login['user_name'] ?? get_user_name($conn, $login['user_id']) ?></td>
                            <td><?= $login['ip_address'] ?></td>
                            <td title="<?= $login['user_agent'] ?>"><?= substr($login['user_agent'], 0, 50) ?>...</td>
                            <td><?= format_date($login['attempt_time']) ?></td>
                            <td class="<?= $login['success'] ? 'status-success' : 'status-failed' ?>">
                                <?= $login['success'] ? 'Berhasil' : 'Gagal' ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Tab Aktivitas Keamanan -->
        <div class="tab-pane fade" id="activities" role="tabpanel" aria-labelledby="activities-tab">
            <h3 class="mb-3">Aktivitas Keamanan Terbaru</h3>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Jenis Event</th>
                            <th>Deskripsi</th>
                            <th>IP Address</th>
                            <th>Waktu</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_activities as $activity): ?>
                        <tr>
                            <td><?= $activity['id'] ?></td>
                            <td><?= get_user_name($conn, $activity['user_id']) ?></td>
                            <td><?= $activity['event_type'] ?></td>
                            <td title="<?= $activity['description'] ?>"><?= substr($activity['description'], 0, 50) ?>...</td>
                            <td><?= $activity['ip_address'] ?></td>
                            <td><?= format_date($activity['created_at']) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Tab Rate Limits -->
        <div class="tab-pane fade" id="rate-limits" role="tabpanel" aria-labelledby="rate-limits-tab">
            <h3 class="mb-3">Rate Limits Terbaru</h3>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Jenis Aksi</th>
                            <th>IP Address</th>
                            <th>Waktu</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_rate_limits as $rate_limit): ?>
                        <tr>
                            <td><?= $rate_limit['id'] ?></td>
                            <td><?= get_user_name($conn, $rate_limit['user_id']) ?></td>
                            <td><?= $rate_limit['action_type'] ?></td>
                            <td><?= $rate_limit['ip_address'] ?></td>
                            <td><?= format_date($rate_limit['attempt_time']) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Tab Statistik -->
        <div class="tab-pane fade" id="stats" role="tabpanel" aria-labelledby="stats-tab">
            <div class="row">
                <div class="col-md-6 mb-4">
                    <h3 class="mb-3">Statistik Login per Hari (7 Hari Terakhir)</h3>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Total Percobaan</th>
                                    <th>Berhasil</th>
                                    <th>Gagal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($daily_stats as $stat): ?>
                                <tr>
                                    <td><?= date('d M Y', strtotime($stat['date'])) ?></td>
                                    <td><?= $stat['count'] ?></td>
                                    <td class="status-success"><?= $stat['success_count'] ?></td>
                                    <td class="status-failed"><?= $stat['count'] - $stat['success_count'] ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <h3 class="mb-3">Statistik Logout per Hari (7 Hari Terakhir)</h3>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Total Logout</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($daily_logout_stats)): ?>
                                <tr>
                                    <td colspan="2" class="text-center text-muted">Tidak ada data logout</td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($daily_logout_stats as $stat): ?>
                                    <tr>
                                        <td><?= date('d M Y', strtotime($stat['date'])) ?></td>
                                        <td class="status-warning"><?= $stat['logout_count'] ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <h3 class="mb-3">IP Address dengan Percobaan Login Gagal Terbanyak</h3>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>IP Address</th>
                                    <th>Jumlah Percobaan Gagal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($top_failed_ips as $ip): ?>
                                <tr>
                                    <td><?= $ip['ip_address'] ?></td>
                                    <td class="status-failed"><?= $ip['count'] ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <h3 class="mb-3">User dengan Percobaan Login Gagal Terbanyak</h3>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>User ID</th>
                                    <th>Nama</th>
                                    <th>Jumlah Percobaan Gagal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($top_failed_users as $user): ?>
                                <tr>
                                    <td><?= $user['user_id'] ?></td>
                                    <td><?= $user['user_name'] ?></td>
                                    <td class="status-failed"><?= $user['count'] ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <h3 class="mb-3">Jenis Serangan Terdeteksi</h3>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Jenis Serangan</th>
                                    <th>Jumlah</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($stats['attack_types'] as $type => $count): ?>
                                <tr>
                                    <td><?= $type ?></td>
                                    <td class="status-failed"><?= $count ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab Log Files -->
        <div class="tab-pane fade" id="logs" role="tabpanel" aria-labelledby="logs-tab">
            <div class="row">
                <div class="col-md-6 mb-4">
                    <h3 class="mb-3">Security Log</h3>
                    <?php if ($security_log_content): ?>
                    <div class="log-container">
                        <?= htmlspecialchars($security_log_content) ?>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info">
                        File log keamanan tidak ditemukan atau kosong.
                    </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6 mb-4">
                    <h3 class="mb-3">Error Log</h3>
                    <?php if ($error_log_content): ?>
                    <div class="log-container">
                        <?= htmlspecialchars($error_log_content) ?>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info">
                        File log error tidak ditemukan atau kosong.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Cek apakah ada parameter tab di URL
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');

        // Aktifkan tab berdasarkan parameter URL atau tab pertama jika tidak ada parameter
        let tabToActivate;
        if (tabParam) {
            tabToActivate = document.querySelector(`#securityTabs .nav-link[data-bs-target="#${tabParam}"]`);
        }

        if (!tabToActivate) {
            tabToActivate = document.querySelector('#securityTabs .nav-link');
        }

        const tab = new bootstrap.Tab(tabToActivate);
        tab.show();

        // Fungsi untuk menampilkan notifikasi
        function showNotification(message, type = 'success') {
            const alertElement = document.getElementById('notificationAlert');
            const messageElement = document.getElementById('notificationMessage');

            // Set pesan dan tipe alert
            messageElement.textContent = message;
            alertElement.classList.remove('alert-success', 'alert-danger', 'alert-warning', 'alert-info');
            alertElement.classList.add(`alert-${type}`);

            // Tampilkan alert
            alertElement.classList.remove('d-none');

            // Sembunyikan alert setelah 5 detik
            setTimeout(() => {
                alertElement.classList.add('d-none');
            }, 5000);
        }

        // Fungsi untuk refresh data
        document.getElementById('refreshData').addEventListener('click', function() {
            // Animasi loading
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memuat Data...';
            this.disabled = true;

            // Reload halaman setelah 1 detik
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });

        // Fungsi untuk export data
        document.getElementById('exportData').addEventListener('click', function() {
            // Tampilkan modal export
            showExportModal();
        });

        // Fungsi untuk menampilkan modal export
        function showExportModal() {
            // Buat modal
            const modalHTML = `
                <div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="exportModalLabel">Export Data Keamanan</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="exportForm">
                                    <div class="mb-3">
                                        <label for="exportType" class="form-label">Pilih Jenis Data</label>
                                        <select class="form-select" id="exportType" required>
                                            <option value="">-- Pilih Jenis Data --</option>
                                            <option value="login_attempts">Percobaan Login</option>
                                            <option value="security_logs">Aktivitas Keamanan</option>
                                            <option value="rate_limits">Rate Limits</option>
                                            <option value="all">Semua Data</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="exportFormat" class="form-label">Format Export</label>
                                        <select class="form-select" id="exportFormat" required>
                                            <option value="csv">CSV</option>
                                            <option value="excel">Excel</option>
                                            <option value="pdf">PDF</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="dateRange" class="form-label">Rentang Waktu</label>
                                        <select class="form-select" id="dateRange" required>
                                            <option value="today">Hari Ini</option>
                                            <option value="yesterday">Kemarin</option>
                                            <option value="last7days">7 Hari Terakhir</option>
                                            <option value="last30days">30 Hari Terakhir</option>
                                            <option value="thismonth">Bulan Ini</option>
                                            <option value="lastmonth">Bulan Lalu</option>
                                            <option value="all">Semua Waktu</option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                <button type="button" class="btn btn-primary" id="doExport">Export</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Tambahkan modal ke body
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Tampilkan modal
            const exportModal = new bootstrap.Modal(document.getElementById('exportModal'));
            exportModal.show();

            // Event listener untuk tombol export
            document.getElementById('doExport').addEventListener('click', function() {
                const exportType = document.getElementById('exportType').value;
                const exportFormat = document.getElementById('exportFormat').value;
                const dateRange = document.getElementById('dateRange').value;

                if (!exportType || !exportFormat || !dateRange) {
                    showNotification('Silakan lengkapi semua field', 'danger');
                    return;
                }

                // Simulasi export
                exportModal.hide();
                showNotification(`Data ${exportType} berhasil diexport dalam format ${exportFormat}`, 'success');

                // Hapus modal setelah ditutup
                document.getElementById('exportModal').addEventListener('hidden.bs.modal', function() {
                    this.remove();
                });
            });
        }

        // Tambahkan event listener untuk baris tabel
        const tableRows = document.querySelectorAll('.table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('click', function() {
                // Toggle class selected
                this.classList.toggle('selected');

                // Jika ada class selected, tambahkan background color
                if (this.classList.contains('selected')) {
                    this.style.backgroundColor = 'var(--primary-color-lighter)';
                } else {
                    this.style.backgroundColor = '';
                }
            });
        });

        // Tambahkan pencarian untuk setiap tabel
        const tables = document.querySelectorAll('.table');
        tables.forEach((table, index) => {
            // Buat input pencarian
            const searchContainer = document.createElement('div');
            searchContainer.className = 'mb-3 position-relative';
            searchContainer.innerHTML = `
                <input type="text" class="form-control" placeholder="Cari..." id="searchTable${index}">
                <i class="fas fa-search position-absolute" style="right: 10px; top: 10px;"></i>
            `;

            // Tambahkan input pencarian sebelum tabel
            table.parentNode.insertBefore(searchContainer, table);

            // Event listener untuk input pencarian
            const searchInput = document.getElementById(`searchTable${index}`);
            searchInput.addEventListener('keyup', function() {
                const searchText = this.value.toLowerCase();
                const rows = table.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(searchText)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        });

        // Tambahkan animasi fade-in untuk semua elemen
        const fadeElements = document.querySelectorAll('.stats-card, .table-responsive, .log-container');
        fadeElements.forEach((element, index) => {
            element.classList.add('fade-in');
            element.style.animationDelay = `${index * 0.1}s`;
        });
    });
</script>
</body>
</html>
