<?php
session_start();
require_once '../includes/auth_check.php'; // Ensure user is logged in and is an admin
require_once '../config/config.php'; // Database connection and configuration

// Konversi koneksi mysqli ke PDO
$pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
require_once '../config/activity_logger.php'; // Include the activity logger

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Akses ditolak. Anda tidak memiliki izin untuk melakukan tindakan ini.']);
    exit;
}

// Validate input
if (!isset($_POST['history_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'ID riwayat tidak diberikan.']);
    exit;
}

$historyId = intval($_POST['history_id']);
$adminUsername = $_SESSION['name'] ?? 'Admin Tidak Dikenal'; // User performing the deletion

// Fetch the history record to log details before deletion
try {
    $stmt = $pdo->prepare("SELECT nik, action_type FROM karyawan_history WHERE history_id = ?");
    $stmt->execute([$historyId]);
    $history = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$history) {
        throw new Exception("Data riwayat tidak ditemukan.");
    }

    $nik = $history['nik'];
    $actionType = $history['action_type'];

    // Begin Transaction
    $pdo->beginTransaction();

    // Delete the history record
    $deleteStmt = $pdo->prepare("DELETE FROM karyawan_history WHERE history_id = ?");
    $deleteSuccess = $deleteStmt->execute([$historyId]);

    if (!$deleteSuccess) {
        throw new Exception("Gagal menghapus riwayat.");
    }

    // Log the deletion action
    $userId = $_SESSION['user_id'] ?? 0;
    $logMessage = "Menghapus riwayat ID: {$historyId} (NIK: {$nik}, Aksi: {$actionType}) oleh {$adminUsername}.";
    $details = ['message' => $logMessage];
    log_activity($userId, 'Hapus Riwayat', 'Riwayat Karyawan', $details);

    // Commit Transaction
    $pdo->commit();
    
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'message' => 'Riwayat berhasil dihapus.']);

} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    // Log the database error
    error_log("Database Error during history deletion (History ID: {$historyId}): " . $e->getMessage());
    $dbErrorMsg = "Kesalahan database saat menghapus riwayat ID {$historyId}: " . $e->getMessage();
    $userId = $_SESSION['user_id'] ?? 0;
    $details = ['error' => $dbErrorMsg];
    log_activity($userId, 'Hapus Riwayat Gagal', 'Kesalahan Hapus Riwayat', $details);
    
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Kesalahan database saat menghapus riwayat: ' . $e->getMessage()]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    // Log the general error
    error_log("General Error during history deletion (History ID: {$historyId}): " . $e->getMessage());
    $generalErrorMsg = "Kesalahan saat menghapus riwayat ID {$historyId}: " . $e->getMessage();
    $userId = $_SESSION['user_id'] ?? 0;
    $details = ['error' => $generalErrorMsg];
    log_activity($userId, 'Hapus Riwayat Gagal', 'Kesalahan Hapus Riwayat', $details);
    
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Gagal menghapus riwayat: ' . $e->getMessage()]);
}

exit;
