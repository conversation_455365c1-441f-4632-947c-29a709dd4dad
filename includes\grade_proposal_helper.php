<?php
/**
 * Helper functions for managing grade proposals
 */

/**
 * Create a new grade proposal (for assistants)
 * 
 * @param int $class_id Class ID
 * @param int $assignment_id Assignment ID
 * @param int $student_id Student ID
 * @param int $proposed_by User ID of the assistant proposing the grade
 * @param float $proposed_grade Proposed grade
 * @param string $feedback Feedback for the student
 * @return int|bool ID of the new proposal or false on failure
 */
function createGradeProposal($class_id, $assignment_id, $student_id, $proposed_by, $proposed_grade, $feedback) {
    global $conn;
    
    // Check if proposer is an assistant in this class
    if (!isAssistant($proposed_by, $class_id)) {
        return false;
    }
    
    $query = "INSERT INTO training_grade_proposals 
              (class_id, assignment_id, student_id, proposed_by, proposed_grade, feedback) 
              VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("iiiids", $class_id, $assignment_id, $student_id, $proposed_by, $proposed_grade, $feedback);
    
    if ($stmt->execute()) {
        $proposal_id = $stmt->insert_id;
        
        // Notify instructors about the new grade proposal
        $assignment_query = "SELECT title FROM training_assignments WHERE id = ?";
        $stmt = $conn->prepare($assignment_query);
        $stmt->bind_param("i", $assignment_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $assignment = $result->fetch_assoc();
        
        $student_query = "SELECT name FROM users WHERE id = ?";
        $stmt = $conn->prepare($student_query);
        $stmt->bind_param("i", $student_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $student = $result->fetch_assoc();
        
        $title = "Pengajuan Nilai Baru";
        $message = "Asisten telah mengajukan nilai untuk tugas '{$assignment['title']}' milik {$student['name']}. Mohon ditinjau.";
        notifyInstructors($class_id, $title, $message, 'info');
        
        return $proposal_id;
    }
    
    return false;
}

/**
 * Get pending grade proposals for a class
 * 
 * @param int $class_id Class ID
 * @return array Array of pending grade proposals
 */
function getPendingGradeProposals($class_id) {
    global $conn;
    
    $query = "SELECT p.*, 
                     a.title as assignment_title,
                     s.name as student_name,
                     u.name as proposed_by_name
              FROM training_grade_proposals p
              JOIN training_assignments a ON p.assignment_id = a.id
              JOIN users s ON p.student_id = s.id
              JOIN users u ON p.proposed_by = u.id
              WHERE p.class_id = ? AND p.status = 'pending'
              ORDER BY p.created_at DESC";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $class_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $proposals = [];
    while ($row = $result->fetch_assoc()) {
        $proposals[] = $row;
    }
    
    return $proposals;
}

/**
 * Review a grade proposal (approve or reject)
 * 
 * @param int $proposal_id Proposal ID
 * @param int $reviewer_id User ID of the instructor reviewing the proposal
 * @param string $status New status ('approved' or 'rejected')
 * @param string $comments Review comments
 * @return bool True if the review was successful
 */
function reviewGradeProposal($proposal_id, $reviewer_id, $status, $comments = '') {
    global $conn;
    
    // Get proposal details
    $query = "SELECT * FROM training_grade_proposals WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $proposal_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $proposal = $result->fetch_assoc();
    
    if (!$proposal) {
        return false;
    }
    
    // Check if reviewer is an instructor in this class
    if (!isInstructor($reviewer_id, $proposal['class_id'])) {
        return false;
    }
    
    // Update proposal status
    $query = "UPDATE training_grade_proposals 
              SET status = ?, reviewed_by = ?, review_comments = ? 
              WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("sisi", $status, $reviewer_id, $comments, $proposal_id);
    $result = $stmt->execute();
    
    if ($result) {
        // If approved, update the actual grade
        if ($status == 'approved') {
            $query = "UPDATE training_assignment_submissions 
                      SET grade = ?, feedback = ?, graded_by = ?, graded_at = NOW() 
                      WHERE assignment_id = ? AND user_id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("dsiii", $proposal['proposed_grade'], $proposal['feedback'], 
                             $reviewer_id, $proposal['assignment_id'], $proposal['student_id']);
            $stmt->execute();
        }
        
        // Notify the assistant who proposed the grade
        $title = "Pengajuan Nilai Ditinjau";
        $status_text = ($status == 'approved') ? 'disetujui' : 'ditolak';
        $message = "Pengajuan nilai Anda telah $status_text oleh instruktur.";
        if (!empty($comments)) {
            $message .= " Komentar: $comments";
        }
        createNotification($proposal['proposed_by'], $proposal['class_id'], $title, $message, 
                          ($status == 'approved') ? 'success' : 'warning');
        
        return true;
    }
    
    return false;
}

/**
 * Check if a user can grade assignments directly
 * 
 * @param int $user_id User ID
 * @param int $class_id Class ID
 * @return bool True if user can grade directly
 */
function canGradeDirectly($user_id, $class_id) {
    return isInstructor($user_id, $class_id);
}

/**
 * Check if a user can propose grades
 * 
 * @param int $user_id User ID
 * @param int $class_id Class ID
 * @return bool True if user can propose grades
 */
function canProposeGrades($user_id, $class_id) {
    return isAssistant($user_id, $class_id);
}
