<?php
session_start();

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}
include '../config/config.php';
?>
<!DOCTYPE html>
<html lang="en">

<?php include '../config/head.php'; ?>
<script>
        function showNotification(type, message) {
            let notification = document.createElement('div');
            notification.className = type === 'error' ? 'error-message' : 'success-message';
            notification.innerHTML = `<p>${message}</p>`;
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 5000);
        }
    </script>
    <style>
        .error-message, .success-message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 16px;
            z-index: 1000;
        }
        .full-width {
    width: 97%;
    height: 30px;
    margin-bottom: 20px;

}
input[type="date"] {
 /* Agar selebar input lain */
    padding: 10px; /* Menyamakan padding dengan input lain */
    font-size: 12px; /* Menyamakan ukuran font */
    font-weight: bold;
    border: 1px solid #ccc; /* Menyamakan border */
    border-radius: 5px; /* Membuat sudut melengkung */
    background-color: white;
    color: black;
}


        .error-message { background: #ff4d4d; color: white; }
        .success-message { background: #4caf50; color: white; }
    </style>
<body>
    <div class="container-form">
<?php if (isset($_SESSION['errors'])): ?>
    <script>
        <?php foreach ($_SESSION['errors'] as $error): ?>
            showNotification('error', "<?php echo addslashes($error); ?>");
        <?php endforeach; ?>
    </script>
    <?php unset($_SESSION['errors']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['success'])): ?>
    <script>
        showNotification('success', "<?php echo addslashes($_SESSION['success']); ?>");
    </script>
    <?php unset($_SESSION['success']); ?>
<?php endif; ?>
<?php include '../config/navbar.php'; ?>
<div class="jarak-form"></div>
<div class="form-container">
    <div class="form-header">
        <h1>Form Pengajuan Training</h1>
        <p>Silakan isi form di bawah ini untuk mengajukan training.</p>
    </div>
    <form action="../config/submit.php" method="POST">
    <?php
    if (!isset($_SESSION['token'])) {
        $_SESSION['token'] = bin2hex(random_bytes(32));
    }
    ?>
    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['token']; ?>">

    <div class="form-group">
        <label class="required" for="full_name">Nama Lengkap</label>
        <input type="text" id="full_name" name="full_name" required placeholder="Masukkan nama lengkap Anda">
    </div>
    <div class="form-group">
        <label class="required" for="email">Email</label>
        <input type="email" id="email" name="email" required placeholder="Masukkan email Anda">
    </div>
    <div class="form-group">
        <label class="required" for="phone">Telepon</label>
        <input type="tel" id="phone" name="phone" required placeholder="Masukkan nomor telepon Anda" pattern="\d{10,15}" title="Nomor telepon harus berisi 10 hingga 15 digit angka">
    </div>
    <div class="form-group">
        <label class="required" for="training_topic">Topik Training</label>
        <input type="text" id="training_topic" name="training_topic" required placeholder="Masukkan topik training yang diinginkan">
    </div>
    <div class="form-group">
        <label class="required" for="start_date">Tanggal Training</label>
        <input type="date" id="start_date" name="start_date" class="full-width" required>
    </div>

    <div class="form-group">
        <div class="expanded">
        <label for="additional_info">Informasi Tambahan</label>
        <textarea id="additional_info" name="additional_info" rows="4" required placeholder="Masukkan informasi tambahan"></textarea>
    </div>
    </div>
    <button type="submit">Kirim Pengajuan</button>
    </form>
</div>
</div>
<?php include '../config/footer.php'; ?>
</body>
</html>
