<?php
/**
 * Migration Script for Announcements
 * This script migrates old announcements to the new recipient system
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header("Location: ../login.php");
    exit();
}

$migration_results = [];

if (isset($_POST['migrate'])) {
    try {
        // Start transaction
        $conn->begin_transaction();
        
        // Get all announcements that don't have recipients yet
        $announcements_query = "SELECT a.id, a.target_role 
                               FROM announcements a 
                               WHERE NOT EXISTS (
                                   SELECT 1 FROM announcement_recipients ar 
                                   WHERE ar.announcement_id = a.id
                               )";
        $result = $conn->query($announcements_query);
        
        $migrated_count = 0;
        
        while ($announcement = $result->fetch_assoc()) {
            $announcement_id = $announcement['id'];
            $target_role = $announcement['target_role'];
            
            if (empty($target_role)) {
                // No target role = send to all active users
                $users_query = "SELECT id FROM users WHERE is_active = 1";
                $users_result = $conn->query($users_query);
                
                while ($user = $users_result->fetch_assoc()) {
                    $insert_query = "INSERT IGNORE INTO announcement_recipients (announcement_id, user_id) VALUES (?, ?)";
                    $stmt = $conn->prepare($insert_query);
                    $stmt->bind_param("ii", $announcement_id, $user['id']);
                    $stmt->execute();
                    $stmt->close();
                }
                
                $migration_results[] = "Announcement ID $announcement_id: Migrated to ALL USERS";
                
            } else {
                // Has target role = send to users with that role
                $users_query = "SELECT id FROM users WHERE is_active = 1 AND role_id = ?";
                $stmt = $conn->prepare($users_query);
                $stmt->bind_param("s", $target_role);
                $stmt->execute();
                $users_result = $stmt->get_result();
                
                $role_names = [
                    '1' => 'Pemohon', '2' => 'Dept Head', '3' => 'HRD', 
                    '4' => 'GA', '5' => 'Factory Manager', '6' => 'Direktur', '99' => 'Admin'
                ];
                $role_name = $role_names[$target_role] ?? "Role $target_role";
                
                $user_count = 0;
                while ($user = $users_result->fetch_assoc()) {
                    $insert_query = "INSERT IGNORE INTO announcement_recipients (announcement_id, user_id) VALUES (?, ?)";
                    $stmt_insert = $conn->prepare($insert_query);
                    $stmt_insert->bind_param("ii", $announcement_id, $user['id']);
                    $stmt_insert->execute();
                    $stmt_insert->close();
                    $user_count++;
                }
                
                $stmt->close();
                $migration_results[] = "Announcement ID $announcement_id: Migrated to ROLE $role_name ($user_count users)";
            }
            
            $migrated_count++;
        }
        
        // Commit transaction
        $conn->commit();
        
        $migration_results[] = "<strong>Migration completed successfully! Total announcements migrated: $migrated_count</strong>";
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        $migration_results[] = "<strong>Error during migration: " . $e->getMessage() . "</strong>";
    }
}

// Get statistics
$stats_query = "SELECT 
                    COUNT(*) as total_announcements,
                    COUNT(CASE WHEN EXISTS (SELECT 1 FROM announcement_recipients ar WHERE ar.announcement_id = a.id) THEN 1 END) as with_recipients,
                    COUNT(CASE WHEN NOT EXISTS (SELECT 1 FROM announcement_recipients ar WHERE ar.announcement_id = a.id) THEN 1 END) as without_recipients
                FROM announcements a";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Migrate Announcements - Training Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-database"></i> Migrate Announcements to New System</h2>
                <p class="text-muted">Migrasi pengumuman lama ke sistem recipient baru.</p>
                
                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5><i class="fas fa-bullhorn"></i> Total Announcements</h5>
                                <h3><?= $stats['total_announcements'] ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5><i class="fas fa-check"></i> With Recipients</h5>
                                <h3><?= $stats['with_recipients'] ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <h5><i class="fas fa-exclamation"></i> Need Migration</h5>
                                <h3><?= $stats['without_recipients'] ?></h3>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Migration Form -->
                <?php if ($stats['without_recipients'] > 0): ?>
                    <div class="card mb-4">
                        <div class="card-header bg-warning">
                            <h5><i class="fas fa-exclamation-triangle"></i> Migration Required</h5>
                        </div>
                        <div class="card-body">
                            <p>Ada <strong><?= $stats['without_recipients'] ?></strong> pengumuman yang belum memiliki data recipients.</p>
                            <p>Klik tombol di bawah untuk melakukan migrasi otomatis:</p>
                            
                            <form method="POST">
                                <button type="submit" name="migrate" class="btn btn-warning" onclick="return confirm('Apakah Anda yakin ingin melakukan migrasi? Proses ini akan menambahkan data recipients berdasarkan target_role yang ada.')">
                                    <i class="fas fa-database"></i> Migrate Announcements
                                </button>
                            </form>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> 
                        <strong>All announcements have been migrated!</strong> 
                        Semua pengumuman sudah memiliki data recipients.
                    </div>
                <?php endif; ?>
                
                <!-- Migration Results -->
                <?php if (!empty($migration_results)): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-list"></i> Migration Results</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($migration_results as $result): ?>
                                <div class="alert alert-info mb-2">
                                    <?= $result ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Announcements without Recipients -->
                <?php if ($stats['without_recipients'] > 0): ?>
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list"></i> Announcements Needing Migration</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $pending_query = "SELECT a.id, a.title, a.target_role, a.created_at, u.name as creator_name
                                            FROM announcements a
                                            LEFT JOIN users u ON a.created_by = u.id
                                            WHERE NOT EXISTS (
                                                SELECT 1 FROM announcement_recipients ar 
                                                WHERE ar.announcement_id = a.id
                                            )
                                            ORDER BY a.created_at DESC";
                            $pending_result = $conn->query($pending_query);
                            
                            if ($pending_result->num_rows > 0) {
                                echo '<div class="table-responsive">';
                                echo '<table class="table table-striped">';
                                echo '<thead><tr><th>ID</th><th>Title</th><th>Target Role</th><th>Creator</th><th>Created</th></tr></thead>';
                                echo '<tbody>';
                                
                                while ($row = $pending_result->fetch_assoc()) {
                                    $role_names = [
                                        '1' => 'Pemohon', '2' => 'Dept Head', '3' => 'HRD', 
                                        '4' => 'GA', '5' => 'Factory Manager', '6' => 'Direktur', '99' => 'Admin'
                                    ];
                                    $target_role_display = empty($row['target_role']) ? 'All Users' : ($role_names[$row['target_role']] ?? 'Role ' . $row['target_role']);
                                    
                                    echo '<tr>';
                                    echo '<td>' . $row['id'] . '</td>';
                                    echo '<td>' . htmlspecialchars($row['title']) . '</td>';
                                    echo '<td><span class="badge bg-info">' . $target_role_display . '</span></td>';
                                    echo '<td>' . htmlspecialchars($row['creator_name']) . '</td>';
                                    echo '<td>' . date('d M Y H:i', strtotime($row['created_at'])) . '</td>';
                                    echo '</tr>';
                                }
                                
                                echo '</tbody></table>';
                                echo '</div>';
                            }
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="mt-3">
                    <a href="manage_announcements.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Manage Announcements
                    </a>
                    <a href="debug_announcements.php" class="btn btn-info">
                        <i class="fas fa-bug"></i> Debug Announcements
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
