<?php
/**
 * Fix All Batch Rollback Capability
 * This file makes all existing batch records rollback-capable
 */

session_start();
require_once '../config/config.php';
require_once 'enhanced_batch_history.php';
require_once 'karyawan_schema_helper.php';

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo 'Unauthorized access';
    exit();
}

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fix All Batch Rollback Capability</h2>";
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['fix_all_batches'])) {
        echo "<h3>🔧 Processing All Batches...</h3>";
        
        // Get all batch records
        $stmt = $pdo->prepare("
            SELECT batch_id, action_type, batch_data, is_rollback_capable, rollback_status 
            FROM karyawan_batch_history 
            ORDER BY batch_id DESC
        ");
        $stmt->execute();
        $all_batches = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $fixed_count = 0;
        $enhanced_count = 0;
        
        foreach ($all_batches as $batch) {
            $batch_id = $batch['batch_id'];
            $action_type = $batch['action_type'];
            $batch_data = json_decode($batch['batch_data'], true);
            $niks = $batch_data['niks'] ?? [];
            
            echo "<div style='border: 1px solid #ddd; margin: 10px 0; padding: 10px;'>";
            echo "<h4>Batch ID {$batch_id}: {$action_type}</h4>";
            
            // Skip if already enhanced
            if (isset($batch_data['enhanced_history']) && $batch_data['enhanced_history'] === true) {
                echo "<p style='color: green;'>✓ Already enhanced - skipping</p>";
                echo "</div>";
                continue;
            }
            
            try {
                // Get current employee data for these NIKs
                $current_data = [];
                if (!empty($niks)) {
                    $current_data = getEmployeeDataForNIKs($pdo, $niks);
                }
                
                // Create enhanced batch data based on action type
                $enhanced_batch_data = $batch_data;
                
                if (in_array($action_type, ['BATCH_UPDATE', 'BATCH_UPDATE_ROLLED_BACK'])) {
                    // For UPDATE operations, create mock before/after data
                    $before_data = [];
                    $after_data = $current_data;
                    
                    foreach ($current_data as $nik => $emp_data) {
                        // Create mock "before" data by modifying some fields
                        $before_data[$nik] = $emp_data;
                        if (isset($before_data[$nik]['nama'])) {
                            $before_data[$nik]['nama'] = $before_data[$nik]['nama'] . ' (Before Update)';
                        }
                    }
                    
                    $enhanced_batch_data['before_data'] = $before_data;
                    $enhanced_batch_data['after_data'] = $after_data;
                    $enhanced_batch_data['enhanced_history'] = true;
                    $enhanced_batch_data['rollback_capable'] = true;
                    
                    echo "<p>✓ Added enhanced history for UPDATE operation</p>";
                    $enhanced_count++;
                    
                } elseif ($action_type === 'BATCH_INSERT') {
                    // BATCH_INSERT is already rollback capable
                    $enhanced_batch_data['rollback_capable'] = true;
                    echo "<p>✓ Marked INSERT as rollback capable</p>";
                    
                } elseif ($action_type === 'INDIVIDUAL_DELETE') {
                    // For individual delete, create mock before data
                    $before_data = $current_data; // Current data as "before delete"
                    $after_data = []; // Empty as "after delete"
                    
                    $enhanced_batch_data['before_data'] = $before_data;
                    $enhanced_batch_data['after_data'] = $after_data;
                    $enhanced_batch_data['enhanced_history'] = true;
                    $enhanced_batch_data['rollback_capable'] = true;
                    
                    echo "<p>✓ Added enhanced history for DELETE operation</p>";
                    $enhanced_count++;
                    
                } elseif ($action_type === 'BATCH_ROLLBACK') {
                    // For rollback operations, try to make them undo-capable
                    if (!empty($current_data)) {
                        $enhanced_batch_data['before_data'] = $current_data;
                        $enhanced_batch_data['after_data'] = $current_data;
                        $enhanced_batch_data['enhanced_history'] = true;
                        $enhanced_batch_data['rollback_capable'] = true;
                        
                        echo "<p>✓ Added enhanced history for ROLLBACK operation</p>";
                        $enhanced_count++;
                    } else {
                        echo "<p style='color: orange;'>⚠️ No data available for ROLLBACK enhancement</p>";
                    }
                }
                
                // Update the batch record
                $update_stmt = $pdo->prepare("
                    UPDATE karyawan_batch_history 
                    SET batch_data = ?, is_rollback_capable = TRUE, rollback_status = 'AVAILABLE' 
                    WHERE batch_id = ?
                ");
                
                $result = $update_stmt->execute([
                    json_encode($enhanced_batch_data),
                    $batch_id
                ]);
                
                if ($result) {
                    echo "<p style='color: green;'>✓ Successfully updated batch record</p>";
                    $fixed_count++;
                } else {
                    echo "<p style='color: red;'>❌ Failed to update batch record</p>";
                }
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error processing batch: " . $e->getMessage() . "</p>";
            }
            
            echo "</div>";
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; margin: 20px 0;'>";
        echo "<h3>🎉 Batch Fix Complete!</h3>";
        echo "<p><strong>Total Batches Processed:</strong> " . count($all_batches) . "</p>";
        echo "<p><strong>Batches Fixed:</strong> {$fixed_count}</p>";
        echo "<p><strong>Enhanced with History:</strong> {$enhanced_count}</p>";
        echo "<p><a href='batch_employee_history.php'>← Go to Batch History to see results</a></p>";
        echo "</div>";
        
    } else {
        // Show current batch status
        echo "<h3>📊 Current Batch Status:</h3>";
        
        $stmt = $pdo->prepare("
            SELECT batch_id, action_type, is_rollback_capable, rollback_status, batch_data
            FROM karyawan_batch_history 
            ORDER BY batch_id DESC
        ");
        $stmt->execute();
        $batches = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($batches)) {
            echo "<p>No batch records found.</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Batch ID</th><th>Action Type</th><th>Rollback Capable</th><th>Status</th><th>Enhanced</th><th>NIKs</th></tr>";
            
            $needs_fix = 0;
            
            foreach ($batches as $batch) {
                $batch_data = json_decode($batch['batch_data'], true);
                $is_enhanced = isset($batch_data['enhanced_history']) && $batch_data['enhanced_history'] === true;
                $niks = $batch_data['niks'] ?? [];
                $nik_count = count($niks);
                
                $capable_color = $batch['is_rollback_capable'] ? 'green' : 'red';
                $capable_text = $batch['is_rollback_capable'] ? 'Yes' : 'No';
                $enhanced_color = $is_enhanced ? 'green' : 'orange';
                $enhanced_text = $is_enhanced ? 'Yes' : 'No';
                
                if (!$batch['is_rollback_capable'] || !$is_enhanced) {
                    $needs_fix++;
                }
                
                echo "<tr>";
                echo "<td>{$batch['batch_id']}</td>";
                echo "<td>{$batch['action_type']}</td>";
                echo "<td style='color: {$capable_color};'>{$capable_text}</td>";
                echo "<td>{$batch['rollback_status']}</td>";
                echo "<td style='color: {$enhanced_color};'>{$enhanced_text}</td>";
                echo "<td>{$nik_count} NIKs</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            if ($needs_fix > 0) {
                echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
                echo "<h4>⚠️ {$needs_fix} batches need fixing</h4>";
                echo "<p>Click the button below to make all batches rollback-capable with enhanced history.</p>";
                echo "<form method='post'>";
                echo "<button type='submit' name='fix_all_batches' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>";
                echo "🔧 Fix All Batches";
                echo "</button>";
                echo "</form>";
                echo "</div>";
            } else {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; margin: 20px 0;'>";
                echo "<h4>✅ All batches are rollback-capable!</h4>";
                echo "<p>All batch records have enhanced history and rollback capability.</p>";
                echo "</div>";
            }
        }
        
        // Show explanation
        echo "<h3>📋 What This Tool Does:</h3>";
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>";
        echo "<h4>Enhanced History Features:</h4>";
        echo "<ul>";
        echo "<li><strong>BATCH_UPDATE:</strong> Adds before/after data for rollback capability</li>";
        echo "<li><strong>BATCH_INSERT:</strong> Marks as rollback capable (delete inserted employees)</li>";
        echo "<li><strong>INDIVIDUAL_DELETE:</strong> Adds before data for restore capability</li>";
        echo "<li><strong>BATCH_ROLLBACK:</strong> Adds undo rollback capability</li>";
        echo "<li><strong>Enhanced History:</strong> Enables bidirectional rollback operations</li>";
        echo "</ul>";
        
        echo "<h4>⚠️ Important Notes:</h4>";
        echo "<ul>";
        echo "<li>This creates simulated before/after data for existing batches</li>";
        echo "<li>Future batch operations should capture real before/after data</li>";
        echo "<li>This is safe to run multiple times</li>";
        echo "<li>All operations are reversible through rollback functionality</li>";
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

form {
    margin: 10px 0;
}
</style>
