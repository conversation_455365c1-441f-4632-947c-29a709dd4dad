<?php
include 'config.php';
// session_start() sudah dipanggil di config.php
include 'mail.php';

if (!isset($_SESSION['temp_user_id'])) {
    header('Location: ../view/Aktivasi.php');
    exit();
}

$user_id = $_SESSION['temp_user_id'];
$message = '';
$email = ''; // Inisialisasi variabel email

// Ambil data user
$query = "SELECT * FROM users WHERE id = ? AND is_active = 0";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user) {
    header('Location: ../view/Aktivasi.php');
    exit();
}

// Jika user ditemukan, set email dari database
if ($user) {
    $email = $user['email'];
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $password = trim($_POST['new_password']);
    $confirm_password = trim($_POST['confirm_password']);
    
    // Validasi input
    if (empty($email) || empty($password) || empty($confirm_password)) {
        $message = '<div class="error-message">Semua field harus diisi!</div>';
    } elseif ($password !== $confirm_password) {
        $message = '<div class="error-message">Password tidak cocok!</div>';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = '<div class="error-message">Format email tidak valid!</div>';
    } else {
        // Cek apakah email sudah digunakan (kecuali oleh user yang sedang aktif)
        $check_email = "SELECT id FROM users WHERE email = ? AND id != ?";
        $check_stmt = $conn->prepare($check_email);
        $check_stmt->bind_param("si", $email, $user_id);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        
        if ($result->num_rows > 0) {
            $message = '<div class="error-message">Email sudah digunakan. Silakan gunakan email lain.</div>';
        } else {
            // Lanjutkan dengan proses update seperti sebelumnya
            $verification_code = sprintf("%06d", mt_rand(0, 999999));
            $expires = date('Y-m-d H:i:s', strtotime('+15 minutes'));
            
            $update_query = "UPDATE users SET 
                            email = ?, 
                            password = ?, 
                            verification_code = ?,
                            verification_expires = ?
                            WHERE id = ?";
            $update_stmt = $conn->prepare($update_query);
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $user_id = $_SESSION['temp_user_id'];
            
            $update_stmt->bind_param("ssssi", $email, $hashed_password, $verification_code, $expires, $user_id);

            if ($update_stmt->execute()) {
                // Ambil konfigurasi email dari database
                $settingsQuery = "SELECT smtp_server, smtp_port, smtp_password, sender_email, sender_name, smtp_encryption FROM settings WHERE id = 1";
                $settingsResult = $conn->query($settingsQuery);
                $settings = $settingsResult->fetch_assoc();
                
                // Kirim email verifikasi
                $mail_result = send_mail(
                    $email,
                    'Kode Verifikasi Aktivasi Akun Anda - Training Center PAS',
                    "
                    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 2px solid #a50000; border-radius: 5px; background-color: #f9f9f9;'>
                        <h2 style='color: #a50000; text-align: center;'>Kode Verifikasi Aktivasi Akun</h2>
                        <p style='color: #333; font-size: 16px;'>Berikut adalah kode verifikasi untuk aktivasi akun Anda:</p>
                        <div style='background-color: #ffffff; border: 2px solid #a50000; border-radius: 5px; padding: 15px; margin: 20px 0; text-align: center;'>
                            <h1 style='font-size: 36px; letter-spacing: 5px; color: #a50000; margin: 0; font-weight: bold;'>$verification_code</h1>
                        </div>
                        <p style='color: #333; font-size: 14px;'>Kode ini akan kadaluarsa dalam <strong>15 menit</strong>.</p>
                        <p style='color: #333; font-size: 14px;'>Jika Anda tidak meminta aktivasi akun, abaikan email ini.</p>
                        <div style='text-align: center; margin-top: 30px;'>
                            <p style='color: #333; font-size: 12px;'>Email ini dikirim secara otomatis. Mohon jangan membalas email ini.</p>
                        </div>
                    </div>
                    ",
                    $settings
                );

                if ($mail_result['success']) {
                    $_SESSION['verification_email'] = $email;
                    header('Location: verify_activation.php');
                    exit();
                } else {
                    $message = '<div class="error-message">Gagal mengirim kode verifikasi: ' . $mail_result['message'] . '</div>';
                }
            } else {
                $message = '<div class="error-message">Gagal menyimpan data!</div>';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include 'head.php'; ?>
<style>
    body {
        font-family: Arial, sans-serif;
        background-color: #f4f4f4;
        margin: 0;
        padding: 0;
    }
    .container-form {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 80vh;
    }
    .form-container {
        background: #fff;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 500px;
        text-align: center;
    }
    h2 {
        color: #333;
        margin-bottom: 20px;
        font-size: 1.8rem;
    }
    .info-box {
        background: #f9f9f9;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: left;
    }
    .info-box p {
        margin: 10px 0;
        font-size: 1.1rem;
        color: #555;
    }
    .info-box strong {
        color: #333;
        display: inline-block;
        width: 120px;
    }
    .edit-form {
        margin-top: 20px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .edit-form label {
        display: block;
        text-align: left;
        margin: 10px 0 5px;
        font-weight: bold;
        color: #555;
    }
    .edit-form .input-group {
        position: relative;
        margin-bottom: 15px;
    }
    .edit-form input {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        box-sizing: border-box;
        font-size: 14px;
    }
    .edit-form .toggle-password {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #666;
        font-size: 14px;
    }
    .btn-group {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
    }
    button {
        padding: 10px 20px;
        color: white;
        border: none;
        border-radius: 5px;
        text-decoration: none;
        font-size: 1rem;
        cursor: pointer;
        transition: background-color 0.3s;
    }
    button[type="submit"] {
        background-color: #28a745;
    }
    button[type="submit"]:hover {
        background-color: #218838;
    }
    .welcome-text {
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 20px;
    }
    .error-message, .success-message {
        padding: 10px;
        margin-bottom: 15px;
        border-radius: 5px;
        text-align: center;
    }
    .error-message {
        background: #f8d7da;
        color: #721c24;
    }
    .success-message {
        background: #d4edda;
        color: #155724;
    }
</style>
<body>
<?php include 'navbarb.php'; ?>

<div class="container-form">
    <div class="form-container">
        <h2>Aktivasi Akun</h2>
        <p class="welcome-text">Selamat datang! Silakan atur informasi dan password untuk akun Anda.</p>
            
        <?php if (!empty($message)) echo $message; ?>
        <form method="POST" class="edit-form">
            <label for="email">Email</label>
            <input type="email" 
                   name="email" 
                   id="email" 
                   value="<?php echo htmlspecialchars($email ?? ''); ?>"
                   placeholder="Masukkan email baru" 
                   required>

            <label for="new_password">Password</label>
            <div class="input-group">
                <input type="password" 
                       name="new_password" 
                       id="new_password" 
                       placeholder="Masukkan password baru" 
                       required>
                <span class="toggle-password" onclick="togglePassword('new_password')">
                    <i class="far fa-eye"></i>
                </span>
            </div>

            <label for="confirm_password">Konfirmasi Password</label>
            <div class="input-group">
                <input type="password" 
                       name="confirm_password" 
                       id="confirm_password" 
                       placeholder="Konfirmasi password baru" 
                       required>
                <span class="toggle-password" onclick="togglePassword('confirm_password')">
                    <i class="far fa-eye"></i>
                </span>
            </div>

            <button type="submit">Aktivasi Akun</button>
        </form>
    </div>
</div>

<?php include 'footer.php'; ?>
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggle = field.nextElementSibling.querySelector('i');
    if (field.type === "password") {
        field.type = "text";
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        field.type = "password";
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}
</script>
</body>
</html>
