<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendar Fix Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0056b3;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
            white-space: pre-wrap;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 Calendar Fix Verification</h1>
            <p>Verifikasi bahwa masalah timezone calendar sudah diperbaiki</p>
        </div>

        <div class="alert alert-success">
            <strong>Masalah yang diperbaiki:</strong><br>
            ✅ Calendar cell data-date menggunakan local format (bukan UTC)<br>
            ✅ Event parsing menggunakan noon time (12:00) untuk menghindari timezone shift<br>
            ✅ Konsistensi antara single-day dan multi-day event parsing<br>
            ✅ Debug logging untuk troubleshooting
        </div>

        <div class="section">
            <h3>1. Expected Console Output</h3>
            <p>Setelah refresh calendar, console output yang diharapkan:</p>
            
            <div style="background: #e8f5e9; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px;">
Multi-day event: Training Internal Test 2<br>
Raw start: 2025-05-23, Raw end: 2025-05-26<br>
Parsed start: 2025-05-23<br>
Parsed end: 2025-05-26<br>
Total days for event: 4<br>
Loop will start from: 2025-05-23<br>
Loop will end at: 2025-05-26<br>
Day 1: 2025-05-23 (23/5/2025), Cell found: true<br>
Creating event element: multi-start for day 1<br>
Day 2: 2025-05-24 (24/5/2025), Cell found: true<br>
Creating event element: multi-middle for day 2<br>
Day 3: 2025-05-25 (25/5/2025), Cell found: true<br>
Creating event element: multi-middle for day 3<br>
Day 4: 2025-05-26 (26/5/2025), Cell found: true<br>
Creating event element: multi-end for day 4
            </div>
        </div>

        <div class="section">
            <h3>2. Live Console Output</h3>
            <p>Output real-time dari console:</p>
            
            <div class="console-output" id="consoleOutput">
                Waiting for console output...
            </div>
            
            <button class="btn" onclick="clearConsole()">Clear Console</button>
            <a href="../pemohon/dashboard.php" class="btn" target="_blank">Open Calendar</a>
        </div>

        <div class="section">
            <h3>3. Verification Checklist</h3>
            <ul class="checklist">
                <li>Open Developer Tools (F12) dan go to Console tab</li>
                <li>Open calendar dashboard</li>
                <li>Verify "Training Internal Test 2" muncul di tanggal 23, 24, 25, 26 Mei 2025</li>
                <li>Verify "Bahasa Mandarin HSK 1" muncul di tanggal 29, 30, 31 Mei 2025</li>
                <li>Check modal detail menampilkan tanggal yang benar</li>
                <li>Verify console log menunjukkan "Cell found: true" untuk semua hari</li>
                <li>Verify tidak ada "No calendar cell found" warnings</li>
                <li>Test dengan single-day events juga</li>
            </ul>
        </div>

        <div class="section">
            <h3>4. Technical Changes Made</h3>
            <ul>
                <li><strong>createDayCell():</strong> Menggunakan local date format untuk data-date attribute</li>
                <li><strong>displayMultiDayEvent():</strong> Inline parseSimpleDate dengan noon time</li>
                <li><strong>displaySingleDayEvent():</strong> Konsisten dengan multi-day parsing</li>
                <li><strong>Debug logging:</strong> Enhanced untuk troubleshooting</li>
            </ul>
        </div>

        <div class="section">
            <h3>5. Quick Test</h3>
            <p>Test JavaScript date parsing secara langsung:</p>
            
            <div id="testResult"></div>
            
            <button class="btn" onclick="runQuickTest()">Run Quick Test</button>
        </div>
    </div>

    <script>
        function parseSimpleDate(dateStr) {
            if (!dateStr) return null;
            if (typeof dateStr === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                const [year, month, day] = dateStr.split('-').map(Number);
                return new Date(year, month - 1, day, 12, 0, 0, 0);
            }
            return null;
        }

        function formatDateForAPI(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        function runQuickTest() {
            const testDates = ['2025-05-23', '2025-05-26', '2025-05-29', '2025-05-31'];
            let html = '<h5>Quick Test Results:</h5>';
            
            testDates.forEach(testDate => {
                const parsed = parseSimpleDate(testDate);
                const formatted = parsed ? formatDateForAPI(parsed) : 'null';
                const isCorrect = formatted === testDate;
                const status = isCorrect ? '✅' : '❌';
                
                html += `<div>${testDate} → ${formatted} ${status}</div>`;
            });

            document.getElementById('testResult').innerHTML = html;
        }

        function clearConsole() {
            document.getElementById('consoleOutput').innerHTML = 'Console cleared...';
        }

        // Capture console.log for display
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;

        function addToConsoleOutput(type, args) {
            const consoleOutput = document.getElementById('consoleOutput');
            if (consoleOutput) {
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                
                const timestamp = new Date().toLocaleTimeString();
                const typeIcon = type === 'warn' ? '⚠️' : 'ℹ️';
                
                consoleOutput.innerHTML += `${timestamp} ${typeIcon} ${message}\n`;
                consoleOutput.scrollTop = consoleOutput.scrollHeight;
            }
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsoleOutput('log', args);
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsoleOutput('warn', args);
        };

        // Auto-run quick test
        window.addEventListener('load', function() {
            runQuickTest();
        });
    </script>
</body>
</html>
