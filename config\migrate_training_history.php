<?php
/**
 * Migration script to update training_history table structure
 * This script adds the new column structure (start_date, end_date, is_confirmed)
 * to the training_history table and migrates existing data
 */

include 'config.php';

echo "<h2>🔄 Training History Table Migration</h2>";

try {
    // Check if new columns already exist
    $check_columns = "SHOW COLUMNS FROM training_history LIKE 'start_date'";
    $result = $conn->query($check_columns);
    
    if ($result->num_rows > 0) {
        echo "<p>✅ New columns already exist in training_history table.</p>";
    } else {
        echo "<h3>1. Adding new columns to training_history table</h3>";
        
        // Add new columns
        $add_columns = [
            "ALTER TABLE training_history ADD COLUMN start_date DATE DEFAULT NULL COMMENT 'Training start date'",
            "ALTER TABLE training_history ADD COLUMN end_date DATE DEFAULT NULL COMMENT 'Training end date (NULL for single day)'", 
            "ALTER TABLE training_history ADD COLUMN is_confirmed TINYINT(1) DEFAULT 0 COMMENT 'Date confirmation status (0=tentative, 1=confirmed)'"
        ];
        
        foreach ($add_columns as $sql) {
            if ($conn->query($sql)) {
                echo "<p>✅ Column added successfully</p>";
            } else {
                echo "<p>❌ Error adding column: " . $conn->error . "</p>";
            }
        }
        
        echo "<h3>2. Migrating existing data</h3>";
        
        // Migrate existing data from old columns to new columns
        $migrate_data = "UPDATE training_history SET 
                        start_date = COALESCE(training_date_fixed, training_date),
                        end_date = training_date_fixed,
                        is_confirmed = CASE WHEN training_date_fixed IS NOT NULL THEN 1 ELSE 0 END
                        WHERE start_date IS NULL";
        
        if ($conn->query($migrate_data)) {
            $affected = $conn->affected_rows;
            echo "<p>✅ Migrated $affected records to new column structure</p>";
        } else {
            echo "<p>❌ Error migrating data: " . $conn->error . "</p>";
        }
    }
    
    echo "<h3>3. Summary</h3>";
    echo "<p>✅ Migration completed successfully!</p>";
    echo "<p>📝 The training_history table now has both old and new column structures for compatibility.</p>";
    echo "<p>🔧 You can now update the functions.php to use the new column structure if desired.</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Migration failed: " . $e->getMessage() . "</p>";
}

$conn->close();
?>
