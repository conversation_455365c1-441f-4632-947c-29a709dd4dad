/**
 * Card Management System - CSS
 *
 * Provides styling for the RFID card management system
 */

/* Card styling */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
}


.card-body {
    padding: 1.25rem;
}

/* Form styling */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    border-radius: 0.35rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d3e2;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-control-file {
    display: block;
    width: 100%;
}

/* Button styling */
.btn {
    border-radius: 0.35rem;
    padding: 0.375rem 0.75rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: #BF0000;
    border-color: #BF0000;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #900000;
    border-color: #900000;
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-success:hover, .btn-success:focus {
    background-color: #17a673;
    border-color: #169b6b;
}

.btn-info {
    background-color: #36b9cc;
    border-color: #36b9cc;
}

.btn-info:hover, .btn-info:focus {
    background-color: #2c9faf;
    border-color: #2a96a5;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover, .btn-secondary:focus {
    background-color: #5a6268;
    border-color: #545b62;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

/* Table styling */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #e3e6f0;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #e3e6f0;
    background-color: #f8f9fc;
    color: #4e73df;
    font-weight: 700;
}

.table-bordered {
    border: 1px solid #e3e6f0;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #e3e6f0;
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-hover tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.05);
}

/* Alert styling */
.alert {
    position: relative;
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.35rem;
}

.alert-success {
    color: #0f6848;
    background-color: #d1f0e0;
    border-color: #bfe8d8;
}

.alert-danger {
    color: #78261f;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-info {
    color: #1c606a;
    background-color: #d7f1f5;
    border-color: #c7ebf1;
}

/* Pagination styling */
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.35rem;
}

.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #4e73df;
    background-color: #fff;
    border: 1px solid #dddfeb;
}

.page-link:hover {
    z-index: 2;
    color: #224abe;
    text-decoration: none;
    background-color: #eaecf4;
    border-color: #dddfeb;
}

.page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #4e73df;
    border-color: #4e73df;
}

/* Modal styling */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.modal-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}

.modal-title {
    color: #4e73df;
    font-weight: 700;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}

/* Loading indicator */
.loading-spinner {
    display: none;
    text-align: center;
    padding: 20px;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    font-size: 24px;
    color: rgb(157, 0, 0);
    animation: spin 1s infinite linear;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Card number badge */
.card-number-badge {
    display: inline-block;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.35rem;
    background-color: #f8f9fc;
    border: 1px solid #d1d3e2;
    color: #5a5c69;
    font-family: monospace;
    letter-spacing: 0.05em;
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: nowrap;
    justify-content: center;
}

.action-buttons .btn {
    white-space: nowrap;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-header {
        padding: 0.75rem 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .table th,
    .table td {
        padding: 0.5rem;
    }

    .btn {
        padding: 0.25rem 0.5rem;
    }

    .form-inline {
        flex-direction: column;
        align-items: stretch;
    }

    .form-inline .form-group {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .form-inline .btn {
        margin-left: 0 !important;
        margin-top: 0.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 1rem;
    }

    h1.h3 {
        font-size: 1.5rem;
    }

    .card-header h6 {
        color: white;
        font-size: 0.9rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
}
