<?php
session_start();
include '../config/config.php';

// Pastikan request adalah POST dan user sudah login
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Pastikan semua data yang diperlukan tersedia
if (!isset($_POST['faq_id']) || !isset($_POST['vote_type'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required parameters']);
    exit();
}

$faq_id = intval($_POST['faq_id']);
$user_id = $_SESSION['user_id'];
$vote_type = $_POST['vote_type'];

// Validasi vote_type
if (!in_array($vote_type, ['helpful', 'not_helpful'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid vote type']);
    exit();
}

try {
    // Cek apakah FAQ ada
    $check_faq = $conn->prepare("SELECT id FROM faq WHERE id = ?");
    $check_faq->bind_param("i", $faq_id);
    $check_faq->execute();
    $faq_result = $check_faq->get_result();

    if ($faq_result->num_rows === 0) {
        throw new Exception('FAQ not found');
    }

    // Cek apakah user sudah melakukan voting sebelumnya
    $check_vote = $conn->prepare("SELECT id, vote_type FROM faq_votes WHERE faq_id = ? AND user_id = ?");
    $check_vote->bind_param("ii", $faq_id, $user_id);
    $check_vote->execute();
    $vote_result = $check_vote->get_result();

    if ($vote_result->num_rows > 0) {
        // Update vote yang sudah ada
        $existing_vote = $vote_result->fetch_assoc();
        if ($existing_vote['vote_type'] === $vote_type) {
            // Hapus vote jika user memilih tipe yang sama (toggle)
            $delete_vote = $conn->prepare("DELETE FROM faq_votes WHERE id = ?");
            $delete_vote->bind_param("i", $existing_vote['id']);
            $delete_vote->execute();
            $message = 'Vote removed';
        } else {
            // Update tipe vote jika berbeda
            $update_vote = $conn->prepare("UPDATE faq_votes SET vote_type = ? WHERE id = ?");
            $update_vote->bind_param("si", $vote_type, $existing_vote['id']);
            $update_vote->execute();
            $message = 'Vote updated';
        }
    } else {
        // Tambah vote baru
        $add_vote = $conn->prepare("INSERT INTO faq_votes (faq_id, user_id, vote_type) VALUES (?, ?, ?)");
        $add_vote->bind_param("iis", $faq_id, $user_id, $vote_type);
        $add_vote->execute();
        $message = 'Vote added';
    }

    // Ambil jumlah vote terbaru
    $get_votes = $conn->prepare("SELECT 
        COUNT(CASE WHEN vote_type = 'helpful' THEN 1 END) as helpful_votes,
        COUNT(CASE WHEN vote_type = 'not_helpful' THEN 1 END) as not_helpful_votes
        FROM faq_votes WHERE faq_id = ?");
    $get_votes->bind_param("i", $faq_id);
    $get_votes->execute();
    $votes = $get_votes->get_result()->fetch_assoc();

    echo json_encode([
        'success' => true,
        'message' => $message,
        'votes' => $votes
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error',
        'message' => $e->getMessage()
    ]);
}