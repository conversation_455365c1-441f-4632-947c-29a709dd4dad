<?php
session_start();
include '../config/config.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Ambil kategori dari request jika ada
$data = json_decode(file_get_contents('php://input'), true);
$category = $data['category'] ?? 'all';

// Query untuk menghapus log aktivitas
if ($category === 'all') {
    $query = "DELETE FROM activity_logs";
    $stmt = $conn->prepare($query);
} else {
    $query = "DELETE FROM activity_logs WHERE category = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $category);
}

// Execute query
$stmt->execute();

// Cek apakah ada log yang dihapus
$affectedRows = $stmt->affected_rows;

// Log aktivitas penghapusan (ini akan menjadi satu-satunya log jika semua dihapus)
$userId = $_SESSION['user_id'];
$action = ($category === 'all') ? 'Menghapus semua log aktivitas' : "Menghapus log aktivitas kategori {$category}";
$query = "INSERT INTO activity_logs (user_id, action, category, timestamp) 
          VALUES (?, ?, 'logs', NOW())";
$stmt = $conn->prepare($query);
$stmt->bind_param("is", $userId, $action);
$stmt->execute();

// Kembalikan response
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'message' => 'Log aktivitas berhasil dihapus',
    'affected_rows' => $affectedRows
]);
