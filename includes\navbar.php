<?php
// Determine base URL
$base_url = '../';

// Get current page
$current_page = basename($_SERVER['PHP_SELF']);

// Get user role for conditional menu items
$user_role = $_SESSION['role_id'] ?? 0;
$user_name = $_SESSION['full_name'] ?? 'User';
$user_initial = strtoupper(substr($user_name, 0, 1));
?>

<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <div class="container-fluid">
        <a class="navbar-brand" href="<?= $base_url ?>pemohon/index.php">Training System</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?= ($current_page == 'index.php') ? 'active' : '' ?>" href="<?= $base_url ?>pemohon/index.php">
                        <i class="fas fa-home"></i> Home
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= ($current_page == 'classroom.php') ? 'active' : '' ?>" href="<?= $base_url ?>pemohon/classroom.php">
                        <i class="fas fa-chalkboard"></i> Kelas
                    </a>
                </li>
                <?php if ($user_role == 99): // Admin menu items ?>
                <li class="nav-item">
                    <a class="nav-link <?= ($current_page == 'admin_dashboard.php') ? 'active' : '' ?>" href="<?= $base_url ?>admin/index.php">
                        <i class="fas fa-tachometer-alt"></i> Admin Dashboard
                    </a>
                </li>
                <?php endif; ?>
            </ul>

            <ul class="navbar-nav">
                <?php
                // Check if notifications_dropdown.php exists before including it
                $notifications_path = $base_url . 'includes/notifications_dropdown.php';
                if (file_exists($notifications_path)) {
                    include_once $notifications_path;
                } else {
                    // Fallback to direct path if base_url doesn't work
                    include_once __DIR__ . '/notifications_dropdown.php';
                }
                ?>

                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="avatar-circle"><?= $user_initial ?></span>
                        <span class="d-none d-md-inline ms-2"><?= htmlspecialchars($user_name) ?></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="<?= $base_url ?>pemohon/profile.php"><i class="fas fa-user me-2"></i> Profil</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?= $base_url ?>view/logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
    .avatar-circle {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: #007bff;
        color: white;
        font-weight: bold;
    }

    .navbar .dropdown-menu {
        min-width: 200px;
    }

    .navbar .dropdown-item {
        padding: 8px 16px;
    }

    .navbar .dropdown-item i {
        width: 20px;
        text-align: center;
    }
</style>
