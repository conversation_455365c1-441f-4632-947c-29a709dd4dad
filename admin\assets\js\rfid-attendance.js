/**
 * RFID Attendance System - JavaScript
 *
 * Provides interactive and real-time functionality for the RFID attendance system
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    let searchTimeout;
    let currentTrainingId = '';
    let lastRefreshTime = new Date();
    const refreshInterval = 30000; // 30 seconds

    // Elements
    const trainingSelect = document.getElementById('training_id');
    const manualTrainingSelect = document.getElementById('training_id_manual');
    const cardNumberInput = document.getElementById('card_number');
    const manualNikInput = document.getElementById('manual_nik');
    const employeeSearchInput = document.getElementById('employee_search');
    const attendanceTable = document.getElementById('attendance_table');
    const employeeResults = document.getElementById('employee_results');
    const loadingSpinner = document.getElementById('loading_spinner');

    // Initialize Select2 for better dropdown experience
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select2-dropdown').select2({
            theme: 'bootstrap4',
            placeholder: 'Pilih opsi...',
            width: '100%',
            language: {
                noResults: function() {
                    return "Tidak ada hasil yang ditemukan";
                },
                searching: function() {
                    return "Mencari...";
                }
            },
            escapeMarkup: function(markup) {
                return markup;
            },
            templateResult: function(data) {
                if (data.loading) return data.text;

                if (!data.id) return data.text;

                return $('<div class="select2-result-item">' +
                         '<div class="select2-result-item__title">' + data.text + '</div>' +
                         '</div>');
            }
        });

        // Tambahkan event listener untuk perubahan pada dropdown training
        $('#training_id, #training_id_manual').on('change', function() {
            if (this.value !== '') {
                // Show loading spinner
                $('#loading_spinner').removeClass('d-none');

                // Navigate to the page with the selected training
                window.location.href = 'rfid_attendance.php?training_id=' + this.value;
            }
        });
    }

    // Add table hover effect
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        table.classList.add('table-hover');
    });

    // Auto focus on card number input when page loads
    if (cardNumberInput) {
        setTimeout(() => {
            cardNumberInput.focus();
        }, 500);
    }

    // Auto submit form when card is scanned
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function() {
            if (this.value.length > 0 && trainingSelect && trainingSelect.value !== '') {
                // Show loading indicator
                if (loadingSpinner) loadingSpinner.classList.remove('d-none');

                // Submit form after a short delay
                setTimeout(function() {
                    const form = cardNumberInput.closest('form');
                    if (form) form.submit();
                }, 500);
            }
        });
    }

    // Update URL when training is selected
    if (trainingSelect) {
        trainingSelect.addEventListener('change', function() {
            if (this.value !== '') {
                // Show loading indicator
                if (loadingSpinner) loadingSpinner.classList.remove('d-none');

                // Store current training ID
                currentTrainingId = this.value;

                // Navigate to the page with the selected training
                window.location.href = 'rfid_attendance.php?training_id=' + this.value;
            }
        });
    }

    if (manualTrainingSelect) {
        manualTrainingSelect.addEventListener('change', function() {
            if (this.value !== '') {
                // Show loading indicator
                if (loadingSpinner) loadingSpinner.classList.remove('d-none');

                // Store current training ID
                currentTrainingId = this.value;

                // Navigate to the page with the selected training
                window.location.href = 'rfid_attendance.php?training_id=' + this.value;
            }
        });
    }

    // Employee search with autocomplete
    if (manualNikInput) {
        manualNikInput.addEventListener('input', function() {
            const searchTerm = this.value.trim();

            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // Set new timeout for search
            searchTimeout = setTimeout(() => {
                if (searchTerm.length >= 2) {
                    searchEmployees(searchTerm);
                } else if (employeeResults) {
                    employeeResults.innerHTML = '';
                    employeeResults.classList.add('d-none');
                }
            }, 300);
        });
    }

    // Function to search employees
    function searchEmployees(searchTerm) {
        if (loadingSpinner) loadingSpinner.classList.remove('d-none');

        // Make AJAX request to search employees
        fetch(`search_employees.php?search=${encodeURIComponent(searchTerm)}&limit=5`)
            .then(response => response.json())
            .then(data => {
                if (loadingSpinner) loadingSpinner.classList.add('d-none');

                if (data.success && employeeResults) {
                    // Display results
                    displayEmployeeResults(data.employees);
                }
            })
            .catch(error => {
                console.error('Error searching employees:', error);
                if (loadingSpinner) loadingSpinner.classList.add('d-none');
            });
    }

    // Function to display employee search results
    function displayEmployeeResults(employees) {
        if (!employeeResults) return;

        if (employees.length === 0) {
            employeeResults.innerHTML = '<div class="p-2 text-center text-muted">Tidak ada karyawan yang ditemukan</div>';
            employeeResults.classList.remove('d-none');
            return;
        }

        let html = '<div class="list-group">';

        employees.forEach(employee => {
            html += `
                <a href="#" class="list-group-item list-group-item-action employee-item"
                   data-nik="${employee.nik}" data-nama="${employee.nama}" data-dept="${employee.dept}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${employee.nik}</strong> - ${employee.nama}
                        </div>
                        <span class="badge badge-primary badge-pill">${employee.dept}</span>
                    </div>
                </a>
            `;
        });

        html += '</div>';

        employeeResults.innerHTML = html;
        employeeResults.classList.remove('d-none');

        // Add click event to employee items
        document.querySelectorAll('.employee-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                const nik = this.getAttribute('data-nik');

                if (manualNikInput) {
                    manualNikInput.value = nik;
                    employeeResults.classList.add('d-none');

                    // Auto submit if training is selected
                    if (manualTrainingSelect && manualTrainingSelect.value !== '') {
                        setTimeout(() => {
                            const form = manualNikInput.closest('form');
                            if (form) form.submit();
                        }, 500);
                    }
                }
            });
        });
    }

    // Function to refresh attendance data
    function refreshAttendanceData() {
        if (!currentTrainingId || !attendanceTable) return;

        const now = new Date();
        if ((now - lastRefreshTime) < refreshInterval) return;

        lastRefreshTime = now;

        fetch(`get_attendance_data.php?training_id=${currentTrainingId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateAttendanceTable(data.attendance);
                }
            })
            .catch(error => {
                console.error('Error refreshing attendance data:', error);
            });
    }

    // Function to update attendance table
    function updateAttendanceTable(attendanceData) {
        if (!attendanceTable) return;

        // Manual update
        let html = '';

        if (attendanceData.length === 0) {
            html = `
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="py-4">
                            <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                            <p class="text-gray-500 mb-0">Belum ada data absensi</p>
                            <p class="text-gray-500">Scan kartu RFID atau masukkan NIK karyawan untuk mencatat absensi</p>
                        </div>
                    </td>
                </tr>
            `;
        } else {
            let counter = 1;
            attendanceData.forEach(attendance => {
                const statusClass = attendance.status === 'hadir' ? 'success' :
                                   (attendance.status === 'terlambat' ? 'warning' :
                                   (attendance.status === 'izin' ? 'info' : 'danger'));

                const checkIn = attendance.check_in ? new Date(attendance.check_in).toLocaleString('id-ID') : '-';
                const checkOut = attendance.check_out ? new Date(attendance.check_out).toLocaleString('id-ID') : '-';

                html += `
                    <tr>
                        <td class="text-center">${counter++}</td>
                        <td>${attendance.nik}</td>
                        <td>${attendance.nama}</td>
                        <td>${attendance.dept}</td>
                        <td>${checkIn}</td>
                        <td>${checkOut}</td>
                        <td class="text-center">
                            <span class="badge badge-${statusClass}">${attendance.status}</span>
                        </td>
                        <td class="text-center">
                            <a href="edit_attendance.php?id=${attendance.id}&training_id=${currentTrainingId}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit Absensi">
                                <i class="fas fa-edit"></i>
                            </a>
                        </td>
                    </tr>
                `;
            });
        }

        const tbody = attendanceTable.querySelector('tbody');
        if (tbody) {
            tbody.innerHTML = html;
        }

        // Re-initialize tooltips
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    // Set up periodic refresh of attendance data
    if (attendanceTable) {
        // Initial value for currentTrainingId
        const urlParams = new URLSearchParams(window.location.search);
        currentTrainingId = urlParams.get('training_id') || '';

        // Set up interval for refreshing data
        if (currentTrainingId) {
            setInterval(refreshAttendanceData, refreshInterval);
        }
    }
});
