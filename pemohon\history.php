<?php
include '../config/config.php';
include 'security.php';

$user_id = $_SESSION['user_id'];

// Debug: Check user ID
error_log("History.php - User ID: " . $user_id);

// Get training history from both external and internal training
// First, let's try each query separately to debug

// Check external training first
$external_query = "SELECT
    ts.id,
    ts.training_topic,
    COALESCE(ts.training_date_start, ts.training_date_fixed, ts.training_date) as start_date,
    ts.training_date_end as end_date,
    CASE WHEN ts.training_date_fixed IS NOT NULL THEN 1 ELSE 0 END as is_confirmed,
    ts.training_place,
    ts.training_cost,
    ts.status,
    ts.submission_date as created_at,
    'External' as training_type,
    'training_submissions' as source_table
FROM training_submissions ts
WHERE ts.user_id = ? AND (ts.deleted_at IS NULL OR ts.deleted_at = '')";

$external_stmt = $conn->prepare($external_query);
$external_stmt->bind_param("i", $user_id);
$external_stmt->execute();
$external_result = $external_stmt->get_result();
$external_count = $external_result->num_rows;
error_log("External training count: " . $external_count);

// Check internal training
$internal_query = "SELECT
    ot.id,
    ot.training_topic,
    ot.start_date,
    ot.end_date,
    ot.is_confirmed,
    ot.location as training_place,
    NULL as training_cost,
    CASE
        WHEN ot.approved_dept_head = 'Approved' AND ot.approved_lnd = 'Approved' THEN 'Approved'
        WHEN ot.approved_dept_head = 'Rejected' OR ot.approved_lnd = 'Rejected' THEN 'Rejected'
        ELSE 'Pending'
    END as status,
    ot.created_at,
    'Internal' as training_type,
    'offline_training' as source_table
FROM offline_training ot
WHERE ot.created_by = ?";

$internal_stmt = $conn->prepare($internal_query);
$internal_stmt->bind_param("i", $user_id);
$internal_stmt->execute();
$internal_result = $internal_stmt->get_result();
$internal_count = $internal_result->num_rows;
error_log("Internal training count: " . $internal_count);

// Now combine both results
$all_training = [];

// Add external training
while ($row = $external_result->fetch_assoc()) {
    $all_training[] = $row;
}

// Add internal training
while ($row = $internal_result->fetch_assoc()) {
    $all_training[] = $row;
}

// Sort by created_at descending
usort($all_training, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

error_log("Total training records: " . count($all_training));
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
.history-container {
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 20px auto;
    max-width: 1200px;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.history-table th,
.history-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.history-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    color: #333;
}

.history-table tr:hover {
    background-color: #f9f9f9;
}

/* Status styling */
.status-approved {
    color: #2e7d32;
    font-weight: bold;
    background-color: #e8f5e8;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.status-pending {
    color: #f57c00;
    font-weight: bold;
    background-color: #fff3e0;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.status-rejected {
    color: #d32f2f;
    font-weight: bold;
    background-color: #ffebee;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.status-revise {
    color: #7b1fa2;
    font-weight: bold;
    background-color: #f3e5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

/* Training type styling */
.training-type-external {
    color: #1976d2;
    font-weight: bold;
    background-color: #e3f2fd;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.training-type-internal {
    color: #388e3c;
    font-weight: bold;
    background-color: #e8f5e8;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.text-center {
    text-align: center;
}

.jarak {
    height: 100px;
}
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="history-container">
    <h2>Riwayat Training</h2>

    <table class="history-table">
        <thead>
            <tr>
                <th>Topik Training</th>
                <th>Jenis Training</th>
                <th>Tanggal Training</th>
                <th>Status</th>
                <th>Tempat</th>
                <th>Biaya</th>
                <th>Tanggal Pengajuan</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($all_training)): ?>
            <tr>
                <td colspan="7" class="text-center">
                    <p>Belum ada riwayat training.</p>
                    <small class="text-muted">
                        Debug info: User ID = <?= $user_id ?>,
                        External = <?= $external_count ?>,
                        Internal = <?= $internal_count ?>
                    </small>
                </td>
            </tr>
            <?php else: ?>
                <?php foreach ($all_training as $row): ?>
                <tr>
                    <td style="white-space: wrap;"><?= htmlspecialchars($row['training_topic']) ?></td>
                    <td>
                        <span class="training-type-<?= strtolower($row['training_type']) ?>">
                            <?= htmlspecialchars($row['training_type']) ?>
                        </span>
                    </td>
                    <td style="white-space: wrap;">
                        <?php
                        // Format date range
                        $start_date = $row['start_date'] ? date('d M Y', strtotime($row['start_date'])) : '-';
                        $end_date = $row['end_date'] ? date('d M Y', strtotime($row['end_date'])) : null;

                        if ($end_date && $end_date !== $start_date) {
                            echo $start_date . ' - ' . $end_date;
                        } else {
                            echo $start_date;
                        }

                        // Show confirmation status
                        if (isset($row['is_confirmed'])) {
                            if ($row['is_confirmed'] == 1) {
                                echo ' <small style="color: #2e7d32;">(Confirmed)</small>';
                            } else {
                                echo ' <small style="color: #f57c00;">(Tentative)</small>';
                            }
                        }
                        ?>
                    </td>
                    <td>
                        <span class="status-<?= strtolower($row['status']) ?>">
                            <?= htmlspecialchars($row['status']) ?>
                        </span>
                    </td>
                    <td style="white-space: wrap;"><?= htmlspecialchars($row['training_place'] ?: '-') ?></td>
                    <td>
                        <?php
                        if ($row['training_cost'] && is_numeric($row['training_cost'])) {
                            echo 'Rp ' . number_format($row['training_cost'], 0, ',', '.');
                        } elseif ($row['training_cost']) {
                            echo htmlspecialchars($row['training_cost']);
                        } else {
                            echo '-';
                        }
                        ?>
                    </td>
                    <td><?= date('d M Y H:i', strtotime($row['created_at'])) ?></td>
                </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php include '../config/footer.php'; ?>
</body>
</html>

<?php
// Close statements
$external_stmt->close();
$internal_stmt->close();
$conn->close();
?>