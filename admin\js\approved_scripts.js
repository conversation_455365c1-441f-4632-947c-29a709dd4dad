function confirmAction(action, id) {
    const modal = document.getElementById('confirmModal');
    const message = document.getElementById('modalMessage');
    const confirmYes = document.getElementById('confirmYes');
    
    let actionText = action === 'approve' ? 'menyetujui' : 'membatalkan';
    message.textContent = `<PERSON><PERSON><PERSON>h Anda yakin ingin ${actionText} training ini?`;
    
    modal.style.display = 'block';
    
    confirmYes.onclick = function() {
        window.location.href = `approved.php?${action === 'approve' ? 'approve_id' : 'delete_id'}=${id}`;
    };
    
    document.getElementById('confirmNo').onclick = function() {
        modal.style.display = 'none';
    };
    
    window.onclick = function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    };
}

document.addEventListener('DOMContentLoaded', function() {
    // Add fade effect to messages
    const messages = document.querySelectorAll('.success-message, .error-message');
    messages.forEach(message => {
        setTimeout(() => {
            message.style.transition = 'opacity 0.5s ease-in-out';
            message.style.opacity = '0';
            setTimeout(() => message.remove(), 500);
        }, 3000);
    });
});