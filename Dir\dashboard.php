<?php
include '../config/config.php';
include 'security.php';
$user_id = $_SESSION['user_id'];

// Calculate statistics
$stats = [];

// Total submissions
$total_query = "SELECT COUNT(*) as total FROM training_submissions
                WHERE current_approver_role_id = ? AND status != 'Rejected'";
$stmt = $conn->prepare($total_query);
$stmt->bind_param("i", $_SESSION['role_id']);
$stmt->execute();
$result = $stmt->get_result();
$stats['total'] = $result->fetch_assoc()['total'];

// Pending approvals (consistent capitalization)
$pending_query = "SELECT COUNT(*) as pending FROM training_submissions
                 WHERE current_approver_role_id = 5
                 AND approved_dir = 'Pending'";
$stmt = $conn->prepare($pending_query);
$stmt->execute();
$result = $stmt->get_result();
$stats['pending'] = $result->fetch_assoc()['pending'];

// Approved submissions
$approved_query = "SELECT COUNT(*) as approved FROM training_submissions
                  WHERE current_approver_role_id = ?
                  AND approved_dir = 'Approved'";
$stmt = $conn->prepare($approved_query);
$stmt->bind_param("i", $_SESSION['role_id']);
$stmt->execute();
$result = $stmt->get_result();
$stats['approved'] = $result->fetch_assoc()['approved'];

// Rejected trainings (consistent capitalization and logic)
$rejected_query = "SELECT COUNT(*) as rejected FROM training_submissions
                   WHERE status = 'Rejected'";
$stmt = $conn->prepare($rejected_query);
$stmt->execute();
$result = $stmt->get_result();
$stats['rejected'] = $result->fetch_assoc()['rejected'];

// Add 'completed' key for template compatibility (same as rejected for Director)
$stats['completed'] = $stats['rejected'];

// Main query for submissions list - Simplified version - Added additional filters for pending approvals and approved submissions only. This is to avoid showing rejected submissions.
$query = "SELECT DISTINCT
            ts.id,
            ts.training_topic,
            ts.start_date,
            ts.end_date,
            ts.is_confirmed,
            ts.approved_dir,
            u.name AS requester_name,
            u.dept AS departemen,
            r.role_name AS current_approver,
            COUNT(p.id) AS total_participants
          FROM training_submissions ts
          LEFT JOIN roles r ON ts.current_approver_role_id = r.id
          LEFT JOIN users u ON ts.user_id = u.id
          LEFT JOIN participants p ON ts.id = p.training_id
          WHERE ts.current_approver_role_id = ?
          AND ts.status != 'Rejected'
          AND (ts.approved_dir IS NULL OR ts.approved_dir = 'Pending')
          GROUP BY ts.id, ts.training_topic, ts.start_date, ts.end_date, ts.is_confirmed, ts.approved_dir, u.name, u.dept, r.role_name
          ORDER BY ts.id DESC";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $_SESSION['role_id']);
$stmt->execute();
$result = $stmt->get_result();
$result_array = $result->fetch_all(MYSQLI_ASSOC);
?>
<style>
    .jarak-form {
        margin-top: 20px;
    }
</style>
<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<body>
    <?php include '../config/navbara.php'; ?>
<div class="jarak-form"></div>
<div class="container-form">
    <div class="notifications-container">
        <?php if (isset($_SESSION['errors'])): ?>
            <div class="error-message">
                <div class="notification-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="notification-content">
                    <?php foreach ($_SESSION['errors'] as $error): ?>
                        <p><?php echo htmlspecialchars($error); ?></p>
                    <?php endforeach; ?>
                </div>
                <button class="notification-close" onclick="this.parentElement.style.animation='slideOut 0.5s forwards';setTimeout(() => this.parentElement.remove(), 500)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <?php unset($_SESSION['errors']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['success'])): ?>
            <div class="success-message">
                <div class="notification-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="notification-content">
                    <p><?php echo htmlspecialchars($_SESSION['success']); ?></p>
                </div>
                <button class="notification-close" onclick="this.parentElement.style.animation='slideOut 0.5s forwards';setTimeout(() => this.parentElement.remove(), 500)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>
    </div>
<div class="jarak"></div>
    <?php include '../config/setujuDIR.php'; ?>
</div>

<script>
    // Auto-hide notifications after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const notifications = document.querySelectorAll('.success-message, .error-message');

        notifications.forEach(function(notification) {
            // Set timeout to hide notification
            setTimeout(function() {
                notification.style.animation = 'slideOut 0.5s ease-in forwards';

                // Remove from DOM after animation completes
                setTimeout(function() {
                    notification.remove();
                }, 500);
            }, 5000); // 5 seconds

            // Add click event to close buttons
            const closeButtons = document.querySelectorAll('.notification-close');
            closeButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    const notification = this.closest('.success-message, .error-message');
                    if (notification) {
                        notification.style.animation = 'slideOut 0.5s ease-in forwards';
                        setTimeout(function() {
                            notification.remove();
                        }, 500);
                    }
                });
            });
        });
    });
</script>

<?php include '../config/footer.php'; ?>
</body>
</html>
