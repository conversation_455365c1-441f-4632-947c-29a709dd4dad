<?php
/**
 * Test untuk sistem notifikasi internal (database notifications)
 */

// Disable session untuk testing
define('NO_SESSION', true);

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/../includes/notification_helper.php';

echo "🔔 TESTING INTERNAL NOTIFICATION SYSTEM\n";
echo "========================================\n\n";

$all_tests_passed = true;

// Test 1: Check training_notifications table
echo "1️⃣ Testing training_notifications table...\n";
$table_check_query = "SHOW TABLES LIKE 'training_notifications'";
$table_check_result = $conn->query($table_check_query);

if ($table_check_result && $table_check_result->num_rows > 0) {
    echo "   ✅ training_notifications table exists\n";
    
    // Check table structure
    $structure_query = "DESCRIBE training_notifications";
    $structure_result = $conn->query($structure_query);
    
    if ($structure_result) {
        $columns = [];
        while ($row = $structure_result->fetch_assoc()) {
            $columns[] = $row['Field'];
        }
        
        $required_columns = ['id', 'user_id', 'class_id', 'title', 'message', 'type', 'is_read', 'created_at'];
        $missing_columns = array_diff($required_columns, $columns);
        
        if (empty($missing_columns)) {
            echo "   ✅ Table structure complete\n";
        } else {
            echo "   ❌ Missing columns: " . implode(', ', $missing_columns) . "\n";
            $all_tests_passed = false;
        }
    }
} else {
    echo "   ❌ training_notifications table not found\n";
    $all_tests_passed = false;
}

echo "\n";

// Test 2: Test createNotification function
echo "2️⃣ Testing createNotification function...\n";

// Find a test user
$user_query = "SELECT id, name FROM users WHERE is_active = 1 LIMIT 1";
$user_result = $conn->query($user_query);

if ($user_result && $user_result->num_rows > 0) {
    $test_user = $user_result->fetch_assoc();
    echo "   👤 Test user: {$test_user['name']} (ID: {$test_user['id']})\n";
    
    try {
        $notification_created = createNotification(
            $test_user['id'],
            0, // class_id
            'Test Notification',
            'This is a test notification from the system test',
            'info'
        );
        
        if ($notification_created) {
            echo "   ✅ createNotification function works\n";
            
            // Verify notification was created
            $verify_query = "SELECT * FROM training_notifications WHERE user_id = ? AND title = 'Test Notification' ORDER BY created_at DESC LIMIT 1";
            $verify_stmt = $conn->prepare($verify_query);
            $verify_stmt->bind_param("i", $test_user['id']);
            $verify_stmt->execute();
            $verify_result = $verify_stmt->get_result();
            
            if ($verify_result->num_rows > 0) {
                $notification = $verify_result->fetch_assoc();
                echo "   ✅ Notification saved to database (ID: {$notification['id']})\n";
            } else {
                echo "   ❌ Notification not found in database\n";
                $all_tests_passed = false;
            }
        } else {
            echo "   ❌ createNotification function failed\n";
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "   ❌ Error in createNotification: " . $e->getMessage() . "\n";
        $all_tests_passed = false;
    }
} else {
    echo "   ❌ No test user found\n";
    $all_tests_passed = false;
}

echo "\n";

// Test 3: Test getUnreadNotifications function
echo "3️⃣ Testing getUnreadNotifications function...\n";

if (isset($test_user)) {
    try {
        $unread_notifications = getUnreadNotifications($test_user['id'], 5);
        
        if (is_array($unread_notifications)) {
            echo "   ✅ getUnreadNotifications function works\n";
            echo "   📊 Found " . count($unread_notifications) . " unread notifications\n";
            
            if (count($unread_notifications) > 0) {
                $latest = $unread_notifications[0];
                echo "   📝 Latest: {$latest['title']}\n";
            }
        } else {
            echo "   ❌ getUnreadNotifications returned invalid data\n";
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "   ❌ Error in getUnreadNotifications: " . $e->getMessage() . "\n";
        $all_tests_passed = false;
    }
}

echo "\n";

// Test 4: Test markNotificationAsRead function
echo "4️⃣ Testing markNotificationAsRead function...\n";

if (isset($test_user) && isset($notification)) {
    try {
        $mark_read_result = markNotificationAsRead($notification['id'], $test_user['id']);
        
        if ($mark_read_result) {
            echo "   ✅ markNotificationAsRead function works\n";
            
            // Verify notification was marked as read
            $verify_read_query = "SELECT is_read FROM training_notifications WHERE id = ?";
            $verify_read_stmt = $conn->prepare($verify_read_query);
            $verify_read_stmt->bind_param("i", $notification['id']);
            $verify_read_stmt->execute();
            $verify_read_result = $verify_read_stmt->get_result();
            
            if ($verify_read_result->num_rows > 0) {
                $read_status = $verify_read_result->fetch_assoc();
                if ($read_status['is_read'] == 1) {
                    echo "   ✅ Notification marked as read in database\n";
                } else {
                    echo "   ❌ Notification not marked as read\n";
                    $all_tests_passed = false;
                }
            }
        } else {
            echo "   ❌ markNotificationAsRead function failed\n";
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "   ❌ Error in markNotificationAsRead: " . $e->getMessage() . "\n";
        $all_tests_passed = false;
    }
}

echo "\n";

// Test 5: Check notification statistics
echo "5️⃣ Testing notification statistics...\n";

try {
    $stats_query = "SELECT 
                        COUNT(*) as total_notifications,
                        COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_count,
                        COUNT(CASE WHEN is_read = 1 THEN 1 END) as read_count,
                        COUNT(DISTINCT user_id) as users_with_notifications
                    FROM training_notifications";
    $stats_result = $conn->query($stats_query);
    
    if ($stats_result) {
        $stats = $stats_result->fetch_assoc();
        echo "   📊 Total notifications: {$stats['total_notifications']}\n";
        echo "   📬 Unread: {$stats['unread_count']}\n";
        echo "   ✅ Read: {$stats['read_count']}\n";
        echo "   👥 Users with notifications: {$stats['users_with_notifications']}\n";
        
        if ($stats['total_notifications'] > 0) {
            echo "   ✅ Notification system has data\n";
        } else {
            echo "   ℹ️  No notifications in system yet\n";
        }
    } else {
        echo "   ❌ Failed to get notification statistics\n";
        $all_tests_passed = false;
    }
} catch (Exception $e) {
    echo "   ❌ Error getting statistics: " . $e->getMessage() . "\n";
    $all_tests_passed = false;
}

echo "\n";

// Final Result
echo "🏁 INTERNAL NOTIFICATION TEST RESULTS\n";
echo "======================================\n";

if ($all_tests_passed) {
    echo "🎉 ALL TESTS PASSED!\n";
    echo "✅ Sistem notifikasi internal BERFUNGSI\n\n";
    
    echo "📋 Summary:\n";
    echo "   ✅ Database table: Available\n";
    echo "   ✅ Create notifications: Working\n";
    echo "   ✅ Read notifications: Working\n";
    echo "   ✅ Mark as read: Working\n";
    echo "   ✅ Statistics: Available\n";
    
    echo "\n🔔 SISTEM NOTIFIKASI INTERNAL SIAP DIGUNAKAN!\n";
} else {
    echo "❌ SOME TESTS FAILED\n";
    echo "⚠️  Sistem notifikasi internal mungkin tidak berfungsi optimal\n";
    echo "🔧 Periksa komponen yang gagal di atas\n";
}

// Cleanup test notification
if (isset($notification)) {
    $cleanup_query = "DELETE FROM training_notifications WHERE id = ? AND title = 'Test Notification'";
    $cleanup_stmt = $conn->prepare($cleanup_query);
    $cleanup_stmt->bind_param("i", $notification['id']);
    $cleanup_stmt->execute();
    echo "\n🧹 Test notification cleaned up\n";
}

$conn->close();
?>
