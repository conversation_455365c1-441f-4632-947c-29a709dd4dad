<?php
/**
 * Secure Material File Download Handler
 * This script handles secure file downloads for training materials
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once 'config/config.php';
include 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];
$role_id = $_SESSION['role_id'];

// Check if material ID is provided
$material_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($material_id <= 0) {
    header('HTTP/1.1 400 Bad Request');
    echo "Invalid request";
    exit();
}

// Define allowed file types and their content types
$allowed_extensions = [
    'pdf' => 'application/pdf',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'ppt' => 'application/vnd.ms-powerpoint',
    'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'xls' => 'application/vnd.ms-excel',
    'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'txt' => 'text/plain',
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'mp4' => 'video/mp4',
    'avi' => 'video/x-msvideo',
    'mov' => 'video/quicktime',
    'wmv' => 'video/x-ms-wmv',
    'flv' => 'video/x-flv',
    'mkv' => 'video/x-matroska'
];

// Get material information
$query = "SELECT m.*, c.id as class_id
          FROM training_materials m
          JOIN training_classes c ON m.class_id = c.id
          WHERE m.id = ?";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $material_id);
$stmt->execute();
$result = $stmt->get_result();
$material = $result->fetch_assoc();
$stmt->close();

if (!$material) {
    header('HTTP/1.1 404 Not Found');
    echo "Material not found";
    exit();
}

// Check if user has access to this material
$has_access = false;

if ($role_id == 99) {
    // Admin has access to all materials
    $has_access = true;
} else if (in_array($role_id, [1,2,3,4,5])) {
    // Check if user is a participant in this class (including assistants)
    $access_query = "SELECT COUNT(*) as count FROM training_participants
                    WHERE user_id = ? AND class_id = ?";
    $stmt = $conn->prepare($access_query);
    $stmt->bind_param("ii", $user_id, $material['class_id']);
    $stmt->execute();
    $access_result = $stmt->get_result();
    $access_data = $access_result->fetch_assoc();
    $has_access = ($access_data['count'] > 0);
    $stmt->close();
}

if (!$has_access) {
    header('HTTP/1.1 403 Forbidden');
    echo "You don't have permission to access this file";
    exit();
}

// Check if material has a file
if (empty($material['file_path'])) {
    header('HTTP/1.1 404 Not Found');
    echo "No file associated with this material";
    exit();
}

// Get the full file path
$file_path = $material['file_path'];
if (strpos($file_path, 'uploads/') !== 0) {
    $file_path = 'uploads/materials/' . basename($file_path);
}

// Check if file exists
if (!file_exists($file_path)) {
    header('HTTP/1.1 404 Not Found');
    echo "File not found on server";
    exit();
}

// Get file extension
$file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

// Check if file extension is allowed
if (!array_key_exists($file_extension, $allowed_extensions)) {
    header('HTTP/1.1 403 Forbidden');
    echo "File type not allowed";
    exit();
}

// Get content type
$content_type = $allowed_extensions[$file_extension];

// Get original filename
$original_filename = basename($file_path);
if (strpos($original_filename, '_') !== false) {
    $original_filename = substr($original_filename, strpos($original_filename, '_') + 1);
}

// Check if this is a download request
$force_download = isset($_GET['download']) && $_GET['download'] == '1';

// Log download attempt
error_log("User ID: $user_id is " . ($force_download ? 'downloading' : 'viewing') . " material ID: $material_id, File: $file_path");

// Set headers for download or view
header('Content-Type: ' . $content_type);
if ($force_download) {
    header('Content-Disposition: attachment; filename="' . $original_filename . '"');
} else {
    header('Content-Disposition: inline; filename="' . $original_filename . '"');
}
header('Content-Length: ' . filesize($file_path));
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Output file content
readfile($file_path);
exit();
?>
