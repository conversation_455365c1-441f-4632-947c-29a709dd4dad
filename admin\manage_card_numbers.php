<?php
// File: admin/manage_card_numbers.php
// Deskripsi: Halaman untuk mengelola nomor kartu RFID karyawan

include '../config/config.php';
include 'security.php';

// Security check sudah dilakukan di security.php

// Proses update nomor kartu
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update_card') {
        $karyawan_id = $_POST['karyawan_id'];
        $card_number = $_POST['card_number'];

        // Set current_user_id untuk trigger
        $conn->query("SET @current_user_id = " . $_SESSION['user_id']);

        // Update nomor kartu
        $query_update = "UPDATE karyawan SET card_number = ? WHERE id = ?";
        $stmt_update = $conn->prepare($query_update);
        $stmt_update->bind_param("si", $card_number, $karyawan_id);

        if ($stmt_update->execute()) {
            $success = "Nomor kartu berhasil diupdate";

            // Log aktivitas
            $query_karyawan = "SELECT nik, nama FROM karyawan WHERE id = ?";
            $stmt_karyawan = $conn->prepare($query_karyawan);
            $stmt_karyawan->bind_param("i", $karyawan_id);
            $stmt_karyawan->execute();
            $result_karyawan = $stmt_karyawan->get_result();
            $karyawan = $result_karyawan->fetch_assoc();

            $log_query = "INSERT INTO activity_logs (user_id, action, category, timestamp)
                          VALUES (?, CONCAT('Update nomor kartu RFID untuk karyawan: ', ?, ' (NIK: ', ?, ')'), 'employee', NOW())";
            $log_stmt = $conn->prepare($log_query);
            $log_stmt->bind_param("iss", $_SESSION['user_id'], $karyawan['nama'], $karyawan['nik']);
            $log_stmt->execute();
        } else {
            $error = "Gagal mengupdate nomor kartu: " . $conn->error;
        }
    } elseif (isset($_POST['action']) && $_POST['action'] === 'scan_card') {
        $card_number = $_POST['card_number'];
        $karyawan_id = $_POST['karyawan_id'];

        // Cek apakah kartu sudah digunakan
        $query_check = "SELECT id, nik, nama FROM karyawan WHERE card_number = ? AND id != ?";
        $stmt_check = $conn->prepare($query_check);
        $stmt_check->bind_param("si", $card_number, $karyawan_id);
        $stmt_check->execute();
        $result_check = $stmt_check->get_result();

        if ($result_check->num_rows > 0) {
            $existing_karyawan = $result_check->fetch_assoc();
            $error = "Kartu RFID sudah digunakan oleh karyawan lain: " . $existing_karyawan['nama'] . " (NIK: " . $existing_karyawan['nik'] . ")";
        } else {
            // Set current_user_id untuk trigger
            $conn->query("SET @current_user_id = " . $_SESSION['user_id']);

            // Update nomor kartu
            $query_update = "UPDATE karyawan SET card_number = ? WHERE id = ?";
            $stmt_update = $conn->prepare($query_update);
            $stmt_update->bind_param("si", $card_number, $karyawan_id);

            if ($stmt_update->execute()) {
                $success = "Nomor kartu berhasil diupdate";

                // Log aktivitas
                $query_karyawan = "SELECT nik, nama FROM karyawan WHERE id = ?";
                $stmt_karyawan = $conn->prepare($query_karyawan);
                $stmt_karyawan->bind_param("i", $karyawan_id);
                $stmt_karyawan->execute();
                $result_karyawan = $stmt_karyawan->get_result();
                $karyawan = $result_karyawan->fetch_assoc();

                $log_query = "INSERT INTO activity_logs (user_id, action, category, timestamp)
                              VALUES (?, CONCAT('Update nomor kartu RFID untuk karyawan: ', ?, ' (NIK: ', ?, ')'), 'employee', NOW())";
                $log_stmt = $conn->prepare($log_query);
                $log_stmt->bind_param("iss", $_SESSION['user_id'], $karyawan['nama'], $karyawan['nik']);
                $log_stmt->execute();
            } else {
                $error = "Gagal mengupdate nomor kartu: " . $conn->error;
            }
        }
    } elseif (isset($_POST['action']) && $_POST['action'] === 'bulk_update') {
        // Proses upload file Excel
        if (isset($_FILES['excel_file']) && $_FILES['excel_file']['error'] === UPLOAD_ERR_OK) {
            $file_tmp = $_FILES['excel_file']['tmp_name'];

            // Require library PhpSpreadsheet
            require '../vendor/autoload.php';

            $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
            $spreadsheet = $reader->load($file_tmp);
            $worksheet = $spreadsheet->getActiveSheet();
            $highestRow = $worksheet->getHighestRow();

            $success_count = 0;
            $error_count = 0;
            $error_details = [];

            // Mulai dari baris ke-2 (baris pertama adalah header)
            for ($row = 2; $row <= $highestRow; $row++) {
                $nik = $worksheet->getCell('A' . $row)->getValue();
                $card_number = $worksheet->getCell('B' . $row)->getValue();

                if (empty($nik) || empty($card_number)) {
                    continue;
                }

                // Cek apakah NIK ada
                $query_check_nik = "SELECT id, nama FROM karyawan WHERE nik = ?";
                $stmt_check_nik = $conn->prepare($query_check_nik);
                $stmt_check_nik->bind_param("s", $nik);
                $stmt_check_nik->execute();
                $result_check_nik = $stmt_check_nik->get_result();

                if ($result_check_nik->num_rows === 0) {
                    $error_count++;
                    $error_details[] = "Baris {$row}: NIK {$nik} tidak ditemukan";
                    continue;
                }

                $karyawan = $result_check_nik->fetch_assoc();

                // Cek apakah kartu sudah digunakan
                $query_check_card = "SELECT id, nik, nama FROM karyawan WHERE card_number = ? AND nik != ?";
                $stmt_check_card = $conn->prepare($query_check_card);
                $stmt_check_card->bind_param("ss", $card_number, $nik);
                $stmt_check_card->execute();
                $result_check_card = $stmt_check_card->get_result();

                if ($result_check_card->num_rows > 0) {
                    $existing_karyawan = $result_check_card->fetch_assoc();
                    $error_count++;
                    $error_details[] = "Baris {$row}: Kartu RFID {$card_number} sudah digunakan oleh karyawan lain: " . $existing_karyawan['nama'] . " (NIK: " . $existing_karyawan['nik'] . ")";
                    continue;
                }

                // Set current_user_id untuk trigger
                $conn->query("SET @current_user_id = " . $_SESSION['user_id']);

                // Update nomor kartu
                $query_update = "UPDATE karyawan SET card_number = ? WHERE nik = ?";
                $stmt_update = $conn->prepare($query_update);
                $stmt_update->bind_param("ss", $card_number, $nik);

                if ($stmt_update->execute()) {
                    $success_count++;
                } else {
                    $error_count++;
                    $error_details[] = "Baris {$row}: Gagal mengupdate nomor kartu untuk NIK {$nik}: " . $conn->error;
                }
            }

            if ($success_count > 0) {
                $success = "{$success_count} nomor kartu berhasil diupdate";

                // Log aktivitas
                $log_query = "INSERT INTO activity_logs (user_id, action, category, timestamp)
                              VALUES (?, CONCAT('Bulk update nomor kartu RFID untuk ', ?, ' karyawan'), 'employee', NOW())";
                $log_stmt = $conn->prepare($log_query);
                $log_stmt->bind_param("ii", $_SESSION['user_id'], $success_count);
                $log_stmt->execute();
            }

            if ($error_count > 0) {
                $error = "{$error_count} nomor kartu gagal diupdate:<br>" . implode("<br>", $error_details);
            }
        } else {
            $error = "Gagal mengupload file: " . $_FILES['excel_file']['error'];
        }
    }
}

// Ambil daftar karyawan
$search = isset($_GET['search']) ? $_GET['search'] : '';
$dept = isset($_GET['dept']) ? $_GET['dept'] : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Buat query dasar
$query_karyawan = "SELECT id, nik, nama, dept, bagian, jabatan, card_number FROM karyawan WHERE 1=1";
$query_count = "SELECT COUNT(*) as total FROM karyawan WHERE 1=1";

// Tambahkan filter pencarian
if (!empty($search)) {
    $search_param = "%{$search}%";
    $query_karyawan .= " AND (nik LIKE ? OR nama LIKE ? OR card_number LIKE ?)";
    $query_count .= " AND (nik LIKE ? OR nama LIKE ? OR card_number LIKE ?)";
}

// Tambahkan filter departemen
if (!empty($dept)) {
    $query_karyawan .= " AND dept = ?";
    $query_count .= " AND dept = ?";
}

// Tambahkan sorting dan limit
$query_karyawan .= " ORDER BY nama ASC LIMIT ?, ?";

// Prepare statement untuk count
$stmt_count = $conn->prepare($query_count);

// Bind parameter untuk count
if (!empty($search) && !empty($dept)) {
    $stmt_count->bind_param("ssss", $search_param, $search_param, $search_param, $dept);
} elseif (!empty($search)) {
    $stmt_count->bind_param("sss", $search_param, $search_param, $search_param);
} elseif (!empty($dept)) {
    $stmt_count->bind_param("s", $dept);
}

$stmt_count->execute();
$result_count = $stmt_count->get_result();
$row_count = $result_count->fetch_assoc();
$total_records = $row_count['total'];
$total_pages = ceil($total_records / $limit);

// Prepare statement untuk karyawan
$stmt_karyawan = $conn->prepare($query_karyawan);

// Bind parameter untuk karyawan
if (!empty($search) && !empty($dept)) {
    $stmt_karyawan->bind_param("sssii", $search_param, $search_param, $search_param, $dept, $offset, $limit);
} elseif (!empty($search)) {
    $stmt_karyawan->bind_param("sssii", $search_param, $search_param, $search_param, $offset, $limit);
} elseif (!empty($dept)) {
    $stmt_karyawan->bind_param("sii", $dept, $offset, $limit);
} else {
    $stmt_karyawan->bind_param("ii", $offset, $limit);
}

$stmt_karyawan->execute();
$result_karyawan = $stmt_karyawan->get_result();

// Ambil daftar departemen untuk filter
$query_dept = "SELECT DISTINCT dept FROM karyawan ORDER BY dept ASC";
$result_dept = $conn->query($query_dept);

// Judul halaman
$page_title = "Kelola Nomor Kartu RFID";
include 'header.php';

// Tambahkan CSS dan JS khusus untuk halaman ini
echo '<link rel="stylesheet" href="assets/css/card-management.css">';

// Jika ini adalah request AJAX, hanya proses form dan kembalikan respons
if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
    // Proses form seperti biasa
    // ...

    // Kembalikan respons JSON
    exit;
}
?>

<div class="container-fluid">
    <!-- Loading Spinner -->
    <div class="loading-spinner d-none" id="loading_spinner">
        <div class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>

    <h1 class="welcome-section">Kelola Nomor Kartu RFID</h1>

    <!-- Notification Area -->
    <div id="notification_area">
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
    </div>

    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="" >Scan Kartu RFID</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="" id="scan-form">
                        <input type="hidden" name="action" value="scan_card">
                        <input type="hidden" name="karyawan_id" id="scan_karyawan_id">
                        <div class="form-group">
                            <label for="card_number">Nomor Kartu RFID:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="scan_card_number" name="card_number" placeholder="Scan kartu RFID" autofocus>
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                </div>
                            </div>
                            <small class="form-text text-muted">Pilih karyawan dari tabel di bawah, lalu scan kartu RFID untuk mengaitkan nomor kartu dengan karyawan tersebut.</small>
                        </div>
                        <div id="selected_employee" class="alert alert-info mb-3 d-none">
                            <i class="fas fa-user-check mr-2"></i> <strong>Karyawan terpilih:</strong> <span id="selected_employee_info">-</span>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="" >Import Nomor Kartu dari Excel</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="bulk_update">
                        <div class="form-group">
                            <label for="excel_file">File Excel:</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="excel_file" name="excel_file" accept=".xlsx" required>
                                <label class="custom-file-label" for="excel_file">Pilih file...</label>
                            </div>
                            <small class="form-text text-muted">Format file Excel: Kolom A untuk NIK, Kolom B untuk Nomor Kartu RFID. Baris pertama adalah header.</small>
                        </div>
                        <div class="d-flex mt-3">
                            <button type="submit" class="btn btn-primary mr-2">
                                <i class="fas fa-file-import"></i> Import
                            </button>
                            <a href="template_card_numbers.php" class="btn btn-secondary">
                                <i class="fas fa-download"></i> Download Template
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="" >Daftar Karyawan</h6>
                <a href="export_card_numbers.php" class="btn btn-sm btn-success">
                    <i class="fas fa-file-excel"></i> Export Data Kartu
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-lg-8">
                    <div class="form-inline">
                        <div class="input-group mr-2 mb-2">
                            <input type="text" class="form-control" name="search" id="search_input" placeholder="Cari NIK, Nama, atau Nomor Kartu" value="<?php echo htmlspecialchars($search); ?>">
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                            </div>
                        </div>
                        <div class="form-group mr-2 mb-2">
                            <select id="department" name="dept" class="form-control">
                                <option value="">Semua Departemen</option>
                                <?php
                                // Query untuk mengambil departemen unik dari tabel karyawan
                                $dept_query = "SELECT DISTINCT dept FROM karyawan ORDER BY dept";
                                $dept_result = $conn->query($dept_query);

                                if ($dept_result->num_rows > 0) {
                                    while($row = $dept_result->fetch_assoc()) {
                                        $selected = ($dept === $row['dept']) ? 'selected' : '';
                                        echo "<option value='" . htmlspecialchars($row['dept']) . "' " . $selected . ">" . htmlspecialchars($row['dept']) . "</option>";
                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Table will be loaded here via AJAX -->
            <div class="table-responsive">
                <!-- Loading placeholder -->
                <div class="text-center py-5" id="loading_placeholder">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2">Memuat data...</p>
                </div>
            </div>

            <!-- Pagination will be loaded here via AJAX -->
            <div class="pagination-container"></div>
        </div>
    </div>
</div>

<!-- Modal Edit Nomor Kartu -->
<div class="modal fade" id="editCardModal" tabindex="-1" role="dialog" aria-labelledby="editCardModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCardModalLabel">Edit Nomor Kartu RFID</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_card">
                    <input type="hidden" name="karyawan_id" id="edit_karyawan_id">
                    <div class="form-group">
                        <label>NIK:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="edit_nik" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fas fa-id-badge"></i></span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Nama:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="edit_nama" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="card_number">Nomor Kartu RFID:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="edit_card_number" name="card_number" placeholder="Masukkan nomor kartu RFID">
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                            </div>
                        </div>
                        <small class="form-text text-muted">Masukkan nomor kartu RFID atau scan kartu RFID</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Custom JS -->

<!-- Include AJAX Card Management JS -->
<script src="assets/js/ajax-card-management.js"></script>

<!-- Additional custom scripts -->
<script>
    
  // Searchable dropdown functionality
  const searchInput = document.getElementById('searchDepartment');
  const dropdownList = document.getElementById('dropdownDepartmentList');

  // Hanya jalankan kode jika elemen-elemen tersebut ada di halaman
  if (searchInput && dropdownList) {
    const originalSelect = document.getElementById('department');
    const items = Array.from(dropdownList.getElementsByTagName('li'));

    // Ketika input diklik, tampilkan daftar
    searchInput.addEventListener('click', () => {
      dropdownList.style.display = 'block';
    });

    // Filter saat mengetik
    searchInput.addEventListener('input', () => {
      const searchTerm = searchInput.value.toLowerCase();
      items.forEach(item => {
        const text = item.textContent.toLowerCase();
        item.style.display = text.includes(searchTerm) ? 'block' : 'none';
      });
      dropdownList.style.display = 'block';
    });

    // Set nilai ketika item dipilih
    items.forEach(item => {
      item.addEventListener('click', () => {
        searchInput.value = item.textContent;
        // Set nilai ke hidden select
        if (originalSelect) {
          originalSelect.value = item.getAttribute('data-value');
          // Trigger change event jika diperlukan
          const event = new Event('change');
          originalSelect.dispatchEvent(event);
        }
        dropdownList.style.display = 'none';
      });
    });

    // Sembunyikan dropdown saat klik di luar
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.searchable-select')) {
        dropdownList.style.display = 'none';
      }
    });
  }

  // Toggle verification code field based on direct activation checkbox
  const directActivationCheckbox = document.getElementById('direct_activation');
  const verificationCodeGroup = document.querySelector('.verification-code-group');

  if (directActivationCheckbox && verificationCodeGroup) {
    // Initial state - hide verification code field if direct activation is checked
    verificationCodeGroup.style.display = directActivationCheckbox.checked ? 'none' : 'block';

    // Toggle on checkbox change
    directActivationCheckbox.addEventListener('change', function() {
      verificationCodeGroup.style.display = this.checked ? 'none' : 'block';
    });
  }
    // Initialize custom file input
    document.querySelector('.custom-file-input')?.addEventListener('change', function() {
        const fileName = this.files[0]?.name || 'Pilih file...';
        const label = this.nextElementSibling;
        if (label) {
            label.textContent = fileName;
        }
    });

    // Handle reset filter button
    document.getElementById('reset_filter')?.addEventListener('click', function() {
        document.getElementById('search_input').value = '';
        document.getElementById('dept_select').value = '';

        // Trigger data reload
        if (typeof loadData === 'function') {
            loadData(1, '', '');
        }
    });

    // Handle edit card form submission
    document.getElementById('edit-card-form')?.addEventListener('submit', function(e) {
        e.preventDefault();

        // Show loading spinner
        const loadingSpinner = document.getElementById('loading_spinner');
        if (loadingSpinner) loadingSpinner.classList.remove('d-none');

        const formData = new FormData(this);

        fetch('manage_card_numbers.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(html => {
            // Extract success or error message from the response
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const successAlert = doc.querySelector('.alert-success');
            const errorAlert = doc.querySelector('.alert-danger');

            if (successAlert) {
                showNotification(successAlert.textContent, 'success');
                // Close modal
                const editCardModal = bootstrap.Modal.getInstance(document.getElementById('editCardModal'));
                if (editCardModal) editCardModal.hide();
                // Reload data
                if (typeof loadData === 'function') {
                    loadData(currentPage, currentSearch, currentDept);
                }
            } else if (errorAlert) {
                showNotification(errorAlert.textContent, 'danger');
            } else {
                showNotification('Operasi selesai.', 'info');
            }

            // Hide loading spinner
            if (loadingSpinner) loadingSpinner.classList.add('d-none');
        })
        .catch(error => {
            console.error('Error submitting form:', error);
            showNotification('Error submitting form. Please try again.', 'danger');

            // Hide loading spinner
            if (loadingSpinner) loadingSpinner.classList.add('d-none');
        });
    });
</script>

<?php include 'footer.php'; ?>
