<?php
// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Include batch history recording
require_once 'record_batch_employee_history.php';

// Include fungsi NIK
include 'includes/nik_functions.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi variabel
$success_message = '';
$warning_message = '';
$error_message = '';
$preview_data = [];
$total_success = 0;
$total_error = 0;
$total_skipped = 0; // Tambahkan counter untuk baris yang dilewati
$total_empty = 0;   // Tambahkan counter untuk baris kosong
$error_details = [];
$skipped_niks = []; // Array untuk menyimpan NIK yang dilewati

// Inisialisasi mode upload dan import_only_changed
$upload_mode = isset($_GET['mode']) ? $_GET['mode'] : 'add';
$import_only_changed = isset($_GET['only_changed']) && $_GET['only_changed'] == 1;

// Fungsi untuk memformat tanggal ke format yang konsisten (DD-MM-YYYY)
// Sesuai dengan struktur tabel karyawan yang menggunakan varchar(20) untuk tanggal
// Parameter $format: 'dd-mm-yyyy' (default), 'yyyy-mm-dd', atau 'original' untuk mempertahankan format asli
function formatDate($date, $format = 'dd-mm-yyyy') {
    // Jika input kosong, return null
    if (empty($date)) {
        return null;
    }

    // Simpan tanggal asli untuk diproses dengan format bulan huruf dan strtotime
    $original_date = $date;

    // Jika tanggal adalah angka (mungkin format Excel), coba konversi
    if (is_numeric($date) && $date > 0) {
        try {
            $dateObj = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($date);
            // Format sesuai parameter
            if ($format === 'dd-mm-yyyy') {
                return $dateObj->format('d-m-Y');
            } else if ($format === 'yyyy-mm-dd') {
                return $dateObj->format('Y-m-d');
            }
            return $dateObj->format('d-m-Y'); // Default format
        } catch (\Exception $e) {
            // Jika gagal konversi, lanjutkan dengan metode lain
        }
    }

    // Perbarui tanggal asli jika ada perubahan

    // Hapus tanda petik di awal jika ada
    if (substr($date, 0, 1) === "'") {
        $date = substr($date, 1);
        $original_date = $date; // Update original_date juga
    }

    // Coba parse tanggal dengan format bulan huruf (seperti 15-Jan-2023 atau 15/Jan/2023)
    // Buat array untuk memetakan nama bulan ke angka
    $month_names = [];

    // Singkatan 3 huruf (Inggris dan Indonesia)
    $month_names['jan'] = '01';
    $month_names['feb'] = '02';
    $month_names['mar'] = '03';
    $month_names['apr'] = '04';
    $month_names['may'] = '05';
    $month_names['mei'] = '05';
    $month_names['jun'] = '06';
    $month_names['jul'] = '07';
    $month_names['aug'] = '08';
    $month_names['agu'] = '08';
    $month_names['sep'] = '09';
    $month_names['oct'] = '10';
    $month_names['okt'] = '10';
    $month_names['nov'] = '11';
    $month_names['dec'] = '12';
    $month_names['des'] = '12';

    // Nama bulan lengkap (Inggris)
    $month_names['january'] = '01';
    $month_names['february'] = '02';
    $month_names['march'] = '03';
    $month_names['april'] = '04';
    // 'may' sudah ada di atas
    $month_names['june'] = '06';
    $month_names['july'] = '07';
    $month_names['august'] = '08';
    $month_names['september'] = '09';
    $month_names['october'] = '10';
    $month_names['november'] = '11';
    $month_names['december'] = '12';

    // Nama bulan lengkap (Indonesia)
    $month_names['januari'] = '01';
    $month_names['februari'] = '02';
    $month_names['maret'] = '03';
    // 'april' sudah ada di atas
    // 'mei' sudah ada di atas
    $month_names['juni'] = '06';
    $month_names['juli'] = '07';
    $month_names['agustus'] = '08';
    // 'september' sudah ada di atas
    $month_names['oktober'] = '10';
    $month_names['nopember'] = '11';
    // 'november' sudah ada di atas
    $month_names['desember'] = '12';

    // Format DD-MMM-YYYY atau D-MMM-YYYY (misalnya 15-Jan-2023 atau 15-Januari-2023)
    if (preg_match('/^(\d{1,2})[\-\/](\w+)[\-\/](\d{4})$/i', $original_date, $matches)) {
        $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
        $month_text = strtolower($matches[2]);
        $year = $matches[3];

        // Cek apakah nama bulan ada dalam daftar
        if (isset($month_names[$month_text])) {
            $month = $month_names[$month_text];

            // Validasi tanggal
            if (checkdate((int)$month, (int)$day, (int)$year)) {
                // Format sesuai parameter
                if ($format === 'dd-mm-yyyy') {
                    return "$day-$month-$year";
                } else if ($format === 'yyyy-mm-dd') {
                    return "$year-$month-$day";
                }
                return "$day-$month-$year"; // Default format
            }
        }
    }

    // Format YYYY-MMM-DD atau YYYY/MMM/DD (misalnya 2023-Jan-15 atau 2023-Januari-15)
    if (preg_match('/^(\d{4})[\-\/](\w+)[\-\/](\d{1,2})$/i', $original_date, $matches)) {
        $year = $matches[1];
        $month_text = strtolower($matches[2]);
        $day = str_pad($matches[3], 2, '0', STR_PAD_LEFT);

        // Cek apakah nama bulan ada dalam daftar
        if (isset($month_names[$month_text])) {
            $month = $month_names[$month_text];

            // Validasi tanggal
            if (checkdate((int)$month, (int)$day, (int)$year)) {
                // Format sesuai parameter
                if ($format === 'dd-mm-yyyy') {
                    return "$day-$month-$year";
                } else if ($format === 'yyyy-mm-dd') {
                    return "$year-$month-$day";
                }
                return "$day-$month-$year"; // Default format
            }
        }
    }

    // Hapus karakter non-angka dan non-pemisah untuk format numerik
    $date = preg_replace('/[^0-9\/\-]/', '', $date);

    // Variabel untuk menyimpan komponen tanggal
    $day = null;
    $month = null;
    $year = null;

    // Coba berbagai format tanggal numerik

    // Format DD-MM-YYYY atau D-M-YYYY
    if (preg_match('/^(\d{1,2})-(\d{1,2})-(\d{4})$/', $date, $matches)) {
        $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
        $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
        $year = $matches[3];
    }
    // Format DD/MM/YYYY atau D/M/YYYY
    else if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $date, $matches)) {
        $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
        $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
        $year = $matches[3];
    }
    // Format YYYY-MM-DD
    else if (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $date, $matches)) {
        $year = $matches[1];
        $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
        $day = str_pad($matches[3], 2, '0', STR_PAD_LEFT);
    }
    // Format YYYY/MM/DD
    else if (preg_match('/^(\d{4})\/(\d{1,2})\/(\d{1,2})$/', $date, $matches)) {
        $year = $matches[1];
        $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
        $day = str_pad($matches[3], 2, '0', STR_PAD_LEFT);
    }

    // Jika berhasil mengekstrak komponen tanggal
    if ($day !== null && $month !== null && $year !== null) {
        // Validasi tanggal
        if (checkdate((int)$month, (int)$day, (int)$year)) {
            // Format sesuai parameter
            if ($format === 'dd-mm-yyyy') {
                return "$day-$month-$year";
            } else if ($format === 'yyyy-mm-dd') {
                return "$year-$month-$day";
            }
            return "$day-$month-$year"; // Default format
        }
    }

    // Jika semua metode gagal, coba gunakan strtotime sebagai upaya terakhir
    $timestamp = strtotime($original_date);
    if ($timestamp !== false) {
        if ($format === 'dd-mm-yyyy') {
            return date('d-m-Y', $timestamp);
        } else if ($format === 'yyyy-mm-dd') {
            return date('Y-m-d', $timestamp);
        }
        return date('d-m-Y', $timestamp); // Default format
    }

    // Jika format adalah 'original', kembalikan tanggal asli
    if ($format === 'original') {
        return $original_date;
    }

    // Jika semua metode gagal, kembalikan tanggal asli
    return $original_date;
}

// Proses upload file
if (($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && ($_POST['action'] === 'upload' || $_POST['action'] === 'preview')) ||
    (isset($_GET['temp_file']) && file_exists($_GET['temp_file']))) {

    // Cek apakah file diupload atau ada file temporary dari preview atau dari compare_import.php
    $file_to_process = null;
    $is_temp_file = false;

    // Tentukan mode upload dengan prioritas: POST > GET > default
    if (isset($_POST['upload_mode'])) {
        $upload_mode = $_POST['upload_mode'];
    } elseif (isset($_GET['mode'])) {
        $upload_mode = $_GET['mode'];
    } else {
        $upload_mode = 'add';
    }

    // Debug mode upload
    $debug_info['upload_mode_source'] = isset($_POST['upload_mode']) ? 'POST' : (isset($_GET['mode']) ? 'GET' : 'default');
    $debug_info['upload_mode_value'] = $upload_mode;

    // Log mode upload untuk debugging
    error_log("Upload mode: " . $upload_mode . " (Source: " . $debug_info['upload_mode_source'] . ")");
    error_log("POST data: " . print_r($_POST, true));
    error_log("GET data: " . print_r($_GET, true));

    // Tentukan apakah hanya import data yang berubah (dari POST atau GET)
    if (isset($_POST['import_only_changed'])) {
        $import_only_changed = $_POST['import_only_changed'] == 1;
    } elseif (isset($_GET['only_changed'])) {
        $import_only_changed = $_GET['only_changed'] == 1;
    } else {
        $import_only_changed = false;
    }

    // Log import_only_changed untuk debugging
    error_log("Import only changed: " . ($import_only_changed ? 'Yes' : 'No'));


    if (isset($_GET['temp_file']) && file_exists($_GET['temp_file'])) {
        // Gunakan file temporary dari compare_import.php
        $file_to_process = $_GET['temp_file'];
        $file_name = basename($_GET['temp_file']);
        $is_temp_file = true;
    } elseif (isset($_POST['temp_file']) && file_exists($_POST['temp_file'])) {
        // Gunakan file temporary dari preview
        $file_to_process = $_POST['temp_file'];
        $file_name = isset($_POST['original_filename']) ? $_POST['original_filename'] : 'uploaded_file.xlsx';
        $is_temp_file = true;
    } elseif (isset($_FILES['excel_file']) && $_FILES['excel_file']['error'] === UPLOAD_ERR_OK) {
        // Gunakan file yang baru diupload
        $file_to_process = $_FILES['excel_file']['tmp_name'];
        $file_name = $_FILES['excel_file']['name'];
        $debug_info['file_source'] = 'uploaded_file';
        $debug_info['tmp_name'] = $_FILES['excel_file']['tmp_name'];
        $debug_info['file_exists'] = file_exists($_FILES['excel_file']['tmp_name']);
    } else {
        $error_message = 'Terjadi kesalahan saat upload file. Silakan coba lagi.';
        $debug_info['file_source'] = 'none';
        $debug_info['error'] = 'No file uploaded or temp file not found';
    }

    if ($file_to_process) {
        // Validasi tipe file
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        if ($file_ext !== 'xlsx' && $file_ext !== 'xls') {
            $error_message = 'File harus berformat Excel (.xlsx atau .xls).';
        } else {

    // Cari ID pengguna yang valid
    $valid_user_id = $_SESSION['user_id'] ?? 1;

    // Verifikasi bahwa user_id ada di tabel users
    $check_user = $conn->prepare("SELECT id FROM users WHERE id = ?");
    $check_user->bind_param("i", $valid_user_id);
    $check_user->execute();
    $check_result = $check_user->get_result();
    if ($check_result->num_rows == 0) {
        // Jika tidak ada, cari pengguna lain
        $valid_user_query = "SELECT id FROM users LIMIT 1";
        $user_result = $conn->query($valid_user_query);
        if ($user_result && $user_result->num_rows > 0) {
            $user_row = $user_result->fetch_assoc();
            $valid_user_id = $user_row['id'];
        }
    }

    // Set variabel user_id untuk trigger
    $conn->query("SET @current_user_id = " . $valid_user_id);
// Proses file Excel
            require '../vendor/autoload.php';

            try {
                // Load file Excel
                $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file_to_process);
                $reader->setReadDataOnly(false); // Baca format sel juga
                $spreadsheet = $reader->load($file_to_process);
                $worksheet = $spreadsheet->getActiveSheet();

                // Tambahkan debugging info
                $debug_info['excel_read'] = 'success';
                $debug_info['worksheet_name'] = $worksheet->getTitle();

                // Ambil data dari Excel dengan format yang benar
                $rows = [];
                $highestRow = $worksheet->getHighestRow();
                $highestColumn = $worksheet->getHighestColumn();
                $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

                // Tambahkan debugging untuk highestColumn dan highestColumnIndex
                $debug_info['excel_info'] = [
                    'highest_row' => $highestRow,
                    'highest_column' => $highestColumn,
                    'highest_column_index' => $highestColumnIndex
                ];

                // Cek apakah kolom PT ada dalam file
                // Kita tidak bisa hanya mengandalkan indeks kolom karena mungkin ada kolom kosong di awal
                $hasPTColumn = false;

                // Simpan data mentah dari Excel untuk debugging
                $excelRawData = [];

                for ($row = 1; $row <= $highestRow; $row++) {
                    $rowData = [];

                    // Baca semua kolom yang ada
                    for ($col = 1; $col <= $highestColumnIndex; $col++) {
                        // Konversi indeks kolom ke huruf (1 = A, 2 = B, dst)
                        $colLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($col - 1);
                        $cellCoord = $colLetter . $row;
                        $cell = $worksheet->getCell($cellCoord);
                        $value = $cell->getValue();

                        // Simpan nilai asli dari Excel untuk debugging
                        if ($row <= 4) { // Simpan hanya 4 baris pertama untuk debugging
                            $excelRawData[$row][$col] = [
                                'raw' => $value,
                                'type' => gettype($value),
                                'col_letter' => $colLetter
                            ];
                        }

                        // Jika sel berisi tanggal (kolom C atau F)
                        if (($col == 3 || $col == 6)) {
                            // Konversi ke format DD-MM-YYYY untuk tampilan
                            if (is_numeric($value) && $value > 0) {
                                try {
                                    $dateValue = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($value);
                                    $value = $dateValue->format('d-m-Y');
                                } catch (\Exception $e) {
                                    // Jika gagal konversi, biarkan nilai asli
                                }
                            }
                        }

                        // Jika ini adalah kolom PT (kolom terakhir) dan nilai adalah angka, konversi ke string
                        if ($col == $highestColumnIndex && is_numeric($value)) {
                            $value = (string)$value;
                        }

                        $rowData[] = $value;
                    }

                    // Cek apakah kita perlu membaca kolom PT secara eksplisit (kolom O atau indeks 16)
                    // Ini diperlukan karena PhpSpreadsheet mungkin tidak mendeteksi kolom PT jika kosong di beberapa baris
                    if ($highestColumnIndex < 16) {
                        // Baca kolom PT (kolom O atau indeks 16)
                        $ptColLetter = 'O';
                        $ptCellCoord = $ptColLetter . $row;

                        try {
                            $ptCell = $worksheet->getCell($ptCellCoord);
                            $ptValue = $ptCell->getValue();

                            // Simpan nilai PT untuk debugging
                            if ($row <= 4) {
                                $excelRawData[$row][16] = [
                                    'raw' => $ptValue,
                                    'type' => gettype($ptValue),
                                    'col_letter' => $ptColLetter,
                                    'explicit_read' => true
                                ];
                            }

                            // Jika ini adalah baris pertama (header), tambahkan header PT
                            if ($row == 1) {
                                // Jika nilai PT tidak kosong dan adalah 'PT', tambahkan ke rowData
                                if ($ptValue !== null && strtolower(trim($ptValue)) === 'pt') {
                                    // Jika header PT belum ada, tambahkan
                                    if (!$hasPTColumn) {
                                        $rowData[] = 'PT';
                                        $hasPTColumn = true;
                                    }
                                }
                                // Jika nilai PT kosong atau bukan 'PT', tambahkan header PT
                                else {
                                    $rowData[] = 'PT';
                                    $hasPTColumn = true;
                                }
                            }
                            // Jika ini adalah baris data, tambahkan nilai PT
                            else if ($row > 1) {
                                // Jika nilai PT tidak kosong, tambahkan ke rowData
                                if ($ptValue !== null && $ptValue !== '') {
                                    $rowData[] = $ptValue;
                                }
                                // Jika nilai PT kosong, tambahkan nilai default
                                else {
                                    $rowData[] = 'Unknown';
                                }
                            }
                        } catch (\Exception $e) {
                            // Jika gagal membaca kolom PT, abaikan
                            if ($row <= 4) {
                                $excelRawData[$row][16] = [
                                    'error' => 'Failed to read PT column',
                                    'message' => $e->getMessage()
                                ];
                            }
                        }
                    }

                    // Jika ini adalah baris pertama (header), cek apakah kolom terakhir adalah PT
                    // Kita tidak perlu melakukan ini lagi karena sudah ditangani di bagian sebelumnya
                    // Namun, kita tetap mempertahankan kode ini untuk kompatibilitas
                    if ($row == 1) {
                        // Jika kolom terakhir bukan PT, tambahkan header PT
                        $lastCol = end($rowData);
                        $lastColClean = strtolower(trim(preg_replace('/\*|\s*\([^)]*\)/', '', $lastCol)));
                        if ($lastColClean !== 'pt') {
                            // Hanya tambahkan header PT jika belum ada
                            if (!$hasPTColumn) {
                                $rowData[] = 'PT';
                                $hasPTColumn = true;
                            }
                        } else {
                            $hasPTColumn = true; // Set hasPTColumn ke true karena kolom PT sudah ada (tidak perlu menambahkan lagi)
                        }
                    }
                    // Jika ini adalah baris data dan kolom PT tidak ada, tambahkan nilai default
                    else if (!$hasPTColumn && $row > 1) {
                        $rowData[] = 'Unknown'; // Nilai default untuk PT
                    }
                    // Jika ini adalah baris data dan nilai PT adalah null atau empty, ganti dengan 'Unknown'
                    else if ($row > 1) {
                        // Cek apakah nilai PT (kolom terakhir) adalah null atau empty
                        $lastIndex = count($rowData) - 1;



                        // Hanya ganti dengan 'Unknown' jika nilai PT adalah null atau empty
                        if (isset($rowData[$lastIndex]) && ($rowData[$lastIndex] === null || $rowData[$lastIndex] === '' || trim($rowData[$lastIndex]) === '')) {
                            $rowData[$lastIndex] = 'Unknown';
                        }


                    }

                    $rows[] = $rowData;
                }



                // Validasi header
                $expected_header = ['NIK*', 'Nama*', 'Tgl Masuk* (Format: DD-MM-YYYY)', 'JK* (L/P)', 'Level Karyawan', 'Tgl Lahir (Format: DD-MM-YYYY)', 'Agama', 'Pendidikan Akhir', 'No. Telp', 'Dept', 'Bagian', 'Jabatan', 'Group', 'Status', 'PT'];
                $header = $rows[0];

                // Cek apakah kolom PT ada dalam header
                $lastHeaderIndex = count($header) - 1;
                if ($lastHeaderIndex >= 0 && !empty($header[$lastHeaderIndex])) {
                    $lastHeader = trim(strtolower(preg_replace('/\*|\s*\([^)]*\)/', '', $header[$lastHeaderIndex])));
                    if ($lastHeader === 'pt') {
                        $hasPTColumn = true;
                    }
                }

                // Hapus tanda bintang dan keterangan format dari header untuk perbandingan
                $clean_header = [];

                // Hapus kolom kosong di awal dan akhir
                $header_start = 0;
                $header_end = count($header) - 1;

                // Cari indeks awal yang tidak null/empty
                while ($header_start < count($header) && (is_null($header[$header_start]) || trim($header[$header_start]) === '')) {
                    $header_start++;
                }

                // Cari indeks akhir yang tidak null/empty
                while ($header_end >= 0 && (is_null($header[$header_end]) || trim($header[$header_end]) === '')) {
                    $header_end--;
                }

                // Jika semua header kosong, gunakan array kosong
                if ($header_start > $header_end) {
                    $clean_header = [];
                } else {
                    // Ambil hanya header yang tidak kosong
                    for ($i = $header_start; $i <= $header_end; $i++) {
                        $col = $header[$i];
                        // Bersihkan header: hapus tanda bintang, keterangan dalam kurung, dan trim spasi
                        $clean_col = trim(preg_replace('/\*|\s*\([^)]*\)/', '', $col));
                        // Konversi ke lowercase untuk perbandingan yang tidak case-sensitive
                        $clean_col = strtolower($clean_col);
                        $clean_header[] = $clean_col;
                    }
                }

                $expected_clean_header = [];
                foreach ($expected_header as $col) {
                    // Bersihkan header yang diharapkan dengan cara yang sama
                    $clean_col = trim(preg_replace('/\*|\s*\([^)]*\)/', '', $col));
                    $clean_col = strtolower($clean_col);
                    $expected_clean_header[] = $clean_col;
                }



                // Cek apakah jumlah kolom sama
                // Jika opsi abaikan validasi header diaktifkan, lewati validasi
                if (isset($_POST['ignore_header_validation']) && $_POST['ignore_header_validation'] == 1) {
                    // Hapus header dan lanjutkan
                    array_shift($rows);
                }
                // Jika jumlah kolom tidak sama, coba cek apakah semua kolom yang diharapkan ada dalam header
                else if (count($clean_header) !== count($expected_clean_header)) {
                    // Cek apakah semua kolom yang diharapkan ada dalam header (mungkin urutan berbeda)
                    $missing_columns = array_diff($expected_clean_header, $clean_header);
                    $extra_columns = array_diff($clean_header, $expected_clean_header);

                    // Jika hanya kolom PT yang hilang, kita bisa lanjutkan dengan peringatan
                    if (count($missing_columns) === 1 && in_array('pt', $missing_columns)) {
                        $warning_message = 'Kolom PT tidak ditemukan dalam file. Nilai default "Unknown" akan digunakan untuk kolom PT.';

                        // Tambahkan informasi tambahan ke pesan peringatan
                        $warning_message .= '<div class="mt-3"><strong>Informasi:</strong><br>';
                        $warning_message .= 'Sistem akan menambahkan kolom PT secara otomatis dengan nilai default "Unknown".<br>';
                        $warning_message .= 'Anda dapat mengubah nilai PT setelah data diimpor melalui fitur edit karyawan.';
                        $warning_message .= '</div>';

                        // Jangan hapus header di sini, akan dihapus nanti sesuai dengan mode (preview atau upload)
                    }
                    // Jika tidak ada kolom yang hilang, mungkin hanya ada kolom tambahan
                    else if (empty($missing_columns)) {
                        // Jika hanya ada kolom tambahan, kita bisa lanjutkan dengan peringatan
                        $warning_message = 'File memiliki ' . count($extra_columns) . ' kolom tambahan: ' . implode(', ', $extra_columns) . '. Kolom ini akan diabaikan.';
                        // Jangan hapus header di sini, akan dihapus nanti sesuai dengan mode (preview atau upload)
                    } else {
                        // Jika ada kolom yang hilang (selain PT), tampilkan error
                        $error_message = 'Jumlah kolom tidak sesuai dengan template. Template memerlukan ' . count($expected_clean_header) . ' kolom, tetapi file memiliki ' . count($clean_header) . ' kolom. ';
                        $error_message .= 'Kolom yang hilang: ' . implode(', ', $missing_columns) . '.';}
                }
                // Cek apakah header sesuai
                else if ($clean_header !== $expected_clean_header) {
                    // Temukan kolom yang berbeda untuk pesan error yang lebih spesifik
                    $diff_columns = [];
                    foreach ($clean_header as $index => $col) {
                        if (isset($expected_clean_header[$index]) && $col !== $expected_clean_header[$index]) {
                            $diff_columns[] = 'Kolom ' . ($index + 1) . ': ditemukan "' . $header[$index] . '", diharapkan "' . $expected_header[$index] . '"';
                        }
                    }

                    $error_message = 'Format header tidak sesuai dengan template. ' .
                                    (count($diff_columns) > 0 ? 'Perbedaan: ' . implode('; ', $diff_columns) : '') .
                                    ' Silakan gunakan template yang disediakan.';



                    // Tambahkan opsi untuk mengabaikan validasi header
                    $error_message .= '<div class="mt-3">';
                    $error_message .= '<form method="post" enctype="multipart/form-data">';
                    $error_message .= '<input type="hidden" name="ignore_header_validation" value="1">';

                    // Simpan file yang diupload ke temporary file dan gunakan path sebagai referensi
                    $temp_file = tempnam(sys_get_temp_dir(), 'excel_');
                    move_uploaded_file($file_to_process, $temp_file);

                    $error_message .= '<input type="hidden" name="temp_file" value="' . $temp_file . '">';
                    $error_message .= '<input type="hidden" name="original_filename" value="' . htmlspecialchars($file_name) . '">';
                    $error_message .= '<input type="hidden" name="action" value="' . $_POST['action'] . '">';
                    $error_message .= '<button type="submit" class="btn btn-warning">';
                    $error_message .= '<i class="fas fa-exclamation-triangle"></i> Abaikan Validasi Header dan Lanjutkan';
                    $error_message .= '</button>';
                    $error_message .= '</form>';
                    $error_message .= '</div>';
                } else {
                    // Jika mode preview, tampilkan data
                    if ($_POST['action'] === 'preview') {
                        // Simpan header untuk ditampilkan di preview
                        $header_row = $rows[0];

                        // Hapus header untuk memproses data
                        $data_rows = array_slice($rows, 1);

                        // Filter baris kosong dengan lebih ketat
                        $filtered_rows = array_filter($data_rows, function($row) {
                            // Cek apakah kolom NIK (indeks 1) atau Nama (indeks 2) memiliki nilai
                            // Ini akan memastikan hanya baris dengan data penting yang ditampilkan
                            return (isset($row[1]) && $row[1] !== null && $row[1] !== '') ||
                                   (isset($row[2]) && $row[2] !== null && $row[2] !== '');
                        });

                        // Ambil 10 baris pertama untuk preview
                        $data_preview = array_slice($filtered_rows, 0, 10);

                        // Tambahkan header ke preview data
                        $preview_data = array_merge([$header_row], $data_preview);

                        // Tambahkan debugging info
                        $debug_info['preview_mode'] = true;
                        $debug_info['header_row'] = $header_row;
                        $debug_info['data_preview'] = $data_preview;
                        $debug_info['preview_data'] = $preview_data;
                        $debug_info['filtered_rows_count'] = count($filtered_rows);
                        $debug_info['data_rows_count'] = count($data_rows);

                        // Jika tidak ada data untuk preview
                        if (count($preview_data) <= 1) { // Hanya header
                            $error_message = 'Tidak ada data yang dapat ditampilkan. File mungkin kosong atau hanya berisi header.';
                        }
                    } else {
                        // Hapus header untuk mode upload
                        array_shift($rows);
                        // Jika mode import, proses data
                        $total_rows = count($rows);
                        $total_success = 0;
                        $total_error = 0;
                        $total_skipped = 0; // Tambahkan counter untuk baris yang dilewati
                        $total_empty = 0;   // Tambahkan counter untuk baris kosong

                        // Tambahkan debugging info
                        $debug_info['upload_mode'] = true;
                        $debug_info['total_rows'] = $total_rows;
                        $error_details = [];

                        foreach ($rows as $index => $row) {
                            // Skip baris kosong
                            if (empty(array_filter($row))) {
                                $total_empty++;
                                continue;
                            }

                            // Validasi data
                            // Validasi dan batasi panjang data untuk kolom nik (varchar(20))
                            $nik = isset($row[1]) ? trim($row[1]) : '';
                            if (strlen($nik) > 20) {
                                $nik = substr($nik, 0, 20);
                            }

                            // Validasi dan batasi panjang data untuk kolom nama (varchar(100))
                            $nama = isset($row[2]) ? trim($row[2]) : '';
                            if (strlen($nama) > 100) {
                                $nama = substr($nama, 0, 100);
                            }

                            // Validasi dan batasi panjang data untuk kolom tgl_masuk (varchar(20))
                            $tgl_masuk = isset($row[3]) ? trim($row[3]) : '';
                            if (strlen($tgl_masuk) > 20) {
                                $tgl_masuk = substr($tgl_masuk, 0, 20);
                            }

                            // Validasi dan batasi panjang data untuk kolom jk (varchar(1))
                            $jk_original = isset($row[4]) ? $row[4] : '';
                            $jk = isset($row[4]) ? trim($row[4]) : '';

                            // Log nilai JK asli untuk debugging
                            error_log("JK original value for NIK $nik: '$jk_original', type: " . gettype($jk_original));

                            // Pastikan jk adalah string
                            if (!is_string($jk)) {
                                $jk = (string)$jk;
                            }

                            // Hapus tanda petik di awal jika ada (seperti pada tanggal)
                            if (substr($jk, 0, 1) === "'") {
                                $jk = substr($jk, 1);
                                error_log("Removed leading quote from JK: '$jk'");
                            }

                            // Konversi nilai numerik 0 menjadi 'L' dan 1 menjadi 'P'
                            if ($jk === '0') {
                                $jk = 'L';
                            } else if ($jk === '1') {
                                $jk = 'P';
                            }

                            // Konversi ke uppercase untuk konsistensi
                            $jk = strtoupper($jk);

                            // Log nilai JK setelah konversi
                            error_log("JK after conversion for NIK $nik: '$jk'");

                            // Debug info untuk JK
                            if ($index < 5) {
                                $debug_info['jk_debug'][$index] = [
                                    'original' => $jk_original,
                                    'trimmed' => $jk,
                                    'type_original' => gettype($jk_original),
                                    'type_trimmed' => gettype($jk),
                                    'final_value' => $jk
                                ];
                            }

                            if (strlen($jk) > 1) {
                                $jk = substr($jk, 0, 1);
                                error_log("JK truncated to first character for NIK $nik: '$jk'");
                            }

                            // Pastikan nilai JK adalah 'L' atau 'P'
                            if ($jk !== 'L' && $jk !== 'P') {
                                error_log("Invalid JK value for NIK $nik: '$jk', defaulting to 'L'");
                                // Default ke 'L' jika nilai tidak valid
                                $jk = 'L';
                            }

                            // Pastikan JK tidak kosong atau hanya berisi whitespace
                            if (trim($jk) === '') {
                                error_log("Empty JK value for NIK $nik, defaulting to 'L'");
                                $jk = 'L';
                            }

                            // Inisialisasi variabel lainnya dengan nilai default
                            $level_karyawan = isset($row[5]) && !empty($row[5]) ? intval(trim($row[5])) : 0;
                            $tgl_lahir = isset($row[6]) && !empty($row[6]) ? trim($row[6]) : '';

                            // Validasi dan batasi panjang data untuk kolom agama (varchar(20))
                            $agama = isset($row[7]) && !empty($row[7]) ? trim($row[7]) : '';
                            if (strlen($agama) > 20) {
                                $agama = substr($agama, 0, 20);
                            }

                            // Validasi dan batasi panjang data untuk kolom pendidikan_akhir (varchar(50))
                            $pendidikan_akhir = isset($row[8]) && !empty($row[8]) ? trim($row[8]) : '';
                            if (strlen($pendidikan_akhir) > 50) {
                                $pendidikan_akhir = substr($pendidikan_akhir, 0, 50);
                            }
                            $pendidikan = $pendidikan_akhir; // Alias untuk pendidikan_akhir

                            // Pastikan no_telp valid dan tidak melebihi 255 karakter
                            $no_telp = isset($row[9]) && !empty($row[9]) ? trim($row[9]) : '';

                            // Konversi ke string jika bukan string
                            if (!is_string($no_telp)) {
                                $no_telp = (string)$no_telp;
                            }

                            // Hapus karakter non-alfanumerik kecuali tanda hubung, plus, dan spasi
                            $no_telp = preg_replace('/[^0-9a-zA-Z\-\+\s]/', '', $no_telp);

                            // Batasi panjang maksimum
                            if (strlen($no_telp) > 255) {
                                $no_telp = substr($no_telp, 0, 255);
                            }

                            // Jika masih kosong setelah pembersihan, berikan nilai default
                            if (empty($no_telp)) {
                                $no_telp = '';
                            }

                            // Tambahkan debugging untuk no_telp
                            if ($index < 5) { // Hanya untuk 5 baris pertama
                                $debug_info['no_telp_debug'][$index] = [
                                    'original' => isset($row[9]) ? $row[9] : 'not set',
                                    'cleaned' => $no_telp,
                                    'length' => strlen($no_telp),
                                    'type' => gettype($no_telp)
                                ];
                            }

                            // Validasi dan batasi panjang data untuk kolom dept (varchar(50))
                            $dept = isset($row[10]) && !empty($row[10]) ? trim($row[10]) : '';
                            if (strlen($dept) > 50) {
                                $dept = substr($dept, 0, 50);
                            }
                            $departemen = $dept; // Alias untuk dept

                            // Validasi dan batasi panjang data untuk kolom bagian (varchar(50))
                            $bagian = isset($row[11]) && !empty($row[11]) ? trim($row[11]) : '';
                            if (strlen($bagian) > 50) {
                                $bagian = substr($bagian, 0, 50);
                            }

                            // Validasi dan batasi panjang data untuk kolom jabatan (varchar(50))
                            $jabatan = isset($row[12]) && !empty($row[12]) ? trim($row[12]) : '';
                            if (strlen($jabatan) > 50) {
                                $jabatan = substr($jabatan, 0, 50);
                            }

                            // Validasi dan batasi panjang data untuk kolom group (varchar(50))
                            $group = isset($row[13]) && !empty($row[13]) ? trim($row[13]) : '';
                            if (strlen($group) > 50) {
                                $group = substr($group, 0, 50);
                            }

                            // Validasi dan batasi panjang data untuk kolom status (varchar(20))
                            $status = isset($row[14]) && !empty($row[14]) ? trim($row[14]) : 'Aktif';
                            if (strlen($status) > 20) {
                                $status = substr($status, 0, 20);
                            }

                            // Validasi dan batasi panjang data untuk kolom pt (varchar(50))
                            $pt = isset($row[15]) && !empty($row[15]) ? trim($row[15]) : '';
                            if (strlen($pt) > 50) {
                                $pt = substr($pt, 0, 50);
                            }

                            // Definisikan variabel jenis_kelamin berdasarkan jk
                            $jenis_kelamin = $jk;

                            // Validasi field wajib
                            if (empty($nik) || empty($nama) || empty($tgl_masuk) || empty($jk)) {
                                $total_error++;
                                $error_details[] = "Baris " . ($index + 2) . ": Field wajib (NIK, Nama, Tgl Masuk, JK) tidak boleh kosong.";
                                continue;
                            }

                            // Simpan NIK asli (dengan tanda strip jika ada)
                            $original_nik = $nik;

                            // Normalisasi NIK untuk pencarian dan perbandingan
                            $normalized_nik = normalizeNIK($nik);

                            // Validasi NIK setelah normalisasi
                            if (empty($normalized_nik)) {
                                $total_error++;
                                $error_details[] = "Baris " . ($index + 2) . ": NIK tidak valid. NIK harus mengandung angka.";
                                continue;
                            }

                            // Format NIK dengan mempertahankan format asli
                            $formatted_nik = formatNIK($original_nik);

                            // Gunakan NIK yang sudah diformat untuk disimpan ke database
                            $nik = $formatted_nik;

                            // Log untuk debugging
                            error_log("NIK processing - Original: $original_nik, Normalized: $normalized_nik, Formatted: $formatted_nik");

                            // Validasi JK sudah dilakukan di atas, tidak perlu validasi lagi di sini

                            // Validasi format tanggal
                            $tgl_masuk_formatted = null;
                            if (!empty($tgl_masuk)) {
                                // Hapus tanda petik di awal jika ada
                                if (substr($tgl_masuk, 0, 1) === "'") {
                                    $tgl_masuk = substr($tgl_masuk, 1);
                                }

                                // Gunakan fungsi formatDate dengan format dd-mm-yyyy sesuai struktur tabel
                                $tgl_masuk_formatted = formatDate($tgl_masuk, 'dd-mm-yyyy');

                                // Jika tidak ada format yang cocok
                                if (!$tgl_masuk_formatted) {
                                    $total_error++;
                                    $error_details[] = "Baris " . ($index + 2) . ": Format Tgl Masuk tidak valid. Gunakan format DD-MM-YYYY atau DD/MM/YYYY.";
                                    continue;
                                }
                            }

                            $tgl_lahir_formatted = null;
                            if (isset($row[6]) && !empty($row[6])) {
                                $tgl_lahir = $row[6];

                                // Hapus tanda petik di awal jika ada
                                if (substr($tgl_lahir, 0, 1) === "'") {
                                    $tgl_lahir = substr($tgl_lahir, 1);
                                }

                                // Gunakan fungsi formatDate dengan format dd-mm-yyyy sesuai struktur tabel
                                $tgl_lahir_formatted = formatDate($tgl_lahir, 'dd-mm-yyyy');

                                // Jika tidak ada format yang cocok
                                if (!$tgl_lahir_formatted) {
                                    $total_error++;
                                    $error_details[] = "Baris " . ($index + 2) . ": Format Tgl Lahir tidak valid. Gunakan format DD-MM-YYYY atau DD/MM/YYYY.";
                                    continue;
                                }
                            }

                            // Siapkan data karyawan untuk pengecekan duplikasi
                            $employee_data = [
                                'nik' => $nik,
                                'nama' => $nama,
                                'tgl_lahir' => $tgl_lahir_formatted,
                                'no_telp' => $no_telp ?? '',
                                'dept' => $dept ?? '',
                                'bagian' => $bagian ?? '',
                                'jabatan' => $jabatan ?? ''
                            ];

                            // Cek apakah NIK sudah ada dengan format yang sama atau berbeda
                            $existing_employee = checkNIKExists($conn, $nik);
                            $row_count = $existing_employee ? 1 : 0;
                            $existing_id = $existing_employee ? $existing_employee['id'] : null;

                            // Log data karyawan yang sudah ada untuk debugging
                            if ($existing_employee) {
                                error_log("Existing employee data for NIK $nik: " . json_encode($existing_employee));
                            }

                            // Tambahkan debugging untuk NIK dan mode upload
                            if ($index < 5) { // Hanya untuk 5 baris pertama
                                $debug_info['nik_check'][$index] = [
                                    'original_nik' => $original_nik,
                                    'normalized_nik' => $normalized_nik,
                                    'formatted_nik' => $formatted_nik,
                                    'nik_used' => $nik,
                                    'existing' => $row_count > 0 ? 'yes' : 'no',
                                    'upload_mode' => $upload_mode,
                                    'will_skip' => ($row_count > 0 && $upload_mode === 'add') ? 'yes' : 'no'
                                ];
                            }

                            // Jika NIK tidak ditemukan, cek kemungkinan duplikasi berdasarkan nama dan data lainnya
                            if (!$row_count) {
                                $possible_duplicates = checkPossibleDuplicates($conn, $employee_data);

                                // Jika ada kemungkinan duplikasi, tambahkan peringatan
                                if (!empty($possible_duplicates)) {
                                    foreach ($possible_duplicates as $duplicate) {
                                        if (isPossibleSamePerson($employee_data, $duplicate)) {
                                            // Tambahkan peringatan tapi tetap lanjutkan proses
                                            $warning_message = "Kemungkinan duplikasi data ditemukan. Beberapa karyawan memiliki data yang sama dengan NIK berbeda.";
                                            break;
                                        }
                                    }
                                }
                            }

                            // Mode upload sudah ditentukan di awal proses
                            // Tidak perlu mengambil lagi di sini, gunakan variabel $upload_mode yang sudah ada

                            // Tambahkan debug info untuk mode upload
                            error_log("Processing row " . ($index + 2) . " with NIK: $nik, Mode: $upload_mode, Exists: " . ($row_count > 0 ? 'Yes' : 'No') . ", Import Only Changed: " . ($import_only_changed ? 'Yes' : 'No'));

                            // Jika NIK sudah ada dan mode adalah 'add', skip
                            if ($row_count > 0 && $upload_mode === 'add') {
                                $total_skipped++; // Hitung sebagai skipped, bukan error
                                $error_details[] = "Baris " . ($index + 2) . ": NIK $nik sudah terdaftar dengan format " . $existing_employee['nik'] . ". Format NIK akan disesuaikan menjadi format standar dengan tanda strip (-). Gunakan mode 'Update Data' untuk mengupdate.";
                                // Simpan NIK yang dilewati
                                $skipped_niks[] = $nik;
                                error_log("Skipping row " . ($index + 2) . " with NIK: $nik (already exists and mode is 'add')");
                                continue;
                            }

                            // Jika NIK sudah ada dan mode adalah 'update', cek apakah perlu memeriksa perubahan data
                            if ($row_count > 0 && $upload_mode === 'update' && $import_only_changed) {
                                // Hanya periksa perubahan data jika opsi import_only_changed aktif
                                // Ambil data karyawan yang sudah ada
                                $existing_data = checkNIKExists($conn, $nik);

                                // Cek apakah data berubah
                                $data_changed = false;

                                // Bandingkan data yang ada dengan data baru
                                if ($existing_data) {
                                    // Daftar kolom yang akan dibandingkan
                                    $compare_columns = [
                                        'nama' => $nama,
                                        'jk' => $jk, // Gunakan jk bukan jenis_kelamin untuk konsistensi
                                        'tgl_lahir' => $tgl_lahir_formatted,
                                        'tgl_masuk' => $tgl_masuk_formatted,
                                        'agama' => $agama,
                                        'pendidikan_akhir' => $pendidikan_akhir,
                                        'no_telp' => $no_telp,
                                        'dept' => $dept,
                                        'bagian' => $bagian,
                                        'jabatan' => $jabatan,
                                        'group' => $group,
                                        'status' => $status,
                                        'pt' => $pt,
                                        'level_karyawan' => $level_karyawan
                                    ];

                                    // Bandingkan setiap kolom
                                    foreach ($compare_columns as $column => $new_value) {
                                        if (isset($existing_data[$column]) && $existing_data[$column] != $new_value) {
                                            $data_changed = true;
                                            error_log("Data changed for NIK $nik: column $column, old value: {$existing_data[$column]}, new value: $new_value");
                                            break;
                                        }
                                    }

                                    // Jika data tidak berubah, skip
                                    if (!$data_changed) {
                                        $total_skipped++; // Hitung sebagai skipped, bukan error
                                        $error_details[] = "Baris " . ($index + 2) . ": NIK $nik dilewati karena data tidak berubah.";
                                        // Simpan NIK yang dilewati
                                        $skipped_niks[] = $nik;
                                        error_log("Skipping row " . ($index + 2) . " with NIK: $nik (data not changed)");
                                        continue;
                                    }
                                }
                            }

                            // Jika mode adalah 'update' tanpa import_only_changed, lanjutkan tanpa memeriksa perubahan data

                            // Validasi dan format tanggal lahir jika ada
                            if (!empty($tgl_lahir)) {
                                // Hapus tanda petik di awal jika ada
                                if (substr($tgl_lahir, 0, 1) === "'") {
                                    $tgl_lahir = substr($tgl_lahir, 1);
                                }
                            }

                            // Cek apakah kolom PT hilang dan berikan nilai default jika perlu
                            if (count($row) < 16 || $pt === null) {
                                $pt = 'Unknown'; // Nilai default untuk PT jika kolom tidak ada
                            }

                            // Cek apakah kolom normalized_nik sudah ada
                            $check_column_query = "SHOW COLUMNS FROM karyawan LIKE 'normalized_nik'";
                            $check_column_result = $conn->query($check_column_query);
                            $has_normalized_column = $check_column_result->num_rows > 0;

                            // Log struktur tabel karyawan untuk debugging
                            $table_structure_query = "DESCRIBE karyawan";
                            $table_structure_result = $conn->query($table_structure_query);
                            $table_structure = [];
                            if ($table_structure_result) {
                                while ($row = $table_structure_result->fetch_assoc()) {
                                    $table_structure[] = $row;
                                }
                                error_log("Table structure for karyawan: " . json_encode($table_structure));
                            } else {
                                error_log("Failed to get table structure for karyawan: " . $conn->error);
                            }

                            // Tentukan query berdasarkan mode upload dan keberadaan NIK
                            if ($row_count > 0 && $upload_mode === 'update') {
                                // Update data yang sudah ada
                                // Log query parameters untuk debugging
                                error_log("Update parameters - NIK: $nik, JK: $jk, No Telp: $no_telp, Level: $level_karyawan");

                                // Selalu gunakan kolom normalized_nik jika tersedia
                                if ($has_normalized_column) {
                                    $query = "UPDATE karyawan SET
                                              nama = ?,
                                              tgl_masuk = ?,
                                              jk = ?,
                                              level_karyawan = ?,
                                              tgl_lahir = ?,
                                              agama = ?,
                                              pendidikan_akhir = ?,
                                              no_telp = ?,
                                              dept = ?,
                                              bagian = ?,
                                              jabatan = ?,
                                              `group` = ?,
                                              status = ?,
                                              pt = ?,
                                              normalized_nik = ?
                                              WHERE nik = ?";
                                } else {
                                    $query = "UPDATE karyawan SET
                                              nama = ?,
                                              tgl_masuk = ?,
                                              jk = ?,
                                              level_karyawan = ?,
                                              tgl_lahir = ?,
                                              agama = ?,
                                              pendidikan_akhir = ?,
                                              no_telp = ?,
                                              dept = ?,
                                              bagian = ?,
                                              jabatan = ?,
                                              `group` = ?,
                                              status = ?,
                                              pt = ?
                                              WHERE nik = ?";
                                }
                            } else {
                                // Insert data baru
                                if ($has_normalized_column) {
                                    $query = "INSERT INTO karyawan (nik, normalized_nik, nama, tgl_masuk, jk, level_karyawan, tgl_lahir, agama, pendidikan_akhir, no_telp, dept, bagian, jabatan, `group`, status, pt)
                                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                                } else {
                                    $query = "INSERT INTO karyawan (nik, nama, tgl_masuk, jk, level_karyawan, tgl_lahir, agama, pendidikan_akhir, no_telp, dept, bagian, jabatan, `group`, status, pt)
                                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                                }
                            }

                            $stmt = $conn->prepare($query);

                            // Bind parameter berbeda untuk update dan insert
                            if ($row_count > 0 && $upload_mode === 'update') {
                                // Untuk update, NIK diletakkan di akhir untuk WHERE clause
                                // Log binding parameters untuk debugging
                                error_log("Binding parameters for UPDATE - NIK: $nik, Normalized NIK: $normalized_nik");

                                if ($has_normalized_column) {
                                    // Pastikan semua parameter memiliki nilai yang valid
                                    $nama = $nama ?: '';
                                    $tgl_masuk_formatted = $tgl_masuk_formatted ?: '';
                                    $jk = $jk ?: 'L';
                                    $level_karyawan = $level_karyawan ?: 0;
                                    $tgl_lahir_formatted = $tgl_lahir_formatted ?: '';
                                    $agama = $agama ?: '';
                                    $pendidikan_akhir = $pendidikan_akhir ?: '';
                                    $no_telp = $no_telp ?: '';
                                    $dept = $dept ?: '';
                                    $bagian = $bagian ?: '';
                                    $jabatan = $jabatan ?: '';
                                    $group = $group ?: '';
                                    $status = $status ?: 'Aktif';
                                    $pt = $pt ?: '';
                                    // Selalu gunakan NIK yang dinormalisasi untuk kolom normalized_nik
                                    $normalized_nik = normalizeNIK($nik);

                                    // Log nilai JK sebelum binding
                                    error_log("JK value before binding for NIK $nik: '$jk'");

                                    $stmt->bind_param("ssisssssssssssss",
                                        $nama,
                                        $tgl_masuk_formatted,
                                        $jk,
                                        $level_karyawan,
                                        $tgl_lahir_formatted,
                                        $agama,
                                        $pendidikan_akhir,
                                        $no_telp,
                                        $dept,
                                        $bagian,
                                        $jabatan,
                                        $group,
                                        $status,
                                        $pt,
                                        $normalized_nik, // Normalized NIK untuk kolom normalized_nik
                                        $nik // NIK asli untuk WHERE clause
                                    );
                                } else {
                                    // Pastikan semua parameter memiliki nilai yang valid
                                    $nama = $nama ?: '';
                                    $tgl_masuk_formatted = $tgl_masuk_formatted ?: '';
                                    $jk = $jk ?: 'L';
                                    $level_karyawan = $level_karyawan ?: 0;
                                    $tgl_lahir_formatted = $tgl_lahir_formatted ?: '';
                                    $agama = $agama ?: '';
                                    $pendidikan_akhir = $pendidikan_akhir ?: '';
                                    $no_telp = $no_telp ?: '';
                                    $dept = $dept ?: '';
                                    $bagian = $bagian ?: '';
                                    $jabatan = $jabatan ?: '';
                                    $group = $group ?: '';
                                    $status = $status ?: 'Aktif';
                                    $pt = $pt ?: '';

                                    $stmt->bind_param("ssissssssssssss",
                                        $nama,
                                        $tgl_masuk_formatted,
                                        $jk,
                                        $level_karyawan,
                                        $tgl_lahir_formatted,
                                        $agama,
                                        $pendidikan_akhir,
                                        $no_telp,
                                        $dept,
                                        $bagian,
                                        $jabatan,
                                        $group,
                                        $status,
                                        $pt,
                                        $nik // NIK untuk WHERE clause
                                    );
                                }
                            } else {
                                // Untuk insert, NIK diletakkan di awal
                                // Log binding parameters untuk debugging
                                error_log("Binding parameters for INSERT - NIK: $nik, Normalized NIK: $normalized_nik");

                                // Pastikan semua parameter memiliki nilai yang valid
                                $nama = $nama ?: '';
                                $tgl_masuk_formatted = $tgl_masuk_formatted ?: '';
                                $jk = $jk ?: 'L';
                                $level_karyawan = $level_karyawan ?: 0;
                                $tgl_lahir_formatted = $tgl_lahir_formatted ?: '';
                                $agama = $agama ?: '';
                                $pendidikan_akhir = $pendidikan_akhir ?: '';
                                $no_telp = $no_telp ?: '';
                                $dept = $dept ?: '';
                                $bagian = $bagian ?: '';
                                $jabatan = $jabatan ?: '';
                                $group = $group ?: '';
                                $status = $status ?: 'Aktif';
                                $pt = $pt ?: '';
                                // Selalu gunakan NIK yang dinormalisasi untuk kolom normalized_nik
                                $normalized_nik = normalizeNIK($nik);

                                // Log nilai JK sebelum binding untuk INSERT
                                error_log("JK value before binding for INSERT NIK $nik: '$jk'");

                                if ($has_normalized_column) {
                                    $stmt->bind_param("ssssisssssssssss",
                                        $nik, // NIK asli
                                        $normalized_nik, // Normalized NIK
                                        $nama,
                                        $tgl_masuk_formatted,
                                        $jk,
                                        $level_karyawan,
                                        $tgl_lahir_formatted,
                                        $agama,
                                        $pendidikan_akhir,
                                        $no_telp,
                                        $dept,
                                        $bagian,
                                        $jabatan,
                                        $group,
                                        $status,
                                        $pt
                                    );
                                } else {
                                    $stmt->bind_param("sssisssssssssss",
                                        $nik,
                                        $nama,
                                        $tgl_masuk_formatted,
                                        $jk,
                                        $level_karyawan,
                                        $tgl_lahir_formatted,
                                        $agama,
                                        $pendidikan_akhir,
                                        $no_telp,
                                        $dept,
                                        $bagian,
                                        $jabatan,
                                        $group,
                                        $status,
                                        $pt
                                    );
                                }
                            }

                            // Tambahkan debugging info untuk data yang akan diinsert
                            if ($index < 3) { // Hanya untuk 3 baris pertama
                                $debug_info['insert_data'][$index] = [
                                    'nik' => $nik,
                                    'nama' => $nama,
                                    'tgl_masuk' => $tgl_masuk_formatted,
                                    'jk' => $jk,
                                    'level_karyawan' => $level_karyawan,
                                    'tgl_lahir' => $tgl_lahir_formatted,
                                    'agama' => $agama,
                                    'pendidikan_akhir' => $pendidikan_akhir,
                                    'no_telp' => $no_telp,
                                    'dept' => $dept,
                                    'bagian' => $bagian,
                                    'jabatan' => $jabatan,
                                    'group' => $group,
                                    'status' => $status,
                                    'pt' => $pt
                                ];
                            }

                            // Log query yang akan dieksekusi untuk debugging
                            error_log("Executing query for NIK: $nik, Mode: $upload_mode, Row exists: " . ($row_count > 0 ? 'Yes' : 'No'));

                            // Tambahkan try-catch untuk menangkap error yang mungkin terjadi
                            try {
                                if ($stmt->execute()) {
                                    // Periksa jumlah baris yang terpengaruh
                                    $affected_rows = $stmt->affected_rows;
                                    error_log("Query executed successfully. Affected rows: $affected_rows");

                                    // Jika tidak ada baris yang terpengaruh (untuk UPDATE)
                                    if ($affected_rows == 0 && $row_count > 0 && $upload_mode === 'update') {
                                        // Ini mungkin karena data yang diupload sama persis dengan data yang sudah ada
                                        error_log("No rows affected for UPDATE operation on NIK: $nik");

                                        // Jika mode update tanpa import_only_changed, hitung sebagai success
                                        if (!$import_only_changed) {
                                            // Hitung sebagai success karena update berhasil (meskipun tidak ada perubahan)
                                            $total_success++;
                                            $total_update = isset($total_update) ? $total_update + 1 : 1;
                                            error_log("Counting as success for NIK: $nik (no changes but update mode without import_only_changed)");
                                        } else {
                                            // Jika import_only_changed aktif, hitung sebagai skipped
                                            $total_skipped++;
                                            $error_details[] = "Baris " . ($index + 2) . ": NIK $nik tidak diupdate karena data tidak berubah.";
                                            // Simpan NIK yang dilewati
                                            $skipped_niks[] = $nik;
                                            error_log("Skipping NIK: $nik (no changes and import_only_changed is active)");
                                        }
                                    }
                                    // Jika ada baris yang terpengaruh
                                    else if ($affected_rows > 0) {
                                        // Tambahkan counter berdasarkan mode
                                        if ($row_count > 0 && $upload_mode === 'update') {
                                            // Jika update berhasil
                                            $total_update = isset($total_update) ? $total_update + 1 : 1;
                                            error_log("Update successful for NIK: $nik");
                                        } else {
                                            // Jika insert berhasil
                                            $total_insert = isset($total_insert) ? $total_insert + 1 : 1;
                                            error_log("Insert successful for NIK: $nik");
                                        }
                                        $total_success++;
                                    }
                                    // Jika tidak ada baris yang terpengaruh (untuk INSERT)
                                    else {
                                        // Ini mungkin karena constraint atau masalah lain
                                        $total_error++;
                                        $error_details[] = "Baris " . ($index + 2) . ": Gagal menyimpan data. Tidak ada baris yang terpengaruh.";
                                        error_log("Failed to save data for NIK: $nik. No rows affected.");
                                    }
                                } else {
                                    $total_error++;
                                    $error_message = "Baris " . ($index + 2) . ": Gagal menyimpan data. Error: " . $stmt->error;
                                    $error_details[] = $error_message;
                                    error_log("SQL Error: " . $error_message);

                                    // Tambahkan error ke debug info
                                    $debug_info['excel_read'] = 'error';
                                    $debug_info['excel_error'] = $stmt->error;
                                    $debug_info['excel_error_trace'] = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);

                                    // Tambahkan NIK yang gagal ke daftar yang dilewati
                                    $skipped_niks[] = $nik;
                                    $total_skipped++;
                                }
                            } catch (Exception $e) {
                                $total_error++;
                                $error_message = "Baris " . ($index + 2) . ": Exception saat menyimpan data. Error: " . $e->getMessage();
                                $error_details[] = $error_message;
                                error_log("Exception: " . $error_message);

                                // Tambahkan error ke debug info
                                $debug_info['excel_read'] = 'error';
                                $debug_info['excel_error'] = $e->getMessage();
                                $debug_info['excel_error_trace'] = $e->getTraceAsString();

                                // Tambahkan NIK yang gagal ke daftar yang dilewati
                                $skipped_niks[] = $nik;
                                $total_skipped++;
                            }
                        }

                        // Log aktivitas
                        $user_id = $_SESSION['user_id'];
                        $log_query = "INSERT INTO activity_logs (user_id, action, category, timestamp)
                                      VALUES (?, CONCAT('Upload karyawan: ', ? ,' berhasil, ', ?, ' gagal'), 'employee', NOW())";

                        $log_stmt = $conn->prepare($log_query);
                        $log_stmt->bind_param("iii", $user_id, $total_success, $total_error);
                        $log_stmt->execute();

                        // Record batch history
                        $action_type = 'BATCH_INSERT';
                        if ($upload_mode === 'update') {
                            $action_type = 'BATCH_UPDATE';
                        }

                        // Collect all NIKs processed (successful, skipped, and failed)
                        $all_niks = [];
                        foreach ($rows as $index => $row) {
                            if ($index > 0 && !empty($row[0])) { // Skip header row and empty rows
                                $all_niks[] = $row[0]; // NIK is in the first column
                            }
                        }

                        // Record batch history
                        $batch_id = recordBatchEmployeeHistory(
                            $conn,
                            $action_type,
                            $all_niks,
                            $upload_mode,
                            $total_success,
                            $total_error,
                            $total_skipped
                        );

                        if ($batch_id) {
                            error_log("Batch history recorded with ID: $batch_id");
                        } else {
                            error_log("Failed to record batch history");
                        }

                        // Set pesan sukses
                        if ($total_success > 0) {
                            $total_insert = $total_insert ?? 0;
                            $total_update = $total_update ?? 0;

                            // Hitung total baris yang diproses
                            $total_processed = $total_success + $total_error + $total_skipped + $total_empty;

                            $success_message = "Berhasil mengimport $total_success data karyawan";

                            // Tambahkan detail insert dan update jika ada
                            if ($total_insert > 0 || $total_update > 0) {
                                $success_message .= " (";

                                if ($total_insert > 0) {
                                    $success_message .= "$total_insert data baru";
                                    if ($total_update > 0) {
                                        $success_message .= ", ";
                                    }
                                }

                                if ($total_update > 0) {
                                    $success_message .= "$total_update data diupdate";
                                }

                                $success_message .= ").";
                            } else {
                                $success_message .= ".";
                            }

                            // Tambahkan informasi tentang baris yang dilewati dan kosong
                            if ($total_skipped > 0) {
                                // Tentukan alasan dilewati berdasarkan mode
                                $skip_reason = "";
                                if ($upload_mode === 'add') {
                                    $skip_reason = "NIK sudah ada";
                                } else if ($import_only_changed) {
                                    $skip_reason = "NIK sudah ada atau data tidak berubah";
                                } else {
                                    $skip_reason = "terjadi kesalahan saat memproses data";
                                }

                                $success_message .= " $total_skipped data dilewati karena $skip_reason.";

                                // Tambahkan detail NIK yang dilewati
                                if (!empty($skipped_niks)) {
                                    // Batasi jumlah NIK yang ditampilkan jika terlalu banyak
                                    if (count($skipped_niks) > 10) {
                                        $displayed_niks = array_slice($skipped_niks, 0, 10);
                                        $success_message .= "<br><small class='text-muted'>NIK yang dilewati: " . implode(", ", $displayed_niks) . ", dan " . (count($skipped_niks) - 10) . " lainnya.</small>";
                                    } else {
                                        $success_message .= "<br><small class='text-muted'>NIK yang dilewati: " . implode(", ", $skipped_niks) . "</small>";
                                    }
                                }
                            }

                            if ($total_empty > 0) {
                                $success_message .= " $total_empty baris kosong diabaikan.";
                            }

                            if ($total_error > 0) {
                                $success_message .= " $total_error data gagal diimport.";
                            }

                            // Tambahkan informasi tentang total baris dalam file
                            $success_message .= "<br><small class='text-muted'>Total baris dalam file: $total_rows (tidak termasuk header).</small>";

                            // Tambahkan informasi tentang perbedaan jumlah
                            if ($total_processed < $total_rows) {
                                $difference = $total_rows - $total_processed;
                                $success_message .= "<br><small class='text-muted'>Catatan: $difference baris tidak diproses karena format tidak valid atau alasan lainnya.</small>";
                            }

                            // Tambahkan penjelasan tentang perbedaan jumlah data
                            $success_message .= "<br><small class='text-muted'><strong>Penting:</strong> Jumlah data yang berhasil diimport ($total_success) mungkin berbeda dengan jumlah data yang terlihat di database karena:</small>";
                            $success_message .= "<br><small class='text-muted'>1. Beberapa data mungkin merupakan update untuk NIK yang sama (duplikat)</small>";
                            $success_message .= "<br><small class='text-muted'>2. Beberapa data mungkin tidak memenuhi kriteria untuk ditampilkan di halaman manajemen karyawan</small>";
                            $success_message .= "<br><small class='text-muted'>3. Untuk melihat jumlah data yang sebenarnya ada di database, gunakan <a href='check_employee_count.php' class='alert-link'>Statistik Data Karyawan</a></small>";
                            $success_message .= "<br><small class='text-muted'><strong>Catatan Format NIK:</strong> Sistem secara otomatis menormalisasi NIK dengan menghapus karakter non-angka untuk konsistensi data.</small>";
                        } else {
                            $error_message = "Gagal mengimport data karyawan. $total_error data gagal diimport.";

                            // Tambahkan informasi tentang baris yang dilewati dan kosong
                            if ($total_skipped > 0) {
                                // Tentukan alasan dilewati berdasarkan mode
                                $skip_reason = "";
                                if ($upload_mode === 'add') {
                                    $skip_reason = "NIK sudah ada";
                                } else if ($import_only_changed) {
                                    $skip_reason = "NIK sudah ada atau data tidak berubah";
                                } else {
                                    $skip_reason = "terjadi kesalahan saat memproses data";
                                }

                                $error_message .= " $total_skipped data dilewati karena $skip_reason.";

                                // Tambahkan detail NIK yang dilewati
                                if (!empty($skipped_niks)) {
                                    // Batasi jumlah NIK yang ditampilkan jika terlalu banyak
                                    if (count($skipped_niks) > 10) {
                                        $displayed_niks = array_slice($skipped_niks, 0, 10);
                                        $error_message .= "<br><small class='text-muted'>NIK yang dilewati: " . implode(", ", $displayed_niks) . ", dan " . (count($skipped_niks) - 10) . " lainnya.</small>";
                                    } else {
                                        $error_message .= "<br><small class='text-muted'>NIK yang dilewati: " . implode(", ", $skipped_niks) . "</small>";
                                    }
                                }
                            }

                            if ($total_empty > 0) {
                                $error_message .= " $total_empty baris kosong diabaikan.";
                            }

                            // Tambahkan informasi tentang total baris dalam file
                            $error_message .= "<br><small class='text-muted'>Total baris dalam file: $total_rows (tidak termasuk header).</small>";
                            $error_message .= "<br><small class='text-muted'><strong>Catatan Format NIK:</strong> Sistem secara otomatis menormalisasi NIK dengan menghapus karakter non-angka untuk konsistensi data.</small>";
                        }

                        // Hapus file temporary jika ada
                        if ($is_temp_file && file_exists($file_to_process)) {
                            @unlink($file_to_process);
                        }
                    }
                }
            } catch (Exception $e) {
                $error_message = 'Terjadi kesalahan: ' . $e->getMessage();

                // Tambahkan debugging info
                $debug_info['excel_read'] = 'error';
                $debug_info['excel_error'] = $e->getMessage();
                $debug_info['excel_error_trace'] = $e->getTraceAsString();

                // Hapus file temporary jika ada
                if (isset($is_temp_file) && $is_temp_file && isset($file_to_process) && file_exists($file_to_process)) {
                    @unlink($file_to_process);
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    body {
        background-color: #f5f5f5;
        overflow-x: hidden;
    }

    .container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
    }

    .card {
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 20px;
    }

    .card-header {
        background-color: #BF0000 !important;
        color: white;
        padding: 15px 20px;
    }

    .card-header h3 {
        margin: 0;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
    }

    .card-header h3 i {
        margin-right: 10px;
    }

    .card-body {
        padding: 20px;
        background-color: white;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 5px;
        display: block;
    }

    .form-control {
        width: 100%;
        padding: 10px;
        border: 1px solid #ced4da;
        border-radius: 5px;
    }

    .btn-primary {
        background-color: #BF0000;
        border-color: #BF0000;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #a00000;
        border-color: #a00000;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #5a6268;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    .alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }

    .table-container {
        overflow-x: auto;
        margin-top: 20px;
    }

    .table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .table th {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 600;
        text-align: left;
        padding: 12px 15px;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        padding: 12px 15px;
        border-bottom: 1px solid #e9ecef;
        color: #495057;
    }

    .table tr:hover {
        background-color: #f8f9fa;
    }

    .steps {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20px;
        gap: 20px;
    }

    .step {
        flex: 1;
        min-width: 200px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
        border-left: 4px solid #BF0000;
    }

    .step-number {
        display: inline-block;
        width: 30px;
        height: 30px;
        background-color: #BF0000;
        color: white;
        border-radius: 50%;
        text-align: center;
        line-height: 30px;
        margin-right: 10px;
        font-weight: bold;
    }

    .step-title {
        font-weight: 600;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .step-description {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .error-details {
        max-height: 200px;
        overflow-y: auto;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-top: 10px;
    }

    .error-details ul {
        margin: 0;
        padding-left: 20px;
    }

    .error-details li {
        margin-bottom: 5px;
    }

    /* Alert styles are already defined by Bootstrap */

    .jarak {
        height: 100px;
    }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container">
    <div class="card">
        <div class="card-header">
            <h3><i class="fas fa-upload"></i> Upload Data Karyawan</h3>
        </div>
        <div class="card-body">
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($warning_message)): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $warning_message; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <div class="steps">
                <div class="step">
                    <div class="step-title">
                        <span class="step-number">1</span> Download Template
                    </div>
                    <div class="step-description">
                        Download template Excel untuk upload data karyawan. Template berisi format yang benar dan contoh data.
                    </div>
                    <div class="mt-3">
                        <a href="templates/template_karyawan.php" class="btn btn-primary">
                            <i class="fas fa-download"></i> Download Template
                        </a>
                    </div>
                </div>

                <div class="step">
                    <div class="step-title">
                        <span class="step-number">2</span> Isi Template
                    </div>
                    <div class="step-description">
                        Isi template dengan data karyawan yang ingin diupload. Pastikan mengikuti format yang sudah ditentukan.
                    </div>
                </div>

                <div class="step">
                    <div class="step-title">
                        <span class="step-number">3</span> Upload File
                    </div>
                    <div class="step-description">
                        Upload file Excel yang sudah diisi. Sistem akan memvalidasi data sebelum menyimpannya.
                    </div>
                </div>
            </div>

            <div class="alert alert-info">
                <h4 class="alert-heading"><i class="fas fa-info-circle"></i> Informasi Penting</h4>
                <p>Sistem akan secara otomatis menormalisasi NIK dengan menghapus karakter non-angka. Hal ini untuk mencegah duplikasi data karyawan dengan NIK yang sama namun format berbeda.</p>
                <p class="mb-0">Contoh: NIK "305000181" dan "3050-00181" akan dianggap sebagai NIK yang sama dan disimpan sebagai "305000181".</p>
            </div>

            <form method="post" enctype="multipart/form-data" id="uploadForm">
                <div class="form-group">
                    <label class="form-label">Mode Upload</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="upload_mode" id="modeAdd" value="add" checked>
                        <label class="form-check-label" for="modeAdd">
                            Tambah Data Baru (Skip jika NIK sudah ada)
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="upload_mode" id="modeUpdate" value="update">
                        <label class="form-check-label" for="modeUpdate">
                            Update Data (Update jika NIK sudah ada, tambah jika belum)
                        </label>
                    </div>
                    <div class="alert alert-info mt-2">
                        <i class="fas fa-info-circle"></i> <strong>Mode saat ini:</strong>
                        <span id="currentModeText"><?php echo $upload_mode === 'update' ? 'Update Data (Lengkap)' : 'Tambah Data Baru'; ?></span>
                        <span id="onlyChangedBadge" class="badge bg-secondary ms-2" style="<?php echo ($import_only_changed && $upload_mode === 'update') ? '' : 'display: none;' ?>">
                            Hanya import data yang berubah
                        </span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Pilih File Excel</label>
                    <input type="file" name="excel_file" id="excelFile" class="form-control" accept=".xlsx, .xls" required>
                    <small class="text-muted">File harus berformat Excel (.xlsx atau .xls)</small>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="import_only_changed" id="importOnlyChanged" value="1" <?php echo $import_only_changed ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="importOnlyChanged">
                            Hanya import data yang berubah (untuk mode Update Data)
                        </label>
                    </div>
                    <small class="text-muted">Jika dicentang, sistem hanya akan mengupdate data yang benar-benar berubah. Data yang sama persis dengan yang sudah ada di database akan dilewati.</small>
                </div>

                <div class="form-group">
                    <button type="button" class="btn btn-secondary" id="previewBtn">
                        <i class="fas fa-eye"></i> Preview Data
                    </button>
                    <button type="button" class="btn btn-primary" id="uploadBtn">
                        <i class="fas fa-upload"></i> Upload Data
                    </button>
                    <a href="employee_management.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>

                <!-- Hidden inputs for form submission -->
                <input type="hidden" name="action" id="formAction" value="">
            </form>

            <!-- Loading indicator -->
            <div id="loadingIndicator" style="display:none;" class="text-center mt-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2">Sedang memproses file, mohon tunggu...</p>
            </div>

            <?php if (!empty($preview_data)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h3><i class="fas fa-eye"></i> Preview Data</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table">
                                <!-- Header akan diambil dari data Excel -->
                                <tbody>
                                    <?php
                                    // Tambahkan debugging info untuk tabel preview
                                    $debug_info['preview_table'] = 'rendering';
                                    $debug_info['preview_data_count'] = count($preview_data);

                                    // Tampilkan header dengan style berbeda
                                    if (!empty($preview_data) && count($preview_data) > 0):
                                        $header = $preview_data[0];
                                        $debug_info['preview_header'] = $header;
                                    ?>
                                        <tr class="table-primary">
                                            <?php
                                            // Mulai dari indeks 1 untuk melewati kolom pertama yang selalu null
                                            for ($i = 1; $i < count($header); $i++):
                                                $cell = $header[$i];
                                            ?>
                                                <th><?php echo ($cell !== null) ? htmlspecialchars($cell) : ''; ?></th>
                                            <?php endfor; ?>
                                        </tr>
                                    <?php endif; ?>

                                    <?php
                                    // Tampilkan data (mulai dari indeks 1 untuk melewati header)
                                    for ($i = 1; $i < count($preview_data); $i++):
                                        $row = $preview_data[$i];
                                    ?>
                                        <tr>
                                            <?php
                                            // Mulai dari indeks 1 untuk melewati kolom pertama yang selalu null
                                            for ($j = 1; $j < count($row); $j++):
                                                $cell = $row[$j];
                                            ?>
                                                <td><?php echo ($cell !== null) ? htmlspecialchars($cell) : ''; ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                    <?php endfor; ?>

                                    <?php
                                    // Jika tidak ada data, tampilkan pesan
                                    if (count($preview_data) <= 1):
                                    ?>
                                        <tr>
                                            <td colspan="14" class="text-center">Tidak ada data untuk ditampilkan</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <p class="text-muted">Menampilkan <?php echo count($preview_data); ?> baris pertama dari file Excel.</p>
                            <form method="post" enctype="multipart/form-data">
                                <!-- Simpan file yang diupload ke temporary file dan gunakan path sebagai referensi -->
                                <?php
                                    // Cek apakah file sudah diupload
                                    if (isset($_FILES['excel_file']) && !empty($_FILES['excel_file']['tmp_name'])) {
                                        $temp_file = tempnam(sys_get_temp_dir(), 'excel_');
                                        move_uploaded_file($_FILES['excel_file']['tmp_name'], $temp_file);
                                    } else {
                                        // Jika tidak ada file baru, gunakan file yang sama dari preview
                                        $temp_file = $_POST['temp_file'] ?? tempnam(sys_get_temp_dir(), 'excel_');
                                    }
                                ?>
                                <input type="hidden" name="temp_file" value="<?php echo $temp_file; ?>">
                                <input type="hidden" name="original_filename" value="<?php echo isset($_FILES['excel_file']) ? htmlspecialchars($_FILES['excel_file']['name']) : (isset($_POST['original_filename']) ? htmlspecialchars($_POST['original_filename']) : 'uploaded_file.xlsx'); ?>">
                                <input type="hidden" name="action" value="upload">
                                <input type="hidden" name="upload_mode" value="<?php echo isset($_POST['upload_mode']) ? htmlspecialchars($_POST['upload_mode']) : 'add'; ?>">
                                <?php
                                // Debug info
                                error_log("Preview form with upload_mode: " . (isset($_POST['upload_mode']) ? $_POST['upload_mode'] : 'add'));
                                ?>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload"></i> Upload Data
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($total_error > 0 && !empty($error_details)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h3><i class="fas fa-exclamation-triangle"></i> Detail Error</h3>
                    </div>
                    <div class="card-body">
                        <div class="error-details">
                            <ul>
                                <?php foreach ($error_details as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($total_skipped > 0 && !empty($skipped_niks)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> NIK yang Dilewati</h3>
                    </div>
                    <div class="card-body">
                        <p>Total <?php echo $total_skipped; ?> NIK dilewati karena sudah ada di database atau data tidak berubah:</p>
                        <div class="skipped-niks-container" style="max-height: 200px; overflow-y: auto; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border: 1px solid #dee2e6;">
                            <?php
                            // Kelompokkan NIK dalam baris-baris dengan 10 NIK per baris
                            $grouped_niks = array_chunk($skipped_niks, 10);
                            foreach ($grouped_niks as $nik_group):
                            ?>
                                <div class="nik-row" style="margin-bottom: 10px;">
                                    <?php echo implode(", ", $nik_group); ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
// Menangani error runtime.lastError
window.addEventListener("error", function(e) {
    // Cek apakah error adalah runtime.lastError
    if (e && e.message && e.message.includes("runtime.lastError")) {
        console.log("Caught runtime.lastError:", e.message);
        // Mencegah error ditampilkan di konsol browser
        e.preventDefault();
        e.stopPropagation();
        return true;
    }
});

// Menangani unhandled promise rejection
window.addEventListener("unhandledrejection", function(e) {
    // Cek apakah rejection adalah runtime.lastError
    if (e && e.reason && e.reason.message && e.reason.message.includes("runtime.lastError")) {
        console.log("Caught unhandled promise rejection with runtime.lastError:", e.reason.message);
        // Mencegah error ditampilkan di konsol browser
        e.preventDefault();
        e.stopPropagation();
        return true;
    }
});

// Menangani submit form untuk mencegah multiple submission
document.addEventListener("DOMContentLoaded", function() {
    const uploadForm = document.getElementById("uploadForm");
    const loadingIndicator = document.getElementById("loadingIndicator");
    const formAction = document.getElementById("formAction");

    if (uploadForm) {
        // Set radio button sesuai dengan mode upload saat ini
        const currentMode = "<?php echo $upload_mode; ?>";
        const importOnlyChanged = <?php echo $import_only_changed ? 'true' : 'false'; ?>;
        console.log("Current upload mode:", currentMode, "Import only changed:", importOnlyChanged);

        // Set radio button yang sesuai
        if (currentMode === "update") {
            const modeUpdateRadio = document.getElementById("modeUpdate");
            if (modeUpdateRadio) {
                modeUpdateRadio.checked = true;
            }
        } else {
            const modeAddRadio = document.getElementById("modeAdd");
            if (modeAddRadio) {
                modeAddRadio.checked = true;
            }
        }

        // Set checkbox import_only_changed
        const importOnlyChangedCheckbox = document.getElementById("importOnlyChanged");
        if (importOnlyChangedCheckbox) {
            importOnlyChangedCheckbox.checked = importOnlyChanged;

            // Disable checkbox jika mode adalah 'add'
            if (currentMode === "add") {
                importOnlyChangedCheckbox.disabled = true;
            }

            // Tambahkan event listener untuk checkbox
            importOnlyChangedCheckbox.addEventListener("change", function() {
                // Update tampilan mode saat ini
                updateCurrentModeDisplay();
            });
        }

        // Fungsi untuk mengupdate tampilan mode saat ini
        function updateCurrentModeDisplay() {
            const currentModeText = document.getElementById("currentModeText");
            const onlyChangedBadge = document.getElementById("onlyChangedBadge");
            const selectedMode = document.querySelector("input[name='upload_mode']:checked")?.value || "add";

            if (currentModeText) {
                currentModeText.textContent = selectedMode === "update" ? "Update Data (Lengkap)" : "Tambah Data Baru";
            }

            if (onlyChangedBadge) {
                if (selectedMode === "update" && importOnlyChangedCheckbox && importOnlyChangedCheckbox.checked) {
                    onlyChangedBadge.style.display = "";
                } else {
                    onlyChangedBadge.style.display = "none";
                }
            }
        }

        // Tambahkan event listener untuk radio button mode upload
        const modeRadios = document.querySelectorAll("input[name='upload_mode']");
        modeRadios.forEach(function(radio) {
            radio.addEventListener("change", function() {
                if (importOnlyChangedCheckbox) {
                    // Enable/disable checkbox berdasarkan mode yang dipilih
                    importOnlyChangedCheckbox.disabled = this.value === "add";

                    // Uncheck checkbox jika mode adalah 'add'
                    if (this.value === "add") {
                        importOnlyChangedCheckbox.checked = false;
                    }
                }

                // Update tampilan mode saat ini
                updateCurrentModeDisplay();
            });
        });

        // Panggil fungsi updateCurrentModeDisplay saat halaman dimuat
        updateCurrentModeDisplay();

        const excelFileInput = document.getElementById("excelFile");
        const previewBtn = document.getElementById("previewBtn");
        const uploadBtn = document.getElementById("uploadBtn");

        // Validasi file sebelum submit
        excelFileInput.addEventListener("change", function() {
            const file = this.files[0];
            if (file) {
                // Validasi tipe file
                const fileType = file.name.split(".").pop().toLowerCase();
                if (fileType !== "xlsx" && fileType !== "xls") {
                    alert("File harus berformat Excel (.xlsx atau .xls)");
                    this.value = ""; // Reset input file
                    return;
                }

                // Validasi ukuran file (max 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert("Ukuran file terlalu besar. Maksimal 5MB.");
                    this.value = ""; // Reset input file
                    return;
                }

                // Enable tombol jika file valid
                previewBtn.disabled = false;
                uploadBtn.disabled = false;
            } else {
                // Disable tombol jika tidak ada file
                previewBtn.disabled = true;
                uploadBtn.disabled = true;
            }
        });

        // Tangani klik tombol preview
        previewBtn.addEventListener("click", function() {
            // Validasi file sebelum submit
            if (!excelFileInput.files || excelFileInput.files.length === 0) {
                alert("Silakan pilih file Excel terlebih dahulu.");
                return;
            }

            // Set action dan submit form
            formAction.value = "preview";

            // Tampilkan loading indicator
            loadingIndicator.style.display = "block";

            // Disable tombol
            previewBtn.disabled = true;
            uploadBtn.disabled = true;

            // Submit form
            uploadForm.submit();
        });

        // Tangani klik tombol upload
        uploadBtn.addEventListener("click", function() {
            // Validasi file sebelum submit
            if (!excelFileInput.files || excelFileInput.files.length === 0) {
                alert("Silakan pilih file Excel terlebih dahulu.");
                return;
            }

            // Gunakan confirmAction sebagai pengganti confirm
            confirmAction(
                "Apakah Anda yakin ingin mengupload data ini?",
                function() {
                    // Kode ini akan dijalankan ketika user mengklik "Ya"

                    // Set action dan submit form
                    formAction.value = "upload";

                    // Debug info
                    console.log("Upload button clicked with mode:", document.querySelector("input[name='upload_mode']:checked")?.value || "add");

                    // Tambahkan debug info ke form
                    const debugInfo = document.createElement("input");
                    debugInfo.type = "hidden";
                    debugInfo.name = "debug_info";
                    debugInfo.value = "Upload button clicked with mode: " + (document.querySelector("input[name='upload_mode']:checked")?.value || "add");
                    uploadForm.appendChild(debugInfo);

                    // Tampilkan loading indicator
                    loadingIndicator.style.display = "block";

                    // Disable tombol
                    previewBtn.disabled = true;
                    uploadBtn.disabled = true;

                    // Submit form
                    uploadForm.submit();
                },
                function() {
                    // Kode ini akan dijalankan ketika user mengklik "Tidak"
                    console.log("Upload dibatalkan oleh user");
                },
                "Konfirmasi Upload"
            );
        });
    }
});
</script>
</body>
</html>

