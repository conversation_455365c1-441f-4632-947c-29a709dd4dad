<?php
include '../config/config.php';
include '../config/access_control.php';
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../view/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get access eligibility info
$eligibility = getAccessEligibility($user_id, 4, ['Supervisor', 'Chief']);
$user_data = getUserLevel($user_id);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Access Control</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .access-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .access-granted {
            border-left: 5px solid #28a745;
            background-color: #d4edda;
        }
        .access-denied {
            border-left: 5px solid #dc3545;
            background-color: #f8d7da;
        }
        .info-card {
            border-left: 5px solid #007bff;
            background-color: #d1ecf1;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <h1 class="text-center mb-4">
                    <i class="fas fa-shield-alt"></i> Test Access Control System
                </h1>

                <!-- User Information Card -->
                <div class="card access-card info-card">
                    <div class="card-header">
                        <h5><i class="fas fa-user"></i> Informasi User</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($user_data): ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Nama:</strong> <?= htmlspecialchars($user_data['name']) ?></p>
                                    <p><strong>NIK:</strong> <?= htmlspecialchars($user_data['nik']) ?></p>
                                    <p><strong>Level:</strong> <?= $user_data['level_karyawan'] ?> (<?= getLevelDescription($user_data['level_karyawan']) ?>)</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Jabatan:</strong> <?= htmlspecialchars($user_data['jabatan']) ?></p>
                                    <p><strong>Departemen:</strong> <?= htmlspecialchars($user_data['dept']) ?></p>
                                    <p><strong>Bagian:</strong> <?= htmlspecialchars($user_data['bagian']) ?></p>
                                </div>
                            </div>
                        <?php else: ?>
                            <p class="text-danger">Data user tidak ditemukan!</p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Access Test Results -->
                <div class="card access-card <?= $eligibility['eligible'] ? 'access-granted' : 'access-denied' ?>">
                    <div class="card-header">
                        <h5>
                            <?php if ($eligibility['eligible']): ?>
                                <i class="fas fa-check-circle text-success"></i> Akses Diizinkan
                            <?php else: ?>
                                <i class="fas fa-times-circle text-danger"></i> Akses Ditolak
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Alasan:</strong> <?= htmlspecialchars($eligibility['reason']) ?></p>
                        
                        <?php if ($eligibility['eligible'] && isset($eligibility['access_type'])): ?>
                            <div class="alert alert-success">
                                <strong>Tipe Akses:</strong> 
                                <?php if ($eligibility['access_type'] === 'level'): ?>
                                    <span class="badge bg-primary">Berdasarkan Level</span>
                                <?php elseif ($eligibility['access_type'] === 'position'): ?>
                                    <span class="badge bg-warning">Berdasarkan Jabatan Khusus</span>
                                    <br><small>Jabatan khusus yang terdeteksi: <strong><?= htmlspecialchars($eligibility['special_position']) ?></strong></small>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Test Different Scenarios -->
                <div class="card access-card">
                    <div class="card-header">
                        <h5><i class="fas fa-vial"></i> Test Skenario Berbeda</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Test Level Requirements:</h6>
                                <ul class="list-group list-group-flush">
                                    <?php for ($level = 3; $level <= 6; $level++): ?>
                                        <?php $has_access = hasMinimumLevel($level); ?>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Level <?= $level ?>+
                                            <?php if ($has_access): ?>
                                                <span class="badge bg-success"><i class="fas fa-check"></i> Ya</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger"><i class="fas fa-times"></i> Tidak</span>
                                            <?php endif; ?>
                                        </li>
                                    <?php endfor; ?>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Test Special Positions:</h6>
                                <ul class="list-group list-group-flush">
                                    <?php 
                                    $special_positions = ['Supervisor', 'Chief', 'Manager', 'Head'];
                                    foreach ($special_positions as $position): 
                                        $has_position = hasSpecialPosition($user_id, [$position]);
                                    ?>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <?= $position ?>
                                            <?php if ($has_position): ?>
                                                <span class="badge bg-success"><i class="fas fa-check"></i> Ya</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary"><i class="fas fa-times"></i> Tidak</span>
                                            <?php endif; ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Access Rules -->
                <div class="card access-card info-card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> Aturan Akses</h5>
                    </div>
                    <div class="card-body">
                        <h6>Untuk mengakses Form Pengajuan Training, user harus memenuhi salah satu syarat:</h6>
                        <ul>
                            <li><strong>Level 4 ke atas</strong> (Assistant Manager, Manager, dll)</li>
                            <li><strong>Jabatan khusus:</strong> Supervisor atau Chief (meskipun level di bawah 4)</li>
                        </ul>
                        
                        <h6 class="mt-3">Contoh Jabatan yang Diizinkan:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="text-success">
                                    <li>Supervisor Production</li>
                                    <li>Chief Engineer</li>
                                    <li>Supervisor Quality</li>
                                    <li>Chief Security</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="text-muted">
                                    <li>Staff Admin (Level 1-3)</li>
                                    <li>Operator (Level 1-3)</li>
                                    <li>Helper (Level 1-3)</li>
                                    <li>Clerk (Level 1-3)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center">
                    <?php if ($eligibility['eligible']): ?>
                        <a href="../pemohon/form.php" class="btn btn-success btn-lg">
                            <i class="fas fa-edit"></i> Akses Form Pengajuan Training
                        </a>
                    <?php else: ?>
                        <button class="btn btn-danger btn-lg" disabled>
                            <i class="fas fa-lock"></i> Akses Ditolak
                        </button>
                    <?php endif; ?>
                    
                    <a href="../index.php" class="btn btn-secondary btn-lg ms-2">
                        <i class="fas fa-home"></i> Kembali ke Home
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
