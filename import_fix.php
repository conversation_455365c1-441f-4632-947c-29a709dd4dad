<?php
/**
 * Database Import Fix Tool
 *
 * Tool ini membantu mengatasi masalah foreign key constraint saat mengimpor database.
 * Terintegrasi dengan setupdb.php untuk pengalaman pengguna yang konsisten.
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database configuration - menggunakan konfigurasi yang sama dengan setupdb.php
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'db_training';

// Function to display messages (copied from setupdb.php)
function showMessage($message, $isError = false) {
    echo '<div style="padding: 10px; margin: 5px; border-radius: 5px; ' .
         ($isError ? 'background-color: #ffebee; color: #c62828;' : 'background-color: #e8f5e9; color: #2e7d32;') .
         '">' . $message . '</div>';
}

// Function to check MySQL server status (copied from setupdb.php)
function checkMySqlStatus($db_host, $db_user, $db_pass) {
    try {
        $conn = new mysqli($db_host, $db_user, $db_pass);
        if ($conn->connect_error) {
            return [
                'status' => false,
                'message' => "Connection failed: " . $conn->connect_error
            ];
        }

        // Get server info
        $server_info = $conn->server_info;
        $host_info = $conn->host_info;

        // Get MySQL version
        $version_query = $conn->query("SELECT VERSION() as version");
        $version = $version_query ? $version_query->fetch_assoc()['version'] : 'Unknown';

        $conn->close();

        return [
            'status' => true,
            'server_info' => $server_info,
            'host_info' => $host_info,
            'version' => $version
        ];
    } catch (Exception $e) {
        return [
            'status' => false,
            'message' => "Exception: " . $e->getMessage()
        ];
    }
}

// Function to check database tables and their dependencies
function getDatabaseDiagnosis($db_host, $db_user, $db_pass, $db_name) {
    try {
        $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
        if ($conn->connect_error) {
            return [
                'status' => false,
                'message' => "Connection failed: " . $conn->connect_error
            ];
        }

        $diagnosis = [
            'status' => true,
            'tables' => [],
            'foreign_keys' => [],
            'issues' => [],
            'warnings' => []
        ];

        // Get all tables
        $tables_result = $conn->query("SHOW TABLES");
        if ($tables_result) {
            while ($row = $tables_result->fetch_row()) {
                $table_name = $row[0];

                // Get row count for each table
                $count_result = $conn->query("SELECT COUNT(*) AS count FROM `$table_name`");
                $row_count = $count_result ? $count_result->fetch_assoc()['count'] : 0;

                $diagnosis['tables'][$table_name] = [
                    'name' => $table_name,
                    'row_count' => $row_count
                ];
            }
        }

        // Get foreign key relationships
        $fk_result = $conn->query("
            SELECT
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM
                information_schema.KEY_COLUMN_USAGE
            WHERE
                REFERENCED_TABLE_SCHEMA = '$db_name' AND
                REFERENCED_TABLE_NAME IS NOT NULL
        ");

        if ($fk_result) {
            while ($row = $fk_result->fetch_assoc()) {
                $diagnosis['foreign_keys'][] = [
                    'table' => $row['TABLE_NAME'],
                    'column' => $row['COLUMN_NAME'],
                    'constraint_name' => $row['CONSTRAINT_NAME'],
                    'referenced_table' => $row['REFERENCED_TABLE_NAME'],
                    'referenced_column' => $row['REFERENCED_COLUMN_NAME']
                ];
            }
        }

        // Check for potential issues
        foreach ($diagnosis['foreign_keys'] as $fk) {
            $child_table = $fk['table'];
            $parent_table = $fk['referenced_table'];
            $child_column = $fk['column'];
            $parent_column = $fk['referenced_column'];

            // Issue 1: If parent table is empty but child table has records, this is an issue
            if (isset($diagnosis['tables'][$parent_table]) &&
                isset($diagnosis['tables'][$child_table]) &&
                $diagnosis['tables'][$parent_table]['row_count'] == 0 &&
                $diagnosis['tables'][$child_table]['row_count'] > 0) {

                $diagnosis['issues'][] = [
                    'type' => 'empty_parent',
                    'message' => "Tabel '$parent_table' kosong tetapi direferensikan oleh tabel '$child_table'",
                    'child_table' => $child_table,
                    'parent_table' => $parent_table,
                    'constraint' => $fk['constraint_name']
                ];
            }

            // Warning 1: Even if parent table has data, we should warn about potential import issues
            if (isset($diagnosis['tables'][$parent_table]) &&
                isset($diagnosis['tables'][$child_table]) &&
                $diagnosis['tables'][$parent_table]['row_count'] > 0) {

                $diagnosis['warnings'][] = [
                    'type' => 'import_order',
                    'message' => "Tabel '$child_table' memiliki foreign key ke tabel '$parent_table'. Pastikan tabel '$parent_table' diimport terlebih dahulu.",
                    'child_table' => $child_table,
                    'parent_table' => $parent_table,
                    'constraint' => $fk['constraint_name']
                ];
            }

            // Warning 2: Check for potential data inconsistency
            if (isset($diagnosis['tables'][$parent_table]) &&
                isset($diagnosis['tables'][$child_table]) &&
                $diagnosis['tables'][$parent_table]['row_count'] > 0 &&
                $diagnosis['tables'][$child_table]['row_count'] > 0) {

                // Check if there are any orphaned foreign keys
                $orphan_check_query = "
                    SELECT COUNT(*) AS orphan_count
                    FROM `$child_table` c
                    LEFT JOIN `$parent_table` p ON c.`$child_column` = p.`$parent_column`
                    WHERE p.`$parent_column` IS NULL AND c.`$child_column` IS NOT NULL
                ";

                $orphan_result = $conn->query($orphan_check_query);
                if ($orphan_result && $row = $orphan_result->fetch_assoc()) {
                    $orphan_count = $row['orphan_count'];

                    if ($orphan_count > 0) {
                        $diagnosis['issues'][] = [
                            'type' => 'orphaned_records',
                            'message' => "Ditemukan $orphan_count data di tabel '$child_table' yang mereferensikan data tidak ada di tabel '$parent_table'",
                            'child_table' => $child_table,
                            'parent_table' => $parent_table,
                            'constraint' => $fk['constraint_name'],
                            'orphan_count' => $orphan_count
                        ];
                    }
                }
            }
        }

        // Add general warning about import order
        if (count($diagnosis['foreign_keys']) > 0) {
            $diagnosis['warnings'][] = [
                'type' => 'general_import_order',
                'message' => "Database memiliki relasi foreign key. Pastikan tabel diimport dalam urutan yang benar (tabel induk sebelum tabel anak)."
            ];

            // Add specific warning about activity_logs and users tables
            if (isset($diagnosis['tables']['activity_logs']) && isset($diagnosis['tables']['users'])) {
                $diagnosis['warnings'][] = [
                    'type' => 'specific_import_order',
                    'message' => "Tabel 'activity_logs' memiliki foreign key ke tabel 'users'. Pastikan tabel 'users' diimport terlebih dahulu."
                ];
            }
        }

        $conn->close();
        return $diagnosis;
    } catch (Exception $e) {
        return [
            'status' => false,
            'message' => "Exception: " . $e->getMessage()
        ];
    }
}

// Function to toggle foreign key checks
function toggleForeignKeyChecks($db_host, $db_user, $db_pass, $db_name, $enable = true) {
    try {
        $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
        if ($conn->connect_error) {
            return [
                'status' => false,
                'message' => "Connection failed: " . $conn->connect_error
            ];
        }

        $value = $enable ? '1' : '0';
        $result = $conn->query("SET FOREIGN_KEY_CHECKS=$value");

        $conn->close();

        return [
            'status' => $result !== false,
            'message' => $result !== false
                ? ($enable ? "Foreign key checks enabled" : "Foreign key checks disabled")
                : "Failed to " . ($enable ? "enable" : "disable") . " foreign key checks"
        ];
    } catch (Exception $e) {
        return [
            'status' => false,
            'message' => "Exception: " . $e->getMessage()
        ];
    }
}

// Function to drop foreign key constraints
function dropForeignKeyConstraints($db_host, $db_user, $db_pass, $db_name) {
    try {
        $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
        if ($conn->connect_error) {
            return [
                'status' => false,
                'message' => "Connection failed: " . $conn->connect_error
            ];
        }

        // Get all foreign key constraints
        $constraints = [];
        $query = "
            SELECT
                TABLE_NAME,
                CONSTRAINT_NAME
            FROM
                information_schema.TABLE_CONSTRAINTS
            WHERE
                CONSTRAINT_TYPE = 'FOREIGN KEY' AND
                TABLE_SCHEMA = '$db_name'
        ";

        $result = $conn->query($query);
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $constraints[] = [
                    'table' => $row['TABLE_NAME'],
                    'constraint' => $row['CONSTRAINT_NAME']
                ];
            }
        }

        // Drop each constraint
        $dropped = [];
        $errors = [];

        foreach ($constraints as $constraint) {
            $table = $constraint['table'];
            $constraintName = $constraint['constraint'];

            $dropQuery = "ALTER TABLE `$table` DROP FOREIGN KEY `$constraintName`";
            if ($conn->query($dropQuery)) {
                $dropped[] = "$table.$constraintName";
            } else {
                $errors[] = "Failed to drop $table.$constraintName: " . $conn->error;
            }
        }

        $conn->close();

        return [
            'status' => empty($errors),
            'dropped' => $dropped,
            'errors' => $errors,
            'message' => empty($errors)
                ? "Successfully dropped " . count($dropped) . " foreign key constraints"
                : "Failed to drop some foreign key constraints"
        ];
    } catch (Exception $e) {
        return [
            'status' => false,
            'message' => "Exception: " . $e->getMessage()
        ];
    }
}

// Function to restore foreign key constraints
function restoreForeignKeyConstraints($db_host, $db_user, $db_pass, $db_name, $constraints) {
    // This would be implemented to restore constraints after operations
    // For now, we'll just return a placeholder as this is complex to implement correctly
    return [
        'status' => true,
        'message' => "Foreign key constraints would need to be recreated manually"
    ];
}

// HTML header
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perbaikan Import Database</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: #fff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #a50000;
            text-align: center;
            margin-bottom: 25px;
            font-size: 28px;
        }
        h2 {
            color: #444;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
            margin-top: 30px;
            font-size: 22px;
        }
        h3 {
            color: #555;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 18px;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border: 1px solid #eee;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background-color: #a50000;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 10px;
            font-weight: bold;
        }
        .success {
            color: #2e7d32;
            font-weight: bold;
        }
        .warning {
            color: #ff8f00;
            font-weight: bold;
        }
        .error {
            color: #c62828;
            font-weight: bold;
        }
        .info-box {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
            font-size: 15px;
        }
        .warning-box {
            background-color: #fff8e1;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
            font-size: 15px;
        }
        .error-box {
            background-color: #ffebee;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #f44336;
            font-size: 15px;
        }
        .success-box {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
            font-size: 15px;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #a50000;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 8px 5px;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .button:hover {
            background-color: #8b0000;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .button-secondary {
            background-color: #757575;
        }
        .button-secondary:hover {
            background-color: #616161;
        }
        .button-warning {
            background-color: #ff9800;
        }
        .button-warning:hover {
            background-color: #f57c00;
        }
        .button-success {
            background-color: #4caf50;
        }
        .button-success:hover {
            background-color: #388e3c;
        }
        .button-blue {
            background-color: #2196f3;
        }
        .button-blue:hover {
            background-color: #1976d2;
        }
        .button-container {
            text-align: center;
            margin-top: 25px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        table, th, td {
            border: 1px solid #eee;
        }
        th {
            background-color: #f5f5f5;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            color: #555;
        }
        td {
            padding: 10px 12px;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        .code {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 3px 6px;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-size: 14px;
        }
        .collapsible {
            background-color: #f5f5f5;
            color: #444;
            cursor: pointer;
            padding: 15px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 16px;
            border-radius: 8px;
            margin-bottom: 8px;
            font-weight: bold;
            transition: all 0.3s;
            border: 1px solid #eee;
        }
        .active, .collapsible:hover {
            background-color: #e0e0e0;
        }
        .collapsible:after {
            content: '\002B';
            color: #777;
            font-weight: bold;
            float: right;
            margin-left: 5px;
            transition: all 0.3s;
        }
        .active:after {
            content: "\2212";
            transform: rotate(90deg);
        }
        .content {
            padding: 0 18px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background-color: #fafafa;
            border-radius: 0 0 8px 8px;
            margin-top: -8px;
            border-left: 1px solid #eee;
            border-right: 1px solid #eee;
            border-bottom: 1px solid #eee;
        }
        .header-with-icon {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .header-icon {
            margin-right: 10px;
            font-size: 24px;
            color: #a50000;
        }
        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin: 30px 0;
            position: relative;
        }
        .progress-steps:before {
            content: '';
            position: absolute;
            top: 15px;
            left: 0;
            right: 0;
            height: 2px;
            background: #ddd;
            z-index: 1;
        }
        .step-item {
            position: relative;
            z-index: 2;
            background: white;
            text-align: center;
            width: 30%;
        }
        .step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #ddd;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-weight: bold;
        }
        .active-step .step-circle {
            background-color: #a50000;
        }
        .completed-step .step-circle {
            background-color: #4caf50;
        }
        .step-title {
            font-weight: bold;
            font-size: 14px;
            color: #555;
        }
        .active-step .step-title {
            color: #a50000;
        }
        .completed-step .step-title {
            color: #4caf50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Perbaikan Import Database</h1>

        <?php
        // Check MySQL server status
        $mysql_status = checkMySqlStatus($db_host, $db_user, $db_pass);

        if (!$mysql_status['status']) {
            // MySQL connection failed
            echo '<div class="step">';
            echo '<div class="header-with-icon">';
            echo '<div class="header-icon">⚠️</div>';
            echo '<h2>Koneksi Database Gagal</h2>';
            echo '</div>';

            echo '<div class="error-box">';
            echo '<p><strong>Status Koneksi:</strong> <span class="error">Gagal</span></p>';
            echo '<p><strong>Error:</strong> ' . htmlspecialchars($mysql_status['message']) . '</p>';
            echo '</div>';

            echo '<div class="button-container">';
            echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
            echo '</div>';
            echo '</div>';
        } else {
            // MySQL connection successful

            // Check what action to perform
            $action = $_GET['action'] ?? '';

            // Progress steps indicator
            $step1_class = '';
            $step2_class = '';
            $step3_class = '';

            if ($action == 'diagnose') {
                $step1_class = 'active-step';
            } elseif ($action == 'disable') {
                $step1_class = 'completed-step';
                $step2_class = 'active-step';
            } elseif ($action == 'enable') {
                $step1_class = 'completed-step';
                $step2_class = 'completed-step';
                $step3_class = 'active-step';
            }

            if ($action == 'disable') {
                // Disable foreign key checks
                echo '<div class="progress-steps">';
                echo '<div class="step-item ' . $step1_class . '">';
                echo '<div class="step-circle">1</div>';
                echo '<div class="step-title">Diagnosa</div>';
                echo '</div>';
                echo '<div class="step-item ' . $step2_class . '">';
                echo '<div class="step-circle">2</div>';
                echo '<div class="step-title">Nonaktifkan Constraint</div>';
                echo '</div>';
                echo '<div class="step-item ' . $step3_class . '">';
                echo '<div class="step-circle">3</div>';
                echo '<div class="step-title">Aktifkan Kembali</div>';
                echo '</div>';
                echo '</div>';

                echo '<div class="step">';
                echo '<div class="header-with-icon">';
                echo '<div class="header-icon">🔓</div>';
                echo '<h2>Nonaktifkan Pengecekan Foreign Key</h2>';
                echo '</div>';

                $result = toggleForeignKeyChecks($db_host, $db_user, $db_pass, $db_name, false);

                if ($result['status']) {
                    echo '<div class="success-box">';
                    echo '<p><strong>Status:</strong> <span class="success">Berhasil</span></p>';
                    echo '<p>Pengecekan foreign key telah dinonaktifkan. Anda sekarang dapat mengimpor database tanpa kendala foreign key.</p>';
                    echo '</div>';

                    echo '<div class="info-box">';
                    echo '<h3>Langkah Selanjutnya:</h3>';
                    echo '<ol>';
                    echo '<li>Kembali ke halaman import database</li>';
                    echo '<li>Lakukan import database Anda</li>';
                    echo '<li>Setelah import selesai, klik tombol "Aktifkan Pengecekan Foreign Key" di bawah</li>';
                    echo '</ol>';
                    echo '</div>';

                    echo '<div class="warning-box">';
                    echo '<p><strong>Penting:</strong> Jangan lupa untuk mengaktifkan kembali pengecekan foreign key setelah selesai mengimpor database.</p>';
                    echo '</div>';
                } else {
                    echo '<div class="error-box">';
                    echo '<p><strong>Status:</strong> <span class="error">Gagal</span></p>';
                    echo '<p><strong>Error:</strong> ' . htmlspecialchars($result['message']) . '</p>';
                    echo '</div>';
                }

                echo '<div class="button-container">';
                echo '<a href="setupdb.php?action=import" class="button">Kembali ke Import Database</a>';
                echo '<a href="import_fix.php?action=enable" class="button button-success">Aktifkan Pengecekan Foreign Key</a>';
                echo '</div>';
                echo '</div>';
            }
            elseif ($action == 'enable') {
                // Re-enable foreign key checks
                echo '<div class="progress-steps">';
                echo '<div class="step-item ' . $step1_class . '">';
                echo '<div class="step-circle">1</div>';
                echo '<div class="step-title">Diagnosa</div>';
                echo '</div>';
                echo '<div class="step-item ' . $step2_class . '">';
                echo '<div class="step-circle">2</div>';
                echo '<div class="step-title">Nonaktifkan Constraint</div>';
                echo '</div>';
                echo '<div class="step-item ' . $step3_class . '">';
                echo '<div class="step-circle">3</div>';
                echo '<div class="step-title">Aktifkan Kembali</div>';
                echo '</div>';
                echo '</div>';

                echo '<div class="step">';
                echo '<div class="header-with-icon">';
                echo '<div class="header-icon">🔒</div>';
                echo '<h2>Aktifkan Kembali Pengecekan Foreign Key</h2>';
                echo '</div>';

                $result = toggleForeignKeyChecks($db_host, $db_user, $db_pass, $db_name, true);

                if ($result['status']) {
                    echo '<div class="success-box">';
                    echo '<p><strong>Status:</strong> <span class="success">Berhasil</span></p>';
                    echo '<p>Pengecekan foreign key telah diaktifkan kembali. Integritas database telah dipulihkan.</p>';
                    echo '</div>';

                    echo '<div class="info-box">';
                    echo '<p>Proses perbaikan telah selesai. Database Anda sekarang dalam kondisi normal dengan integritas data terjaga.</p>';
                    echo '</div>';
                } else {
                    echo '<div class="error-box">';
                    echo '<p><strong>Status:</strong> <span class="error">Gagal</span></p>';
                    echo '<p><strong>Error:</strong> ' . htmlspecialchars($result['message']) . '</p>';
                    echo '</div>';
                }

                echo '<div class="button-container">';
                echo '<a href="setupdb.php" class="button">Kembali ke Pengaturan Database</a>';
                echo '</div>';
                echo '</div>';
            }
            elseif ($action == 'drop_constraints') {
                // Drop foreign key constraints
                echo '<div class="step">';
                echo '<div class="header-with-icon">';
                echo '<div class="header-icon">⚠️</div>';
                echo '<h2>Hapus Foreign Key Constraints</h2>';
                echo '</div>';

                echo '<div class="warning-box">';
                echo '<p><strong>Peringatan:</strong> Menghapus foreign key constraints adalah tindakan yang berisiko. Ini akan menghapus semua relasi antar tabel dalam database Anda.</p>';
                echo '<p>Gunakan opsi ini hanya jika Anda mengalami error seperti:</p>';
                echo '<div style="background-color: #f8f8f8; padding: 12px; border-radius: 6px; font-family: monospace; margin: 10px 0; border-left: 4px solid #f44336;">';
                echo 'ERROR 3730 (HY000): Cannot drop table referenced by a foreign key constraint';
                echo '</div>';
                echo '<p>Setelah menghapus constraints, Anda dapat menghapus tabel atau melakukan operasi lain yang sebelumnya dibatasi oleh foreign key constraints.</p>';
                echo '</div>';

                // Confirm action
                if (!isset($_GET['confirm']) || $_GET['confirm'] != 'true') {
                    echo '<div class="info-box">';
                    echo '<h3>Konfirmasi Tindakan</h3>';
                    echo '<p>Apakah Anda yakin ingin menghapus semua foreign key constraints dari database?</p>';
                    echo '<p>Tindakan ini tidak dapat dibatalkan dan akan menghapus semua relasi antar tabel.</p>';
                    echo '</div>';

                    echo '<div class="button-container">';
                    echo '<a href="import_fix.php" class="button button-secondary">Batal</a>';
                    echo '<a href="import_fix.php?action=drop_constraints&confirm=true" class="button" style="background-color: #ff5722;">Ya, Hapus Semua Foreign Key Constraints</a>';
                    echo '</div>';
                } else {
                    // Drop constraints
                    $result = dropForeignKeyConstraints($db_host, $db_user, $db_pass, $db_name);

                    if ($result['status']) {
                        echo '<div class="success-box">';
                        echo '<p><strong>Status:</strong> <span class="success">Berhasil</span></p>';
                        echo '<p>Berhasil menghapus ' . count($result['dropped']) . ' foreign key constraints.</p>';

                        if (!empty($result['dropped'])) {
                            echo '<div style="max-height: 200px; overflow-y: auto; margin: 10px 0; padding: 10px; background-color: #f5f5f5; border-radius: 5px;">';
                            echo '<p><strong>Constraints yang dihapus:</strong></p>';
                            echo '<ul>';
                            foreach ($result['dropped'] as $constraint) {
                                echo '<li>' . htmlspecialchars($constraint) . '</li>';
                            }
                            echo '</ul>';
                            echo '</div>';
                        }

                        echo '</div>';

                        echo '<div class="info-box">';
                        echo '<h3>Langkah Selanjutnya:</h3>';
                        echo '<p>Sekarang Anda dapat:</p>';
                        echo '<ol>';
                        echo '<li>Menghapus tabel yang sebelumnya tidak bisa dihapus karena foreign key constraints</li>';
                        echo '<li>Mengimpor database tanpa kendala foreign key</li>';
                        echo '</ol>';
                        echo '<p><strong>Catatan:</strong> Jika Anda ingin mengembalikan relasi antar tabel, Anda perlu mengimpor ulang struktur database atau membuat ulang foreign key constraints secara manual.</p>';
                        echo '</div>';
                    } else {
                        echo '<div class="error-box">';
                        echo '<p><strong>Status:</strong> <span class="error">Gagal</span></p>';
                        echo '<p><strong>Error:</strong> ' . htmlspecialchars($result['message']) . '</p>';

                        if (!empty($result['errors'])) {
                            echo '<div style="max-height: 200px; overflow-y: auto; margin: 10px 0; padding: 10px; background-color: #f5f5f5; border-radius: 5px;">';
                            echo '<p><strong>Detail Error:</strong></p>';
                            echo '<ul>';
                            foreach ($result['errors'] as $error) {
                                echo '<li>' . htmlspecialchars($error) . '</li>';
                            }
                            echo '</ul>';
                            echo '</div>';
                        }

                        echo '</div>';
                    }

                    echo '<div class="button-container">';
                    echo '<a href="setupdb.php?action=import" class="button">Kembali ke Import Database</a>';
                    echo '<a href="import_fix.php" class="button button-secondary">Kembali ke Alat Perbaikan</a>';
                    echo '</div>';
                }

                echo '</div>';
            }
            elseif ($action == 'diagnose') {
                // Diagnose database issues
                echo '<div class="progress-steps">';
                echo '<div class="step-item ' . $step1_class . '">';
                echo '<div class="step-circle">1</div>';
                echo '<div class="step-title">Diagnosa</div>';
                echo '</div>';
                echo '<div class="step-item ' . $step2_class . '">';
                echo '<div class="step-circle">2</div>';
                echo '<div class="step-title">Nonaktifkan Constraint</div>';
                echo '</div>';
                echo '<div class="step-item ' . $step3_class . '">';
                echo '<div class="step-circle">3</div>';
                echo '<div class="step-title">Aktifkan Kembali</div>';
                echo '</div>';
                echo '</div>';

                echo '<div class="step">';
                echo '<div class="header-with-icon">';
                echo '<div class="header-icon">🔍</div>';
                echo '<h2>Diagnosa Database</h2>';
                echo '</div>';

                try {
                    // Connect to database
                    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
                    if ($conn->connect_error) {
                        throw new Exception("Koneksi database gagal: {$conn->connect_error}");
                    }

                    // Get database diagnosis
                    $diagnosis = getDatabaseDiagnosis($db_host, $db_user, $db_pass, $db_name);

                    if (!$diagnosis['status']) {
                        echo '<div class="error-box">';
                        echo '<p><strong>Status:</strong> <span class="error">Gagal</span></p>';
                        echo '<p><strong>Error:</strong> ' . htmlspecialchars($diagnosis['message']) . '</p>';
                        echo '</div>';
                    } else {
                        // Show issues first if any
                        if (!empty($diagnosis['issues'])) {
                            echo '<div class="error-box">';
                            echo '<h3>Masalah Terdeteksi</h3>';

                            foreach ($diagnosis['issues'] as $issue) {
                                echo '<div style="margin-bottom: 15px;">';
                                echo '<p><strong>Masalah:</strong> ' . htmlspecialchars($issue['message']) . '</p>';

                                if ($issue['type'] == 'empty_parent') {
                                    echo '<p><strong>Rekomendasi:</strong> Import data ke tabel <span class="code">' .
                                        htmlspecialchars($issue['parent_table']) . '</span> terlebih dahulu sebelum mengimpor data ke tabel <span class="code">' .
                                        htmlspecialchars($issue['child_table']) . '</span>.</p>';
                                }

                                echo '</div>';
                            }

                            echo '</div>';

                            // Show solution options
                            echo '<div class="warning-box">';
                            echo '<h3>Cara Memperbaiki:</h3>';
                            echo '<ol>';
                            echo '<li><strong>Cara Terbaik:</strong> Import tabel dalam urutan yang benar (tabel induk terlebih dahulu, kemudian tabel anak)</li>';
                            echo '<li><strong>Cara Alternatif:</strong> Nonaktifkan sementara pengecekan foreign key selama import (klik tombol "Nonaktifkan Pengecekan Foreign Key" di bawah)</li>';
                            echo '</ol>';
                            echo '</div>';
                        } else {
                            echo '<div class="success-box">';
                            echo '<p><strong>Status:</strong> <span class="success">Tidak ada masalah terdeteksi saat ini</span></p>';
                            echo '<p>Database Anda tampaknya dalam kondisi baik. Tidak ditemukan masalah foreign key constraint yang aktif.</p>';
                            echo '</div>';

                            // Even if no issues, show warnings about potential import problems
                            if (!empty($diagnosis['warnings'])) {
                                echo '<div class="warning-box">';
                                echo '<h3>Peringatan untuk Import Database</h3>';

                                // Show specific warnings first
                                $specific_warnings = array_filter($diagnosis['warnings'], function($warning) {
                                    return $warning['type'] != 'general_import_order';
                                });

                                if (!empty($specific_warnings)) {
                                    echo '<p><strong>Penting:</strong> Meskipun tidak ada masalah saat ini, Anda mungkin mengalami error saat mengimport database jika urutan tabel tidak benar.</p>';

                                    foreach ($specific_warnings as $warning) {
                                        if ($warning['type'] == 'specific_import_order') {
                                            echo '<div style="margin: 10px 0; padding: 10px; background-color: #fff3e0; border-left: 3px solid #ff9800;">';
                                            echo '<p>' . htmlspecialchars($warning['message']) . '</p>';
                                            echo '</div>';
                                        }
                                    }

                                    echo '<p><strong>Rekomendasi:</strong> Jika Anda mengalami error saat import, gunakan tombol "Nonaktifkan Pengecekan Foreign Key" di bawah sebelum melakukan import.</p>';
                                }

                                echo '</div>';

                                // Add a note about the specific error message
                                echo '<div class="info-box">';
                                echo '<h3>Catatan Penting</h3>';
                                echo '<p>Jika Anda melihat error seperti ini saat import:</p>';
                                echo '<div style="background-color: #f8f8f8; padding: 12px; border-radius: 6px; font-family: monospace; margin: 10px 0; border-left: 4px solid #f44336;">';
                                echo 'ERROR 1452 (23000): Cannot add or update a child row: a foreign key constraint fails (`db_training`.`activity_logs`, CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE)';
                                echo '</div>';
                                echo '<p>Ini berarti Anda mencoba mengimport data ke tabel <span class="code">activity_logs</span> dengan <span class="code">user_id</span> yang tidak ada di tabel <span class="code">users</span>.</p>';
                                echo '<p>Solusinya adalah:</p>';
                                echo '<ol>';
                                echo '<li>Import tabel <span class="code">users</span> terlebih dahulu, atau</li>';
                                echo '<li>Nonaktifkan pengecekan foreign key selama import</li>';
                                echo '</ol>';
                                echo '</div>';
                            }
                        }

                        // Show database tables in collapsible section
                        echo '<button type="button" class="collapsible">Lihat Informasi Tabel Database</button>';
                        echo '<div class="content">';
                        echo '<div style="padding: 15px;">';

                        if (empty($diagnosis['tables'])) {
                            echo '<p>Tidak ada tabel yang ditemukan dalam database.</p>';
                        } else {
                            echo '<table>';
                            echo '<tr><th>Nama Tabel</th><th>Jumlah Baris</th></tr>';

                            foreach ($diagnosis['tables'] as $table) {
                                echo '<tr>';
                                echo '<td><span class="code">' . htmlspecialchars($table['name']) . '</span></td>';
                                echo '<td>' . number_format($table['row_count']) . '</td>';
                                echo '</tr>';
                            }

                            echo '</table>';
                        }
                        echo '</div>';
                        echo '</div>';

                        // Show foreign key relationships in collapsible section
                        if (!empty($diagnosis['foreign_keys'])) {
                            echo '<button type="button" class="collapsible">Lihat Relasi Foreign Key</button>';
                            echo '<div class="content">';
                            echo '<div style="padding: 15px;">';
                            echo '<table>';
                            echo '<tr><th>Tabel</th><th>Kolom</th><th>Referensi Tabel</th><th>Referensi Kolom</th></tr>';

                            foreach ($diagnosis['foreign_keys'] as $fk) {
                                echo '<tr>';
                                echo '<td><span class="code">' . htmlspecialchars($fk['table']) . '</span></td>';
                                echo '<td><span class="code">' . htmlspecialchars($fk['column']) . '</span></td>';
                                echo '<td><span class="code">' . htmlspecialchars($fk['referenced_table']) . '</span></td>';
                                echo '<td><span class="code">' . htmlspecialchars($fk['referenced_column']) . '</span></td>';
                                echo '</tr>';
                            }

                            echo '</table>';
                            echo '</div>';
                            echo '</div>';
                        }
                    }

                    $conn->close();
                } catch (Exception $e) {
                    echo '<div class="error-box">';
                    echo '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '</div>';
                }

                echo '<div class="button-container">';
                echo '<a href="import_fix.php" class="button button-secondary">Kembali</a>';

                // Show disable button if there are issues or warnings
                if (!empty($diagnosis['issues']) || !empty($diagnosis['warnings'])) {
                    echo '<a href="import_fix.php?action=disable" class="button button-warning">Nonaktifkan Pengecekan Foreign Key</a>';
                }

                echo '<a href="setupdb.php?action=import" class="button">Kembali ke Import Database</a>';
                echo '</div>';
                echo '</div>';
            }
            else {
                // Default view - show options
                echo '<div class="step">';
                echo '<div class="header-with-icon">';
                echo '<div class="header-icon">🛠️</div>';
                echo '<h2>Alat Perbaikan Import Database</h2>';
                echo '</div>';

                echo '<div class="info-box">';
                echo '<p>Alat ini membantu mengatasi masalah <strong>foreign key constraint</strong> saat mengimpor database.</p>';
                echo '<p>Jika Anda mengalami error seperti berikut saat mengimpor database:</p>';
                echo '<div style="background-color: #f8f8f8; padding: 12px; border-radius: 6px; font-family: monospace; margin: 10px 0; border-left: 4px solid #f44336;">';
                echo 'ERROR 1452 (23000): Cannot add or update a child row: a foreign key constraint fails';
                echo '</div>';
                echo '<p>Atau error seperti ini saat mencoba menghapus tabel:</p>';
                echo '<div style="background-color: #f8f8f8; padding: 12px; border-radius: 6px; font-family: monospace; margin: 10px 0; border-left: 4px solid #f44336;">';
                echo 'ERROR 3730 (HY000): Cannot drop table referenced by a foreign key constraint';
                echo '</div>';
                echo '</div>';

                echo '<div class="step">';
                echo '<h3>Cara Menggunakan Alat Ini:</h3>';

                echo '<div style="display: flex; margin: 20px 0;">';
                echo '<div class="step-number">1</div>';
                echo '<div style="flex: 1;">';
                echo '<p><strong>Diagnosa Database</strong> - Mengidentifikasi masalah foreign key constraint dalam database Anda</p>';
                echo '</div>';
                echo '</div>';

                echo '<div style="display: flex; margin: 20px 0;">';
                echo '<div class="step-number">2</div>';
                echo '<div style="flex: 1;">';
                echo '<p><strong>Nonaktifkan Pengecekan Foreign Key</strong> - Menonaktifkan sementara pengecekan foreign key agar import dapat dilakukan</p>';
                echo '</div>';
                echo '</div>';

                echo '<div style="display: flex; margin: 20px 0;">';
                echo '<div class="step-number">3</div>';
                echo '<div style="flex: 1;">';
                echo '<p><strong>Import Database</strong> - Kembali ke halaman import database dan lakukan import</p>';
                echo '</div>';
                echo '</div>';

                echo '<div style="display: flex; margin: 20px 0;">';
                echo '<div class="step-number">4</div>';
                echo '<div style="flex: 1;">';
                echo '<p><strong>Aktifkan Kembali Pengecekan Foreign Key</strong> - Mengaktifkan kembali pengecekan foreign key setelah import selesai</p>';
                echo '</div>';
                echo '</div>';

                echo '</div>';

                echo '<div class="button-container">';
                echo '<a href="import_fix.php?action=diagnose" class="button button-blue">Diagnosa Database</a>';
                echo '<a href="import_fix.php?action=disable" class="button button-warning">Nonaktifkan Pengecekan Foreign Key</a>';
                echo '<a href="import_fix.php?action=drop_constraints" class="button" style="background-color: #ff5722;">Hapus Foreign Key Constraints</a>';
                echo '<a href="setupdb.php" class="button button-secondary">Kembali ke Pengaturan Database</a>';
                echo '</div>';
                echo '</div>';
            }
        }
        ?>

        <script>
        // JavaScript for collapsible sections
        document.addEventListener('DOMContentLoaded', function() {
            // Handle collapsible sections
            var coll = document.getElementsByClassName("collapsible");
            for (var i = 0; i < coll.length; i++) {
                coll[i].addEventListener("click", function() {
                    this.classList.toggle("active");
                    var content = this.nextElementSibling;
                    if (content.style.maxHeight) {
                        content.style.maxHeight = null;
                    } else {
                        content.style.maxHeight = content.scrollHeight + "px";
                    }
                });
            }

            // Automatically expand first collapsible if there are issues detected
            var errorBox = document.querySelector('.error-box');
            if (errorBox && errorBox.querySelector('h3') && errorBox.querySelector('h3').textContent === 'Masalah Terdeteksi') {
                // There are issues, expand the first collapsible
                var firstCollapsible = document.querySelector('.collapsible');
                if (firstCollapsible) {
                    firstCollapsible.click();
                }
            }

            // Add hover effect to buttons
            var buttons = document.querySelectorAll('.button');
            buttons.forEach(function(button) {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
                });

                button.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                    this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
                });
            });
        });
        </script>
    </div>
</body>
</html>
