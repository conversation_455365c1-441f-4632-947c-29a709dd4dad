# 🔧 Fix Session dan Array Key Errors

## ❌ Error yang Diperbaiki

### **1. Session Already Active Error**
```
Notice: session_start(): Ignoring session_start() because a session is already active (started from C:\laragon\www\Training\config\config.php on line 130) in C:\laragon\www\Training\LnD\security.php on line 3
```

### **2. Undefined Array Key Error**
```
Warning: Undefined array key "current_approver_role_id" in C:\laragon\www\Training\LnD\dashboard.php on line 68
```

## ✅ Solusi yang Diimplementasi

### **Problem 1: Duplicate session_start() Calls**

**Root Cause:**
- `config.php` sudah memanggil `session_start()` di line 130
- File-file lain masih memanggil `session_start()` lagi
- PHP menganggap ini sebagai error karena session sudah aktif

**Solution:**
Hapus `session_start()` dari file-file yang sudah include `config.php`

### **Problem 2: Missing Field in SQL SELECT**

**Root Cause:**
- Query di `LnD/dashboard.php` menggunakan `current_approver_role_id` di WHERE clause
- Tapi field tersebut tidak di-SELECT, sehingga tidak tersedia di result array
- PHP menganggap ini sebagai undefined array key

**Solution:**
- Tambahkan `current_approver_role_id` ke SELECT clause
- Tambahkan null check sebelum menggunakan field tersebut

## 📋 File yang Diperbaiki

### **1. LnD/security.php**

```php
// ❌ Sebelum
<?php
include '../config/config.php';
session_start();

// ✅ Sesudah
<?php
include '../config/config.php';
// session_start() sudah dipanggil di config.php, tidak perlu dipanggil lagi
```

### **2. LnD/dashboard.php**

**Fix 1: Tambah field ke SELECT clause**

```sql
-- ❌ Sebelum
SELECT
    ts.id,
    ts.training_topic,
    ts.start_date,
    ts.end_date,
    ts.is_confirmed,
    ts.approved_hrd,
    ts.status,
    ts.rejected_by,
    ts.status AS training_status,
    u.name AS requester_name,
    u.dept AS departemen,
    COUNT(p.id) AS total_participants
FROM training_submissions ts
WHERE (ts.current_approver_role_id = ? OR ...)

-- ✅ Sesudah
SELECT
    ts.id,
    ts.training_topic,
    ts.start_date,
    ts.end_date,
    ts.is_confirmed,
    ts.approved_hrd,
    ts.status,
    ts.rejected_by,
    ts.status AS training_status,
    ts.current_approver_role_id,  -- ✅ Ditambahkan
    u.name AS requester_name,
    u.dept AS departemen,
    COUNT(p.id) AS total_participants
FROM training_submissions ts
WHERE (ts.current_approver_role_id = ? OR ...)
GROUP BY ts.id, ts.current_approver_role_id, u.name, u.dept  -- ✅ Ditambahkan ke GROUP BY
```

**Fix 2: Tambah null check**

```php
// ❌ Sebelum
if ($row['current_approver_role_id'] == $_SESSION['role_id']) {
    $pending_for_lnd[] = $row;
}

// ✅ Sesudah
if (isset($row['current_approver_role_id']) && $row['current_approver_role_id'] == $_SESSION['role_id']) {
    $pending_for_lnd[] = $row;
}
```

### **3. File-file Config yang Diperbaiki**

**config/userinfo.php:**
```php
// ❌ Sebelum
<?php
session_start();
include '../config/config.php';

// ✅ Sesudah
<?php
include '../config/config.php';
// session_start() sudah dipanggil di config.php
```

**config/userinfoa.php:**
```php
// ❌ Sebelum
<?php
session_start();
include 'config.php';

// ✅ Sesudah
<?php
include 'config.php';
// session_start() sudah dipanggil di config.php
```

**config/verify_activation.php:**
```php
// ❌ Sebelum
<?php
session_start();
include 'config.php';

// ✅ Sesudah
<?php
include 'config.php';
// session_start() sudah dipanggil di config.php
```

**config/submita.php:**
```php
// ❌ Sebelum
<?php
session_start();
require '../config/config.php';

// ✅ Sesudah
<?php
require '../config/config.php';
// session_start() sudah dipanggil di config.php
```

**config/submit.php:**
```php
// ❌ Sebelum
<?php
session_start();
require 'config.php';

// ✅ Sesudah
<?php
require 'config.php';
// session_start() sudah dipanggil di config.php
```

**config/download_memo.php:**
```php
// ❌ Sebelum
<?php
session_start();
require_once 'config.php';

// ✅ Sesudah
<?php
require_once 'config.php';
// session_start() sudah dipanggil di config.php
```

## 🔍 Pattern yang Sudah Benar

### **File-file yang Sudah Menggunakan Pattern yang Benar:**

```php
// ✅ Pattern yang benar untuk session handling
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
```

**File yang sudah benar:**
- `config/navbara.php`
- `config/navbarb.php` 
- `config/navbar.php`
- `config/security_monitor.php`
- `admin/security.php`
- `view/error.php`
- `download.php`
- Dan file-file lainnya

## 🎯 Best Practices untuk Session Management

### **1. Centralized Session Start**
```php
// Di config.php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
```

### **2. Avoid Duplicate session_start()**
```php
// ❌ Jangan lakukan ini jika sudah include config.php
session_start();
include 'config.php';

// ✅ Lakukan ini
include 'config.php';
// session sudah dimulai di config.php
```

### **3. Safe Array Access**
```php
// ❌ Langsung akses tanpa check
$value = $array['key'];

// ✅ Gunakan isset() atau null coalescing
$value = isset($array['key']) ? $array['key'] : 'default';
$value = $array['key'] ?? 'default';
```

### **4. SQL Field Consistency**
```php
// ❌ Gunakan field di WHERE tapi tidak di SELECT
SELECT id, name FROM table WHERE hidden_field = ?

// ✅ SELECT semua field yang digunakan
SELECT id, name, hidden_field FROM table WHERE hidden_field = ?
```

## 🛠️ Testing

### **Before Fix:**
```
Notice: session_start(): Ignoring session_start()...
Warning: Undefined array key "current_approver_role_id"...
```

### **After Fix:**
- ✅ No session warnings
- ✅ No undefined array key warnings
- ✅ Dashboard loads properly
- ✅ All functionality works correctly

## 📚 Prevention Guidelines

### **1. Code Review Checklist**
- [ ] Check if `config.php` sudah di-include sebelum `session_start()`
- [ ] Pastikan semua field yang digunakan ada di SELECT clause
- [ ] Gunakan `isset()` atau `??` untuk array access
- [ ] Test di environment dengan error reporting enabled

### **2. Development Standards**
- Selalu gunakan `session_status()` check sebelum `session_start()`
- Selalu include `config.php` di awal file
- Gunakan null coalescing operator untuk safe array access
- Pastikan SQL SELECT dan WHERE clause konsisten

### **3. Error Monitoring**
- Enable error reporting di development
- Monitor PHP error logs di production
- Use static analysis tools untuk detect issues early

## ✅ Hasil

Setelah implementasi fix:

- ✅ **No Session Warnings**: Tidak ada lagi duplicate session_start() warnings
- ✅ **No Array Key Warnings**: Semua array access sudah safe
- ✅ **Clean Error Logs**: PHP error logs bersih dari warnings
- ✅ **Improved Performance**: Tidak ada overhead dari duplicate session calls
- ✅ **Better Code Quality**: Code lebih robust dan maintainable

---

**💡 TIP**: Selalu gunakan centralized session management di `config.php` dan avoid duplicate `session_start()` calls di file-file lain untuk mencegah warnings dan improve performance.
