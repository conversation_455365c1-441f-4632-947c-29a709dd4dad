<?php
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    include '../config/config.php';

    $name = $_POST['name'];
    $nik = $_POST['nik'];
    $email = $_POST['email'];

    // Periksa apakah NIK sudah terdaftar
    $query_nik = "SELECT * FROM users WHERE nik = '$nik'";
    $result_nik = mysqli_query($conn, $query_nik);

    if (mysqli_num_rows($result_nik) > 0) {
        $error_message = "NIK sudah terdaftar.";
    } else {
        // Periksa apakah email sudah terdaftar
        $query_email = "SELECT * FROM users WHERE email = '$email'";
        $result_email = mysqli_query($conn, $query_email);

        if (mysqli_num_rows($result_email) > 0) {
            $error_message = "Email sudah terdaftar.";
        } else {
            // Masukkan data pengguna baru ke database
            $query = "INSERT INTO users (name, nik, email) VALUES ('$name', '$nik', '$email')";
            if (mysqli_query($conn, $query)) {
                $_SESSION['success'] = "Pendaftaran berhasil, silakan login.";
                header('Location: login.php');
                exit();
            } else {
                $error_message = "Gagal mendaftar, coba lagi.";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    /* Base styles */
    body {
        font-family: 'Roboto', Arial, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 0;
    }

    /* Container styles */
    .container-form {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 120px); /* Adjust for navbar and footer */
        padding: 20px;
        background: linear-gradient(135deg, #c40000 0%, #8b0000 100%);
    }

    .signup-container {
        width: 100%;
        max-width: 400px;
        background: rgba(255, 255, 255, 0.98);
        border-radius: 15px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        padding: 30px;
        transition: all 0.3s ease;
    }

    /* Header styles */
    .signup-container h2 {
        color: #BF0000;
        font-size: 28px;
        margin-top: 0;
        margin-bottom: 20px;
        font-weight: 600;
        text-align: center;
    }

    /* Form styles */
    .signup-container form {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .signup-container label {
        display: block;
        margin-bottom: 8px;
        color: #444;
        font-weight: 500;
        font-size: 15px;
    }

    .signup-container input {
        width: 100%;
        padding: 14px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }

    .signup-container input:focus {
        border-color: #c40000;
        outline: none;
        box-shadow: 0 0 0 3px rgba(196, 0, 0, 0.1);
    }

    /* Button styles */
    .signup-container button {
        width: 100%;
        padding: 14px;
        background-color: #BF0000;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 10px;
    }

    .signup-container button:hover {
        background-color: #a00000;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .signup-container button:active {
        transform: translateY(0);
    }

    /* Error message */
    .error-message {
        color: #c40000;
        background-color: #fff2f2;
        border: 1px solid #ffcfcf;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: center;
        font-weight: 500;
    }

    /* Link styles */
    .signup-container p {
        text-align: center;
        margin-top: 20px;
        color: #555;
    }

    .signup-container a {
        color: #c40000;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .signup-container a:hover {
        color: #a00000;
        text-decoration: underline;
    }

    /* Responsive styles */
    @media (max-width: 768px) {
        .container-form {
            padding: 15px;
            padding-top: 70px; /* Add top padding to prevent navbar overlap */
        }

        .signup-container {
            padding: 25px 20px;
        }

        .signup-container h2 {
            font-size: 24px;
        }

        .signup-container input {
            padding: 12px;
            font-size: 14px;
        }

        .signup-container button {
            padding: 12px;
            font-size: 15px;
        }
    }

    @media (max-width: 480px) {
        .signup-container {
            padding: 20px 15px;
        }

        .signup-container h2 {
            font-size: 22px;
        }

        .signup-container label {
            font-size: 14px;
        }

        .signup-container input {
            padding: 10px;
            font-size: 14px;
        }

        .signup-container button {
            padding: 12px;
            font-size: 14px;
        }

        .error-message {
            padding: 10px;
            font-size: 14px;
        }
    }
</style>
<body>
    <?php include'../config/navbarb.php'?>
<div class="container-form">
    <div class="signup-container">
        <h2>Daftar Akun</h2>
        <?php if (isset($error_message)) : ?>
            <p class="error-message"><?php echo $error_message; ?></p>
        <?php endif; ?>
        <form method="POST">
            <label for="name">Nama Lengkap:</label>
            <input type="text" name="name" required>

            <label for="nik">NIK:</label>
            <input type="text" name="nik" required>

            <label for="email">Email:</label>
            <input type="email" name="email" required>

            <button type="submit">Daftar</button>
        </form>
        <p>Sudah punya akun? <a style="text-decoration:none;"href="login.php">Login disini</a></p>
    </div>
</div>
    <?php include'../config/footer.php'?>

</body>
</html>
