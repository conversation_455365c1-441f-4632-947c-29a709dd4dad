<?php
session_start();

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 6 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Include database connection file
include '../config/config.php';

// Ambil ID dari URL
if (isset($_GET['id'])) {
    $id = $_GET['id'];

    // Query untuk mengambil detail submission berdasarkan ID
    $query = "SELECT ts.id, ts.full_name, ts.email, ts.phone, ts.training_topic, ts.start_date, ts.end_date, ts.is_confirmed, ts.training_skill_type, ts.status, ts.additional_info, u.name AS approved_by_name, c.name AS canceled_by_name
              FROM training_submissions ts
              LEFT JOIN users u ON ts.approved_by = u.id
              LEFT JOIN users c ON ts.canceled_by = c.id
              WHERE ts.id = ?";

    // Persiapkan statement
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    // Cek apakah ada data yang ditemukan
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
    } else {
        echo "Training submission tidak ditemukan.";
        exit();
    }
} else {
    echo "ID tidak valid.";
    exit();
}

?>

<!DOCTYPE html>
<html lang="en">
    <?php include'../config/head.php'?>
    <style>
        body {
    font-family: 'Arial', sans-serif;
    background-color: #f4f7f6;
    margin: 0;
    padding: 0;
}

.container {
    width: 80%;
    margin: 20px auto;
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h2 {
    color: #333;
    font-size: 24px;
    text-align: center;
    margin-bottom: 20px;
}

table.submission-details {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

table.submission-details th,
table.submission-details td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

table.submission-details th {
    width: 150px;
    background-color: #f1f1f1;
    font-weight: bold;
}

table.submission-details td {
    background-color: #fafafa;
}

a.back-button {
    display: inline-block;
    padding: 10px 15px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    font-weight: bold;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

a.back-button:hover {
    background-color: #0056b3;
}

@media screen and (max-width: 768px) {
    .container {
        width: 90%;
    }

    table.submission-details th,
    table.submission-details td {
        font-size: 14px;
        padding: 8px;
    }
}

    </style>
<body>
    <div class="container">
        <h2>Training Submission Detail</h2>
        <table class="submission-details">
            <tr>
                <th>Full Name</th>
                <td><?php echo htmlspecialchars($row['full_name']); ?></td>
            </tr>
            <tr>
                <th>Email</th>
                <td><?php echo htmlspecialchars($row['email']); ?></td>
            </tr>
            <tr>
                <th>Phone</th>
                <td><?php echo htmlspecialchars($row['phone']); ?></td>
            </tr>
            <tr>
                <th>Training Topic</th>
                <td><?php echo htmlspecialchars($row['training_topic']); ?></td>
            </tr>
            <tr>
                <th>Training Date</th>
                <td>
                    <?php
                    if ($row['is_confirmed'] == 1 && $row['start_date']) {
                        echo '<span style="color: #2e7d32;"><i class="fas fa-calendar-check"></i> ' . htmlspecialchars($row['start_date']);
                        if ($row['end_date'] && $row['end_date'] != $row['start_date']) {
                            echo ' - ' . htmlspecialchars($row['end_date']);
                        }
                        echo ' (Confirmed)</span>';
                    } elseif ($row['start_date']) {
                        echo '<span style="color: #f57c00;"><i class="fas fa-calendar-alt"></i> ' . htmlspecialchars($row['start_date']);
                        if ($row['end_date'] && $row['end_date'] != $row['start_date']) {
                            echo ' - ' . htmlspecialchars($row['end_date']);
                        }
                        echo ' (Tentative)</span>';
                    } else {
                        echo '<span style="color: #999;"><i class="fas fa-calendar-times"></i> Belum ditentukan</span>';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <th>Jenis Skill Training</th>
                <td><?php echo htmlspecialchars($row['training_skill_type'] ?? 'Tidak ada jenis skill training'); ?></td>
            </tr>
            <tr>
                <th>Status</th>
                <td><?php echo htmlspecialchars($row['status']); ?></td>
            </tr>
            <tr>
                <th>Approved By</th>
                <td><?php echo htmlspecialchars($row['approved_by_name'] ? $row['approved_by_name'] : 'Not Approved'); ?></td>
            </tr>
            <tr>
                <th>Canceled By</th>
                <td><?php echo htmlspecialchars($row['canceled_by_name'] ? $row['canceled_by_name'] : 'Not Canceled'); ?></td>
            </tr>
            <tr>
                <th>Additional Info</th>
                <td><?php echo nl2br(htmlspecialchars($row['additional_info'])); ?></td>
            </tr>
        </table>

        <a href="approved.php" class="back-button">Back to List</a>
    </div>
    <?php include '../config/footer.php'; ?>
</body>
</html>
