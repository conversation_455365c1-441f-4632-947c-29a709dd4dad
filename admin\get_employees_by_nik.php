<?php
// Sertakan file koneksi database
require_once '../config/config.php';

// Inisialisasi response
$response = [
    'success' => false,
    'message' => '',
    'employees' => []
];

// Periksa apakah parameter NIK diberikan
if (!isset($_GET['niks']) || empty($_GET['niks'])) {
    $response['message'] = 'Parameter NIK tidak diberikan';
    echo json_encode($response);
    exit;
}

// Ambil NIK dari parameter
$niks = explode(',', $_GET['niks']);

// Validasi NIK
$validNIKs = [];
foreach ($niks as $nik) {
    $nik = trim($nik);
    if (!empty($nik)) {
        $validNIKs[] = $nik;
    }
}

if (empty($validNIKs)) {
    $response['message'] = 'Tidak ada NIK yang valid';
    echo json_encode($response);
    exit;
}

try {
    // Buat placeholder untuk query IN
    $placeholders = str_repeat('?,', count($validNIKs) - 1) . '?';

    // Query untuk mengambil data karyawan berdasarkan NIK
    $query = "SELECT * FROM karyawan WHERE nik IN ($placeholders)";

    // Prepare statement
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Prepare statement failed: " . $conn->error);
    }

    // Bind parameter
    $types = str_repeat('s', count($validNIKs));
    $params = array_merge([$types], $validNIKs);
    $bindParams = [];
    foreach ($params as $key => $value) {
        $bindParams[$key] = &$params[$key];
    }

    if (!call_user_func_array([$stmt, 'bind_param'], $bindParams)) {
        throw new Exception("Binding parameters failed: " . $stmt->error);
    }

    // Execute query
    if (!$stmt->execute()) {
        throw new Exception("Execute failed: " . $stmt->error);
    }

    // Get result
    $result = $stmt->get_result();
    if ($result === false) {
        throw new Exception("Getting result failed: " . $stmt->error);
    }

    // Fetch data
    while ($row = $result->fetch_assoc()) {
        $response['employees'][] = $row;
    }

    // Set success response
    $response['success'] = true;
    $response['message'] = 'Data karyawan berhasil diambil';
    $response['count'] = count($response['employees']);
    $response['requested_niks'] = $validNIKs;

} catch (Exception $e) {
    $response['message'] = 'Terjadi kesalahan: ' . $e->getMessage();
    $response['error_details'] = [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ];
}

// Return response as JSON
header('Content-Type: application/json');
echo json_encode($response);
