<?php
// Disable error display to prevent HTML output
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Set content type early
header('Content-Type: application/json');

// Start session
session_start();

// Include database connection
include '../config/config.php';

// Check authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Check database connection
if (!$conn) {
    echo json_encode(['error' => 'Database connection failed']);
    exit();
}

// Get filter parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? trim($_GET['status']) : '';
$date_from = isset($_GET['date_from']) ? trim($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? trim($_GET['date_to']) : '';

// Log received parameters for debugging
error_log("API Parameters - search: '$search', status: '$status_filter', date_from: '$date_from', date_to: '$date_to'");

// Validate date parameters - only use if both are provided and not empty
$use_date_filter = false;
if (!empty($date_from) && !empty($date_to)) {
    // Validate date format
    if (DateTime::createFromFormat('Y-m-d', $date_from) && DateTime::createFromFormat('Y-m-d', $date_to)) {
        $use_date_filter = true;
        error_log("Date filter enabled: $date_from to $date_to");
    } else {
        error_log("Invalid date format: from='$date_from', to='$date_to'");
    }
} else {
    error_log("Date filter disabled - empty parameters");
}

try {
    // First check what columns actually exist
    $columns_query = "SHOW COLUMNS FROM training_submissions";
    $columns_result = $conn->query($columns_query);
    $available_columns = [];
    while ($col = $columns_result->fetch_assoc()) {
        $available_columns[] = $col['Field'];
    }

    // Build SELECT clause based on available columns
    $select_fields = ['ts.id', 'ts.full_name', 'ts.training_topic', 'ts.status', 'ts.email'];

    // Add date columns if they exist
    if (in_array('start_date', $available_columns)) {
        $select_fields[] = 'ts.start_date';
    } else {
        $select_fields[] = 'NULL as start_date';
    }

    if (in_array('end_date', $available_columns)) {
        $select_fields[] = 'ts.end_date';
    } else {
        $select_fields[] = 'NULL as end_date';
    }

    if (in_array('training_date_start', $available_columns)) {
        $select_fields[] = 'ts.training_date_start';
    } else {
        $select_fields[] = 'NULL as training_date_start';
    }

    if (in_array('training_date_end', $available_columns)) {
        $select_fields[] = 'ts.training_date_end';
    } else {
        $select_fields[] = 'NULL as training_date_end';
    }

    if (in_array('submission_date', $available_columns)) {
        $select_fields[] = 'ts.submission_date';
    } else {
        $select_fields[] = 'ts.created_at as submission_date';
    }

    if (in_array('is_confirmed', $available_columns)) {
        $select_fields[] = 'ts.is_confirmed';
    } else {
        $select_fields[] = 'NULL as is_confirmed';
    }

    $select_fields[] = 'u.name as requester_name';

    $query = "SELECT " . implode(', ', $select_fields) . "
              FROM training_submissions ts
              LEFT JOIN users u ON ts.user_id = u.id
              WHERE (ts.deleted_at IS NULL OR ts.deleted_at = '')";

    $params = [];
    $types = "";

    // Add search filter
    if (!empty($search)) {
        $query .= " AND (ts.full_name LIKE ? OR ts.training_topic LIKE ? OR ts.email LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
        $types .= "sss";
    }

    // Add status filter
    if (!empty($status_filter)) {
        $query .= " AND ts.status = ?";
        $params[] = $status_filter;
        $types .= "s";
    }

    // Add date filter - only use if dates are valid
    if ($use_date_filter) {
        $date_conditions = [];
        if (in_array('start_date', $available_columns)) {
            $date_conditions[] = "ts.start_date BETWEEN ? AND ?";
            $params[] = $date_from;
            $params[] = $date_to;
            $types .= "ss";
        }
        if (in_array('training_date_fixed', $available_columns)) {
            $date_conditions[] = "ts.training_date_fixed BETWEEN ? AND ?";
            $params[] = $date_from;
            $params[] = $date_to;
            $types .= "ss";
        }
        if (in_array('training_date_start', $available_columns)) {
            $date_conditions[] = "ts.training_date_start BETWEEN ? AND ?";
            $params[] = $date_from;
            $params[] = $date_to;
            $types .= "ss";
        }
        if (in_array('training_date_end', $available_columns)) {
            $date_conditions[] = "ts.training_date_end BETWEEN ? AND ?";
            $params[] = $date_from;
            $params[] = $date_to;
            $types .= "ss";
        }
        if (!empty($date_conditions)) {
            $query .= " AND (" . implode(" OR ", $date_conditions) . ")";
        }
    }

    $query .= " ORDER BY ts.id DESC";

    // Prepare and execute query
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception('Database prepare error: ' . $conn->error);
    }

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    if (!$stmt->execute()) {
        throw new Exception('Database execute error: ' . $stmt->error);
    }

    $result = $stmt->get_result();
    if (!$result) {
        throw new Exception('Database result error: ' . $stmt->error);
    }

    // Fetch data
    $trainings = [];
    while ($row = $result->fetch_assoc()) {
        $trainings[] = [
            'id' => (int)$row['id'],
            'full_name' => $row['full_name'] ?? '',
            'training_topic' => $row['training_topic'] ?? '',
            'training_date' => $row['start_date'] ?? null, // Use start_date as primary date
            'training_date_fixed' => ($row['is_confirmed'] == 1) ? $row['start_date'] : null,
            'training_date_start' => $row['start_date'] ?? null,
            'training_date_end' => $row['end_date'] ?? null,
            'status' => $row['status'] ?? 'pending',
            'email' => $row['email'] ?? '',
            'submission_date' => $row['submission_date'] ?? null,
            'requester_name' => $row['requester_name'] ?? ''
        ];
    }

    // Log for debugging
    error_log("Available columns: " . implode(', ', $available_columns));
    error_log("Records found: " . count($trainings));

    // Return JSON response
    echo json_encode($trainings);

    $stmt->close();

} catch (Exception $e) {
    // Log error and return error response
    error_log("get_filtered_training.php error: " . $e->getMessage());
    echo json_encode(['error' => $e->getMessage()]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
