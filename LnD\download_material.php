<?php
/**
 * Download Material Handler for Pemohon (Applicant)
 * This script handles secure file downloads for training materials
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';
// Get user information
$user_id = $_SESSION['user_id'];

// Check if material ID is provided
$material_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($material_id <= 0) {
    die('ID materi tidak valid.');
}

// Get material information and verify user access
try {
    // Get material details
    $material_query = "SELECT m.*, c.id as class_id 
                     FROM training_materials m
                     JOIN training_classes c ON m.class_id = c.id
                     WHERE m.id = ? AND m.is_published = 1";
    $stmt = $conn->prepare($material_query);
    $stmt->bind_param("i", $material_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $material = $result->fetch_assoc();
    $stmt->close();

    if (!$material) {
        die('Materi tidak ditemukan.');
    }

    // Check if user is a participant in this class
    $participant_query = "SELECT * FROM training_participants 
                         WHERE class_id = ? AND user_id = ?";
    $stmt = $conn->prepare($participant_query);
    $stmt->bind_param("ii", $material['class_id'], $user_id);
    $stmt->execute();
    $is_participant = $stmt->get_result()->num_rows > 0;
    $stmt->close();

    if (!$is_participant) {
        die('Anda tidak terdaftar dalam kelas ini.');
    }

    // Get the file path
    $file_path = "../uploads/materials/" . $material['file_name'];

    // Check if file exists
    if (!file_exists($file_path)) {
        die('File tidak ditemukan.');
    }

    // Get file information
    $file_info = pathinfo($file_path);
    $file_size = filesize($file_path);

    // Set appropriate headers for file download
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $material['original_name'] . '"');
    header('Content-Length: ' . $file_size);
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: public');

    // Clear output buffer
    ob_clean();
    flush();

    // Output file content
    readfile($file_path);
    exit();

} catch (Exception $e) {
    error_log('Error downloading material: ' . $e->getMessage());
    die('Terjadi kesalahan saat mengunduh file. Silakan coba lagi nanti.');
}