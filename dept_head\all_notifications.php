<?php
include '../config/config.php';
include 'security.php';
include '../includes/notification_helper.php';

// Get all notifications for the current user
$query = "SELECT n.*, c.title as class_title 
          FROM training_notifications n
          LEFT JOIN training_classes c ON n.class_id = c.id
          WHERE n.user_id = ?
          ORDER BY n.created_at DESC";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

$notifications = [];
while ($row = $result->fetch_assoc()) {
    $notifications[] = $row;
}

// Mark all as read if requested
if (isset($_GET['mark_all_read'])) {
    $update_query = "UPDATE training_notifications SET is_read = 1 WHERE user_id = ?";
    $stmt = $conn->prepare($update_query);
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    
    $_SESSION['success'] = "Semua notifikasi telah ditandai sebagai dibaca.";
    header("Location: all_notifications.php");
    exit();
}

// Page title
$page_title = "Semua Notifikasi";
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #BF0000;
            --primary-color-dark: #900000;
            --primary-color-light: rgba(191, 0, 0, 0.1);
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --error-color: #F44336;
            --info-color: #2196F3;
            --text-dark: #333;
            --text-light: #666;
            --text-muted: #999;
            --bg-light: #f8f9fa;
            --border-color: #e0e0e0;
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 8px rgba(0,0,0,0.15);
            --shadow-lg: 0 8px 25px rgba(0,0,0,0.15);
            --border-radius: 8px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: var(--text-dark);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
        }

        .page-header h1 {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .page-header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .page-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-color-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-outline {
            background-color: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .alert {
            padding: 15px 20px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            border-left: 4px solid;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-left-color: var(--success-color);
        }

        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-left-color: var(--info-color);
        }

        .notifications-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .notification-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            transition: var(--transition);
            border-left: 4px solid var(--info-color);
        }

        .notification-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        .notification-card.unread {
            border-left-width: 6px;
            background: linear-gradient(90deg, rgba(33, 150, 243, 0.05) 0%, white 10%);
        }

        .notification-card.read {
            opacity: 0.8;
            border-left-color: var(--text-muted);
        }

        .notification-card.type-success {
            border-left-color: var(--success-color);
        }

        .notification-card.type-warning {
            border-left-color: var(--warning-color);
        }

        .notification-card.type-error {
            border-left-color: var(--error-color);
        }

        .notification-card.unread.type-success {
            background: linear-gradient(90deg, rgba(76, 175, 80, 0.05) 0%, white 10%);
        }

        .notification-card.unread.type-warning {
            background: linear-gradient(90deg, rgba(255, 152, 0, 0.05) 0%, white 10%);
        }

        .notification-card.unread.type-error {
            background: linear-gradient(90deg, rgba(244, 67, 54, 0.05) 0%, white 10%);
        }

        .notification-content {
            padding: 25px;
        }

        .notification-header {
            display: flex;
            align-items: flex-start;
            gap: 20px;
            margin-bottom: 15px;
        }

        .notification-icon {
            flex-shrink: 0;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .notification-icon.type-info {
            background-color: var(--info-color);
        }

        .notification-icon.type-success {
            background-color: var(--success-color);
        }

        .notification-icon.type-warning {
            background-color: var(--warning-color);
        }

        .notification-icon.type-error {
            background-color: var(--error-color);
        }

        .notification-main {
            flex: 1;
            min-width: 0;
        }

        .notification-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .notification-message {
            color: var(--text-light);
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .notification-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .notification-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        .notification-time {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .notification-class {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-mark-read {
            background-color: var(--bg-light);
            color: var(--text-light);
            border: 1px solid var(--border-color);
            padding: 8px 15px;
            font-size: 0.85rem;
        }

        .btn-mark-read:hover {
            background-color: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
        }

        .empty-state i {
            font-size: 4rem;
            color: var(--text-muted);
            margin-bottom: 20px;
        }

        .empty-state h3 {
            color: var(--text-light);
            margin-bottom: 10px;
        }

        .empty-state p {
            color: var(--text-muted);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .page-header {
                padding: 20px 15px;
                margin-bottom: 20px;
            }

            .page-header h1 {
                font-size: 1.5rem;
            }

            .page-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .notification-content {
                padding: 20px;
            }

            .notification-header {
                gap: 15px;
            }

            .notification-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .notification-title {
                font-size: 1.1rem;
            }

            .notification-footer {
                flex-direction: column;
                align-items: stretch;
            }

            .notification-meta {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <?php include '../config/navbara.php'; ?>

    <div class="container">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h1><i class="fas fa-bell"></i> Semua Notifikasi</h1>
            <p>Kelola dan lihat semua notifikasi Anda di satu tempat</p>
        </div>

        <!-- Success Message -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?= $_SESSION['success'] ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <!-- Page Actions -->
        <div class="page-actions">
            <div>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
                </a>
            </div>
            <div>
                <?php if (!empty($notifications)): ?>
                    <a href="all_notifications.php?mark_all_read=1" class="btn btn-outline">
                        <i class="fas fa-check-double"></i> Tandai Semua Dibaca
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Notifications Container -->
        <div class="notifications-container">
            <?php if (empty($notifications)): ?>
                <div class="empty-state">
                    <i class="fas fa-bell-slash"></i>
                    <h3>Tidak Ada Notifikasi</h3>
                    <p>Anda belum memiliki notifikasi. Notifikasi akan muncul di sini ketika ada update terkait training atau aktivitas sistem.</p>
                </div>
            <?php else: ?>
                <?php foreach ($notifications as $notification): ?>
                    <?php
                    $icon_class = 'fa-info-circle';
                    $type_class = 'type-info';

                    switch ($notification['type']) {
                        case 'success':
                            $icon_class = 'fa-check-circle';
                            $type_class = 'type-success';
                            break;
                        case 'warning':
                            $icon_class = 'fa-exclamation-triangle';
                            $type_class = 'type-warning';
                            break;
                        case 'error':
                            $icon_class = 'fa-times-circle';
                            $type_class = 'type-error';
                            break;
                    }
                    ?>
                    <div class="notification-card <?= $notification['is_read'] ? 'read' : 'unread' ?> <?= $type_class ?>">
                        <div class="notification-content">
                            <div class="notification-header">
                                <div class="notification-icon <?= $type_class ?>">
                                    <i class="fas <?= $icon_class ?>"></i>
                                </div>
                                <div class="notification-main">
                                    <div class="notification-title">
                                        <?= htmlspecialchars($notification['title']) ?>
                                        <?php if (!$notification['is_read']): ?>
                                            <span style="color: var(--primary-color); font-size: 0.8em; margin-left: 10px;">● BARU</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="notification-message">
                                        <?= nl2br(htmlspecialchars($notification['message'])) ?>
                                    </div>
                                </div>
                            </div>

                            <div class="notification-footer">
                                <div class="notification-meta">
                                    <div class="notification-time">
                                        <i class="fas fa-clock"></i>
                                        <?= date('d M Y H:i', strtotime($notification['created_at'])) ?>
                                    </div>
                                    <?php if ($notification['class_title']): ?>
                                        <div class="notification-class">
                                            <i class="fas fa-chalkboard"></i>
                                            <?= htmlspecialchars($notification['class_title']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <?php if (!$notification['is_read']): ?>
                                    <a href="mark_notification.php?id=<?= $notification['id'] ?>&redirect=<?= urlencode($_SERVER['REQUEST_URI']) ?>" class="btn btn-mark-read">
                                        <i class="fas fa-check"></i> Tandai Dibaca
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Add smooth scroll behavior
        document.documentElement.style.scrollBehavior = 'smooth';

        // Add loading animation for mark as read buttons
        document.querySelectorAll('.btn-mark-read').forEach(button => {
            button.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';
                this.style.pointerEvents = 'none';
            });
        });

        // Add entrance animation for notification cards
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Apply animation to notification cards
        document.querySelectorAll('.notification-card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            card.style.transitionDelay = `${index * 0.1}s`;
            observer.observe(card);
        });
    </script>
</body>
</html>
