<?php
session_start();
include '../config/config.php';

header('Content-Type: application/json');

try {
    $raw_data = file_get_contents('php://input');
    $data = json_decode($raw_data, true);
    
    if (!isset($data['user_id']) || !isset($data['status'])) {
        throw new Exception('Missing required fields: user_id or status');
    }

    $user_id = (int)$data['user_id'];
    $status = $data['status'] ? 1 : 0;

    if ($user_id <= 0) {
        throw new Exception('Invalid user ID');
    }

    // Menghapus updated_at dari query
    $query = "UPDATE users SET is_active = ? WHERE id = ?";
    $stmt = $conn->prepare($query);
    
    if (!$stmt) {
        throw new Exception('Database prepare error: ' . $conn->error);
    }

    $stmt->bind_param("ii", $status, $user_id);
    
    if (!$stmt->execute()) {
        throw new Exception('Database execute error: ' . $stmt->error);
    }

    if ($stmt->affected_rows === 0) {
        throw new Exception('No user found with ID: ' . $user_id);
    }

    echo json_encode([
        'success' => true,
        'message' => 'User status updated successfully'
    ]);

} catch (Exception $e) {
    error_log("Toggle user status error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($stmt)) {
        $stmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}

