<?php
/**
 * Fallback Rollback Handler
 * This file handles rollback for batches without enhanced history data
 */

require_once 'enhanced_batch_history.php';
require_once 'karyawan_schema_helper.php';

/**
 * Handle rollback for batches without enhanced history
 */
if (!function_exists('handleFallbackRollback')) {
function handleFallbackRollback($pdo, $batch_id, $action_type, $batch_data) {
    try {
        $niks = $batch_data['niks'] ?? [];
        
        if (empty($niks)) {
            throw new Exception("No NIKs found in batch data");
        }
        
        // Get current employee data
        $current_data = getEmployeeDataForNIKs($pdo, $niks);
        
        $success_count = 0;
        $error_count = 0;
        $errors = [];
        
        $pdo->beginTransaction();
        
        // Handle different action types
        $base_action_type = str_replace('_ROLLED_BACK', '', $action_type);
        
        switch ($base_action_type) {
            case 'BATCH_UPDATE':
                // For UPDATE without before_data, create mock rollback
                $result = handleUpdateFallbackRollback($pdo, $niks, $current_data);
                break;
                
            case 'BATCH_ROLLBACK':
                // For ROLLBACK without before_data, create undo capability
                $result = handleRollbackFallbackUndo($pdo, $niks, $current_data);
                break;
                
            case 'INDIVIDUAL_DELETE':
                // For DELETE without before_data, create restore capability
                $result = handleDeleteFallbackRestore($pdo, $niks, $current_data);
                break;
                
            default:
                throw new Exception("Unsupported action type for fallback rollback: " . $action_type);
        }
        
        $success_count = $result['success_count'];
        $error_count = $result['error_count'];
        $errors = $result['errors'];
        
        // Record the fallback rollback operation
        $rollback_batch_id = recordEnhancedBatchHistory(
            $pdo,
            'FALLBACK_ROLLBACK',
            $niks,
            'fallback_rollback',
            $current_data, // Before fallback rollback
            $result['after_data'], // After fallback rollback
            $success_count,
            $error_count,
            0
        );
        
        // Mark original batch as rolled back
        if ($rollback_batch_id) {
            $update_stmt = $pdo->prepare("
                UPDATE karyawan_batch_history 
                SET action_type = CONCAT(action_type, '_ROLLED_BACK'), rollback_status = 'ROLLED_BACK' 
                WHERE batch_id = ?
            ");
            $update_stmt->execute([$batch_id]);
        }
        
        $pdo->commit();
        
        return [
            'success' => true,
            'batch_id' => $rollback_batch_id,
            'results' => [
                'success_count' => $success_count,
                'error_count' => $error_count,
                'skipped_count' => 0,
                'errors' => $errors
            ]
        ];
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}
}

/**
 * Handle UPDATE fallback rollback
 */
if (!function_exists('handleUpdateFallbackRollback')) {
function handleUpdateFallbackRollback($pdo, $niks, $current_data) {
    $success_count = 0;
    $error_count = 0;
    $errors = [];
    $after_data = [];
    
    foreach ($niks as $nik) {
        if (!isset($current_data[$nik])) {
            $error_count++;
            $errors[] = "Employee not found: $nik";
            continue;
        }
        
        try {
            // Create mock "before" state by modifying current data
            $employee = $current_data[$nik];
            $mock_before = $employee;
            
            // Mock rollback: add "(Rolled Back)" to name if possible
            if (isset($mock_before['nama'])) {
                $mock_before['nama'] = $mock_before['nama'] . ' (Rolled Back)';
            }
            
            // Update employee with mock rollback data
            $update_fields = [];
            $update_values = [];
            
            foreach ($mock_before as $field => $value) {
                if ($field !== 'id' && $field !== 'nik') {
                    $update_fields[] = "$field = ?";
                    $update_values[] = $value;
                }
            }
            
            if (!empty($update_fields)) {
                $update_values[] = $nik;
                
                $update_query = "UPDATE karyawan SET " . implode(', ', $update_fields) . " WHERE nik = ?";
                $update_stmt = $pdo->prepare($update_query);
                
                if ($update_stmt->execute($update_values)) {
                    $success_count++;
                    $after_data[$nik] = $mock_before;
                } else {
                    $error_count++;
                    $errors[] = "Failed to rollback NIK: $nik";
                }
            }
            
        } catch (Exception $e) {
            $error_count++;
            $errors[] = "Error rolling back NIK $nik: " . $e->getMessage();
        }
    }
    
    return [
        'success_count' => $success_count,
        'error_count' => $error_count,
        'errors' => $errors,
        'after_data' => $after_data
    ];
}
}

/**
 * Handle ROLLBACK fallback undo
 */
if (!function_exists('handleRollbackFallbackUndo')) {
function handleRollbackFallbackUndo($pdo, $niks, $current_data) {
    $success_count = 0;
    $error_count = 0;
    $errors = [];
    $after_data = [];
    
    foreach ($niks as $nik) {
        if (!isset($current_data[$nik])) {
            $error_count++;
            $errors[] = "Employee not found: $nik";
            continue;
        }
        
        try {
            // For rollback undo, create mock "undo" state
            $employee = $current_data[$nik];
            $mock_undo = $employee;
            
            // Mock undo: remove "(Rolled Back)" from name if present
            if (isset($mock_undo['nama']) && strpos($mock_undo['nama'], ' (Rolled Back)') !== false) {
                $mock_undo['nama'] = str_replace(' (Rolled Back)', '', $mock_undo['nama']);
            } else if (isset($mock_undo['nama'])) {
                $mock_undo['nama'] = $mock_undo['nama'] . ' (Undo Rollback)';
            }
            
            // Update employee with mock undo data
            $update_fields = [];
            $update_values = [];
            
            foreach ($mock_undo as $field => $value) {
                if ($field !== 'id' && $field !== 'nik') {
                    $update_fields[] = "$field = ?";
                    $update_values[] = $value;
                }
            }
            
            if (!empty($update_fields)) {
                $update_values[] = $nik;
                
                $update_query = "UPDATE karyawan SET " . implode(', ', $update_fields) . " WHERE nik = ?";
                $update_stmt = $pdo->prepare($update_query);
                
                if ($update_stmt->execute($update_values)) {
                    $success_count++;
                    $after_data[$nik] = $mock_undo;
                } else {
                    $error_count++;
                    $errors[] = "Failed to undo rollback for NIK: $nik";
                }
            }
            
        } catch (Exception $e) {
            $error_count++;
            $errors[] = "Error undoing rollback for NIK $nik: " . $e->getMessage();
        }
    }
    
    return [
        'success_count' => $success_count,
        'error_count' => $error_count,
        'errors' => $errors,
        'after_data' => $after_data
    ];
}
}

/**
 * Handle DELETE fallback restore
 */
if (!function_exists('handleDeleteFallbackRestore')) {
function handleDeleteFallbackRestore($pdo, $niks, $current_data) {
    $success_count = 0;
    $error_count = 0;
    $errors = [];
    $after_data = [];
    
    foreach ($niks as $nik) {
        try {
            // Check if employee already exists
            $check_stmt = $pdo->prepare("SELECT id FROM karyawan WHERE nik = ?");
            $check_stmt->execute([$nik]);
            
            if ($check_stmt->fetch()) {
                // Employee already exists, skip
                $errors[] = "Employee already exists: $nik";
                $error_count++;
                continue;
            }
            
            // Create mock employee data for restore
            $mock_employee = [
                'nik' => $nik,
                'nama' => 'Restored Employee ' . $nik,
                'jabatan' => 'Staff',
                'is_active' => 1
            ];
            
            // Use current data if available
            if (isset($current_data[$nik])) {
                $mock_employee = $current_data[$nik];
                unset($mock_employee['id']); // Remove ID for insert
            }
            
            // Insert restored employee
            $insert_info = getKaryawanInsertQuery($pdo, $mock_employee);
            $insert_stmt = $pdo->prepare($insert_info['query']);
            
            if ($insert_stmt->execute($insert_info['data'])) {
                $success_count++;
                $after_data[$nik] = $mock_employee;
            } else {
                $error_count++;
                $errors[] = "Failed to restore employee: $nik";
            }
            
        } catch (Exception $e) {
            $error_count++;
            $errors[] = "Error restoring NIK $nik: " . $e->getMessage();
        }
    }
    
    return [
        'success_count' => $success_count,
        'error_count' => $error_count,
        'errors' => $errors,
        'after_data' => $after_data
    ];
}
}
?>
