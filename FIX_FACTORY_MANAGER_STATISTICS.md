# 🔧 Fix Statistik Factory Manager Dashboard

## ❌ Masalah yang Terjadi

Di dashboard Factory Manager (`dept_head/dashboard.php`), **statistik tidak menampilkan data yang benar** karena menggunakan field approval yang salah:

### **Statistik yang Salah:**
```
Total Pengajuan Departemen: 0    ← Seharusnya ada data
Menunggu Persetujuan Anda: 0     ← Seharusnya ada data
Disetujui Oleh Anda: 0           ← Seharusnya ada data
Dibatalkan Oleh Anda: 0          ← Seharusnya ada data
```

### **Root Cause:**
Statistik Factory Manager menggunakan **field yang salah**:
- ❌ **Approved**: Menggunakan `approved_dept_head` (seharusnya `approved_fm`)
- ❌ **Rejected**: Menggunakan `rejected_by` (seharusnya `approved_fm = 'Rejected'`)
- ❌ **Pending**: Menggunakan logic dept head (seharusnya logic Factory Manager)
- ❌ **Total**: Menggunakan filter departemen (seharusnya semua training yang sampai ke FM)

## ✅ Solusi yang Diimplementasi

### **Enhanced Statistics Logic untuk Factory Manager**

**Approach:** Buat logic statistik yang berbeda untuk Factory Manager vs Dept Head biasa.

### **1. Fix Approved Count**

```php
// Query untuk menghitung jumlah training yang disetujui - berbeda untuk Factory Manager
if ($is_factory_manager) {
    // Untuk Factory Manager, hitung berdasarkan approved_fm
    $query_approved = "SELECT COUNT(*) as total_approved
                      FROM training_submissions ts
                      LEFT JOIN users u ON ts.user_id = u.id
                      WHERE ts.approved_fm = 'Approved'
                      AND ts.status != 'Rejected'";
    $stmt_approved = $conn->prepare($query_approved);
    $stmt_approved->execute();
    $result_approved = $stmt_approved->get_result();
    $approved_count = $result_approved->fetch_assoc()['total_approved'];
    $stmt_approved->close();
} else {
    // Untuk Dept Head biasa, hitung berdasarkan approved_dept_head
    $query_approved = "SELECT COUNT(*) as total_approved
                      FROM training_submissions ts
                      LEFT JOIN users u ON ts.user_id = u.id
                      WHERE ts.approved_dept_head = 'Approved'
                      AND ts.status != 'Rejected'
                      AND COALESCE(ts.departemen, u.dept) IN (...)";
    // ... logic dept head
}
```

**Changes:**
- ✅ **Factory Manager**: Gunakan `approved_fm = 'Approved'`
- ✅ **Dept Head**: Tetap gunakan `approved_dept_head = 'Approved'`
- ✅ **No Department Filter**: Factory Manager tidak dibatasi departemen

### **2. Fix Rejected Count**

```php
// Query untuk menghitung jumlah training yang di-reject - berbeda untuk Factory Manager
if ($is_factory_manager) {
    // Untuk Factory Manager, hitung berdasarkan approved_fm = 'Rejected'
    $query_rejected = "SELECT COUNT(*) as total_rejected
                      FROM training_submissions ts
                      LEFT JOIN users u ON ts.user_id = u.id
                      WHERE ts.approved_fm = 'Rejected'
                      AND ts.status = 'Rejected'";
    $stmt_rejected = $conn->prepare($query_rejected);
    $stmt_rejected->execute();
    $result_rejected = $stmt_rejected->get_result();
    $rejected_count = $result_rejected->fetch_assoc()['total_rejected'];
    $stmt_rejected->close();
} else {
    // Untuk Dept Head biasa, hitung berdasarkan rejected_by
    $query_rejected = "SELECT COUNT(*) as total_rejected
                      FROM training_submissions ts
                      LEFT JOIN users u ON ts.user_id = u.id
                      WHERE ts.rejected_by = ?
                      AND ts.status = 'Rejected'
                      AND COALESCE(ts.departemen, u.dept) IN (...)";
    // ... logic dept head
}
```

**Changes:**
- ✅ **Factory Manager**: Gunakan `approved_fm = 'Rejected'`
- ✅ **Dept Head**: Tetap gunakan `rejected_by = user_id`

### **3. Fix Pending Count**

```php
// Query untuk menghitung jumlah training yang menunggu persetujuan - berbeda untuk Factory Manager
if ($is_factory_manager) {
    // Untuk Factory Manager, hitung training yang menunggu approved_fm
    $query_pending = "SELECT COUNT(*) as total_pending
                     FROM training_submissions ts
                     LEFT JOIN users u ON ts.user_id = u.id
                     WHERE (
                         -- Training yang ditujukan langsung ke Factory Manager
                         (ts.current_approver_role_id = 2 AND ts.next_approver_id = ?)
                         OR
                         -- Training yang sudah disetujui Manager HRGA dan menunggu Factory Manager
                         (ts.approved_ga = 'Approved' AND (ts.approved_fm IS NULL OR ts.approved_fm = 'Pending'))
                     )
                     AND ts.status != 'Rejected'";
    $stmt_pending = $conn->prepare($query_pending);
    $stmt_pending->bind_param("i", $user_id);
    // ... execute
} else {
    // Logic dept head biasa
}
```

**Changes:**
- ✅ **Factory Manager**: Hitung training yang menunggu `approved_fm`
- ✅ **Condition 1**: Training yang ditujukan langsung ke Factory Manager
- ✅ **Condition 2**: Training yang sudah disetujui Manager HRGA

### **4. Fix Total Count**

```php
// Query untuk menghitung jumlah total training - berbeda untuk Factory Manager
if ($is_factory_manager) {
    // Untuk Factory Manager, hitung semua training yang pernah sampai ke level Factory Manager
    $query_total = "SELECT COUNT(*) as total_submissions
                   FROM training_submissions ts
                   LEFT JOIN users u ON ts.user_id = u.id
                   WHERE (
                       -- Training yang sudah disetujui Manager HRGA (sampai ke Factory Manager)
                       ts.approved_ga = 'Approved'
                       OR
                       -- Training yang sudah di-approve/reject oleh Factory Manager
                       ts.approved_fm IS NOT NULL
                   )
                   AND ts.status != 'Rejected'";
    $stmt_total = $conn->prepare($query_total);
    $stmt_total->execute();
    // ... execute
} else {
    // Logic dept head berdasarkan departemen
}
```

**Changes:**
- ✅ **Factory Manager**: Hitung semua training yang sampai ke level Factory Manager
- ✅ **Condition 1**: Training yang sudah disetujui Manager HRGA
- ✅ **Condition 2**: Training yang sudah di-handle oleh Factory Manager

## 📋 File yang Diperbaiki

### **dept_head/dashboard.php**

**Location 1: Lines 185-213 (Approved Count)**
```php
// ❌ Sebelum (salah untuk Factory Manager)
WHERE ts.approved_dept_head = 'Approved'

// ✅ Sesudah (benar untuk Factory Manager)
if ($is_factory_manager) {
    WHERE ts.approved_fm = 'Approved'
} else {
    WHERE ts.approved_dept_head = 'Approved'
}
```

**Location 2: Lines 215-244 (Rejected Count)**
```php
// ❌ Sebelum (salah untuk Factory Manager)
WHERE ts.rejected_by = ?

// ✅ Sesudah (benar untuk Factory Manager)
if ($is_factory_manager) {
    WHERE ts.approved_fm = 'Rejected'
} else {
    WHERE ts.rejected_by = ?
}
```

**Location 3: Lines 246-286 (Pending Count)**
```php
// ❌ Sebelum (salah untuk Factory Manager)
WHERE ts.current_approver_role_id = 2
AND (ts.next_approver_id = ? OR ...)

// ✅ Sesudah (benar untuk Factory Manager)
if ($is_factory_manager) {
    WHERE (
        (ts.current_approver_role_id = 2 AND ts.next_approver_id = ?)
        OR
        (ts.approved_ga = 'Approved' AND (ts.approved_fm IS NULL OR ts.approved_fm = 'Pending'))
    )
} else {
    // Logic dept head biasa
}
```

**Location 4: Lines 288-322 (Total Count)**
```php
// ❌ Sebelum (salah untuk Factory Manager)
WHERE COALESCE(ts.departemen, u.dept) IN (...)

// ✅ Sesudah (benar untuk Factory Manager)
if ($is_factory_manager) {
    WHERE (
        ts.approved_ga = 'Approved'
        OR
        ts.approved_fm IS NOT NULL
    )
} else {
    WHERE COALESCE(ts.departemen, u.dept) IN (...)
}
```

## 🎯 Database Fields untuk Factory Manager

### **Training Approval Workflow:**
```
Dept Head → LnD → Manager HRGA → Factory Manager → Direktur
approved_dept_head → approved_hrd → approved_ga → approved_fm → approved_dir
```

### **Factory Manager Fields:**
- **`approved_fm`**: Status approval Factory Manager ('Pending', 'Approved', 'Rejected')
- **`comments_fm`**: Comments dari Factory Manager
- **`approved_ga`**: Status dari Manager HRGA (prerequisite untuk Factory Manager)
- **`current_approver_role_id`**: Role ID yang sedang meng-approve (2 = Dept Head level)
- **`next_approver_id`**: ID user Factory Manager yang dituju

## 🎯 Testing Scenarios

### **Test Case 1: Factory Manager Login**
**Expected Statistics:**
```
✅ Total Pengajuan: [Jumlah training yang sampai ke Factory Manager]
✅ Menunggu Persetujuan: [Training dengan approved_ga='Approved' dan approved_fm='Pending']
✅ Disetujui Oleh Anda: [Training dengan approved_fm='Approved']
✅ Dibatalkan Oleh Anda: [Training dengan approved_fm='Rejected']
```

### **Test Case 2: Dept Head Biasa Login**
**Expected Statistics:**
```
✅ Total Pengajuan: [Training dari departemen yang dikelola]
✅ Menunggu Persetujuan: [Training dengan approved_dept_head='Pending']
✅ Disetujui Oleh Anda: [Training dengan approved_dept_head='Approved']
✅ Dibatalkan Oleh Anda: [Training yang di-reject oleh user ini]
```

### **Test Case 3: Training Data Flow**
**Scenario:**
1. Training diajukan → approved_dept_head='Approved'
2. LnD approve → approved_hrd='Approved'
3. Manager HRGA approve → approved_ga='Approved'
4. **Factory Manager approve** → approved_fm='Approved' ✨
5. Direktur approve → approved_dir='Approved'

**Expected Factory Manager Statistics:**
- ✅ **Total**: +1 (training sampai ke Factory Manager)
- ✅ **Pending**: -1 (tidak lagi pending)
- ✅ **Approved**: +1 (Factory Manager approve)

## 🛠️ Benefits

### **Accuracy:**
- ✅ **Correct Fields**: Menggunakan field approval yang tepat untuk Factory Manager
- ✅ **Proper Logic**: Logic statistik yang sesuai dengan role Factory Manager
- ✅ **No Department Filter**: Factory Manager tidak dibatasi departemen

### **Functionality:**
- ✅ **Working Statistics**: Statistik menampilkan data yang benar
- ✅ **Proper Counts**: Angka yang akurat untuk semua kategori
- ✅ **Role-based Logic**: Logic yang berbeda untuk Factory Manager vs Dept Head

### **User Experience:**
- ✅ **Informative Dashboard**: Factory Manager bisa melihat workload yang benar
- ✅ **Clear Metrics**: Statistik yang meaningful dan actionable
- ✅ **Proper Workflow**: Dashboard yang sesuai dengan approval workflow

## ✅ Hasil

### **Before Fix:**
```
❌ Total Pengajuan Departemen: 0    ← Tidak ada data
❌ Menunggu Persetujuan Anda: 0     ← Tidak ada data
❌ Disetujui Oleh Anda: 0           ← Tidak ada data
❌ Dibatalkan Oleh Anda: 0          ← Tidak ada data
❌ Tidak ada training yang perlu di-approve saat ini
```

### **After Fix:**
```
✅ Total Pengajuan: [Angka yang benar]     ← Data Factory Manager
✅ Menunggu Persetujuan: [Angka yang benar] ← Training pending untuk FM
✅ Disetujui Oleh Anda: [Angka yang benar]  ← Training yang di-approve FM
✅ Dibatalkan Oleh Anda: [Angka yang benar] ← Training yang di-reject FM
✅ [Daftar training yang perlu di-approve]  ← Data training yang benar
```

---

**💡 KEY FIX**: Factory Manager sekarang menggunakan field `approved_fm` yang benar untuk semua statistik, bukan field `approved_dept_head` yang salah. Dashboard menampilkan data yang akurat sesuai dengan role dan approval workflow.
