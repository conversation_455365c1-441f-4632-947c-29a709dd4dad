<?php
// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Include fungsi NIK
include 'includes/nik_functions.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi variabel
$success_message = '';
$error_message = '';
$duplicate_groups = [];
$total_duplicates = 0;

// Cek apakah kolom normalized_nik sudah ada
$check_column_query = "SHOW COLUMNS FROM karyawan LIKE 'normalized_nik'";
$check_column_result = $conn->query($check_column_query);

if ($check_column_result->num_rows == 0) {
    $error_message = "Kolom normalized_nik belum ada. Silakan hubungi administrator sistem untuk mengaktifkan fitur ini.";
} else {
    // Jalankan script untuk membuat tabel ignored_duplicates jika belum ada
    ob_start(); // Mulai output buffering untuk menangkap output
    include 'create_ignored_duplicates_table.php';
    ob_end_clean(); // Bersihkan buffer dan abaikan output

    // Proses aksi merge jika ada
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        // Proses bulk ignore
        if ($_POST['action'] === 'bulk_ignore') {
            try {
                // Mulai transaction
                $conn->begin_transaction();

                $bulk_ignore_data = isset($_POST['bulk_ignore_data']) ? json_decode($_POST['bulk_ignore_data'], true) : [];

                if (empty($bulk_ignore_data)) {
                    throw new Exception('Tidak ada data yang dipilih untuk diabaikan secara massal');
                }

                $ignored_count = 0;
                $log_details = [];

                foreach ($bulk_ignore_data as $group_data) {
                    $group_type = isset($group_data['group_type']) ? $group_data['group_type'] : '';
                    $group_value = isset($group_data['group_value']) ? $group_data['group_value'] : '';
                    $employee_ids = isset($group_data['employee_ids']) ? $group_data['employee_ids'] : [];

                    if (empty($group_type) || empty($group_value) || empty($employee_ids)) {
                        continue; // Skip jika data tidak lengkap
                    }

                    // Konversi array employee_ids menjadi string jika perlu
                    if (is_array($employee_ids)) {
                        $employee_ids = implode(',', array_map('intval', $employee_ids));
                    }

                    // Cek apakah kelompok ini sudah diabaikan sebelumnya
                    $check_query = "SELECT id FROM ignored_duplicates WHERE group_type = ? AND group_value = ?";
                    $stmt = $conn->prepare($check_query);
                    $stmt->bind_param("ss", $group_type, $group_value);
                    $stmt->execute();
                    $result = $stmt->get_result();

                    if ($result->num_rows > 0) {
                        // Update data yang sudah ada
                        $row = $result->fetch_assoc();
                        $update_query = "UPDATE ignored_duplicates SET employee_ids = ?, ignored_by = ?, ignored_at = CURRENT_TIMESTAMP WHERE id = ?";
                        $stmt = $conn->prepare($update_query);
                        $stmt->bind_param("sii", $employee_ids, $_SESSION['user_id'], $row['id']);
                        $stmt->execute();
                    } else {
                        // Insert data baru
                        $insert_query = "INSERT INTO ignored_duplicates (group_type, group_value, employee_ids, ignored_by) VALUES (?, ?, ?, ?)";
                        $stmt = $conn->prepare($insert_query);
                        $stmt->bind_param("sssi", $group_type, $group_value, $employee_ids, $_SESSION['user_id']);
                        $stmt->execute();
                    }

                    $ignored_count++;

                    $log_details[] = [
                        'group_type' => $group_type,
                        'group_value' => $group_value,
                        'employee_count' => count(explode(',', $employee_ids))
                    ];
                }

                // Log aktivitas bulk ignore
                if (file_exists('../config/activity_logger.php') && $ignored_count > 0) {
                    include_once '../config/activity_logger.php';
                    if (function_exists('log_activity')) {
                        log_activity($_SESSION['user_id'], "Mengabaikan $ignored_count kelompok data duplikat secara massal", "employee", [
                            'bulk_ignore' => true,
                            'ignored_count' => $ignored_count,
                            'details' => $log_details
                        ]);
                    }
                }

                // Commit transaction
                $conn->commit();

                if ($ignored_count > 0) {
                    $success_message = "Berhasil mengabaikan $ignored_count kelompok data duplikat secara massal. Data ini tidak akan muncul lagi di halaman kelola duplikat.";
                } else {
                    $error_message = "Tidak ada data yang berhasil diabaikan";
                }

                // Redirect untuk menyegarkan halaman dan menghilangkan kelompok yang diabaikan
                header("Location: manage_duplicate_employees.php?success=" . urlencode($success_message));
                exit();

            } catch (Exception $e) {
                // Rollback transaction jika terjadi error
                if ($conn->connect_errno === 0) {
                    $conn->rollback();
                }

                $error_message = $e->getMessage();
            }
        }
        // Proses ignore duplicate
        else if ($_POST['action'] === 'ignore') {
            try {
                // Mulai transaction
                $conn->begin_transaction();

                $group_type = isset($_POST['group_type']) ? $_POST['group_type'] : '';
                $group_value = isset($_POST['group_value']) ? $_POST['group_value'] : '';
                $employee_ids = isset($_POST['employee_ids']) ? $_POST['employee_ids'] : [];

                if (empty($group_type) || empty($group_value) || empty($employee_ids)) {
                    throw new Exception('Data tidak lengkap untuk mengabaikan kelompok');
                }

                // Konversi array employee_ids menjadi string jika perlu
                if (is_array($employee_ids)) {
                    $employee_ids = implode(',', array_map('intval', $employee_ids));
                }

                // Cek apakah kelompok ini sudah diabaikan sebelumnya
                $check_query = "SELECT id FROM ignored_duplicates WHERE group_type = ? AND group_value = ?";
                $stmt = $conn->prepare($check_query);
                $stmt->bind_param("ss", $group_type, $group_value);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result->num_rows > 0) {
                    // Update data yang sudah ada
                    $row = $result->fetch_assoc();
                    $update_query = "UPDATE ignored_duplicates SET employee_ids = ?, ignored_by = ?, ignored_at = CURRENT_TIMESTAMP WHERE id = ?";
                    $stmt = $conn->prepare($update_query);
                    $stmt->bind_param("sii", $employee_ids, $_SESSION['user_id'], $row['id']);
                    $stmt->execute();
                } else {
                    // Insert data baru
                    $insert_query = "INSERT INTO ignored_duplicates (group_type, group_value, employee_ids, ignored_by) VALUES (?, ?, ?, ?)";
                    $stmt = $conn->prepare($insert_query);
                    $stmt->bind_param("sssi", $group_type, $group_value, $employee_ids, $_SESSION['user_id']);
                    $stmt->execute();
                }

                // Log aktivitas
                if (file_exists('../config/activity_logger.php')) {
                    include_once '../config/activity_logger.php';
                    if (function_exists('log_activity')) {
                        log_activity($_SESSION['user_id'], "Mengabaikan kelompok data duplikat ($group_type: $group_value)", "employee", [
                            'group_type' => $group_type,
                            'group_value' => $group_value,
                            'employee_ids' => $employee_ids
                        ]);
                    }
                }

                // Commit transaction
                $conn->commit();

                $success_message = "Berhasil mengabaikan kelompok data duplikat. Data ini tidak akan muncul lagi di halaman kelola duplikat.";

                // Redirect untuk menyegarkan halaman dan menghilangkan kelompok yang diabaikan
                header("Location: manage_duplicate_employees.php?success=" . urlencode($success_message));
                exit();

            } catch (Exception $e) {
                // Rollback transaction jika terjadi error
                if ($conn->connect_errno === 0) {
                    $conn->rollback();
                }

                $error_message = $e->getMessage();
            }
        }
        // Proses bulk merge
        else if ($_POST['action'] === 'bulk_merge') {
            try {
                // Mulai transaction
                $conn->begin_transaction();

                $bulk_data = isset($_POST['bulk_data']) ? json_decode($_POST['bulk_data'], true) : [];

                if (empty($bulk_data)) {
                    throw new Exception('Tidak ada data yang dipilih untuk penggabungan masal');
                }

                $total_deleted = 0;
                $merged_groups = 0;
                $log_details = [];

                foreach ($bulk_data as $group_data) {
                    $keep_id = isset($group_data['keep_id']) ? (int)$group_data['keep_id'] : 0;
                    $merge_ids = isset($group_data['merge_ids']) ? $group_data['merge_ids'] : [];

                    if (empty($merge_ids) || $keep_id === 0) {
                        continue; // Skip jika data tidak lengkap
                    }

                    // Konversi array merge_ids menjadi string untuk query
                    $merge_ids_string = implode(',', array_map('intval', $merge_ids));

                    // Ambil data karyawan yang akan dipertahankan
                    $query = "SELECT * FROM karyawan WHERE id = ?";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("i", $keep_id);
                    $stmt->execute();
                    $result = $stmt->get_result();

                    if ($result->num_rows === 0) {
                        continue; // Skip jika data karyawan tidak ditemukan
                    }

                    $keep_employee = $result->fetch_assoc();

                    // Update referensi di tabel lain yang menggunakan NIK karyawan yang akan dihapus
                    // Contoh: tabel training_submissions, dll.
                    // Sesuaikan dengan struktur database Anda

                    // Hapus karyawan yang tidak dipertahankan
                    $delete_query = "DELETE FROM karyawan WHERE id IN ($merge_ids_string) AND id != ?";
                    $stmt = $conn->prepare($delete_query);
                    $stmt->bind_param("i", $keep_id);
                    $stmt->execute();

                    $deleted_count = $stmt->affected_rows;
                    $total_deleted += $deleted_count;

                    if ($deleted_count > 0) {
                        $merged_groups++;

                        // Log aktivitas untuk setiap grup
                        if (file_exists('../config/activity_logger.php')) {
                            include_once '../config/activity_logger.php';
                            if (function_exists('log_activity')) {
                                log_activity($_SESSION['user_id'], "Menggabungkan $deleted_count data karyawan duplikat dengan NIK {$keep_employee['nik']}", "employee", [
                                    'kept_id' => $keep_id,
                                    'merged_ids' => $merge_ids,
                                    'merged_count' => $deleted_count,
                                    'bulk_merge' => true
                                ]);
                            }
                        }

                        $log_details[] = [
                            'nik' => $keep_employee['nik'],
                            'nama' => $keep_employee['nama'],
                            'deleted_count' => $deleted_count
                        ];
                    }
                }

                // Log aktivitas bulk merge
                if (file_exists('../config/activity_logger.php') && $total_deleted > 0) {
                    include_once '../config/activity_logger.php';
                    if (function_exists('log_activity')) {
                        log_activity($_SESSION['user_id'], "Menggabungkan $total_deleted data karyawan duplikat dari $merged_groups kelompok secara masal", "employee", [
                            'bulk_merge' => true,
                            'total_deleted' => $total_deleted,
                            'merged_groups' => $merged_groups,
                            'details' => $log_details
                        ]);
                    }
                }

                // Commit transaction
                $conn->commit();

                if ($total_deleted > 0) {
                    $success_message = "Berhasil menggabungkan $total_deleted data karyawan duplikat dari $merged_groups kelompok";
                } else {
                    $error_message = "Tidak ada data yang berhasil digabungkan";
                }

            } catch (Exception $e) {
                // Rollback transaction jika terjadi error
                if ($conn->connect_errno === 0) {
                    $conn->rollback();
                }

                $error_message = $e->getMessage();
            }
        }
        // Proses single merge
        else if ($_POST['action'] === 'merge') {
            try {
                // Mulai transaction
                $conn->begin_transaction();

                $keep_id = isset($_POST['keep_id']) ? (int)$_POST['keep_id'] : 0;
                $merge_ids = isset($_POST['merge_ids']) ? $_POST['merge_ids'] : [];

                // Handle case where merge_ids is a comma-separated string
                if (is_array($merge_ids) && count($merge_ids) === 1 && strpos($merge_ids[0], ',') !== false) {
                    $merge_ids = explode(',', $merge_ids[0]);
                }

                if (empty($merge_ids) || $keep_id === 0) {
                    throw new Exception('Data tidak lengkap untuk proses penggabungan');
                }

                // Konversi array merge_ids menjadi string untuk query
                $merge_ids_string = implode(',', array_map('intval', $merge_ids));

                // Ambil data karyawan yang akan dipertahankan
                $query = "SELECT * FROM karyawan WHERE id = ?";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("i", $keep_id);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result->num_rows === 0) {
                    throw new Exception('Data karyawan yang akan dipertahankan tidak ditemukan');
                }

                $keep_employee = $result->fetch_assoc();

                // Update referensi di tabel lain yang menggunakan NIK karyawan yang akan dihapus
                // Contoh: tabel training_submissions, dll.
                // Sesuaikan dengan struktur database Anda

                // Hapus karyawan yang tidak dipertahankan
                $delete_query = "DELETE FROM karyawan WHERE id IN ($merge_ids_string) AND id != ?";
                $stmt = $conn->prepare($delete_query);
                $stmt->bind_param("i", $keep_id);
                $stmt->execute();

                $deleted_count = $stmt->affected_rows;

                // Log aktivitas
                if (file_exists('../config/activity_logger.php')) {
                    include_once '../config/activity_logger.php';
                    if (function_exists('log_activity')) {
                        log_activity($_SESSION['user_id'], "Menggabungkan $deleted_count data karyawan duplikat dengan NIK {$keep_employee['nik']}", "employee", [
                            'kept_id' => $keep_id,
                            'merged_ids' => $merge_ids,
                            'merged_count' => $deleted_count
                        ]);
                    }
                }

                // Commit transaction
                $conn->commit();

                $success_message = "Berhasil menggabungkan $deleted_count data karyawan duplikat";

            } catch (Exception $e) {
                // Rollback transaction jika terjadi error
                if ($conn->connect_errno === 0) {
                    $conn->rollback();
                }

                $error_message = $e->getMessage();
            }
        }
    }

    // Ambil daftar normalized_nik yang sudah diabaikan
    $ignored_niks = [];
    $ignored_query = "SELECT group_value FROM ignored_duplicates WHERE group_type = 'nik'";
    $ignored_result = $conn->query($ignored_query);
    if ($ignored_result && $ignored_result->num_rows > 0) {
        while ($row = $ignored_result->fetch_assoc()) {
            $ignored_niks[] = $row['group_value'];
        }
    }

    // Cari potensi duplikasi berdasarkan normalized_nik
    $query = "SELECT normalized_nik, COUNT(*) as count
              FROM karyawan
              WHERE normalized_nik IS NOT NULL";

    // Tambahkan kondisi untuk mengabaikan normalized_nik yang sudah diabaikan
    if (!empty($ignored_niks)) {
        $ignored_niks_str = "'" . implode("','", array_map([$conn, 'real_escape_string'], $ignored_niks)) . "'";
        $query .= " AND normalized_nik NOT IN ($ignored_niks_str)";
    }

    $query .= " GROUP BY normalized_nik
               HAVING COUNT(*) > 1";
    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $normalized_nik = $row['normalized_nik'];
            $count = $row['count'];
            $total_duplicates += $count;

            // Ambil detail karyawan dengan normalized_nik yang sama
            $detail_query = "SELECT * FROM karyawan WHERE normalized_nik = ? ORDER BY nama";
            $stmt = $conn->prepare($detail_query);
            $stmt->bind_param("s", $normalized_nik);
            $stmt->execute();
            $detail_result = $stmt->get_result();

            $employees = [];
            while ($employee = $detail_result->fetch_assoc()) {
                $employees[] = $employee;
            }

            $duplicate_groups[] = [
                'normalized_nik' => $normalized_nik,
                'count' => $count,
                'employees' => $employees
            ];
        }
    }

    // Ambil daftar nama+dept yang sudah diabaikan
    $ignored_names = [];
    $ignored_query = "SELECT group_value FROM ignored_duplicates WHERE group_type = 'name'";
    $ignored_result = $conn->query($ignored_query);
    if ($ignored_result && $ignored_result->num_rows > 0) {
        while ($row = $ignored_result->fetch_assoc()) {
            $ignored_names[] = $row['group_value'];
        }
    }

    // Cari potensi duplikasi berdasarkan nama dan data lainnya
    $query = "SELECT nama, dept, CONCAT(nama, '|', dept) as name_key, COUNT(*) as count
              FROM karyawan";

    // Tambahkan kondisi untuk mengabaikan nama+dept yang sudah diabaikan
    if (!empty($ignored_names)) {
        $ignored_names_str = "'" . implode("','", array_map([$conn, 'real_escape_string'], $ignored_names)) . "'";
        $query .= " WHERE CONCAT(nama, '|', dept) NOT IN ($ignored_names_str)";
    }

    $query .= " GROUP BY nama, dept
               HAVING COUNT(*) > 1";
    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $nama = $row['nama'];
            $dept = $row['dept'];
            $count = $row['count'];

            // Ambil detail karyawan dengan nama dan dept yang sama
            $detail_query = "SELECT * FROM karyawan WHERE nama = ? AND dept = ? ORDER BY nik";
            $stmt = $conn->prepare($detail_query);
            $stmt->bind_param("ss", $nama, $dept);
            $stmt->execute();
            $detail_result = $stmt->get_result();

            $employees = [];
            $normalized_niks = [];

            while ($employee = $detail_result->fetch_assoc()) {
                $employees[] = $employee;
                $normalized_niks[] = $employee['normalized_nik'] ?? normalizeNIK($employee['nik']);
            }

            // Hanya tambahkan ke duplicate_groups jika normalized_nik berbeda
            // dan belum ada di duplicate_groups sebelumnya
            if (count(array_unique($normalized_niks)) > 1) {
                $is_new_group = true;

                // Cek apakah grup ini sudah ada di duplicate_groups
                foreach ($duplicate_groups as $group) {
                    $group_niks = array_map(function($emp) {
                        return $emp['normalized_nik'] ?? normalizeNIK($emp['nik']);
                    }, $group['employees']);

                    // Jika ada irisan antara normalized_niks dan group_niks, berarti grup ini sudah ada
                    if (count(array_intersect($normalized_niks, $group_niks)) > 0) {
                        $is_new_group = false;
                        break;
                    }
                }

                if ($is_new_group) {
                    $duplicate_groups[] = [
                        'nama' => $nama,
                        'dept' => $dept,
                        'count' => $count,
                        'employees' => $employees,
                        'by_name' => true
                    ];
                    $total_duplicates += $count;
                }
            }
        }
    }
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    body {
        background-color: #f5f5f5;
        overflow-x: hidden;
    }

    .container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
    }

    .card {
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 20px;
    }

    .card-header {
        background-color: #BF0000 !important;
        color: white;
        padding: 15px 20px;
    }

    .card-header h3 {
        margin: 0;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
    }

    .card-header h3 i {
        margin-right: 10px;
    }

    .card-body {
        padding: 20px;
        background-color: white;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    .alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }

    .alert-warning {
        background-color: #fff3cd;
        border-color: #ffeeba;
        color: #856404;
    }

    .info-box {
        background-color: #e7f3fe;
        border: 1px solid #b6d4fe;
        border-left: 4px solid #0d6efd;
        color: #084298;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .info-box h4 {
        color: #084298;
        margin-top: 0;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .info-box h4 i {
        margin-right: 10px;
    }

    .info-box p {
        margin-bottom: 10px;
    }

    .info-box p:last-child {
        margin-bottom: 0;
    }

    .duplicate-group {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        margin-bottom: 20px;
        overflow: hidden;
    }

    .duplicate-group-header {
        background-color: #e9ecef;
        padding: 10px 15px;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .group-header-left {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .ignore-checkbox {
        width: 18px;
        height: 18px;
        cursor: pointer;
    }

    .clickable-header {
        cursor: pointer;
        user-select: none;
        padding: 2px 5px;
        border-radius: 3px;
        transition: background-color 0.2s ease;
    }

    .clickable-header:hover {
        background-color: #e2e6ea;
    }

    .duplicate-group-body {
        padding: 15px;
    }

    .employee-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 15px;
    }

    .employee-table th {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 600;
        text-align: left;
        padding: 10px;
        border-bottom: 2px solid #dee2e6;
    }

    .employee-table td {
        padding: 10px;
        border-bottom: 1px solid #e9ecef;
        color: #495057;
    }

    .employee-table tbody tr {
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .employee-table tbody tr:hover {
        background-color: #f1f8ff;
    }

    .employee-table tr.selected {
        background-color: #e7f3fe;
        border-left: 3px solid #0d6efd;
        font-weight: 500;
    }

    .employee-table tr.selected td {
        background-color: #e7f3fe;
    }

    .btn-primary {
        background-color: #BF0000;
        border-color: #BF0000;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #a00000;
        border-color: #a00000;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #5a6268;
    }

    .btn-sm {
        padding: 5px 10px;
        font-size: 0.875rem;
    }

    .jarak {
        height: 100px;
    }

    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #dee2e6;
    }

    .empty-state h4 {
        margin-bottom: 10px;
        font-size: 1.5rem;
    }

    .empty-state p {
        max-width: 500px;
        margin: 0 auto;
    }

    .radio-container {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .highlight {
        background-color: #ffffcc;
        font-weight: bold;
    }

    .merge-actions {
        margin-top: 15px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .skip-merge-button {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .skip-merge-button:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    .group-skipped {
        opacity: 0.6;
        position: relative;
    }

    .group-skipped::after {
        content: 'Diabaikan';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(108, 117, 125, 0.8);
        color: white;
        padding: 5px 15px;
        border-radius: 5px;
        font-weight: bold;
        z-index: 10;
    }

    .bulk-actions {
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .bulk-buttons {
        display: flex;
        gap: 10px;
    }

    .bulk-select-all {
        display: flex;
        align-items: center;
        gap: 5px;
        font-weight: 500;
    }

    .bulk-select-all input[type="checkbox"] {
        width: 18px;
        height: 18px;
        cursor: pointer;
    }

    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .btn-danger:disabled {
        background-color: #dc354580;
        border-color: #dc354580;
        cursor: not-allowed;
    }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container">
    <div class="card">
        <div class="card-header">
            <h3><i class="fas fa-user-friends"></i> Kelola Data Karyawan Duplikat</h3>
        </div>
        <div class="card-body">
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <div class="info-box">
                <h4><i class="fas fa-info-circle"></i> Informasi</h4>
                <p>Halaman ini menampilkan data karyawan yang terdeteksi sebagai duplikat berdasarkan NIK yang sudah dinormalisasi (tanpa karakter non-angka) atau berdasarkan kesamaan nama dan departemen.</p>
                <p>Untuk menggabungkan data duplikat, cukup pilih satu data yang ingin dipertahankan dengan mengklik baris data tersebut atau radio button di sampingnya. Semua data lainnya dalam kelompok yang sama akan otomatis dihapus saat Anda mengklik tombol "Gabungkan Data Terpilih".</p>
                <p>Jika Anda yakin bahwa data tertentu bukan merupakan duplikat atau tidak perlu digabungkan, klik tombol "Jangan Gabungkan". Data tersebut akan dihapus dari daftar duplikat dan tidak akan muncul lagi di halaman ini.</p>
                <p>Untuk mengabaikan beberapa kelompok data duplikat sekaligus, centang kotak di samping kelompok yang ingin diabaikan, lalu klik tombol "Jangan Gabungkan Data Terpilih" di bagian atas halaman.</p>
                <p><i class="fas fa-info-circle"></i> <strong>Catatan:</strong> Data yang ditandai "Jangan Gabungkan" akan disimpan secara permanen dan tidak akan muncul lagi di halaman kelola duplikat, kecuali jika ada perubahan data baru.</p>
                <p><i class="fas fa-lightbulb"></i> <strong>Tip:</strong> Anda dapat mengklik di mana saja pada baris untuk memilih data tersebut.</p>
            </div>

            <?php if (empty($duplicate_groups)): ?>
                <div class="empty-state">
                    <i class="fas fa-check-circle"></i>
                    <h4>Tidak Ada Data Duplikat</h4>
                    <p>Tidak ditemukan data karyawan yang duplikat dalam sistem.</p>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> Ditemukan <?php echo count($duplicate_groups); ?> kelompok data duplikat dengan total <?php echo $total_duplicates; ?> karyawan.
                </div>

                <div class="bulk-actions">
                    <div class="bulk-buttons">
                        <form method="post" id="bulkIgnoreForm">
                            <input type="hidden" name="action" value="bulk_ignore">
                            <input type="hidden" name="bulk_ignore_data" id="bulkIgnoreDataInput" value="">
                            <button type="button" id="bulkIgnoreButton" class="btn-secondary" disabled>
                                <i class="fas fa-ban"></i> Jangan Gabungkan Data Terpilih
                            </button>
                        </form>
                        <form method="post" id="bulkMergeForm">
                            <input type="hidden" name="action" value="bulk_merge">
                            <input type="hidden" name="bulk_data" id="bulkDataInput" value="">
                            <button type="button" id="bulkMergeButton" class="btn-danger" disabled>
                                <i class="fas fa-object-group"></i> Gabungkan Semua Data Terpilih
                            </button>
                        </form>
                    </div>
                    <div class="bulk-select-all">
                        <label>
                            <input type="checkbox" id="selectAllCheckbox"> Pilih Semua
                        </label>
                    </div>
                </div>

                <?php foreach ($duplicate_groups as $index => $group): ?>
                    <div class="duplicate-group">
                        <div class="duplicate-group-header">
                            <div class="group-header-left">
                                <input type="checkbox" class="ignore-checkbox" id="ignore-checkbox-<?php echo $index; ?>" data-group="<?php echo $index; ?>" title="Pilih untuk diabaikan secara massal">
                                <?php if (isset($group['normalized_nik'])): ?>
                                    <span class="clickable-header" data-checkbox="ignore-checkbox-<?php echo $index; ?>" title="Klik untuk memilih/membatalkan pilihan">Kelompok Duplikat NIK: <?php echo $group['normalized_nik']; ?> (<?php echo $group['count']; ?> karyawan)</span>
                                <?php else: ?>
                                    <span class="clickable-header" data-checkbox="ignore-checkbox-<?php echo $index; ?>" title="Klik untuk memilih/membatalkan pilihan">Kelompok Duplikat Nama: <?php echo $group['nama']; ?> - <?php echo $group['dept']; ?> (<?php echo $group['count']; ?> karyawan)</span>
                                <?php endif; ?>
                            </div>
                            <button class="btn-secondary btn-sm toggle-group" data-group="<?php echo $index; ?>">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="duplicate-group-body" id="group-<?php echo $index; ?>">
                            <form method="post" class="merge-form">
                                <input type="hidden" name="action" value="merge">

                                <table class="employee-table">
                                    <thead>
                                        <tr>
                                            <th>Pertahankan</th>
                                            <th>NIK</th>
                                            <th>Nama</th>
                                            <th>Departemen</th>
                                            <th>Bagian</th>
                                            <th>Jabatan</th>
                                            <th>Tgl Masuk</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($group['employees'] as $employee): ?>
                                            <tr title="Klik untuk memilih data ini">
                                                <td>
                                                    <div class="radio-container">
                                                        <input type="radio" name="keep_id" value="<?php echo $employee['id']; ?>" class="keep-radio" required>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="<?php echo isset($group['normalized_nik']) ? 'highlight' : ''; ?>"><?php echo htmlspecialchars($employee['nik']); ?></span>
                                                    <?php if (isset($employee['normalized_nik'])): ?>
                                                        <br><small>(Normalized: <?php echo $employee['normalized_nik']; ?>)</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="<?php echo isset($group['by_name']) ? 'highlight' : ''; ?>"><?php echo htmlspecialchars($employee['nama']); ?></td>
                                                <td class="<?php echo isset($group['by_name']) ? 'highlight' : ''; ?>"><?php echo htmlspecialchars($employee['dept']); ?></td>
                                                <td><?php echo htmlspecialchars($employee['bagian']); ?></td>
                                                <td><?php echo htmlspecialchars($employee['jabatan']); ?></td>
                                                <td><?php echo htmlspecialchars($employee['tgl_masuk']); ?></td>
                                                <td><?php echo htmlspecialchars($employee['status']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>

                                <div class="merge-actions">
                                    <button type="submit" class="btn-primary merge-button">
                                        <i class="fas fa-object-group"></i> Gabungkan Data Terpilih
                                    </button>
                                </div>
                            </form>

                            <form method="post" class="ignore-form" style="margin-top: 10px;" onsubmit="event.preventDefault(); showConfirmDialog('Anda yakin ingin mengabaikan kelompok data duplikat ini? Data ini tidak akan muncul lagi di halaman kelola duplikat.', () => { this.submit(); })">
                                <input type="hidden" name="action" value="ignore">
                                <?php if (isset($group['normalized_nik'])): ?>
                                    <input type="hidden" name="group_type" value="nik">
                                    <input type="hidden" name="group_value" value="<?php echo htmlspecialchars($group['normalized_nik']); ?>">
                                <?php else: ?>
                                    <input type="hidden" name="group_type" value="name">
                                    <input type="hidden" name="group_value" value="<?php echo htmlspecialchars($group['nama'] . '|' . $group['dept']); ?>">
                                <?php endif; ?>
                                <input type="hidden" name="employee_ids" value="<?php
                                    echo implode(',', array_map(function($emp) { return $emp['id']; }, $group['employees']));
                                ?>">
                                <button type="submit" class="btn-secondary ignore-button" style="width: 100%;">
                                    <i class="fas fa-ban"></i> Jangan Gabungkan
                                </button>
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <div class="text-center mt-4">
                <a href="employee_management.php" class="btn-secondary" style="text-decoration: none;">
                    <i class="fas fa-arrow-left"></i> Kembali ke Manajemen Karyawan
                </a>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
// Fungsi untuk menampilkan toast notification
function showToast(message, type = 'success', duration = 3000) {
    // Hapus toast yang sudah ada jika ada
    const existingToast = document.querySelector('.toast-notification');
    if (existingToast) {
        document.body.removeChild(existingToast);
    }

    // Buat toast baru
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;

    // Set icon berdasarkan type
    let icon = 'check';
    if (type === 'error') icon = 'exclamation-circle';
    if (type === 'warning') icon = 'exclamation-triangle';
    if (type === 'info') icon = 'info-circle';

    toast.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;
    document.body.appendChild(toast);

    // Animasi toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // Hapus toast setelah durasi tertentu
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// Fungsi untuk menampilkan dialog konfirmasi kustom
function showConfirmDialog(message, onConfirm, onCancel) {
    // Hapus dialog yang sudah ada jika ada
    const existingDialog = document.querySelector('.confirm-dialog-container');
    if (existingDialog) {
        document.body.removeChild(existingDialog);
    }

    // Buat dialog konfirmasi
    const dialogContainer = document.createElement('div');
    dialogContainer.className = 'confirm-dialog-container';

    dialogContainer.innerHTML = `
        <div class="confirm-dialog">
            <div class="confirm-dialog-header">
                <i class="fas fa-question-circle"></i>
                <h4>Konfirmasi</h4>
            </div>
            <div class="confirm-dialog-body">
                <p>${message}</p>
            </div>
            <div class="confirm-dialog-footer">
                <button class="btn btn-secondary btn-cancel">Batal</button>
                <button class="btn btn-danger btn-confirm">Ya, Lanjutkan</button>
            </div>
        </div>
    `;

    document.body.appendChild(dialogContainer);

    // Tambahkan event listener untuk tombol
    const cancelBtn = dialogContainer.querySelector('.btn-cancel');
    const confirmBtn = dialogContainer.querySelector('.btn-confirm');

    cancelBtn.addEventListener('click', () => {
        document.body.removeChild(dialogContainer);
        if (onCancel) onCancel();
    });

    confirmBtn.addEventListener('click', () => {
        document.body.removeChild(dialogContainer);
        if (onConfirm) onConfirm();
    });

    // Animasi dialog
    setTimeout(() => {
        dialogContainer.classList.add('show');
    }, 10);
}

// Tambahkan CSS untuk toast notification dan dialog konfirmasi
document.head.insertAdjacentHTML('beforeend', `
<style>
.toast-notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.toast-notification.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.toast-success i {
    color: #4CAF50;
}

.toast-error i {
    color: #F44336;
}

.toast-warning i {
    color: #FF9800;
}

.toast-info i {
    color: #2196F3;
}

.confirm-dialog-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.confirm-dialog-container.show {
    opacity: 1;
}

.confirm-dialog {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.confirm-dialog-container.show .confirm-dialog {
    transform: translateY(0);
}

.confirm-dialog-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px;
}

.confirm-dialog-header i {
    color: #BF0000;
    font-size: 1.2rem;
}

.confirm-dialog-header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: #212529;
}

.confirm-dialog-body {
    padding: 20px;
}

.confirm-dialog-body p {
    margin: 0;
    color: #495057;
}

.confirm-dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

@media screen and (max-width: 576px) {
    .confirm-dialog {
        width: 95%;
    }

    .confirm-dialog-header,
    .confirm-dialog-body,
    .confirm-dialog-footer {
        padding: 12px 15px;
    }
}
</style>
`);

document.addEventListener('DOMContentLoaded', function() {
    // Toggle group visibility
    const toggleButtons = document.querySelectorAll('.toggle-group');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const groupIndex = this.getAttribute('data-group');
            const groupBody = document.getElementById('group-' + groupIndex);

            if (groupBody.style.display === 'none') {
                groupBody.style.display = 'block';
                this.innerHTML = '<i class="fas fa-chevron-up"></i>';
            } else {
                groupBody.style.display = 'none';
                this.innerHTML = '<i class="fas fa-chevron-down"></i>';
            }
        });
    });

    // Handle radio selection
    const mergeForms = document.querySelectorAll('.merge-form');
    const bulkMergeButton = document.getElementById('bulkMergeButton');
    const bulkMergeForm = document.getElementById('bulkMergeForm');
    const bulkDataInput = document.getElementById('bulkDataInput');

    // Array untuk menyimpan data yang akan di-merge secara bulk
    let bulkMergeData = [];

    // Fungsi untuk mengupdate status tombol bulk merge
    function updateBulkMergeButton() {
        const validGroups = bulkMergeData.filter(group =>
            group.keep_id !== null && !group.skipped
        );

        if (validGroups.length > 0) {
            bulkMergeButton.disabled = false;
            bulkMergeButton.innerHTML = `<i class="fas fa-object-group"></i> Gabungkan ${validGroups.length} Kelompok Data Terpilih`;
        } else {
            bulkMergeButton.disabled = true;
            bulkMergeButton.innerHTML = `<i class="fas fa-object-group"></i> Gabungkan Semua Data Terpilih`;
        }
    }

    // Inisialisasi array bulkMergeData
    mergeForms.forEach((form, index) => {
        bulkMergeData[index] = {
            group_index: index,
            keep_id: null,
            merge_ids: [],
            skipped: false
        };
    });

    mergeForms.forEach((form, formIndex) => {
        const keepRadios = form.querySelectorAll('.keep-radio');
        const employeeRows = form.querySelectorAll('tbody tr');

        // Collect all employee IDs in this group
        const allEmployeeIds = Array.from(keepRadios).map(radio => parseInt(radio.value));

        // Function to handle selection of a row
        function selectRow(radio) {
            const keepId = parseInt(radio.value);

            // Update bulkMergeData
            bulkMergeData[formIndex].keep_id = keepId;

            // Set merge_ids to all IDs except the one to keep
            bulkMergeData[formIndex].merge_ids = allEmployeeIds.filter(id => id !== keepId);

            // Highlight the selected row
            employeeRows.forEach(row => {
                const rowRadio = row.querySelector('.keep-radio');
                if (rowRadio && parseInt(rowRadio.value) === keepId) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }
            });

            updateBulkMergeButton();
        }

        // When a radio is selected
        keepRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                selectRow(this);
            });
        });

        // Make entire row clickable
        employeeRows.forEach(row => {
            row.style.cursor = 'pointer'; // Change cursor to pointer to indicate clickable

            row.addEventListener('click', function(e) {
                // Don't trigger if clicking on the radio button itself (it already handles the change event)
                if (e.target.type !== 'radio') {
                    const radio = this.querySelector('.keep-radio');
                    if (radio) {
                        radio.checked = true;
                        selectRow(radio);
                    }
                }
            });
        });

        // Form submission validation
        form.addEventListener('submit', function(e) {
            const checkedRadios = form.querySelectorAll('.keep-radio:checked');

            if (checkedRadios.length === 0) {
                e.preventDefault();
                showToast('Silakan pilih satu data yang akan dipertahankan', 'warning');
                return;
            }

            // Get all employee IDs in this group
            const keepId = parseInt(checkedRadios[0].value);
            const allEmployeeIds = Array.from(form.querySelectorAll('.keep-radio')).map(radio => parseInt(radio.value));
            const mergeIds = allEmployeeIds.filter(id => id !== keepId);

            // Set hidden input values
            const mergeIdsInput = document.createElement('input');
            mergeIdsInput.type = 'hidden';
            mergeIdsInput.name = 'merge_ids[]';
            mergeIdsInput.value = mergeIds.join(',');
            form.appendChild(mergeIdsInput);

            // Confirm before submitting
            e.preventDefault();
            showConfirmDialog(`Anda yakin ingin mempertahankan data terpilih dan menghapus ${mergeIds.length} data lainnya?`, () => {
                // Submit form jika user mengkonfirmasi
                form.submit();
            }, () => {
                // Batal jika user tidak mengkonfirmasi
                form.removeChild(mergeIdsInput);
            });
        });
    });

    // Bulk merge button click handler
    bulkMergeButton.addEventListener('click', function() {
        const validGroups = bulkMergeData.filter(group =>
            group.keep_id !== null && group.merge_ids.length > 0 && !group.skipped
        );

        if (validGroups.length === 0) {
            showToast('Tidak ada data yang dipilih untuk penggabungan masal', 'warning');
            return;
        }

        // Count total records to be deleted
        const totalToDelete = validGroups.reduce((total, group) => total + group.merge_ids.length, 0);

        // Count skipped groups
        const skippedCount = bulkMergeData.filter(group => group.skipped).length;
        let skippedMessage = '';
        if (skippedCount > 0) {
            skippedMessage = ` (${skippedCount} kelompok diabaikan)`;
        }

        // Set bulk data input value
        bulkDataInput.value = JSON.stringify(validGroups);

        // Confirm before submitting
        showConfirmDialog(`Anda yakin ingin mempertahankan ${validGroups.length} data terpilih dan menghapus ${totalToDelete} data lainnya dari ${validGroups.length} kelompok${skippedMessage}?`, () => {
            bulkMergeForm.submit();
        });
    });

    // Handle skip merge buttons
    const skipMergeButtons = document.querySelectorAll('.skip-merge-button');
    const skippedGroups = new Set();

    skipMergeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const groupIndex = this.getAttribute('data-group');
            const duplicateGroup = this.closest('.duplicate-group');
            const form = duplicateGroup.querySelector('form');

            if (skippedGroups.has(groupIndex)) {
                // Unskip this group
                skippedGroups.delete(groupIndex);
                duplicateGroup.classList.remove('group-skipped');
                this.innerHTML = '<i class="fas fa-ban"></i> Jangan Gabungkan';

                // Re-enable form elements
                const formElements = form.querySelectorAll('input, button.merge-button');
                formElements.forEach(el => el.disabled = false);

                // Update bulkMergeData if a radio is checked
                const checkedRadio = form.querySelector('.keep-radio:checked');
                if (checkedRadio) {
                    const formIndex = Array.from(mergeForms).indexOf(form);
                    if (formIndex !== -1) {
                        const keepId = parseInt(checkedRadio.value);
                        const allEmployeeIds = Array.from(form.querySelectorAll('.keep-radio')).map(radio => parseInt(radio.value));

                        bulkMergeData[formIndex].keep_id = keepId;
                        bulkMergeData[formIndex].merge_ids = allEmployeeIds.filter(id => id !== keepId);
                        bulkMergeData[formIndex].skipped = false;
                    }
                }
            } else {
                // Skip this group
                skippedGroups.add(groupIndex);
                duplicateGroup.classList.add('group-skipped');
                this.innerHTML = '<i class="fas fa-redo"></i> Gabungkan Kembali';

                // Disable form elements
                const formElements = form.querySelectorAll('input, button.merge-button');
                formElements.forEach(el => el.disabled = true);

                // Update bulkMergeData
                const formIndex = Array.from(mergeForms).indexOf(form);
                if (formIndex !== -1) {
                    bulkMergeData[formIndex].keep_id = null;
                    bulkMergeData[formIndex].merge_ids = [];
                    bulkMergeData[formIndex].skipped = true;
                }
            }

            // Update bulk merge button
            updateBulkMergeButton();
        });
    });

    // Handle select all checkbox
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const ignoreCheckboxes = document.querySelectorAll('.ignore-checkbox');
    const bulkIgnoreButton = document.getElementById('bulkIgnoreButton');
    const bulkIgnoreForm = document.getElementById('bulkIgnoreForm');
    const bulkIgnoreDataInput = document.getElementById('bulkIgnoreDataInput');

    // Array untuk menyimpan data yang akan diabaikan secara bulk
    let bulkIgnoreData = [];

    // Fungsi untuk mengupdate status tombol bulk ignore
    function updateBulkIgnoreButton() {
        const checkedCount = document.querySelectorAll('.ignore-checkbox:checked').length;

        if (checkedCount > 0) {
            bulkIgnoreButton.disabled = false;
            bulkIgnoreButton.innerHTML = `<i class="fas fa-ban"></i> Jangan Gabungkan ${checkedCount} Kelompok Terpilih`;
        } else {
            bulkIgnoreButton.disabled = true;
            bulkIgnoreButton.innerHTML = `<i class="fas fa-ban"></i> Jangan Gabungkan Data Terpilih`;
        }
    }

    // Handle select all checkbox
    selectAllCheckbox.addEventListener('change', function() {
        ignoreCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });

        updateBulkIgnoreButton();
    });

    // Handle individual checkboxes
    ignoreCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // Update select all checkbox
            if (!this.checked) {
                selectAllCheckbox.checked = false;
            } else {
                // Check if all checkboxes are checked
                const allChecked = Array.from(ignoreCheckboxes).every(cb => cb.checked);
                selectAllCheckbox.checked = allChecked;
            }

            updateBulkIgnoreButton();
        });
    });

    // Handle clickable headers
    const clickableHeaders = document.querySelectorAll('.clickable-header');
    clickableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const checkboxId = this.getAttribute('data-checkbox');
            const checkbox = document.getElementById(checkboxId);

            if (checkbox) {
                checkbox.checked = !checkbox.checked;

                // Trigger change event
                const event = new Event('change', { bubbles: true });
                checkbox.dispatchEvent(event);
            }
        });
    });

    // Bulk ignore button click handler
    bulkIgnoreButton.addEventListener('click', function() {
        const checkedCheckboxes = document.querySelectorAll('.ignore-checkbox:checked');

        if (checkedCheckboxes.length === 0) {
            alert('Tidak ada data yang dipilih untuk diabaikan secara massal');
            return;
        }

        // Prepare bulk ignore data
        bulkIgnoreData = [];

        checkedCheckboxes.forEach(checkbox => {
            const groupIndex = parseInt(checkbox.getAttribute('data-group'));
            const duplicateGroup = checkbox.closest('.duplicate-group');

            // Get group type and value
            let groupType, groupValue;
            const groupHeader = duplicateGroup.querySelector('.duplicate-group-header');
            const headerText = groupHeader.textContent.trim();

            if (headerText.includes('NIK:')) {
                groupType = 'nik';
                groupValue = headerText.match(/NIK:\s*([^\s]+)/)[1];
            } else {
                groupType = 'name';
                const nameMatch = headerText.match(/Nama:\s*([^-]+)\s*-\s*([^\(]+)/);
                if (nameMatch) {
                    groupValue = nameMatch[1].trim() + '|' + nameMatch[2].trim();
                } else {
                    // Fallback to getting data from form
                    const ignoreForm = duplicateGroup.querySelector('.ignore-form');
                    groupType = ignoreForm.querySelector('input[name="group_type"]').value;
                    groupValue = ignoreForm.querySelector('input[name="group_value"]').value;
                }
            }

            // Get employee IDs
            const ignoreForm = duplicateGroup.querySelector('.ignore-form');
            const employeeIdsInput = ignoreForm.querySelector('input[name="employee_ids"]');
            const employeeIds = employeeIdsInput.value.split(',').map(id => parseInt(id));

            bulkIgnoreData.push({
                group_index: groupIndex,
                group_type: groupType,
                group_value: groupValue,
                employee_ids: employeeIds
            });
        });

        // Set bulk ignore data input value
        bulkIgnoreDataInput.value = JSON.stringify(bulkIgnoreData);

        // Confirm before submitting
        showConfirmDialog(`Anda yakin ingin mengabaikan ${bulkIgnoreData.length} kelompok data duplikat? Data ini tidak akan muncul lagi di halaman kelola duplikat.`, () => {
            bulkIgnoreForm.submit();
        });
    });

    // Initialize buttons
    updateBulkIgnoreButton();
    updateBulkMergeButton();
});
</script>
</body>
</html>
