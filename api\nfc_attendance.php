<?php
// File: api/nfc_attendance.php
// Deskripsi: API untuk menerima data NFC dari smartphone dan meneruskannya ke sistem absensi

// Mengizinkan akses dari domain manapun (untuk pengembangan)
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// <PERSON>ka request adalah OPTIONS (preflight), kembalikan 200 OK
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Include file konfigurasi database
include '../config/config.php';

// Inisialisasi respons
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// Fungsi untuk mencatat log
function logNfcActivity($message, $data = []) {
    $log_file = '../logs/nfc_activity.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$message} " . (!empty($data) ? json_encode($data) : '') . PHP_EOL;
    
    // Pastikan direktori logs ada
    if (!is_dir('../logs')) {
        mkdir('../logs', 0755, true);
    }
    
    file_put_contents($log_file, $log_entry, FILE_APPEND);
}

// Proses request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Ambil data JSON dari request body
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);
    
    logNfcActivity("Received NFC data:", $data);
    
    // Validasi data
    if (isset($data['card_number']) && !empty($data['card_number'])) {
        $card_number = $data['card_number'];
        $training_id = isset($data['training_id']) ? $data['training_id'] : null;
        
        // Jika training_id tidak disediakan, coba ambil training aktif
        if (!$training_id) {
            $query_active_training = "SELECT id FROM offline_training WHERE status = 'Active' ORDER BY training_date DESC LIMIT 1";
            $result_active_training = $conn->query($query_active_training);
            
            if ($result_active_training && $result_active_training->num_rows > 0) {
                $training = $result_active_training->fetch_assoc();
                $training_id = $training['id'];
            } else {
                $response['message'] = "Tidak ada training aktif yang ditemukan";
                logNfcActivity("No active training found");
                echo json_encode($response);
                exit;
            }
        }
        
        // Cek apakah card number terdaftar
        $query_karyawan = "SELECT id, nik, nama, dept, jabatan FROM karyawan WHERE card_number = ?";
        $stmt_karyawan = $conn->prepare($query_karyawan);
        $stmt_karyawan->bind_param("s", $card_number);
        $stmt_karyawan->execute();
        $result_karyawan = $stmt_karyawan->get_result();
        
        if ($result_karyawan->num_rows > 0) {
            $karyawan = $result_karyawan->fetch_assoc();
            $karyawan_id = $karyawan['id'];
            $nik = $karyawan['nik'];
            $nama = $karyawan['nama'];
            
            // Cek apakah karyawan sudah terdaftar di absensi training ini
            $query_check = "SELECT id, check_in, check_out FROM offline_training_attendance
                           WHERE offline_training_id = ? AND (karyawan_id = ? OR card_number = ?)";
            $stmt_check = $conn->prepare($query_check);
            $stmt_check->bind_param("iis", $training_id, $karyawan_id, $card_number);
            $stmt_check->execute();
            $result_check = $stmt_check->get_result();
            
            if ($result_check->num_rows > 0) {
                // Karyawan sudah terdaftar, update check-in atau check-out
                $attendance = $result_check->fetch_assoc();
                $attendance_id = $attendance['id'];
                
                if ($attendance['check_in'] === null) {
                    // Belum check-in, lakukan check-in
                    $query_update = "UPDATE offline_training_attendance
                                    SET check_in = NOW(), status = 'hadir', updated_by = 1
                                    WHERE id = ?";
                    $stmt_update = $conn->prepare($query_update);
                    $stmt_update->bind_param("i", $attendance_id);
                    
                    if ($stmt_update->execute()) {
                        $response['success'] = true;
                        $response['message'] = "Check-in berhasil untuk {$nama} ({$nik})";
                        $response['data'] = [
                            'nik' => $karyawan['nik'],
                            'nama' => $karyawan['nama'],
                            'jabatan' => $karyawan['jabatan'] ?? '-',
                            'dept' => $karyawan['dept'] ?? '-',
                            'action' => 'check_in'
                        ];
                        logNfcActivity("Check-in success", $response['data']);
                    } else {
                        $response['message'] = "Gagal melakukan check-in: " . $conn->error;
                        logNfcActivity("Check-in failed", ['error' => $conn->error]);
                    }
                } elseif ($attendance['check_out'] === null) {
                    // Sudah check-in tapi belum check-out, lakukan check-out
                    $query_update = "UPDATE offline_training_attendance
                                    SET check_out = NOW(), updated_by = 1
                                    WHERE id = ?";
                    $stmt_update = $conn->prepare($query_update);
                    $stmt_update->bind_param("i", $attendance_id);
                    
                    if ($stmt_update->execute()) {
                        $response['success'] = true;
                        $response['message'] = "Check-out berhasil untuk {$nama} ({$nik})";
                        $response['data'] = [
                            'nik' => $karyawan['nik'],
                            'nama' => $karyawan['nama'],
                            'jabatan' => $karyawan['jabatan'] ?? '-',
                            'dept' => $karyawan['dept'] ?? '-',
                            'action' => 'check_out'
                        ];
                        logNfcActivity("Check-out success", $response['data']);
                    } else {
                        $response['message'] = "Gagal melakukan check-out: " . $conn->error;
                        logNfcActivity("Check-out failed", ['error' => $conn->error]);
                    }
                } else {
                    // Sudah check-in dan check-out
                    $response['success'] = true;
                    $response['message'] = "Karyawan {$nama} ({$nik}) sudah melakukan check-in dan check-out";
                    $response['data'] = [
                        'nik' => $karyawan['nik'],
                        'nama' => $karyawan['nama'],
                        'jabatan' => $karyawan['jabatan'] ?? '-',
                        'dept' => $karyawan['dept'] ?? '-',
                        'action' => 'already_complete'
                    ];
                    logNfcActivity("Already completed attendance", $response['data']);
                }
            } else {
                // Karyawan belum terdaftar, tambahkan ke absensi
                $query_insert = "INSERT INTO offline_training_attendance
                                (offline_training_id, karyawan_id, nik, card_number, nama, check_in, status, created_by)
                                VALUES (?, ?, ?, ?, ?, NOW(), 'hadir', 1)";
                $stmt_insert = $conn->prepare($query_insert);
                $stmt_insert->bind_param("iisss", $training_id, $karyawan_id, $nik, $card_number, $nama);
                
                if ($stmt_insert->execute()) {
                    $response['success'] = true;
                    $response['message'] = "Absensi berhasil ditambahkan untuk {$nama} ({$nik})";
                    $response['data'] = [
                        'nik' => $karyawan['nik'],
                        'nama' => $karyawan['nama'],
                        'jabatan' => $karyawan['jabatan'] ?? '-',
                        'dept' => $karyawan['dept'] ?? '-',
                        'action' => 'new_attendance'
                    ];
                    logNfcActivity("New attendance added", $response['data']);
                } else {
                    $response['message'] = "Gagal menambahkan absensi: " . $conn->error;
                    logNfcActivity("Failed to add attendance", ['error' => $conn->error]);
                }
            }
        } else {
            $response['message'] = "Kartu RFID tidak terdaftar";
            logNfcActivity("Unregistered RFID card", ['card_number' => $card_number]);
        }
    } else {
        $response['message'] = "Data tidak lengkap. Diperlukan card_number.";
        logNfcActivity("Incomplete data");
    }
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Endpoint untuk mendapatkan daftar training aktif
    if (isset($_GET['action']) && $_GET['action'] === 'get_trainings') {
        $query_trainings = "SELECT id, training_topic, training_date, training_time_start, training_time_end, location 
                          FROM offline_training 
                          WHERE status = 'Active' 
                          ORDER BY training_date DESC";
        $result_trainings = $conn->query($query_trainings);
        
        if ($result_trainings && $result_trainings->num_rows > 0) {
            $trainings = [];
            while ($row = $result_trainings->fetch_assoc()) {
                $trainings[] = [
                    'id' => $row['id'],
                    'topic' => $row['training_topic'],
                    'date' => date('d-m-Y', strtotime($row['training_date'])),
                    'time' => $row['training_time_start'] . ' - ' . $row['training_time_end'],
                    'location' => $row['location']
                ];
            }
            
            $response['success'] = true;
            $response['message'] = "Berhasil mendapatkan daftar training";
            $response['data'] = $trainings;
        } else {
            $response['message'] = "Tidak ada training aktif yang ditemukan";
        }
    } else {
        $response['message'] = "Endpoint tidak valid";
    }
} else {
    $response['message'] = "Metode request tidak didukung";
}

// Kembalikan respons
echo json_encode($response);
?>
