-- MySQL dump 10.13  Distrib 8.0.30, for Win64 (x86_64)
--
-- Host: localhost    Database: db_training
-- ------------------------------------------------------
-- Server version	8.0.30

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `ignored_duplicates`
--

DROP TABLE IF EXISTS `ignored_duplicates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ignored_duplicates` (
  `id` int NOT NULL AUTO_INCREMENT,
  `group_type` enum('nik','name') NOT NULL COMMENT 'Tipe pengelompokan: berdasarkan NIK atau nama',
  `group_value` varchar(255) NOT NULL COMMENT 'Nilai yang digunakan untuk pengelompokan (normalized_nik atau nama+dept)',
  `employee_ids` text NOT NULL COMMENT 'ID karyawan yang ada dalam kelompok ini',
  `ignored_by` int NOT NULL COMMENT 'User ID yang mengabaikan kelompok ini',
  `ignored_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Waktu kelompok ini diabaikan',
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_type` (`group_type`,`group_value`),
  KEY `idx_group_type_value` (`group_type`,`group_value`),
  KEY `idx_ignored_by` (`ignored_by`),
  KEY `idx_ignored_at` (`ignored_at`)
) ENGINE=InnoDB AUTO_INCREMENT=126 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ignored_duplicates`
--

LOCK TABLES `ignored_duplicates` WRITE;
/*!40000 ALTER TABLE `ignored_duplicates` DISABLE KEYS */;
/*!40000 ALTER TABLE `ignored_duplicates` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-23 22:13:51
