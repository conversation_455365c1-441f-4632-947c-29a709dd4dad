/**
 * CustomModal.js - Komponen modal kustom untuk menampilkan pesan
 * Mendukung berbagai jenis pesan: success, warning, error, info
 */

// <PERSON>ik<PERSON> apakah CustomModal sudah didefinisikan sebelumnya
if (typeof window.CustomModal === 'undefined') {
    window.CustomModal = {
        /**
         * Menampilkan pesan sukses
         * @param {string} message - Pesan yang akan ditampilkan
         * @param {string} title - Judul modal (opsional)
         */
        success: function(message, title = 'Sukses') {
            this._showModal(message, title, 'success');
        },
        
        /**
         * Menampilkan pesan peringatan
         * @param {string} message - Pesan yang akan ditampilkan
         * @param {string} title - Judul modal (opsional)
         */
        warning: function(message, title = 'Peringatan') {
            this._showModal(message, title, 'warning');
        },
        
        /**
         * Menampilkan pesan error
         * @param {string} message - <PERSON><PERSON> yang akan ditampilkan
         * @param {string} title - Judul modal (opsional)
         */
        error: function(message, title = 'Error') {
            this._showModal(message, title, 'error');
        },
        
        /**
         * Menampilkan pesan informasi
         * @param {string} message - Pesan yang akan ditampilkan
         * @param {string} title - Judul modal (opsional)
         */
        info: function(message, title = 'Informasi') {
            this._showModal(message, title, 'info');
        },
        
        /**
         * Menampilkan modal dengan pesan dan tipe yang ditentukan
         * @private
         * @param {string} message - Pesan yang akan ditampilkan
         * @param {string} title - Judul modal
         * @param {string} type - Tipe modal (success, warning, error, info)
         */
        _showModal: function(message, title, type) {
            // Hapus modal lama jika ada
            const existingModal = document.getElementById('customModal');
            if (existingModal) {
                document.body.removeChild(existingModal);
            }
            
            // Buat elemen modal
            const modalContainer = document.createElement('div');
            modalContainer.id = 'customModal';
            modalContainer.className = 'custom-modal';
            
            // Tentukan warna berdasarkan tipe
            let color = '#BF0000'; // Default merah
            let icon = '⚠️';
            
            switch (type) {
                case 'success':
                    color = '#28a745';
                    icon = '✅';
                    break;
                case 'warning':
                    color = '#ffc107';
                    icon = '⚠️';
                    break;
                case 'error':
                    color = '#BF0000';
                    icon = '❌';
                    break;
                case 'info':
                    color = '#17a2b8';
                    icon = 'ℹ️';
                    break;
            }
            
            // Buat konten HTML untuk modal
            modalContainer.innerHTML = `
                <div class="custom-modal-content" style="border-top: 5px solid ${color};">
                    <div class="custom-modal-header" style="color: ${color};">
                        <span class="custom-modal-icon">${icon}</span>
                        <h3>${title}</h3>
                        <span class="custom-modal-close">&times;</span>
                    </div>
                    <div class="custom-modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="custom-modal-footer">
                        <button class="custom-modal-button" style="background-color: ${color};">OK</button>
                    </div>
                </div>
            `;
            
            // Tambahkan modal ke body
            document.body.appendChild(modalContainer);
            
            // Tambahkan event listener untuk tombol close
            const closeButton = modalContainer.querySelector('.custom-modal-close');
            closeButton.addEventListener('click', function() {
                document.body.removeChild(modalContainer);
            });
            
            // Tambahkan event listener untuk tombol OK
            const okButton = modalContainer.querySelector('.custom-modal-button');
            okButton.addEventListener('click', function() {
                document.body.removeChild(modalContainer);
            });
            
            // Tambahkan event listener untuk klik di luar modal
            modalContainer.addEventListener('click', function(event) {
                if (event.target === modalContainer) {
                    document.body.removeChild(modalContainer);
                }
            });
            
            // Tambahkan event listener untuk tombol Escape
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape' && document.getElementById('customModal')) {
                    document.body.removeChild(modalContainer);
                }
            });
            
            // Focus pada tombol OK
            okButton.focus();
        },
        
        /**
         * Menampilkan modal konfirmasi dengan tombol Ya dan Tidak
         * @param {string} message - Pesan yang akan ditampilkan
         * @param {string} title - Judul modal (opsional)
         * @param {Object} options - Opsi tambahan (onConfirm, onCancel)
         */
        confirm: function(message, title = 'Konfirmasi', options = {}) {
            // Hapus modal lama jika ada
            const existingModal = document.getElementById('customModal');
            if (existingModal) {
                document.body.removeChild(existingModal);
            }
            
            // Buat elemen modal
            const modalContainer = document.createElement('div');
            modalContainer.id = 'customModal';
            modalContainer.className = 'custom-modal';
            
            // Buat konten HTML untuk modal
            modalContainer.innerHTML = `
                <div class="custom-modal-content" style="border-top: 5px solid #17a2b8;">
                    <div class="custom-modal-header" style="color: #17a2b8;">
                        <span class="custom-modal-icon">❓</span>
                        <h3>${title}</h3>
                        <span class="custom-modal-close">&times;</span>
                    </div>
                    <div class="custom-modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="custom-modal-footer">
                        <button class="custom-modal-button custom-modal-cancel" style="background-color: #6c757d;">Tidak</button>
                        <button class="custom-modal-button custom-modal-confirm" style="background-color: #17a2b8;">Ya</button>
                    </div>
                </div>
            `;
            
            // Tambahkan modal ke body
            document.body.appendChild(modalContainer);
            
            // Tambahkan event listener untuk tombol close
            const closeButton = modalContainer.querySelector('.custom-modal-close');
            closeButton.addEventListener('click', function() {
                document.body.removeChild(modalContainer);
                if (options.onCancel && typeof options.onCancel === 'function') {
                    options.onCancel();
                }
            });
            
            // Tambahkan event listener untuk tombol Tidak
            const cancelButton = modalContainer.querySelector('.custom-modal-cancel');
            cancelButton.addEventListener('click', function() {
                document.body.removeChild(modalContainer);
                if (options.onCancel && typeof options.onCancel === 'function') {
                    options.onCancel();
                }
            });
            
            // Tambahkan event listener untuk tombol Ya
            const confirmButton = modalContainer.querySelector('.custom-modal-confirm');
            confirmButton.addEventListener('click', function() {
                document.body.removeChild(modalContainer);
                if (options.onConfirm && typeof options.onConfirm === 'function') {
                    options.onConfirm();
                }
            });
            
            // Tambahkan event listener untuk klik di luar modal
            modalContainer.addEventListener('click', function(event) {
                if (event.target === modalContainer) {
                    document.body.removeChild(modalContainer);
                    if (options.onCancel && typeof options.onCancel === 'function') {
                        options.onCancel();
                    }
                }
            });
            
            // Tambahkan event listener untuk tombol Escape
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape' && document.getElementById('customModal')) {
                    document.body.removeChild(modalContainer);
                    if (options.onCancel && typeof options.onCancel === 'function') {
                        options.onCancel();
                    }
                }
            });
            
            // Focus pada tombol Ya
            confirmButton.focus();
        }
    };
}

// Gunakan objek CustomModal yang sudah ada di window
const CustomModal = window.CustomModal;