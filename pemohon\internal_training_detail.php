<?php
include 'security.php';
include '../config/config.php';

$user_id = $_SESSION['user_id'];
$training_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$training_id) {
    $_SESSION['error'] = "ID training tidak valid.";
    header("Location: dashboard.php");
    exit();
}

// Get training details
$query = "SELECT ot.*, 
                 dh.name as dept_head_name,
                 lnd.name as lnd_name,
                 u.name as requester_name,
                 k.nama as requester_nama,
                 k.dept as requester_dept,
                 k.bagian as requester_bagian,
                 k.jabatan as requester_jabatan
          FROM offline_training ot
          LEFT JOIN users dh ON ot.approved_by_dept_head = dh.id
          LEFT JOIN users lnd ON ot.approved_by_lnd = lnd.id
          LEFT JOIN users u ON ot.created_by = u.id
          LEFT JOIN karyawan k ON u.nik = k.nik
          WHERE ot.id = ? AND ot.created_by = ?";

$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $training_id, $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error'] = "Training tidak ditemukan atau Anda tidak memiliki akses.";
    header("Location: dashboard.php");
    exit();
}

$training = $result->fetch_assoc();
$stmt->close();
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    .detail-container {
        max-width: 1000px;
        margin: 20px auto;
        padding: 20px;
    }

    .detail-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 30px;
    }

    .detail-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }

    .card-header {
        background: #f8f9fa;
        padding: 20px;
        border-bottom: 1px solid #eee;
        font-weight: 600;
        color: #333;
    }

    .card-content {
        padding: 20px;
    }

    .detail-row {
        display: flex;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #f0f0f0;
    }

    .detail-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .detail-label {
        font-weight: 600;
        min-width: 180px;
        color: #555;
    }

    .detail-value {
        flex: 1;
        color: #333;
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .status-pending {
        background: #fff3cd;
        color: #856404;
    }

    .status-approved {
        background: #d4edda;
        color: #155724;
    }

    .status-rejected {
        background: #f8d7da;
        color: #721c24;
    }

    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #ddd;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 30px;
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: -37px;
        top: 25px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #ddd;
        border: 3px solid white;
        box-shadow: 0 0 0 3px #ddd;
    }

    .timeline-item.completed::before {
        background: #28a745;
        box-shadow: 0 0 0 3px #28a745;
    }

    .timeline-item.rejected::before {
        background: #dc3545;
        box-shadow: 0 0 0 3px #dc3545;
    }

    .timeline-item.pending::before {
        background: #ffc107;
        box-shadow: 0 0 0 3px #ffc107;
    }

    .timeline-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
    }

    .timeline-content {
        color: #666;
        line-height: 1.6;
    }

    .comments-section {
        background: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
        border-left: 4px solid #2196f3;
    }

    .back-button {
        background: #6c757d;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .back-button:hover {
        background: #5a6268;
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .detail-container {
            margin: 10px;
            padding: 10px;
        }
        
        .detail-row {
            flex-direction: column;
        }
        
        .detail-label {
            min-width: auto;
            margin-bottom: 5px;
        }
        
        .timeline {
            padding-left: 20px;
        }
        
        .timeline-item::before {
            left: -27px;
        }
    }
</style>

<body>
    <?php include '../config/navbara.php'; ?>
    <div class="jarak-form"></div>

    <div class="detail-container">
        <div class="detail-header">
            <h1><i class="fas fa-building"></i> Detail Training Internal</h1>
            <p>ID: #<?= $training['id'] ?> | <?= htmlspecialchars($training['training_topic']) ?></p>
        </div>

        <!-- Training Information -->
        <div class="detail-card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> Informasi Training
            </div>
            <div class="card-content">
                <div class="detail-row">
                    <div class="detail-label">Nama Training:</div>
                    <div class="detail-value"><?= htmlspecialchars($training['training_topic']) ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Tanggal Mulai:</div>
                    <div class="detail-value">
                        <?= !empty($training['start_date']) ? date('d F Y', strtotime($training['start_date'])) : 'Belum ditentukan' ?>
                    </div>
                </div>
                <?php if (!empty($training['end_date']) && $training['end_date'] !== $training['start_date']): ?>
                <div class="detail-row">
                    <div class="detail-label">Tanggal Selesai:</div>
                    <div class="detail-value"><?= date('d F Y', strtotime($training['end_date'])) ?></div>
                </div>
                <?php endif; ?>
                <?php if (!empty($training['training_time_start'])): ?>
                <div class="detail-row">
                    <div class="detail-label">Waktu:</div>
                    <div class="detail-value">
                        <?= date('H:i', strtotime($training['training_time_start'])) ?>
                        <?php if (!empty($training['training_time_end'])): ?>
                            - <?= date('H:i', strtotime($training['training_time_end'])) ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                <?php if (!empty($training['location'])): ?>
                <div class="detail-row">
                    <div class="detail-label">Lokasi:</div>
                    <div class="detail-value"><?= htmlspecialchars($training['location']) ?></div>
                </div>
                <?php endif; ?>
                <?php if (!empty($training['max_participants'])): ?>
                <div class="detail-row">
                    <div class="detail-label">Estimasi Peserta:</div>
                    <div class="detail-value"><?= htmlspecialchars($training['max_participants']) ?> orang</div>
                </div>
                <?php endif; ?>
                <div class="detail-row">
                    <div class="detail-label">Status Saat Ini:</div>
                    <div class="detail-value">
                        <?php
                        $status_class = '';
                        $status_text = '';
                        $status_icon = '';
                        
                        if ($training['status'] === 'Pending Dept Head Approval') {
                            $status_class = 'status-pending';
                            $status_text = 'Menunggu Persetujuan Department Head';
                            $status_icon = 'fas fa-clock';
                        } elseif ($training['status'] === 'Pending L&D Approval') {
                            $status_class = 'status-pending';
                            $status_text = 'Menunggu Persetujuan L&D';
                            $status_icon = 'fas fa-clock';
                        } elseif ($training['status'] === 'Approved') {
                            $status_class = 'status-approved';
                            $status_text = 'Disetujui - Siap Dilaksanakan';
                            $status_icon = 'fas fa-check-circle';
                        } elseif (strpos($training['status'], 'Rejected') !== false) {
                            $status_class = 'status-rejected';
                            $status_text = 'Ditolak';
                            $status_icon = 'fas fa-times-circle';
                        } else {
                            $status_class = 'status-pending';
                            $status_text = $training['status'];
                            $status_icon = 'fas fa-clock';
                        }
                        ?>
                        <span class="status-badge <?= $status_class ?>">
                            <i class="<?= $status_icon ?>"></i> <?= $status_text ?>
                        </span>
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Diajukan Pada:</div>
                    <div class="detail-value"><?= date('d F Y H:i', strtotime($training['created_at'])) ?></div>
                </div>
            </div>
        </div>

        <!-- Requester Information -->
        <div class="detail-card">
            <div class="card-header">
                <i class="fas fa-user"></i> Informasi Pemohon
            </div>
            <div class="card-content">
                <div class="detail-row">
                    <div class="detail-label">Nama:</div>
                    <div class="detail-value"><?= htmlspecialchars($training['requester_nama'] ?? $training['requester_name']) ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Departemen:</div>
                    <div class="detail-value"><?= htmlspecialchars($training['requester_dept']) ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Bagian:</div>
                    <div class="detail-value"><?= htmlspecialchars($training['requester_bagian']) ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Jabatan:</div>
                    <div class="detail-value"><?= htmlspecialchars($training['requester_jabatan']) ?></div>
                </div>
            </div>
        </div>

        <!-- Training Description -->
        <div class="detail-card">
            <div class="card-header">
                <i class="fas fa-file-alt"></i> Deskripsi Training
            </div>
            <div class="card-content">
                <div style="white-space: pre-line; line-height: 1.6;">
                    <?= htmlspecialchars($training['training_description']) ?>
                </div>
            </div>
        </div>

        <!-- Approval Timeline -->
        <div class="detail-card">
            <div class="card-header">
                <i class="fas fa-history"></i> Timeline Approval
            </div>
            <div class="card-content">
                <div class="timeline">
                    <!-- Submitted -->
                    <div class="timeline-item completed">
                        <div class="timeline-title">
                            <i class="fas fa-paper-plane"></i> Pengajuan Dikirim
                        </div>
                        <div class="timeline-content">
                            Training internal berhasil diajukan pada <?= date('d F Y H:i', strtotime($training['created_at'])) ?>
                        </div>
                    </div>

                    <!-- Department Head Approval -->
                    <div class="timeline-item <?= $training['approved_dept_head'] === 'Approved' ? 'completed' : ($training['approved_dept_head'] === 'Rejected' ? 'rejected' : ($training['current_approver'] === 'dept_head' ? 'pending' : '')) ?>">
                        <div class="timeline-title">
                            <i class="fas fa-user-tie"></i> Persetujuan Department Head
                        </div>
                        <div class="timeline-content">
                            <?php if ($training['approved_dept_head'] === 'Approved'): ?>
                                ✅ Disetujui oleh <?= htmlspecialchars($training['dept_head_name']) ?>
                                <?php if (!empty($training['approved_at_dept_head'])): ?>
                                    pada <?= date('d F Y H:i', strtotime($training['approved_at_dept_head'])) ?>
                                <?php endif; ?>
                                <?php if (!empty($training['comments_dept_head'])): ?>
                                    <div class="comments-section">
                                        <strong>Komentar:</strong><br>
                                        <?= htmlspecialchars($training['comments_dept_head']) ?>
                                    </div>
                                <?php endif; ?>
                            <?php elseif ($training['approved_dept_head'] === 'Rejected'): ?>
                                ❌ Ditolak oleh <?= htmlspecialchars($training['dept_head_name']) ?>
                                <?php if (!empty($training['approved_at_dept_head'])): ?>
                                    pada <?= date('d F Y H:i', strtotime($training['approved_at_dept_head'])) ?>
                                <?php endif; ?>
                                <?php if (!empty($training['comments_dept_head'])): ?>
                                    <div class="comments-section">
                                        <strong>Alasan Penolakan:</strong><br>
                                        <?= htmlspecialchars($training['comments_dept_head']) ?>
                                    </div>
                                <?php endif; ?>
                            <?php elseif ($training['current_approver'] === 'dept_head'): ?>
                                ⏳ Menunggu persetujuan dari Department Head
                            <?php else: ?>
                                ⏸️ Belum diproses
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- L&D Approval -->
                    <div class="timeline-item <?= $training['approved_lnd'] === 'Approved' ? 'completed' : ($training['approved_lnd'] === 'Rejected' ? 'rejected' : ($training['current_approver'] === 'lnd' ? 'pending' : '')) ?>">
                        <div class="timeline-title">
                            <i class="fas fa-graduation-cap"></i> Persetujuan Learning & Development
                        </div>
                        <div class="timeline-content">
                            <?php if ($training['approved_lnd'] === 'Approved'): ?>
                                ✅ Disetujui oleh <?= htmlspecialchars($training['lnd_name']) ?>
                                <?php if (!empty($training['approved_at_lnd'])): ?>
                                    pada <?= date('d F Y H:i', strtotime($training['approved_at_lnd'])) ?>
                                <?php endif; ?>
                                <?php if (!empty($training['comments_lnd'])): ?>
                                    <div class="comments-section">
                                        <strong>Komentar L&D:</strong><br>
                                        <?= htmlspecialchars($training['comments_lnd']) ?>
                                    </div>
                                <?php endif; ?>
                            <?php elseif ($training['approved_lnd'] === 'Rejected'): ?>
                                ❌ Ditolak oleh <?= htmlspecialchars($training['lnd_name']) ?>
                                <?php if (!empty($training['approved_at_lnd'])): ?>
                                    pada <?= date('d F Y H:i', strtotime($training['approved_at_lnd'])) ?>
                                <?php endif; ?>
                                <?php if (!empty($training['comments_lnd'])): ?>
                                    <div class="comments-section">
                                        <strong>Alasan Penolakan:</strong><br>
                                        <?= htmlspecialchars($training['comments_lnd']) ?>
                                    </div>
                                <?php endif; ?>
                            <?php elseif ($training['current_approver'] === 'lnd'): ?>
                                ⏳ Menunggu persetujuan dari Learning & Development
                            <?php else: ?>
                                ⏸️ Belum diproses
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Final Status -->
                    <div class="timeline-item <?= $training['status'] === 'Approved' ? 'completed' : (strpos($training['status'], 'Rejected') !== false ? 'rejected' : '') ?>">
                        <div class="timeline-title">
                            <i class="fas <?= $training['status'] === 'Approved' ? 'fa-flag-checkered' : (strpos($training['status'], 'Rejected') !== false ? 'fa-ban' : 'fa-clock') ?>"></i> 
                            Status Final
                        </div>
                        <div class="timeline-content">
                            <?php if ($training['status'] === 'Approved'): ?>
                                🎉 Training internal telah disetujui dan siap untuk dilaksanakan!
                            <?php elseif (strpos($training['status'], 'Rejected') !== false): ?>
                                ❌ Pengajuan training internal ditolak
                            <?php else: ?>
                                ⏳ Masih dalam proses persetujuan
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="dashboard.php" class="back-button">
                <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
            </a>
        </div>
    </div>

    <footer>
        <?php include '../config/footer.php'; ?>
    </footer>
</body>
</html>
