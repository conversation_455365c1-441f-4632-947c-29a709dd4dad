@echo off
REM === Set variabel ===
set "MYSQLDUMP_PATH=C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin\mysqldump.exe"
set "USER=root"
set "PASSWORD="   REM ← Kosongkan jika MySQL tanpa password
set "DATABASE=db_training"
set "OUTPUT=setupdb.sql"

REM === Ekspor struktur saja (tanpa data) ===
if defined PASSWORD (
    "%MYSQLDUMP_PATH%" -u %USER% -p%PASSWORD% --no-data %DATABASE% > %OUTPUT%
) else (
    "%MYSQLDUMP_PATH%" -u %USER% --no-data %DATABASE% > %OUTPUT%
)

echo Struktur database %DATABASE% berhasil diekspor ke %OUTPUT%
pause
