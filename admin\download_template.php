<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Load library PhpSpreadsheet
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

// Buat spreadsheet baru
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('User Template');

// Set header
$headers = ['nama', 'nik', 'email', 'role_id', 'dept', 'bagian', 'jabatan'];
$column = 'A';
foreach ($headers as $header) {
    $sheet->setCellValue($column . '1', $header);
    $column++;
}

// Format header
$headerStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FFFFFF'],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'BF0000'],
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['rgb' => '000000'],
        ],
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
];
$sheet->getStyle('A1:G1')->applyFromArray($headerStyle);

// Tambahkan contoh data
$examples = [
    ['John Doe', '123456', '<EMAIL>', 1, 'IT', 'Development', 'Developer'],
    ['Jane Smith', '654321', '<EMAIL>', 2, 'HR', 'Recruitment', 'Manager'],
];

$row = 2;
foreach ($examples as $example) {
    $column = 'A';
    foreach ($example as $value) {
        $sheet->setCellValue($column . $row, $value);
        $column++;
    }
    $row++;
}

// Format contoh data
$dataStyle = [
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['rgb' => '000000'],
        ],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'F9F9F9'],
    ],
];
$sheet->getStyle('A2:G3')->applyFromArray($dataStyle);

// Tambahkan komentar pada header
$sheet->getComment('A1')->getText()->createTextRun('Nama lengkap pengguna');
$sheet->getComment('B1')->getText()->createTextRun('NIK karyawan (harus unik)');
$sheet->getComment('C1')->getText()->createTextRun('Email pengguna (harus unik)');
$sheet->getComment('D1')->getText()->createTextRun('ID Role: 1=Pemohon, 2=Dept Head, 3=LnD, 4=FM, 5=Direktur, 6=HRGA, 99=Admin');
$sheet->getComment('E1')->getText()->createTextRun('Departemen pengguna');
$sheet->getComment('F1')->getText()->createTextRun('Bagian/sub-departemen pengguna');
$sheet->getComment('G1')->getText()->createTextRun('Jabatan pengguna');

// Auto-size kolom
foreach (range('A', 'G') as $column) {
    $sheet->getColumnDimension($column)->setAutoSize(true);
}

// Tambahkan sheet kedua dengan informasi role
$sheet2 = $spreadsheet->createSheet();
$sheet2->setTitle('Roles');

// Set header untuk sheet roles
$sheet2->setCellValue('A1', 'role_id');
$sheet2->setCellValue('B1', 'role_name');
$sheet2->setCellValue('C1', 'description');
$sheet2->getStyle('A1:C1')->applyFromArray($headerStyle);

// Query untuk mengambil daftar role
$query = "SELECT * FROM roles ORDER BY id";
$result = $conn->query($query);

$row = 2;
while ($role = $result->fetch_assoc()) {
    $sheet2->setCellValue('A' . $row, $role['id']);
    $sheet2->setCellValue('B' . $row, $role['role_name']);
    $sheet2->setCellValue('C' . $row, $role['description']);
    $row++;
}

// Format data role
$sheet2->getStyle('A2:C' . ($row - 1))->applyFromArray($dataStyle);

// Auto-size kolom sheet roles
foreach (range('A', 'C') as $column) {
    $sheet2->getColumnDimension($column)->setAutoSize(true);
}

// Tambahkan sheet ketiga dengan informasi departemen
$sheet3 = $spreadsheet->createSheet();
$sheet3->setTitle('Departments');

// Set header untuk sheet departments
$sheet3->setCellValue('A1', 'dept');
$sheet3->getStyle('A1')->applyFromArray($headerStyle);

// Query untuk mengambil daftar departemen
$query = "SELECT DISTINCT dept FROM karyawan ORDER BY dept";
$result = $conn->query($query);

$row = 2;
while ($dept = $result->fetch_assoc()) {
    $sheet3->setCellValue('A' . $row, $dept['dept']);
    $row++;
}

// Format data departemen
$sheet3->getStyle('A2:A' . ($row - 1))->applyFromArray($dataStyle);

// Auto-size kolom sheet departments
$sheet3->getColumnDimension('A')->setAutoSize(true);

// Kembali ke sheet pertama
$spreadsheet->setActiveSheetIndex(0);

// Set header untuk download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="user_template.xlsx"');
header('Cache-Control: max-age=0');

// Simpan file ke output
$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit;
?>
