<?php
// Include database connection
include '../config/config.php';

// Set header to return JSON
header('Content-Type: application/json');

// Check if email parameter exists
if (isset($_GET['email'])) {
    $email = trim($_GET['email']);
    
    // Prepare statement to check if email exists
    $query = "SELECT COUNT(*) as count FROM users WHERE email = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    // Return JSON response
    echo json_encode([
        'exists' => ($row['count'] > 0),
        'email' => $email
    ]);
} else {
    // Return error if email parameter is missing
    echo json_encode([
        'error' => 'Email parameter is required',
        'exists' => false
    ]);
}
