<?php
/**
 * Import Quiz Questions from Excel
 * This page allows admins to import quiz questions from an Excel file
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Check if quiz_id is provided
$quiz_id = isset($_GET['quiz_id']) ? intval($_GET['quiz_id']) : 0;

if ($quiz_id <= 0) {
    header('Location: manage_quizzes.php');
    exit();
}

// Get quiz information
$quiz_query = "SELECT q.*, c.title as class_title, t.training_topic
              FROM training_quizzes q
              JOIN training_classes c ON q.class_id = c.id
              JOIN training_submissions t ON c.training_id = t.id
              WHERE q.id = ?";
$stmt = $conn->prepare($quiz_query);
$stmt->bind_param("i", $quiz_id);
$stmt->execute();
$result = $stmt->get_result();
$quiz = $result->fetch_assoc();
$stmt->close();

if (!$quiz) {
    header('Location: manage_quizzes.php');
    exit();
}

// Initialize variables
$success_message = '';
$error_message = '';
$import_results = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['import_questions'])) {
    // Check if file was uploaded
    if (!isset($_FILES['excel_file']) || $_FILES['excel_file']['error'] != UPLOAD_ERR_OK) {
        $error_message = "Terjadi kesalahan saat mengunggah file. Silakan coba lagi.";
    } else {
        // Check if PhpSpreadsheet is installed
        if (!file_exists('../vendor/autoload.php')) {
            $error_message = "PhpSpreadsheet tidak ditemukan. Silakan jalankan 'composer require phpoffice/phpspreadsheet' di direktori root.";
        } else {
            require '../vendor/autoload.php';

            try {
                // Log start of import process
                error_log("Starting Excel import process for quiz_id: $quiz_id");

                // Load the Excel file
                $inputFileName = $_FILES['excel_file']['tmp_name'];
                $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($inputFileName);

                // Explicitly select the "Pertanyaan" sheet
                if (!$spreadsheet->sheetNameExists('Pertanyaan')) {
                    throw new Exception("Sheet 'Pertanyaan' tidak ditemukan dalam file Excel.");
                }
                $worksheet = $spreadsheet->getSheetByName('Pertanyaan');

                // Get the highest row with data
                $highestRow = $worksheet->getHighestDataRow();
                if ($highestRow <= 1) {
                    throw new Exception("Sheet 'Pertanyaan' tidak berisi data pertanyaan (hanya header).");
                }

                // Log information for debugging
                error_log("Processing 'Pertanyaan' sheet with $highestRow rows");

                // Initialize counters
                $total_questions = 0;
                $imported_questions = 0;
                $skipped_questions = 0;
                $errors = [];

                // Start transaction
                $conn->begin_transaction();

                // Process each row (skip header row)
                for ($row = 2; $row <= $highestRow; $row++) {
                    try {
                        // Function to safely get cell value
                        $getCellValue = function($worksheet, $column, $row) {
                            try {
                                $value = $worksheet->getCell($column . $row)->getValue();
                                return is_null($value) ? '' : trim((string)$value);
                            } catch (Exception $e) {
                                error_log("Error reading cell $column$row: " . $e->getMessage());
                                return '';
                            }
                        };

                        // Get data from row
                        $question_text = $getCellValue($worksheet, 'A', $row);
                        $question_type = strtolower($getCellValue($worksheet, 'B', $row)); // Case-insensitive
                        $points = intval($getCellValue($worksheet, 'C', $row));
                        $option_a = $getCellValue($worksheet, 'D', $row);
                        $option_b = $getCellValue($worksheet, 'E', $row);
                        $option_c = $getCellValue($worksheet, 'F', $row);
                        $option_d = $getCellValue($worksheet, 'G', $row);
                        $option_e = $getCellValue($worksheet, 'H', $row);
                        $correct_answer = strtoupper($getCellValue($worksheet, 'I', $row)); // Case-insensitive

                        // Log successful read
                        error_log("Read row $row: $question_text");

                        // Skip empty rows
                        if (empty($question_text)) {
                            continue;
                        }

                        $total_questions++;

                        // Validate data
                        $valid = true;
                        $error_msg = '';

                        // Check question type
                        if (!in_array($question_type, ['multiple_choice', 'true_false'])) {
                            $valid = false;
                            $error_msg = "Tipe pertanyaan tidak valid. Harus 'multiple_choice' atau 'true_false'.";
                        }

                        // Check points
                        if ($points <= 0) {
                            $valid = false;
                            $error_msg = "Poin harus lebih dari 0.";
                        }

                        // Check options
                        if ($question_type == 'multiple_choice') {
                            if (empty($option_a) || empty($option_b)) {
                                $valid = false;
                                $error_msg = "Pertanyaan pilihan ganda harus memiliki minimal 2 opsi (A dan B).";
                            }
                        } elseif ($question_type == 'true_false') {
                            if (strtolower($option_a) != 'benar' || strtolower($option_b) != 'salah') {
                                $valid = false;
                                $error_msg = "Pertanyaan benar/salah harus memiliki opsi A='Benar' dan opsi B='Salah'.";
                            }
                        }

                        // Check correct answer
                        if (!in_array($correct_answer, ['A', 'B', 'C', 'D', 'E'])) {
                            $valid = false;
                            $error_msg = "Jawaban benar harus A, B, C, D, atau E.";
                        }

                        // Check if correct answer points to an empty option
                        if (($correct_answer == 'C' && empty($option_c)) ||
                            ($correct_answer == 'D' && empty($option_d)) ||
                            ($correct_answer == 'E' && empty($option_e))) {
                            $valid = false;
                            $error_msg = "Jawaban benar menunjuk ke opsi yang kosong.";
                        }

                        if (!$valid) {
                            $skipped_questions++;
                            $errors[] = "Baris $row: $error_msg";
                            error_log("Validation failed for row $row: $error_msg");
                            continue;
                        }

                        // Insert question
                        $insert_question_query = "INSERT INTO training_questions (quiz_id, question_text, question_type, points, order_number)
                                                VALUES (?, ?, ?, ?, ?)";
                        $stmt = $conn->prepare($insert_question_query);
                        $order_number = $row - 1; // Use row number as order
                        $stmt->bind_param("issii", $quiz_id, $question_text, $question_type, $points, $order_number);

                        if ($stmt->execute()) {
                            $question_id = $stmt->insert_id;
                            $stmt->close();

                            // Insert options
                            $options = [
                                [$option_a, $correct_answer == 'A' ? 1 : 0, 0], // A = order 0
                                [$option_b, $correct_answer == 'B' ? 1 : 0, 1]  // B = order 1
                            ];

                            if (!empty($option_c)) {
                                $options[] = [$option_c, $correct_answer == 'C' ? 1 : 0, 2]; // C = order 2
                            }
                            if (!empty($option_d)) {
                                $options[] = [$option_d, $correct_answer == 'D' ? 1 : 0, 3]; // D = order 3
                            }
                            if (!empty($option_e)) {
                                $options[] = [$option_e, $correct_answer == 'E' ? 1 : 0, 4]; // E = order 4
                            }

                            $insert_option_query = "INSERT INTO training_question_options (question_id, option_text, is_correct, order_number)
                                                  VALUES (?, ?, ?, ?)";
                            $stmt = $conn->prepare($insert_option_query);

                            foreach ($options as $option) {
                                $stmt->bind_param("isii", $question_id, $option[0], $option[1], $option[2]);
                                if (!$stmt->execute()) {
                                    throw new Exception("Gagal menyimpan opsi untuk pertanyaan di baris $row.");
                                }
                            }
                            $stmt->close();
                            $imported_questions++;
                        } else {
                            throw new Exception("Gagal menyimpan pertanyaan di baris $row.");
                        }
                    } catch (Exception $e) {
                        $skipped_questions++;
                        $errors[] = "Baris $row: " . $e->getMessage();
                        error_log("Error processing row $row: " . $e->getMessage());
                        continue;
                    }
                }

                // Commit or rollback transaction
                if ($imported_questions > 0) {
                    $conn->commit();
                    $success_message = "Berhasil mengimpor $imported_questions dari $total_questions pertanyaan.";
                    if ($skipped_questions > 0) {
                        $success_message .= " $skipped_questions pertanyaan dilewati karena error.";
                    }
                } else {
                    $conn->rollback();
                    $error_message = "Tidak ada pertanyaan yang berhasil diimpor. Periksa format file Excel atau data yang diisi.";
                }

                $import_results = [
                    'total' => $total_questions,
                    'imported' => $imported_questions,
                    'skipped' => $skipped_questions,
                    'errors' => $errors
                ];

            } catch (Exception $e) {
                $conn->rollback();
                $error_message = "Terjadi kesalahan saat memproses file: " . $e->getMessage();
                error_log("Import error: " . $e->getMessage());
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .import-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .import-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .import-steps {
        counter-reset: step-counter;
        margin-bottom: 20px;
    }

    .import-step {
        position: relative;
        padding-left: 40px;
        margin-bottom: 15px;
    }

    .import-step:before {
        content: counter(step-counter);
        counter-increment: step-counter;
        position: absolute;
        left: 0;
        top: 0;
        width: 30px;
        height: 30px;
        background-color: #4472C4;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .result-summary {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .error-list {
        max-height: 200px;
        overflow-y: auto;
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="import-container">
        <div class="welcome-section">
            <h1><i class="fas fa-file-import"></i> Import Pertanyaan Kuis</h1>
            <p class="text-white">Import pertanyaan kuis dari file Excel untuk <?= htmlspecialchars($quiz['title']) ?></p>
            <a href="edit_quiz.php?id=<?= $quiz_id ?>" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Kembali ke Edit Kuis
            </a>
        </div>

        <?php if (!empty($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= htmlspecialchars($success_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= htmlspecialchars($error_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="import-card">
            <h3>Panduan Import</h3>

            <div class="import-steps">
                <div class="import-step">
                    <h5>Download Template Excel</h5>
                    <p>Download template Excel yang sudah disediakan. Template ini berisi contoh dan instruksi pengisian di sheet 'Panduan'.</p>
                    <a href="create_quiz_template.php" class="btn btn-primary">
                        <i class="fas fa-download"></i> Download Template
                    </a>
                </div>

                <div class="import-step">
                    <h5>Isi Template</h5>
                    <p>Isi sheet 'Pertanyaan' dengan pertanyaan kuis Anda sesuai instruksi di sheet 'Panduan'.</p>
                    <ul>
                        <li>Kolom A: <strong>Pertanyaan</strong> - Teks pertanyaan</li>
                        <li>Kolom B: <strong>Tipe Pertanyaan</strong> - Pilih 'multiple_choice' atau 'true_false'</li>
                        <li>Kolom C: <strong>Poin</strong> - Nilai poin (angka lebih dari 0)</li>
                        <li>Kolom D-H: <strong>Opsi A-E</strong> - Opsi jawaban (minimal A dan B untuk pilihan ganda; A='Benar', B='Salah' untuk benar/salah)</li>
                        <li>Kolom I: <strong>Jawaban Benar</strong> - Huruf opsi yang benar (A, B, C, D, atau E)</li>
                    </ul>
                </div>

                <div class="import-step">
                    <h5>Upload File Excel</h5>
                    <p>Upload file Excel yang sudah diisi dengan pertanyaan kuis.</p>

                    <form method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="excel_file" class="form-label">File Excel</label>
                            <input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xlsx, .xls" required>
                            <div class="form-text">Format yang didukung: .xlsx, .xls</div>
                        </div>

                        <button type="submit" name="import_questions" class="btn btn-success">
                            <i class="fas fa-file-import"></i> Import Pertanyaan
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <?php if (!empty($import_results)): ?>
            <div class="import-card">
                <h3>Hasil Import</h3>

                <div class="result-summary">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <h4><?= $import_results['total'] ?></h4>
                            <p>Total Pertanyaan</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <h4 class="text-success"><?= $import_results['imported'] ?></h4>
                            <p>Berhasil Diimpor</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <h4 class="text-danger"><?= $import_results['skipped'] ?></h4>
                            <p>Dilewati (Error)</p>
                        </div>
                    </div>
                </div>

                <?php if (!empty($import_results['errors'])): ?>
                    <h5>Error yang Ditemukan:</h5>
                    <div class="error-list">
                        <ul>
                            <?php foreach ($import_results['errors'] as $error): ?>
                                <li><?= htmlspecialchars($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    // Auto-dismiss alerts after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>  