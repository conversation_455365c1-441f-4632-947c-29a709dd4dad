<?php
session_start();
require_once '../includes/auth_check.php'; // Ensure user is logged in and is an admin
require_once '../config/config.php'; // Database connection and configuration
require_once 'record_batch_employee_history.php'; // Include batch history functions
require_once '../config/activity_logger.php'; // Include the activity logger

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Akses ditolak. Anda tidak memiliki izin untuk melakukan tindakan ini.']);
    exit;
}

// Validate input
if (!isset($_POST['batch_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'ID batch tidak diberikan.']);
    exit;
}

$batchId = intval($_POST['batch_id']);
$adminUsername = $_SESSION['name'] ?? 'Admin Tidak Dikenal'; // User performing the deletion

// Fetch the batch record to log details before deletion
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get batch details
    $batch = getBatchHistoryDetails($batchId);
    
    if (!$batch) {
        throw new Exception("Data batch tidak ditemukan.");
    }
    
    // Begin Transaction
    $pdo->beginTransaction();
    
    // Delete the batch record
    $deleteSuccess = deleteBatchHistory($batchId);
    
    if (!$deleteSuccess) {
        throw new Exception("Gagal menghapus riwayat batch.");
    }
    
    // Log the deletion action
    $userId = $_SESSION['user_id'] ?? 0;
    $logMessage = "Menghapus riwayat batch ID: {$batchId} (Aksi: {$batch['action_type']}) oleh {$adminUsername}.";
    $details = ['message' => $logMessage];
    log_activity($userId, 'Hapus Riwayat Batch', 'Riwayat Batch Karyawan', $details);
    
    // Commit Transaction
    $pdo->commit();
    
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'message' => 'Riwayat batch berhasil dihapus.']);
    
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    // Log the database error
    error_log("Database Error during batch history deletion (Batch ID: {$batchId}): " . $e->getMessage());
    $dbErrorMsg = "Kesalahan database saat menghapus riwayat batch ID {$batchId}: " . $e->getMessage();
    $userId = $_SESSION['user_id'] ?? 0;
    $details = ['error' => $dbErrorMsg];
    log_activity($userId, 'Hapus Riwayat Batch Gagal', 'Kesalahan Hapus Riwayat Batch', $details);
    
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Kesalahan database saat menghapus riwayat batch: ' . $e->getMessage()]);
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    // Log the general error
    error_log("General Error during batch history deletion (Batch ID: {$batchId}): " . $e->getMessage());
    $generalErrorMsg = "Kesalahan saat menghapus riwayat batch ID {$batchId}: " . $e->getMessage();
    $userId = $_SESSION['user_id'] ?? 0;
    $details = ['error' => $generalErrorMsg];
    log_activity($userId, 'Hapus Riwayat Batch Gagal', 'Kesalahan Hapus Riwayat Batch', $details);
    
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Gagal menghapus riwayat batch: ' . $e->getMessage()]);
}

exit;
