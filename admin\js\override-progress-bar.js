/**
 * Override lengkap untuk fungsi loadEmployeesLazy
 * File ini mengganti fungsi loadEmployeesLazy asli dengan implementasi yang memperbaiki masalah progress bar
 */

// Tunggu hingga DOM selesai dimuat
document.addEventListener('DOMContentLoaded', function() {
    console.log('Override progress bar loaded');
    
    // Fungsi untuk mengganti fungsi loadEmployeesLazy asli
    function overrideLoadEmployeesLazy() {
        // Cari fungsi loadEmployeesLazy asli
        if (typeof window.loadEmployeesLazy === 'function') {
            console.log('Found loadEmployeesLazy function, applying complete override');
            
            // Ganti dengan fungsi yang telah dimodifikasi sepenuhnya
            window.loadEmployeesLazy = async function(query, selectedNIKsParam, columnFiltersParam) {
                console.log('Using completely overridden loadEmployeesLazy function');
                
                const startTime = performance.now();

                // Tampilkan pesan loading khusus untuk data besar
                tableContainer.innerHTML = `
                    <div class="loading-message">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Memuat data dalam jumlah besar (${entriesPerPage} entri)...</p>
                        <p class="text-muted">Ini mungkin membutuhkan waktu beberapa saat</p>
                        <div class="progress mt-3" style="height: 20px;">
                            <div id="loadingProgressBar" class="progress-bar"
                                 role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                        <p id="loadingStatus" class="mt-2">Mempersiapkan data...</p>
                    </div>
                `;
                
                // Dapatkan referensi ke elemen progress bar dan status
                const progressBar = document.getElementById('loadingProgressBar');
                const loadingStatus = document.getElementById('loadingStatus');
                
                // Variabel untuk menyimpan semua data karyawan
                let allEmployees = [];
                let offset = 0;
                const chunkSize = 500; // Ukuran chunk yang lebih kecil untuk mengurangi beban server
                let totalRecords = 0;
                let loadedRecords = 0;
                
                // Buat URL dasar
                const baseUrl = `get_employees_lazy.php?search=${encodeURIComponent(query)}
                    &selected_niks=${selectedNIKsParam}
                    &column_filters=${columnFiltersParam}
                    &sort_column=${sortColumn}
                    &sort_direction=${sortDirection}
                    &limit=${chunkSize}`;
                
                // Bersihkan URL dari whitespace
                const cleanBaseUrl = baseUrl.replace(/\s+/g, '');
                
                // Ambil chunk pertama untuk mendapatkan total records
                const firstChunkUrl = `${cleanBaseUrl}&offset=0`;
                
                try {
                    // Ambil chunk pertama
                    const response = await fetch(firstChunkUrl);
                    
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    
                    const data = await response.json();
                    
                    if (!data.success) {
                        throw new Error(data.message || 'Terjadi kesalahan saat mengambil data');
                    }
                    
                    // Set total records
                    totalRecords = data.total_records;
                    
                    // Tambahkan data ke array
                    allEmployees = [...data.employees];
                    loadedRecords = data.employees.length;
                    offset = data.employees.length;
                    
                    // Fungsi untuk memperbarui progress bar
                    const updateProgress = () => {
                        const percentage = Math.round((loadedRecords / totalRecords) * 100);
                        
                        // Log untuk debugging
                        console.log(`Progress update: ${loadedRecords}/${totalRecords} = ${percentage}%`);
                        
                        // Perbarui progress bar dengan pendekatan yang lebih langsung
                        progressBar.style.width = percentage + '%';
                        progressBar.setAttribute('aria-valuenow', percentage);
                        progressBar.textContent = percentage + '%';
                        
                        // Perbarui teks status
                        loadingStatus.textContent = `Memuat ${loadedRecords} dari ${totalRecords} data (${percentage}%)...`;
                    };
                    
                    // Update progress awal
                    updateProgress();
                    
                    // Ambil chunk berikutnya jika masih ada data
                    let chunkCounter = 1;
                    while (loadedRecords < totalRecords && loadedRecords < entriesPerPage) {
                        // Buat URL untuk chunk berikutnya
                        const nextChunkUrl = `${cleanBaseUrl}&offset=${offset}`;
                        
                        // Ambil chunk berikutnya
                        const response = await fetch(nextChunkUrl);
                        
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        
                        const data = await response.json();
                        
                        if (!data.success) {
                            throw new Error(data.message || 'Terjadi kesalahan saat mengambil data');
                        }
                        
                        // Tambahkan data ke array
                        allEmployees = [...allEmployees, ...data.employees];
                        loadedRecords += data.employees.length;
                        offset += data.employees.length;
                        chunkCounter++;
                        
                        // Update progress
                        updateProgress();
                        
                        // Hentikan jika sudah mencapai batas entri per halaman
                        if (loadedRecords >= entriesPerPage) {
                            break;
                        }
                        
                        // Hentikan jika tidak ada lagi data
                        if (!data.has_more) {
                            break;
                        }
                    }
                    
                    // Batasi jumlah data yang ditampilkan sesuai entriesPerPage
                    if (allEmployees.length > entriesPerPage) {
                        allEmployees = allEmployees.slice(0, entriesPerPage);
                    }
                    
                    // Update total entries dan tabel
                    totalEntries = totalRecords;
                    
                    // Update tabel
                    updateTable(allEmployees);
                    
                    // Update pagination dan info entries
                    updatePagination();
                    updateEntriesInfo();
                    
                    // Update tombol bulk copy
                    updateBulkCopyButton();
                    
                    const endTime = performance.now();
                    const totalTime = ((endTime - startTime) / 1000).toFixed(3);
                    
                    console.log(`Lazy loading selesai dalam ${totalTime} detik`);
                    
                    return allEmployees;
                } catch (error) {
                    console.error('Error in overridden loadEmployeesLazy:', error);
                    
                    // Tampilkan pesan error
                    tableContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> Error: ${error.message}
                        </div>
                    `;
                    
                    throw error;
                }
            };
            
            console.log('Complete override of loadEmployeesLazy applied successfully');
        } else {
            console.warn('Could not find loadEmployeesLazy function to override');
        }
    }
    
    // Jalankan override
    setTimeout(overrideLoadEmployeesLazy, 500);
});
