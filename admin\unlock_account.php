<?php
session_start();
include '../config/config.php';
include '../config/security_helper.php';

// Cek apakah pengguna sudah login dan memiliki role admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    // Jika bukan admin, redirect ke halaman login
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi variabel
$success_message = '';
$error_message = '';

// Cek apakah ada parameter user_id
if (isset($_GET['user_id']) && is_numeric($_GET['user_id'])) {
    $user_id = intval($_GET['user_id']);
    
    // Cek apakah user_id valid
    $query = "SELECT id, name, email, nik, failed_attempts FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        
        // Reset failed_attempts dan last_failed_attempt
        $update_query = "UPDATE users SET 
                        failed_attempts = 0,
                        last_failed_attempt = NULL
                        WHERE id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("i", $user_id);
        
        if ($update_stmt->execute()) {
            $success_message = "Akun untuk {$user['name']} (NIK: {$user['nik']}) berhasil dibuka kembali.";
            
            // Log aktivitas
            if (file_exists('../config/activity_logger.php')) {
                include_once '../config/activity_logger.php';
                if (function_exists('log_activity')) {
                    log_activity($_SESSION['user_id'], "Membuka kunci akun user ID: {$user_id}", "security", [
                        'target_user_id' => $user_id,
                        'target_user_name' => $user['name'],
                        'target_user_nik' => $user['nik']
                    ]);
                }
            }
        } else {
            $error_message = "Gagal membuka kunci akun. Error: " . $conn->error;
        }
    } else {
        $error_message = "User ID tidak valid.";
    }
} else {
    $error_message = "Parameter user_id tidak valid.";
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<body>
    <?php include '../config/navbarb.php'; ?>
    
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Buka Kunci Akun</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($success_message): ?>
                            <div class="alert alert-success">
                                <?php echo $success_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger">
                                <?php echo $error_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <p>
                            <a href="user_management.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Kembali ke Manajemen User
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php include '../config/footer.php'; ?>
</body>
</html>
