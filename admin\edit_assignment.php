<?php
/**
 * Edit Assignment Page for Admin
 * This page allows admins to edit an existing assignment
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if assignment ID is provided
$assignment_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($assignment_id <= 0) {
    $_SESSION['error_message'] = "ID tugas tidak valid.";
    header('Location: manage_classes.php');
    exit();
}

// Get assignment information
$assignment_query = "SELECT a.*, c.id as class_id, c.title as class_title, t.training_topic
                    FROM training_assignments a
                    JOIN training_classes c ON a.class_id = c.id
                    JOIN training_submissions t ON c.training_id = t.id
                    WHERE a.id = ?";
$stmt = $conn->prepare($assignment_query);
$stmt->bind_param("i", $assignment_id);
$stmt->execute();
$result = $stmt->get_result();
$assignment = $result->fetch_assoc();
$stmt->close();

if (!$assignment) {
    $_SESSION['error_message'] = "Tugas tidak ditemukan.";
    header('Location: manage_classes.php');
    exit();
}

// Extract class ID from assignment
$class_id = $assignment['class_id'];

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_assignment'])) {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $instructions = trim($_POST['instructions']);
    $due_date = $_POST['due_date'] . ' ' . $_POST['due_time'];
    $points = !empty($_POST['points']) ? intval($_POST['points']) : null;
    $allow_attachments = isset($_POST['allow_attachments']) ? 1 : 0;
    $max_file_size = !empty($_POST['max_file_size']) ? intval($_POST['max_file_size']) * 1024 * 1024 : 5242880; // Convert MB to bytes
    $allowed_file_types = !empty($_POST['allowed_file_types']) ? trim($_POST['allowed_file_types']) : 'pdf,doc,docx,ppt,pptx,xls,xlsx,txt,zip,rar,jpg,jpeg,png';
    $is_published = isset($_POST['is_published']) ? 1 : 0;

    // Validate input
    if (empty($title)) {
        $error_message = "Judul tugas harus diisi.";
    } elseif (empty($due_date)) {
        $error_message = "Tenggat waktu harus diisi.";
    } else {
        // Check if the allow_attachments column exists
        $check_column_query = "SHOW COLUMNS FROM training_assignments LIKE 'allow_attachments'";
        $column_result = $conn->query($check_column_query);
        $allow_attachments_exists = ($column_result && $column_result->num_rows > 0);

        if (!$allow_attachments_exists) {
            // Add the column if it doesn't exist
            $add_column_query = "ALTER TABLE training_assignments
                               ADD COLUMN allow_attachments tinyint(1) DEFAULT '1' AFTER points,
                               ADD COLUMN max_file_size int(11) DEFAULT '5242880' AFTER allow_attachments,
                               ADD COLUMN allowed_file_types varchar(255) DEFAULT 'pdf,doc,docx,ppt,pptx,xls,xlsx,txt,zip,rar,jpg,jpeg,png' AFTER max_file_size";
            $conn->query($add_column_query);
        }

        // Update assignment
        $update_query = "UPDATE training_assignments
                       SET title = ?, description = ?, instructions = ?, due_date = ?,
                           points = ?, allow_attachments = ?, max_file_size = ?,
                           allowed_file_types = ?, is_published = ?
                       WHERE id = ?";

        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("ssssiissii", $title, $description, $instructions, $due_date,
                         $points, $allow_attachments, $max_file_size, $allowed_file_types,
                         $is_published, $assignment_id);

        if ($stmt->execute()) {
            $success_message = "Tugas berhasil diperbarui.";

            // Update assignment data
            $assignment['title'] = $title;
            $assignment['description'] = $description;
            $assignment['instructions'] = $instructions;
            $assignment['due_date'] = $due_date;
            $assignment['points'] = $points;
            $assignment['allow_attachments'] = $allow_attachments;
            $assignment['max_file_size'] = $max_file_size;
            $assignment['allowed_file_types'] = $allowed_file_types;
            $assignment['is_published'] = $is_published;
        } else {
            $error_message = "Gagal memperbarui tugas: " . $conn->error;
        }

        $stmt->close();
    }
}

// Parse due date and time
$due_date_parts = explode(' ', $assignment['due_date']);
$due_date = isset($due_date_parts[0]) ? $due_date_parts[0] : date('Y-m-d');
$due_time = isset($due_date_parts[1]) ? $due_date_parts[1] : '23:59:00';
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .form-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .form-section h3 {
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .class-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .class-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .class-topic {
        color: #6c757d;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-edit"></i> Edit Tugas</h1>
                <a href="manage_class.php?id=<?= $class_id ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Kelas
                </a>
            </div>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="class-info">
                <div class="class-title"><?= htmlspecialchars($assignment['class_title']) ?></div>
                <div class="class-topic"><?= htmlspecialchars($assignment['training_topic']) ?></div>
            </div>

            <div class="form-section">
                <h3>Informasi Tugas</h3>

                <form method="post" action="">
                    <div class="mb-3">
                        <label for="title" class="form-label">Judul Tugas <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" value="<?= htmlspecialchars($assignment['title']) ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Deskripsi Tugas</label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?= htmlspecialchars($assignment['description']) ?></textarea>
                        <div class="form-text">Deskripsi singkat tentang tugas ini.</div>
                    </div>

                    <div class="mb-3">
                        <label for="instructions" class="form-label">Instruksi Tugas</label>
                        <textarea class="form-control" id="instructions" name="instructions" rows="5"><?= htmlspecialchars($assignment['instructions']) ?></textarea>
                        <div class="form-text">Instruksi detail tentang cara mengerjakan tugas ini.</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="due_date" class="form-label">Tanggal Tenggat <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="due_date" name="due_date" value="<?= $due_date ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="due_time" class="form-label">Waktu Tenggat <span class="text-danger">*</span></label>
                                <input type="time" class="form-control" id="due_time" name="due_time" value="<?= substr($due_time, 0, 5) ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="points" class="form-label">Poin</label>
                        <input type="number" class="form-control" id="points" name="points" min="0" max="100" value="<?= $assignment['points'] ?>">
                        <div class="form-text">Poin maksimal yang dapat diperoleh untuk tugas ini.</div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="allow_attachments" name="allow_attachments" <?= isset($assignment['allow_attachments']) && $assignment['allow_attachments'] ? 'checked' : '' ?>>
                        <label class="form-check-label" for="allow_attachments">Izinkan Lampiran File</label>
                    </div>

                    <div class="row" id="attachment_options">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_file_size" class="form-label">Ukuran File Maksimal (MB)</label>
                                <input type="number" class="form-control" id="max_file_size" name="max_file_size" min="1" max="50" value="<?= isset($assignment['max_file_size']) ? floor($assignment['max_file_size'] / (1024 * 1024)) : 5 ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="allowed_file_types" class="form-label">Tipe File yang Diizinkan</label>
                                <input type="text" class="form-control" id="allowed_file_types" name="allowed_file_types" value="<?= isset($assignment['allowed_file_types']) ? htmlspecialchars($assignment['allowed_file_types']) : 'pdf,doc,docx,ppt,pptx,xls,xlsx,txt,zip,rar,jpg,jpeg,png' ?>">
                                <div class="form-text">Pisahkan dengan koma (contoh: pdf,doc,docx)</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_published" name="is_published" <?= isset($assignment['is_published']) && $assignment['is_published'] ? 'checked' : '' ?>>
                        <label class="form-check-label" for="is_published">Publikasikan Tugas</label>
                        <div class="form-text">Jika dicentang, tugas akan terlihat oleh peserta.</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="delete_assignment.php?id=<?= $assignment_id ?>&class_id=<?= $class_id ?>" class="btn btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus tugas ini?')">
                            <i class="fas fa-trash"></i> Hapus Tugas
                        </a>
                        <button type="submit" name="update_assignment" class="btn btn-primary">
                            <i class="fas fa-save"></i> Simpan Perubahan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);

        // Toggle attachment options based on allow_attachments checkbox
        const allowAttachmentsCheckbox = document.getElementById('allow_attachments');
        const attachmentOptions = document.getElementById('attachment_options');

        function toggleAttachmentOptions() {
            if (allowAttachmentsCheckbox.checked) {
                attachmentOptions.style.display = 'flex';
            } else {
                attachmentOptions.style.display = 'none';
            }
        }

        if (allowAttachmentsCheckbox && attachmentOptions) {
            toggleAttachmentOptions();
            allowAttachmentsCheckbox.addEventListener('change', toggleAttachmentOptions);
        }
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
