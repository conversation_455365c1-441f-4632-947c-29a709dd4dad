<?php
session_start();
require_once '../config/config.php'; // Database connection and configuration
require_once 'record_batch_employee_history.php'; // Include batch history functions

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

$pageTitle = "Riwayat Batch Karyawan";

// Debug settings - hanya admin yang dapat mengaktifkan debug
$debug = false;
if (isset($_SESSION['user_id']) && $_SESSION['role_id'] == 99) {
    if (isset($_GET['debug'])) {
        $debug = $_GET['debug'] == '1';
        // Simpan setting debug dalam session
        $_SESSION['batch_history_debug'] = $debug;
    } elseif (isset($_SESSION['batch_history_debug'])) {
        $debug = $_SESSION['batch_history_debug'];
    }
}

// Create batch history table if it doesn't exist
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    createBatchHistoryTable($pdo);
} catch (Exception $e) {
    $error_message = "Error creating batch history table: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <?php include '../config/head.php'; ?>
    <style>
        body {
            background-color: #f5f5f5;
        }
        .container-fluid {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
            margin-top: 80px;
            position: relative;
            z-index: 1;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
            overflow: visible;
        }
        .card-header {
            background: rgb(157, 0, 0) !important;
            color: white;
            padding: 20px;
            border-bottom: none;
            border-radius: 10px 10px 0 0 !important;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        .card-header .btn {
            margin-left: 10px;
        }
        .card-body {
            padding: 30px;
        }
        .filter-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .table-responsive {
            margin-top: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        .table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            text-align: left;
            padding: 15px 10px;
            border-bottom: 2px solid #dee2e6;
        }
        .table td {
            padding: 15px 10px;
            border-bottom: 1px solid #e9ecef;
            color: #495057;
            vertical-align: middle;
        }
        .table tr:hover {
            background-color: #f8f9fa;
        }
        .pagination {
            margin-top: 20px;
            justify-content: center;
        }
        .pagination .page-item.active .page-link {
            background-color: #9d0000;
            border-color: #9d0000;
        }
        .pagination .page-link {
            color: #9d0000;
        }
        .btn-primary {
            background-color: #9d0000;
            border-color: #9d0000;
        }
        .btn-primary:hover {
            background-color: #7d0000;
            border-color: #7d0000;
        }
        .action-batch-insert {
            color: #28a745;
            font-weight: bold;
        }
        .action-batch-update {
            color: #ffc107;
            font-weight: bold;
        }
        .action-batch-delete {
            color: #dc3545;
            font-weight: bold;
        }
        .action-batch-rollback {
            color: #6f42c1;
            font-weight: bold;
        }
        #loading-indicator {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
            color: #9d0000;
        }
        .badge-success {
            background-color: #28a745;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-danger {
            background-color: #dc3545;
        }
        .badge-info {
            background-color: #17a2b8;
        }
        .badge-secondary {
            background-color: #6c757d;
        }
        .btn-warning {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
            border-color: #d39e00;
            color: #212529;
        }
        .rollback-batch {
            font-weight: 500;
        }
        .rollback-batch:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <?php include '../config/navbara.php'; ?>

    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-history"></i> <?php echo $pageTitle; ?></h3>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-12">
                        <a href="employee_history.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali ke Riwayat Karyawan
                        </a>
                        <button id="delete-all-batch-history" class="btn btn-danger ml-2">
                            <i class="fas fa-trash-alt"></i> Hapus Semua Riwayat Batch
                        </button>
                    </div>
                </div>

                <!-- Loading Indicator -->
                <div id="loading-indicator">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Memuat data...</p>
                </div>

                <!-- Batch History Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Jenis Aksi</th>
                                <th>Mode</th>
                                <th>Jumlah Sukses</th>
                                <th>Jumlah Error</th>
                                <th>Jumlah Dilewati</th>
                                <th>Waktu</th>
                                <th>Diubah Oleh</th>
                                <th>Detail</th>
                                <th>Rollback</th>
                                <th>Hapus</th>
                            </tr>
                        </thead>
                        <tbody id="batch-history-data">
                            <?php
                            try {
                                // Get batch history data
                                $stmt = $pdo->prepare("
                                    SELECT b.*, u.name as user_name
                                    FROM karyawan_batch_history b
                                    LEFT JOIN users u ON b.changed_by = u.id
                                    ORDER BY b.change_timestamp DESC
                                    LIMIT 100
                                ");
                                $stmt->execute();
                                $batch_history = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                if (count($batch_history) > 0) {
                                    foreach ($batch_history as $batch) {
                                        $batch_data = json_decode($batch['batch_data'], true);
                                        $action_class = '';
                                        $action_label = '';

                                        switch($batch['action_type']) {
                                            case 'BATCH_INSERT':
                                                $action_class = 'action-batch-insert';
                                                $action_label = 'Penambahan Batch';
                                                break;
                                            case 'BATCH_UPDATE':
                                                $action_class = 'action-batch-update';
                                                $action_label = 'Perubahan Batch';
                                                break;
                                            case 'BATCH_DELETE':
                                                $action_class = 'action-batch-delete';
                                                $action_label = 'Penghapusan Batch';
                                                break;
                                            case 'INDIVIDUAL_DELETE':
                                                $action_class = 'action-batch-delete';
                                                $action_label = 'Penghapusan Individual';
                                                break;
                                            case 'BATCH_ROLLBACK':
                                                $action_class = 'action-batch-rollback';
                                                $action_label = 'Rollback Batch';
                                                break;
                                            case 'BATCH_UNDO_ROLLBACK':
                                                $action_class = 'action-batch-rollback';
                                                $action_label = 'Undo Rollback';
                                                break;
                                            case 'BATCH_ROLLBACK_AGAIN':
                                                $action_class = 'action-batch-rollback';
                                                $action_label = 'Rollback Again';
                                                break;
                                            case 'FALLBACK_ROLLBACK':
                                                $action_class = 'action-batch-rollback';
                                                $action_label = 'Fallback Rollback';
                                                break;
                                            default:
                                                $action_label = $batch['action_type'];
                                        }

                                        $mode = isset($batch_data['mode']) ? $batch_data['mode'] : 'unknown';
                                        $mode_label = ($mode === 'add') ? 'Tambah' : (($mode === 'update') ? 'Update' : $mode);

                                        $total_success = isset($batch_data['total_success']) ? $batch_data['total_success'] : 0;
                                        $total_error = isset($batch_data['total_error']) ? $batch_data['total_error'] : 0;
                                        $total_skipped = isset($batch_data['total_skipped']) ? $batch_data['total_skipped'] : 0;

                                        echo "<tr>";
                                        echo "<td>{$batch['batch_id']}</td>";
                                        echo "<td class='{$action_class}'>{$action_label}</td>";
                                        echo "<td>{$mode_label}</td>";
                                        echo "<td><span class='badge badge-success'>{$total_success}</span></td>";
                                        echo "<td><span class='badge badge-danger'>{$total_error}</span></td>";
                                        echo "<td><span class='badge badge-warning'>{$total_skipped}</span></td>";
                                        echo "<td>" . date('d M Y H:i:s', strtotime($batch['change_timestamp'])) . "</td>";
                                        echo "<td>" . htmlspecialchars($batch['user_name'] ?? 'Unknown') . "</td>";
                                        echo "<td>
                                                <button class='btn btn-info btn-sm view-batch-details' data-id='{$batch['batch_id']}'>
                                                    <i class='fas fa-eye'></i> Lihat
                                                </button>
                                              </td>";

                                        // Check if rollback is possible
                                        $can_rollback = canRollback($batch_data, $batch['action_type'], $batch['batch_id']);
                                        $is_rolled_back = strpos($batch['action_type'], '_ROLLED_BACK') !== false;

                                        echo "<td>";
                                        if ($can_rollback) {
                                            // Determine button text based on action type
                                            $button_text = 'Rollback';
                                            $button_icon = 'fas fa-undo';

                                            if ($batch['action_type'] === 'BATCH_ROLLBACK') {
                                                $button_text = 'Undo Rollback';
                                                $button_icon = 'fas fa-redo';
                                            } elseif (strpos($batch['action_type'], '_ROLLED_BACK') !== false) {
                                                $button_text = 'Rollback Again';
                                                $button_icon = 'fas fa-undo';
                                            }

                                            echo "<button class='btn btn-warning btn-sm rollback-batch' data-id='{$batch['batch_id']}' data-action='{$batch['action_type']}'>
                                                    <i class='{$button_icon}'></i> {$button_text}
                                                  </button>";
                                        } else {
                                            // Debug information
                                            $debug_info = "Action: " . $batch['action_type'];
                                            if ($batch['action_type'] === 'BATCH_INSERT') {
                                                $debug_info .= " (Should be available)";
                                            }
                                            echo "<span class='badge badge-secondary' title='{$debug_info}'>Not Available</span>";
                                        }
                                        echo "</td>";

                                        echo "<td>
                                                <button class='btn btn-danger btn-sm delete-batch-history' data-id='{$batch['batch_id']}'>
                                                    <i class='fas fa-trash'></i> Hapus
                                                </button>
                                              </td>";
                                        echo "</tr>";
                                    }
                                } else {
                                    echo "<tr><td colspan='11' class='text-center'>Tidak ada data riwayat batch yang ditemukan</td></tr>";
                                }
                            } catch (Exception $e) {
                                echo "<tr><td colspan='11' class='text-center text-danger'>Error: " . $e->getMessage() . "</td></tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Batch Detail Modal -->
    <div class="modal fade" id="batchDetailModal" tabindex="-1" aria-labelledby="batchDetailModalLabel">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header" style="background-color: #9d0000; color: white;">
            <h5 class="modal-title" id="batchDetailModalLabel">Detail Batch</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="color: white;"></button>
          </div>
          <div class="modal-body" id="batchDetailModalBody">
            <!-- Details will be loaded here -->
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Rollback Confirmation Modal -->
    <div class="modal fade" id="rollbackConfirmModal" tabindex="-1" aria-labelledby="rollbackConfirmModalLabel">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header" style="background-color: #ffc107; color: #212529;">
            <h5 class="modal-title" id="rollbackConfirmModalLabel">
                <i class="fas fa-exclamation-triangle"></i> Konfirmasi Rollback Batch
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" id="rollbackConfirmModalBody">
            <!-- Rollback confirmation details will be loaded here -->
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
            <button type="button" class="btn btn-warning" id="confirmRollbackBtn">
                <i class="fas fa-undo"></i> Ya, Lakukan Rollback
            </button>
          </div>
        </div>
      </div>
    </div>

    <?php include '../config/footer.php'; ?>
    <script>
        $(document).ready(function() {
            // View Batch Details Button Click
            $(document).on('click', '.view-batch-details', function() {
                var batchId = $(this).data('id');

                // Show loading in modal
                $('#batchDetailModalBody').html('<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">Memuat detail...</p></div>');

                // Show modal
                var batchDetailModal = new bootstrap.Modal(document.getElementById('batchDetailModal'));
                batchDetailModal.show();

                // AJAX call to get batch details
                $.ajax({
                    url: 'get_batch_history_details.php',
                    type: 'POST',
                    data: { batch_id: batchId },
                    success: function(response) {
                        $('#batchDetailModalBody').html(response);
                    },
                    error: function() {
                        $('#batchDetailModalBody').html('<div class="alert alert-danger">Error: Gagal mengambil detail batch.</div>');
                    }
                });
            });

            // Delete Batch History Button Click
            $(document).on('click', '.delete-batch-history', function() {
                var batchId = $(this).data('id');

                // Gunakan confirmAction sebagai pengganti confirm
                confirmAction(
                    'Apakah Anda yakin ingin menghapus riwayat batch ini (ID: ' + batchId + ')? Tindakan ini tidak dapat dibatalkan.',
                    function() {
                        // AJAX call to delete batch history
                        $.ajax({
                            url: 'delete_batch_history.php',
                            type: 'POST',
                            data: { batch_id: batchId },
                            dataType: 'json',
                            success: function(response) {
                                if (response.success) {
                                    alert('Riwayat batch berhasil dihapus!');
                                    // Reload page to reflect changes
                                    location.reload();
                                } else {
                                    alert('Gagal menghapus riwayat batch: ' + response.message);
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error("Delete Batch History Error:", xhr.responseText);
                                alert('Terjadi kesalahan saat menghapus riwayat batch. Periksa konsol untuk detail.');
                            }
                        });
                    }
                );
            });

            // Rollback Batch Button Click
            $(document).on('click', '.rollback-batch', function() {
                var batchId = $(this).data('id');
                var actionType = $(this).data('action');

                // Show rollback preview modal
                $('#rollbackConfirmModalBody').html('<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">Memuat preview rollback...</p></div>');

                var rollbackModal = new bootstrap.Modal(document.getElementById('rollbackConfirmModal'));
                rollbackModal.show();

                // Store batch ID for confirmation
                $('#confirmRollbackBtn').data('batch-id', batchId);

                // AJAX call to get rollback preview
                $.ajax({
                    url: 'get_rollback_preview.php',
                    type: 'POST',
                    data: { batch_id: batchId },
                    success: function(response) {
                        $('#rollbackConfirmModalBody').html(response);
                    },
                    error: function() {
                        $('#rollbackConfirmModalBody').html('<div class="alert alert-danger">Error: Gagal mengambil preview rollback.</div>');
                    }
                });
            });

            // Confirm Rollback Button Click
            $(document).on('click', '#confirmRollbackBtn', function() {
                var batchId = $(this).data('batch-id');

                // Hide modal
                var rollbackModal = bootstrap.Modal.getInstance(document.getElementById('rollbackConfirmModal'));
                rollbackModal.hide();

                // Show loading indicator
                $('#loading-indicator').show();

                // AJAX call to perform rollback
                $.ajax({
                    url: 'batch_rollback.php',
                    type: 'POST',
                    data: { batch_id: batchId },
                    dataType: 'json',
                    success: function(response) {
                        $('#loading-indicator').hide();
                        if (response.success) {
                            var message = 'Rollback berhasil!\n\n';
                            message += 'Sukses: ' + response.results.success_count + '\n';
                            message += 'Error: ' + response.results.error_count + '\n';
                            message += 'Dilewati: ' + response.results.skipped_count + '\n';

                            if (response.results.errors && response.results.errors.length > 0) {
                                message += '\nErrors:\n' + response.results.errors.join('\n');
                            }

                            alert(message);
                            location.reload();
                        } else {
                            alert('Rollback gagal: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#loading-indicator').hide();
                        console.error("Rollback Error:", xhr.responseText);
                        alert('Terjadi kesalahan saat melakukan rollback. Periksa konsol untuk detail.');
                    }
                });
            });

            // Delete All Batch History Button Click
            $(document).on('click', '#delete-all-batch-history', function() {
                // Gunakan confirmAction sebagai pengganti confirm
                confirmAction(
                    'PERHATIAN: Apakah Anda yakin ingin menghapus SEMUA riwayat batch karyawan? Tindakan ini tidak dapat dibatalkan dan akan menghapus seluruh catatan riwayat batch.',
                    function() {
                        // Double confirmation for critical action
                        confirmAction(
                            'Konfirmasi sekali lagi: Anda akan menghapus SEMUA riwayat batch karyawan. Lanjutkan?',
                            function() {
                                // Show loading indicator
                                $('#loading-indicator').show();

                                // AJAX call to delete all batch history
                                $.ajax({
                                    url: 'delete_all_batch_history.php',
                                    type: 'POST',
                                    dataType: 'json',
                                    success: function(response) {
                                        $('#loading-indicator').hide();
                                        if (response.success) {
                                            alert('Semua riwayat batch berhasil dihapus! ' + response.count + ' catatan telah dihapus.');
                                            // Reload page to reflect changes
                                            location.reload();
                                        } else {
                                            alert('Gagal menghapus semua riwayat batch: ' + response.message);
                                        }
                                    },
                                    error: function(xhr, status, error) {
                                        $('#loading-indicator').hide();
                                        console.error("Delete All Batch History Error:", xhr.responseText);
                                        alert('Terjadi kesalahan saat menghapus semua riwayat batch. Periksa konsol untuk detail.');
                                    }
                                });
                            }
                        );
                    }
                );
            });
        });
    </script>
</body>
</html>
