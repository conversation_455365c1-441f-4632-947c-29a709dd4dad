<?php
/**
 * API untuk toggle display status training di kalender
 * Ketika training dipilih untuk ditampilkan, set is_hidden = 0
 * Ketika training di-hide dari kalender, set is_hidden = 1
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include necessary files
require_once '../../config/config.php';
require_once '../security.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role_id'], [2, 3, 4, 5, 99])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized - Admin access required']);
    exit;
}

try {
    // Get parameters
    $training_id = $_POST['training_id'] ?? '';
    $training_type = $_POST['training_type'] ?? '';
    $action = $_POST['action'] ?? ''; // 'show' or 'hide'
    
    if (empty($training_id) || empty($training_type) || empty($action)) {
        throw new Exception('Missing required parameters');
    }
    
    if (!in_array($training_type, ['online', 'offline'])) {
        throw new Exception('Invalid training type');
    }
    
    if (!in_array($action, ['show', 'hide'])) {
        throw new Exception('Invalid action');
    }
    
    // Set is_hidden value based on action
    $is_hidden = ($action === 'hide') ? 1 : 0;
    
    // Update the appropriate table
    if ($training_type === 'offline') {
        $query = "UPDATE offline_training SET is_hidden = ? WHERE id = ?";
    } else {
        $query = "UPDATE training_submissions SET is_hidden = ? WHERE id = ?";
    }
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $is_hidden, $training_id);
    
    if ($stmt->execute()) {
        $affected_rows = $stmt->affected_rows;
        $stmt->close();
        
        if ($affected_rows > 0) {
            echo json_encode([
                'success' => true,
                'message' => $action === 'show' ? 
                    'Training berhasil ditampilkan di kalender' : 
                    'Training berhasil disembunyikan dari kalender',
                'training_id' => $training_id,
                'training_type' => $training_type,
                'action' => $action,
                'is_hidden' => $is_hidden
            ]);
        } else {
            throw new Exception('Training tidak ditemukan atau tidak ada perubahan');
        }
    } else {
        throw new Exception('Gagal mengupdate status training: ' . $stmt->error);
    }
    
} catch (Exception $e) {
    error_log("Error in toggle_training_display.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
