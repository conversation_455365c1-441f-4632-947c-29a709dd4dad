<?php
// <PERSON><PERSON><PERSON> to help with Composer installation

echo "<html>\n<head>\n<title>Composer Setup Helper</title>\n<style>\nbody { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }\n.container { max-width: 800px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }\nh1 { color: #333; }\n.error { color: #e74c3c; background-color: #fadbd8; padding: 10px; border-radius: 5px; margin-bottom: 15px; }\n.success { color: #27ae60; background-color: #d4efdf; padding: 10px; border-radius: 5px; margin-bottom: 15px; }\n.info { color: #2980b9; background-color: #ebf5fb; padding: 10px; border-radius: 5px; margin-bottom: 15px; }\ncode { background: #f8f8f8; padding: 2px 5px; border-radius: 3px; font-family: monospace; }\nol { margin-left: 20px; }\nli { margin-bottom: 10px; }\n</style>\n</head>\n<body>\n<div class=\"container\">\n<h1>Composer Setup Helper</h1>";

// Check if vendor directory exists
$vendorDir = __DIR__ . '/vendor';
$vendorExists = file_exists($vendorDir) && is_dir($vendorDir);
$autoloadExists = file_exists($vendorDir . '/autoload.php');

if ($vendorExists && $autoloadExists) {
    echo "<div class=\"success\">\n<strong>Success!</strong> The vendor directory and autoload.php file exist.\n</div>";
    echo "<p>Your Composer dependencies appear to be installed correctly. You should be able to use the application without the autoload.php error.</p>";
} else {
    echo "<div class=\"error\">\n<strong>Error:</strong> " . 
        (!$vendorExists ? "The vendor directory does not exist." : "The autoload.php file is missing in the vendor directory.") . 
        "\n</div>";
    
    echo "<div class=\"info\">\n<h2>How to Fix This Issue</h2>\n<p>You need to install the Composer dependencies for this project. Here's how:</p>\n\n<ol>\n<li><strong>Install Composer</strong> if you don't have it already:\n<br>Download from <a href=\"https://getcomposer.org/download/\" target=\"_blank\">getcomposer.org</a></li>\n\n<li><strong>Open a command prompt or terminal</strong> and navigate to your project directory:\n<br><code>cd " . __DIR__ . "</code></li>\n\n<li><strong>Run the Composer install command</strong>:\n<br><code>composer install</code></li>\n</ol>\n\n<p>If you're using Laragon, you can also:</p>\n<ol>\n<li>Right-click on the Laragon tray icon</li>\n<li>Select 'Terminal'</li>\n<li>Navigate to your project directory: <code>cd " . __DIR__ . "</code></li>\n<li>Run: <code>composer install</code></li>\n</ol>\n</div>";
}

// Check PHP version and extensions
echo "<div class=\"info\">\n<h2>System Information</h2>\n<p><strong>PHP Version:</strong> " . phpversion() . "</p>";

// Check required extensions for PhpSpreadsheet
$requiredExtensions = ['gd', 'mbstring', 'zip', 'xml'];
echo "<p><strong>Required Extensions:</strong></p>\n<ul>";
foreach ($requiredExtensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "<li>" . $ext . ": " . ($loaded ? "<span style='color:green'>Loaded</span>" : "<span style='color:red'>Not Loaded</span>") . "</li>";
}
echo "</ul>\n</div>";

// Check composer.json
$composerJsonPath = __DIR__ . '/composer.json';
if (file_exists($composerJsonPath)) {
    $composerJson = file_get_contents($composerJsonPath);
    echo "<div class=\"info\">\n<h2>Composer Configuration</h2>\n<pre>" . htmlspecialchars($composerJson) . "</pre>\n</div>";
} else {
    echo "<div class=\"error\">\n<strong>Error:</strong> composer.json file not found.\n</div>";
}

echo "</div>\n</body>\n</html>";