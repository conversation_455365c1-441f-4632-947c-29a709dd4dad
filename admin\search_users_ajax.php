<?php
/**
 * AJAX Search Users
 * This file handles AJAX requests for searching users
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Get parameters
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$search_term = isset($_GET['search']) ? trim($_GET['search']) : '';

if ($class_id <= 0) {
    echo json_encode(['error' => 'Invalid class ID']);
    exit();
}

// Get existing participants
$existing_participants = [];
$participants_query = "SELECT user_id FROM training_participants WHERE class_id = ?";
$stmt = $conn->prepare($participants_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$participants_result = $stmt->get_result();

while ($row = $participants_result->fetch_assoc()) {
    $existing_participants[] = $row['user_id'];
}
$stmt->close();

// First, let's check the structure of the users table to find the name column
$user_table_query = "DESCRIBE users";
$user_table_result = $conn->query($user_table_query);
$name_column = 'user_name'; // Default to user_name if we can't find a better column

if ($user_table_result) {
    while ($column = $user_table_result->fetch_assoc()) {
        // Look for common name column patterns
        if (in_array(strtolower($column['Field']), ['full_name', 'name', 'user_name', 'username', 'nama'])) {
            $name_column = $column['Field'];
            // Prefer full_name or name if available
            if (in_array(strtolower($column['Field']), ['full_name', 'name', 'nama'])) {
                break;
            }
        }
    }
}

// Prepare the query
$users = [];
$query_params = [];
$query = "SELECT id, $name_column as user_name, email, role_id FROM users WHERE 1=1";

// Add search condition if search term is provided
if (!empty($search_term)) {
    $query .= " AND ($name_column LIKE ? OR email LIKE ?)";
    $search_param = "%" . $search_term . "%";
    $query_params[] = $search_param;
    $query_params[] = $search_param;
}

// Exclude existing participants
if (!empty($existing_participants)) {
    $placeholders = implode(',', array_fill(0, count($existing_participants), '?'));
    $query .= " AND id NOT IN ($placeholders)";
    foreach ($existing_participants as $participant_id) {
        $query_params[] = $participant_id;
    }
} else {
    // If no existing participants, just exclude 0 to keep the query valid
    $query .= " AND id NOT IN (0)";
}

// Add order by
$query .= " ORDER BY $name_column ASC LIMIT 50";

// Prepare and execute the query
$stmt = $conn->prepare($query);

if (!empty($query_params)) {
    $types = str_repeat('s', count($query_params));
    $stmt->bind_param($types, ...$query_params);
}

$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $users[] = $row;
}
$stmt->close();

// Define role labels
$role_labels = [
    '1' => 'Pemohon',
    '2' => 'Dept Head',
    '3' => 'HRD',
    '4' => 'GA',
    '5' => 'Factory Manager',
    '6' => 'Direktur',
    '99' => 'Admin'
];

// Prepare HTML output
$html = '';

if (empty($users)) {
    $html = '<div class="alert alert-info">Tidak ada pengguna yang ditemukan.</div>';
} else {
    foreach ($users as $user) {
        $role_label = isset($role_labels[$user['role_id']]) ? $role_labels[$user['role_id']] : 'Role ' . $user['role_id'];
        $html .= '
        <div class="user-card">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="selected_users[]" value="' . $user['id'] . '" id="user-' . $user['id'] . '">
                <label class="form-check-label" for="user-' . $user['id'] . '">
                    <div class="user-info">
                        <div class="user-avatar">
                            ' . strtoupper(substr($user['user_name'], 0, 1)) . '
                        </div>
                        <div>
                            <div class="user-name">' . htmlspecialchars($user['user_name']) . '</div>
                            <div class="user-email">' . htmlspecialchars($user['email']) . '</div>
                            <div class="user-role">' . $role_label . '</div>
                        </div>
                    </div>
                </label>
            </div>
        </div>';
    }
}

// Return the HTML
echo json_encode(['html' => $html, 'count' => count($users)]);

// Close database connection
$conn->close();
?>
