<?php
/**
 * API untuk toggle visibility training (hide/unhide)
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include necessary files
require_once '../../config/config.php';
require_once '../security.php';

// Check if user is admin (same validation as security.php)
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role_id'], [2, 3, 4, 5, 99])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized - Admin access required']);
    exit;
}

try {
    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        // Fallback to POST data
        $training_id = $_POST['training_id'] ?? '';
        $training_type = $_POST['training_type'] ?? '';
        $action = $_POST['action'] ?? '';
    } else {
        $training_id = $input['training_id'] ?? '';
        $training_type = $input['training_type'] ?? '';
        $action = $input['action'] ?? '';
    }
    
    // Validate input
    if (empty($training_id) || empty($training_type) || empty($action)) {
        throw new Exception('Missing required parameters: training_id, training_type, action');
    }
    
    if (!is_numeric($training_id)) {
        throw new Exception('Invalid training ID: must be numeric');
    }
    
    if (!in_array($training_type, ['offline', 'online'])) {
        throw new Exception('Invalid training type: must be offline or online');
    }
    
    if (!in_array($action, ['hide', 'unhide'])) {
        throw new Exception('Invalid action: must be hide or unhide');
    }
    
    $training_id = (int)$training_id;
    $is_hidden = ($action === 'hide') ? 1 : 0;
    
    // Update visibility based on training type
    if ($training_type === 'offline') {
        // Check if training exists
        $check_query = "SELECT id, training_topic FROM offline_training WHERE id = ?";
        $stmt = $conn->prepare($check_query);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            throw new Exception('Offline training not found');
        }
        
        $training = $result->fetch_assoc();
        $stmt->close();
        
        // Update visibility
        $update_query = "UPDATE offline_training SET is_hidden = ? WHERE id = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("ii", $is_hidden, $training_id);
        
        if (!$stmt->execute()) {
            throw new Exception('Failed to update offline training visibility: ' . $stmt->error);
        }
        $stmt->close();
        
        $training_title = $training['training_topic'];
        
    } elseif ($training_type === 'online') {
        // Check if training exists
        $check_query = "SELECT id, training_topic FROM training_submissions WHERE id = ?";
        $stmt = $conn->prepare($check_query);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            throw new Exception('Online training not found');
        }
        
        $training = $result->fetch_assoc();
        $stmt->close();
        
        // Update visibility
        $update_query = "UPDATE training_submissions SET is_hidden = ? WHERE id = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("ii", $is_hidden, $training_id);
        
        if (!$stmt->execute()) {
            throw new Exception('Failed to update online training visibility: ' . $stmt->error);
        }
        $stmt->close();
        
        $training_title = $training['training_topic'];
    }
    
    // Log the action
    error_log("Training visibility updated: ID=$training_id, Type=$training_type, Action=$action, User=" . $_SESSION['user_id']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Training visibility updated successfully',
        'training_id' => $training_id,
        'training_type' => $training_type,
        'training_title' => $training_title,
        'action' => $action,
        'is_hidden' => $is_hidden
    ]);
    
} catch (Exception $e) {
    error_log("Error in toggle_training_visibility.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error updating training visibility: ' . $e->getMessage()
    ]);
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
