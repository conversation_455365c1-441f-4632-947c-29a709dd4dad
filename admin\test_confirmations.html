<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Confirmation Functions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 10px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
    </style>
</head>
<body>
    <h1>Test Confirmation Functions</h1>
    <p>Halaman ini untuk menguji semua fungsi konfirmasi yang digunakan di FAQ Management.</p>

    <div class="test-section">
        <h3>1. Basic Confirmation (confirmaction)</h3>
        <button class="btn btn-primary" onclick="testBasicConfirm()">Test Basic Confirm</button>
        <p>Fungsi: <code>confirmaction(message)</code></p>
    </div>

    <div class="test-section">
        <h3>2. Delete Confirmation (confirmDelete)</h3>
        <button class="btn btn-warning" onclick="testDeleteConfirm()">Test Delete Confirm</button>
        <p>Fungsi: <code>confirmDelete(message, callback)</code></p>
    </div>

    <div class="test-section">
        <h3>3. Dangerous Action Confirmation (confirmDangerousAction)</h3>
        <button class="btn btn-danger" onclick="testDangerousConfirm()">Test Dangerous Action Confirm</button>
        <p>Fungsi: <code>confirmDangerousAction(message, callback)</code></p>
    </div>

    <div class="test-section">
        <h3>4. FAQ Management Actions</h3>
        <button class="btn btn-success" onclick="testAnswerSubmission()">Test Answer Submission</button>
        <button class="btn btn-info" onclick="testRejectSubmission()">Test Reject Submission</button>
        <button class="btn btn-warning" onclick="testToggleVisibility()">Test Toggle Visibility</button>
        <button class="btn btn-danger" onclick="testDeleteSubmission()">Test Delete Submission</button>
        <button class="btn btn-danger" onclick="testDeleteFAQ()">Test Delete FAQ</button>
    </div>

    <div class="test-section">
        <h3>5. Test Results</h3>
        <div id="testResults"></div>
    </div>

    <script>
        // Copy all confirmation functions from manage_faq.php
        
        // Global confirmation function
        function confirmaction(message) {
            return confirm(message);
        }

        // Enhanced confirmation with custom styling
        function confirmDelete(message, callback) {
            if (confirm(message || 'Apakah Anda yakin ingin menghapus item ini?')) {
                if (typeof callback === 'function') {
                    callback();
                }
                return true;
            }
            return false;
        }

        // Confirmation for dangerous actions
        function confirmDangerousAction(message, callback) {
            const confirmed = confirm(
                (message || 'Tindakan ini tidak dapat dibatalkan!') + 
                '\n\nKlik OK untuk melanjutkan atau Cancel untuk membatalkan.'
            );
            
            if (confirmed && typeof callback === 'function') {
                callback();
            }
            
            return confirmed;
        }

        // Test functions
        function testBasicConfirm() {
            const result = confirmaction('Ini adalah test basic confirmation.\n\nApakah Anda ingin melanjutkan?');
            logResult('Basic Confirm', result);
        }

        function testDeleteConfirm() {
            const result = confirmDelete('Ini adalah test delete confirmation.\n\nItem akan dihapus permanen.', function() {
                logResult('Delete Confirm Callback', 'Callback executed successfully');
            });
            logResult('Delete Confirm', result);
        }

        function testDangerousConfirm() {
            const result = confirmDangerousAction('Ini adalah test dangerous action confirmation.\n\nTindakan ini sangat berbahaya!', function() {
                logResult('Dangerous Confirm Callback', 'Callback executed successfully');
            });
            logResult('Dangerous Confirm', result);
        }

        function testAnswerSubmission() {
            const confirmMessage = 'Apakah Anda yakin ingin menjawab submission ini?';
            
            if (confirmaction(confirmMessage)) {
                const answer = prompt('Test Answer Submission\n\nMasukkan jawaban:');
                
                if (answer && answer.trim()) {
                    logResult('Answer Submission', `Confirmed with answer: "${answer}"`);
                } else {
                    logResult('Answer Submission', 'Confirmed but no answer provided');
                }
            } else {
                logResult('Answer Submission', 'Cancelled');
            }
        }

        function testRejectSubmission() {
            const confirmMessage = 'Apakah Anda yakin ingin menolak submission ini?\n\n' +
                                  'Submission akan ditandai sebagai ditolak.\n\n' +
                                  'Klik OK untuk melanjutkan atau Cancel untuk membatalkan.';
            
            if (confirmaction(confirmMessage)) {
                const reason = prompt('Alasan penolakan (opsional):');
                logResult('Reject Submission', `Confirmed with reason: "${reason || 'No reason provided'}"`);
            } else {
                logResult('Reject Submission', 'Cancelled');
            }
        }

        function testToggleVisibility() {
            const isHidden = Math.random() > 0.5; // Random true/false
            const action = isHidden ? 'menyembunyikan' : 'menampilkan';
            const message = `Apakah Anda yakin ingin ${action} FAQ ini di halaman publik?\n\n` +
                           `FAQ akan ${isHidden ? 'disembunyikan' : 'ditampilkan'} untuk semua pengunjung.\n\n` +
                           'Klik OK untuk melanjutkan atau Cancel untuk membatalkan.';
            
            if (confirmaction(message)) {
                logResult('Toggle Visibility', `Confirmed to ${action} FAQ`);
            } else {
                logResult('Toggle Visibility', 'Cancelled');
            }
        }

        function testDeleteSubmission() {
            const message = 'Apakah Anda yakin ingin menghapus submission ini?\n\n' +
                           'Tindakan ini akan:\n' +
                           '• Menghapus submission secara permanen\n' +
                           '• Tidak dapat dibatalkan\n\n' +
                           'Klik OK untuk melanjutkan atau Cancel untuk membatalkan.';
            
            if (confirmDangerousAction(message)) {
                logResult('Delete Submission', 'Confirmed - submission would be deleted');
            } else {
                logResult('Delete Submission', 'Cancelled');
            }
        }

        function testDeleteFAQ() {
            const message = 'Apakah Anda yakin ingin menghapus FAQ ini?\n\n' +
                           'Tindakan ini akan:\n' +
                           '• Menghapus FAQ secara permanen\n' +
                           '• Tidak dapat dibatalkan\n\n' +
                           'Klik OK untuk melanjutkan atau Cancel untuk membatalkan.';
            
            if (confirmDangerousAction(message)) {
                logResult('Delete FAQ', 'Confirmed - FAQ would be deleted');
            } else {
                logResult('Delete FAQ', 'Cancelled');
            }
        }

        function logResult(testName, result) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const resultElement = document.createElement('div');
            resultElement.style.padding = '5px';
            resultElement.style.margin = '2px 0';
            resultElement.style.backgroundColor = result.includes('Confirmed') || result.includes('executed') ? '#d4edda' : '#f8d7da';
            resultElement.style.border = '1px solid ' + (result.includes('Confirmed') || result.includes('executed') ? '#c3e6cb' : '#f5c6cb');
            resultElement.style.borderRadius = '3px';
            resultElement.innerHTML = `<strong>[${timestamp}] ${testName}:</strong> ${result}`;
            resultsDiv.appendChild(resultElement);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logResult('System', 'Test page loaded successfully');
        });
    </script>
</body>
</html>
