<?php
/**
 * Script untuk memverifikasi bahwa semua navbar sudah memiliki notifikasi
 */

echo "🔍 VERIFYING NOTIFICATIONS IN ALL NAVBARS\n";
echo "==========================================\n\n";

// Daftar file navbar yang perlu dicek
$navbar_files = [
    'includes/navbar.php' => 'Main navbar',
    'config/navbar.php' => 'Config navbar',
    'config/navbarb.php' => 'Config navbar B',
    'config/navbara.php' => 'Config navbar A (used by dept_head/index.php)',
    'includes/fallback_navbar.php' => 'Fallback navbar'
];

// Daftar file yang menggunakan fallback navbar
$files_with_fallback = [
    'Dir/assistant_dashboard.php',
    'LnD/assistant_dashboard.php', 
    'dept_head/assistant_dashboard.php',
    'pemohon/assistant_dashboard.php',
    'Dir/instructor_dashboard.php',
    'LnD/instructor_dashboard.php',
    'dept_head/instructor_dashboard.php',
    'pemohon/instructor_dashboard.php'
];

$total_checked = 0;
$with_notifications = 0;
$without_notifications = 0;

echo "1️⃣ CHECKING NAVBAR FILES:\n";
echo "-------------------------\n";

foreach ($navbar_files as $file => $description) {
    echo "📄 $description ($file):\n";
    
    if (!file_exists($file)) {
        echo "   ❌ File not found\n";
        $without_notifications++;
    } else {
        $content = file_get_contents($file);
        
        // Cek apakah ada notifikasi
        $has_notifications = false;
        $notification_indicators = [
            'notifications_dropdown.php',
            'getUnreadNotifications',
            'fa-bell',
            'notificationsDropdown',
            'notification-dropdown'
        ];
        
        foreach ($notification_indicators as $indicator) {
            if (strpos($content, $indicator) !== false) {
                $has_notifications = true;
                break;
            }
        }
        
        if ($has_notifications) {
            echo "   ✅ Has notifications\n";
            $with_notifications++;
        } else {
            echo "   ❌ No notifications found\n";
            $without_notifications++;
        }
    }
    
    $total_checked++;
    echo "\n";
}

echo "2️⃣ CHECKING FILES WITH FALLBACK NAVBAR:\n";
echo "---------------------------------------\n";

$fallback_with_notifications = 0;
$fallback_without_notifications = 0;

foreach ($files_with_fallback as $file) {
    echo "📄 $file:\n";
    
    if (!file_exists($file)) {
        echo "   ❌ File not found\n";
        $fallback_without_notifications++;
    } else {
        $content = file_get_contents($file);
        
        // Cek apakah menggunakan fallback navbar yang baru
        $uses_new_fallback = strpos($content, "include '../includes/fallback_navbar.php'") !== false;
        
        if ($uses_new_fallback) {
            echo "   ✅ Uses new fallback navbar (with notifications)\n";
            $fallback_with_notifications++;
        } else {
            // Cek apakah ada notifikasi di fallback lama
            $has_notifications = false;
            $notification_indicators = [
                'fa-bell',
                'notificationsDropdown',
                'getUnreadNotifications'
            ];
            
            foreach ($notification_indicators as $indicator) {
                if (strpos($content, $indicator) !== false) {
                    $has_notifications = true;
                    break;
                }
            }
            
            if ($has_notifications) {
                echo "   ✅ Has notifications in old fallback\n";
                $fallback_with_notifications++;
            } else {
                echo "   ❌ No notifications in fallback\n";
                $fallback_without_notifications++;
            }
        }
    }
    
    echo "\n";
}

echo "3️⃣ TESTING NOTIFICATION FUNCTIONALITY:\n";
echo "--------------------------------------\n";

// Test notification helper
try {
    require_once __DIR__ . '/../includes/notification_helper.php';
    echo "✅ Notification helper loaded successfully\n";
    
    // Test dengan user pertama
    $user_query = "SELECT id, name FROM users WHERE is_active = 1 LIMIT 1";
    require_once __DIR__ . '/config.php';
    $user_result = $conn->query($user_query);
    
    if ($user_result && $user_result->num_rows > 0) {
        $user = $user_result->fetch_assoc();
        echo "✅ Test user found: {$user['name']}\n";
        
        // Test get notifications
        $notifications = getUnreadNotifications($user['id'], 1);
        echo "✅ getUnreadNotifications function works\n";
        echo "📊 User has " . count($notifications) . " unread notifications\n";
    } else {
        echo "⚠️  No test user found\n";
    }
    
    $conn->close();
} catch (Exception $e) {
    echo "❌ Error testing notification functionality: " . $e->getMessage() . "\n";
}

echo "\n📊 SUMMARY:\n";
echo "===========\n";
echo "📄 Navbar files checked: $total_checked\n";
echo "   ✅ With notifications: $with_notifications\n";
echo "   ❌ Without notifications: $without_notifications\n";
echo "\n";
echo "📱 Fallback files checked: " . count($files_with_fallback) . "\n";
echo "   ✅ With notifications: $fallback_with_notifications\n";
echo "   ❌ Without notifications: $fallback_without_notifications\n";

$total_files = $total_checked + count($files_with_fallback);
$total_with_notifications = $with_notifications + $fallback_with_notifications;
$total_without_notifications = $without_notifications + $fallback_without_notifications;

echo "\n🎯 OVERALL RESULT:\n";
echo "==================\n";
echo "📁 Total files: $total_files\n";
echo "✅ With notifications: $total_with_notifications\n";
echo "❌ Without notifications: $total_without_notifications\n";

$percentage = round(($total_with_notifications / $total_files) * 100, 1);
echo "📈 Coverage: $percentage%\n";

if ($total_without_notifications == 0) {
    echo "\n🎉 ALL NAVBARS HAVE NOTIFICATIONS!\n";
    echo "✅ Notification system is fully integrated across all navbars\n";
} else {
    echo "\n⚠️  Some navbars still need notification integration\n";
    echo "🔧 Files that need attention: $total_without_notifications\n";
}

echo "\n📋 NEXT STEPS:\n";
echo "==============\n";
echo "1. 🌐 Test in browser: Login and check notification bell icon\n";
echo "2. 🔔 Create demo notifications: php config/simple_demo_notifications.php\n";
echo "3. 👆 Click bell icon to test dropdown functionality\n";
echo "4. 📱 Test on different roles and pages\n";
echo "5. ✅ Verify mark as read functionality works\n";
?>
