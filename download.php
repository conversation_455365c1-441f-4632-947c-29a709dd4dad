<?php
/**
 * Secure File Download Handler
 * This script handles secure file downloads for assignment submissions
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once 'config/config.php';
include 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];
$role_id = $_SESSION['role_id'];

// Check if file ID is provided
$file_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$type = isset($_GET['type']) ? $_GET['type'] : '';

if ($file_id <= 0 || empty($type)) {
    header('HTTP/1.1 400 Bad Request');
    echo "Invalid request";
    exit();
}

// Define allowed file types
$allowed_extensions = ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt', 'jpg', 'jpeg', 'png', 'gif'];

// Initialize variables
$file_path = '';
$original_filename = '';
$content_type = '';

// Handle different file types
if ($type === 'assignment') {
    // Get submission information
    $query = "SELECT s.file_path, s.original_filename, a.class_id
              FROM training_assignment_submissions s
              JOIN training_assignments a ON s.assignment_id = a.id
              WHERE s.id = ?";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $file_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $file_info = $result->fetch_assoc();
    $stmt->close();

    if (!$file_info) {
        header('HTTP/1.1 404 Not Found');
        echo "File not found";
        exit();
    }

    // Check if user has access to this file
    $has_access = false;

    if ($role_id == 99) {
        // Admin has access to all files
        $has_access = true;
    } else if ($role_id == 1) {
        // Check if user is a participant in this class
        $access_query = "SELECT COUNT(*) as count FROM training_participants
                        WHERE user_id = ? AND class_id = ?";
        $stmt = $conn->prepare($access_query);
        $stmt->bind_param("ii", $user_id, $file_info['class_id']);
        $stmt->execute();
        $access_result = $stmt->get_result();
        $access_data = $access_result->fetch_assoc();
        $has_access = ($access_data['count'] > 0);
        $stmt->close();
    }

    if (!$has_access) {
        header('HTTP/1.1 403 Forbidden');
        echo "You don't have permission to access this file";
        exit();
    }

    $file_path = 'uploads/assignments/' . $file_info['file_path'];

    // Check if original_filename exists in the result set
    if (isset($file_info['original_filename']) && !empty($file_info['original_filename'])) {
        $original_filename = $file_info['original_filename'];
    } else {
        // Extract filename from file_path
        if (strpos($file_info['file_path'], '_') !== false) {
            $original_filename = substr($file_info['file_path'], strpos($file_info['file_path'], '_') + 1);
        } else {
            $original_filename = basename($file_info['file_path']);
        }
    }
}
// Add more file types as needed (e.g., materials, etc.)

// Check if file exists
if (!file_exists($file_path)) {
    header('HTTP/1.1 404 Not Found');
    echo "File not found";
    exit();
}

// Get file extension
$file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

// Check if file extension is allowed
if (!in_array($file_extension, $allowed_extensions)) {
    header('HTTP/1.1 403 Forbidden');
    echo "File type not allowed";
    exit();
}

// Set appropriate content type
switch ($file_extension) {
    case 'pdf':
        $content_type = 'application/pdf';
        break;
    case 'doc':
    case 'docx':
        $content_type = 'application/msword';
        break;
    case 'xls':
    case 'xlsx':
        $content_type = 'application/vnd.ms-excel';
        break;
    case 'ppt':
    case 'pptx':
        $content_type = 'application/vnd.ms-powerpoint';
        break;
    case 'txt':
        $content_type = 'text/plain';
        break;
    case 'jpg':
    case 'jpeg':
        $content_type = 'image/jpeg';
        break;
    case 'png':
        $content_type = 'image/png';
        break;
    case 'gif':
        $content_type = 'image/gif';
        break;
    default:
        $content_type = 'application/octet-stream';
}

// Set headers for download
header('Content-Type: ' . $content_type);
header('Content-Disposition: inline; filename="' . $original_filename . '"');
header('Content-Length: ' . filesize($file_path));
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Output file content
readfile($file_path);
exit();
