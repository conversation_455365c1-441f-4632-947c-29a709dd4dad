<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Judul halaman
$pageTitle = "Kelola Struktur Organisasi";
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    .container-form {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .card-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
    }

    .card-body {
        padding: 20px;
    }

    .menu-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .menu-item {
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        transition: all 0.3s ease;
    }

    .menu-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .menu-icon {
        font-size: 48px;
        margin-bottom: 15px;
        color: #a50000;
    }

    .menu-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .menu-description {
        color: #666;
        margin-bottom: 15px;
    }

    .menu-link {
        display: inline-block;
        background-color: #bf0000;
        color: white;
        padding: 8px 15px;
        border-radius: 4px;
        text-decoration: none;
        transition: background-color 0.3s ease;
    }

    .menu-link:hover {
        background-color: #a50000;
        color: white;
    }
</style>

<body>
<?php include '../config/navbarb.php'; ?>
<div class="jarak"></div>
<div class="container-form">
<button style="border-radius: 10px; margin-bottom:10px;">
        <a href="dashboard.php" style="text-decoration:none; color:white">Kembali</a>
    </button>
    <div class="card">
        <div class="card-header">
            <h2><?php echo $pageTitle; ?></h2>
        </div>
        <div class="card-body">
            <div class="menu-grid">
                <div class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="menu-title">Kelola Departemen</div>
                    <div class="menu-description">
                        Tambah, edit, dan hapus departemen dalam struktur organisasi.
                    </div>
                    <a href="manage_departments.php" class="menu-link">Buka</a>
                </div>

                <div class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="menu-title">Kelola Sub Departemen</div>
                    <div class="menu-description">
                        Tambah, edit, dan hapus sub departemen dalam struktur organisasi.
                    </div>
                    <a href="manage_sub_departments.php" class="menu-link">Buka</a>
                </div>

                <div class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="menu-title">Kelola Jabatan</div>
                    <div class="menu-description">
                        Tambah, edit, dan hapus jabatan dalam struktur organisasi.
                    </div>
                    <a href="manage_positions.php" class="menu-link">Buka</a>
                </div>

                <div class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="menu-title">Update Script.js</div>
                    <div class="menu-description">
                        Perbarui file script.js dengan data terbaru dari database.
                    </div>
                    <a href="update_script_js.php" class="menu-link">Buka</a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>
</body>
</html>
