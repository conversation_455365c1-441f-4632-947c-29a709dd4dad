<?php
include 'security.php';
include '../config/config.php';

// ===== GET USER DATA =====
// Semua user dapat mengakses halaman success training internal
$user_id = $_SESSION['user_id'];
// ===== END USER DATA =====

// Get training ID from URL
$training_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($training_id <= 0) {
    header('Location: internal_training_form.php');
    exit();
}

// Get training details
$query = "SELECT * FROM offline_training WHERE id = ? AND created_by = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $training_id, $user_id);
$stmt->execute();
$result = $stmt->get_result();
$training = $result->fetch_assoc();
$stmt->close();

if (!$training) {
    header('Location: internal_training_form.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    .success-container {
        max-width: 800px;
        margin: 50px auto;
        background: #ffffff;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .success-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 40px;
        text-align: center;
    }

    .success-header .icon {
        font-size: 4rem;
        margin-bottom: 20px;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }

    .success-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 600;
    }

    .success-header p {
        margin: 15px 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    .success-content {
        padding: 40px;
    }

    .training-details {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 30px;
        border-left: 5px solid #28a745;
    }

    .training-details h3 {
        color: #333;
        margin-bottom: 20px;
        font-size: 1.3rem;
        font-weight: 600;
    }

    .detail-row {
        display: flex;
        margin-bottom: 15px;
        align-items: flex-start;
    }

    .detail-label {
        font-weight: 600;
        color: #555;
        min-width: 150px;
        margin-right: 15px;
    }

    .detail-value {
        color: #333;
        flex: 1;
    }

    .next-steps {
        background: #e3f2fd;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 30px;
        border-left: 5px solid #2196f3;
    }

    .next-steps h3 {
        color: #1976d2;
        margin-bottom: 20px;
        font-size: 1.3rem;
        font-weight: 600;
    }

    .next-steps ul {
        margin: 0;
        padding-left: 20px;
    }

    .next-steps li {
        margin-bottom: 10px;
        color: #333;
    }

    .action-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn {
        padding: 12px 25px;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #c40000 0%,rgb(255, 7, 7) 100%);
        color: white;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-success {
        background: #28a745;
        color: white;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        border: none;
    }

    .alert-info {
        background-color: #d1ecf1;
        color: #0c5460;
        border-left: 4px solid #17a2b8;
    }

    @media (max-width: 768px) {
        .success-container {
            margin: 20px;
            border-radius: 10px;
        }

        .success-header {
            padding: 30px 20px;
        }

        .success-header h1 {
            font-size: 2rem;
        }

        .success-content {
            padding: 30px 20px;
        }

        .detail-row {
            flex-direction: column;
        }

        .detail-label {
            min-width: auto;
            margin-bottom: 5px;
        }

        .action-buttons {
            flex-direction: column;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>

<body>
    <?php include '../config/navbara.php'; ?>
    <div class="jarak-form"></div>

    <div class="container-form">
        <div class="success-container">
            <div class="success-header">
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h1>Pengajuan Berhasil!</h1>
                <p>Training internal Anda telah berhasil diajukan</p>
            </div>

            <div class="success-content">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>ID Pengajuan: #<?= htmlspecialchars($training_id) ?></strong><br>
                    Simpan ID ini untuk referensi dan follow-up pengajuan Anda.
                </div>

                <div class="training-details">
                    <h3><i class="fas fa-clipboard-list"></i> Detail Pengajuan Training</h3>

                    <div class="detail-row">
                        <div class="detail-label">Topik Training:</div>
                        <div class="detail-value"><?= htmlspecialchars($training['training_topic']) ?></div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">Tanggal Mulai:</div>
                        <div class="detail-value">
                            <?= date('d F Y', strtotime($training['start_date'])) ?>
                            <?php if (!empty($training['end_date']) && $training['end_date'] !== $training['start_date']): ?>
                                - <?= date('d F Y', strtotime($training['end_date'])) ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if (!empty($training['training_time_start']) || !empty($training['training_time_end'])): ?>
                    <div class="detail-row">
                        <div class="detail-label">Waktu:</div>
                        <div class="detail-value">
                            <?= !empty($training['training_time_start']) ? date('H:i', strtotime($training['training_time_start'])) : '-' ?>
                            s/d
                            <?= !empty($training['training_time_end']) ? date('H:i', strtotime($training['training_time_end'])) : '-' ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($training['location'])): ?>
                    <div class="detail-row">
                        <div class="detail-label">Lokasi:</div>
                        <div class="detail-value"><?= htmlspecialchars($training['location']) ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($training['max_participants'])): ?>
                    <div class="detail-row">
                        <div class="detail-label">Est. Peserta:</div>
                        <div class="detail-value"><?= htmlspecialchars($training['max_participants']) ?> orang</div>
                    </div>
                    <?php endif; ?>

                    <div class="detail-row">
                        <div class="detail-label">Status:</div>
                        <div class="detail-value">
                            <span style="background: #ffc107; color: #212529; padding: 4px 12px; border-radius: 20px; font-size: 0.9rem;">
                                <?= htmlspecialchars($training['status']) ?>
                            </span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">Tanggal Pengajuan:</div>
                        <div class="detail-value"><?= date('d F Y H:i', strtotime($training['created_at'])) ?></div>
                    </div>
                </div>

                <div class="next-steps">
                    <h3><i class="fas fa-route"></i> Langkah Selanjutnya</h3>
                    <ul>
                        <li><strong>Review Tim L&D:</strong> Tim Learning & Development akan meninjau pengajuan Anda dalam 2-3 hari kerja</li>
                        <li><strong>Koordinasi:</strong> Tim L&D akan menghubungi Anda untuk koordinasi detail training (trainer, materi, jadwal final)</li>
                        <li><strong>Persetujuan:</strong> Setelah detail disepakati, training akan disetujui dan dijadwalkan</li>
                        <li><strong>Pelaksanaan:</strong> Training akan dilaksanakan sesuai jadwal yang telah disepakati</li>
                        <li><strong>Follow-up:</strong> Anda dapat memantau status pengajuan melalui dashboard atau menghubungi tim L&D</li>
                    </ul>
                </div>

                <div class="action-buttons">
                    <a href="dashboard.php" class="btn btn-primary">
                        <i class="fas fa-home"></i> Kembali ke Dashboard
                    </a>
                    <a href="internal_training_form.php" class="btn btn-secondary">
                        <i class="fas fa-plus"></i> Ajukan Training Lain
                    </a>
                    <!-- <a href="javascript:window.print()" class="btn btn-success">
                        <i class="fas fa-print"></i> Cetak Konfirmasi
                    </a> -->
                </div>
            </div>
        </div>
    </div>

    <footer>
        <?php include '../config/footer.php'; ?>
    </footer>

    <script>
        // Auto-hide any alerts after 10 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 10000);

        // Add some celebration animation
        document.addEventListener('DOMContentLoaded', function() {
            // Create confetti effect (simple version)
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];

            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    createConfetti();
                }, i * 100);
            }
        });

        function createConfetti() {
            const confetti = document.createElement('div');
            confetti.style.position = 'fixed';
            confetti.style.width = '10px';
            confetti.style.height = '10px';
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.left = Math.random() * 100 + 'vw';
            confetti.style.top = '-10px';
            confetti.style.zIndex = '9999';
            confetti.style.borderRadius = '50%';
            confetti.style.pointerEvents = 'none';

            document.body.appendChild(confetti);

            const animation = confetti.animate([
                { transform: 'translateY(0) rotate(0deg)', opacity: 1 },
                { transform: 'translateY(100vh) rotate(360deg)', opacity: 0 }
            ], {
                duration: 3000,
                easing: 'linear'
            });

            animation.onfinish = () => confetti.remove();
        }
    </script>
</body>
</html>
