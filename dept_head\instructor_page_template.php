<?php
/**
 * Template for Instructor Pages
 * This template provides a common structure for all instructor pages
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';
include '../includes/class_role_helper.php';
include '../includes/notification_helper.php';
include '../includes/permission_helper.php';

// Get class ID from URL
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$user_id = $_SESSION['user_id'];

// Check if class exists
$class_query = "SELECT c.*, t.training_topic, t.training_type 
               FROM training_classes c
               JOIN training_submissions t ON c.training_id = t.id
               WHERE c.id = ?";
$stmt = $conn->prepare($class_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$class = $result->fetch_assoc();
$stmt->close();

if (!$class) {
    $_SESSION['error'] = "Kelas tidak ditemukan.";
    header("Location: classroom.php");
    exit();
}

// Check if user is an instructor in this class
if (!isInstructor($user_id, $class_id)) {
    $_SESSION['error'] = "Anda tidak memiliki akses sebagai instruktur untuk kelas ini.";
    header("Location: classroom_detail.php?id=$class_id");
    exit();
}

// Set page title and other variables
$page_title = "TITLE_PLACEHOLDER - " . $class['title'];
$page_icon = "ICON_PLACEHOLDER";
$page_description = "DESCRIPTION_PLACEHOLDER";

// Process form submission if needed
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // FORM_PROCESSING_PLACEHOLDER
}

// Get data for the page
// DATA_RETRIEVAL_PLACEHOLDER

?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="../asset/css/bootstrap.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../asset/css/styles.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Check if CSS files exist and provide fallback CDN links -->
    <script>
        // Function to check if a CSS file exists
        function checkCssFile(url) {
            var http = new XMLHttpRequest();
            http.open('HEAD', url, false);
            http.send();
            return http.status != 404;
        }
        
        // Check Bootstrap CSS
        if (!checkCssFile('../asset/css/bootstrap.min.css')) {
            document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">');
        }
        
        // Check Font Awesome
        if (!checkCssFile('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css')) {
            document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">');
        }
    </script>
    <style>
        .page-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .action-button {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .breadcrumb-container {
            margin-bottom: 20px;
        }
        
        /* ADDITIONAL_STYLES_PLACEHOLDER */
    </style>
</head>
<body>
    <?php
    // Try to include navbar with different paths
    $navbar_included = false;
    $navbar_paths = [
        '../includes/navbar.php',
        dirname(__DIR__) . '/includes/navbar.php',
        '../config/navbara.php'
    ];
    
    foreach ($navbar_paths as $path) {
        if (file_exists($path)) {
            include $path;
            $navbar_included = true;
            break;
        }
    }
    
    // Fallback navbar if none of the includes work
    if (!$navbar_included) {
    ?>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container-fluid">
            <a class="navbar-brand" href="../pemohon/index.php">Training System</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../pemohon/index.php">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../pemohon/classroom.php">
                            <i class="fas fa-chalkboard"></i> Kelas
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user"></i> <?= htmlspecialchars($_SESSION['full_name'] ?? 'User') ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="../pemohon/profile.php"><i class="fas fa-user me-2"></i> Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../view/logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <?php } ?>
    
    <!-- Spacer after navbar -->
    <div style="margin-top: 20px;"></div>
    
    <div class="page-container">
        <!-- Breadcrumb -->
        <div class="breadcrumb-container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="classroom.php">Kelas</a></li>
                    <li class="breadcrumb-item"><a href="classroom_detail.php?id=<?= $class_id ?>"><?= htmlspecialchars($class['title']) ?></a></li>
                    <li class="breadcrumb-item"><a href="instructor_dashboard.php?class_id=<?= $class_id ?>">Panel Instruktur</a></li>
                    <li class="breadcrumb-item active" aria-current="page">BREADCRUMB_PLACEHOLDER</li>
                </ol>
            </nav>
        </div>
        
        <!-- Page Header -->
        <div class="page-header">
            <div class="container">
                <h1><i class="fas <?= $page_icon ?>"></i> <?= str_replace("TITLE_PLACEHOLDER", "", $page_title) ?></h1>
                <p class="mb-0"><?= $page_description ?></p>
            </div>
        </div>
        
        <!-- Alerts -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <?= $_SESSION['success'] ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <?= $_SESSION['error'] ?>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>
        
        <!-- Main Content -->
        <div class="content-card">
            <!-- MAIN_CONTENT_PLACEHOLDER -->
        </div>
        
        <!-- Navigation Buttons -->
        <div class="d-flex justify-content-between mt-4">
            <a href="instructor_dashboard.php?class_id=<?= $class_id ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali ke Panel Instruktur
            </a>
            
            <!-- ADDITIONAL_BUTTONS_PLACEHOLDER -->
        </div>
    </div>
    
    <?php 
    // Include footer
    $footer_included = false;
    $footer_paths = [
        '../includes/footer.php',
        dirname(__DIR__) . '/includes/footer.php',
        '../config/footer.php'
    ];
    
    foreach ($footer_paths as $path) {
        if (file_exists($path)) {
            include $path;
            $footer_included = true;
            break;
        }
    }
    
    // Fallback footer if none of the includes work
    if (!$footer_included) {
    ?>
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">&copy; <?= date('Y') ?> Training System. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">Version 1.0</p>
                </div>
            </div>
        </div>
    </footer>
    
    <style>
        .footer {
            margin-top: 50px;
            border-top: 1px solid #e9ecef;
        }
    </style>
    <?php } ?>
    
    <!-- Bootstrap JS Bundle -->
    <script src="../asset/js/bootstrap.bundle.min.js"></script>
    
    <!-- Fallback to CDN if local file doesn't exist -->
    <script>
        if(typeof($.fn) === 'undefined') {
            document.write('<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"><\/script>');
        }
    </script>
    
    <!-- Initialize Bootstrap components -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // Initialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
            
            // Initialize dropdowns
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });
            
            // ADDITIONAL_SCRIPTS_PLACEHOLDER
        });
    </script>
</body>
</html>
