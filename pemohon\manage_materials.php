<?php
/**
 * Manage Materials Page for Instructor
 * This page allows instructors to manage materials for a class
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';
include '../includes/class_role_helper.php';
include '../includes/notification_helper.php';
include '../includes/permission_helper.php';

// Get class ID from URL
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$user_id = $_SESSION['user_id'];

// Check if class exists
$class_query = "SELECT c.*, t.training_topic, t.training_type 
               FROM training_classes c
               JOIN training_submissions t ON c.training_id = t.id
               WHERE c.id = ?";
$stmt = $conn->prepare($class_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$class = $result->fetch_assoc();
$stmt->close();

if (!$class) {
    $_SESSION['error'] = "Kelas tidak ditemukan.";
    header("Location: classroom.php");
    exit();
}

// Check if user is an instructor in this class
if (!isInstructor($user_id, $class_id)) {
    $_SESSION['error'] = "Anda tidak memiliki akses sebagai instruktur untuk kelas ini.";
    header("Location: classroom_detail.php?id=$class_id");
    exit();
}

// Set page title and other variables
$page_title = "Kelola Materi - " . $class['title'];
$page_icon = "fa-book";
$page_description = "Kelola materi pembelajaran untuk kelas " . $class['title'];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Add new material
        if ($_POST['action'] === 'add') {
            $title = trim($_POST['title']);
            $description = trim($_POST['description']);
            $content = trim($_POST['content']);
            $is_published = isset($_POST['is_published']) ? 1 : 0;
            
            if (empty($title)) {
                $_SESSION['error'] = "Judul materi tidak boleh kosong.";
            } else {
                $insert_query = "INSERT INTO training_materials (class_id, title, description, content, is_published, created_by, created_at) 
                                VALUES (?, ?, ?, ?, ?, ?, NOW())";
                $stmt = $conn->prepare($insert_query);
                $stmt->bind_param("isssii", $class_id, $title, $description, $content, $is_published, $user_id);
                
                if ($stmt->execute()) {
                    $_SESSION['success'] = "Materi berhasil ditambahkan.";
                    
                    // If published, notify students
                    if ($is_published) {
                        $material_id = $stmt->insert_id;
                        
                        // Get all students in the class
                        $students_query = "SELECT user_id FROM training_participants 
                                          WHERE class_id = ? AND role = 'student' AND status = 'active'";
                        $stmt_students = $conn->prepare($students_query);
                        $stmt_students->bind_param("i", $class_id);
                        $stmt_students->execute();
                        $students_result = $stmt_students->get_result();
                        
                        while ($student = $students_result->fetch_assoc()) {
                            // Create notification for each student
                            createNotification(
                                $student['user_id'],
                                $class_id,
                                "Materi Baru: $title",
                                "Materi baru telah ditambahkan ke kelas " . $class['title'],
                                "info"
                            );
                        }
                    }
                } else {
                    $_SESSION['error'] = "Gagal menambahkan materi: " . $stmt->error;
                }
                $stmt->close();
            }
        }
        
        // Update material
        else if ($_POST['action'] === 'update' && isset($_POST['material_id'])) {
            $material_id = intval($_POST['material_id']);
            $title = trim($_POST['title']);
            $description = trim($_POST['description']);
            $content = trim($_POST['content']);
            $is_published = isset($_POST['is_published']) ? 1 : 0;
            
            if (empty($title)) {
                $_SESSION['error'] = "Judul materi tidak boleh kosong.";
            } else {
                $update_query = "UPDATE training_materials 
                                SET title = ?, description = ?, content = ?, is_published = ?, updated_at = NOW() 
                                WHERE id = ? AND class_id = ?";
                $stmt = $conn->prepare($update_query);
                $stmt->bind_param("sssiii", $title, $description, $content, $is_published, $material_id, $class_id);
                
                if ($stmt->execute()) {
                    $_SESSION['success'] = "Materi berhasil diperbarui.";
                } else {
                    $_SESSION['error'] = "Gagal memperbarui materi: " . $stmt->error;
                }
                $stmt->close();
            }
        }
        
        // Delete material
        else if ($_POST['action'] === 'delete' && isset($_POST['material_id'])) {
            $material_id = intval($_POST['material_id']);
            
            $delete_query = "DELETE FROM training_materials WHERE id = ? AND class_id = ?";
            $stmt = $conn->prepare($delete_query);
            $stmt->bind_param("ii", $material_id, $class_id);
            
            if ($stmt->execute()) {
                $_SESSION['success'] = "Materi berhasil dihapus.";
            } else {
                $_SESSION['error'] = "Gagal menghapus materi: " . $stmt->error;
            }
            $stmt->close();
        }
        
        // Redirect to avoid form resubmission
        header("Location: manage_materials.php?class_id=$class_id");
        exit();
    }
}

// Get all materials for this class
$materials_query = "SELECT m.*, u.name as creator_name 
                   FROM training_materials m
                   JOIN users u ON m.created_by = u.id
                   WHERE m.class_id = ?
                   ORDER BY m.created_at DESC";
$stmt = $conn->prepare($materials_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$materials = [];
while ($row = $result->fetch_assoc()) {
    $materials[] = $row;
}
$stmt->close();

?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="../asset/css/bootstrap.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../asset/css/styles.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Check if CSS files exist and provide fallback CDN links -->
    <script>
        // Function to check if a CSS file exists
        function checkCssFile(url) {
            var http = new XMLHttpRequest();
            http.open('HEAD', url, false);
            http.send();
            return http.status != 404;
        }
        
        // Check Bootstrap CSS
        if (!checkCssFile('../asset/css/bootstrap.min.css')) {
            document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">');
        }
        
        // Check Font Awesome
        if (!checkCssFile('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css')) {
            document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">');
        }
    </script>
    <style>
        .page-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .material-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .material-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .material-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .material-description {
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        .material-meta {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .material-actions {
            margin-top: 10px;
        }
        
        .badge-published {
            background-color: #28a745;
            color: white;
        }
        
        .badge-draft {
            background-color: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <?php
    // Try to include navbar with different paths
    $navbar_included = false;
    $navbar_paths = [
        '../includes/navbar.php',
        dirname(__DIR__) . '/includes/navbar.php',
        '../config/navbara.php'
    ];
    
    foreach ($navbar_paths as $path) {
        if (file_exists($path)) {
            include $path;
            $navbar_included = true;
            break;
        }
    }
    
    // Fallback navbar if none of the includes work
    if (!$navbar_included) {
    ?>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container-fluid">
            <a class="navbar-brand" href="../pemohon/index.php">Training System</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../pemohon/index.php">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../pemohon/classroom.php">
                            <i class="fas fa-chalkboard"></i> Kelas
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user"></i> <?= htmlspecialchars($_SESSION['full_name'] ?? 'User') ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="../pemohon/profile.php"><i class="fas fa-user me-2"></i> Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../view/logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <?php } ?>
    
    <!-- Spacer after navbar -->
    <div style="margin-top: 20px;"></div>
    
    <div class="page-container">
        <!-- Breadcrumb -->
        <div class="breadcrumb-container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="classroom.php">Kelas</a></li>
                    <li class="breadcrumb-item"><a href="classroom_detail.php?id=<?= $class_id ?>"><?= htmlspecialchars($class['title']) ?></a></li>
                    <li class="breadcrumb-item"><a href="instructor_dashboard.php?class_id=<?= $class_id ?>">Panel Instruktur</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Kelola Materi</li>
                </ol>
            </nav>
        </div>
        
        <!-- Page Header -->
        <div class="page-header">
            <div class="container">
                <h1><i class="fas <?= $page_icon ?>"></i> Kelola Materi</h1>
                <p class="mb-0"><?= $page_description ?></p>
            </div>
        </div>
        
        <!-- Alerts -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <?= $_SESSION['success'] ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <?= $_SESSION['error'] ?>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>
        
        <!-- Add New Material Button -->
        <div class="mb-4">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMaterialModal">
                <i class="fas fa-plus"></i> Tambah Materi Baru
            </button>
        </div>
        
        <!-- Materials List -->
        <div class="content-card">
            <h3 class="mb-4">Daftar Materi</h3>
            
            <?php if (empty($materials)): ?>
                <div class="alert alert-info">
                    Belum ada materi untuk kelas ini. Klik tombol "Tambah Materi Baru" untuk membuat materi.
                </div>
            <?php else: ?>
                <?php foreach ($materials as $material): ?>
                    <div class="material-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="material-title">
                                    <?= htmlspecialchars($material['title']) ?>
                                    <?php if ($material['is_published']): ?>
                                        <span class="badge badge-published">Dipublikasikan</span>
                                    <?php else: ?>
                                        <span class="badge badge-draft">Draft</span>
                                    <?php endif; ?>
                                </div>
                                <div class="material-description">
                                    <?= !empty($material['description']) ? htmlspecialchars($material['description']) : 'Tidak ada deskripsi' ?>
                                </div>
                                <div class="material-meta">
                                    <span><i class="fas fa-user"></i> <?= htmlspecialchars($material['creator_name']) ?></span>
                                    <span class="ms-3"><i class="fas fa-calendar"></i> <?= date('d M Y H:i', strtotime($material['created_at'])) ?></span>
                                </div>
                            </div>
                            <div class="material-actions">
                                <button type="button" class="btn btn-sm btn-primary edit-material" 
                                        data-id="<?= $material['id'] ?>"
                                        data-title="<?= htmlspecialchars($material['title']) ?>"
                                        data-description="<?= htmlspecialchars($material['description']) ?>"
                                        data-content="<?= htmlspecialchars($material['content']) ?>"
                                        data-published="<?= $material['is_published'] ?>"
                                        data-bs-toggle="modal" data-bs-target="#editMaterialModal">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button type="button" class="btn btn-sm btn-danger delete-material" 
                                        data-id="<?= $material['id'] ?>"
                                        data-title="<?= htmlspecialchars($material['title']) ?>"
                                        data-bs-toggle="modal" data-bs-target="#deleteMaterialModal">
                                    <i class="fas fa-trash"></i> Hapus
                                </button>
                                <a href="classroom_material.php?id=<?= $material['id'] ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> Lihat
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <!-- Navigation Buttons -->
        <div class="d-flex justify-content-between mt-4">
            <a href="instructor_dashboard.php?class_id=<?= $class_id ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali ke Panel Instruktur
            </a>
        </div>
    </div>
    
    <!-- Add Material Modal -->
    <div class="modal fade" id="addMaterialModal" tabindex="-1" aria-labelledby="addMaterialModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addMaterialModalLabel">Tambah Materi Baru</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="manage_materials.php?class_id=<?= $class_id ?>" method="post">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">Judul Materi <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="content" class="form-label">Konten Materi</label>
                            <textarea class="form-control" id="content" name="content" rows="10"></textarea>
                            <small class="text-muted">Anda dapat menggunakan format Markdown untuk konten.</small>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_published" name="is_published" value="1">
                            <label class="form-check-label" for="is_published">Publikasikan materi ini</label>
                            <small class="form-text text-muted d-block">Jika dicentang, materi akan langsung terlihat oleh peserta kelas.</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan Materi</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Edit Material Modal -->
    <div class="modal fade" id="editMaterialModal" tabindex="-1" aria-labelledby="editMaterialModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editMaterialModalLabel">Edit Materi</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="manage_materials.php?class_id=<?= $class_id ?>" method="post">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="material_id" id="edit_material_id">
                        
                        <div class="mb-3">
                            <label for="edit_title" class="form-label">Judul Materi <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_title" name="title" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="2"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_content" class="form-label">Konten Materi</label>
                            <textarea class="form-control" id="edit_content" name="content" rows="10"></textarea>
                            <small class="text-muted">Anda dapat menggunakan format Markdown untuk konten.</small>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="edit_is_published" name="is_published" value="1">
                            <label class="form-check-label" for="edit_is_published">Publikasikan materi ini</label>
                            <small class="form-text text-muted d-block">Jika dicentang, materi akan langsung terlihat oleh peserta kelas.</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Material Modal -->
    <div class="modal fade" id="deleteMaterialModal" tabindex="-1" aria-labelledby="deleteMaterialModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteMaterialModalLabel">Konfirmasi Hapus Materi</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin menghapus materi "<span id="delete_material_title"></span>"?</p>
                    <p class="text-danger">Tindakan ini tidak dapat dibatalkan.</p>
                </div>
                <div class="modal-footer">
                    <form action="manage_materials.php?class_id=<?= $class_id ?>" method="post">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="material_id" id="delete_material_id">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-danger">Hapus Materi</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <?php 
    // Include footer
    $footer_included = false;
    $footer_paths = [
        '../includes/footer.php',
        dirname(__DIR__) . '/includes/footer.php',
        '../config/footer.php'
    ];
    
    foreach ($footer_paths as $path) {
        if (file_exists($path)) {
            include $path;
            $footer_included = true;
            break;
        }
    }
    
    // Fallback footer if none of the includes work
    if (!$footer_included) {
    ?>
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">&copy; <?= date('Y') ?> Training System. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">Version 1.0</p>
                </div>
            </div>
        </div>
    </footer>
    
    <style>
        .footer {
            margin-top: 50px;
            border-top: 1px solid #e9ecef;
        }
    </style>
    <?php } ?>
    
    <!-- Bootstrap JS Bundle -->
    <script src="../asset/js/bootstrap.bundle.min.js"></script>
    
    <!-- Fallback to CDN if local file doesn't exist -->
    <script>
        if(typeof($.fn) === 'undefined') {
            document.write('<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"><\/script>');
        }
    </script>
    
    <!-- Initialize Bootstrap components -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // Initialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
            
            // Initialize dropdowns
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });
            
            // Handle edit material modal
            document.querySelectorAll('.edit-material').forEach(function(button) {
                button.addEventListener('click', function() {
                    document.getElementById('edit_material_id').value = this.getAttribute('data-id');
                    document.getElementById('edit_title').value = this.getAttribute('data-title');
                    document.getElementById('edit_description').value = this.getAttribute('data-description');
                    document.getElementById('edit_content').value = this.getAttribute('data-content');
                    document.getElementById('edit_is_published').checked = this.getAttribute('data-published') === '1';
                });
            });
            
            // Handle delete material modal
            document.querySelectorAll('.delete-material').forEach(function(button) {
                button.addEventListener('click', function() {
                    document.getElementById('delete_material_id').value = this.getAttribute('data-id');
                    document.getElementById('delete_material_title').textContent = this.getAttribute('data-title');
                });
            });
        });
    </script>
</body>
</html>
