
<!-- CSS moved to dashboard-style.css -->

<div class="welcome-section">
        <h1>Dashboard Persetujuan Training</h1>
        <p>Selamat datang, <?= htmlspecialchars($_SESSION['user_name'] ?? 'LnD') ?></p>
    </div>

    <div class="stats-section">
        <div class="stat-card">
            <h3>Total Pengajuan</h3>
            <p><?= $stats['total'] ?></p>
        </div>
        <div class="stat-card">
            <h3>Menunggu Persetujuan</h3>
            <p><?= $stats['pending'] ?></p>
        </div>
        <div class="stat-card">
            <h3>Disetujui</h3>
            <p><?= $stats['approved'] ?></p>
        </div>
        <div class="stat-card">
            <h3>Dibatalkan</h3>
            <p><?= $stats['rejected'] ?></p>
        </div>
    </div>

    <!-- Desktop Table View -->
    <div class="submissions-table desktop-table">
        <div class="table-responsive">
            <table>
                <thead>
                    <tr>
                        <th>Topik Training</th>
                        <th>Pengaju</th>
                        <th>Departemen</th>
                        <th>Peserta</th>
                        <th>Tanggal</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($pending_for_lnd)): ?>
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 20px;">
                                Tidak ada training yang perlu di-approve saat ini.
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($pending_for_lnd as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['training_topic']) ?></td>
                                <td><?= htmlspecialchars($row['requester_name']) ?></td>
                                <td>
                                    <span class="department-badge">
                                        <i class="fas fa-building"></i>
                                        <?= htmlspecialchars($row['departemen'] ?? 'N/A') ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="participants-count">
                                        <i class="fas fa-users"></i>
                                        <?= $row['total_participants'] ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    if (!empty($row['start_date'])) {
                                        // Ada tanggal training
                                        if ($row['is_confirmed'] == 1) {
                                            // Tanggal sudah dikonfirmasi/fixed
                                            if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                                                // Multi-day training yang sudah dikonfirmasi
                                                echo '<span class="date-confirmed" style="color: #2e7d32; background-color: rgba(76, 175, 80, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #2e7d32; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">';
                                                echo '<i class="fas fa-calendar-check"></i> ';
                                                echo htmlspecialchars($row['start_date']) . ' s/d ' . htmlspecialchars($row['end_date']);
                                                echo '<span style="background-color: rgba(76, 175, 80, 0.2); color: #155724; border: 1px solid rgba(76, 175, 80, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Dikonfirmasi</span>';
                                                echo '</span>';
                                            } else {
                                                // Single day training yang sudah dikonfirmasi
                                                echo '<span class="date-confirmed" style="color: #2e7d32; background-color: rgba(76, 175, 80, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #2e7d32; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">';
                                                echo '<i class="fas fa-calendar-check"></i> ';
                                                echo htmlspecialchars($row['start_date']);
                                                echo '<span style="background-color: rgba(76, 175, 80, 0.2); color: #155724; border: 1px solid rgba(76, 175, 80, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Dikonfirmasi</span>';
                                                echo '</span>';
                                            }
                                        } else {
                                            // Tanggal belum dikonfirmasi (pending)
                                            if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                                                // Multi-day training yang masih pending
                                                echo '<span class="date-pending" style="color: #f57c00; background-color: rgba(255, 193, 7, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #f57c00; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">';
                                                echo '<i class="fas fa-calendar-alt"></i> ';
                                                echo htmlspecialchars($row['start_date']) . ' s/d ' . htmlspecialchars($row['end_date']);
                                                echo '<span style="background-color: rgba(255, 193, 7, 0.2); color: #856404; border: 1px solid rgba(255, 193, 7, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Pending</span>';
                                                echo '</span>';
                                            } else {
                                                // Single day training yang masih pending
                                                echo '<span class="date-pending" style="color: #f57c00; background-color: rgba(255, 193, 7, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #f57c00; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">';
                                                echo '<i class="fas fa-calendar-alt"></i> ';
                                                echo htmlspecialchars($row['start_date']);
                                                echo '<span style="background-color: rgba(255, 193, 7, 0.2); color: #856404; border: 1px solid rgba(255, 193, 7, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Pending</span>';
                                                echo '</span>';
                                            }
                                        }
                                    } else {
                                        // Belum ada tanggal sama sekali
                                        echo '<span class="date-pending" style="color: #f57c00; background-color: rgba(255, 193, 7, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #f57c00; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">';
                                        echo '<i class="fas fa-calendar-times"></i> ';
                                        echo 'Belum Ditentukan';
                                        echo '<span style="background-color: rgba(255, 193, 7, 0.2); color: #856404; border: 1px solid rgba(255, 193, 7, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Menunggu</span>';
                                        echo '</span>';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php if (isset($row['status']) && $row['status'] == 'Rejected'): ?>
                                        <span class="status-badge status-rejected">
                                            <i class="fas fa-times-circle"></i>
                                            <?php
                                            if (!empty($row['rejected_by'])) {
                                                // Ambil nama user yang membatalkan
                                                $rejecter_query = "SELECT name, jabatan FROM users WHERE id = ?";
                                                $rejecter_stmt = $conn->prepare($rejecter_query);
                                                $rejecter_stmt->bind_param("i", $row['rejected_by']);
                                                $rejecter_stmt->execute();
                                                $rejecter_result = $rejecter_stmt->get_result();

                                                if ($rejecter_row = $rejecter_result->fetch_assoc()) {
                                                    $rejecter_name = $rejecter_row['name'];
                                                    $rejecter_position = $rejecter_row['jabatan'];
                                                    echo "Training telah dibatalkan oleh " . htmlspecialchars($rejecter_name) . " (" . htmlspecialchars($rejecter_position) . ")";

                                                    if (!empty($row['rejected_at'])) {
                                                        $rejected_date = new DateTime($row['rejected_at']);
                                                        echo " pada " . $rejected_date->format('d-m-Y H:i');
                                                    }
                                                } else {
                                                    echo "Training telah dibatalkan";
                                                }
                                                $rejecter_stmt->close();
                                            } else {
                                                echo "Training telah dibatalkan";
                                            }
                                            ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="status-badge status-pending">
                                            <i class="fas fa-hourglass-half"></i>
                                            Pending
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="detail_training.php?id=<?= $row['id'] ?>" class="action-button">
                                        <i class="fas fa-info-circle"></i>
                                        Detail
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Mobile Card View -->
    <div class="mobile-card">
        <?php if (empty($pending_for_lnd)): ?>
            <div class="empty-state">
                <i class="fas fa-clipboard-list"></i>
                <h3>Tidak Ada Pengajuan</h3>
                <p>Tidak ada training yang perlu di-approve saat ini.</p>
            </div>
        <?php else: ?>
            <?php foreach ($pending_for_lnd as $row): ?>
                <div class="mobile-submission-card">
                    <div class="mobile-card-header">
                        <?= htmlspecialchars($row['training_topic']) ?>
                    </div>
                    <div class="mobile-card-body">
                        <div class="mobile-card-row">
                            <div class="mobile-card-label">Pengaju:</div>
                            <div class="mobile-card-value">
                                <?= htmlspecialchars($row['requester_name']) ?>
                            </div>
                        </div>
                        <div class="mobile-card-row">
                            <div class="mobile-card-label">Departemen:</div>
                            <div class="mobile-card-value">
                                <span class="department-badge">
                                    <i class="fas fa-building"></i>
                                    <?= htmlspecialchars($row['departemen'] ?? 'N/A') ?>
                                </span>
                            </div>
                        </div>
                        <div class="mobile-card-row">
                            <div class="mobile-card-label">Peserta:</div>
                            <div class="mobile-card-value">
                                <span class="participants-count">
                                    <i class="fas fa-users"></i>
                                    <?= $row['total_participants'] ?>
                                </span>
                            </div>
                        </div>
                        <div class="mobile-card-row">
                            <div class="mobile-card-label">Tanggal:</div>
                            <div class="mobile-card-value">
                                <?php
                                if (!empty($row['start_date'])) {
                                    // Ada tanggal training
                                    if ($row['is_confirmed'] == 1) {
                                        // Tanggal sudah dikonfirmasi/fixed
                                        if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                                            // Multi-day training yang sudah dikonfirmasi
                                            echo '<span class="date-confirmed" style="color: #2e7d32; background-color: rgba(76, 175, 80, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #2e7d32; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">';
                                            echo '<i class="fas fa-calendar-check"></i> ';
                                            echo htmlspecialchars($row['start_date']) . ' s/d ' . htmlspecialchars($row['end_date']);
                                            echo '<span style="background-color: rgba(76, 175, 80, 0.2); color: #155724; border: 1px solid rgba(76, 175, 80, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Dikonfirmasi</span>';
                                            echo '</span>';
                                        } else {
                                            // Single day training yang sudah dikonfirmasi
                                            echo '<span class="date-confirmed" style="color: #2e7d32; background-color: rgba(76, 175, 80, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #2e7d32; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">';
                                            echo '<i class="fas fa-calendar-check"></i> ';
                                            echo htmlspecialchars($row['start_date']);
                                            echo '<span style="background-color: rgba(76, 175, 80, 0.2); color: #155724; border: 1px solid rgba(76, 175, 80, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Dikonfirmasi</span>';
                                            echo '</span>';
                                        }
                                    } else {
                                        // Tanggal belum dikonfirmasi (pending)
                                        if (!empty($row['end_date']) && $row['end_date'] !== $row['start_date']) {
                                            // Multi-day training yang masih pending
                                            echo '<span class="date-pending" style="color: #f57c00; background-color: rgba(255, 193, 7, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #f57c00; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">';
                                            echo '<i class="fas fa-calendar-alt"></i> ';
                                            echo htmlspecialchars($row['start_date']) . ' s/d ' . htmlspecialchars($row['end_date']);
                                            echo '<span style="background-color: rgba(255, 193, 7, 0.2); color: #856404; border: 1px solid rgba(255, 193, 7, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Pending</span>';
                                            echo '</span>';
                                        } else {
                                            // Single day training yang masih pending
                                            echo '<span class="date-pending" style="color: #f57c00; background-color: rgba(255, 193, 7, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #f57c00; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">';
                                            echo '<i class="fas fa-calendar-alt"></i> ';
                                            echo htmlspecialchars($row['start_date']);
                                            echo '<span style="background-color: rgba(255, 193, 7, 0.2); color: #856404; border: 1px solid rgba(255, 193, 7, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Pending</span>';
                                            echo '</span>';
                                        }
                                    }
                                } else {
                                    // Belum ada tanggal sama sekali
                                    echo '<span class="date-pending" style="color: #f57c00; background-color: rgba(255, 193, 7, 0.1); padding: 8px 12px; border-radius: 4px; border-left: 4px solid #f57c00; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">';
                                    echo '<i class="fas fa-calendar-times"></i> ';
                                    echo 'Belum Ditentukan';
                                    echo '<span style="background-color: rgba(255, 193, 7, 0.2); color: #856404; border: 1px solid rgba(255, 193, 7, 0.4); font-size: 10px; padding: 2px 6px; border-radius: 3px; margin-left: 8px;">Menunggu</span>';
                                    echo '</span>';
                                }
                                ?>
                            </div>
                        </div>
                        <div class="mobile-card-row">
                            <div class="mobile-card-label">Status:</div>
                            <div class="mobile-card-value">
                                <?php if (isset($row['status']) && $row['status'] == 'Rejected'): ?>
                                    <span class="status-badge status-rejected">
                                        <i class="fas fa-times-circle"></i>
                                        <?php
                                        if (!empty($row['rejected_by'])) {
                                            // Ambil nama user yang membatalkan
                                            $rejecter_query = "SELECT name, jabatan FROM users WHERE id = ?";
                                            $rejecter_stmt = $conn->prepare($rejecter_query);
                                            $rejecter_stmt->bind_param("i", $row['rejected_by']);
                                            $rejecter_stmt->execute();
                                            $rejecter_result = $rejecter_stmt->get_result();

                                            if ($rejecter_row = $rejecter_result->fetch_assoc()) {
                                                $rejecter_name = $rejecter_row['name'];
                                                $rejecter_position = $rejecter_row['jabatan'];
                                                echo "Training telah dibatalkan oleh " . htmlspecialchars($rejecter_name) . " (" . htmlspecialchars($rejecter_position) . ")";

                                                if (!empty($row['rejected_at'])) {
                                                    $rejected_date = new DateTime($row['rejected_at']);
                                                    echo " pada " . $rejected_date->format('d-m-Y H:i');
                                                }
                                            } else {
                                                echo "Training telah dibatalkan";
                                            }
                                            $rejecter_stmt->close();
                                        } else {
                                            echo "Training telah dibatalkan";
                                        }
                                        ?>
                                    </span>
                                <?php else: ?>
                                    <span class="status-badge status-pending">
                                        <i class="fas fa-hourglass-half"></i>
                                        Pending
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="mobile-card-footer">
                        <a href="detail_training.php?id=<?= $row['id'] ?>" class="action-button">
                            <i class="fas fa-info-circle"></i> Detail
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>