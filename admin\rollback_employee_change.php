<?php
session_start();
require_once '../includes/auth_check.php'; // Ensure user is logged in and is an admin
require_once '../config/config.php'; // Database connection and configuration

// Konversi koneksi mysqli ke PDO
$pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
require_once '../config/activity_logger.php'; // Include the activity logger

/**
 * Mencatat aktivitas pengguna ke dalam database
 *
 * @param int $user_id ID pengguna yang melakukan aktivitas
 * @param string $action Deskripsi aktivitas yang dilakukan
 * @param string $category Kategori aktivitas (settings, training, employee, dll)
 * @param array $details Detail tambahan tentang aktivitas (opsional)
 * @return bool True jika berhasil, False jika gagal
 */
// function log_activity($user_id, $action, $category, $details = []) {} // Definisi fungsi ada di activity_logger.php

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Akses ditolak. Anda tidak memiliki izin untuk melakukan tindakan ini.']);
    exit;
}

// Validate input
if (!isset($_POST['history_id']) || !isset($_POST['action'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Parameter yang diperlukan tidak lengkap.']);
    exit;
}

$historyId = intval($_POST['history_id']);
$action = $_POST['action']; // Action type from the history record (INSERT, UPDATE, DELETE)
$adminUsername = $_SESSION['name'] ?? 'Admin Tidak Dikenal'; // User performing the rollback

// Fetch the history record
try {
    $stmt = $pdo->prepare("SELECT nik, old_data, new_data FROM karyawan_history WHERE history_id = ?");
    $stmt->execute([$historyId]);
    $history = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$history) {
        throw new Exception("Data riwayat tidak ditemukan.");
    }

    $nik = $history['nik'];
    $oldData = !empty($history['old_data']) ? json_decode($history['old_data'], true) : null;
    $newData = !empty($history['new_data']) ? json_decode($history['new_data'], true) : null;

    // --- Begin Transaction ---
    $pdo->beginTransaction();

    $rollbackSuccess = false;
    $logMessage = "Percobaan rollback untuk ID riwayat: {$historyId} (Aksi: {$action}, NIK: {$nik}) oleh {$adminUsername}.";

    // --- Perform Rollback based on Action ---
    switch ($action) {
        case 'INSERT': // Rollback an INSERT means deleting the record
            if (!$nik) {
                 throw new Exception("Tidak dapat melakukan rollback PENAMBAHAN: NIK tidak ditemukan dalam data riwayat.");
            }
            // Check if record still exists before deleting
            $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM karyawan WHERE nik = ?");
            $checkStmt->execute([$nik]);
            if ($checkStmt->fetchColumn() > 0) {
                $deleteStmt = $pdo->prepare("DELETE FROM karyawan WHERE nik = ?");
                $rollbackSuccess = $deleteStmt->execute([$nik]);
                if (!$rollbackSuccess) {
                    throw new Exception("Gagal menghapus karyawan saat rollback PENAMBAHAN.");
                }
                $logMessage = "Berhasil melakukan rollback PENAMBAHAN untuk NIK {$nik} (ID Riwayat: {$historyId}) dengan menghapus data. Dilakukan oleh {$adminUsername}.";
            } else {
                 // Record already deleted, consider rollback successful in this context
                 $rollbackSuccess = true;
                 $logMessage = "Rollback PENAMBAHAN (ID Riwayat: {$historyId}, NIK: {$nik}) dilewati: Data sudah tidak ada. Dianggap berhasil oleh {$adminUsername}.";
            }
            break;

        case 'UPDATE': // Rollback an UPDATE means restoring the old_data
             if (!$nik) {
                 throw new Exception("Tidak dapat melakukan rollback PERUBAHAN: NIK tidak ditemukan dalam data riwayat.");
             }
            if (!$oldData || !is_array($oldData)) {
                throw new Exception("Tidak dapat melakukan rollback PERUBAHAN: Data lama tidak valid atau tidak ditemukan dalam data riwayat.");
            }

            // Check if record still exists before updating
            $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM karyawan WHERE nik = ?");
            $checkStmt->execute([$nik]);
            if ($checkStmt->fetchColumn() > 0) {
                // Construct the UPDATE statement dynamically from old_data keys
                $setClauses = [];
                $bindings = [];
                foreach ($oldData as $key => $value) {
                    // Ensure 'nik' itself is not part of the SET clause if it exists in old_data
                    if (strtolower($key) !== 'nik') {
                        $setClauses[] = "`" . $key . "` = ?";
                        $bindings[] = $value;
                    }
                }

                if (empty($setClauses)) {
                     throw new Exception("Tidak dapat melakukan rollback PERUBAHAN: Tidak ada kolom yang ditemukan dalam data lama untuk dipulihkan.");
                }

                $bindings[] = $nik; // Add NIK for the WHERE clause
                $sql = "UPDATE karyawan SET " . implode(', ', $setClauses) . " WHERE nik = ?";
                $updateStmt = $pdo->prepare($sql);
                $rollbackSuccess = $updateStmt->execute($bindings);
                 if (!$rollbackSuccess) {
                    throw new Exception("Gagal memperbarui data karyawan saat rollback PERUBAHAN.");
                }
                $logMessage = "Berhasil melakukan rollback PERUBAHAN untuk NIK {$nik} (ID Riwayat: {$historyId}) dengan mengembalikan nilai sebelumnya. Dilakukan oleh {$adminUsername}.";
            } else {
                // Record doesn't exist, cannot rollback update
                 throw new Exception("Tidak dapat melakukan rollback PERUBAHAN: Karyawan dengan NIK {$nik} sudah tidak ada.");
            }
            break;

        case 'DELETE': // Rollback a DELETE means re-inserting the old_data
            if (!$oldData || !is_array($oldData) || !isset($oldData['nik'])) {
                throw new Exception("Tidak dapat melakukan rollback PENGHAPUSAN: Data lama tidak valid, tidak ditemukan, atau NIK tidak ada dalam data lama.");
            }

             // Check if record already exists (maybe re-added manually?)
            $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM karyawan WHERE nik = ?");
            $checkStmt->execute([$oldData['nik']]);
            if ($checkStmt->fetchColumn() == 0) {
                // Construct the INSERT statement dynamically
                $columns = array_keys($oldData);
                $placeholders = array_fill(0, count($columns), '?');
                $bindings = array_values($oldData);

                $sql = "INSERT INTO karyawan (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $placeholders) . ")";
                $insertStmt = $pdo->prepare($sql);
                $rollbackSuccess = $insertStmt->execute($bindings);
                 if (!$rollbackSuccess) {
                    throw new Exception("Gagal menambahkan kembali karyawan saat rollback PENGHAPUSAN.");
                }
                 $logMessage = "Berhasil melakukan rollback PENGHAPUSAN untuk NIK {$oldData['nik']} (ID Riwayat: {$historyId}) dengan menambahkan kembali data. Dilakukan oleh {$adminUsername}.";
            } else {
                 // Record already exists, cannot re-insert
                 throw new Exception("Tidak dapat melakukan rollback PENGHAPUSAN: Karyawan dengan NIK {$oldData['nik']} sudah ada.");
            }
            break;

        default:
            throw new Exception("Jenis aksi tidak valid untuk rollback.");
    }

    // --- Log the successful rollback action ---
    if ($rollbackSuccess) {
        $userId = $_SESSION['user_id'] ?? 0; // Get user ID from session, default to 0 if not set
        $details = ['message' => $logMessage];
        log_activity($userId, 'Rollback Berhasil', 'Rollback Karyawan', $details); // Pass user ID and use associative array for details
        // --- Commit Transaction ---
        $pdo->commit();
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => 'Rollback berhasil dilakukan.']);
    } else {
        // This part might not be reached if exceptions are thrown correctly
         throw new Exception("Operasi rollback gagal karena alasan yang tidak diketahui.");
    }

} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    // Log the database error
    error_log("Database Error during rollback (History ID: {$historyId}): " . $e->getMessage());
    $dbErrorMsg = "Kesalahan database saat rollback untuk ID Riwayat {$historyId}: " . $e->getMessage();
    $userId = $_SESSION['user_id'] ?? 0; // Get user ID from session
    $details = ['error' => $dbErrorMsg];
    log_activity($userId, 'Rollback Gagal', 'Kesalahan Rollback Karyawan', $details); // Pass user ID and use associative array
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Kesalahan database saat rollback: ' . $e->getMessage()]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
     // Log the general error
    error_log("General Error during rollback (History ID: {$historyId}): " . $e->getMessage());
    $generalErrorMsg = "Kesalahan saat rollback untuk ID Riwayat {$historyId}: " . $e->getMessage();
    $userId = $_SESSION['user_id'] ?? 0; // Get user ID from session
    $details = ['error' => $generalErrorMsg];
    log_activity($userId, 'Rollback Gagal', 'Kesalahan Rollback Karyawan', $details); // Pass user ID and use associative array
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Rollback gagal: ' . $e->getMessage()]);
}

exit;
