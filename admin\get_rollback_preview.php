<?php
/**
 * Get Rollback Preview
 * This file provides preview information for batch rollback operations
 */

session_start();
require_once '../config/config.php';
require_once 'record_batch_employee_history.php';

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo '<div class="alert alert-danger">Unauthorized access</div>';
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['batch_id'])) {
    $batch_id = intval($_POST['batch_id']);
    
    try {
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Get batch history details
        $batch_details = getBatchHistoryDetails($batch_id);
        
        if (!$batch_details) {
            echo '<div class="alert alert-danger">Batch history not found</div>';
            exit();
        }
        
        $batch_data = $batch_details['batch_data'];
        $action_type = $batch_details['action_type'];
        $niks = $batch_data['niks'] ?? [];
        
        echo '<div class="rollback-preview">';
        echo '<div class="alert alert-warning">';
        echo '<h6><i class="fas fa-exclamation-triangle"></i> PERINGATAN ROLLBACK</h6>';
        echo '<p>Anda akan melakukan rollback untuk operasi batch berikut:</p>';
        echo '</div>';
        
        echo '<div class="row">';
        echo '<div class="col-md-6">';
        echo '<h6>Informasi Batch:</h6>';
        echo '<ul class="list-unstyled">';
        echo '<li><strong>Batch ID:</strong> ' . $batch_id . '</li>';
        echo '<li><strong>Action Type:</strong> ' . $action_type . '</li>';
        echo '<li><strong>Tanggal:</strong> ' . date('d M Y H:i:s', strtotime($batch_details['change_timestamp'])) . '</li>';
        echo '<li><strong>Jumlah NIK:</strong> ' . count($niks) . '</li>';
        echo '</ul>';
        echo '</div>';
        
        echo '<div class="col-md-6">';
        echo '<h6>Statistik Batch:</h6>';
        echo '<ul class="list-unstyled">';
        echo '<li><strong>Sukses:</strong> <span class="badge badge-success">' . ($batch_data['total_success'] ?? 0) . '</span></li>';
        echo '<li><strong>Error:</strong> <span class="badge badge-danger">' . ($batch_data['total_error'] ?? 0) . '</span></li>';
        echo '<li><strong>Dilewati:</strong> <span class="badge badge-warning">' . ($batch_data['total_skipped'] ?? 0) . '</span></li>';
        echo '</ul>';
        echo '</div>';
        echo '</div>';
        
        // Show rollback impact based on action type
        echo '<div class="mt-3">';
        echo '<h6>Dampak Rollback:</h6>';
        
        switch ($action_type) {
            case 'BATCH_INSERT':
                echo '<div class="alert alert-danger">';
                echo '<h6><i class="fas fa-trash"></i> PENGHAPUSAN DATA</h6>';
                echo '<p>Rollback akan <strong>MENGHAPUS</strong> semua karyawan yang ditambahkan dalam batch ini.</p>';
                echo '<p>Data yang akan dihapus:</p>';
                
                // Get current employee data for these NIKs
                if (!empty($niks)) {
                    $placeholders = str_repeat('?,', count($niks) - 1) . '?';
                    $stmt = $pdo->prepare("SELECT nik, name, jabatan, departemen FROM karyawan WHERE nik IN ($placeholders)");
                    $stmt->execute($niks);
                    $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    if (!empty($employees)) {
                        echo '<div class="table-responsive" style="max-height: 300px; overflow-y: auto;">';
                        echo '<table class="table table-sm table-striped">';
                        echo '<thead><tr><th>NIK</th><th>Nama</th><th>Jabatan</th><th>Departemen</th></tr></thead>';
                        echo '<tbody>';
                        foreach ($employees as $emp) {
                            echo '<tr>';
                            echo '<td>' . htmlspecialchars($emp['nik']) . '</td>';
                            echo '<td>' . htmlspecialchars($emp['name']) . '</td>';
                            echo '<td>' . htmlspecialchars($emp['jabatan']) . '</td>';
                            echo '<td>' . htmlspecialchars($emp['departemen']) . '</td>';
                            echo '</tr>';
                        }
                        echo '</tbody></table>';
                        echo '</div>';
                        
                        $missing_count = count($niks) - count($employees);
                        if ($missing_count > 0) {
                            echo '<p class="text-muted"><small>' . $missing_count . ' karyawan sudah tidak ada dalam database (mungkin sudah dihapus).</small></p>';
                        }
                    } else {
                        echo '<p class="text-muted">Tidak ada karyawan yang ditemukan dengan NIK tersebut. Mungkin sudah dihapus.</p>';
                    }
                }
                echo '</div>';
                break;
                
            case 'BATCH_UPDATE':
                echo '<div class="alert alert-info">';
                echo '<h6><i class="fas fa-edit"></i> PEMULIHAN DATA</h6>';
                echo '<p>Rollback akan <strong>MENGEMBALIKAN</strong> data karyawan ke kondisi sebelum update.</p>';
                echo '<p class="text-warning"><strong>Catatan:</strong> Fitur rollback untuk UPDATE memerlukan enhanced history tracking yang akan tersedia dalam update berikutnya.</p>';
                echo '</div>';
                break;
                
            case 'BATCH_DELETE':
                echo '<div class="alert alert-success">';
                echo '<h6><i class="fas fa-plus"></i> PEMULIHAN DATA</h6>';
                echo '<p>Rollback akan <strong>MENGEMBALIKAN</strong> karyawan yang dihapus dalam batch ini.</p>';
                echo '<p class="text-warning"><strong>Catatan:</strong> Fitur rollback untuk DELETE memerlukan enhanced history tracking yang akan tersedia dalam update berikutnya.</p>';
                echo '</div>';
                break;
                
            default:
                echo '<div class="alert alert-secondary">';
                echo '<p>Action type tidak dikenali: ' . htmlspecialchars($action_type) . '</p>';
                echo '</div>';
        }
        echo '</div>';
        
        // Show NIK list if not too many
        if (count($niks) <= 20) {
            echo '<div class="mt-3">';
            echo '<h6>NIK yang Terpengaruh:</h6>';
            echo '<div class="d-flex flex-wrap">';
            foreach ($niks as $nik) {
                echo '<span class="badge badge-secondary mr-1 mb-1">' . htmlspecialchars($nik) . '</span>';
            }
            echo '</div>';
            echo '</div>';
        } else {
            echo '<div class="mt-3">';
            echo '<h6>NIK yang Terpengaruh:</h6>';
            echo '<p>' . count($niks) . ' NIK (terlalu banyak untuk ditampilkan)</p>';
            echo '</div>';
        }
        
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
} else {
    echo '<div class="alert alert-danger">Invalid request</div>';
}
?>
