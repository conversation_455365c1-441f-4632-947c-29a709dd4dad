<?php
// File: admin/export_attendance.php
// Deskripsi: Script untuk mengekspor data absensi training ke Excel

// Aktifkan output buffering
ob_start();

include '../config/config.php';
include 'security.php';

// Cek apakah training ID ada
if (!isset($_GET['training_id'])) {
    header("Location: rfid_attendance.php");
    exit();
}

$training_id = $_GET['training_id'];

// Ambil data training
$query_training = "SELECT training_topic, training_date, training_date_fixed, training_date_start, training_date_end
                  FROM training_submissions
                  WHERE id = ?";
$stmt_training = $conn->prepare($query_training);
$stmt_training->bind_param("i", $training_id);
$stmt_training->execute();
$result_training = $stmt_training->get_result();

if ($result_training->num_rows === 0) {
    header("Location: rfid_attendance.php");
    exit();
}

$training = $result_training->fetch_assoc();

// Fungsi untuk mendapatkan tanggal training yang sesuai
function getTrainingDate($row) {
    if (!empty($row['training_date_fixed'])) {
        return $row['training_date_fixed'];
    } elseif (!empty($row['training_date_start']) && !empty($row['training_date_end'])) {
        return $row['training_date_start'] . ' s/d ' . $row['training_date_end'];
    } else {
        return $row['training_date'];
    }
}

// Ambil data absensi
$query_attendance = "SELECT ta.id, ta.nik, ta.nama, ta.check_in, ta.check_out, ta.status, ta.keterangan,
                     k.dept, k.bagian, k.jabatan, k.card_number
                     FROM training_attendance ta
                     LEFT JOIN karyawan k ON ta.karyawan_id = k.id
                     WHERE ta.training_id = ?
                     ORDER BY ta.nama ASC";
$stmt_attendance = $conn->prepare($query_attendance);
$stmt_attendance->bind_param("i", $training_id);
$stmt_attendance->execute();
$result_attendance = $stmt_attendance->get_result();

// Require library PhpSpreadsheet
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// Buat objek spreadsheet baru
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set judul sheet
$sheet->setTitle('Absensi Training');

// Set header
$sheet->setCellValue('A1', 'DAFTAR HADIR TRAINING');
$sheet->mergeCells('A1:I1');
$sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
$sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

// Set informasi training
$sheet->setCellValue('A3', 'Topik Training:');
$sheet->setCellValue('B3', $training['training_topic']);
$sheet->mergeCells('B3:I3');

$sheet->setCellValue('A4', 'Tanggal Training:');
$sheet->setCellValue('B4', getTrainingDate($training));
$sheet->mergeCells('B4:I4');

// Set header tabel
$sheet->setCellValue('A6', 'No');
$sheet->setCellValue('B6', 'NIK');
$sheet->setCellValue('C6', 'Nama');
$sheet->setCellValue('D6', 'Departemen');
$sheet->setCellValue('E6', 'Bagian');
$sheet->setCellValue('F6', 'Jabatan');
$sheet->setCellValue('G6', 'Check In');
$sheet->setCellValue('H6', 'Check Out');
$sheet->setCellValue('I6', 'Status');

// Style header tabel
$headerStyle = [
    'font' => [
        'bold' => true,
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
        ],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => [
            'rgb' => 'E2EFDA',
        ],
    ],
];

$sheet->getStyle('A6:I6')->applyFromArray($headerStyle);

// Isi data
$row = 7;
$no = 1;

while ($attendance = $result_attendance->fetch_assoc()) {
    $sheet->setCellValue('A' . $row, $no++);
    $sheet->setCellValue('B' . $row, $attendance['nik']);
    $sheet->setCellValue('C' . $row, $attendance['nama']);
    $sheet->setCellValue('D' . $row, $attendance['dept']);
    $sheet->setCellValue('E' . $row, $attendance['bagian']);
    $sheet->setCellValue('F' . $row, $attendance['jabatan']);
    $sheet->setCellValue('G' . $row, $attendance['check_in'] ? date('d-m-Y H:i:s', strtotime($attendance['check_in'])) : '-');
    $sheet->setCellValue('H' . $row, $attendance['check_out'] ? date('d-m-Y H:i:s', strtotime($attendance['check_out'])) : '-');
    $sheet->setCellValue('I' . $row, ucfirst($attendance['status']));

    $row++;
}

// Style data
$dataStyle = [
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
        ],
    ],
];

$sheet->getStyle('A7:I' . ($row - 1))->applyFromArray($dataStyle);

// Set lebar kolom
$sheet->getColumnDimension('A')->setWidth(5);
$sheet->getColumnDimension('B')->setWidth(15);
$sheet->getColumnDimension('C')->setWidth(25);
$sheet->getColumnDimension('D')->setWidth(15);
$sheet->getColumnDimension('E')->setWidth(15);
$sheet->getColumnDimension('F')->setWidth(20);
$sheet->getColumnDimension('G')->setWidth(20);
$sheet->getColumnDimension('H')->setWidth(20);
$sheet->getColumnDimension('I')->setWidth(15);

// Set alignment
$sheet->getStyle('A7:A' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
$sheet->getStyle('B7:B' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
$sheet->getStyle('G7:H' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
$sheet->getStyle('I7:I' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

// Tambahkan tanda tangan
$row += 2;
$sheet->setCellValue('G' . $row, 'Mengetahui,');
$sheet->mergeCells('G' . $row . ':I' . $row);
$sheet->getStyle('G' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

$row += 5;
$sheet->setCellValue('G' . $row, '(_____________________)');
$sheet->mergeCells('G' . $row . ':I' . $row);
$sheet->getStyle('G' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

$row += 1;
$sheet->setCellValue('G' . $row, 'HRGA Manager');
$sheet->mergeCells('G' . $row . ':I' . $row);
$sheet->getStyle('G' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

// Pastikan tidak ada output sebelum header
ob_clean();

// Buat file Excel
$filename = 'Absensi_Training_' . preg_replace('/[^a-zA-Z0-9]/', '_', $training['training_topic']) . '_' . date('Y-m-d') . '.xlsx';

// Set header untuk download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');
header('Pragma: public');

// Tulis ke output
$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit;
?>
