<?php
include 'security.php';
include '../config/config.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $request_id = $_POST['request_id'];
    $role = $_SESSION['user_role']; // Role user yang sedang login
    $action = $_POST['action']; // 'approve' atau 'reject'
    
    $status = ($action == 'approve') ? 'Approved' : 'Rejected';
    
    $column = '';
    switch ($role) {
        case 'Dept Head':
            $column = 'approved_dept_head';
            break;
        case 'LND':
            $column = 'approved_hrd';
            break;
        case 'HRGA':
            $column = 'approved_ga';
            break;
        case 'FM':
            $column = 'approved_fm';
            break;
        case 'Dir':
            $column = 'approved_dir';
            break;
        default:
            $_SESSION['errors'][] = "Role tidak valid.";
            header("Location: dashboard.php");
            exit();
    }

    if ($role == 'Dept Head') {
        // Cek apakah user berhak approve untuk departemen tersebut
        $checkAuth = "SELECT 1 FROM user_departments ud 
                     JOIN training_submissions ts ON ts.dept = ud.dept
                     WHERE ud.user_id = ? AND ts.id = ?";
        $stmt = $conn->prepare($checkAuth);
        $stmt->bind_param("ii", $_SESSION['user_id'], $request_id);
        $stmt->execute();
        if ($stmt->get_result()->num_rows == 0) {
            $_SESSION['errors'][] = "Anda tidak memiliki akses untuk menyetujui training ini.";
            header("Location: dashboard.php");
            exit();
        }
    }

    // Update status approval sesuai role
    $query = "UPDATE training_requests SET $column = ? WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("si", $status, $request_id);
    
    if ($stmt->execute()) {
        $_SESSION['success'] = "Approval berhasil diperbarui.";
    } else {
        $_SESSION['errors'][] = "Gagal memperbarui approval.";
    }

    $stmt->close();
    $conn->close();
    header("Location: dashboard.php");
    exit();
}
?>
