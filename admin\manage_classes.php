<?php
/**
 * Manage Classes Page for Admin
 * This page allows admins to manage training classes
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if the classroom tables exist
$classroom_tables_exist = true;
$required_tables = [
    'training_classes',
    'training_participants',
    'training_materials',
    'training_assignments'
];

foreach ($required_tables as $table) {
    $check_table_query = "SHOW TABLES LIKE '$table'";
    $table_result = $conn->query($check_table_query);
    if (!$table_result || $table_result->num_rows == 0) {
        $classroom_tables_exist = false;
        break;
    }
}

// Handle class creation
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_class'])) {
    $training_id = intval($_POST['training_id']);
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $start_date = !empty($_POST['start_date']) ? $_POST['start_date'] : null;
    $end_date = !empty($_POST['end_date']) ? $_POST['end_date'] : null;
    $status = $_POST['status'];
    $max_participants = !empty($_POST['max_participants']) ? intval($_POST['max_participants']) : null;

    // Validate input
    if (empty($title)) {
        $error_message = "Judul kelas harus diisi.";
    } elseif ($training_id <= 0) {
        $error_message = "Training tidak valid.";
    } else {

        // Check if training exists in training_submissions
        $training_source = 'external'; // Default
        $check_external = $conn->prepare("SELECT id FROM training_submissions WHERE id = ?");
        $check_external->bind_param("i", $training_id);
        $check_external->execute();
        $external_result = $check_external->get_result();

        if ($external_result->num_rows == 0) {
            // Check if training exists in offline_training
            $check_internal = $conn->prepare("SELECT id FROM offline_training WHERE id = ?");
            $check_internal->bind_param("i", $training_id);
            $check_internal->execute();
            $internal_result = $check_internal->get_result();

            if ($internal_result->num_rows > 0) {
                $training_source = 'internal';
            } else {
                $error_message = "Training tidak ditemukan.";
            }
            $check_internal->close();
        }
        $check_external->close();

        if (empty($error_message)) {
            // Insert class
            $insert_query = "INSERT INTO training_classes (
                            training_id, title, description, start_date, end_date, status, max_participants, created_by, training_source
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param(
                "isssssiss",
                $training_id, $title, $description, $start_date, $end_date, $status, $max_participants, $user_id, $training_source
            );

            if ($stmt->execute()) {
                $class_id = $stmt->insert_id;
                $success_message = "Kelas berhasil dibuat.";

                // Redirect to class detail page
                header("Location: manage_class.php?id=" . $class_id);
                exit();
            } else {
                $error_message = "Gagal membuat kelas: " . $conn->error;
            }
            $stmt->close();
        }
    }
}

// Handle class deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $class_id = intval($_GET['delete']);

    // Delete class
    $delete_query = "DELETE FROM training_classes WHERE id = ?";
    $stmt = $conn->prepare($delete_query);
    $stmt->bind_param("i", $class_id);

    if ($stmt->execute()) {
        $success_message = "Kelas berhasil dihapus.";
    } else {
        $error_message = "Gagal menghapus kelas: " . $conn->error;
    }
    $stmt->close();
}

// Get all classes
$classes = [];
if ($classroom_tables_exist) {
    // First, let's check the structure of the users table to find the name column
    $user_table_query = "DESCRIBE users";
    $user_table_result = $conn->query($user_table_query);
    $name_column = 'name'; // Default to name

    if ($user_table_result) {
        while ($column = $user_table_result->fetch_assoc()) {
            // Look for common name column patterns
            if (in_array(strtolower($column['Field']), ['full_name', 'name', 'user_name', 'username', 'nama'])) {
                $name_column = $column['Field'];
                // Prefer full_name or name if available
                if (in_array(strtolower($column['Field']), ['full_name', 'name', 'nama'])) {
                    break;
                }
            }
        }
    }

    // Check if training_source column exists in training_classes
    $check_column_query = "SHOW COLUMNS FROM training_classes LIKE 'training_source'";
    $column_result = $conn->query($check_column_query);
    $has_training_source = $column_result && $column_result->num_rows > 0;

    // Add training_source column if it doesn't exist
    if (!$has_training_source) {
        $add_column_query = "ALTER TABLE training_classes ADD COLUMN training_source VARCHAR(20) DEFAULT 'external'";
        if ($conn->query($add_column_query)) {
            $has_training_source = true;
        }
    }

    if ($has_training_source) {
        // Use UNION to get classes from both external and internal trainings
        $classes_query = "
            SELECT c.*, t.training_topic, t.training_type,
                   (SELECT COUNT(*) FROM training_participants WHERE class_id = c.id) as participant_count,
                   u.$name_column as creator_name, 'external' as source
            FROM training_classes c
            JOIN training_submissions t ON c.training_id = t.id AND (c.training_source = 'external' OR c.training_source IS NULL)
            LEFT JOIN users u ON c.created_by = u.id

            UNION ALL

            SELECT c.*, ot.training_topic as training_topic,
                   'Internal' as training_type,
                   (SELECT COUNT(*) FROM training_participants WHERE class_id = c.id) as participant_count,
                   u.$name_column as creator_name, 'internal' as source
            FROM training_classes c
            JOIN offline_training ot ON c.training_id = ot.id AND c.training_source = 'internal'
            LEFT JOIN users u ON c.created_by = u.id

            ORDER BY created_at DESC";
    } else {
        // Fallback to original query for backward compatibility
        $classes_query = "SELECT c.*, t.training_topic, t.training_type,
                         (SELECT COUNT(*) FROM training_participants WHERE class_id = c.id) as participant_count,
                         u.$name_column as creator_name, 'external' as source
                         FROM training_classes c
                         JOIN training_submissions t ON c.training_id = t.id
                         LEFT JOIN users u ON c.created_by = u.id
                         ORDER BY c.created_at DESC";
    }

    $result = $conn->query($classes_query);

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $classes[] = $row;
        }
    }
}

// Get available trainings for creating new classes (both external and internal)
$trainings = [];



// Get external trainings from training_submissions (using actual statuses)
$external_trainings_query = "SELECT id, training_topic, training_type, status, 'external' as source
                            FROM training_submissions
                            WHERE status IN ('Approved', 'Pending', 'approved', 'pending')
                               OR status IS NULL
                            ORDER BY id DESC";
$result = $conn->query($external_trainings_query);

$external_count = 0;
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $trainings[] = $row;
        $external_count++;
    }
}

// Get internal trainings from offline_training (using actual statuses)
$internal_trainings_query = "SELECT id, training_topic, status, 'Internal' as training_type, 'internal' as source
                            FROM offline_training
                            WHERE status IN ('Active', 'Pending Dept Head Approval', 'Pending L&D Approval', 'active')
                               OR status IS NULL
                            ORDER BY id DESC";
$result = $conn->query($internal_trainings_query);

$internal_count = 0;
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $trainings[] = $row;
        $internal_count++;
    }
}

// If still no results, get all trainings regardless of status
if (count($trainings) < 5) { // If we have less than 5 trainings, get more
    // Get all external trainings
    $all_external_query = "SELECT id, training_topic, training_type, status, 'external' as source
                          FROM training_submissions
                          ORDER BY id DESC";
    $result = $conn->query($all_external_query);

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            // Check if not already added
            $already_exists = false;
            foreach ($trainings as $existing) {
                if ($existing['id'] == $row['id'] && $existing['source'] == 'external') {
                    $already_exists = true;
                    break;
                }
            }
            if (!$already_exists) {
                $trainings[] = $row;
                $external_count++;
            }
        }
    }

    // Get all internal trainings
    $all_internal_query = "SELECT id, training_topic, status, 'Internal' as training_type, 'internal' as source
                          FROM offline_training
                          ORDER BY id DESC";
    $result = $conn->query($all_internal_query);

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            // Check if not already added
            $already_exists = false;
            foreach ($trainings as $existing) {
                if ($existing['id'] == $row['id'] && $existing['source'] == 'internal') {
                    $already_exists = true;
                    break;
                }
            }
            if (!$already_exists) {
                $trainings[] = $row;
                $internal_count++;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .class-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .class-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .class-header {
        padding: 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        border-radius: 8px 8px 0 0;
    }

    .class-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .class-topic {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .class-body {
        padding: 15px;
    }

    .class-description {
        margin-bottom: 15px;
        color: #333;
    }

    .class-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 15px;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .class-meta-item {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .class-footer {
        padding: 15px;
        background-color: #f8f9fa;
        border-top: 1px solid #e9ecef;
        border-radius: 0 0 8px 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .class-status {
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-active {
        background-color: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }

    .status-completed {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    .class-actions {
        display: flex;
        gap: 10px;
    }

    .form-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .form-section h3 {
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .setup-required {
        background-color: #f8d7da;
        color: #721c24;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: center;
    }

    .filters {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }

    .filter-item {
        flex: 1;
        min-width: 200px;
    }

    /* Searchable dropdown styling */
    .searchable-select {
        position: relative;
    }

    #dropdownTrainingList li:hover,
    #dropdownTrainingList li.highlighted {
        background-color: #f8f9fa;
    }

    #dropdownTrainingList li:hover {
        background-color: #e9ecef;
    }

    #dropdownTrainingList li.highlighted {
        background-color:rgb(178, 178, 178);
        color: white;
    }

    #dropdownTrainingList li.highlighted .badge {
        background-color: #DDD!important;
    }

    #searchTraining:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        #dropdownTrainingList {
            max-height: 250px;
        }
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="welcome-section">
                <div>
                    <h1><i class="fas fa-chalkboard-teacher"></i> Kelola Kelas Training</h1>
                    <p class="text-white">Buat dan kelola kelas untuk training</p>
                </div>
                <div>
                    <a href="import_template.php" class="btn btn-primary">
                        <i class="fas fa-file-import"></i> Import Data Bulk
                    </a>
                </div>
            </div>

            <?php if (!$classroom_tables_exist): ?>
                <div class="setup-required">
                    <h3><i class="fas fa-exclamation-triangle"></i> Pengaturan Classroom Diperlukan</h3>
                    <p>Tabel database untuk fitur classroom belum dibuat. Silakan jalankan script setup terlebih dahulu.</p>
                    <a href="../config/create_classroom_tables.php" class="btn btn-primary">Setup Tabel Classroom</a>
                </div>
            <?php else: ?>
                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?= $success_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $error_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>



                <div class="form-section">
                    <h3>Buat Kelas Baru</h3>

                    <form method="post" action="">
                    <div class="form-group">
    <label for="searchTraining">
        <i class="fas fa-chalkboard-teacher"></i> Training
    </label>

    <div class="searchable-select" style="position: relative;">
        <!-- Hidden select for form submission -->
        <select id="training_id" name="training_id" style="display: none;" required>
            <option value="">-- Pilih Training --</option>
            <?php foreach ($trainings as $training): ?>
                <option value="<?= $training['id'] ?>">
                    <?= htmlspecialchars($training['training_topic']) ?> (<?= htmlspecialchars($training['training_type'] ?? 'N/A') ?>)
                </option>
            <?php endforeach; ?>
        </select>

        <!-- Search input -->
        <input type="text"
               id="searchTraining"
               class="form-control"
               placeholder="Ketik untuk mencari training..."
               autocomplete="off" />

        <!-- Dropdown list -->
        <ul id="dropdownTrainingList" style="display: none; position: absolute; z-index: 1000; width: 100%; max-height: 300px; overflow-y: auto; border: 1px solid #ccc; background: #fff; top: 100%; left: 0; box-shadow: 0 4px 6px rgba(0,0,0,0.1); list-style: none; padding: 0; margin: 0;">
            <?php if (empty($trainings)): ?>
                <li style="padding: 12px; color: #999; font-style: italic; text-align: center;">
                    Tidak ada training tersedia
                </li>
            <?php else: ?>
                <?php foreach ($trainings as $training): ?>
                    <li data-value="<?= $training['id'] ?>"
                        data-source="<?= $training['source'] ?>"
                        style="padding: 12px; cursor: pointer; border-bottom: 1px solid #eee; transition: background-color 0.2s;">
                        <div style="font-weight: 500; color: #333;">
                            <?= htmlspecialchars($training['training_topic']) ?>
                        </div>
                        <small style="color: #666;">
                            <i class="fas fa-tag"></i> <?= htmlspecialchars($training['training_type'] ?? 'N/A') ?>
                            <?php if ($training['source'] === 'internal'): ?>
                                <span class="badge bg-info ms-2">Internal</span>
                            <?php else: ?>
                                <span class="badge bg-primary ms-2">External</span>
                            <?php endif; ?>
                        </small>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ul>
    </div>

    <div class="form-text">
        <i class="fas fa-info-circle"></i>
        Ketik nama training untuk mencari. Total <?= count($trainings) ?> training tersedia.
    </div>

    <?php if (empty($trainings)): ?>
        <div class="alert alert-warning mt-2">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Tidak ada training yang tersedia!</strong><br>
            <small>
                Pastikan ada training dengan status yang sesuai di database.
            </small>
        </div>
    <?php endif; ?>
</div>

                        <div class="mb-3">
                            <label for="title" class="form-label">Judul Kelas</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi Kelas</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">Tanggal Mulai</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">Tanggal Selesai</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="active">Aktif</option>
                                <option value="inactive">Tidak Aktif</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="max_participants" class="form-label">Maksimum Peserta</label>
                            <input type="number" class="form-control" id="max_participants" name="max_participants" min="1">
                            <div class="form-text">Biarkan kosong jika tidak ada batasan jumlah peserta</div>
                        </div>

                        <button type="submit" name="create_class" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Buat Kelas
                        </button>
                    </form>
                </div>

                <div class="filters">
                    <div class="filter-item">
                        <label for="filter-status" class="form-label">Filter Status</label>
                        <select class="form-select" id="filter-status">
                            <option value="">Semua Status</option>
                            <option value="active">Aktif</option>
                            <option value="inactive">Tidak Aktif</option>
                            <option value="completed">Selesai</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <label for="filter-type" class="form-label">Filter Tipe</label>
                        <select class="form-select" id="filter-type">
                            <option value="">Semua Tipe</option>
                            <option value="Inhouse">Inhouse</option>
                            <option value="Outhouse">Outhouse</option>
                            <option value="Hybrid">Hybrid</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <label for="search" class="form-label">Cari</label>
                        <input type="text" class="form-control" id="search" placeholder="Cari judul atau topik...">
                    </div>
                </div>

                <div class="row" id="classes-container">
                    <?php if (empty($classes)): ?>
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                Belum ada kelas yang dibuat. Silakan buat kelas baru menggunakan form di atas.
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($classes as $class): ?>
                            <div class="col-md-6 class-item"
                                 data-status="<?= $class['status'] ?>"
                                 data-type="<?= $class['training_type'] ?>"
                                 data-title="<?= strtolower($class['title']) ?>"
                                 data-topic="<?= strtolower($class['training_topic']) ?>">
                                <div class="class-card">
                                    <div class="class-header">
                                        <div class="class-title"><?= htmlspecialchars($class['title']) ?></div>
                                        <div class="class-topic"><?= htmlspecialchars($class['training_topic']) ?></div>
                                    </div>
                                    <div class="class-body">
                                        <div class="class-description">
                                            <?= !empty($class['description']) ? nl2br(htmlspecialchars($class['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                                        </div>
                                        <div class="class-meta">
                                            <div class="class-meta-item">
                                                <i class="fas fa-users"></i>
                                                <span><?= $class['participant_count'] ?> Peserta</span>
                                                <?php if (!empty($class['max_participants'])): ?>
                                                    <span class="text-muted">(Maks: <?= $class['max_participants'] ?>)</span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="class-meta-item">
                                                <i class="fas fa-tag"></i>
                                                <span><?= htmlspecialchars($class['training_type']) ?></span>
                                            </div>
                                            <?php if (!empty($class['start_date'])): ?>
                                                <div class="class-meta-item">
                                                    <i class="fas fa-calendar-alt"></i>
                                                    <span>Mulai: <?= date('d M Y', strtotime($class['start_date'])) ?></span>
                                                </div>
                                            <?php endif; ?>
                                            <?php if (!empty($class['end_date'])): ?>
                                                <div class="class-meta-item">
                                                    <i class="fas fa-calendar-check"></i>
                                                    <span>Selesai: <?= date('d M Y', strtotime($class['end_date'])) ?></span>
                                                </div>
                                            <?php endif; ?>
                                            <div class="class-meta-item">
                                                <i class="fas fa-user"></i>
                                                <span>Dibuat oleh: <?= htmlspecialchars($class['creator_name']) ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="class-footer">
                                        <div>
                                            <span class="class-status status-<?= $class['status'] ?>">
                                                <?php
                                                    $status_labels = [
                                                        'active' => 'Aktif',
                                                        'inactive' => 'Tidak Aktif',
                                                        'completed' => 'Selesai'
                                                    ];
                                                    echo $status_labels[$class['status']] ?? $class['status'];
                                                ?>
                                            </span>
                                        </div>
                                        <div class="class-actions">
                                            <a href="manage_class.php?id=<?= $class['id'] ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i> Kelola
                                            </a>
                                            <a href="#" class="btn btn-sm btn-danger delete-class-btn" data-class-id="<?= $class['id'] ?>">
                                                <i class="fas fa-trash"></i> Hapus
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Searchable training dropdown functionality
        const searchInput = document.getElementById('searchTraining');
        const dropdownList = document.getElementById('dropdownTrainingList');
        const originalSelect = document.getElementById('training_id');

        if (searchInput && dropdownList && originalSelect) {
            const items = Array.from(dropdownList.getElementsByTagName('li'));
            let selectedIndex = -1;

            // Show dropdown when input is clicked or focused
            searchInput.addEventListener('click', () => {
                dropdownList.style.display = 'block';
                selectedIndex = -1;
            });

            searchInput.addEventListener('focus', () => {
                dropdownList.style.display = 'block';
                selectedIndex = -1;
            });

            // Filter items as user types
            searchInput.addEventListener('input', () => {
                const searchTerm = searchInput.value.toLowerCase();
                let visibleCount = 0;
                selectedIndex = -1;

                items.forEach((item, index) => {
                    const text = item.textContent.toLowerCase();
                    const isVisible = text.includes(searchTerm);
                    item.style.display = isVisible ? 'block' : 'none';
                    item.classList.remove('highlighted');
                    if (isVisible) visibleCount++;
                });

                dropdownList.style.display = visibleCount > 0 ? 'block' : 'none';

                // Show "no results" message if no matches
                if (visibleCount === 0 && searchTerm.length > 0) {
                    showNoResults(searchTerm);
                }
            });

            // Keyboard navigation
            searchInput.addEventListener('keydown', (e) => {
                const visibleItems = items.filter(item => item.style.display !== 'none');

                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, visibleItems.length - 1);
                    highlightItem(visibleItems, selectedIndex);
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, -1);
                    highlightItem(visibleItems, selectedIndex);
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    if (selectedIndex >= 0 && visibleItems[selectedIndex]) {
                        selectItem(visibleItems[selectedIndex]);
                    }
                } else if (e.key === 'Escape') {
                    dropdownList.style.display = 'none';
                    selectedIndex = -1;
                }
            });

            // Select item when clicked
            items.forEach(item => {
                item.addEventListener('click', () => {
                    selectItem(item);
                });

                // Hover effect
                item.addEventListener('mouseenter', () => {
                    items.forEach(i => i.classList.remove('highlighted'));
                    item.classList.add('highlighted');
                });
            });

            // Hide dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.searchable-select')) {
                    dropdownList.style.display = 'none';
                    selectedIndex = -1;
                }
            });

            // Helper functions
            function selectItem(item) {
                const value = item.getAttribute('data-value');
                const text = item.querySelector('div').textContent;

                searchInput.value = text;
                originalSelect.value = value;
                dropdownList.style.display = 'none';
                selectedIndex = -1;

                // Reset border color
                searchInput.style.borderColor = '';

                // Trigger change event
                const event = new Event('change');
                originalSelect.dispatchEvent(event);
            }

            function highlightItem(visibleItems, index) {
                visibleItems.forEach(item => item.classList.remove('highlighted'));
                if (index >= 0 && visibleItems[index]) {
                    visibleItems[index].classList.add('highlighted');
                    visibleItems[index].scrollIntoView({ block: 'nearest' });
                }
            }

            function showNoResults(searchTerm) {
                const noResultsHtml = `
                    <li style="padding: 12px; color: #999; font-style: italic; text-align: center;">
                        <i class="fas fa-search"></i> Tidak ditemukan training dengan kata kunci "${searchTerm}"
                    </li>
                `;
                dropdownList.innerHTML = noResultsHtml;
                dropdownList.style.display = 'block';
            }
        }

        // Form validation
        const form = document.querySelector('form[method="post"]');
        if (form && originalSelect) {
            form.addEventListener('submit', function(e) {
                if (!originalSelect.value || originalSelect.value === '') {
                    e.preventDefault();
                    alert('Silakan pilih training terlebih dahulu!');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.style.borderColor = '#dc3545';
                    }
                    return false;
                }
            });
        }
    });

    // Delete class functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);

        // Filter functionality
        const filterStatus = document.getElementById('filter-status');
        const filterType = document.getElementById('filter-type');
        const searchInput = document.getElementById('search');

        function applyFilters() {
            const statusFilter = filterStatus.value.toLowerCase();
            const typeFilter = filterType.value;
            const searchFilter = searchInput.value.toLowerCase();

            const classItems = document.querySelectorAll('.class-item');

            classItems.forEach(function(item) {
                const status = item.dataset.status;
                const type = item.dataset.type;
                const title = item.dataset.title;
                const topic = item.dataset.topic;

                const statusMatch = statusFilter === '' || status === statusFilter;
                const typeMatch = typeFilter === '' || type === typeFilter;
                const searchMatch = searchFilter === '' ||
                                   title.includes(searchFilter) ||
                                   topic.includes(searchFilter);

                if (statusMatch && typeMatch && searchMatch) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        filterStatus.addEventListener('change', applyFilters);
        filterType.addEventListener('change', applyFilters);
        searchInput.addEventListener('input', applyFilters);

        // Date validation
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');

        endDateInput.addEventListener('change', function() {
            if (startDateInput.value && endDateInput.value) {
                if (new Date(endDateInput.value) < new Date(startDateInput.value)) {
                    // Use custom modal instead of alert
                    CustomModal.alert('Tanggal selesai tidak boleh lebih awal dari tanggal mulai');
                    endDateInput.value = '';
                }
            }
        });

        startDateInput.addEventListener('change', function() {
            if (startDateInput.value && endDateInput.value) {
                if (new Date(endDateInput.value) < new Date(startDateInput.value)) {
                    // Use custom modal instead of alert
                    CustomModal.alert('Tanggal mulai tidak boleh lebih akhir dari tanggal selesai');
                    startDateInput.value = '';
                }
            }
        });

        // Custom modal for delete confirmation
        const deleteButtons = document.querySelectorAll('.delete-class-btn');
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const classId = this.getAttribute('data-class-id');

                // Use confirmAction function from custom-modal.js
                confirmAction(
                    'Apakah Anda yakin ingin menghapus kelas ini? Semua data terkait kelas ini akan dihapus.',
                    function() {
                        // User clicked "Ya"
                        window.location.href = 'manage_classes.php?delete=' + classId;
                    },
                    function() {
                        // User clicked "Tidak" (optional)
                        console.log('Delete cancelled');
                    },
                    'Konfirmasi Hapus Kelas' // Custom title
                );
            });
        });
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
