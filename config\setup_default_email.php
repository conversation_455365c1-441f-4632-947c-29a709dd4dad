<?php
/**
 * <PERSON>ript untuk memastikan ada konfigurasi email default di database
 * Jalankan script ini jika belum ada konfigurasi email
 */

require_once __DIR__ . '/config.php';

// Cek apakah sudah ada record di settings
$check_query = "SELECT COUNT(*) as count FROM settings WHERE id = 1";
$check_result = $conn->query($check_query);
$count = $check_result->fetch_assoc()['count'];

if ($count == 0) {
    // Insert record baru dengan konfigurasi default
    $insert_query = "INSERT INTO settings (
        id, 
        app_name, 
        smtp_server, 
        smtp_port, 
        smtp_password, 
        sender_email, 
        sender_name, 
        smtp_encryption,
        timezone,
        login_attempts,
        block_time,
        password_expiry,
        require_uppercase,
        require_number,
        require_special,
        min_password_length,
        encrypt_password,
        password_reset_days
    ) VALUES (
        1,
        'Training System PT PAS',
        'smtp.gmail.com',
        587,
        'xoexfurpjbbknila',
        '<EMAIL>',
        'TRAINING PT PAS',
        'tls',
        'Asia/Jakarta',
        5,
        300,
        90,
        1,
        1,
        1,
        8,
        1,
        90
    )";
    
    if ($conn->query($insert_query)) {
        echo "✅ Default email configuration inserted successfully\n";
    } else {
        echo "❌ Failed to insert default configuration: " . $conn->error . "\n";
    }
} else {
    // Update konfigurasi email jika belum ada
    $update_query = "UPDATE settings SET 
        smtp_server = COALESCE(NULLIF(smtp_server, ''), 'smtp.gmail.com'),
        smtp_port = COALESCE(smtp_port, 587),
        smtp_password = COALESCE(NULLIF(smtp_password, ''), 'xoexfurpjbbknila'),
        sender_email = COALESCE(NULLIF(sender_email, ''), '<EMAIL>'),
        sender_name = COALESCE(NULLIF(sender_name, ''), 'TRAINING PT PAS'),
        smtp_encryption = COALESCE(NULLIF(smtp_encryption, ''), 'tls')
        WHERE id = 1";
    
    if ($conn->query($update_query)) {
        echo "✅ Email configuration updated with defaults where missing\n";
    } else {
        echo "❌ Failed to update configuration: " . $conn->error . "\n";
    }
}

// Tampilkan konfigurasi saat ini
$current_query = "SELECT smtp_server, smtp_port, sender_email, sender_name, smtp_encryption FROM settings WHERE id = 1";
$current_result = $conn->query($current_query);

if ($current_result && $current_result->num_rows > 0) {
    $config = $current_result->fetch_assoc();
    echo "\n📧 Current Email Configuration:\n";
    echo "SMTP Server: " . ($config['smtp_server'] ?? 'Not set') . "\n";
    echo "SMTP Port: " . ($config['smtp_port'] ?? 'Not set') . "\n";
    echo "Sender Email: " . ($config['sender_email'] ?? 'Not set') . "\n";
    echo "Sender Name: " . ($config['sender_name'] ?? 'Not set') . "\n";
    echo "Encryption: " . ($config['smtp_encryption'] ?? 'Not set') . "\n";
} else {
    echo "❌ Could not retrieve current configuration\n";
}

$conn->close();
?>
