<?php
session_start();

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Include database connection file
include '../config/config.php';

// Query untuk mengambil pengajuan training
$query = "SELECT ts.id, ts.full_name, ts.email, ts.phone, ts.training_topic, ts.training_date, ts.status, u.name AS approved_by_name
          FROM training_submissions ts
          LEFT JOIN users u ON ts.approved_by = u.id
          WHERE ts.status IN ('approved', 'pending', 'completed')";
$result = mysqli_query($conn, $query);
$submissions = mysqli_fetch_all($result, MYSQLI_ASSOC);

// Cek dan update training yang sudah lewat
foreach ($submissions as $row) {
    $current_date = date("Y-m-d"); // Tanggal saat ini
    if (strtotime($row['training_date']) < strtotime($current_date) && $row['status'] == 'approved') {
        // Update status training yang sudah lewat menjadi 'completed'
        $stmt = $conn->prepare("UPDATE training_submissions SET status='completed' WHERE id=?");
        $stmt->bind_param("i", $row['id']);
        $stmt->execute();
    }
}

// Mengambil data training yang sudah selesai (completed)
$query_completed = "SELECT ts.id, ts.full_name, ts.email, ts.phone, ts.training_topic, ts.training_date, ts.status, u.name AS approved_by_name
                    FROM training_submissions ts
                    LEFT JOIN users u ON ts.approved_by = u.id
                    WHERE ts.status = 'completed'";
$result_completed = mysqli_query($conn, $query_completed);
$submissions_completed = mysqli_fetch_all($result_completed, MYSQLI_ASSOC);

// Proses Approve Training
if (isset($_GET['approve_id'])) {
    $id = $_GET['approve_id'];
    $approved_by = $_SESSION['user_id'];

    $stmt = $conn->prepare("UPDATE training_submissions SET status='approved', approved_by=? WHERE id=?");
    $stmt->bind_param("ii", $approved_by, $id);
    if ($stmt->execute()) {
        header("Location: approved.php");
        exit();
    }
}

// Proses Hapus Training
if (isset($_GET['delete_id'])) {
    $delete_id = $_GET['delete_id'];

    $stmt = $conn->prepare("DELETE FROM training_submissions WHERE id=?");
    $stmt->bind_param("i", $delete_id);
    if ($stmt->execute()) {
        header("Location: approved.php");
        exit();
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .form-container {
        max-width: 1000px;
        margin: 20px auto;
        padding: 20px;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        overflow-x: auto;
    }
    
    .form-header {
        text-align: center;
        margin-bottom: 20px;
    }
    
    table {
        width: 100%;
        border-collapse: collapse;
    }
    
    th, td {
        padding: 10px;
        text-align: left;
        border: 1px solid #ddd;
    }
    
    th {
        background-color: #4CAF50;
        color: white;
    }
    
    td {
        background-color: #f9f9f9;
    }
    
    tr:nth-child(even) td {
        background-color: #f2f2f2;
    }
    
    tr:hover td {
        background-color: #e0e0e0;
    }
  
</style>
<body>
  
<?php include '../config/navbar.php';?>
<div class="container-form">
    <div class="form-container">
        <div class="form-header">
            <h1>Training yang Sudah Terlaksana</h1>
            <p>Daftar training yang telah terlaksana.</p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>No</th>
                    <th>Nama</th>
                    <th>Email</th>
                    <th>Telepon</th>
                    <th>Jenis Training</th>
                    <th>Tanggal Training</th>
                    <th>Approved By</th>
                </tr>
            </thead>
            <tbody>
                <?php
                    $no = 1;
                    foreach ($submissions_completed as $row) {
                        echo "<tr>";
                        echo "<td>{$no}</td>";
                        echo "<td>" . htmlspecialchars($row['full_name']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['email']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['phone']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['training_topic']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['training_date']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['approved_by_name'] ?? 'Admin') . "</td>";
                        echo "</tr>";
                        $no++;
                    }
                ?>
            </tbody>
        </table>
    </div>
    </div>
    <?php include '../config/footer.php';?>
</body>
</html>