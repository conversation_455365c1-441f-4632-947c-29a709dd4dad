<?php
session_start();
include '../config/config.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Ambil pengaturan backup dari database
$query = "SELECT backup_location, compress_backup FROM settings WHERE id = 1";
$result = $conn->query($query);
$settings = $result->fetch_assoc();

$backupLocation = $settings['backup_location'] ?? '../backups';
$compressBackup = ($settings['compress_backup'] ?? '1') == '1';

// Cek apakah lokasi adalah path relatif atau absolut
if (strpos($backupLocation, '/') !== 0 && strpos($backupLocation, ':') !== 1) {
    // Path relatif, tambahkan base path
    $fullPath = dirname(__DIR__) . '/' . ltrim($backupLocation, '/');
} else {
    // Path absolut
    $fullPath = $backupLocation;
}

// Pastikan direktori backup ada
if (!file_exists($fullPath)) {
    if (!mkdir($fullPath, 0755, true)) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Direktori backup tidak ditemukan dan tidak dapat dibuat'
        ]);
        exit();
    }
}

// Buat nama file backup
$timestamp = date('Y-m-d_H-i-s');
$dbName = $db_name; // Gunakan variabel global dari config.php
$backupFileName = "db_{$dbName}_backup_{$timestamp}.sql";
$backupFilePath = "{$fullPath}/{$backupFileName}";

// Command untuk backup database
if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
    // Windows
    $command = "mysqldump -h {$db_host} -u {$db_user}";
    if (!empty($db_pass)) {
        $command .= " -p\"{$db_pass}\"";
    }
    $command .= " {$dbName} > \"{$backupFilePath}\" 2>&1";
} else {
    // Linux/Unix/Mac
    $command = "mysqldump -h {$db_host} -u {$db_user}";
    if (!empty($db_pass)) {
        $command .= " -p'{$db_pass}'";
    }
    $command .= " {$dbName} > '{$backupFilePath}' 2>&1";
}

// Log command untuk debugging (tanpa password)
$log_command = preg_replace('/-p"[^"]*"/', '-p"***"', $command);
error_log("Executing backup command: " . $log_command);

// Jalankan command backup
exec($command, $output, $returnVar);

// Cek apakah backup berhasil dengan mysqldump
$mysqldump_success = ($returnVar === 0 && file_exists($backupFilePath) && filesize($backupFilePath) > 0);

// Jika mysqldump gagal, coba metode PHP
if (!$mysqldump_success) {
    error_log("Mysqldump failed, trying PHP backup method");

    try {
        // Buat file backup
        $backup_file = fopen($backupFilePath, 'w');
        if (!$backup_file) {
            throw new Exception("Could not create backup file: $backupFilePath");
        }

        // Tambahkan header
        fwrite($backup_file, "-- Database Backup for {$dbName}\n");
        fwrite($backup_file, "-- Generated on " . date('Y-m-d H:i:s') . "\n");
        fwrite($backup_file, "-- Using PHP Backup Method\n\n");

        // Set database
        fwrite($backup_file, "USE `{$dbName}`;\n\n");

        // Dapatkan semua tabel
        $tables_result = $conn->query("SHOW TABLES");
        if (!$tables_result) {
            throw new Exception("Failed to get tables: " . $conn->error);
        }

        $tables = [];
        while ($row = $tables_result->fetch_array()) {
            $tables[] = $row[0];
        }

        // Proses setiap tabel
        foreach ($tables as $table) {
            // Dapatkan statement create table
            $create_table_result = $conn->query("SHOW CREATE TABLE `{$table}`");
            if (!$create_table_result) {
                throw new Exception("Failed to get CREATE TABLE for {$table}: " . $conn->error);
            }

            $create_table_row = $create_table_result->fetch_array();
            $create_table_sql = $create_table_row[1];

            // Tulis statement create table
            fwrite($backup_file, "-- Table structure for table `{$table}`\n");
            fwrite($backup_file, "DROP TABLE IF EXISTS `{$table}`;\n");
            fwrite($backup_file, $create_table_sql . ";\n\n");

            // Dapatkan data tabel
            $data_result = $conn->query("SELECT * FROM `{$table}`");
            if (!$data_result) {
                throw new Exception("Failed to get data for {$table}: " . $conn->error);
            }

            if ($data_result->num_rows > 0) {
                fwrite($backup_file, "-- Data for table `{$table}`\n");
                fwrite($backup_file, "INSERT INTO `{$table}` VALUES\n");

                $row_count = 0;
                $total_rows = $data_result->num_rows;

                while ($row = $data_result->fetch_assoc()) {
                    $row_count++;

                    // Mulai baris
                    fwrite($backup_file, "(");

                    // Proses setiap kolom
                    $columns = [];
                    foreach ($row as $column) {
                        if ($column === null) {
                            $columns[] = "NULL";
                        } else {
                            $columns[] = "'" . $conn->real_escape_string($column) . "'";
                        }
                    }

                    // Gabungkan kolom dan akhiri baris
                    fwrite($backup_file, implode(",", $columns));

                    // Jika ini baris terakhir, akhiri dengan titik koma, jika tidak dengan koma
                    if ($row_count == $total_rows) {
                        fwrite($backup_file, ");\n\n");
                    } else {
                        fwrite($backup_file, "),\n");
                    }
                }
            }
        }

        // Tutup file
        fclose($backup_file);

        // Cek apakah backup berhasil
        if (file_exists($backupFilePath) && filesize($backupFilePath) > 0) {
            error_log("PHP backup method successful");
            $mysqldump_success = true;
        } else {
            throw new Exception("Backup file is empty or does not exist");
        }

    } catch (Exception $e) {
        error_log("PHP backup method failed: " . $e->getMessage());

        // Check if mysqldump is available
        $mysqldump_check = '';
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            exec('where mysqldump 2>&1', $check_output, $check_return);
            $mysqldump_check = implode("\n", $check_output);
        } else {
            exec('which mysqldump 2>&1', $check_output, $check_return);
            $mysqldump_check = implode("\n", $check_output);
        }

        // Check directory permissions
        $dir_writable = is_writable($fullPath) ? 'Yes' : 'No';

        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Gagal membuat backup database: ' . $e->getMessage(),
            'output' => $output,
            'debug_info' => [
                'mysqldump_check' => $mysqldump_check,
                'directory_writable' => $dir_writable,
                'backup_path' => $fullPath,
                'command' => $log_command,
                'php_error' => $e->getMessage()
            ]
        ]);
        exit();
    }
}

// Jika masih gagal setelah mencoba kedua metode
if (!$mysqldump_success) {
    // Check if mysqldump is available
    $mysqldump_check = '';
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        exec('where mysqldump 2>&1', $check_output, $check_return);
        $mysqldump_check = implode("\n", $check_output);
    } else {
        exec('which mysqldump 2>&1', $check_output, $check_return);
        $mysqldump_check = implode("\n", $check_output);
    }

    // Check directory permissions
    $dir_writable = is_writable($fullPath) ? 'Yes' : 'No';

    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Gagal membuat backup database dengan kedua metode. Error code: ' . $returnVar,
        'output' => $output,
        'debug_info' => [
            'mysqldump_check' => $mysqldump_check,
            'directory_writable' => $dir_writable,
            'backup_path' => $fullPath,
            'command' => $log_command
        ]
    ]);
    exit();
}

// Kompres file backup jika diaktifkan
if ($compressBackup && extension_loaded('zip')) {
    $zipFileName = "{$backupFilePath}.zip";
    $zip = new ZipArchive();

    if ($zip->open($zipFileName, ZipArchive::CREATE) === TRUE) {
        $zip->addFile($backupFilePath, $backupFileName);
        $zip->close();

        // Hapus file SQL asli jika kompresi berhasil
        if (file_exists($zipFileName)) {
            unlink($backupFilePath);
            $backupFilePath = $zipFileName;
            $backupFileName .= '.zip';
        }
    }
}

// Log aktivitas
$userId = $_SESSION['user_id'];
$query = "INSERT INTO activity_logs (user_id, action, category, timestamp)
          VALUES (?, 'Membuat backup manual', 'backup', NOW())";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $userId);
$stmt->execute();

// Kembalikan response sukses
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'message' => 'Backup database berhasil dibuat',
    'file_path' => $backupFilePath,
    'file_name' => $backupFileName
]);
