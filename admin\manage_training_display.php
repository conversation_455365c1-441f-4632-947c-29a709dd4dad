<?php
/**
 * Manage Training Display - Interface untuk mengelola training yang ditampilkan di kalender
 */

include '../config/config.php';
include 'security.php';

// Set timezone
date_default_timezone_set('Asia/Jakarta');
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .training-card {
            transition: all 0.3s ease;
            border-left: 4px solid;
        }
        .training-card.online {
            border-left-color: #007bff;
        }
        .training-card.offline {
            border-left-color: #28a745;
        }
        .training-card.hidden {
            opacity: 0.6;
            background-color: #f8f9fa;
        }
        .training-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status-badge {
            font-size: 0.8em;
            padding: 4px 8px;
        }
        .search-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-calendar-alt"></i> Admin Training
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="calendar_management.php">
                    <i class="fas fa-calendar"></i> Kalender
                </a>
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="search-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8 mx-auto text-center">
                    <h2><i class="fas fa-eye"></i> Kelola Tampilan Training</h2>
                    <p class="lead">Pilih training yang akan ditampilkan atau disembunyikan dari kalender</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- Filter Section -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="trainingType" class="form-label">Tipe Training</label>
                                <select class="form-select" id="trainingType">
                                    <option value="offline">Offline Training</option>
                                    <option value="online">Online Training</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="displayFilter" class="form-label">Status Tampilan</label>
                                <select class="form-select" id="displayFilter">
                                    <option value="all">Semua Training</option>
                                    <option value="displayed">Ditampilkan di Kalender</option>
                                    <option value="hidden">Disembunyikan dari Kalender</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="searchInput" class="form-label">Cari Training</label>
                                <input type="text" class="form-control" id="searchInput" placeholder="Cari berdasarkan judul...">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <button class="btn btn-primary" onclick="loadTrainings()">
                                    <i class="fas fa-search"></i> Cari Training
                                </button>
                                <button class="btn btn-success" onclick="showAllTrainings()">
                                    <i class="fas fa-eye"></i> Tampilkan Semua
                                </button>
                                <button class="btn btn-warning" onclick="hideAllTrainings()">
                                    <i class="fas fa-eye-slash"></i> Sembunyikan Semua
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4 id="totalCount">0</h4>
                        <p>Total Training</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4 id="displayedCount">0</h4>
                        <p>Ditampilkan</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4 id="hiddenCount">0</h4>
                        <p>Disembunyikan</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4 id="calendarCount">0</h4>
                        <p>Di Kalender</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Training List -->
        <div class="row">
            <div class="col-md-12">
                <div id="loadingSpinner" class="text-center" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Memuat training...</p>
                </div>
                
                <div id="trainingList" class="row">
                    <!-- Training cards will be loaded here -->
                </div>
                
                <div id="noResults" class="text-center" style="display: none;">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Tidak ada training ditemukan</strong><br>
                        Coba ubah filter atau kata kunci pencarian.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentTrainings = [];

        // Load trainings on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadTrainings();
        });

        function loadTrainings() {
            const trainingType = document.getElementById('trainingType').value;
            const searchQuery = document.getElementById('searchInput').value;
            const displayFilter = document.getElementById('displayFilter').value;
            
            showLoading(true);
            
            // Load all trainings (including displayed ones for management)
            const url = `api/get_existing_trainings.php?type=${trainingType}&search=${encodeURIComponent(searchQuery)}&exclude_displayed=false`;
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentTrainings = data.trainings;
                        filterAndDisplayTrainings(displayFilter);
                        updateStatistics();
                    } else {
                        throw new Error(data.message || 'Failed to load trainings');
                    }
                })
                .catch(error => {
                    console.error('Error loading trainings:', error);
                    showError('Error loading trainings: ' + error.message);
                })
                .finally(() => {
                    showLoading(false);
                });
        }

        function filterAndDisplayTrainings(filter) {
            let filteredTrainings = currentTrainings;
            
            if (filter === 'displayed') {
                filteredTrainings = currentTrainings.filter(t => !t.is_displayed);
            } else if (filter === 'hidden') {
                filteredTrainings = currentTrainings.filter(t => t.is_displayed);
            }
            
            displayTrainings(filteredTrainings);
        }

        function displayTrainings(trainings) {
            const container = document.getElementById('trainingList');
            const noResults = document.getElementById('noResults');
            
            if (trainings.length === 0) {
                container.innerHTML = '';
                noResults.style.display = 'block';
                return;
            }
            
            noResults.style.display = 'none';
            
            const html = trainings.map(training => createTrainingCard(training)).join('');
            container.innerHTML = html;
        }

        function createTrainingCard(training) {
            const isDisplayed = !training.is_displayed;
            const statusClass = isDisplayed ? 'success' : 'secondary';
            const statusText = isDisplayed ? 'Ditampilkan' : 'Disembunyikan';
            const statusIcon = isDisplayed ? 'eye' : 'eye-slash';
            const actionText = isDisplayed ? 'Sembunyikan' : 'Tampilkan';
            const actionClass = isDisplayed ? 'warning' : 'success';
            const actionIcon = isDisplayed ? 'eye-slash' : 'eye';
            
            return `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card training-card ${training.type} ${!isDisplayed ? 'hidden' : ''}">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="badge bg-${training.type === 'online' ? 'primary' : 'success'}">${training.type.toUpperCase()}</span>
                                <span class="badge bg-${statusClass} status-badge">
                                    <i class="fas fa-${statusIcon}"></i> ${statusText}
                                </span>
                            </div>
                            <h6 class="card-title">${training.title}</h6>
                            <p class="card-text text-muted small">${training.description || 'Tidak ada deskripsi'}</p>
                            <div class="small text-muted mb-3">
                                <div><i class="fas fa-calendar"></i> ${training.date}</div>
                                <div><i class="fas fa-clock"></i> ${training.time || 'Waktu belum ditentukan'}</div>
                                <div><i class="fas fa-map-marker-alt"></i> ${training.location || 'Lokasi belum ditentukan'}</div>
                            </div>
                            <button class="btn btn-${actionClass} btn-sm w-100" onclick="toggleTrainingDisplay('${training.id}', '${training.type}', '${isDisplayed ? 'hide' : 'show'}')">
                                <i class="fas fa-${actionIcon}"></i> ${actionText}
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function toggleTrainingDisplay(trainingId, trainingType, action) {
            const formData = new FormData();
            formData.append('training_id', trainingId);
            formData.append('training_type', trainingType);
            formData.append('action', action);
            
            fetch('api/toggle_training_display.php', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccess(data.message);
                        loadTrainings(); // Reload to update display
                    } else {
                        throw new Error(data.message || 'Failed to toggle training display');
                    }
                })
                .catch(error => {
                    console.error('Error toggling training display:', error);
                    showError('Error: ' + error.message);
                });
        }

        function showAllTrainings() {
            if (confirm('Tampilkan semua training di kalender?')) {
                bulkToggleTrainings('show');
            }
        }

        function hideAllTrainings() {
            if (confirm('Sembunyikan semua training dari kalender?')) {
                bulkToggleTrainings('hide');
            }
        }

        function bulkToggleTrainings(action) {
            const trainingType = document.getElementById('trainingType').value;
            const promises = currentTrainings.map(training => {
                const formData = new FormData();
                formData.append('training_id', training.id);
                formData.append('training_type', training.type);
                formData.append('action', action);
                
                return fetch('api/toggle_training_display.php', {
                    method: 'POST',
                    body: formData
                });
            });
            
            Promise.all(promises)
                .then(() => {
                    showSuccess(`Semua training berhasil ${action === 'show' ? 'ditampilkan' : 'disembunyikan'}`);
                    loadTrainings();
                })
                .catch(error => {
                    console.error('Error in bulk operation:', error);
                    showError('Error dalam operasi bulk: ' + error.message);
                });
        }

        function updateStatistics() {
            const total = currentTrainings.length;
            const displayed = currentTrainings.filter(t => !t.is_displayed).length;
            const hidden = currentTrainings.filter(t => t.is_displayed).length;
            
            document.getElementById('totalCount').textContent = total;
            document.getElementById('displayedCount').textContent = displayed;
            document.getElementById('hiddenCount').textContent = hidden;
            document.getElementById('calendarCount').textContent = displayed;
        }

        function showLoading(show) {
            document.getElementById('loadingSpinner').style.display = show ? 'block' : 'none';
        }

        function showSuccess(message) {
            alert('✅ ' + message);
        }

        function showError(message) {
            alert('❌ ' + message);
        }

        // Event listeners
        document.getElementById('displayFilter').addEventListener('change', function() {
            filterAndDisplayTrainings(this.value);
        });

        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loadTrainings();
            }
        });
    </script>
</body>
</html>
