<?php
session_start();
include '../config/config.php';

// Check if user has a valid session from direct activation
if (!isset($_SESSION['temp_user_id']) || !isset($_SESSION['temp_user_name']) || !isset($_SESSION['temp_user_nik'])) {
    header('Location: Aktivasi.php');
    exit();
}

$user_id = $_SESSION['temp_user_id'];
$user_name = $_SESSION['temp_user_name'];
$user_nik = $_SESSION['temp_user_nik'];
$message = '';
$success = false;

// Check if user exists and is active
$query = "SELECT * FROM users WHERE id = ? AND is_active = 1";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    // User not found or not active, redirect to activation page
    header('Location: Aktivasi.php');
    exit();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $password = trim($_POST['password']);
    $confirm_password = trim($_POST['confirm_password']);

    // Validate inputs
    $errors = [];

    // Validate email
    if (empty($email)) {
        $errors[] = "Email tidak boleh kosong.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Format email tidak valid.";
    } else {
        // Check if email already exists for another user
        $check_query = "SELECT id FROM users WHERE email = ? AND id != ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("si", $email, $user_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $errors[] = "Email sudah digunakan oleh pengguna lain.";
        }
    }

    // Validate password
    if (empty($password)) {
        $errors[] = "Password tidak boleh kosong.";
    } elseif ($password !== $confirm_password) {
        $errors[] = "Konfirmasi password tidak cocok.";
    } else {
        // Validate password strength using security settings from database
        $password_errors = validate_password_strength($password);
        if (!empty($password_errors)) {
            $errors = array_merge($errors, $password_errors);
        }
    }

    // If no errors, update user data
    if (empty($errors)) {
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        // Update user data
        $update_query = "UPDATE users SET email = ?, password = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("ssi", $email, $hashed_password, $user_id);

        if ($update_stmt->execute()) {
            $success = true;
            $message = '<div class="success-message">Email dan password berhasil diperbarui! Anda akan dialihkan ke halaman login.</div>';

            // Clear session data
            unset($_SESSION['temp_user_id']);
            unset($_SESSION['temp_user_name']);
            unset($_SESSION['temp_user_nik']);

            // Redirect to login page after 3 seconds
            header('Refresh: 3; URL=login.php');
        } else {
            $message = '<div class="error-message">Gagal memperbarui data. Silakan coba lagi.</div>';
        }
    } else {
        // Display errors
        $message = '<div class="error-message"><ul>';
        foreach ($errors as $error) {
            $message .= '<li>' . $error . '</li>';
        }
        $message .= '</ul></div>';
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<body>
    <?php include '../config/navbarb.php'; ?>
<div class="jarak"></div>
    <div class="container-form">
        <div class="update-container">
            <h2>Perbarui Email dan Password</h2>
            <p>Selamat! Akun Anda telah berhasil diaktivasi. Silakan perbarui email dan password Anda.</p>

            <div class="user-info">
                <p><strong>Nama:</strong> <?php echo htmlspecialchars($user_name); ?></p>
                <p><strong>NIK:</strong> <?php echo htmlspecialchars($user_nik); ?></p>
            </div>

            <?php if ($message): ?>
                <?php echo $message; ?>
            <?php endif; ?>

            <?php if (!$success): ?>
                <?php
                // Get security settings for password requirements
                $security_settings = get_security_settings();
                ?>

                <div class="password-requirements">
                    <h4>Persyaratan Password</h4>
                    <ul>
                        <li>Minimal <?php echo $security_settings['min_password_length']; ?> karakter</li>
                        <?php if ($security_settings['require_uppercase']): ?>
                            <li>Minimal satu huruf besar (A-Z)</li>
                        <?php endif; ?>
                        <?php if ($security_settings['require_number']): ?>
                            <li>Minimal satu angka (0-9)</li>
                        <?php endif; ?>
                        <?php if ($security_settings['require_special']): ?>
                            <li>Minimal satu karakter khusus (!@#$%^&*)</li>
                        <?php endif; ?>
                    </ul>
                </div>

                <form method="POST">
                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="email"
                               name="email"
                               id="email"
                               required
                               placeholder="Masukkan email valid Anda"
                               class="form-control"
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                        <small class="form-text">Masukkan alamat email yang valid untuk menerima notifikasi.</small>
                    </div>

                    <div class="form-group">
                        <label for="password">Password Baru:</label>
                        <input type="password"
                               name="password"
                               id="password"
                               required
                               placeholder="Sesuai persyaratan di atas"
                               class="form-control">
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">Konfirmasi Password:</label>
                        <input type="password"
                               name="confirm_password"
                               id="confirm_password"
                               required
                               placeholder="Masukkan password yang sama"
                               class="form-control">
                    </div>

                    <button type="submit" class="btn btn-primary"><i class="fas fa-sync-alt"></i> Perbarui Data</button>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <?php include '../config/footer.php'; ?>

    <style>
    .update-container {
        max-width: 500px;
        margin: 50px auto;
        padding: 25px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .user-info {
        background: #f9f9f9;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        border-left: 4px solid #BF0000;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
    }

    .form-text {
        font-size: 14px;
        color: #666;
        margin-top: 5px;
    }

    .btn-primary {
        background: #BF0000;
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
        width: 100%;
    }

    .btn-primary:hover {
        background: #900000;
    }

    .success-message {
        color: green;
        padding: 15px;
        background: #e8f5e9;
        border-radius: 4px;
        margin: 15px 0;
    }

    .error-message {
        color: #d32f2f;
        padding: 15px;
        background: #ffebee;
        border-radius: 4px;
        margin: 15px 0;
    }

    .error-message ul {
        margin: 5px 0 0 20px;
        padding: 0;
    }

    /* Password requirements box */
    .password-requirements {
        background-color: #f9f9f9;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        border-left: 4px solid #BF0000;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    .password-requirements h4 {
        margin-top: 0;
        margin-bottom: 10px;
        color: #333;
        font-size: 16px;
        font-weight: 600;
    }

    .password-requirements ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .password-requirements li {
        margin-bottom: 5px;
        color: #555;
        font-size: 14px;
        line-height: 1.4;
    }
    </style>
</body>
</html>
