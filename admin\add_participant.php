<?php
/**
 * Add Participant Page for Admin
 * This page allows admins to add participants to a training class
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if class ID is provided
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;

if ($class_id <= 0) {
    header('Location: manage_classes.php');
    exit();
}

// Get class information
$class_query = "SELECT c.*, t.training_topic
               FROM training_classes c
               JOIN training_submissions t ON c.training_id = t.id
               WHERE c.id = ?";
$stmt = $conn->prepare($class_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$class = $result->fetch_assoc();
$stmt->close();

if (!$class) {
    header('Location: manage_classes.php');
    exit();
}

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_participant'])) {
    $selected_users = isset($_POST['selected_users']) ? $_POST['selected_users'] : [];
    $role = $_POST['role'];

    if (empty($selected_users)) {
        $error_message = "Pilih setidaknya satu pengguna.";
    } else {
        $success_count = 0;
        $error_count = 0;

        foreach ($selected_users as $selected_user_id) {
            // Check if user is already a participant
            $check_query = "SELECT * FROM training_participants
                           WHERE class_id = ? AND user_id = ?";
            $stmt = $conn->prepare($check_query);
            $stmt->bind_param("ii", $class_id, $selected_user_id);
            $stmt->execute();
            $check_result = $stmt->get_result();

            if ($check_result->num_rows > 0) {
                $error_count++;
                continue; // Skip if already a participant
            }

            // Add user as participant
            $insert_query = "INSERT INTO training_participants (class_id, user_id, role, status)
                            VALUES (?, ?, ?, 'active')";
            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("iis", $class_id, $selected_user_id, $role);

            if ($stmt->execute()) {
                $success_count++;
            } else {
                $error_count++;
            }
        }

        if ($success_count > 0) {
            $success_message = "$success_count peserta berhasil ditambahkan ke kelas.";
        }

        if ($error_count > 0) {
            $error_message = "$error_count peserta gagal ditambahkan (mungkin sudah terdaftar).";
        }
    }
}

// Get existing participants
$existing_participants = [];
$participants_query = "SELECT user_id FROM training_participants WHERE class_id = ?";
$stmt = $conn->prepare($participants_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$participants_result = $stmt->get_result();

while ($row = $participants_result->fetch_assoc()) {
    $existing_participants[] = $row['user_id'];
}
$stmt->close();

// Get users who are not already participants
$users = [];

// First, let's check the structure of the users table to find the name column
$user_table_query = "DESCRIBE users";
$user_table_result = $conn->query($user_table_query);
$name_column = 'user_name'; // Default to user_name if we can't find a better column

if ($user_table_result) {
    while ($column = $user_table_result->fetch_assoc()) {
        // Look for common name column patterns
        if (in_array(strtolower($column['Field']), ['full_name', 'name', 'user_name', 'username', 'nama'])) {
            $name_column = $column['Field'];
            // Prefer full_name or name if available
            if (in_array(strtolower($column['Field']), ['full_name', 'name', 'nama'])) {
                break;
            }
        }
    }
}

// Get users who are not already participants
$users_query = "SELECT id, $name_column as user_name, email, role_id
               FROM users
               WHERE id NOT IN (" . implode(',', array_merge($existing_participants, [0])) . ")
               ORDER BY $name_column ASC";
$result = $conn->query($users_query);

if ($result) {
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }
}

// Filter users by search term
$filtered_users = $users;
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $search_term = strtolower($_GET['search']);
    $filtered_users = array_filter($users, function($user) use ($search_term) {
        return strpos(strtolower($user['user_name']), $search_term) !== false ||
               strpos(strtolower($user['email']), $search_term) !== false;
    });
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .user-list {
        max-height: 500px;
        overflow-y: auto;
    }

    .user-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: #6c757d;
    }

    .user-name {
        font-weight: 600;
    }

    .user-email {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .user-role {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .search-box {
        margin-bottom: 20px;
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }

    .search-results-info {
        margin-bottom: 10px;
        font-size: 0.9rem;
        color: #6c757d;
    }

    .select-all-container {
        margin-bottom: 15px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 8px;
        display: flex;
        align-items: center;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-user-plus"></i> Tambah Peserta</h1>
                <a href="manage_class.php?id=<?= $class_id ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Kelas
                </a>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Informasi Kelas</h5>
                </div>
                <div class="card-body">
                    <h5><?= htmlspecialchars($class['title']) ?></h5>
                    <p class="text-muted"><?= htmlspecialchars($class['training_topic']) ?></p>
                </div>
            </div>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Pilih Peserta</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="role" class="form-label">Peran dalam Kelas</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="student">Peserta</option>
                                <option value="instructor">Instruktur</option>
                                <option value="assistant">Asisten</option>
                            </select>
                            <div class="form-text">Pilih peran untuk pengguna yang akan ditambahkan</div>
                        </div>

                        <div class="search-box">
                            <div class="input-group">
                                <input type="text" class="form-control" id="search-input" placeholder="Cari pengguna..." value="<?= isset($_GET['search']) ? htmlspecialchars($_GET['search']) : '' ?>">
                                <button class="btn btn-outline-secondary" type="button" id="search-button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="form-text">Ketik untuk mencari. Hasil akan muncul secara otomatis.</div>
                        </div>

                        <div class="search-results-info" id="search-results-info"></div>

                        <div class="select-all-container" id="select-all-container" style="display: none;">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select-all-checkbox">
                                <label class="form-check-label" for="select-all-checkbox">
                                    <strong>Pilih Semua</strong>
                                </label>
                            </div>
                        </div>

                        <div class="loading-spinner" id="loading-spinner">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Mencari pengguna...</p>
                        </div>

                        <div class="user-list" id="user-list">
                            <!-- User list will be loaded here via AJAX -->
                            <?php if (empty($filtered_users)): ?>
                                <div class="alert alert-info">
                                    Mulai ketik untuk mencari pengguna.
                                </div>
                            <?php else: ?>
                                <?php foreach ($filtered_users as $user): ?>
                                    <div class="user-card">
                                        <div class="form-check">
                                            <input class="form-check-input user-checkbox" type="checkbox" name="selected_users[]" value="<?= $user['id'] ?>" id="user-<?= $user['id'] ?>">
                                            <label class="form-check-label" for="user-<?= $user['id'] ?>">
                                                <div class="user-info">
                                                    <div class="user-avatar">
                                                        <?= strtoupper(substr($user['user_name'], 0, 1)) ?>
                                                    </div>
                                                    <div>
                                                        <div class="user-name"><?= htmlspecialchars($user['user_name']) ?></div>
                                                        <div class="user-email"><?= htmlspecialchars($user['email']) ?></div>
                                                        <div class="user-role">
                                                            <?php
                                                                $role_labels = [
                                                                    '1' => 'Pemohon',
                                                                    '2' => 'Dept Head',
                                                                    '3' => 'HRD',
                                                                    '4' => 'GA',
                                                                    '5' => 'Factory Manager',
                                                                    '6' => 'Direktur',
                                                                    '99' => 'Admin'
                                                                ];
                                                                echo isset($role_labels[$user['role_id']]) ? $role_labels[$user['role_id']] : 'Role ' . $user['role_id'];
                                                            ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <div class="mt-3">
                            <button type="submit" name="add_participant" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Tambahkan Peserta Terpilih
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);

        // Elements
        const searchInput = document.getElementById('search-input');
        const searchButton = document.getElementById('search-button');
        const userList = document.getElementById('user-list');
        const loadingSpinner = document.getElementById('loading-spinner');
        const searchResultsInfo = document.getElementById('search-results-info');
        const selectAllContainer = document.getElementById('select-all-container');
        const selectAllCheckbox = document.getElementById('select-all-checkbox');

        // Debounce function to limit how often a function can be called
        function debounce(func, wait) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(function() {
                    func.apply(context, args);
                }, wait);
            };
        }

        // Function to perform AJAX search
        function performAjaxSearch() {
            const searchTerm = searchInput.value.trim();

            // Show loading spinner
            loadingSpinner.style.display = 'block';

            // Clear previous results info
            searchResultsInfo.textContent = '';

            // Hide select all container
            selectAllContainer.style.display = 'none';

            // Make AJAX request
            fetch('search_users_ajax.php?class_id=<?= $class_id ?>&search=' + encodeURIComponent(searchTerm))
                .then(response => response.json())
                .then(data => {
                    // Hide loading spinner
                    loadingSpinner.style.display = 'none';

                    // Update user list
                    userList.innerHTML = data.html;

                    // Update results info
                    if (data.count > 0) {
                        searchResultsInfo.textContent = `Ditemukan ${data.count} pengguna`;
                        selectAllContainer.style.display = 'block';
                    } else {
                        searchResultsInfo.textContent = 'Tidak ada pengguna yang ditemukan';
                        selectAllContainer.style.display = 'none';
                    }

                    // Reinitialize select all functionality
                    initSelectAll();
                })
                .catch(error => {
                    console.error('Error:', error);
                    loadingSpinner.style.display = 'none';
                    userList.innerHTML = '<div class="alert alert-danger">Terjadi kesalahan saat mencari pengguna.</div>';
                });
        }

        // Debounced search function
        const debouncedSearch = debounce(performAjaxSearch, 500);

        // Event listeners
        searchInput.addEventListener('input', debouncedSearch);

        searchButton.addEventListener('click', performAjaxSearch);

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performAjaxSearch();
            }
        });

        // Function to initialize select all functionality
        function initSelectAll() {
            const userCheckboxes = document.querySelectorAll('.user-checkbox');

            // Reset select all checkbox
            selectAllCheckbox.checked = false;

            // Add event listener to select all checkbox
            selectAllCheckbox.addEventListener('change', function() {
                userCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            // Add event listeners to user checkboxes
            userCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    // Check if all checkboxes are checked
                    const allChecked = Array.from(userCheckboxes).every(cb => cb.checked);
                    selectAllCheckbox.checked = allChecked;
                });
            });
        }

        // Initialize select all functionality
        initSelectAll();

        // Perform initial search if there's a search term
        if (searchInput.value.trim()) {
            performAjaxSearch();
        }
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
