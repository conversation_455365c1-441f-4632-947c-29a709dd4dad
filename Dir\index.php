<?php
/**
 * Index file for pemohon (applicant) role
 * This file displays a landing page for the pemohon role
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';
// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['full_name'] ?? 'Pengguna';
$user_dept = $_SESSION['user_dept'] ?? '';
$user_jabatan = $_SESSION['user_jabatan'] ?? '';

// Get training counts using consistent logic (same as pemohon pages)
$counts_query = "SELECT status, approved_dir FROM training_submissions WHERE user_id = ?";
$stmt = $conn->prepare($counts_query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$counts_result = $stmt->get_result();

$pending_count = 0;
$approved_count = 0;
$rejected_count = 0;

while ($row = $counts_result->fetch_assoc()) {
    if ($row['status'] == 'Rejected') {
        $rejected_count++;
    } elseif ($row['approved_dir'] == 'Approved') {
        $approved_count++;
    } elseif ($row['approved_dir'] == 'Pending') {
        $pending_count++;
    }
}
$stmt->close();

// Get training submissions that need approval from Dir (role_id = 5)
// Dir receives training that has been approved by previous levels and current_approver_role_id = 5
$approval_query = "SELECT COUNT(*) as count FROM training_submissions ts
                  LEFT JOIN users u ON ts.user_id = u.id
                  WHERE ts.current_approver_role_id = 5
                  AND ts.status != 'Rejected'
                  AND (ts.approved_dir IS NULL OR ts.approved_dir = 'Pending')";

$stmt = $conn->prepare($approval_query);
$stmt->execute();
$approval_result = $stmt->get_result();
$approval_count = $approval_result->fetch_assoc()['count'];
$stmt->close();

// Get recent training submissions that need approval from Dir (for detailed display)
$recent_approval_query = "SELECT ts.id, ts.training_topic, ts.start_date, ts.end_date, ts.is_confirmed,
                         u.name AS requester_name, COALESCE(ts.departemen, u.dept) AS departemen
                         FROM training_submissions ts
                         LEFT JOIN users u ON ts.user_id = u.id
                         WHERE ts.current_approver_role_id = 5
                         AND ts.status != 'Rejected'
                         AND (ts.approved_dir IS NULL OR ts.approved_dir = 'Pending')
                         ORDER BY ts.id DESC LIMIT 5";

$stmt = $conn->prepare($recent_approval_query);
$stmt->execute();
$recent_approval_result = $stmt->get_result();
$recent_approvals = [];
while ($row = $recent_approval_result->fetch_assoc()) {
    $recent_approvals[] = $row;
}
$stmt->close();

// Check if notification should be shown (only show once per session unless count changes)
$show_notification = false;
if ($approval_count > 0) {
    $session_key = 'approval_notification_shown_' . $user_id;
    $session_count_key = 'approval_count_' . $user_id;

    // Show notification if:
    // 1. Never shown in this session, OR
    // 2. The count has changed since last shown
    if (!isset($_SESSION[$session_key]) ||
        !isset($_SESSION[$session_count_key]) ||
        $_SESSION[$session_count_key] != $approval_count) {
        $show_notification = true;
        $_SESSION[$session_key] = true;
        $_SESSION[$session_count_key] = $approval_count;
    }
}

// Get recent trainings
$recent_query = "SELECT id, training_topic, start_date, end_date, is_confirmed, status
                FROM training_submissions
                WHERE user_id = ?
                ORDER BY id DESC LIMIT 5";
$stmt = $conn->prepare($recent_query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$recent_result = $stmt->get_result();
$recent_trainings = [];
while ($row = $recent_result->fetch_assoc()) {
    $recent_trainings[] = $row;
}
$stmt->close();

// Get announcements (if the announcements table exists)
$announcements = [];

// Check if the announcements table exists
$table_exists = false;
$check_table_query = "SHOW TABLES LIKE 'announcements'";
$table_result = $conn->query($check_table_query);
if ($table_result && $table_result->num_rows > 0) {
    $table_exists = true;
}

// Only query the announcements table if it exists
if ($table_exists) {
    // Get current date for expiry check
    $current_date = date('Y-m-d');

    // First, let's check the structure of the users table to find the name column
    $user_table_query = "DESCRIBE users";
    $user_table_result = $conn->query($user_table_query);
    $name_column = 'user_name'; // Default to user_name if we can't find a better column

    if ($user_table_result) {
        while ($column = $user_table_result->fetch_assoc()) {
            // Look for common name column patterns
            if (in_array(strtolower($column['Field']), ['full_name', 'name', 'user_name', 'username', 'nama'])) {
                $name_column = $column['Field'];
                // Prefer full_name or name if available
                if (in_array(strtolower($column['Field']), ['full_name', 'name', 'nama'])) {
                    break;
                }
            }
        }
    }

    // Get announcements that are active, not expired, and targeted to this user
    $announcement_query = "SELECT a.*, u.$name_column as creator_name
                          FROM announcements a
                          LEFT JOIN users u ON a.created_by = u.id
                          INNER JOIN announcement_recipients ar ON a.id = ar.announcement_id
                          WHERE a.active = 1
                          AND (a.expiry_date IS NULL OR a.expiry_date >= ?)
                          AND ar.user_id = ?
                          GROUP BY a.id
                          ORDER BY a.created_at DESC LIMIT 5";

    $stmt = $conn->prepare($announcement_query);
    $user_id = $_SESSION['user_id'];
    $stmt->bind_param("si", $current_date, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $announcements[] = $row;
    }
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<link rel="stylesheet" href="../css/universal-calendar.css">
<script src="../js/universal-calendar.js"></script>
<style>
:root {
    --primary-color: rgb(157, 0, 0);
    --primary-color-dark: rgb(127, 0, 0);
    --primary-color-light: rgba(157, 0, 0, 0.1);
    --secondary-color: #2c3e50;
    --success-color: #4CAF50;
    --success-color-light: #e8f5e9;
    --warning-color: #ffc107;
    --danger-color: #f44336;
    --info-color: #2196F3;
    --text-dark: #333;
    --text-light: #757575;
    --white: #ffffff;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
    --shadow-lg: 0 6px 15px rgba(0,0,0,0.1);
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
    --font-size-xs: 11px;
    --font-size-sm: 13px;
    --font-size-md: 16px;
    --font-size-lg: 20px;
    --font-size-xl: 24px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
}

body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
}


.landing-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--white);
    padding: var(--spacing-xl) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    text-align: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    transform: rotate(30deg);
    pointer-events: none;
}

.hero-section h1 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.hero-section p {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-lg);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-lg);
}

.btn-hero {
    padding: 12px 24px;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 180px;
}

.btn-primary {
    background-color: var(--white);
    color: var(--primary-color);
    border: none;
}

.btn-primary:hover {
    color: var(--primary-color-dark);
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-secondary {
    background-color: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-card .icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.stat-card h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-xs);
    color: var(--text-dark);
}

.stat-card .number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-card .label {
    color: var(--text-dark);
    font-size: var(--font-size-sm);
}

.card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
    transition: box-shadow var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header .icon {
    font-size: 1.2em;
}

.card-body {
    padding: var(--spacing-lg);
}

.recent-trainings {
    list-style: none;
    padding: 0;
    margin: 0;
}

.recent-trainings li {
    padding: var(--spacing-md);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: background-color var(--transition-fast);
}

.recent-trainings li:last-child {
    border-bottom: none;
}

.recent-trainings li:hover {
    background-color: rgba(0,0,0,0.02);
}

.training-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.training-info {
    flex: 1;
}

.training-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 5px;
    font-size: var(--font-size-md);
}

.training-date {
    color: var(--text-dark);
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.status-pending {
    background-color: rgba(255, 193, 7, 0.2);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.4);
}

.status-approved {
    background-color: rgba(76, 175, 80, 0.2);
    color: #155724;
    border: 1px solid rgba(76, 175, 80, 0.4);
}

.status-rejected {
    background-color: rgba(244, 67, 54, 0.2);
    color: #721c24;
    border: 1px solid rgba(244, 67, 54, 0.4);
}

.status-completed {
    background-color: rgba(33, 150, 243, 0.2);
    color: #0c5460;
    border: 1px solid rgba(33, 150, 243, 0.4);
}

.announcements {
    list-style: none;
    padding: 0;
    margin: 0;
}

.announcement-item {
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    margin: var(--spacing-md);
    background-color: var(--primary-color-dark);
    color: var(--text-dark);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    padding: var(--spacing-md);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: background-color var(--transition-fast);
}

.announcement-item:last-child {
    border-bottom: none;
}

.announcement-item:hover {
    background-color: var(--primary-color);
    color: var(--text-dark);
    box-shadow: var(--shadow-md);
}
.announcement-item:hover .announcement-title {
    color: var(--text-light);
}

.announcement-title {
    background-color: var(--primary-color);
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 5px;
    font-size: var(--font-size-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: color var(--transition-fast);
}

.announcement-meta {
    color: var(--text-light);
    font-size: var(--font-size-xs);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}
.announcement-item:hover .announcement-title{
    color: var(--text-light);
    background-color: var(--primary-color-dark);
}
.announcement-content {
    background-color: var(--white);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    color: var(--text-dark);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    white-space: pre-line;
}

.expiry-badge {
    display: inline-block;
    font-size: var(--font-size-xs);
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
    vertical-align: middle;
}

.quick-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.quick-link {
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    text-decoration: none;
    color: var(--text-dark);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.quick-link:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    color: var(--primary-color);
    text-decoration: none;
}

.quick-link .icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.quick-link .title {
    font-weight: 600;
    font-size: var(--font-size-md);
}

.quick-link .description {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
}

.empty-state .icon {
    font-size: 3rem;
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.empty-state .message {
    color: var(--text-dark);
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-md);
}

.view-all {
    display: block;
    text-align: center;
    padding: var(--spacing-md);
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--transition-fast);
}

.view-all:hover {
    color: var(--primary-color-dark);
    text-decoration: none;
}

.view-all i {
    margin-left: 5px;
}

@media (max-width: 992px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Calendar Styles */
.calendar-container {
    background: var(--white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background-color: var(--primary-color);
    color: var(--white);
}

.calendar-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.calendar-nav button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--white);
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.calendar-nav button:hover {
    background: rgba(255, 255, 255, 0.3);
}

.calendar-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: #e0e0e0;
    max-width: 100%;
    width: 100%;
    overflow: hidden;
}

.calendar-day-header {
    background-color: var(--primary-color-light);
    color: var(--primary-color);
    padding: var(--spacing-sm);
    text-align: center;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.calendar-day {
    background-color: var(--white);
    min-height: 100px;
    padding: var(--spacing-xs);
    position: relative;
    cursor: pointer;
    transition: background-color var(--transition-fast);
    overflow: hidden;
    word-wrap: break-word;
}

.calendar-day:hover {
    background-color: rgba(157, 0, 0, 0.05);
}

.calendar-day.other-month {
    background-color: #f8f8f8;
    color: #ccc;
}

.calendar-day.today {
    background-color: var(--primary-color-light);
    border: 2px solid var(--primary-color);
}

.calendar-day-number {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.calendar-event {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    font-size: 10px;
    margin-bottom: 2px;
    cursor: pointer;
    transition: all var(--transition-fast);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
    word-wrap: break-word;
    line-height: 1.2;
}

.calendar-event:hover {
    background-color: var(--primary-color-dark);
    transform: scale(1.02);
}

.calendar-event.online {
    background-color: var(--info-color);
}

.calendar-event.offline {
    background-color: var(--success-color);
}

/* Multi-day event styles */
.calendar-event.multi-start {
    border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
    margin-right: 0;
}

.calendar-event.multi-middle {
    border-radius: 0;
    margin-left: 0;
    margin-right: 0;
}

.calendar-event.multi-end {
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    margin-left: 0;
}

.calendar-event.multi {
    border-radius: var(--border-radius-sm);
}

/* Text truncation for event titles to prevent height expansion */
.calendar-event {
    display: block;
    width: 100%;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 18px;
    line-height: 18px;
}

.calendar-event.long-title {
    font-size: 9px;
    height: 16px;
    line-height: 16px;
}

.calendar-container {
    max-width: 100%;
    overflow-x: auto;
}

.calendar-legend {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: var(--white);
    margin: 5% auto;
    padding: 0;
    border-radius: var(--border-radius-md);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease;
}

.modal-header {
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.modal-title {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: var(--white);
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color var(--transition-fast);
}

.modal-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: var(--spacing-lg);
}

.training-detail {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius-md);
    background-color: #f8f9fa;
}

.training-detail h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--primary-color);
    font-size: var(--font-size-md);
}

.training-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.training-info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.training-info-item i {
    color: var(--primary-color);
    width: 16px;
}

.participants-section {
    margin-top: var(--spacing-lg);
}

.participants-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius-sm);
}

.participant-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid #f0f0f0;
    transition: background-color var(--transition-fast);
}

.participant-item:last-child {
    border-bottom: none;
}

.participant-item:hover {
    background-color: rgba(157, 0, 0, 0.05);
}

.participant-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.participant-info {
    flex: 1;
}

.participant-name {
    font-weight: 600;
    margin-bottom: 2px;
    font-size: var(--font-size-sm);
}

.participant-dept {
    color: var(--text-dark);
    font-size: var(--font-size-xs);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Announcement Modal Specific Styles */
.announcement-modal .modal-content {
    max-width: 800px !important;
    width: 95% !important;
}

.announcement-modal-content {
    max-width: 800px;
    width: 95%;
}

.announcement-img-hover {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.announcement-img-hover:hover {
    transform: scale(1.02);
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(191, 0, 0, 0.2);
}

.announcement-modal .modal-body {
    padding: 20px;
}

.announcement-modal-image {
    text-align: center;
    margin-bottom: 20px;
}

.announcement-modal-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.announcement-modal-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    font-size: 0.9rem;
    color: #6c757d;
}

.announcement-modal-content-text {
    line-height: 1.6;
    margin-bottom: 20px;
    color: #333;
}

.announcement-modal-link {
    text-align: center;
    margin-top: 20px;
}

.announcement-modal-expiry {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
    margin-top: 15px;
    text-align: center;
}

/* Responsive adjustments for announcement modal */
@media (max-width: 768px) {
    .announcement-modal .modal-content {
        width: 98% !important;
        margin: 2% auto !important;
    }

    .announcement-modal-content {
        width: 98%;
        margin: 2% auto;
    }
    
    .announcement-modal-meta {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: var(--spacing-lg);
    }

    .hero-section h1 {
        font-size: 2rem;
    }

    .hero-section p {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
    }

    .btn-hero {
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .calendar-day {
        min-height: 80px;
    }

    .calendar-event {
        font-size: 8px;
        padding: 1px 3px;
        height: 14px;
        line-height: 14px;
    }

    .calendar-event.long-title {
        font-size: 7px;
        height: 12px;
        line-height: 12px;
    }

    .calendar-nav {
        gap: var(--spacing-sm);
    }

    .calendar-title {
        font-size: var(--font-size-md);
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .training-info-grid {
        grid-template-columns: 1fr;
    }
}

/* Urgent/Priority Styles */
.urgent-approval {
    border: 2px solid #ff6b35;
    background: linear-gradient(135deg, #fff5f2, #ffe8e1);
    animation: pulse-border 2s infinite;
}

.urgent-approval .icon {
    color: #ff6b35;
}

.urgent-approval .number {
    color: #ff6b35;
}

.urgent-indicator {
    background: #ff6b35;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 4px;
    animation: pulse-glow 1.5s infinite;
}

.urgent-card {
    border-left: 4px solid #ff6b35;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.15);
}

.urgent-header {
    background: linear-gradient(135deg, #ff6b35, #e55a2b);
    color: white;
    position: relative;
}

.urgent-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.urgent-link {
    background: linear-gradient(135deg, #ff6b35, #e55a2b);
    color: white !important;
    padding: 12px 20px;
    border-radius: 6px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.urgent-link:hover {
    background: linear-gradient(135deg, #e55a2b, #cc4f24);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
    text-decoration: none;
    color: white !important;
}

.action-button {
    background: var(--primary-color);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
}

.action-button:hover {
    background: var(--primary-color-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(157, 0, 0, 0.3);
    text-decoration: none;
    color: white;
}

.approve-btn {
    background: #28a745;
}

.approve-btn:hover {
    background: #218838;
}

/* Notification Modal Styles */
.notification-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    animation: fadeIn 0.3s ease;
}

.notification-content {
    background: linear-gradient(135deg, #fff5f2, #ffffff);
    margin: 10% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
    border: 2px solid #ff6b35;
    animation: slideInDown 0.4s ease;
}

.notification-header {
    background: linear-gradient(135deg, #ff6b35, #e55a2b);
    color: white;
    padding: 20px;
    border-radius: 10px 10px 0 0;
    text-align: center;
    position: relative;
}

.notification-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.notification-header .icon {
    font-size: 24px;
    animation: bounce 1s infinite;
}

.notification-body {
    padding: 25px;
    text-align: center;
}

.notification-count {
    font-size: 48px;
    font-weight: 700;
    color: #ff6b35;
    margin: 10px 0;
    text-shadow: 2px 2px 4px rgba(255, 107, 53, 0.1);
}

.notification-message {
    font-size: 16px;
    color: #333;
    margin-bottom: 20px;
    line-height: 1.5;
}

.notification-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

.notification-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary-notification {
    background: linear-gradient(135deg, #ff6b35, #e55a2b);
    color: white;
}

.btn-primary-notification:hover {
    background: linear-gradient(135deg, #e55a2b, #cc4f24);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
    text-decoration: none;
    color: white;
}

.btn-secondary-notification {
    background: #6c757d;
    color: white;
}

.btn-secondary-notification:hover {
    background: #5a6268;
    transform: translateY(-2px);
    text-decoration: none;
    color: white;
}

/* Animations */
@keyframes pulse-border {
    0% { border-color: #ff6b35; }
    50% { border-color: #ff8c5a; }
    100% { border-color: #ff6b35; }
}

@keyframes pulse-glow {
    0% { box-shadow: 0 0 5px rgba(255, 107, 53, 0.5); }
    50% { box-shadow: 0 0 15px rgba(255, 107, 53, 0.8); }
    100% { box-shadow: 0 0 5px rgba(255, 107, 53, 0.5); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="landing-container">
    <div class="hero-section">
        <h1>Selamat Datang, <?= htmlspecialchars($user_name) ?>!</h1>
        <p>Sebagai Direktur, Anda memiliki otoritas tertinggi dalam menyetujui program pelatihan strategis. Review dan berikan persetujuan final untuk training yang telah melalui proses approval sebelumnya.</p>
        <div class="hero-buttons">
            <a href="classroom.php" class="btn-hero btn-primary">
                <i class="fas fa-chalkboard-teacher"></i> Classroom Training
            </a>
            <a href="dashboard.php" class="btn-hero btn-secondary">
                <i class="fas fa-tachometer-alt"></i> Dashboard Direktur
            </a>
            <?php if ($approval_count > 0): ?>
            <a href="dashboard.php" class="btn-hero" style="background: linear-gradient(135deg, #ff6b35, #e55a2b); color: white; animation: pulse-glow 1.5s infinite;">
                <i class="fas fa-exclamation-triangle"></i> Review Training (<?= $approval_count ?>)
            </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <h3>Total Pengajuan Saya</h3>
            <div class="number"><?= $pending_count + $approved_count ?></div>
            <div class="label">Training yang telah saya ajukan</div>
        </div>
        <div class="stat-card <?= $approval_count > 0 ? 'urgent-approval' : '' ?>">
            <div class="icon">
                <i class="fas fa-user-check"></i>
            </div>
            <h3>Perlu Approval Direktur</h3>
            <div class="number"><?= $approval_count ?></div>
            <div class="label">Training menunggu keputusan final</div>
            <?php if ($approval_count > 0): ?>
                <div class="urgent-indicator">
                    <i class="fas fa-exclamation-triangle"></i>
                    Keputusan Final Diperlukan!
                </div>
            <?php endif; ?>
        </div>
        <div class="stat-card">
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3>Disetujui Saya</h3>
            <div class="number"><?= $approved_count ?></div>
            <div class="label">Training saya yang disetujui</div>
        </div>
    </div>
    <div class="dashboard-grid">
        <div class="main-content">
            <!-- Card untuk Training yang Perlu Disetujui -->
            <?php if ($approval_count > 0): ?>
            <div class="card urgent-card">
                <div class="card-header urgent-header">
                    <h2><i class="fas fa-exclamation-triangle icon"></i>Training Memerlukan Keputusan Final Direktur</h2>
                    <span class="urgent-badge"><?= $approval_count ?> Training</span>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_approvals)): ?>
                        <div class="empty-state">
                            <div class="icon"><i class="fas fa-clipboard-check"></i></div>
                            <div class="message">Tidak ada training yang perlu disetujui saat ini</div>
                        </div>
                    <?php else: ?>
                        <ul class="recent-trainings">
                            <?php foreach ($recent_approvals as $approval): ?>
                                <li>
                                    <div class="training-item">
                                        <div class="training-info">
                                            <div class="training-title"><?= htmlspecialchars($approval['training_topic']) ?></div>
                                            <div class="training-date">
                                                <i class="fas fa-user"></i> <?= htmlspecialchars($approval['requester_name']) ?>
                                                (<?= htmlspecialchars($approval['departemen']) ?>)
                                            </div>
                                            <div class="training-date">
                                                <?php if (!empty($approval['start_date']) && !empty($approval['end_date']) && $approval['start_date'] !== $approval['end_date']): ?>
                                                    <i class="fas fa-calendar-week"></i> <?= htmlspecialchars($approval['start_date']) ?> s/d <?= htmlspecialchars($approval['end_date']) ?>
                                                <?php elseif (!empty($approval['start_date'])): ?>
                                                    <i class="fas fa-calendar-check"></i> <?= htmlspecialchars($approval['start_date']) ?>
                                                <?php else: ?>
                                                    <i class="fas fa-calendar-alt"></i> Tanggal belum ditentukan
                                                <?php endif; ?>
                                                <?php if (!$approval['is_confirmed']): ?>
                                                    <span class="date-pending">Belum Dikonfirmasi</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div>
                                            <a href="detail_training.php?id=<?= $approval['id'] ?>" class="action-button approve-btn">
                                                <i class="fas fa-eye"></i> Review
                                            </a>
                                        </div>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                        <a href="dashboard.php" class="view-all urgent-link">
                            <i class="fas fa-clipboard-check"></i> Lihat Semua Training yang Perlu Disetujui
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>


            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-calendar-alt icon"></i> Kalender Training</h2>
                </div>
                <div class="card-body">
                    <div class="calendar-container">
                        <div class="calendar-header">
                            <div class="calendar-nav">
                                <button id="prevMonth" type="button">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <h3 class="calendar-title" id="calendarTitle">Loading...</h3>
                                <button id="nextMonth" type="button">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                            <button id="todayBtn" type="button" style="background: rgba(255, 255, 255, 0.2); border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                                Hari Ini
                            </button>
                        </div>
                        <div class="calendar-grid" id="calendarGrid">
                            <!-- Calendar will be rendered here -->
                        </div>
                        <div class="calendar-legend">
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: #4CAF50;"></div>
                                <span>Training Internal</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: #2196F3;"></div>
                                <span>Training Eksternal</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="sidebar">
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-bullhorn icon"></i> Pengumuman</h2>
                </div>
                <div class="card-body">
                    <?php if (empty($announcements)): ?>
                        <div class="empty-state">
                            <div class="icon"><i class="fas fa-bullhorn"></i></div>
                            <div class="message">Tidak ada pengumuman saat ini</div>
                        </div>
                    <?php else: ?>
                        <ul class="announcements">
                            <?php foreach ($announcements as $announcement): ?>
                                <li class="announcement-item">
                                    <div class="announcement-title">
                                        <?= htmlspecialchars($announcement['title']) ?>
                                        <?php if (!empty($announcement['expiry_date'])): ?>
                                            <span class="expiry-badge">
                                                <i class="fas fa-clock"></i> Berlaku s/d <?= date('d M Y', strtotime($announcement['expiry_date'])) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="announcement-meta">
                                        <span><i class="fas fa-user"></i> <?= htmlspecialchars($announcement['creator_name'] ?? 'Admin') ?></span>
                                        <span class="ms-2"><i class="fas fa-calendar-alt"></i> <?= date('d M Y', strtotime($announcement['created_at'])) ?></span>
                                    </div>
                                    
                                    <!-- Announcement Image -->
                                    <?php if (!empty($announcement['image_path'])): ?>
                                        <div class="announcement-image">
                                            <img src="../<?= htmlspecialchars($announcement['image_path']) ?>" 
                                                 alt="<?= htmlspecialchars($announcement['title']) ?>" 
                                                 class="img-fluid rounded announcement-img-hover"
                                                 style="max-width: 100%; height: auto; margin: 10px 0; cursor: pointer;"
                                                 data-announcement-id="<?= $announcement['id'] ?>"
                                                 data-title="<?= htmlspecialchars($announcement['title']) ?>"
                                                 data-content="<?= htmlspecialchars($announcement['content']) ?>"
                                                 data-creator="<?= htmlspecialchars($announcement['creator_name'] ?? 'Admin') ?>"
                                                 data-date="<?= date('d M Y', strtotime($announcement['created_at'])) ?>"
                                                 data-image="../<?= htmlspecialchars($announcement['image_path']) ?>"
                                                 <?php if (!empty($announcement['link_url'])): ?>
                                                 data-link-url="<?= htmlspecialchars($announcement['link_url']) ?>"
                                                 data-link-text="<?= htmlspecialchars($announcement['link_text'] ?? 'Buka Link') ?>"
                                                 <?php endif; ?>
                                                 <?php if (!empty($announcement['expiry_date'])): ?>
                                                 data-expiry="<?= date('d M Y', strtotime($announcement['expiry_date'])) ?>"
                                                 <?php endif; ?>>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="announcement-content">
                                        <?= nl2br(htmlspecialchars($announcement['content'])) ?>
                                    </div>
                                    
                                    <!-- Announcement Link -->
                                    <?php if (!empty($announcement['link_url'])): ?>
                                        <div class="announcement-link">
                                            <a href="<?= htmlspecialchars($announcement['link_url']) ?>" 
                                               target="_blank" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt"></i> 
                                                <?= htmlspecialchars($announcement['link_text'] ?? 'Buka Link') ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-user icon"></i> Profil Anda</h2>
                </div>
                <div class="card-body">
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <div>
                            <strong><i class="fas fa-user-circle"></i> Nama:</strong>
                            <div><?= htmlspecialchars($user_name) ?></div>
                        </div>
                        <div>
                            <strong><i class="fas fa-building"></i> Departemen:</strong>
                            <div><?= htmlspecialchars($user_dept) ?></div>
                        </div>
                        <div>
                            <strong><i class="fas fa-id-badge"></i> Jabatan:</strong>
                            <div><?= htmlspecialchars($user_jabatan) ?></div>
                        </div>
                        <a href="../config/userinfo.php" class="btn btn-sm btn-primary mt-3">
                            <i class="fas fa-edit"></i> Edit Profil
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk detail training -->
<div id="trainingModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title" id="modalTitle">Detail Training</h3>
            <button class="modal-close" id="modalClose">&times;</button>
        </div>
        <div class="modal-body" id="modalBody">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<!-- Modal untuk detail pengumuman -->
<div id="announcementModal" class="modal announcement-modal">
    <div class="modal-content announcement-modal-content">
        <div class="modal-header">
            <h3 class="modal-title" id="announcementModalTitle">Detail Pengumuman</h3>
            <button class="modal-close" id="announcementModalClose">&times;</button>
        </div>
        <div class="modal-body" id="announcementModalBody">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<!-- Modal Notifikasi Approval -->
<?php if ($show_notification): ?>
<div id="approvalNotificationModal" class="notification-modal">
    <div class="notification-content">
        <div class="notification-header">
            <h3>
                <i class="fas fa-exclamation-triangle icon"></i>
                Perhatian! Training Memerlukan Keputusan Final
            </h3>
        </div>
        <div class="notification-body">
            <div class="notification-count"><?= $approval_count ?></div>
            <div class="notification-message">
                Ada <strong><?= $approval_count ?></strong> pengajuan training yang memerlukan keputusan final Anda sebagai Direktur.
                <br><br>
                Training ini telah melalui semua tahap approval sebelumnya dan menunggu persetujuan tertinggi dari Direktur untuk dapat dilaksanakan.
            </div>
            <div class="notification-actions">
                <a href="dashboard.php" class="notification-btn btn-primary-notification">
                    <i class="fas fa-clipboard-check"></i>
                    Review Sekarang
                </a>
                <button onclick="closeNotificationModal()" class="notification-btn btn-secondary-notification">
                    <i class="fas fa-times"></i>
                    Tutup
                </button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include '../config/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize announcement image hover popup
    initializeAnnouncementPopup();

    // Show approval notification modal if there are pending approvals
    <?php if ($show_notification): ?>
    setTimeout(function() {
        const notificationModal = document.getElementById('approvalNotificationModal');
        if (notificationModal) {
            notificationModal.style.display = 'block';
        }
    }, 1000); // Show after 1 second to let page load completely
    <?php endif; ?>

    // Animation for stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 * index);
    });

    // Initialize Universal Calendar
    if (document.getElementById('calendarGrid')) {
        // Set user role for API calls
        window.userRole = <?= $_SESSION['role_id'] ?>;

        // Set API URLs for universal calendar
        window.calendarConfig = {
            eventsApiUrl: '../api/get_training_events_universal.php',
            detailApiUrl: '../api/get_training_detail_universal.php'
        };

        // Initialize calendar
        initializeUniversalCalendar();
    }

    // Functions
    function closeNotificationModal() {
        const notificationModal = document.getElementById('approvalNotificationModal');
        if (notificationModal) {
            notificationModal.style.display = 'none';
        }
    }

    // Make closeNotificationModal available globally
    window.closeNotificationModal = closeNotificationModal;

    // Close notification modal when clicking outside
    <?php if ($show_notification): ?>
    const notificationModal = document.getElementById('approvalNotificationModal');
    if (notificationModal) {
        notificationModal.addEventListener('click', function(e) {
            if (e.target === notificationModal) {
                closeNotificationModal();
            }
        });
    }
    <?php endif; ?>







    /**
     * Initialize announcement image popup functionality
     */
    function initializeAnnouncementPopup() {
        const announcementImages = document.querySelectorAll('.announcement-img-hover');
        const modal = document.getElementById('announcementModal');
        const modalTitle = document.getElementById('announcementModalTitle');
        const modalBody = document.getElementById('announcementModalBody');
        const modalClose = document.getElementById('announcementModalClose');

        if (!modal || !modalTitle || !modalBody || !modalClose) {
            console.warn('Announcement modal elements not found');
            return;
        }

        // Add click event to each announcement image
        announcementImages.forEach(img => {
            img.addEventListener('click', function() {
                showAnnouncementModal(this);
            });

            // Add hover effect
            img.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.02)';
            });

            img.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Close modal events
        modalClose.addEventListener('click', function() {
            closeAnnouncementModal();
        });

        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeAnnouncementModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modal.style.display === 'block') {
                closeAnnouncementModal();
            }
        });

        /**
         * Show announcement modal with data from image
         */
        function showAnnouncementModal(imgElement) {
            const title = imgElement.getAttribute('data-title');
            const content = imgElement.getAttribute('data-content');
            const creator = imgElement.getAttribute('data-creator');
            const date = imgElement.getAttribute('data-date');
            const imageSrc = imgElement.getAttribute('data-image');
            const linkUrl = imgElement.getAttribute('data-link-url');
            const linkText = imgElement.getAttribute('data-link-text');
            const expiry = imgElement.getAttribute('data-expiry');

            // Set modal title
            modalTitle.textContent = title;

            // Build modal content
            let modalContent = '';

            // Meta information
            modalContent += `
                <div class="announcement-modal-meta">
                    <span><i class="fas fa-user"></i> ${creator}</span>
                    <span><i class="fas fa-calendar-alt"></i> ${date}</span>
                </div>
            `;

            // Image
            if (imageSrc) {
                modalContent += `
                    <div class="announcement-modal-image">
                        <img src="${imageSrc}" alt="${title}" class="img-fluid">
                    </div>
                `;
            }

            // Content
            modalContent += `
                <div class="announcement-modal-content-text">
                    ${content.replace(/\n/g, '<br>')}
                </div>
            `;

            // Link
            if (linkUrl && linkText) {
                modalContent += `
                    <div class="announcement-modal-link">
                        <a href="${linkUrl}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt"></i> ${linkText}
                        </a>
                    </div>
                `;
            }

            // Expiry date
            if (expiry) {
                modalContent += `
                    <div class="announcement-modal-expiry">
                        <i class="fas fa-clock"></i> Berlaku sampai: ${expiry}
                    </div>
                `;
            }

            modalBody.innerHTML = modalContent;

            // Show modal
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';

            // Add animation
            setTimeout(() => {
                modal.querySelector('.modal-content').style.animation = 'slideIn 0.3s ease';
            }, 10);
        }

        /**
         * Close announcement modal
         */
        function closeAnnouncementModal() {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    }

});
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();