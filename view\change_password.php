<?php
session_start();
include '../config/config.php';
include '../config/security_helper.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Cek apakah password perlu diubah
$force_change = isset($_SESSION['require_password_change']) && $_SESSION['require_password_change'];
$password_expired = isset($_SESSION['password_expired']) && $_SESSION['password_expired'];
$default_password_used = isset($_SESSION['default_password_used']) && $_SESSION['default_password_used'];

// Ambil reason dari URL parameter
$reason = $_GET['reason'] ?? '';
if ($reason === 'default') {
    $default_password_used = true;
}

// Inisialisasi variabel
$error_message = '';
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validasi input
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error_message = "Semua field harus diisi.";
    } else if ($new_password !== $confirm_password) {
        $error_message = "Password baru dan konfirmasi password tidak cocok.";
    } else {
        // Validasi kekuatan password
        $password_errors = validate_password_strength($new_password);
        if (!empty($password_errors)) {
            $error_message = "Password tidak memenuhi persyaratan keamanan:<br>";
            $error_message .= "<ul>";
            foreach ($password_errors as $err) {
                $error_message .= "<li>{$err}</li>";
            }
            $error_message .= "</ul>";
        } else {
            // Verifikasi password saat ini
            $query = "SELECT password FROM users WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $_SESSION['user_id']);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $user = $result->fetch_assoc();

                if (password_verify($current_password, $user['password'])) {
                    // Hash password baru
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

                    // Update password di database
                    $update_query = "UPDATE users SET
                                    password = ?,
                                    password_changed_at = NOW()
                                    WHERE id = ?";
                    $update_stmt = $conn->prepare($update_query);
                    $update_stmt->bind_param("si", $hashed_password, $_SESSION['user_id']);

                    if ($update_stmt->execute()) {
                        $success_message = "Password berhasil diubah.";

                        // Hapus flag password expired jika ada
                        if (isset($_SESSION['password_expired'])) {
                            unset($_SESSION['password_expired']);
                        }

                        if (isset($_SESSION['require_password_change'])) {
                            unset($_SESSION['require_password_change']);
                        }

                        if (isset($_SESSION['default_password_used'])) {
                            unset($_SESSION['default_password_used']);
                        }

                        // Redirect setelah 2 detik jika tidak dipaksa mengubah password
                        if (!$force_change) {
                            header("Refresh: 2; URL=dashboard.php");
                        } else {
                            // Redirect ke halaman sesuai role_id
                            header("Refresh: 2; URL=login.php");
                        }
                    } else {
                        $error_message = "Gagal mengubah password. Silakan coba lagi.";
                    }

                    $update_stmt->close();
                } else {
                    $error_message = "Password saat ini tidak valid.";
                }
            } else {
                $error_message = "User tidak ditemukan.";
            }

            $stmt->close();
        }
    }
}

// Ambil pengaturan keamanan untuk ditampilkan
$security_settings = get_security_settings();
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    /* Base styles */
    body {
        font-family: 'Roboto', Arial, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 0;
    }

    /* Password container */
    .password-container {
        max-width: 600px;
        margin: 40px auto;
        padding: 35px;
        background-color: #fff;
        border-radius: 15px;
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        z-index: 1; /* Ensure it's above the navbar */
    }

    /* Header styles */
    .password-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .password-header h2 {
        color: #BF0000;
        margin-bottom: 12px;
        font-size: 28px;
        font-weight: 600;
    }

    .password-header p {
        color: #666;
        font-size: 15px;
        line-height: 1.5;
        margin: 0;
    }

    /* Form styles */
    .form-group {
        margin-bottom: 22px;
    }

    .form-group label {
        display: block;
        margin-bottom: 10px;
        font-weight: 600;
        color: #444;
        font-size: 15px;
    }

    .form-control {
        width: 100%;
        padding: 14px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }

    .form-control:focus {
        border-color: #BF0000;
        outline: none;
        box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
    }

    /* Button styles */
    .btn-submit {
        background-color: #BF0000;
        color: white;
        border: none;
        padding: 14px 20px;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        width: 100%;
        margin-top: 15px;
        font-size: 16px;
        transition: all 0.3s ease;
    }

    .btn-submit:hover {
        background-color: #a00000;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn-submit:active {
        transform: translateY(0);
    }

    /* Alert styles */
    .alert {
        padding: 15px;
        margin-bottom: 25px;
        border-radius: 8px;
        font-size: 15px;
        font-weight: 500;
        text-align: center;
    }

    .alert-danger {
        background-color: #fff2f2;
        border: 1px solid #ffcfcf;
        color: #c40000;
    }

    .alert-success {
        background-color: #f0fff4;
        border: 1px solid #c3e6cb;
        color: #0d6832;
    }

    /* Password requirements box */
    .password-requirements {
        background-color: #f8f9fa;
        padding: 18px 20px;
        border-radius: 8px;
        margin-bottom: 25px;
        border-left: 4px solid #BF0000;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    .password-requirements h4 {
        margin-top: 0;
        margin-bottom: 12px;
        color: #333;
        font-size: 17px;
        font-weight: 600;
    }

    .password-requirements ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .password-requirements li {
        margin-bottom: 8px;
        color: #555;
        font-size: 14px;
        line-height: 1.4;
    }

    /* Responsive styles */
    @media (max-width: 768px) {
        .password-container {
            margin: 70px 20px 30px; /* Add top margin to prevent navbar overlap */
            padding: 25px;
        }

        .password-header h2 {
            font-size: 24px;
        }

        .form-control {
            padding: 12px;
            font-size: 14px;
        }

        .btn-submit {
            padding: 12px;
            font-size: 15px;
        }
    }

    @media (max-width: 480px) {
        .password-container {
            margin: 60px 15px 20px; /* Add top margin to prevent navbar overlap */
            padding: 20px 15px;
        }

        .password-header h2 {
            font-size: 22px;
        }

        .password-header p {
            font-size: 14px;
        }

        .form-group label {
            font-size: 14px;
        }

        .form-control {
            padding: 10px;
            font-size: 14px;
        }

        .btn-submit {
            padding: 12px;
            font-size: 14px;
        }

        .alert {
            padding: 12px;
            font-size: 14px;
        }

        .password-requirements {
            padding: 15px;
        }

        .password-requirements h4 {
            font-size: 16px;
        }

        .password-requirements li {
            font-size: 13px;
        }
    }
</style>
<body>
    <?php include '../config/navbarb.php'; ?>
<div class="jarak"></div>
    <div class="password-container">
        <div class="password-header">
            <h2><?= $force_change ? 'Ubah Password Anda' : 'Ubah Password' ?></h2>
            <?php if ($default_password_used): ?>
                <p style="color: #d9534f; font-weight: 600;">
                    <i class="fas fa-exclamation-triangle"></i>
                    Anda menggunakan password default "asdf". Untuk keamanan akun, silakan ubah password Anda sekarang.
                </p>
            <?php elseif ($password_expired): ?>
                <p>Password Anda telah kedaluwarsa. Silakan ubah password Anda untuk melanjutkan.</p>
            <?php elseif ($force_change): ?>
                <p>Anda diharuskan mengubah password untuk alasan keamanan.</p>
            <?php else: ?>
                <p>Ubah password Anda secara berkala untuk keamanan akun.</p>
            <?php endif; ?>
        </div>

        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger"><?= $error_message ?></div>
        <?php endif; ?>

        <?php if (!empty($success_message)): ?>
            <div class="alert alert-success"><?= $success_message ?></div>
        <?php endif; ?>

        <div class="password-requirements">
            <h4>Persyaratan Password</h4>
            <ul>
                <li>Minimal <?= $security_settings['min_password_length'] ?> karakter</li>
                <?php if ($security_settings['require_uppercase']): ?>
                    <li>Minimal satu huruf besar (A-Z)</li>
                <?php endif; ?>
                <?php if ($security_settings['require_number']): ?>
                    <li>Minimal satu angka (0-9)</li>
                <?php endif; ?>
                <?php if ($security_settings['require_special']): ?>
                    <li>Minimal satu karakter khusus (!@#$%^&*)</li>
                <?php endif; ?>
            </ul>
        </div>

        <form method="POST" action="">
            <div class="form-group">
                <label for="current_password">Password Saat Ini</label>
                <input type="password" id="current_password" name="current_password" class="form-control" required>
            </div>

            <div class="form-group">
                <label for="new_password">Password Baru</label>
                <input type="password" id="new_password" name="new_password" class="form-control" required>
            </div>

            <div class="form-group">
                <label for="confirm_password">Konfirmasi Password Baru</label>
                <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
            </div>

            <button type="submit" class="btn-submit">Ubah Password</button>
        </form>
    </div>

    <?php include '../config/footer.php'; ?>
</body>
</html>
