<?php
// <PERSON><PERSON> output buffering untuk menangkap semua output
ob_start();

// Nonaktifkan error reporting yang bisa merusak JSON
error_reporting(0);
ini_set('display_errors', 0);

// Set header JSON di awal
header('Content-Type: application/json');

// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

try {
    // Validasi input
    if (empty($_GET['parent_column']) || empty($_GET['parent_value']) || empty($_GET['child_column'])) {
        throw new Exception('Missing required parameters');
    }

    $parentColumn = $_GET['parent_column'];
    $parentValue = $_GET['parent_value'];
    $childColumn = $_GET['child_column'];

    // Validasi nama kolom untuk mencegah SQL injection
    $allowed_columns = [
        'nik', 'nama', 'tgl_masuk', 'jk', 'level_karyawan', 'tgl_lahir',
        'agama', 'pendidikan_akhir', 'no_telp', 'dept', 'bagian', 'jabatan',
        'group', 'status', 'pt'
    ];

    if (!in_array($parentColumn, $allowed_columns) || !in_array($childColumn, $allowed_columns)) {
        throw new Exception('Invalid column name');
    }

    // Escape column name untuk keamanan tambahan
    $parentColumnEscaped = "`" . str_replace("`", "``", $parentColumn) . "`";
    $childColumnEscaped = "`" . str_replace("`", "``", $childColumn) . "`";

    // Periksa apakah parent_value adalah array (dari URL parameter)
    if (strpos($parentValue, ',') !== false) {
        // Jika parent_value berisi koma, pisahkan menjadi array
        $parentValues = explode(',', $parentValue);
    } else {
        // Jika tidak, gunakan sebagai nilai tunggal
        $parentValues = [$parentValue];
    }

    // Buat placeholder untuk IN clause
    $placeholders = implode(',', array_fill(0, count($parentValues), '?'));

    // Ambil nilai unik untuk kolom anak berdasarkan nilai kolom induk
    $query = "SELECT DISTINCT $childColumnEscaped FROM karyawan
              WHERE $parentColumnEscaped IN ($placeholders)
              AND $childColumnEscaped IS NOT NULL
              AND $childColumnEscaped != ''
              ORDER BY $childColumnEscaped";

    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Error preparing query: " . $conn->error);
    }

    // Buat array parameter untuk bind_param
    $types = str_repeat('s', count($parentValues));

    // Buat array referensi untuk bind_param
    $bindParams = array($types);
    foreach ($parentValues as $key => $value) {
        $bindParams[] = &$parentValues[$key];
    }

    // Gunakan call_user_func_array untuk bind_param dengan array dinamis
    call_user_func_array(array($stmt, 'bind_param'), $bindParams);

    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result) {
        throw new Exception("Error executing query: " . $stmt->error);
    }

    $values = [];
    while ($row = $result->fetch_array()) {
        $values[] = $row[0];
    }

    // Kembalikan response sukses
    echo json_encode([
        'success' => true,
        'values' => $values,
        'parent_column' => $parentColumn,
        'parent_value' => $parentValue,
        'child_column' => $childColumn
    ]);

} catch (Exception $e) {
    // Kembalikan response error
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    // Bersihkan output buffer dan kirim respons
    $output = ob_get_clean();

    // Periksa apakah output adalah JSON yang valid
    json_decode($output);
    if (json_last_error() !== JSON_ERROR_NONE) {
        // Jika bukan JSON valid, ganti dengan pesan error JSON
        echo json_encode([
            'success' => false,
            'message' => 'Server error: Invalid JSON response',
            'debug_info' => 'Output contains non-JSON content'
        ]);
    } else {
        // Jika JSON valid, kirim output asli
        echo $output;
    }

    // Pastikan tidak ada output lain setelah ini
    exit();
}
