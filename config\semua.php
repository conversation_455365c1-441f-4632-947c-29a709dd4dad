$query = "SELECT ts.id, ts.full_name, ts.status, ts.training_topic,
                 ts.nik, ts.departemen, ts.bagian, ts.jabatan,
                 ts.training_type, ts.training_skill_type, ts.start_date, ts.end_date, ts.is_confirmed,
                 ts.training_place, ts.training_cost,
                 ts.contact_person, ts.contact_number, ts.sharing_knowledge,
                 ts.additional_info, ts.approved_hrd,
                 ts.email, ts.phone, ts.comments_dept_head, ts.comments_hrd,
                 ts.comments_ga, ts.comments_fm, ts.comments_dir,
                 ts.training_time_start, ts.training_time_end, ts.provider_name,
                 ts.provider_address,
                 ts.provider_type, ts.trainer_name_internal, ts.trainer_nik_internal,
                 ts.trainer_department_internal,
                 ts.trainer_sub_department_internal, ts.trainer_position_internal,
                 ts.trainer_name_external, ts.additional_info_provider,
                 r.role_name AS current_approver,
                 GROUP_CONCAT(p.nama_participants SEPARATOR ', ') AS participant_names,
                 GROUP_CONCAT(p.nik_participants SEPARATOR ', ') AS participant_niks,
                 GROUP_CONCAT(p.jabatan_participants SEPARATOR ', ') AS participant_jabatans,
                 GROUP_CONCAT(p.bagian_participants SEPARATOR ', ') AS participant_bagians,
                 GROUP_CONCAT(p.departemen_participants SEPARATOR ', ') AS participant_departemens
          FROM training_submissions ts
          LEFT JOIN roles r ON ts.current_approver_role_id = r.id
          LEFT JOIN participants p ON ts.id = p.training_id
          WHERE ts.id = ?
          GROUP BY ts.id";
