<?php
/**
 * Edit Material Page for Admin
 * This page allows admins to edit materials in a training class
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if material ID is provided
$material_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($material_id <= 0) {
    header('Location: manage_classes.php');
    exit();
}

// Get material information
$material_query = "SELECT m.*, c.id as class_id, c.title as class_title, t.training_topic
                  FROM training_materials m
                  JOIN training_classes c ON m.class_id = c.id
                  JOIN training_submissions t ON c.training_id = t.id
                  WHERE m.id = ?";
$stmt = $conn->prepare($material_query);
$stmt->bind_param("i", $material_id);
$stmt->execute();
$result = $stmt->get_result();
$material = $result->fetch_assoc();
$stmt->close();

if (!$material) {
    header('Location: manage_classes.php');
    exit();
}

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_material'])) {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $type = $_POST['type'];
    $is_published = isset($_POST['is_published']) ? 1 : 0;
    $external_url = null;
    $file_path = $material['file_path']; // Keep existing file path by default

    // Validate input
    if (empty($title)) {
        $error_message = "Judul materi harus diisi.";
    } else {
        // Handle file upload or external URL
        if ($type == 'link') {
            $external_url = trim($_POST['external_url']);
            if (empty($external_url)) {
                $error_message = "URL harus diisi untuk materi tipe tautan.";
            }
        } else if (($type == 'document' || $type == 'video' || $type == 'other')) {
            // Check if a new file was uploaded
            if (isset($_FILES['file']) && $_FILES['file']['error'] != UPLOAD_ERR_NO_FILE && !empty($_FILES['file']['name'])) {
                // New file was uploaded - validate and process it
                if ($_FILES['file']['error'] != UPLOAD_ERR_OK) {
                    // Handle specific upload errors
                    switch ($_FILES['file']['error']) {
                        case UPLOAD_ERR_INI_SIZE:
                            $error_message = "File terlalu besar (melebihi upload_max_filesize di php.ini).";
                            break;
                        case UPLOAD_ERR_FORM_SIZE:
                            $error_message = "File terlalu besar (melebihi MAX_FILE_SIZE di form HTML).";
                            break;
                        case UPLOAD_ERR_PARTIAL:
                            $error_message = "File hanya terunggah sebagian.";
                            break;
                        case UPLOAD_ERR_NO_TMP_DIR:
                            $error_message = "Folder sementara tidak ditemukan.";
                            break;
                        case UPLOAD_ERR_CANT_WRITE:
                            $error_message = "Gagal menulis file ke disk.";
                            break;
                        case UPLOAD_ERR_EXTENSION:
                            $error_message = "Upload dihentikan oleh ekstensi PHP.";
                            break;
                    default:
                        $error_message = "Terjadi kesalahan saat mengunggah file (kode: " . $_FILES['file']['error'] . ").";
                    }
                } else {
                    // File upload is OK, proceed with validation and upload
                    $upload_dir = '../uploads/materials/';

                // Create directory if it doesn't exist
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                $file_name = $_FILES['file']['name'];
                $file_tmp = $_FILES['file']['tmp_name'];
                $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

                // Validate file type for document
                if ($type == 'document') {
                    $allowed_exts = array('pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt');
                    if (!in_array($file_ext, $allowed_exts)) {
                        $error_message = "Format file tidak didukung untuk tipe dokumen. Format yang didukung: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT.";
                    }
                }

                // Validate file type for video
                if ($type == 'video') {
                    $allowed_exts = array('mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv');
                    if (!in_array($file_ext, $allowed_exts)) {
                        $error_message = "Format file tidak didukung untuk tipe video. Format yang didukung: MP4, AVI, MOV, WMV, FLV, MKV.";
                    }
                }

                if (empty($error_message)) {
                    // Generate unique file name
                    $new_file_name = uniqid() . '_' . $file_name;
                    $file_path = 'uploads/materials/' . $new_file_name;

                    // Move uploaded file
                    if (move_uploaded_file($file_tmp, '../' . $file_path)) {
                        // File uploaded successfully

                        // Delete old file if exists
                        if (!empty($material['file_path']) && file_exists('../' . $material['file_path'])) {
                            unlink('../' . $material['file_path']);
                        }
                    } else {
                        $error_message = "Gagal mengunggah file.";
                    }
                }
                }
            }
            // If no new file uploaded, keep existing file path (already set above)
        }

        if (empty($error_message)) {
            // Update material
            $update_query = "UPDATE training_materials SET
                            title = ?, description = ?, type = ?, file_path = ?,
                            external_url = ?, is_published = ?
                            WHERE id = ?";

            $stmt = $conn->prepare($update_query);
            $stmt->bind_param(
                "sssssii",
                $title, $description, $type, $file_path, $external_url, $is_published, $material_id
            );

            if ($stmt->execute()) {
                $success_message = "Materi berhasil diperbarui.";

                // Refresh material data
                $stmt->close();
                $stmt = $conn->prepare($material_query);
                $stmt->bind_param("i", $material_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $material = $result->fetch_assoc();
            } else {
                $error_message = "Gagal memperbarui materi: " . $conn->error;
            }
            $stmt->close();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .material-type-options {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }

    .material-type-option {
        flex: 1;
        text-align: center;
        padding: 15px;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .material-type-option:hover {
        background-color: #f8f9fa;
    }

    .material-type-option.active {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }

    .material-type-option i {
        font-size: 2rem;
        margin-bottom: 10px;
        color: #6c757d;
    }

    .material-type-option.active i {
        color: #212529;
    }

    .material-type-option h5 {
        margin-bottom: 5px;
    }

    .material-type-option p {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .type-specific-fields {
        margin-top: 20px;
    }

    .current-file {
        margin-top: 10px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #dee2e6;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="welcome-section">
                <h1><i class="fas fa-edit"></i> Edit Materi</h1>
                <a href="manage_class.php?id=<?= $material['class_id'] ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Kelas
                </a>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Informasi Kelas</h5>
                </div>
                <div class="card-body">
                    <h5><?= htmlspecialchars($material['class_title']) ?></h5>
                    <p class="text-muted"><?= htmlspecialchars($material['training_topic']) ?></p>
                </div>
            </div>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Edit Materi</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="" enctype="multipart/form-data">
                        <!-- Max file size: 50MB -->
                        <input type="hidden" name="MAX_FILE_SIZE" value="52428800" />
                        <div class="mb-3">
                            <label for="title" class="form-label">Judul Materi</label>
                            <input type="text" class="form-control" id="title" name="title" value="<?= htmlspecialchars($material['title']) ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi Materi</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?= htmlspecialchars($material['description']) ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Jenis Materi</label>
                            <div class="material-type-options">
                                <div class="material-type-option <?= $material['type'] == 'document' ? 'active' : '' ?>" data-type="document">
                                    <i class="fas fa-file-alt"></i>
                                    <h5>Dokumen</h5>
                                    <p>Unggah file dokumen (PDF, Word, Excel, dll)</p>
                                </div>
                                <div class="material-type-option <?= $material['type'] == 'video' ? 'active' : '' ?>" data-type="video">
                                    <i class="fas fa-video"></i>
                                    <h5>Video</h5>
                                    <p>Unggah file video (MP4, AVI, dll)</p>
                                </div>
                                <div class="material-type-option <?= $material['type'] == 'link' ? 'active' : '' ?>" data-type="link">
                                    <i class="fas fa-link"></i>
                                    <h5>Tautan</h5>
                                    <p>Tambahkan tautan ke sumber eksternal</p>
                                </div>
                                <div class="material-type-option <?= $material['type'] == 'other' ? 'active' : '' ?>" data-type="other">
                                    <i class="fas fa-file"></i>
                                    <h5>Lainnya</h5>
                                    <p>Jenis materi lainnya</p>
                                </div>
                            </div>
                            <input type="hidden" id="type" name="type" value="<?= $material['type'] ?>" required>
                        </div>

                        <div class="type-specific-fields">
                            <div id="document-fields" class="mb-3" <?= $material['type'] != 'document' ? 'style="display: none;"' : '' ?>>
                                <label for="document_file" class="form-label">Unggah Dokumen</label>
                                <input type="file" class="form-control" id="document_file" name="file" accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt">
                                <div class="form-text">Format yang didukung: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT</div>
                                <?php if (!empty($material['file_path']) && $material['type'] == 'document'): ?>
                                    <div class="current-file">
                                        <strong>File saat ini:</strong>
                                        <a href="../<?= htmlspecialchars($material['file_path']) ?>" target="_blank">
                                            <?= basename($material['file_path']) ?>
                                        </a>
                                        <div class="form-text">Unggah file baru untuk mengganti file yang ada.</div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div id="video-fields" class="mb-3" <?= $material['type'] != 'video' ? 'style="display: none;"' : '' ?>>
                                <label for="video_file" class="form-label">Unggah Video</label>
                                <input type="file" class="form-control" id="video_file" name="file" accept=".mp4,.avi,.mov,.wmv,.flv,.mkv">
                                <div class="form-text">Format yang didukung: MP4, AVI, MOV, WMV, FLV, MKV</div>
                                <?php if (!empty($material['file_path']) && $material['type'] == 'video'): ?>
                                    <div class="current-file">
                                        <strong>File saat ini:</strong>
                                        <a href="../<?= htmlspecialchars($material['file_path']) ?>" target="_blank">
                                            <?= basename($material['file_path']) ?>
                                        </a>
                                        <div class="form-text">Unggah file baru untuk mengganti file yang ada.</div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div id="link-fields" class="mb-3" <?= $material['type'] != 'link' ? 'style="display: none;"' : '' ?>>
                                <label for="external_url" class="form-label">URL</label>
                                <input type="url" class="form-control" id="external_url" name="external_url" value="<?= htmlspecialchars($material['external_url'] ?? '') ?>">
                                <div class="form-text">Masukkan URL lengkap termasuk http:// atau https://</div>
                            </div>

                            <div id="other-fields" class="mb-3" <?= $material['type'] != 'other' ? 'style="display: none;"' : '' ?>>
                                <label for="other_file" class="form-label">Unggah File</label>
                                <input type="file" class="form-control" id="other_file" name="file">
                                <div class="form-text">Unggah file dalam format apapun. Pastikan file tidak terlalu besar (maks. 50MB).</div>
                                <?php if (!empty($material['file_path']) && $material['type'] == 'other'): ?>
                                    <div class="current-file">
                                        <strong>File saat ini:</strong>
                                        <a href="../<?= htmlspecialchars($material['file_path']) ?>" target="_blank">
                                            <?= basename($material['file_path']) ?>
                                        </a>
                                        <div class="form-text">Unggah file baru untuk mengganti file yang ada.</div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Debug information for file upload -->
                        <div class="mb-3 bg-light p-3 rounded">
                            <h6>Status Unggahan File:</h6>
                            <div id="file-upload-status">Belum ada file yang dipilih</div>

                            <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
                            <div class="mt-2 text-small">
                                <strong>Debug Info:</strong><br>
                                Type: <?= htmlspecialchars($type ?? 'N/A') ?><br>
                                File Error: <?= isset($_FILES['file']) ? $_FILES['file']['error'] : 'No file' ?><br>
                                File Name: <?= isset($_FILES['file']['name']) ? htmlspecialchars($_FILES['file']['name']) : 'N/A' ?><br>
                                <?php if (isset($_FILES['file']['name']) && !empty($_FILES['file']['name'])): ?>
                                File Extension: <?= strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION)) ?><br>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_published" name="is_published" <?= $material['is_published'] ? 'checked' : '' ?>>
                            <label class="form-check-label" for="is_published">Publikasikan Materi</label>
                            <div class="form-text">Jika dicentang, materi akan terlihat oleh peserta</div>
                        </div>

                        <button type="submit" name="update_material" class="btn btn-primary">
                            <i class="fas fa-save"></i> Perbarui Materi
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);

        // Material type selection
        const typeOptions = document.querySelectorAll('.material-type-option');
        const typeInput = document.getElementById('type');
        const documentFields = document.getElementById('document-fields');
        const videoFields = document.getElementById('video-fields');
        const linkFields = document.getElementById('link-fields');
        const otherFields = document.getElementById('other-fields');

        // Show/hide fields based on selected type
        function updateTypeFields() {
            const selectedType = typeInput.value;

            // Show/hide fields
            documentFields.style.display = selectedType === 'document' ? 'block' : 'none';
            videoFields.style.display = selectedType === 'video' ? 'block' : 'none';
            linkFields.style.display = selectedType === 'link' ? 'block' : 'none';
            otherFields.style.display = selectedType === 'other' ? 'block' : 'none';

            // Handle required attributes for file inputs
            const docFileInput = document.getElementById('document_file');
            const videoFileInput = document.getElementById('video_file');
            const otherFileInput = document.getElementById('other_file');
            const urlInput = document.getElementById('external_url');

            // Reset required attributes - for edit form, don't make them required
            // since files might already be uploaded
            if (docFileInput) docFileInput.required = false;
            if (videoFileInput) videoFileInput.required = false;
            if (otherFileInput) otherFileInput.required = false;
            if (urlInput) urlInput.required = (selectedType === 'link');
        }

        updateTypeFields();

        typeOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove active class from all options
                typeOptions.forEach(opt => opt.classList.remove('active'));

                // Add active class to clicked option
                this.classList.add('active');

                // Update hidden input value
                typeInput.value = this.dataset.type;

                // Update visible fields
                updateTypeFields();
            });
        });

        // File upload status tracking
        const fileInputs = document.querySelectorAll('input[type="file"]');
        const statusDiv = document.getElementById('file-upload-status');

        fileInputs.forEach(input => {
            input.addEventListener('change', function() {
                if (this.files && this.files.length > 0) {
                    const file = this.files[0];
                    const fileSize = (file.size / 1024 / 1024).toFixed(2); // Convert to MB
                    statusDiv.innerHTML = `
                        <strong>File dipilih:</strong> ${file.name}<br>
                        <strong>Ukuran:</strong> ${fileSize} MB<br>
                        <strong>Tipe:</strong> ${file.type || 'Tidak diketahui'}
                    `;
                    statusDiv.className = 'text-success';
                } else {
                    statusDiv.innerHTML = 'Belum ada file yang dipilih';
                    statusDiv.className = '';
                }
            });
        });
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
