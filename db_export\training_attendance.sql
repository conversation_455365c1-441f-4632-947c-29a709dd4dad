-- MySQL dump 10.13  Distrib 8.0.30, for Win64 (x86_64)
--
-- Host: localhost    Database: db_training
-- ------------------------------------------------------
-- Server version	8.0.30

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `training_attendance`
--

DROP TABLE IF EXISTS `training_attendance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `training_attendance` (
  `id` int NOT NULL AUTO_INCREMENT,
  `training_id` int NOT NULL,
  `karyawan_id` int NOT NULL,
  `nik` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `card_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `nama` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `check_in` datetime DEFAULT NULL,
  `check_out` datetime DEFAULT NULL,
  `status` enum('hadir','tidak hadir','terlambat','izin') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'tidak hadir',
  `keterangan` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_training_id` (`training_id`),
  KEY `idx_karyawan_id` (`karyawan_id`),
  KEY `idx_nik` (`nik`),
  KEY `idx_card_number` (`card_number`),
  KEY `idx_check_in` (`check_in`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_attendance_karyawan` FOREIGN KEY (`karyawan_id`) REFERENCES `karyawan` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_attendance_training` FOREIGN KEY (`training_id`) REFERENCES `training_submissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `training_attendance`
--

LOCK TABLES `training_attendance` WRITE;
/*!40000 ALTER TABLE `training_attendance` DISABLE KEYS */;
/*!40000 ALTER TABLE `training_attendance` ENABLE KEYS */;
UNLOCK TABLES;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `training_attendance_after_update` AFTER UPDATE ON `training_attendance` FOR EACH ROW BEGIN
                DECLARE user_id INT;
                SET user_id = IFNULL((SELECT id FROM users WHERE id = @current_user_id), 0);
                
                SET @old_data = JSON_OBJECT(
                    'id', OLD.id,
                    'training_id', OLD.training_id,
                    'karyawan_id', OLD.karyawan_id,
                    'nik', OLD.nik,
                    'card_number', OLD.card_number,
                    'nama', OLD.nama,
                    'check_in', OLD.check_in,
                    'check_out', OLD.check_out,
                    'status', OLD.status,
                    'keterangan', OLD.keterangan
                );
                
                SET @new_data = JSON_OBJECT(
                    'id', NEW.id,
                    'training_id', NEW.training_id,
                    'karyawan_id', NEW.karyawan_id,
                    'nik', NEW.nik,
                    'card_number', NEW.card_number,
                    'nama', NEW.nama,
                    'check_in', NEW.check_in,
                    'check_out', NEW.check_out,
                    'status', NEW.status,
                    'keterangan', NEW.keterangan
                );
                
                INSERT INTO training_attendance_logs (
                    attendance_id, training_id, karyawan_id, nik, card_number,
                    action_type, action_time, action_by, ip_address,
                    old_status, new_status, old_data, new_data
                )
                VALUES (
                    NEW.id, NEW.training_id, NEW.karyawan_id, NEW.nik, NEW.card_number,
                    IF(NEW.check_in != OLD.check_in AND OLD.check_in IS NULL, 'check_in',
                       IF(NEW.check_out != OLD.check_out AND OLD.check_out IS NULL, 'check_out', 'manual_update')),
                    NOW(), user_id, SUBSTRING_INDEX(USER(), '@', -1),
                    OLD.status, NEW.status, @old_data, @new_data
                );
            END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-23 22:13:53
