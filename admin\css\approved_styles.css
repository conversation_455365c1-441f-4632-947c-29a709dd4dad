.container-form {
    max-width: 1400px;
    margin: 20px auto;
    padding: 0 20px;
}

.training-section {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    padding: 20px;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.5em;
}

.badge {
    background: #c40000;
    color: white;
    padding: 3px 10px;
    border-radius: 15px;
    margin-left: 10px;
    font-size: 0.9em;
}

.training-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.training-table th {
    background-color: #c40000;
    color: white;
    padding: 12px;
    text-align: left;
    font-weight: 500;
}

.training-table td {
    padding: 12px;
    border-bottom: 1px solid #eee;
}

.training-table tr:hover td {
    background-color: #f8f9fa;
}

.search-container {
    margin-bottom: 30px;
}

.search-form {
    display: flex;
    gap: 10px;
    max-width: 600px;
    margin: 0 auto;
}

.search-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1em;
}

.search-button, .reset-button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
}

.search-button {
    background: #c40000;
    color: white;
}

.reset-button {
    background: #6c757d;
    color: white;
    text-decoration: none;
}

.search-button:hover {
    background: #a30000;
}

.reset-button:hover {
    background: #5a6268;
}

.btn-approve, .btn-delete, .btn-detail {
    padding: 8px 16px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
}

.btn-approve {
    background: #28a745;
    color: white;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-detail {
    background: #17a2b8;
    color: white;
}

.btn-approve:hover { background: #218838; }
.btn-delete:hover { background: #c82333; }
.btn-detail:hover { background: #138496; }

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal-content {
    background: white;
    width: 90%;
    max-width: 500px;
    margin: 100px auto;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.error-message, .success-message {
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    text-align: center;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.success-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

@media (max-width: 768px) {
    .training-table {
        display: block;
        overflow-x: auto;
    }
    
    .search-form {
        flex-direction: column;
    }
    
    .btn-approve, .btn-delete, .btn-detail {
        padding: 6px 12px;
        font-size: 0.9em;
    }
}