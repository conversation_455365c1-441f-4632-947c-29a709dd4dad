/**
 * CSS for Multi-day Event Support in Calendar
 */

/* Base calendar event styles */
.calendar-event {
    padding: 2px 6px;
    margin: 1px 0;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    font-weight: 500;
    /* Text wrapping for better layout */
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;
    line-height: 1.2;
    max-height: 3.6em; /* Limit to ~3 lines */
    /* Remove default background colors - will be set dynamically */
}

.calendar-event:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    z-index: 10;
}

/* Single day events - colors set dynamically via JavaScript */
.calendar-event.single {
    /* Border and background will be set dynamically */
}

/* Multi-day event styles */
.calendar-event.multi-start {
    border-radius: 3px 0 0 3px;
    border-right: none;
    position: relative;
}

.calendar-event.multi-middle {
    border-radius: 0;
    border-left: none;
    border-right: none;
    position: relative;
}

.calendar-event.multi-end {
    border-radius: 0 3px 3px 0;
    border-left: none;
    position: relative;
}

/* Multi-day events - colors set dynamically via JavaScript */
.calendar-event.multi-start,
.calendar-event.multi-middle,
.calendar-event.multi-end {
    /* Background gradients will be set dynamically */
    font-weight: 500;
}

/* Hidden events */
.calendar-event.event-hidden {
    opacity: 0.6;
    border-style: dashed !important;
}

.calendar-event.event-hidden::after {
    content: "🔒";
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 8px;
}

/* Unconfirmed events */
.calendar-event[data-confirmed="false"] {
    border-style: dotted !important;
    opacity: 0.8;
}

.calendar-event[data-confirmed="false"]::before {
    content: "⚠️";
    position: absolute;
    left: 2px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 8px;
}

/* Multi-day event connectors */
.calendar-event.multi-start::after {
    content: "";
    position: absolute;
    right: -1px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: inherit;
    z-index: 1;
}

.calendar-event.multi-end::before {
    content: "";
    position: absolute;
    left: -1px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: inherit;
    z-index: 1;
}

/* Hover effects for multi-day events */
.calendar-event.multi-start:hover,
.calendar-event.multi-middle:hover,
.calendar-event.multi-end:hover {
    z-index: 20;
    box-shadow: 0 3px 8px rgba(0,0,0,0.3);
}

/* Icons for multi-day events */
.calendar-event.multi-start {
    padding-left: 16px;
}

.calendar-event.multi-end {
    padding-right: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .calendar-event {
        font-size: 10px;
        padding: 1px 4px;
    }

    .calendar-event.multi-start,
    .calendar-event.multi-end {
        padding-left: 12px;
        padding-right: 12px;
    }
}

/* Calendar table layout improvements */
.calendar-table {
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
}

.calendar-table th,
.calendar-table td {
    width: 14.28%; /* 100% / 7 days */
    vertical-align: top;
    padding: 8px 4px;
    border: 1px solid #dee2e6;
    position: relative;
    overflow: hidden;
}

.calendar-day {
    min-height: 120px;
    max-height: 150px;
    overflow-y: auto;
    overflow-x: hidden;
}

.calendar-day-number {
    font-weight: bold;
    margin-bottom: 4px;
    position: sticky;
    top: 0;
    background: inherit;
    z-index: 5;
}

/* Prevent table from expanding */
.calendar-container {
    overflow-x: auto;
    max-width: 100%;
}

/* Animation for event appearance */
@keyframes eventAppear {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.calendar-event {
    animation: eventAppear 0.3s ease-out;
}

/* Special styling for weekend multi-day events */
.calendar-day.weekend .calendar-event.multi-start,
.calendar-day.weekend .calendar-event.multi-middle,
.calendar-day.weekend .calendar-event.multi-end {
    filter: brightness(0.9);
}

/* Tooltip enhancements */
.calendar-event[title] {
    position: relative;
}

/* Print styles */
@media print {
    .calendar-event {
        background: white !important;
        color: black !important;
        border: 1px solid black !important;
    }

    .calendar-event.event-hidden {
        opacity: 0.5;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .calendar-event {
        border-width: 2px;
        font-weight: bold;
    }

    .calendar-event.multi-start,
    .calendar-event.multi-middle,
    .calendar-event.multi-end {
        background: black !important;
        color: white !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .calendar-event.single.online {
        background-color: #1a237e;
        color: #bbdefb;
    }

    .calendar-event.single.offline {
        background-color: #1b5e20;
        color: #c8e6c9;
    }
}

/* Focus styles for accessibility */
.calendar-event:focus {
    outline: 2px solid #ff9800;
    outline-offset: 1px;
}

/* Loading state */
.calendar-event.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
