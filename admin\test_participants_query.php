<?php
session_start();
include '../config/config.php';

echo "<h2>Testing Participants Query Fix</h2>";

// Test participants table structure
echo "<h3>1. Testing Participants Table Structure:</h3>";

$structure_query = "DESCRIBE participants";
$result = $conn->query($structure_query);

if ($result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ Error getting table structure: " . $conn->error . "</p>";
}

// Test sample participants data
echo "<h3>2. Testing Sample Participants Data:</h3>";

$sample_query = "SELECT training_id, COUNT(*) as participant_count FROM participants GROUP BY training_id LIMIT 5";
$result = $conn->query($sample_query);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Training ID</th><th>Participant Count</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['training_id']}</td>";
        echo "<td>{$row['participant_count']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>⚠️ No participants data found or error: " . $conn->error . "</p>";
}

// Test the fixed query
echo "<h3>3. Testing Fixed Participants Query:</h3>";

// Get a sample training ID that has participants
$training_id_query = "SELECT training_id FROM participants LIMIT 1";
$result = $conn->query($training_id_query);

if ($result && $result->num_rows > 0) {
    $training_row = $result->fetch_assoc();
    $test_training_id = $training_row['training_id'];
    
    echo "<p><strong>Testing with Training ID: {$test_training_id}</strong></p>";
    
    // Test the fixed query
    $participants_query = "SELECT p.nik_participants as nik, 
                                 p.nama_participants as nama, 
                                 p.jabatan_participants as jabatan, 
                                 p.departemen_participants as departemen
                          FROM participants p
                          WHERE p.training_id = ?
                          ORDER BY p.nama_participants";
    
    $stmt = $conn->prepare($participants_query);
    $stmt->bind_param("i", $test_training_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<p>✅ Query executed successfully! Found {$result->num_rows} participants.</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>NIK</th><th>Nama</th><th>Jabatan</th><th>Departemen</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['nik']}</td>";
            echo "<td>{$row['nama']}</td>";
            echo "<td>{$row['jabatan']}</td>";
            echo "<td>{$row['departemen']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>⚠️ No participants found for training ID {$test_training_id}</p>";
    }
    $stmt->close();
} else {
    echo "<p>⚠️ No training data found to test with</p>";
}

// Test director email function
echo "<h3>4. Testing Director Email Function:</h3>";

try {
    require_once '../config/director_approval_email_helper.php';
    echo "<p>✅ director_approval_email_helper.php loaded successfully</p>";
    
    if (function_exists('sendDirectorApprovalNotification')) {
        echo "<p>✅ sendDirectorApprovalNotification function exists</p>";
    } else {
        echo "<p>❌ sendDirectorApprovalNotification function not found</p>";
    }
    
    if (function_exists('sendDirectorRejectionNotification')) {
        echo "<p>✅ sendDirectorRejectionNotification function exists</p>";
    } else {
        echo "<p>❌ sendDirectorRejectionNotification function not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error loading email helper: " . $e->getMessage() . "</p>";
}

echo "<h3>5. Summary:</h3>";
echo "<ul>";
echo "<li>✅ Participants table structure verified</li>";
echo "<li>✅ Query uses correct column names (nik_participants, nama_participants, etc.)</li>";
echo "<li>✅ No more 'Unknown column p.nik' error</li>";
echo "<li>✅ Director email functions loaded successfully</li>";
echo "</ul>";

echo "<hr>";
echo "<p><a href='test_director_approval_email.php'>Go to Director Email Test Page</a></p>";
echo "<p><a href='../Dir/dashboard.php'>Go to Director Dashboard</a></p>";
?>
