<?php
// Cron Job untuk Auto-Update Training Status
//
// Script ini dijalankan secara otomatis oleh cron job untuk mengupdate
// status training yang sudah melewati tanggal.
//
// Setup Cron Job:
// Jalankan setiap hari jam 00:01
// 1 0 * * * /usr/bin/php /path/to/training/cron/auto_update_training.php
//
// Atau setiap 6 jam
// 0 */6 * * * /usr/bin/php /path/to/training/cron/auto_update_training.php

// Set timezone
date_default_timezone_set('Asia/Jakarta');

// Include database config dan helper
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/config/auto_update_helper.php';

// Logging function untuk cron
function cronLog($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    
    // Log ke file
    $log_file = dirname(__DIR__) . '/logs/auto_update_cron.log';
    $log_dir = dirname($log_file);
    
    // Buat direktori log jika belum ada
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
    
    // Output ke console juga (untuk debugging)
    echo $log_message;
}

// Function untuk cleanup log lama
function cleanupOldLogs($days = 30) {
    try {
        $log_file = dirname(__DIR__) . '/logs/auto_update_cron.log';
        
        if (file_exists($log_file)) {
            $file_time = filemtime($log_file);
            $cutoff_time = time() - ($days * 24 * 60 * 60);
            
            if ($file_time < $cutoff_time) {
                // Backup log lama
                $backup_file = dirname($log_file) . '/auto_update_cron_' . date('Y-m-d', $file_time) . '.log';
                rename($log_file, $backup_file);
                cronLog("Old log backed up to: " . basename($backup_file));
            }
        }
    } catch (Exception $e) {
        cronLog("Error cleaning up logs: " . $e->getMessage(), 'ERROR');
    }
}

// Function untuk send notification jika ada update
function sendUpdateNotification($results) {
    if ($results['total_updated'] > 0) {
        $message = "Auto-Update Training Status Report:\n";
        $message .= "Date: " . date('Y-m-d H:i:s') . "\n";
        $message .= "Approved → Completed: {$results['approved_to_completed']}\n";
        $message .= "Pending → Canceled: {$results['pending_to_canceled']}\n";
        $message .= "Total Updated: {$results['total_updated']}\n\n";
        $message .= "Details:\n" . implode("\n", $results['details']);
        
        // Log notification (bisa dikembangkan untuk email)
        cronLog("Notification: " . str_replace("\n", " | ", $message));
        
        // TODO: Implement email notification jika diperlukan
        // sendEmailNotification($message);
    }
}

// Function untuk health check database
function healthCheckDatabase($conn) {
    try {
        // Test connection
        if (!$conn->ping()) {
            throw new Exception("Database connection lost");
        }
        
        // Test query
        $result = $conn->query("SELECT 1");
        if (!$result) {
            throw new Exception("Database query test failed");
        }
        
        cronLog("Database health check: OK");
        return true;
        
    } catch (Exception $e) {
        cronLog("Database health check failed: " . $e->getMessage(), 'ERROR');
        return false;
    }
}

// Main execution
try {
    cronLog("=== Auto-Update Training Status Cron Job Started ===");
    
    // Health check
    if (!healthCheckDatabase($conn)) {
        cronLog("Aborting due to database health check failure", 'ERROR');
        exit(1);
    }
    
    // Cleanup old logs
    cleanupOldLogs(30);
    
    // Check if already run today (untuk mencegah duplicate run)
    if (isAutoUpdateRunToday($conn)) {
        cronLog("Auto-update already run today, skipping...");
        exit(0);
    }
    
    // Run auto-update
    cronLog("Starting auto-update process...");
    $results = forceAutoUpdateTrainingStatus($conn);
    
    if ($results['success']) {
        cronLog("Auto-update completed successfully");
        cronLog("Results: Completed={$results['approved_to_completed']}, Canceled={$results['pending_to_canceled']}, Total={$results['total_updated']}");
        
        // Send notification if there were updates
        sendUpdateNotification($results);
        
        // Log details
        foreach ($results['details'] as $detail) {
            cronLog("Detail: " . $detail);
        }
        
    } else {
        cronLog("Auto-update failed: " . $results['message'], 'ERROR');
        
        // Log errors
        foreach ($results['errors'] as $error) {
            cronLog("Error: " . $error, 'ERROR');
        }
        
        exit(1);
    }
    
} catch (Exception $e) {
    cronLog("Fatal error in cron job: " . $e->getMessage(), 'ERROR');
    exit(1);
    
} finally {
    // Close database connection
    if (isset($conn) && $conn) {
        $conn->close();
    }
    
    cronLog("=== Auto-Update Training Status Cron Job Finished ===");
}

exit(0);
?>
