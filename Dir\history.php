<?php
include '../config/config.php';
include 'security.php';

$user_id = $_SESSION['user_id'];

// Get training history
$query = "SELECT 
    th.*,
    DATE_FORMAT(th.completed_at, '%d %M %Y') as completed_date
FROM training_history th
WHERE th.user_id = ?
ORDER BY th.completed_at DESC";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
.history-container {
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 20px auto;
    max-width: 1200px;
}

.status-completed {
    color: #2e7d32;
    font-weight: bold;
}

.status-canceled {
    color: #d32f2f;
    font-weight: bold;
}
.jarak
{
    height: 100px;
}
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="history-container">
    <h2>Riwayat Training</h2>
    
    <table class="history-table">
        <thead>
            <tr>
                <th>Topik Training</th>
                <th>Tanggal Training</th>
                <th>Status</th>
                <th>Tempat</th>
                <th>Biaya</th>
                <th>Selesai Pada</th>
            </tr>
        </thead>
        <tbody>
            <?php while ($row = $result->fetch_assoc()): ?>
            <tr>
                <td><?= htmlspecialchars($row['training_topic']) ?></td>
                <td><?= htmlspecialchars($row['start_date']) ?></td>
                <td class="status-<?= strtolower($row['status']) ?>">
                    <?= htmlspecialchars($row['status']) ?>
                </td>
                <td><?= htmlspecialchars($row['training_place']) ?></td>
                <td>Rp <?= number_format($row['training_cost'], 0, ',', '.') ?></td>
                <td><?= htmlspecialchars($row['completed_date']) ?></td>
            </tr>
            <?php endwhile; ?>
            
            <?php if ($result->num_rows === 0): ?>
            <tr>
                <td colspan="6" class="text-center">Belum ada riwayat training.</td>
            </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php include '../config/footer.php'; ?>
</body>
</html>

<?php
$stmt->close();
$conn->close();
?>