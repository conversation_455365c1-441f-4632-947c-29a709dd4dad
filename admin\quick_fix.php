<?php
// Quick fix untuk menambahkan kolom is_hidden
include '../config/config.php';

echo "<h2>Quick Fix: Add is_hidden Column</h2>";

try {
    // Langsung tambahkan kolom tanpa cek berlebihan
    $sql = "ALTER TABLE faq ADD COLUMN is_hidden TINYINT(1) DEFAULT 0";
    
    if ($conn->query($sql)) {
        echo "<p>✅ SUCCESS: is_hidden column added to FAQ table</p>";
    } else {
        // Jika error, mungkin kolom sudah ada
        if (strpos($conn->error, 'Duplicate column name') !== false) {
            echo "<p>ℹ️ INFO: is_hidden column already exists</p>";
        } else {
            echo "<p>❌ ERROR: " . $conn->error . "</p>";
        }
    }
    
    // Test query
    $test = $conn->query("SELECT COUNT(*) as count FROM faq WHERE is_hidden = 0");
    if ($test) {
        $result = $test->fetch_assoc();
        echo "<p>✅ TEST: Found " . $result['count'] . " visible FAQs</p>";
    }
    
    echo "<p>✅ DONE: You can now use FAQ management features</p>";
    
} catch (Exception $e) {
    echo "<p>❌ EXCEPTION: " . $e->getMessage() . "</p>";
}

echo "<p><a href='manage_faq.php'>Go to FAQ Management</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
</style>
