# Panduan Peran dalam Kelas Training

Dokumen ini menjelaskan perbedaan peran dan tanggung jawab dalam sistem kelas training.

## Daftar Isi
1. [<PERSON><PERSON> da<PERSON>](#peran-dalam-kelas)
2. [Instruktur](#instruktur)
3. [As<PERSON><PERSON>](#asisten)
4. [Pese<PERSON>](#peserta)
5. [<PERSON><PERSON>](#alur-kerja-penilaian)
6. [Sistem Notifikasi](#sistem-notifikasi)
7. [<PERSON><PERSON>](#izin-peran)

## Peran dalam Kelas

Sistem kelas training mendukung tiga peran utama:

| <PERSON><PERSON> | Deskripsi |
|-------|-----------|
| Instruktur | Pengajar utama yang bertanggung jawab penuh atas kelas |
| Asisten | Membantu instruktur dalam mengelola kelas dan mendukung peserta |
| Peserta | Mengikuti kelas dan menyelesaikan tugas-tugas yang diberikan |

## Instruktur

### Tanggung Jawab
- Mengelola konten pembelajaran
- Membuat dan mengedit kuis/ujian
- Menilai tugas peserta
- Meninjau pengajuan nilai dari asisten
- Mengelola peserta kelas
- Mengirim pengumuman
- Memoderasi diskusi
- Menerbitkan sertifikat

### Izin Khusus
- Mengelola materi pembelajaran (`manage_materials`)
- Membuat kuis (`create_quiz`)
- Mengedit kuis (`edit_quiz`)
- Menghapus kuis (`delete_quiz`)
- Menilai tugas (`grade_assignments`)
- Meninjau pengajuan nilai (`review_grade_proposals`)
- Mengelola peserta (`manage_participants`)
- Mengirim pengumuman (`send_announcements`)
- Melihat laporan (`view_reports`)
- Memoderasi diskusi (`moderate_discussions`)
- Menerbitkan sertifikat (`issue_certificates`)

## Asisten

### Tanggung Jawab
- Membantu instruktur dalam mengelola kelas
- Mengajukan nilai untuk tugas peserta
- Menjawab pertanyaan peserta
- Memoderasi diskusi
- Memberikan dukungan teknis

### Izin Khusus
- Melihat materi pembelajaran (`view_materials`)
- Mengunggah materi tambahan (`upload_supplementary_materials`)
- Melihat kuis sebelum dipublikasikan (`view_quiz_before_publish`)
- Mengajukan nilai (`propose_grades`)
- Melihat progres peserta (`view_student_progress`)
- Memoderasi diskusi (`moderate_discussions`)
- Menjawab pertanyaan (`answer_questions`)

## Peserta

### Tanggung Jawab
- Mengakses materi pembelajaran
- Mengerjakan kuis dan ujian
- Mengirimkan tugas
- Berpartisipasi dalam diskusi
- Menyelesaikan kelas

### Izin Khusus
- Melihat materi pembelajaran (`view_materials`)
- Mengerjakan kuis (`take_quiz`)
- Mengirimkan tugas (`submit_assignments`)
- Melihat nilai sendiri (`view_own_grades`)
- Berpartisipasi dalam diskusi (`participate_discussions`)
- Melihat progres sendiri (`view_own_progress`)

## Alur Kerja Penilaian

Sistem mendukung dua alur kerja penilaian:

### 1. Penilaian Langsung (oleh Instruktur)
1. Instruktur melihat tugas yang dikirimkan oleh peserta
2. Instruktur memberikan nilai dan umpan balik
3. Nilai langsung disimpan dan ditampilkan kepada peserta

### 2. Pengajuan Nilai (oleh Asisten)
1. Asisten melihat tugas yang dikirimkan oleh peserta
2. Asisten mengajukan nilai dan umpan balik
3. Instruktur menerima notifikasi tentang pengajuan nilai baru
4. Instruktur meninjau pengajuan nilai
5. Instruktur menyetujui atau menolak pengajuan nilai
6. Jika disetujui, nilai disimpan dan ditampilkan kepada peserta
7. Asisten menerima notifikasi tentang hasil peninjauan

## Sistem Notifikasi

Sistem notifikasi memungkinkan komunikasi antar peran:

### Jenis Notifikasi
- **Info**: Informasi umum
- **Success**: Pemberitahuan keberhasilan
- **Warning**: Peringatan
- **Error**: Kesalahan

### Contoh Notifikasi
- Instruktur menerima notifikasi saat asisten mengajukan nilai
- Asisten menerima notifikasi saat instruktur meninjau pengajuan nilai
- Peserta menerima notifikasi saat nilai mereka diumumkan

## Izin Peran

Sistem menggunakan model izin berbasis peran:

### Cara Kerja
1. Setiap peran memiliki serangkaian izin yang ditentukan
2. Izin menentukan tindakan apa yang dapat dilakukan oleh peran
3. Sistem memeriksa izin sebelum mengizinkan tindakan

### Pengecekan Izin
```php
// Contoh pengecekan izin
if (userHasPermission($user_id, $class_id, 'grade_assignments')) {
    // Pengguna dapat menilai tugas
} else {
    // Akses ditolak
}
```

### Penggunaan Fungsi Helper
```php
// Cek apakah pengguna adalah instruktur
if (isInstructor($user_id, $class_id)) {
    // Tampilkan menu instruktur
}

// Cek apakah pengguna adalah asisten
if (isAssistant($user_id, $class_id)) {
    // Tampilkan menu asisten
}

// Cek apakah pengguna adalah peserta
if (isStudent($user_id, $class_id)) {
    // Tampilkan menu peserta
}

// Cek apakah pengguna adalah staf pengajar (instruktur atau asisten)
if (isTeachingStaff($user_id, $class_id)) {
    // Tampilkan menu staf pengajar
}
```
