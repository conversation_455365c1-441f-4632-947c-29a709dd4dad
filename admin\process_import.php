<?php
/**
 * Process Excel Import for Training Data
 * This script processes the uploaded Excel file and imports the data into the database
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Require PhpSpreadsheet library
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;

// Initialize results array
$import_results = [];

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['import_data'])) {
    $_SESSION['error_message'] = "Permintaan tidak valid. <PERSON>lakan submit form dengan benar.";
    header('Location: import_template.php');
    exit();
}

// Check if file was uploaded
if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] != 0) {
    $_SESSION['error_message'] = "Error mengunggah file. Silakan coba lagi.";
    header('Location: import_template.php');
    exit();
}

$file_name = $_FILES['import_file']['name'];
$file_tmp = $_FILES['import_file']['tmp_name'];
$file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

// Check file extension
$allowed_extensions = array("xlsx", "xls");
if (!in_array($file_ext, $allowed_extensions)) {
    $_SESSION['error_message'] = "Ekstensi file tidak diizinkan. Silakan unggah file Excel (.xlsx atau .xls).";
    header('Location: import_template.php');
    exit();
}

try {
    // Load the spreadsheet
    $spreadsheet = IOFactory::load($file_tmp);

    // Deteksi jenis template berdasarkan nama sheet
    $sheetNames = $spreadsheet->getSheetNames();

    // Cek apakah ini template kelas
    if (in_array('Kelas', $sheetNames)) {
        processClassTemplate($spreadsheet, $conn, $user_id, $import_results);
    }

    // Cek apakah ini template kuis
    if (in_array('Kuis', $sheetNames) && in_array('Pertanyaan', $sheetNames)) {
        processQuizTemplate($spreadsheet, $conn, $user_id, $import_results);
    }

    // Jika tidak ada sheet yang dikenali
    if (empty($import_results)) {
        $import_results[] = [
            'type' => 'error',
            'sheet' => 'General',
            'message' => "Template tidak dikenali. Silakan gunakan template yang disediakan."
        ];
    }

    // Simpan hasil di sesi
    $_SESSION['import_results'] = $import_results;
    $_SESSION['success_message'] = "Proses import selesai. Lihat hasil di bawah.";

} catch (Exception $e) {
    $_SESSION['error_message'] = "Error memproses file Excel: " . $e->getMessage();
}

/**
 * Memproses template kelas
 */
function processClassTemplate($spreadsheet, $conn, $user_id, &$import_results) {
    // Ambil sheet Kelas
    $classesSheet = $spreadsheet->getSheetByName('Kelas');
    $classesData = $classesSheet->toArray(null, true, true, true);

    // Lewati baris header (7 baris pertama)
    $classesData = array_slice($classesData, 7);

    $classesImported = 0;
    $classesErrors = 0;

    foreach ($classesData as $row) {
        // Lewati baris kosong
        if (empty($row['A']) || empty($row['B'])) {
            continue;
        }

        $training_topic = trim($row['A']);
        $title = trim($row['B']);
        $description = !empty($row['C']) ? trim($row['C']) : null;
        $start_date = !empty($row['D']) ? trim($row['D']) : null;
        $end_date = !empty($row['E']) ? trim($row['E']) : null;
        $status = !empty($row['F']) ? trim($row['F']) : 'aktif';
        $max_participants = !empty($row['G']) ? intval(trim($row['G'])) : null;

        // Konversi status dari bahasa Indonesia ke bahasa Inggris untuk database
        $status_map = [
            'aktif' => 'active',
            'tidak aktif' => 'inactive',
            'selesai' => 'completed'
        ];

        $status = isset($status_map[$status]) ? $status_map[$status] : 'active';

        // Cari training_id berdasarkan topik
        $check_training_query = "SELECT id FROM training_submissions
                               WHERE training_topic = ? AND (status = 'Approved' OR status = 'Completed')";
        $stmt = $conn->prepare($check_training_query);
        $stmt->bind_param("s", $training_topic);
        $stmt->execute();
        $training_result = $stmt->get_result();

        if ($training_result->num_rows == 0) {
            $classesErrors++;
            $import_results[] = [
                'type' => 'error',
                'sheet' => 'Kelas',
                'message' => "Topik training '$training_topic' tidak ditemukan atau belum disetujui. Baris dilewati."
            ];
            continue;
        }

        // Ambil training_id
        $training_row = $training_result->fetch_assoc();
        $training_id = $training_row['id'];

        // Masukkan kelas
        $insert_query = "INSERT INTO training_classes (
                        training_id, title, description, start_date, end_date,
                        status, max_participants, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param(
            "isssssii",
            $training_id, $title, $description, $start_date, $end_date,
            $status, $max_participants, $user_id
        );

        if ($stmt->execute()) {
            $classesImported++;
            $class_id = $stmt->insert_id;
            $import_results[] = [
                'type' => 'success',
                'sheet' => 'Kelas',
                'message' => "Kelas '$title' berhasil diimpor dengan ID: $class_id"
            ];
        } else {
            $classesErrors++;
            $import_results[] = [
                'type' => 'error',
                'sheet' => 'Kelas',
                'message' => "Error mengimpor kelas '$title': " . $conn->error
            ];
        }
    }

    $import_results[] = [
        'type' => 'info',
        'sheet' => 'Kelas',
        'message' => "Berhasil mengimpor $classesImported kelas. Error: $classesErrors."
    ];
}

/**
 * Memproses template kuis
 */
function processQuizTemplate($spreadsheet, $conn, $user_id, &$import_results) {
    // Proses sheet Kuis
    $quizzesSheet = $spreadsheet->getSheetByName('Kuis');
    $quizzesData = $quizzesSheet->toArray(null, true, true, true);

    // Lewati baris header (6 baris pertama)
    $quizzesData = array_slice($quizzesData, 6);

    $quizzesImported = 0;
    $quizzesErrors = 0;
    $quizIdMap = []; // Untuk memetakan judul kuis ke ID kuis

    foreach ($quizzesData as $row) {
        // Lewati baris kosong
        if (empty($row['A']) || empty($row['B'])) {
            continue;
        }

        $class_title = trim($row['A']);
        $quiz_title = trim($row['B']);
        $description = !empty($row['C']) ? trim($row['C']) : null;
        $instructions = !empty($row['D']) ? trim($row['D']) : null;
        $time_limit = !empty($row['E']) ? intval(trim($row['E'])) : null;
        $passing_score = !empty($row['F']) ? intval(trim($row['F'])) : null;
        $is_published = isset($row['G']) ? (trim($row['G']) == '1' ? 1 : 0) : 0;
        $allow_multiple_attempts = isset($row['H']) ? (trim($row['H']) == '1' ? 1 : 0) : 0;
        $randomize_questions = isset($row['I']) ? (trim($row['I']) == '1' ? 1 : 0) : 0;

        // Cari class_id berdasarkan judul
        $check_class_query = "SELECT id FROM training_classes WHERE title = ?";
        $stmt = $conn->prepare($check_class_query);
        $stmt->bind_param("s", $class_title);
        $stmt->execute();
        $class_result = $stmt->get_result();

        if ($class_result->num_rows == 0) {
            $quizzesErrors++;
            $import_results[] = [
                'type' => 'error',
                'sheet' => 'Kuis',
                'message' => "Kelas dengan judul '$class_title' tidak ditemukan. Baris dilewati."
            ];
            continue;
        }

        // Ambil class_id
        $class_row = $class_result->fetch_assoc();
        $class_id = $class_row['id'];

        // Masukkan kuis
        $insert_query = "INSERT INTO training_quizzes (
                        class_id, title, description, instructions, time_limit,
                        passing_score, is_published, allow_multiple_attempts, randomize_questions, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param(
            "isssiiiiis",
            $class_id, $quiz_title, $description, $instructions, $time_limit,
            $passing_score, $is_published, $allow_multiple_attempts, $randomize_questions, $user_id
        );

        if ($stmt->execute()) {
            $quizzesImported++;
            $quiz_id = $stmt->insert_id;
            $quizIdMap[$quiz_title] = $quiz_id; // Simpan pemetaan judul ke ID

            $import_results[] = [
                'type' => 'success',
                'sheet' => 'Kuis',
                'message' => "Kuis '$quiz_title' berhasil diimpor dengan ID: $quiz_id"
            ];
        } else {
            $quizzesErrors++;
            $import_results[] = [
                'type' => 'error',
                'sheet' => 'Kuis',
                'message' => "Error mengimpor kuis '$quiz_title': " . $conn->error
            ];
        }
    }

    $import_results[] = [
        'type' => 'info',
        'sheet' => 'Kuis',
        'message' => "Berhasil mengimpor $quizzesImported kuis. Error: $quizzesErrors."
    ];

    // Proses sheet Pertanyaan jika ada kuis yang berhasil diimpor
    if (!empty($quizIdMap)) {
        processQuestionsSheet($spreadsheet, $conn, $import_results, $quizIdMap);
    }
}

/**
 * Memproses sheet pertanyaan
 */
function processQuestionsSheet($spreadsheet, $conn, &$import_results, $quizIdMap) {
    // Ambil sheet Pertanyaan
    $questionsSheet = $spreadsheet->getSheetByName('Pertanyaan');
    $questionsData = $questionsSheet->toArray(null, true, true, true);

    // Lewati baris header (7 baris pertama)
    $questionsData = array_slice($questionsData, 7);

    $questionsImported = 0;
    $questionsErrors = 0;
    $questionIdMap = []; // Untuk memetakan teks pertanyaan ke ID pertanyaan

    foreach ($questionsData as $row) {
        // Lewati baris kosong
        if (empty($row['A']) || empty($row['B'])) {
            continue;
        }

        $quiz_title = trim($row['A']);
        $question_text = trim($row['B']);
        $question_type = !empty($row['C']) ? trim($row['C']) : 'pilihan_ganda';
        $points = !empty($row['D']) ? intval(trim($row['D'])) : 10;
        $order_number = !empty($row['E']) ? intval(trim($row['E'])) : null;

        // Konversi tipe pertanyaan dari bahasa Indonesia ke bahasa Inggris untuk database
        $type_map = [
            'pilihan_ganda' => 'multiple_choice',
            'benar_salah' => 'true_false',
            'jawaban_singkat' => 'short_answer',
            'esai' => 'essay'
        ];

        $question_type = isset($type_map[$question_type]) ? $type_map[$question_type] : 'multiple_choice';

        // Cek apakah kuis ada di pemetaan
        if (!isset($quizIdMap[$quiz_title])) {
            $questionsErrors++;
            $import_results[] = [
                'type' => 'error',
                'sheet' => 'Pertanyaan',
                'message' => "Kuis dengan judul '$quiz_title' tidak ditemukan. Baris dilewati."
            ];
            continue;
        }

        $quiz_id = $quizIdMap[$quiz_title];

        // Masukkan pertanyaan
        $insert_query = "INSERT INTO training_questions (
                        quiz_id, question_text, question_type, points, order_number
                    ) VALUES (?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param(
            "issii",
            $quiz_id, $question_text, $question_type, $points, $order_number
        );

        if ($stmt->execute()) {
            $questionsImported++;
            $question_id = $stmt->insert_id;
            $questionIdMap[$question_text] = $question_id; // Simpan pemetaan teks ke ID

            $import_results[] = [
                'type' => 'success',
                'sheet' => 'Pertanyaan',
                'message' => "Pertanyaan untuk kuis '$quiz_title' berhasil diimpor"
            ];
        } else {
            $questionsErrors++;
            $import_results[] = [
                'type' => 'error',
                'sheet' => 'Pertanyaan',
                'message' => "Error mengimpor pertanyaan: " . $conn->error
            ];
        }
    }

    $import_results[] = [
        'type' => 'info',
        'sheet' => 'Pertanyaan',
        'message' => "Berhasil mengimpor $questionsImported pertanyaan. Error: $questionsErrors."
    ];

    // Proses sheet Opsi jika ada pertanyaan yang berhasil diimpor
    if (!empty($questionIdMap) && $spreadsheet->sheetNameExists('Opsi')) {
        processOptionsSheet($spreadsheet, $conn, $import_results, $questionIdMap);
    }
}

/**
 * Memproses sheet opsi
 */
function processOptionsSheet($spreadsheet, $conn, &$import_results, $questionIdMap) {
    // Ambil sheet Opsi
    $optionsSheet = $spreadsheet->getSheetByName('Opsi');
    $optionsData = $optionsSheet->toArray(null, true, true, true);

    // Lewati baris header (6 baris pertama)
    $optionsData = array_slice($optionsData, 6);

    $optionsImported = 0;
    $optionsErrors = 0;

    foreach ($optionsData as $row) {
        // Lewati baris kosong
        if (empty($row['A']) || empty($row['B'])) {
            continue;
        }

        $question_text = trim($row['A']);
        $option_text = trim($row['B']);
        $is_correct = isset($row['C']) ? (trim($row['C']) == '1' ? 1 : 0) : 0;
        $order_number = !empty($row['D']) ? intval(trim($row['D'])) : null;

        // Cek apakah pertanyaan ada di pemetaan
        if (!isset($questionIdMap[$question_text])) {
            $optionsErrors++;
            $import_results[] = [
                'type' => 'error',
                'sheet' => 'Opsi',
                'message' => "Pertanyaan dengan teks '$question_text' tidak ditemukan. Baris dilewati."
            ];
            continue;
        }

        $question_id = $questionIdMap[$question_text];

        // Masukkan opsi
        $insert_query = "INSERT INTO training_question_options (
                        question_id, option_text, is_correct, order_number
                    ) VALUES (?, ?, ?, ?)";

        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param(
            "isii",
            $question_id, $option_text, $is_correct, $order_number
        );

        if ($stmt->execute()) {
            $optionsImported++;
        } else {
            $optionsErrors++;
            $import_results[] = [
                'type' => 'error',
                'sheet' => 'Opsi',
                'message' => "Error mengimpor opsi: " . $conn->error
            ];
        }
    }

    $import_results[] = [
        'type' => 'info',
        'sheet' => 'Opsi',
        'message' => "Berhasil mengimpor $optionsImported opsi. Error: $optionsErrors."
    ];
}

// Redirect back to import page
header('Location: import_template.php');
exit();
?>
