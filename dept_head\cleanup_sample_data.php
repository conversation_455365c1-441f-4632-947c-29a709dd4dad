<?php
/**
 * Script untuk membersihkan data sample training
 * Gunakan dengan hati-hati!
 */

require_once '../config/config.php';

// Konfirmasi sebelum menghapus
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<h2>⚠️ Cleanup Sample Training Data</h2>";
    echo "<p><strong>WARNING:</strong> This will delete sample training data!</p>";
    echo "<p>This action will remove:</p>";
    echo "<ul>";
    echo "<li>Sample offline training records</li>";
    echo "<li>Sample online training classes</li>";
    echo "<li>Sample training participants</li>";
    echo "<li>Sample training attendance records</li>";
    echo "</ul>";
    echo "<p><strong>Are you sure you want to continue?</strong></p>";
    echo "<p><a href='?confirm=yes' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Yes, Delete Sample Data</a></p>";
    echo "<p><a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Cancel</a></p>";
    exit;
}

try {
    echo "<h2>🧹 Cleaning Up Sample Data</h2>";
    
    // 1. Delete training attendance records for sample trainings
    echo "<h3>1. Cleaning Training Attendance</h3>";
    $query = "DELETE FROM training_attendance WHERE training_id IN (
                SELECT id FROM offline_training 
                WHERE trainer_name IN ('Budi Santoso', 'Dr. Sari Wijaya', 'Ir. Ahmad Fauzi')
              )";
    
    if ($conn->query($query)) {
        $affected = $conn->affected_rows;
        echo "✅ Deleted $affected training attendance records<br>";
    } else {
        echo "❌ Error deleting training attendance: " . $conn->error . "<br>";
    }
    
    // 2. Delete training participants for sample classes
    echo "<h3>2. Cleaning Training Participants</h3>";
    $query = "DELETE FROM training_participants WHERE class_id IN (
                SELECT id FROM training_classes 
                WHERE title IN ('Digital Marketing Fundamentals', 'Data Analysis with Excel')
              )";
    
    if ($conn->query($query)) {
        $affected = $conn->affected_rows;
        echo "✅ Deleted $affected training participant records<br>";
    } else {
        echo "❌ Error deleting training participants: " . $conn->error . "<br>";
    }
    
    // 3. Delete sample offline trainings
    echo "<h3>3. Cleaning Offline Training</h3>";
    $query = "DELETE FROM offline_training 
              WHERE trainer_name IN ('Budi Santoso', 'Dr. Sari Wijaya', 'Ir. Ahmad Fauzi')";
    
    if ($conn->query($query)) {
        $affected = $conn->affected_rows;
        echo "✅ Deleted $affected offline training records<br>";
    } else {
        echo "❌ Error deleting offline training: " . $conn->error . "<br>";
    }
    
    // 4. Delete sample online trainings
    echo "<h3>4. Cleaning Online Training Classes</h3>";
    $query = "DELETE FROM training_classes 
              WHERE title IN ('Digital Marketing Fundamentals', 'Data Analysis with Excel')";
    
    if ($conn->query($query)) {
        $affected = $conn->affected_rows;
        echo "✅ Deleted $affected online training class records<br>";
    } else {
        echo "❌ Error deleting training classes: " . $conn->error . "<br>";
    }
    
    // 5. Show remaining data count
    echo "<h3>5. Data Summary After Cleanup</h3>";
    
    $tables = [
        'offline_training' => 'Offline Training',
        'training_classes' => 'Online Training Classes',
        'training_participants' => 'Training Participants',
        'training_attendance' => 'Training Attendance'
    ];
    
    foreach ($tables as $table => $label) {
        $query = "SELECT COUNT(*) as count FROM $table";
        $result = $conn->query($query);
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "📊 $label: $count records remaining<br>";
        }
    }
    
    echo "<h3>✅ Cleanup Complete!</h3>";
    echo "<p>Sample training data has been removed from the database.</p>";
    echo "<p><a href='index.php'>Return to Calendar</a></p>";
    
} catch (Exception $e) {
    echo "❌ Error during cleanup: " . $e->getMessage();
    error_log("Error in cleanup_sample_data.php: " . $e->getMessage());
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
