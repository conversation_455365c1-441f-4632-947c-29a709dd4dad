<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Judul halaman
$pageTitle = "Update Script.js";

// Fungsi untuk mengambil data dari database
function getOrganizationData($conn) {
    // Get departments
    $deptQuery = "SELECT * FROM departments ORDER BY code";
    $deptResult = $conn->query($deptQuery);
    $departments = [];
    
    if ($deptResult) {
        while ($row = $deptResult->fetch_assoc()) {
            $departments[] = $row;
        }
    }
    
    // Get sub departments
    $subDeptQuery = "SELECT s.*, d.code as department_code 
                    FROM sub_departments s
                    JOIN departments d ON s.department_id = d.id
                    ORDER BY s.code";
    $subDeptResult = $conn->query($subDeptQuery);
    $subDepartments = [];
    
    if ($subDeptResult) {
        while ($row = $subDeptResult->fetch_assoc()) {
            $subDepartments[] = $row;
        }
    }
    
    // Get positions
    $posQuery = "SELECT p.*, s.code as sub_department_code, d.code as department_code
                FROM positions p
                JOIN sub_departments s ON p.sub_department_id = s.id
                JOIN departments d ON s.department_id = d.id
                ORDER BY p.name";
    $posResult = $conn->query($posQuery);
    $positions = [];
    
    if ($posResult) {
        while ($row = $posResult->fetch_assoc()) {
            $positions[] = $row;
        }
    }
    
    return [
        'departments' => $departments,
        'subDepartments' => $subDepartments,
        'positions' => $positions
    ];
}

// Fungsi untuk menghasilkan konten script.js
function generateScriptJsContent($data) {
    $departments = $data['departments'];
    $subDepartments = $data['subDepartments'];
    $positions = $data['positions'];
    
    // Generate departemenOptions array
    $deptOptions = [];
    foreach ($departments as $dept) {
        $deptOptions[] = $dept['code'];
    }
    $deptOptionsStr = '"' . implode('", "', $deptOptions) . '"';
    
    // Generate Bagian object
    $bagian = [];
    foreach ($departments as $dept) {
        $deptCode = $dept['code'];
        $subDepts = [];
        
        foreach ($subDepartments as $subDept) {
            if ($subDept['department_code'] === $deptCode) {
                $subDepts[] = $subDept['code'];
            }
        }
        
        if (!empty($subDepts)) {
            $bagian[$deptCode] = $subDepts;
        }
    }
    
    $bagianStr = '';
    foreach ($bagian as $deptCode => $subDepts) {
        $subDeptsStr = '"' . implode('", "', $subDepts) . '"';
        $bagianStr .= "    \"$deptCode\": [$subDeptsStr],\n";
    }
    
    // Generate Jabatan object
    $jabatan = [];
    foreach ($subDepartments as $subDept) {
        $subDeptCode = $subDept['code'];
        $posArray = [];
        
        foreach ($positions as $pos) {
            if ($pos['sub_department_code'] === $subDeptCode) {
                $posArray[] = $pos['name'];
            }
        }
        
        if (!empty($posArray)) {
            $jabatan[$subDeptCode] = $posArray;
        }
    }
    
    $jabatanStr = '';
    foreach ($jabatan as $subDeptCode => $posArray) {
        $jabatanStr .= "\"$subDeptCode\": [\n";
        foreach ($posArray as $pos) {
            $jabatanStr .= "\"$pos\",\n";
        }
        $jabatanStr .= "],\n";
    }
    
    // Generate full script.js content
    $content = <<<EOT
// Departemen options
const departemenOptions = [$deptOptionsStr];

// Bagian (Sub Departemen) berdasarkan Departemen
const Bagian = {
$bagianStr};

// Jabatan berdasarkan Bagian
const Jabatan = {
$jabatanStr};

// Function to update Bagian dropdown based on selected Dept
function updateBagian(deptSelect, bagianSelect) {
    const selectedDept = deptSelect.value;
    bagianSelect.innerHTML = '<option value="">Pilih Sub Dept</option>';

    if (Bagian[selectedDept]) {
        Bagian[selectedDept].forEach(bagian => {
            const option = document.createElement('option');
            option.value = bagian;
            option.text = bagian;
            bagianSelect.appendChild(option);
        });
    }
}

// Function to update Jabatan dropdown based on selected Bagian
function updateJabatan(bagianSelect, jabatanSelect) {
    const selectedBagian = bagianSelect.value;
    jabatanSelect.innerHTML = '<option value="">Pilih Jabatan</option>';

    if (Jabatan[selectedBagian]) {
        Jabatan[selectedBagian].forEach(jabatan => {
            const option = document.createElement('option');
            option.value = jabatan;
            option.text = jabatan;
            jabatanSelect.appendChild(option);
        });
    }
}
EOT;
    
    return $content;
}

// Proses update script.js jika form disubmit
$message = '';
$scriptContent = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_script'])) {
    // Ambil data dari database
    $data = getOrganizationData($conn);
    
    // Generate konten script.js
    $scriptContent = generateScriptJsContent($data);
    
    // Path ke file script.js
    $scriptPath = '../config/script.js';
    
    // Simpan konten ke file
    if (file_put_contents($scriptPath, $scriptContent)) {
        $message = '<div class="alert alert-success">File script.js berhasil diperbarui.</div>';
    } else {
        $message = '<div class="alert alert-danger">Gagal memperbarui file script.js. Pastikan file memiliki izin tulis.</div>';
    }
} else {
    // Ambil konten script.js saat ini
    $scriptPath = '../config/script.js';
    if (file_exists($scriptPath)) {
        $scriptContent = file_get_contents($scriptPath);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    .container-form {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .card-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .btn-primary {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .btn-primary:hover {
        background-color: #45a049;
    }
    
    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 4px;
    }
    
    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .code-preview {
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        font-family: monospace;
        white-space: pre-wrap;
        max-height: 500px;
        overflow-y: auto;
    }
</style>

<body>
<?php include '../config/navbarb.php'; ?>
<div class="jarak"></div>
<div class="container-form">
<button style="border-radius: 10px; margin-bottom:10px;">
        <a href="manage_organization.php" style="text-decoration:none; color:white">Kembali</a>
    </button>
    <div class="card">
        <div class="card-header">
            <h2><?php echo $pageTitle; ?></h2>
        </div>
        <div class="card-body">
            <?php echo $message; ?>
            
            <p>
                Halaman ini akan memperbarui file <code>script.js</code> dengan data departemen, sub departemen, dan jabatan dari database.
                Klik tombol "Update Script.js" untuk memperbarui file.
            </p>
            
            <form method="post" action="">
                <button type="submit" name="update_script" class="btn-primary">Update Script.js</button>
            </form>
            
            <h3 style="margin-top: 20px;">Preview Script.js</h3>
            <div class="code-preview"><?php echo htmlspecialchars($scriptContent); ?></div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>
</body>
</html>
