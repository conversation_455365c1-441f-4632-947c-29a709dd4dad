<?php
/**
 * Create Classroom Tables
 * This script creates the necessary tables for the classroom feature
 */

// Include configuration file
include_once 'config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    echo "Unauthorized access";
    exit();
}

// Function to create tables
function createTables($conn) {
    $success = true;
    $messages = [];

    // Create training_classes table
    $query = "CREATE TABLE IF NOT EXISTS training_classes (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        training_id INT(11) NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NULL,
        start_date DATE NULL,
        end_date DATE NULL,
        status ENUM('active', 'inactive', 'completed') NOT NULL DEFAULT 'active',
        max_participants INT(11) NULL,
        created_by INT(11) NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (training_id) REFERENCES training_submissions(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (!$conn->query($query)) {
        $success = false;
        $messages[] = "Error creating training_classes table: " . $conn->error;
    } else {
        $messages[] = "Training classes table created successfully";
    }

    // Create training_participants table
    $query = "CREATE TABLE IF NOT EXISTS training_participants (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        class_id INT(11) NOT NULL,
        user_id INT(11) NOT NULL,
        role ENUM('student', 'instructor', 'assistant') NOT NULL DEFAULT 'student',
        status ENUM('active', 'inactive', 'completed') NOT NULL DEFAULT 'active',
        joined_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME NULL,
        FOREIGN KEY (class_id) REFERENCES training_classes(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_participant (class_id, user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (!$conn->query($query)) {
        $success = false;
        $messages[] = "Error creating training_participants table: " . $conn->error;
    } else {
        $messages[] = "Training participants table created successfully";
    }

    // Create training_materials table
    $query = "CREATE TABLE IF NOT EXISTS training_materials (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        class_id INT(11) NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NULL,
        type ENUM('document', 'video', 'link', 'other') NOT NULL DEFAULT 'document',
        file_path VARCHAR(255) NULL,
        external_url VARCHAR(255) NULL,
        order_number INT(11) NOT NULL DEFAULT 0,
        is_published TINYINT(1) NOT NULL DEFAULT 1,
        created_by INT(11) NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES training_classes(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (!$conn->query($query)) {
        $success = false;
        $messages[] = "Error creating training_materials table: " . $conn->error;
    } else {
        $messages[] = "Training materials table created successfully";
    }

    // Create training_assignments table
    $query = "CREATE TABLE IF NOT EXISTS training_assignments (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        class_id INT(11) NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NULL,
        instructions TEXT NULL,
        due_date DATETIME NULL,
        points INT(11) NULL,
        is_published TINYINT(1) NOT NULL DEFAULT 1,
        created_by INT(11) NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES training_classes(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (!$conn->query($query)) {
        $success = false;
        $messages[] = "Error creating training_assignments table: " . $conn->error;
    } else {
        $messages[] = "Training assignments table created successfully";
    }

    // Create training_assignment_submissions table
    $query = "CREATE TABLE IF NOT EXISTS training_assignment_submissions (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        assignment_id INT(11) NOT NULL,
        user_id INT(11) NOT NULL,
        submission_text TEXT NULL,
        file_path VARCHAR(255) NULL,
        status ENUM('submitted', 'graded', 'returned') NOT NULL DEFAULT 'submitted',
        grade INT(11) NULL,
        feedback TEXT NULL,
        submitted_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        graded_at DATETIME NULL,
        graded_by INT(11) NULL,
        FOREIGN KEY (assignment_id) REFERENCES training_assignments(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (graded_by) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (!$conn->query($query)) {
        $success = false;
        $messages[] = "Error creating training_assignment_submissions table: " . $conn->error;
    } else {
        $messages[] = "Training assignment submissions table created successfully";
    }

    // Create training_discussions table
    $query = "CREATE TABLE IF NOT EXISTS training_discussions (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        class_id INT(11) NOT NULL,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        is_announcement TINYINT(1) NOT NULL DEFAULT 0,
        is_pinned TINYINT(1) NOT NULL DEFAULT 0,
        created_by INT(11) NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES training_classes(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (!$conn->query($query)) {
        $success = false;
        $messages[] = "Error creating training_discussions table: " . $conn->error;
    } else {
        $messages[] = "Training discussions table created successfully";
    }

    // Create training_discussion_comments table
    $query = "CREATE TABLE IF NOT EXISTS training_discussion_comments (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        discussion_id INT(11) NOT NULL,
        content TEXT NOT NULL,
        parent_id INT(11) NULL,
        created_by INT(11) NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (discussion_id) REFERENCES training_discussions(id) ON DELETE CASCADE,
        FOREIGN KEY (parent_id) REFERENCES training_discussion_comments(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (!$conn->query($query)) {
        $success = false;
        $messages[] = "Error creating training_discussion_comments table: " . $conn->error;
    } else {
        $messages[] = "Training discussion comments table created successfully";
    }

    return ['success' => $success, 'messages' => $messages];
}

// Create tables
$result = createTables($conn);

// Display results
echo "<h1>Create Classroom Tables</h1>";
echo "<ul>";
foreach ($result['messages'] as $message) {
    echo "<li>" . $message . "</li>";
}
echo "</ul>";

if ($result['success']) {
    echo "<p>All tables created successfully. <a href='../admin/dashboard.php'>Go to Dashboard</a></p>";
} else {
    echo "<p>There were errors creating some tables. Please check the messages above.</p>";
}

// Close database connection
$conn->close();
?>
