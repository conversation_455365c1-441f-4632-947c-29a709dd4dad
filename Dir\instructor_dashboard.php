<?php
/**
 * Instructor Dashboard Page
 * This page provides instructor-specific functionality for managing a class
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';
include '../includes/class_role_helper.php';
include '../includes/notification_helper.php';
include '../includes/permission_helper.php';
include '../includes/grade_proposal_helper.php';

// Get class ID from URL
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$user_id = $_SESSION['user_id'];

// Check if class exists
$class_query = "SELECT c.*, t.training_topic, t.training_type
               FROM training_classes c
               JOIN training_submissions t ON c.training_id = t.id
               WHERE c.id = ?";
$stmt = $conn->prepare($class_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$class = $result->fetch_assoc();
$stmt->close();

if (!$class) {
    $_SESSION['error'] = "Kelas tidak ditemukan.";
    header("Location: classroom.php");
    exit();
}

// Check if user is an instructor in this class
if (!isInstructor($user_id, $class_id)) {
    $_SESSION['error'] = "Anda tidak memiliki akses sebagai instruktur untuk kelas ini.";
    header("Location: classroom_detail.php?id=$class_id");
    exit();
}

// Get class statistics
$stats = [];

// Get total participants
$participants_query = "SELECT
                      COUNT(*) as total_participants,
                      SUM(CASE WHEN role = 'student' THEN 1 ELSE 0 END) as student_count,
                      SUM(CASE WHEN role = 'assistant' THEN 1 ELSE 0 END) as assistant_count
                      FROM training_participants
                      WHERE class_id = ? AND status = 'active'";
$stmt = $conn->prepare($participants_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$stats['participants'] = $result->fetch_assoc();
$stmt->close();

// Get total materials
$materials_query = "SELECT COUNT(*) as total_materials FROM training_materials WHERE class_id = ?";
$stmt = $conn->prepare($materials_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$stats['materials'] = $result->fetch_assoc()['total_materials'];
$stmt->close();

// Get total assignments
$assignments_query = "SELECT COUNT(*) as total_assignments FROM training_assignments WHERE class_id = ?";
$stmt = $conn->prepare($assignments_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$stats['assignments'] = $result->fetch_assoc()['total_assignments'];
$stmt->close();

// Get total discussions
$discussions_query = "SELECT COUNT(*) as total_discussions FROM training_discussions WHERE class_id = ?";
$stmt = $conn->prepare($discussions_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$stats['discussions'] = $result->fetch_assoc()['total_discussions'];
$stmt->close();

// Get pending grade proposals
$pending_proposals = getPendingGradeProposals($class_id);
$stats['pending_proposals'] = count($pending_proposals);

// Get recent activities
$activities_query = "SELECT 'material' as type, m.title, m.created_at, u.name as user_name
                    FROM training_materials m
                    JOIN users u ON m.created_by = u.id
                    WHERE m.class_id = ?
                    UNION
                    SELECT 'assignment' as type, a.title, a.created_at, u.name as user_name
                    FROM training_assignments a
                    JOIN users u ON a.created_by = u.id
                    WHERE a.class_id = ?
                    UNION
                    SELECT 'discussion' as type, d.title, d.created_at, u.name as user_name
                    FROM training_discussions d
                    JOIN users u ON d.created_by = u.id
                    WHERE d.class_id = ?
                    ORDER BY created_at DESC
                    LIMIT 5";
$stmt = $conn->prepare($activities_query);
$stmt->bind_param("iii", $class_id, $class_id, $class_id);
$stmt->execute();
$result = $stmt->get_result();
$recent_activities = [];
while ($row = $result->fetch_assoc()) {
    $recent_activities[] = $row;
}
$stmt->close();

// Page title
$page_title = "Panel Instruktur - " . $class['title'];
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="../asset/css/bootstrap.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../asset/css/styles.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Check if CSS files exist and provide fallback CDN links -->
    <script>
        // Function to check if a CSS file exists
        function checkCssFile(url) {
            var http = new XMLHttpRequest();
            http.open('HEAD', url, false);
            http.send();
            return http.status != 404;
        }

        // Check Bootstrap CSS
        if (!checkCssFile('../asset/css/bootstrap.min.css')) {
            document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">');
        }

        // Check Font Awesome
        if (!checkCssFile('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css')) {
            document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">');
        }
    </script>
    <style>
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .stats-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .stats-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #28a745;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .action-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .action-card h3 {
            margin-bottom: 20px;
            color: #343a40;
            font-weight: 600;
        }

        .action-btn {
            display: block;
            width: 100%;
            padding: 12px;
            margin-bottom: 10px;
            border-radius: 5px;
            text-align: left;
            transition: all 0.3s ease;
        }

        .action-btn i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .activity-item {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
        }

        .activity-icon.material {
            background-color: #17a2b8;
        }

        .activity-icon.assignment {
            background-color: #ffc107;
        }

        .activity-icon.discussion {
            background-color: #6f42c1;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .activity-meta {
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <?php
    // Try to include navbar with different paths
    $navbar_included = false;
    $navbar_paths = [
        '../includes/navbar.php',
        dirname(__DIR__) . '/includes/navbar.php',
        '../config/navbara.php'
    ];

    foreach ($navbar_paths as $path) {
        if (file_exists($path)) {
            include $path;
            $navbar_included = true;
            break;
        }
    }

    // Fallback navbar if none of the includes work
    if (!$navbar_included) {
        include '../includes/fallback_navbar.php';
    } ?>

    <!-- Spacer after navbar -->
    <div style="margin-top: 20px;"></div>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div class="container">
                <h1><i class="fas fa-chalkboard-teacher"></i> Panel Instruktur</h1>
                <p class="mb-0"><?= htmlspecialchars($class['title']) ?> - <?= htmlspecialchars($class['training_topic']) ?></p>
            </div>
        </div>

        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <?= $_SESSION['success'] ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <?= $_SESSION['error'] ?>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-8">
                <div class="row">
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="stats-card text-center">
                            <div class="stats-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stats-number"><?= $stats['participants']['total_participants'] ?? 0 ?></div>
                            <div class="stats-label">Total Peserta</div>
                        </div>
                    </div>

                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="stats-card text-center">
                            <div class="stats-icon">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="stats-number"><?= $stats['materials'] ?? 0 ?></div>
                            <div class="stats-label">Materi</div>
                        </div>
                    </div>

                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="stats-card text-center">
                            <div class="stats-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="stats-number"><?= $stats['assignments'] ?? 0 ?></div>
                            <div class="stats-label">Tugas</div>
                        </div>
                    </div>

                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="stats-card text-center">
                            <div class="stats-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div class="stats-number"><?= $stats['discussions'] ?? 0 ?></div>
                            <div class="stats-label">Diskusi</div>
                        </div>
                    </div>
                </div>

                <div class="action-card">
                    <h3>Aktivitas Terbaru</h3>

                    <?php if (empty($recent_activities)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
                            <p class="mb-0">Belum ada aktivitas terbaru.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recent_activities as $activity): ?>
                            <div class="activity-item d-flex align-items-center">
                                <?php
                                $icon = 'fas fa-file-alt';
                                $type_label = 'Materi';
                                $type_class = 'material';

                                if ($activity['type'] == 'assignment') {
                                    $icon = 'fas fa-tasks';
                                    $type_label = 'Tugas';
                                    $type_class = 'assignment';
                                } elseif ($activity['type'] == 'discussion') {
                                    $icon = 'fas fa-comments';
                                    $type_label = 'Diskusi';
                                    $type_class = 'discussion';
                                }
                                ?>
                                <div class="activity-icon <?= $type_class ?>">
                                    <i class="<?= $icon ?>"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title"><?= htmlspecialchars($activity['title']) ?></div>
                                    <div class="activity-meta">
                                        <span><?= $type_label ?> ditambahkan oleh <?= htmlspecialchars($activity['user_name']) ?></span>
                                        <span class="ms-2"><?= date('d M Y H:i', strtotime($activity['created_at'])) ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="col-md-4">
                <div class="action-card">
                    <h3>Tindakan Instruktur</h3>

                    <a href="manage_materials.php?class_id=<?= $class_id ?>" class="btn btn-light action-btn">
                        <i class="fas fa-book"></i> Kelola Materi
                    </a>

                    <a href="manage_assignments.php?class_id=<?= $class_id ?>" class="btn btn-light action-btn">
                        <i class="fas fa-tasks"></i> Kelola Tugas
                    </a>

                    <a href="manage_quizzes.php?class_id=<?= $class_id ?>" class="btn btn-light action-btn">
                        <i class="fas fa-question-circle"></i> Kelola Kuis
                    </a>

                    <a href="manage_participants.php?class_id=<?= $class_id ?>" class="btn btn-light action-btn">
                        <i class="fas fa-users"></i> Kelola Peserta
                    </a>

                    <a href="review_grade_proposals.php?class_id=<?= $class_id ?>" class="btn btn-light action-btn">
                        <?php if ($stats['pending_proposals'] > 0): ?>
                            <span class="badge bg-danger float-end"><?= $stats['pending_proposals'] ?></span>
                        <?php endif; ?>
                        <i class="fas fa-clipboard-check"></i> Tinjau Pengajuan Nilai
                    </a>

                    <a href="send_announcement.php?class_id=<?= $class_id ?>" class="btn btn-light action-btn">
                        <i class="fas fa-bullhorn"></i> Kirim Pengumuman
                    </a>

                    <a href="class_reports.php?class_id=<?= $class_id ?>" class="btn btn-light action-btn">
                        <i class="fas fa-chart-bar"></i> Laporan Kelas
                    </a>
                </div>

                <div class="action-card">
                    <h3>Navigasi</h3>

                    <a href="classroom_detail.php?id=<?= $class_id ?>" class="btn btn-primary action-btn">
                        <i class="fas fa-chalkboard"></i> Kembali ke Kelas
                    </a>

                    <a href="classroom.php" class="btn btn-secondary action-btn">
                        <i class="fas fa-home"></i> Kembali ke Daftar Kelas
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php
    // Include footer
    $footer_included = false;
    $footer_paths = [
        '../includes/footer.php',
        dirname(__DIR__) . '/includes/footer.php',
        '../config/footer.php'
    ];

    foreach ($footer_paths as $path) {
        if (file_exists($path)) {
            include $path;
            $footer_included = true;
            break;
        }
    }

    // Fallback footer if none of the includes work
    if (!$footer_included) {
    ?>
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">&copy; <?= date('Y') ?> Training System. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">Version 1.0</p>
                </div>
            </div>
        </div>
    </footer>

    <style>
        .footer {
            margin-top: 50px;
            border-top: 1px solid #e9ecef;
        }
    </style>
    <?php } ?>

    <!-- Bootstrap JS Bundle -->
    <script src="../asset/js/bootstrap.bundle.min.js"></script>

    <!-- Fallback to CDN if local file doesn't exist -->
    <script>
        if(typeof($.fn.modal) === 'undefined') {
            document.write('<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"><\/script>');
        }
    </script>

    <!-- Initialize Bootstrap components -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });

            // Initialize dropdowns
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });
        });
    </script>
</body>
</html>
