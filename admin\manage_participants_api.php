<?php
// File: admin/manage_participants_api.php
// API untuk mengelola peserta training internal

include '../config/config.php';
include 'security.php';

header('Content-Type: application/json');

// Create participants table if not exists
$create_table_query = "
CREATE TABLE IF NOT EXISTS `offline_training_participants` (
  `id` int NOT NULL AUTO_INCREMENT,
  `offline_training_id` int NOT NULL,
  `karyawan_id` int NOT NULL,
  `nik` varchar(20) NOT NULL,
  `nama` varchar(100) NOT NULL,
  `jabatan` varchar(100) DEFAULT NULL,
  `departemen` varchar(100) DEFAULT NULL,
  `bagian` varchar(100) DEFAULT NULL,
  `status` enum('registered','confirmed','cancelled') NOT NULL DEFAULT 'registered',
  `registered_by` int DEFAULT NULL,
  `registered_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `notes` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_participant` (`offline_training_id`,`karyawan_id`),
  <PERSON>EY `idx_training_id` (`offline_training_id`),
  KEY `idx_karyawan_id` (`karyawan_id`),
  KEY `idx_nik` (`nik`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
";

$conn->query($create_table_query);

$response = ['success' => false, 'message' => '', 'data' => null];

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'get_participants':
                $training_id = $_GET['training_id'] ?? 0;
                
                $query = "SELECT otp.*, k.dept as departemen, k.jabatan, k.bagian
                         FROM offline_training_participants otp
                         LEFT JOIN karyawan k ON otp.karyawan_id = k.id
                         WHERE otp.offline_training_id = ?
                         ORDER BY otp.nama ASC";
                
                $stmt = $conn->prepare($query);
                $stmt->bind_param("i", $training_id);
                $stmt->execute();
                $result = $stmt->get_result();
                
                $participants = [];
                while ($row = $result->fetch_assoc()) {
                    $participants[] = $row;
                }
                
                $response['success'] = true;
                $response['participants'] = $participants;
                break;
                
            case 'get_employees':
                // First try to get employees with active status, if none found, get all
                $query = "SELECT id, nik, nama, dept, jabatan, bagian, status
                         FROM karyawan
                         WHERE LOWER(status) LIKE '%aktif%' OR LOWER(status) LIKE '%active%'
                         ORDER BY nama ASC";

                $result = $conn->query($query);
                $employees = [];

                // If no active employees found, get all employees
                if ($result->num_rows == 0) {
                    $query = "SELECT id, nik, nama, dept, jabatan, bagian, status
                             FROM karyawan
                             ORDER BY nama ASC";
                    $result = $conn->query($query);
                }

                while ($row = $result->fetch_assoc()) {
                    $employees[] = $row;
                }

                $response['success'] = true;
                $response['employees'] = $employees;
                $response['total_count'] = count($employees);
                $response['debug_query'] = $query;
                break;
                
            case 'get_departments':
                $query = "SELECT DISTINCT dept FROM karyawan WHERE dept IS NOT NULL AND dept != '' ORDER BY dept ASC";
                $result = $conn->query($query);
                
                $departments = [];
                while ($row = $result->fetch_assoc()) {
                    $departments[] = $row;
                }
                
                $response['success'] = true;
                $response['departments'] = $departments;
                break;
                
            default:
                $response['message'] = 'Invalid action';
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'add_participants':
                $training_id = $input['training_id'] ?? 0;
                $employees = $input['employees'] ?? [];
                
                if (empty($employees)) {
                    $response['message'] = 'Tidak ada karyawan yang dipilih';
                    break;
                }
                
                $added_count = 0;
                $conn->begin_transaction();
                
                try {
                    foreach ($employees as $employee) {
                        // Get employee details
                        $emp_query = "SELECT * FROM karyawan WHERE id = ?";
                        $emp_stmt = $conn->prepare($emp_query);
                        $emp_stmt->bind_param("i", $employee['id']);
                        $emp_stmt->execute();
                        $emp_result = $emp_stmt->get_result();
                        
                        if ($emp_result->num_rows > 0) {
                            $emp_data = $emp_result->fetch_assoc();
                            
                            // Check if already registered
                            $check_query = "SELECT id FROM offline_training_participants 
                                          WHERE offline_training_id = ? AND karyawan_id = ?";
                            $check_stmt = $conn->prepare($check_query);
                            $check_stmt->bind_param("ii", $training_id, $employee['id']);
                            $check_stmt->execute();
                            $check_result = $check_stmt->get_result();
                            
                            if ($check_result->num_rows == 0) {
                                // Add participant
                                $insert_query = "INSERT INTO offline_training_participants 
                                               (offline_training_id, karyawan_id, nik, nama, jabatan, departemen, bagian, registered_by)
                                               VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                                $insert_stmt = $conn->prepare($insert_query);
                                $insert_stmt->bind_param("iisssssi", 
                                    $training_id, 
                                    $employee['id'], 
                                    $emp_data['nik'], 
                                    $emp_data['nama'],
                                    $emp_data['jabatan'],
                                    $emp_data['dept'],
                                    $emp_data['bagian'],
                                    $_SESSION['user_id']
                                );
                                
                                if ($insert_stmt->execute()) {
                                    $added_count++;
                                }
                            }
                        }
                    }
                    
                    $conn->commit();
                    $response['success'] = true;
                    $response['added_count'] = $added_count;
                    $response['message'] = "Berhasil menambahkan $added_count peserta";
                    
                } catch (Exception $e) {
                    $conn->rollback();
                    $response['message'] = 'Error: ' . $e->getMessage();
                }
                break;
                
            case 'remove_participant':
                $participant_id = $input['participant_id'] ?? 0;
                
                $query = "DELETE FROM offline_training_participants WHERE id = ?";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("i", $participant_id);
                
                if ($stmt->execute()) {
                    $response['success'] = true;
                    $response['message'] = 'Peserta berhasil dihapus';
                } else {
                    $response['message'] = 'Gagal menghapus peserta';
                }
                break;
                
            case 'sync_to_attendance':
                $training_id = $input['training_id'] ?? 0;
                
                // Get all registered participants
                $participants_query = "SELECT * FROM offline_training_participants WHERE offline_training_id = ?";
                $participants_stmt = $conn->prepare($participants_query);
                $participants_stmt->bind_param("i", $training_id);
                $participants_stmt->execute();
                $participants_result = $participants_stmt->get_result();
                
                $synced_count = 0;
                $conn->begin_transaction();
                
                try {
                    while ($participant = $participants_result->fetch_assoc()) {
                        // Check if already in attendance
                        $check_query = "SELECT id FROM offline_training_attendance 
                                      WHERE offline_training_id = ? AND karyawan_id = ?";
                        $check_stmt = $conn->prepare($check_query);
                        $check_stmt->bind_param("ii", $training_id, $participant['karyawan_id']);
                        $check_stmt->execute();
                        $check_result = $check_stmt->get_result();
                        
                        if ($check_result->num_rows == 0) {
                            // Add to attendance with default status
                            $insert_query = "INSERT INTO offline_training_attendance 
                                           (offline_training_id, karyawan_id, nik, nama, status, created_by)
                                           VALUES (?, ?, ?, ?, 'tidak hadir', ?)";
                            $insert_stmt = $conn->prepare($insert_query);
                            $insert_stmt->bind_param("iissi", 
                                $training_id,
                                $participant['karyawan_id'],
                                $participant['nik'],
                                $participant['nama'],
                                $_SESSION['user_id']
                            );
                            
                            if ($insert_stmt->execute()) {
                                $synced_count++;
                            }
                        }
                    }
                    
                    $conn->commit();
                    $response['success'] = true;
                    $response['synced_count'] = $synced_count;
                    $response['message'] = "Berhasil menyinkronkan $synced_count peserta";
                    
                } catch (Exception $e) {
                    $conn->rollback();
                    $response['message'] = 'Error: ' . $e->getMessage();
                }
                break;
                
            default:
                $response['message'] = 'Invalid action';
        }
    }
    
} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
}

echo json_encode($response);
?>
