<?php
// Include constants file
require_once __DIR__ . '/constants.php';

// Database configuration
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'db_training';

// Path configuration
if (!defined('BASE_PATH')) define('BASE_PATH', dirname(__DIR__));
if (!defined('UPLOAD_PATH')) define('UPLOAD_PATH', BASE_PATH . '/uploads');
if (!defined('LOGOS_PATH')) define('LOGOS_PATH', UPLOAD_PATH . '/logos');
if (!defined('UPLOAD_URL')) define('UPLOAD_URL', BASE_URL . 'uploads');
if (!defined('LOGOS_URL')) define('LOGOS_URL', UPLOAD_URL . '/logos');

// Ensure upload directories exist
$directories = [UPLOAD_PATH, LOGOS_PATH];
foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Database connection
try {
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    // Set charset
    $conn->set_charset("utf8mb4");

    // Set default timezone (akan diupdate nanti jika settings tersedia)
    date_default_timezone_set('Asia/Jakarta');

} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    die("Terjadi kesalahan pada sistem. Silakan coba beberapa saat lagi.");
}

// Helper functions
if (!function_exists('get_upload_path')) {
    function get_upload_path($type = '') {
        switch ($type) {
            case 'logos':
                return LOGOS_PATH;
            default:
                return UPLOAD_PATH;
        }
    }
}

if (!function_exists('get_upload_url')) {
    function get_upload_url($type = '') {
        switch ($type) {
            case 'logos':
                return LOGOS_URL;
            default:
                return UPLOAD_URL;
        }
    }
}

// Security functions
if (!function_exists('sanitize_filename')) {
    function sanitize_filename($filename) {
        // Remove any path components
        $filename = basename($filename);

        // Remove special characters
        $filename = preg_replace("/[^a-zA-Z0-9.-]/", "_", $filename);

        // Ensure safe extension
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        if (!in_array($ext, $allowed_extensions)) {
            throw new Exception("File type not allowed");
        }

        return $filename;
    }
}

if (!function_exists('generate_safe_filename')) {
    function generate_safe_filename($original_filename, $prefix = '') {
        $ext = strtolower(pathinfo($original_filename, PATHINFO_EXTENSION));
        return $prefix . uniqid() . '_' . time() . '.' . $ext;
    }
}

// Error handling
if (!function_exists('handle_error')) {
    function handle_error($message, $log = true) {
        if ($log) {
            error_log($message);
        }
        return ['success' => false, 'message' => $message];
    }
}

// Success handling
if (!function_exists('handle_success')) {
    function handle_success($message, $data = []) {
        return array_merge(
            ['success' => true, 'message' => $message],
            $data
        );
    }
}

// Include app settings helper
require_once __DIR__ . '/app_settings.php';

// Include security helper
require_once __DIR__ . '/security_helper.php';

// Update timezone based on settings if available
if (function_exists('get_timezone')) {
    date_default_timezone_set(get_timezone());
}

// Auto-update training status (backup mechanism)
// Hanya jalankan jika belum dijalankan hari ini dan dalam session yang valid
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION['user_id']) && !isset($_SESSION['auto_update_disabled'])) {
    require_once __DIR__ . '/auto_update_helper.php';

    // Cek apakah auto-update sudah dijalankan hari ini
    if (!isAutoUpdateRunToday($conn)) {
        try {
            $results = autoUpdateTrainingStatus($conn);

            // Set flag untuk mencegah multiple run dalam session yang sama
            $_SESSION['last_auto_update'] = date('Y-m-d');

            // Simpan hasil untuk ditampilkan di dashboard jika ada update
            if ($results['total_updated'] > 0) {
                $_SESSION['auto_update_notification'] = [
                    'message' => "{$results['total_updated']} training telah otomatis diupdate statusnya",
                    'details' => $results['details'],
                    'timestamp' => time()
                ];
            }

        } catch (Exception $e) {
            // Log error tapi jangan ganggu aplikasi
            error_log("Auto-update hook error: " . $e->getMessage());
        }
    }
}
