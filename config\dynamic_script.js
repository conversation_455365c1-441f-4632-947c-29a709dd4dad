// Dynamic script to load department, sub department, and position data from database
// Periksa apakah variabel sudah dideklarasikan sebelumnya
if (typeof window.departemenOptions === 'undefined') {
    window.departemenOptions = [];
}
if (typeof window.Bagian === 'undefined') {
    window.Bagian = {};
}
if (typeof window.Jabatan === 'undefined') {
    window.Jabatan = {};
}

// Gunakan variabel global yang sudah ada, jangan deklarasikan ulang
// const departemenOptions = window.departemenOptions; <- Ini yang menyebabkan error

// Load data from API
function loadOrganizationData() {
    // Determine the correct path based on current location
    const currentPath = window.location.pathname;
    let apiPath = 'api/get_organization_data.php?type=all';

    // If we're in a subdirectory, adjust the path
    if (currentPath.includes('/admin/')) {
        apiPath = '../api/get_organization_data.php?type=all';
    } else if (currentPath.includes('/LnD/')) {
        apiPath = '../api/get_organization_data.php?type=all';
    } else if (currentPath.includes('/Dir/') || currentPath.includes('/dept_head/') ||
        currentPath.includes('/pemohon/')) {
        apiPath = '../api/get_organization_data.php?type=all';
    }

    fetch(apiPath)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Process departments
                if (data.data.departments) {
                    // Kosongkan array sebelum mengisi dengan data baru
                    window.departemenOptions.length = 0;

                    // Isi dengan data baru
                    data.data.departments.map(dept => dept.code).forEach(code => {
                        window.departemenOptions.push(code);
                    });

                    // Reset Bagian object
                    Object.keys(window.Bagian).forEach(key => {
                        delete window.Bagian[key];
                    });

                    // Process sub departments
                    if (data.data.sub_departments) {
                        data.data.departments.forEach(dept => {
                            const deptCode = dept.code;
                            const subDepts = data.data.sub_departments
                                .filter(sd => sd.department_id == dept.id)
                                .map(sd => sd.code);

                            window.Bagian[deptCode] = subDepts;
                        });
                    }

                    // Reset Jabatan object
                    Object.keys(window.Jabatan).forEach(key => {
                        delete window.Jabatan[key];
                    });

                    // Process positions
                    if (data.data.positions && data.data.sub_departments) {
                        data.data.sub_departments.forEach(subDept => {
                            const subDeptCode = subDept.code;
                            const positions = data.data.positions
                                .filter(p => p.sub_department_id == subDept.id)
                                .map(p => p.name);

                            window.Jabatan[subDeptCode] = positions;
                        });
                    }

                    // Trigger event to notify that data is loaded
                    const event = new CustomEvent('organizationDataLoaded');
                    document.dispatchEvent(event);
                }
            } else {
                console.error('Error loading organization data:', data.message);
            }
        })
        .catch(error => {
            console.error('Error loading organization data:', error);
        });
}

// Load data when document is ready
document.addEventListener('DOMContentLoaded', loadOrganizationData);

// Functions to update dropdowns (compatible with existing code)
function updateBagian(departemenSelect, bagianSelect) {
    if (!departemenSelect || !bagianSelect) {
        console.log('Elemen select tidak ditemukan');
        return;
    }

    const department = departemenSelect.value;
    bagianSelect.innerHTML = '<option value="">Pilih Sub Dept</option>';

    if (window.Bagian[department]) {
        window.Bagian[department].forEach(bagian => {
            const option = document.createElement("option");
            option.value = bagian;
            option.textContent = bagian;
            bagianSelect.appendChild(option);
        });
    }
}

function updateJabatan(bagianSelect, jabatanSelect) {
    if (!bagianSelect || !jabatanSelect) {
        console.log('Elemen select tidak ditemukan');
        return;
    }

    const bagian = bagianSelect.value;
    jabatanSelect.innerHTML = '<option value="">Pilih Jabatan</option>';

    if (window.Jabatan[bagian]) {
        window.Jabatan[bagian].forEach(jabatan => {
            const option = document.createElement("option");
            option.value = jabatan;
            option.textContent = jabatan;
            jabatanSelect.appendChild(option);
        });
    }
}