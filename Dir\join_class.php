<?php
/**
 * Join Class Page for Pemohon (Applicant)
 * This page allows applicants to join a class using a class code
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';

// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['full_name'] ?? 'Pengguna';

// Initialize variables
$success_message = '';
$error_message = '';
$class_info = null;

// Get class code from various sources
$class_code = '';

// Method 1: From POST form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['class_code'])) {
    $class_code = trim($_POST['class_code']);
}

// Method 2: From GET parameter
if (empty($class_code) && isset($_GET['code'])) {
    $class_code = trim($_GET['code']);
}

// Check if user is already enrolled in the class
if (!empty($class_code)) {
    $check_class_query = "SELECT id FROM training_classes WHERE class_code = '" . $conn->real_escape_string($class_code) . "'";
    $class_result = $conn->query($check_class_query);

    if ($class_result && $class_result->num_rows > 0) {
        $class_data = $class_result->fetch_assoc();
        $class_id = $class_data['id'];

        // Check if user is already a participant
        $check_participant_query = "SELECT id FROM training_participants WHERE class_id = " . $class_id . " AND user_id = " . $user_id;
        $participant_result = $conn->query($check_participant_query);

        if ($participant_result && $participant_result->num_rows > 0) {
            // User is already enrolled, redirect to classroom_detail.php
            header("Location: classroom_detail.php?id=" . $class_id);
            exit();
        }
    }
}

// Process join request
$join_requested = false;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && (isset($_POST['join_class']) || isset($_GET['join']))) {
    $join_requested = true;
}

// Process class code if provided and join requested
if (!empty($class_code) && $join_requested) {
    // SIMPLIFIED APPROACH: Direct query to check if the class exists
    $check_class_query = "SELECT id, title, status FROM training_classes WHERE class_code = '" . $conn->real_escape_string($class_code) . "'";
    $class_result = $conn->query($check_class_query);

    if ($class_result && $class_result->num_rows > 0) {
        $class_data = $class_result->fetch_assoc();
        $class_id = $class_data['id'];
        $class_title = $class_data['title'];

        // Check if class is active
        if ($class_data['status'] != 'active') {
            $error_message = "Kelas ini tidak aktif. Status: " . $class_data['status'];
        } else {
            // Check if user is already a participant (redundant check, can be skipped since we checked above)
            $check_participant_query = "SELECT id FROM training_participants WHERE class_id = " . $class_id . " AND user_id = " . $user_id;
            $participant_result = $conn->query($check_participant_query);

            if ($participant_result && $participant_result->num_rows > 0) {
                $error_message = "Anda sudah terdaftar di kelas ini.";
            } else {
                // Add user as a participant using direct query
                $insert_query = "INSERT INTO training_participants (class_id, user_id, status, joined_at)
                                VALUES (" . $class_id . ", " . $user_id . ", 'active', NOW())";

                if ($conn->query($insert_query)) {
                    $success_message = "Berhasil bergabung dengan kelas: " . $class_title;

                    // Redirect to classroom page after successful join
                    header("Refresh: 3; URL=classroom_detail.php?id=" . $class_id);
                } else {
                    $error_message = "Gagal bergabung dengan kelas: " . $conn->error;
                }
            }
        }
    } else {
        $error_message = "Kode kelas tidak valid atau kelas tidak ditemukan.";
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
:root {
    --primary-color: rgb(157, 0, 0);
    --primary-color-dark: rgb(127, 0, 0);
    --primary-color-light: rgba(157, 0, 0, 0.1);
    --secondary-color: #2c3e50;
    --success-color: #4CAF50;
    --success-color-light: #e8f5e9;
    --warning-color: #ffc107;
    --danger-color: #f44336;
    --info-color: #2196F3;
    --text-dark: #333;
    --text-light: #757575;
    --white: #ffffff;
}

.jarak {
    height: 80px;
}

.join-class-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
}

.join-class-card {
    background-color: var(--white);
    border-radius: 12px;
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.join-class-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--white);
    padding: 30px;
    text-align: center;
}

.join-class-header h1 {
    font-size: 2rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.join-class-header p {
    opacity: 0.9;
    margin-bottom: 0;
}

.join-class-body {
    padding: 30px;
}

.code-input-container {
    margin-bottom: 20px;
}

.code-input {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.code-input input {
    text-align: center;
    font-size: 1.5rem;
    letter-spacing: 5px;
    text-transform: uppercase;
    font-weight: 600;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    width: 100%;
    max-width: 300px;
    transition: all 0.3s ease;
}

.code-input input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(157, 0, 0, 0.2);
}

.code-help {
    text-align: center;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.class-preview {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid var(--primary-color);
}

.class-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.class-topic {
    color: var(--text-dark);
    margin-bottom: 10px;
}

.class-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
}

.class-detail {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.join-button {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.join-button:hover {
    background-color: var(--primary-color-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.join-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.back-link {
    display: block;
    text-align: center;
    margin-top: 20px;
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.3s ease;
}

.back-link:hover {
    color: var(--primary-color);
}
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="join-class-container">
    <div class="join-class-card">
        <div class="join-class-header">
            <h1>Gabung Kelas</h1>
            <p>Masukkan kode kelas untuk bergabung dengan kelas training</p>
        </div>

        <div class="join-class-body">
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <form method="post" action="">
                <div class="code-input-container">
                    <div class="code-input">
                        <input type="text" name="class_code" id="class_code" placeholder="XXXXXX" maxlength="10" value="<?= htmlspecialchars($class_code) ?>" required>
                    </div>
                    <div class="code-help">
                        <p>Kode kelas terdiri dari 6 karakter yang diberikan oleh trainer</p>
                    </div>
                </div>

                <?php
                // Get class info for display if code is provided
                $preview_class = null;
                if (!empty($class_code)) {
                    $preview_query = "SELECT c.*, t.training_topic, t.training_type
                                    FROM training_classes c
                                    LEFT JOIN training_submissions t ON c.training_id = t.id
                                    WHERE c.class_code = '" . $conn->real_escape_string($class_code) . "'";
                    $preview_result = $conn->query($preview_query);

                    if ($preview_result && $preview_result->num_rows > 0) {
                        $preview_class = $preview_result->fetch_assoc();
                        $class_info = $preview_class; // Set class_info for status display
                ?>
                    <div class="class-preview">
                        <div class="class-title"><?= htmlspecialchars($preview_class['title']) ?></div>
                        <?php if (!empty($preview_class['training_topic'])): ?>
                        <div class="class-topic"><?= htmlspecialchars($preview_class['training_topic']) ?></div>
                        <?php endif; ?>

                        <div class="class-details">
                            <?php if (!empty($preview_class['training_type'])): ?>
                            <div class="class-detail">
                                <i class="fas fa-tag"></i>
                                <span><?= htmlspecialchars($preview_class['training_type']) ?></span>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($preview_class['start_date'])): ?>
                            <div class="class-detail">
                                <i class="fas fa-calendar-alt"></i>
                                <span>Mulai: <?= date('d M Y', strtotime($preview_class['start_date'])) ?></span>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($preview_class['end_date'])): ?>
                            <div class="class-detail">
                                <i class="fas fa-calendar-check"></i>
                                <span>Selesai: <?= date('d M Y', strtotime($preview_class['end_date'])) ?></span>
                            </div>
                            <?php endif; ?>

                            <div class="class-detail">
                                <i class="fas fa-info-circle"></i>
                                <span>Status:
                                    <span class="badge <?= $preview_class['status'] == 'active' ? 'bg-success' : ($preview_class['status'] == 'completed' ? 'bg-info' : 'bg-danger') ?>">
                                        <?= $preview_class['status'] == 'active' ? 'Aktif' : ($preview_class['status'] == 'completed' ? 'Selesai' : 'Tidak Aktif') ?>
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                <?php
                    }
                }
                ?>

                <button type="submit" name="join_class" class="join-button">
                    <i class="fas fa-sign-in-alt"></i> Gabung Kelas
                </button>

            </form>

            <!-- Tombol navigasi -->
            <div class="mt-4 text-center">
                <a href="classroom.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Classroom
                </a>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);

        // Auto-uppercase class code input
        const classCodeInput = document.getElementById('class_code');
        if (classCodeInput) {
            classCodeInput.addEventListener('input', function() {
                this.value = this.value.toUpperCase();
            });
        }
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
