<?php
require_once 'config.php';
// session_start() sudah dipanggil di config.php

// Enable error logging for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    error_log("Download memo: Access denied - User not logged in");
    http_response_code(403);
    die('Access denied');
}

// Get file path from parameter
$file_path = $_GET['file'] ?? '';
error_log("Download memo: Requested file path: " . $file_path);

if (empty($file_path)) {
    error_log("Download memo: Empty file path");
    http_response_code(400);
    die('File parameter is required');
}

// Security: Ensure the file path is within uploads directory
$file_path = ltrim($file_path, '/');

// Handle case-insensitive "uploads" directory
if (stripos($file_path, 'uploads/') === 0) {
    // If it starts with "uploads/" (case-insensitive), normalize to lowercase
    $file_path = 'uploads/' . substr($file_path, 8);
} elseif (strpos($file_path, 'uploads/') !== 0) {
    // If it doesn't start with "uploads/", prepend it
    $file_path = 'uploads/' . $file_path;
}
error_log("Download memo: Processed file path: " . $file_path);

// Build full file path
$full_path = dirname(__DIR__) . '/' . $file_path;
error_log("Download memo: Full file path: " . $full_path);

// Security: Prevent directory traversal
$real_path = realpath($full_path);
$uploads_path = realpath(dirname(__DIR__) . '/uploads');
error_log("Download memo: Real path: " . ($real_path ?: 'NULL'));
error_log("Download memo: Uploads path: " . ($uploads_path ?: 'NULL'));

if (!$real_path || strpos($real_path, $uploads_path) !== 0) {
    error_log("Download memo: Security check failed - Invalid file path");
    http_response_code(403);
    die('Access denied - Invalid file path');
}

// Check if file exists
if (!file_exists($real_path)) {
    error_log("Download memo: File not found: " . $real_path);
    http_response_code(404);
    die('File not found: ' . htmlspecialchars($file_path));
}

error_log("Download memo: File found successfully: " . $real_path);

// Get file info
$file_info = pathinfo($real_path);
$file_extension = strtolower($file_info['extension']);
$file_name = $file_info['basename'];

// Set appropriate MIME type
$mime_types = [
    'pdf' => 'application/pdf',
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'bmp' => 'image/bmp',
    'webp' => 'image/webp',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls' => 'application/vnd.ms-excel',
    'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'txt' => 'text/plain'
];

$mime_type = $mime_types[$file_extension] ?? 'application/octet-stream';

// Check if this is a download request or view request
$action = $_GET['action'] ?? 'download';

if ($action === 'view' && in_array($file_extension, ['pdf', 'jpg', 'jpeg', 'png', 'gif'])) {
    // For viewing - set appropriate headers for inline display
    header('Content-Type: ' . $mime_type);
    header('Content-Disposition: inline; filename="' . $file_name . '"');
} else {
    // For downloading - force download
    header('Content-Type: ' . $mime_type);
    header('Content-Disposition: attachment; filename="' . $file_name . '"');
}

// Set additional headers
header('Content-Length: ' . filesize($real_path));
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

// Output file
readfile($real_path);
exit;
?>
