<?php
// Database configuration
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'db_training';
$backup_file = 'backups/db_db_training_backup_2025-05-23_17-01-36.sql';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
// Increase memory limit and execution time
ini_set('memory_limit', '512M');
set_time_limit(300); // 5 minutes

echo "<h1>Import Database</h1>";

// Check if backup file exists
if (!file_exists($backup_file)) {
    die("Error: Backup file not found: $backup_file");
}

echo "<p>Backup file found: $backup_file</p>";
echo "<p>File size: " . round(filesize($backup_file) / 1024, 2) . " KB</p>";

// Connect to MySQL
$conn = new mysqli($db_host, $db_user, $db_pass);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<p>Connected to MySQL server successfully.</p>";

// Drop database if exists
$conn->query("DROP DATABASE IF EXISTS `$db_name`");
echo "<p>Dropped existing database if it existed.</p>";

// Create database
if (!$conn->query("CREATE DATABASE `$db_name`")) {
    die("Error creating database: " . $conn->error);
}

echo "<p>Created new database: $db_name</p>";

// Select database
$conn->select_db($db_name);

// Disable foreign key checks
$conn->query("SET FOREIGN_KEY_CHECKS=0");
echo "<p>Disabled foreign key checks.</p>";

// Use the mysql command line tool instead of parsing the SQL file
$command = '';
if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
    // Windows
    $command = "mysql -h {$db_host} -u {$db_user} " . ($db_pass ? "-p{$db_pass}" : "") . " {$db_name} < \"{$backup_file}\" 2>&1";
    // Use cmd.exe to handle redirection
    $command = "cmd /c \"{$command}\"";
} else {
    // Linux/Unix/Mac
    $command = "mysql -h {$db_host} -u {$db_user} " . ($db_pass ? "-p{$db_pass}" : "") . " {$db_name} < \"{$backup_file}\" 2>&1";
}

echo "<p>Executing command: " . htmlspecialchars($command) . "</p>";

// Execute the command
$output = [];
$return_var = 0;
exec($command, $output, $return_var);

echo "<hr>";
echo "<h2>Import Results</h2>";

if ($return_var === 0) {
    echo "<p style='color:green'><strong>Database import completed successfully!</strong></p>";
} else {
    echo "<p style='color:red'><strong>Database import failed with error code: {$return_var}</strong></p>";
    echo "<h3>Error Output:</h3>";
    echo "<pre style='background-color: #f8f8f8; padding: 10px; border-left: 4px solid #f44336; max-height: 300px; overflow: auto;'>";
    echo htmlspecialchars(implode("\n", $output));
    echo "</pre>";

    // Try alternative approach if the command line approach fails
    echo "<h3>Trying alternative approach...</h3>";

    // Reset database
    $conn->query("DROP DATABASE IF EXISTS `$db_name`");
    $conn->query("CREATE DATABASE `$db_name`");
    $conn->select_db($db_name);
    $conn->query("SET FOREIGN_KEY_CHECKS=0");

    // Read the file line by line and execute SQL statements
    $handle = fopen($backup_file, 'r');
    if ($handle) {
        $query = '';
        $executed_queries = 0;
        $failed_queries = 0;
        $line_number = 0;

        while (($line = fgets($handle)) !== false) {
            $line_number++;
            $line = trim($line);

            // Skip comments and empty lines
            if (empty($line) || strpos($line, '--') === 0 || strpos($line, '/*') === 0) {
                continue;
            }

            $query .= $line;

            // If the line ends with a semicolon, execute the query
            if (substr($line, -1) === ';') {
                try {
                    if ($conn->query($query)) {
                        $executed_queries++;
                    } else {
                        $failed_queries++;
                        echo "<p style='color:red'>Error at line {$line_number}: " . $conn->error . "</p>";
                    }
                } catch (Exception $e) {
                    $failed_queries++;
                    echo "<p style='color:red'>Exception at line {$line_number}: " . $e->getMessage() . "</p>";
                }

                $query = '';
            }
        }

        fclose($handle);

        echo "<p>Executed queries: {$executed_queries}</p>";
        echo "<p>Failed queries: {$failed_queries}</p>";

        if ($failed_queries === 0) {
            echo "<p style='color:green'><strong>Alternative import completed successfully!</strong></p>";
        } else {
            echo "<p style='color:red'><strong>Alternative import completed with errors.</strong></p>";
        }
    } else {
        echo "<p style='color:red'>Could not open file for reading.</p>";
    }
}

// Enable foreign key checks
$conn->query("SET FOREIGN_KEY_CHECKS=1");
echo "<p>Enabled foreign key checks.</p>";

// Close connection
$conn->close();

echo "<p><a href='setupdb.php'>Return to Setup Page</a></p>";
?>
