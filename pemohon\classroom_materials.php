<?php
/**
 * Classroom Materials Page for Pemohon (Applicant)
 * This page displays the materials for a specific training class
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';
// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['full_name'] ?? 'Pengguna';

// Check if class ID is provided
$class_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($class_id <= 0) {
    header('Location: classroom.php');
    exit();
}

// Get class information
$class_query = "SELECT c.*, t.training_topic, t.training_type 
               FROM training_classes c
               JOIN training_submissions t ON c.training_id = t.id
               WHERE c.id = ?";
$stmt = $conn->prepare($class_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$class = $result->fetch_assoc();
$stmt->close();

if (!$class) {
    header('Location: classroom.php');
    exit();
}

// Check if user is a participant in this class
$participant_query = "SELECT * FROM training_participants 
                     WHERE class_id = ? AND user_id = ?";
$stmt = $conn->prepare($participant_query);
$stmt->bind_param("ii", $class_id, $user_id);
$stmt->execute();
$participant_result = $stmt->get_result();
$is_participant = $participant_result->num_rows > 0;
$participant = $participant_result->fetch_assoc();
$stmt->close();

if (!$is_participant) {
    header('Location: classroom.php');
    exit();
}

// Get materials
$materials = [];
$materials_query = "SELECT * FROM training_materials 
                   WHERE class_id = ? AND is_published = 1
                   ORDER BY order_number ASC, created_at ASC";
$stmt = $conn->prepare($materials_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $materials[] = $row;
}
$stmt->close();

// Group materials by type
$document_materials = array_filter($materials, function($m) { return $m['type'] == 'document'; });
$video_materials = array_filter($materials, function($m) { return $m['type'] == 'video'; });
$link_materials = array_filter($materials, function($m) { return $m['type'] == 'link'; });
$other_materials = array_filter($materials, function($m) { return $m['type'] == 'other'; });

// Add error message handling
$error_message = '';
if (isset($_GET['error'])) {
    $error_message = htmlspecialchars($_GET['error']);
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .materials-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
        color: var(--white);
        padding: var(--spacing-xl) var(--spacing-lg);
        border-radius: var(--border-radius-lg);
        margin-bottom: var(--spacing-xl);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-lg);
    }
    
    .materials-title {
        font-size: 2.5rem;
        margin-bottom: var(--spacing-md);
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    .materials-meta {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: var(--spacing-lg);
    }
    
    .material-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .material-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .material-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }
    
    .material-title {
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .material-type {
        font-size: 0.85rem;
        color: #6c757d;
    }
    
    .material-description {
        color: #333;
        margin-bottom: 10px;
    }
    
    .material-section {
        margin-bottom: 30px;
    }
    
    .material-section-title {
        font-size: 1.5rem;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
        color: var(--primary-color);
    }
    
    .empty-state {
        text-align: center;
        padding: 30px;
        background-color: #f8f9fa;
        border-radius: 8px;
    }
    
    .empty-state .icon {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 15px;
    }
    
    .p {
        color: gray;
    }
   .empty-state .message {
        font-size: 1.2rem;
        color: #6c757d;
        margin-bottom: 15px;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="welcome-section">
                <h1><i class="fas fa-book"></i> Materi Pembelajaran</h1>
                <div>
                    <a href="classroom_detail.php?id=<?= $class_id ?>" class="btn btn-outline-primary me-2">
                        <i class="fas fa-chalkboard"></i> Kembali ke Detail Kelas
                    </a>
                    <a href="classroom.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali ke Daftar Kelas
                    </a>
                </div>
            </div>
            
            <?php if (!empty($error_message)): ?>
<div class="alert alert-danger" role="alert">
    <?php echo $error_message; ?>
</div>
<?php endif; ?>

<div class="materials-header">
                <div class="materials-title"><?= htmlspecialchars($class['title']) ?></div>
                <div class="materials-meta">
                    <div><strong>Topik:</strong> <?= htmlspecialchars($class['training_topic']) ?></div>
                    <div><strong>Tipe:</strong> <?= htmlspecialchars($class['training_type']) ?></div>
                    <?php if (!empty($class['start_date'])): ?>
                        <div><strong>Tanggal Mulai:</strong> <?= date('d M Y', strtotime($class['start_date'])) ?></div>
                    <?php endif; ?>
                    <?php if (!empty($class['end_date'])): ?>
                        <div><strong>Tanggal Selesai:</strong> <?= date('d M Y', strtotime($class['end_date'])) ?></div>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if (empty($materials)): ?>
                <div class="empty-state">
                    <div class="icon"><i class="fas fa-book"></i></div>
                    <div class="message">Belum ada materi yang tersedia untuk kelas ini.</div>
                    <p>Silakan periksa kembali nanti atau hubungi instruktur kelas.</p>
                </div>
            <?php else: ?>
                <?php if (!empty($document_materials)): ?>
                    <div class="material-section">
                        <h2 class="material-section-title"><i class="fas fa-file-alt"></i> Dokumen</h2>
                        <?php foreach ($document_materials as $material): ?>
                            <div class="material-card">
                                <div class="material-header">
                                    <div>
                                        <div class="material-title"><?= htmlspecialchars($material['title']) ?></div>
                                        <div class="material-type">
                                            <i class="fas fa-file-alt"></i> Dokumen
                                        </div>
                                    </div>
                                </div>
                                <div class="material-description">
                                    <?= !empty($material['description']) ? nl2br(htmlspecialchars($material['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <?php if (!empty($material['file_path'])): ?>
                                        <a href="download_handler.php?file=<?= basename($material['file_path']) ?>&class_id=<?= $class_id ?>" class="btn btn-primary">
                                            <i class="fas fa-download"></i> Unduh File
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($video_materials)): ?>
                    <div class="material-section">
                        <h2 class="material-section-title"><i class="fas fa-video"></i> Video</h2>
                        <?php foreach ($video_materials as $material): ?>
                            <div class="material-card">
                                <div class="material-header">
                                    <div>
                                        <div class="material-title"><?= htmlspecialchars($material['title']) ?></div>
                                        <div class="material-type">
                                            <i class="fas fa-video"></i> Video
                                        </div>
                                    </div>
                                </div>
                                <div class="material-description">
                                    <?= !empty($material['description']) ? nl2br(htmlspecialchars($material['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <?php if (!empty($material['external_url'])): ?>
                                        <a href="<?= htmlspecialchars($material['external_url']) ?>" class="btn btn-primary" target="_blank">
                                            <i class="fas fa-play"></i> Tonton Video
                                        </a>
                                    <?php elseif (!empty($material['file_path'])): ?>
                                        <a href="download_handler.php?file=<?= basename($material['file_path']) ?>&class_id=<?= $class_id ?>" class="btn btn-primary">
                                            <i class="fas fa-play"></i> Tonton Video
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($link_materials)): ?>
                    <div class="material-section">
                        <h2 class="material-section-title"><i class="fas fa-link"></i> Tautan</h2>
                        <?php foreach ($link_materials as $material): ?>
                            <div class="material-card">
                                <div class="material-header">
                                    <div>
                                        <div class="material-title"><?= htmlspecialchars($material['title']) ?></div>
                                        <div class="material-type">
                                            <i class="fas fa-link"></i> Tautan
                                        </div>
                                    </div>
                                </div>
                                <div class="material-description">
                                    <?= !empty($material['description']) ? nl2br(htmlspecialchars($material['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <?php if (!empty($material['external_url'])): ?>
                                        <a href="<?= htmlspecialchars($material['external_url']) ?>" class="btn btn-primary" target="_blank">
                                            <i class="fas fa-external-link-alt"></i> Buka Tautan
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($other_materials)): ?>
                    <div class="material-section">
                        <h2 class="material-section-title"><i class="fas fa-file"></i> Materi Lainnya</h2>
                        <?php foreach ($other_materials as $material): ?>
                            <div class="material-card">
                                <div class="material-header">
                                    <div>
                                        <div class="material-title"><?= htmlspecialchars($material['title']) ?></div>
                                        <div class="material-type">
                                            <i class="fas fa-file"></i> Lainnya
                                        </div>
                                    </div>
                                </div>
                                <div class="material-description">
                                    <?= !empty($material['description']) ? nl2br(htmlspecialchars($material['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <?php if (!empty($material['file_path'])): ?>
                                        <a href="download_handler.php?file=<?= basename($material['file_path']) ?>&class_id=<?= $class_id ?>" class="btn btn-primary">
                                            <i class="fas fa-download"></i> Unduh File
                                        </a>
                                    <?php elseif (!empty($material['external_url'])): ?>
                                        <a href="<?= htmlspecialchars($material['external_url']) ?>" class="btn btn-primary" target="_blank">
                                            <i class="fas fa-external-link-alt"></i> Buka Tautan
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Animation for material cards
        const materialCards = document.querySelectorAll('.material-card');
        materialCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100 * index);
        });
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
