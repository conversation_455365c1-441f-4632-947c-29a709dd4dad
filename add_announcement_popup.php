<?php
/**
 * Script untuk menambahkan fitur popup pengumuman ke semua dashboard user
 */

echo "🖼️ ADDING ANNOUNCEMENT POPUP TO ALL USER DASHBOARDS\n";
echo "===================================================\n\n";

$dashboard_files = [
    'LnD/index.php',
    'Dir/index.php', 
    'dept_head/index.php'
];

// Template untuk image dengan data attributes
$old_image_template = '<img src="../<?= htmlspecialchars($announcement[\'image_path\']) ?>" 
                                                 alt="<?= htmlspecialchars($announcement[\'title\']) ?>" 
                                                 class="img-fluid rounded"
                                                 style="max-width: 100%; height: auto; margin: 10px 0;">';

$new_image_template = '<img src="../<?= htmlspecialchars($announcement[\'image_path\']) ?>" 
                                                 alt="<?= htmlspecialchars($announcement[\'title\']) ?>" 
                                                 class="img-fluid rounded announcement-img-hover"
                                                 style="max-width: 100%; height: auto; margin: 10px 0; cursor: pointer;"
                                                 data-announcement-id="<?= $announcement[\'id\'] ?>"
                                                 data-title="<?= htmlspecialchars($announcement[\'title\']) ?>"
                                                 data-content="<?= htmlspecialchars($announcement[\'content\']) ?>"
                                                 data-creator="<?= htmlspecialchars($announcement[\'creator_name\'] ?? \'Admin\') ?>"
                                                 data-date="<?= date(\'d M Y\', strtotime($announcement[\'created_at\'])) ?>"
                                                 data-image="../<?= htmlspecialchars($announcement[\'image_path\']) ?>"
                                                 <?php if (!empty($announcement[\'link_url\'])): ?>
                                                 data-link-url="<?= htmlspecialchars($announcement[\'link_url\']) ?>"
                                                 data-link-text="<?= htmlspecialchars($announcement[\'link_text\'] ?? \'Buka Link\') ?>"
                                                 <?php endif; ?>
                                                 <?php if (!empty($announcement[\'expiry_date\'])): ?>
                                                 data-expiry="<?= date(\'d M Y\', strtotime($announcement[\'expiry_date\'])) ?>"
                                                 <?php endif; ?>>';

// CSS untuk announcement modal
$announcement_modal_css = '
/* Announcement Modal Specific Styles */
.announcement-modal-content {
    max-width: 800px;
    width: 95%;
}

.announcement-img-hover {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.announcement-img-hover:hover {
    transform: scale(1.02);
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(191, 0, 0, 0.2);
}

.announcement-modal .modal-body {
    padding: 20px;
}

.announcement-modal-image {
    text-align: center;
    margin-bottom: 20px;
}

.announcement-modal-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.announcement-modal-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    font-size: 0.9rem;
    color: #6c757d;
}

.announcement-modal-content-text {
    line-height: 1.6;
    margin-bottom: 20px;
    color: #333;
}

.announcement-modal-link {
    text-align: center;
    margin-top: 20px;
}

.announcement-modal-expiry {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
    margin-top: 15px;
    text-align: center;
}

/* Responsive adjustments for announcement modal */
@media (max-width: 768px) {
    .announcement-modal-content {
        width: 98%;
        margin: 2% auto;
    }
    
    .announcement-modal-meta {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}';

// Modal HTML
$announcement_modal_html = '
<!-- Modal untuk detail pengumuman -->
<div id="announcementModal" class="modal announcement-modal">
    <div class="modal-content announcement-modal-content">
        <div class="modal-header">
            <h3 class="modal-title" id="announcementModalTitle">Detail Pengumuman</h3>
            <button class="modal-close" id="announcementModalClose">&times;</button>
        </div>
        <div class="modal-body" id="announcementModalBody">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>';

// JavaScript function
$announcement_js_function = '
    /**
     * Initialize announcement image popup functionality
     */
    function initializeAnnouncementPopup() {
        const announcementImages = document.querySelectorAll(\'.announcement-img-hover\');
        const modal = document.getElementById(\'announcementModal\');
        const modalTitle = document.getElementById(\'announcementModalTitle\');
        const modalBody = document.getElementById(\'announcementModalBody\');
        const modalClose = document.getElementById(\'announcementModalClose\');

        if (!modal || !modalTitle || !modalBody || !modalClose) {
            console.warn(\'Announcement modal elements not found\');
            return;
        }

        // Add click event to each announcement image
        announcementImages.forEach(img => {
            img.addEventListener(\'click\', function() {
                showAnnouncementModal(this);
            });

            // Add hover effect
            img.addEventListener(\'mouseenter\', function() {
                this.style.transform = \'scale(1.02)\';
            });

            img.addEventListener(\'mouseleave\', function() {
                this.style.transform = \'scale(1)\';
            });
        });

        // Close modal events
        modalClose.addEventListener(\'click\', function() {
            closeAnnouncementModal();
        });

        modal.addEventListener(\'click\', function(e) {
            if (e.target === modal) {
                closeAnnouncementModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener(\'keydown\', function(e) {
            if (e.key === \'Escape\' && modal.style.display === \'block\') {
                closeAnnouncementModal();
            }
        });

        /**
         * Show announcement modal with data from image
         */
        function showAnnouncementModal(imgElement) {
            const title = imgElement.getAttribute(\'data-title\');
            const content = imgElement.getAttribute(\'data-content\');
            const creator = imgElement.getAttribute(\'data-creator\');
            const date = imgElement.getAttribute(\'data-date\');
            const imageSrc = imgElement.getAttribute(\'data-image\');
            const linkUrl = imgElement.getAttribute(\'data-link-url\');
            const linkText = imgElement.getAttribute(\'data-link-text\');
            const expiry = imgElement.getAttribute(\'data-expiry\');

            // Set modal title
            modalTitle.textContent = title;

            // Build modal content
            let modalContent = \'\';

            // Meta information
            modalContent += `
                <div class="announcement-modal-meta">
                    <span><i class="fas fa-user"></i> ${creator}</span>
                    <span><i class="fas fa-calendar-alt"></i> ${date}</span>
                </div>
            `;

            // Image
            if (imageSrc) {
                modalContent += `
                    <div class="announcement-modal-image">
                        <img src="${imageSrc}" alt="${title}" class="img-fluid">
                    </div>
                `;
            }

            // Content
            modalContent += `
                <div class="announcement-modal-content-text">
                    ${content.replace(/\\n/g, \'<br>\')}
                </div>
            `;

            // Link
            if (linkUrl && linkText) {
                modalContent += `
                    <div class="announcement-modal-link">
                        <a href="${linkUrl}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt"></i> ${linkText}
                        </a>
                    </div>
                `;
            }

            // Expiry date
            if (expiry) {
                modalContent += `
                    <div class="announcement-modal-expiry">
                        <i class="fas fa-clock"></i> Berlaku sampai: ${expiry}
                    </div>
                `;
            }

            modalBody.innerHTML = modalContent;

            // Show modal
            modal.style.display = \'block\';
            document.body.style.overflow = \'hidden\';

            // Add animation
            setTimeout(() => {
                modal.querySelector(\'.modal-content\').style.animation = \'slideIn 0.3s ease\';
            }, 10);
        }

        /**
         * Close announcement modal
         */
        function closeAnnouncementModal() {
            modal.style.display = \'none\';
            document.body.style.overflow = \'auto\';
        }
    }';

foreach ($dashboard_files as $file) {
    echo "📄 Processing: $file\n";
    
    if (!file_exists($file)) {
        echo "   ❌ File not found: $file\n";
        continue;
    }
    
    $content = file_get_contents($file);
    $updated = false;
    
    // 1. Update image template
    if (strpos($content, 'announcement-img-hover') === false && strpos($content, $old_image_template) !== false) {
        $content = str_replace($old_image_template, $new_image_template, $content);
        echo "   ✅ Updated image template\n";
        $updated = true;
    }
    
    // 2. Add CSS if not exists
    if (strpos($content, 'announcement-modal-content') === false) {
        // Find a good place to insert CSS (after existing modal styles)
        $css_insert_pos = strpos($content, '@keyframes slideIn');
        if ($css_insert_pos !== false) {
            $css_insert_pos = strpos($content, '}', $css_insert_pos) + 1;
            $content = substr_replace($content, "\n" . $announcement_modal_css, $css_insert_pos, 0);
            echo "   ✅ Added announcement modal CSS\n";
            $updated = true;
        }
    }
    
    // 3. Add modal HTML if not exists
    if (strpos($content, 'announcementModal') === false) {
        // Find training modal and add after it
        $modal_insert_pos = strpos($content, '</div>');
        if ($modal_insert_pos !== false) {
            // Find the training modal closing div
            $training_modal_pos = strpos($content, 'id="trainingModal"');
            if ($training_modal_pos !== false) {
                // Find the closing div of training modal
                $modal_end_pos = strpos($content, '</div>', $training_modal_pos);
                $modal_end_pos = strpos($content, '</div>', $modal_end_pos + 1); // Get the outer closing div
                $content = substr_replace($content, $announcement_modal_html, $modal_end_pos + 6, 0);
                echo "   ✅ Added announcement modal HTML\n";
                $updated = true;
            }
        }
    }
    
    // 4. Add JavaScript function if not exists
    if (strpos($content, 'initializeAnnouncementPopup') === false) {
        // Find DOMContentLoaded and add the initialization
        $dom_ready_pos = strpos($content, 'document.addEventListener(\'DOMContentLoaded\'');
        if ($dom_ready_pos !== false) {
            // Find the end of the function and add our initialization
            $function_start = strpos($content, 'function() {', $dom_ready_pos);
            if ($function_start !== false) {
                $function_start += 12; // After 'function() {'
                $content = substr_replace($content, "\n    // Initialize announcement image hover popup\n    initializeAnnouncementPopup();\n", $function_start, 0);
                echo "   ✅ Added popup initialization\n";
                $updated = true;
            }
            
            // Add the function before closing script
            $script_end_pos = strrpos($content, '});');
            if ($script_end_pos !== false) {
                $content = substr_replace($content, $announcement_js_function . "\n\n", $script_end_pos, 0);
                echo "   ✅ Added announcement popup function\n";
                $updated = true;
            }
        }
    }
    
    if ($updated) {
        if (file_put_contents($file, $content)) {
            echo "   ✅ File updated successfully\n";
        } else {
            echo "   ❌ Failed to write file\n";
        }
    } else {
        echo "   ⚠️  No updates needed or already updated\n";
    }
    
    echo "\n";
}

echo "📊 SUMMARY:\n";
echo "===========\n";
echo "✅ Updated image templates with data attributes\n";
echo "✅ Added announcement modal CSS styling\n";
echo "✅ Added announcement modal HTML structure\n";
echo "✅ Added JavaScript popup functionality\n";

echo "\n🎯 FEATURES ADDED:\n";
echo "==================\n";
echo "1. 🖼️ **Clickable Images:** Images now have hover effects and are clickable\n";
echo "2. 📱 **Responsive Modal:** Popup modal adapts to different screen sizes\n";
echo "3. 🎨 **Enhanced UI:** Smooth animations and professional styling\n";
echo "4. 🔗 **Full Content:** Modal shows complete announcement with links\n";
echo "5. ⌨️ **Keyboard Support:** ESC key closes modal\n";

echo "\n🚀 ANNOUNCEMENT POPUP FEATURE ADDED TO ALL DASHBOARDS!\n";
?>
