<?php
/**
 * API untuk export kalender training ke Excel
 */

require_once '../../config/config.php';
require_once '../security.php';

// Check if user is admin (same validation as security.php)
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role_id'], [2, 3, 4, 5, 99])) {
    http_response_code(401);
    die('Unauthorized - Admin access required');
}

try {
    // Get date range (default: current month)
    $start_date = $_GET['start'] ?? date('Y-m-01');
    $end_date = $_GET['end'] ?? date('Y-m-t');

    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="Kalender_Training_' . date('Y-m-d') . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');

    // Start HTML table for Excel
    echo '<html>';
    echo '<head>';
    echo '<meta charset="UTF-8">';
    echo '<style>';
    echo 'table { border-collapse: collapse; width: 100%; }';
    echo 'th, td { border: 1px solid #000; padding: 8px; text-align: left; }';
    echo 'th { background-color: #9d0000; color: white; font-weight: bold; }';
    echo '.offline { background-color: #d4edda; }';
    echo '.online { background-color: #cce7ff; }';
    echo '</style>';
    echo '</head>';
    echo '<body>';

    echo '<h2>Kalender Training</h2>';
    echo '<p>Periode: ' . date('d M Y', strtotime($start_date)) . ' - ' . date('d M Y', strtotime($end_date)) . '</p>';
    echo '<p>Diekspor pada: ' . date('d M Y H:i:s') . '</p>';

    // Get all training events
    $events = [];

    // Offline training
    $offline_query = "SELECT
                        'Offline' as jenis,
                        training_topic as judul,
                        training_description as deskripsi,
                        start_date as tanggal,
                        end_date,
                        is_confirmed,
                        CONCAT(COALESCE(training_time_start, ''),
                               CASE WHEN training_time_start IS NOT NULL AND training_time_end IS NOT NULL
                                    THEN ' - ' ELSE '' END,
                               COALESCE(training_time_end, '')) as waktu,
                        location as lokasi,
                        trainer_name as trainer,
                        max_participants as max_peserta,
                        COALESCE(status, 'Active') as status
                      FROM offline_training
                      WHERE start_date BETWEEN ? AND ?
                      ORDER BY start_date";

    $stmt = $conn->prepare($offline_query);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $events[] = $row;
    }
    $stmt->close();

    // Online training
    $online_query = "SELECT
                        'Online' as jenis,
                        training_topic as judul,
                        additional_info as deskripsi,
                        start_date as tanggal,
                        end_date,
                        is_confirmed,
                        CONCAT(COALESCE(training_time_start, ''),
                               CASE WHEN training_time_start IS NOT NULL AND training_time_end IS NOT NULL
                                    THEN ' - ' ELSE '' END,
                               COALESCE(training_time_end, '')) as waktu,
                        COALESCE(training_place, 'Online') as lokasi,
                        contact_person as trainer,
                        NULL as max_peserta,
                        status
                     FROM training_submissions
                     WHERE start_date BETWEEN ? AND ?
                     AND status = 'Approved'
                     ORDER BY start_date";

    $stmt = $conn->prepare($online_query);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $events[] = $row;
    }
    $stmt->close();

    // Sort by date
    usort($events, function($a, $b) {
        return strcmp($a['tanggal'], $b['tanggal']);
    });

    // Create table
    echo '<table>';
    echo '<thead>';
    echo '<tr>';
    echo '<th>No</th>';
    echo '<th>Jenis</th>';
    echo '<th>Judul Training</th>';
    echo '<th>Tanggal</th>';
    echo '<th>Waktu</th>';
    echo '<th>Lokasi</th>';
    echo '<th>Trainer</th>';
    echo '<th>Max Peserta</th>';
    echo '<th>Status</th>';
    echo '<th>Deskripsi</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    if (empty($events)) {
        echo '<tr>';
        echo '<td colspan="10" style="text-align: center;">Tidak ada training dalam periode ini</td>';
        echo '</tr>';
    } else {
        $no = 1;
        foreach ($events as $event) {
            $class = strtolower($event['jenis']);
            echo '<tr class="' . $class . '">';
            echo '<td>' . $no++ . '</td>';
            echo '<td>' . htmlspecialchars($event['jenis']) . '</td>';
            echo '<td>' . htmlspecialchars($event['judul']) . '</td>';
            echo '<td>' . date('d M Y', strtotime($event['tanggal'])) . ($event['end_date'] && $event['end_date'] != $event['tanggal'] ? ' - ' . date('d M Y', strtotime($event['end_date'])) : '') . '</td>';
            echo '<td>' . htmlspecialchars($event['waktu'] ?: '-') . '</td>';
            echo '<td>' . htmlspecialchars($event['lokasi'] ?: '-') . '</td>';
            echo '<td>' . htmlspecialchars($event['trainer'] ?: '-') . '</td>';
            echo '<td>' . ($event['max_peserta'] ?: '-') . '</td>';
            echo '<td>' . htmlspecialchars($event['status']) . '</td>';
            echo '<td>' . htmlspecialchars($event['deskripsi'] ?: '-') . '</td>';
            echo '</tr>';
        }
    }

    echo '</tbody>';
    echo '</table>';

    // Summary
    $total_offline = count(array_filter($events, function($e) { return $e['jenis'] === 'Offline'; }));
    $total_online = count(array_filter($events, function($e) { return $e['jenis'] === 'Online'; }));

    echo '<br><br>';
    echo '<h3>Ringkasan</h3>';
    echo '<table style="width: 300px;">';
    echo '<tr><td><strong>Total Training Internal:</strong></td><td>' . $total_offline . '</td></tr>';
    echo '<tr><td><strong>Total Training Eksternal:</strong></td><td>' . $total_online . '</td></tr>';
    echo '<tr><td><strong>Total Keseluruhan:</strong></td><td>' . count($events) . '</td></tr>';
    echo '</table>';

    echo '</body>';
    echo '</html>';

} catch (Exception $e) {
    error_log("Error in export_calendar.php: " . $e->getMessage());
    http_response_code(500);
    die('Error exporting calendar: ' . $e->getMessage());
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
