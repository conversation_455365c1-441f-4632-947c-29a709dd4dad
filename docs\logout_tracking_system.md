# 📊 Sistem Tracking Logout - Training Center PAS

## 🎯 Overview

Sistem tracking logout telah diintegrasikan dengan `admin/security_monitoring.php` untuk memantau aktivitas logout pengguna secara real-time. Sistem ini mencatat setiap aktivitas logout ke dalam database dan menampilkannya dalam dashboard monitoring keamanan.

## 🔧 Komponen Sistem

### 1. **File Utama yang Dimodifikasi**

#### `view/logout.php`
- **Fungsi**: Mencatat aktivitas logout ke database
- **Tabel Target**: `security_logs` dan `activity_logs`
- **Data yang Dicatat**:
  - User ID dan nama pengguna
  - Timestamp logout
  - IP Address
  - User Agent
  - Event type: 'LOGOUT'

#### `admin/security_monitoring.php`
- **Fungsi**: Menampilkan statistik dan log logout
- **Fitur Baru**:
  - Card statistik "Logout Hari Ini"
  - Tabel statistik logout per hari (7 hari terakhir)
  - Filter logout dalam tab "Log Login & Logout"

### 2. **Database Schema**

#### Tabel `security_logs`
```sql
CREATE TABLE security_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### Tabel `activity_logs` (Opsional)
```sql
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(255) NOT NULL,
    category VARCHAR(50) NOT NULL,
    details TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent VARCHAR(255) NULL,
    timestamp DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 📈 Fitur Monitoring

### 1. **Dashboard Statistik**

#### Card "Logout Hari Ini"
- Menampilkan jumlah total logout pada hari ini
- Icon: 🚪 (sign-out-alt)
- Warna: Info (biru)

#### Tabel Statistik Logout (7 Hari Terakhir)
- Menampilkan breakdown logout per hari
- Format tanggal: dd MMM yyyy
- Warna status: Warning (kuning)

### 2. **Tab "Log Login & Logout"**

#### Filter yang Tersedia:
- **Pengguna**: Dropdown semua pengguna aktif
- **Tanggal**: Date picker untuk filter tanggal spesifik
- **Jenis Aktivitas**: 
  - Semua Aktivitas
  - Login Berhasil
  - Login Gagal
  - **Logout** ← Baru ditambahkan
  - Login Tidak Dikenal

#### Tampilan Log:
- **Waktu**: Tanggal dan jam logout
- **Pengguna**: Nama dan NIK pengguna
- **Aktivitas**: Badge "Logout" dengan icon 🚪
- **IP Address**: IP address saat logout
- **Perangkat**: Browser dan device info

## 🔍 Cara Kerja Sistem

### 1. **Proses Logout**
```php
// Di view/logout.php
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
    $user_name = $_SESSION['name'] ?? 'Unknown User';
    
    // Log ke security_logs
    $stmt = $conn->prepare("INSERT INTO security_logs (user_id, event_type, description, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
    $event_type = 'LOGOUT';
    $description = "User logout dari sistem - " . $user_name;
    $stmt->bind_param("issss", $user_id, $event_type, $description, $ip_address, $user_agent);
    $stmt->execute();
    
    // Log ke activity_logs (opsional)
    log_activity($user_id, "Logout dari sistem", "login", [...]);
}
```

### 2. **Query Statistik**
```php
// Logout hari ini
$query = "SELECT COUNT(*) as count FROM security_logs WHERE event_type = 'LOGOUT' AND DATE(created_at) = CURDATE()";

// Logout per hari (7 hari terakhir)
$query = "SELECT DATE(created_at) as date, COUNT(*) as logout_count
          FROM security_logs
          WHERE event_type = 'LOGOUT' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
          GROUP BY DATE(created_at)
          ORDER BY date DESC";
```

### 3. **Filter dan Pencarian**
```php
// Filter logout dalam log
if ($filter_event == 'LOGOUT') {
    $where_clauses[] = "(event_type = 'LOGOUT' OR event_type = 'logout')";
}
```

## 🎨 UI/UX Features

### 1. **Visual Indicators**
- **Badge Logout**: Warna info (biru) dengan icon sign-out-alt
- **Hover Effects**: Row highlighting saat hover
- **Responsive Design**: Adaptif untuk mobile dan desktop

### 2. **Pagination**
- 20 log per halaman
- Navigation dengan first/last page
- Counter total log dan halaman

### 3. **Real-time Updates**
- Button "Refresh Data" untuk update real-time
- Auto-refresh setiap 30 detik (opsional)

## 🔒 Keamanan

### 1. **Data Protection**
- Prepared statements untuk mencegah SQL injection
- Sanitasi input dengan `htmlspecialchars()`
- Validasi user session sebelum logging

### 2. **Error Handling**
```php
try {
    // Log logout process
} catch (Exception $e) {
    // Jika gagal log, tetap lanjutkan logout
    error_log("Error logging logout: " . $e->getMessage());
}
```

### 3. **Access Control**
- Hanya admin (role_id = 99) yang dapat mengakses monitoring
- Session validation di setiap request

## 📊 Manfaat Sistem

### 1. **Monitoring Keamanan**
- Tracking aktivitas logout untuk audit trail
- Deteksi pola logout yang tidak normal
- Monitoring session management

### 2. **Analytics**
- Analisis pola penggunaan sistem
- Statistik aktivitas harian
- Laporan aktivitas pengguna

### 3. **Compliance**
- Audit trail untuk compliance requirements
- Log retention untuk investigasi
- Dokumentasi aktivitas sistem

## 🚀 Penggunaan

### 1. **Akses Dashboard**
1. Login sebagai admin (role_id = 99)
2. Navigasi ke `admin/security_monitoring.php`
3. Lihat card "Logout Hari Ini" di dashboard utama

### 2. **Melihat Log Logout**
1. Klik tab "Log Login & Logout"
2. Pilih filter "Logout" di dropdown "Jenis Aktivitas"
3. Atur filter tanggal/pengguna sesuai kebutuhan
4. Klik "Filter" untuk menerapkan

### 3. **Melihat Statistik**
1. Klik tab "Statistik"
2. Scroll ke tabel "Statistik Logout per Hari"
3. Lihat breakdown logout 7 hari terakhir

## 🔧 Maintenance

### 1. **Database Cleanup**
```sql
-- Hapus log logout lebih dari 90 hari
DELETE FROM security_logs 
WHERE event_type = 'LOGOUT' 
AND created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

### 2. **Performance Optimization**
```sql
-- Index untuk performa query
CREATE INDEX idx_security_logs_event_date ON security_logs(event_type, created_at);
CREATE INDEX idx_security_logs_user_event ON security_logs(user_id, event_type);
```

## 📝 Catatan

- Sistem ini terintegrasi dengan sistem monitoring keamanan yang sudah ada
- Log logout disimpan dalam format yang konsisten dengan log login
- Sistem dapat diperluas untuk tracking aktivitas lainnya
- Compatible dengan sistem activity logger yang sudah ada

---

**Dibuat**: <?= date('d F Y') ?>  
**Versi**: 1.0  
**Status**: ✅ Aktif dan Terintegrasi
