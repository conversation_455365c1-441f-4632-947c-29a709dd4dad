<?php
// File: admin/edit_attendance.php
// Deskripsi: Halaman untuk mengedit data absensi training

include '../config/config.php';
include 'security.php';

// Security check sudah dilakukan di security.php

// Cek apakah ID absensi dan training ID ada
if (!isset($_GET['id']) || !isset($_GET['training_id'])) {
    header("Location: rfid_attendance.php");
    exit();
}

$attendance_id = $_GET['id'];
$training_id = $_GET['training_id'];

// Ambil data absensi
$query_attendance = "SELECT ta.*, k.dept, k.bagian, k.jabatan
                    FROM training_attendance ta
                    LEFT JOIN karyawan k ON ta.karyawan_id = k.id
                    WHERE ta.id = ?";
$stmt_attendance = $conn->prepare($query_attendance);
$stmt_attendance->bind_param("i", $attendance_id);
$stmt_attendance->execute();
$result_attendance = $stmt_attendance->get_result();

if ($result_attendance->num_rows === 0) {
    header("Location: rfid_attendance.php?training_id=" . $training_id);
    exit();
}

$attendance = $result_attendance->fetch_assoc();

// Ambil data training
$query_training = "SELECT training_topic, training_date, training_date_fixed, training_date_start, training_date_end
                  FROM training_submissions
                  WHERE id = ?";
$stmt_training = $conn->prepare($query_training);
$stmt_training->bind_param("i", $training_id);
$stmt_training->execute();
$result_training = $stmt_training->get_result();
$training = $result_training->fetch_assoc();

// Fungsi untuk mendapatkan tanggal training yang sesuai
function getTrainingDate($row) {
    if (!empty($row['training_date_fixed'])) {
        return $row['training_date_fixed'];
    } elseif (!empty($row['training_date_start']) && !empty($row['training_date_end'])) {
        return $row['training_date_start'] . ' s/d ' . $row['training_date_end'];
    } else {
        return $row['training_date'];
    }
}

// Proses update absensi
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $status = $_POST['status'];
    $keterangan = $_POST['keterangan'];
    $check_in = !empty($_POST['check_in']) ? $_POST['check_in'] : null;
    $check_out = !empty($_POST['check_out']) ? $_POST['check_out'] : null;

    // Validasi input
    if (empty($status)) {
        $error = "Status tidak boleh kosong";
    } else {
        // Update data absensi
        $query_update = "UPDATE training_attendance
                        SET status = ?, keterangan = ?,
                            check_in = ?, check_out = ?,
                            updated_by = ?, updated_at = NOW()
                        WHERE id = ?";
        $stmt_update = $conn->prepare($query_update);
        $stmt_update->bind_param("ssssii", $status, $keterangan, $check_in, $check_out, $_SESSION['user_id'], $attendance_id);

        if ($stmt_update->execute()) {
            $success = "Data absensi berhasil diupdate";

            // Refresh data
            $stmt_attendance->execute();
            $result_attendance = $stmt_attendance->get_result();
            $attendance = $result_attendance->fetch_assoc();
        } else {
            $error = "Gagal mengupdate data absensi: " . $conn->error;
        }
    }
}

// Judul halaman
$page_title = "Edit Absensi Training";
include 'header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Edit Absensi Training</h1>

    <div class="row">
        <div class="col-md-12 mb-4">
            <a href="rfid_attendance.php?training_id=<?php echo $training_id; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Data Training</h6>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>Topik Training</th>
                            <td><?php echo $training['training_topic']; ?></td>
                        </tr>
                        <tr>
                            <th>Tanggal Training</th>
                            <td><?php echo getTrainingDate($training); ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Data Karyawan</h6>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>NIK</th>
                            <td><?php echo $attendance['nik']; ?></td>
                        </tr>
                        <tr>
                            <th>Nama</th>
                            <td><?php echo $attendance['nama']; ?></td>
                        </tr>
                        <tr>
                            <th>Departemen</th>
                            <td><?php echo $attendance['dept']; ?></td>
                        </tr>
                        <tr>
                            <th>Bagian</th>
                            <td><?php echo $attendance['bagian']; ?></td>
                        </tr>
                        <tr>
                            <th>Jabatan</th>
                            <td><?php echo $attendance['jabatan']; ?></td>
                        </tr>
                        <?php if (!empty($attendance['card_number'])): ?>
                        <tr>
                            <th>Nomor Kartu</th>
                            <td><?php echo $attendance['card_number']; ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Edit Absensi</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="form-group">
                            <label for="check_in">Check In:</label>
                            <input type="datetime-local" class="form-control" id="check_in" name="check_in"
                                   value="<?php echo $attendance['check_in'] ? date('Y-m-d\TH:i', strtotime($attendance['check_in'])) : ''; ?>">
                        </div>
                        <div class="form-group">
                            <label for="check_out">Check Out:</label>
                            <input type="datetime-local" class="form-control" id="check_out" name="check_out"
                                   value="<?php echo $attendance['check_out'] ? date('Y-m-d\TH:i', strtotime($attendance['check_out'])) : ''; ?>">
                        </div>
                        <div class="form-group">
                            <label for="status">Status:</label>
                            <select class="form-control" id="status" name="status" required>
                                <option value="hadir" <?php echo $attendance['status'] == 'hadir' ? 'selected' : ''; ?>>Hadir</option>
                                <option value="tidak hadir" <?php echo $attendance['status'] == 'tidak hadir' ? 'selected' : ''; ?>>Tidak Hadir</option>
                                <option value="terlambat" <?php echo $attendance['status'] == 'terlambat' ? 'selected' : ''; ?>>Terlambat</option>
                                <option value="izin" <?php echo $attendance['status'] == 'izin' ? 'selected' : ''; ?>>Izin</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="keterangan">Keterangan:</label>
                            <textarea class="form-control" id="keterangan" name="keterangan" rows="3"><?php echo $attendance['keterangan']; ?></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                    </form>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Riwayat Absensi</h6>
                </div>
                <div class="card-body">
                    <?php
                    // Ambil riwayat absensi
                    $query_logs = "SELECT tal.*, u.name as updated_by_name
                                  FROM training_attendance_logs tal
                                  LEFT JOIN users u ON tal.action_by = u.id
                                  WHERE tal.attendance_id = ?
                                  ORDER BY tal.action_time DESC";
                    $stmt_logs = $conn->prepare($query_logs);
                    $stmt_logs->bind_param("i", $attendance_id);
                    $stmt_logs->execute();
                    $result_logs = $stmt_logs->get_result();

                    if ($result_logs->num_rows > 0):
                    ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Waktu</th>
                                        <th>Tipe Aksi</th>
                                        <th>Status</th>
                                        <th>Oleh</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($log = $result_logs->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo date('d-m-Y H:i:s', strtotime($log['action_time'])); ?></td>
                                            <td>
                                                <?php
                                                    switch ($log['action_type']) {
                                                        case 'check_in':
                                                            echo 'Check In';
                                                            break;
                                                        case 'check_out':
                                                            echo 'Check Out';
                                                            break;
                                                        case 'manual_update':
                                                            echo 'Update Manual';
                                                            break;
                                                        default:
                                                            echo $log['action_type'];
                                                    }
                                                ?>
                                            </td>
                                            <td>
                                                <?php if ($log['old_status'] != $log['new_status']): ?>
                                                    <?php echo ucfirst($log['old_status'] ?: '-'); ?> → <?php echo ucfirst($log['new_status'] ?: '-'); ?>
                                                <?php else: ?>
                                                    <?php echo ucfirst($log['new_status'] ?: '-'); ?>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $log['updated_by_name'] ?: 'Sistem'; ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-center">Belum ada riwayat absensi</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'footer.php'; ?>
