<?php
/**
 * Test Script untuk Secret Password Viewer
 * Script ini membantu testing fitur secret password viewer
 */

session_start();
include '../config/config.php';

// Cek admin access
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    die('Access denied. Admin only.');
}

$action = $_GET['action'] ?? '';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Secret Password Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .btn-danger { background: #dc3545; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .info { background: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .success { background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Secret Password Viewer</h1>
        <p>Script untuk testing fitur Secret Password Viewer</p>
        
        <div class="info">
            <strong>Current Session:</strong><br>
            User ID: <?php echo $_SESSION['user_id']; ?><br>
            Role ID: <?php echo $_SESSION['role_id']; ?><br>
            Name: <?php echo $_SESSION['user_name'] ?? 'Unknown'; ?>
        </div>
    </div>

    <?php if (empty($action)): ?>
    <div class="container">
        <h2>🔧 Test Actions</h2>
        <a href="?action=check_config" class="btn">1. Check Configuration</a>
        <a href="?action=check_users" class="btn">2. Check Users & Passwords</a>
        <a href="?action=test_access" class="btn btn-warning">3. Test Developer Access</a>
        <a href="?action=create_test_users" class="btn btn-danger">4. Create Test Users</a>
        <a href="?action=cleanup" class="btn btn-danger">5. Cleanup Test Data</a>
    </div>
    <?php endif; ?>

    <?php if ($action === 'check_config'): ?>
    <div class="container">
        <h2>📋 Configuration Check</h2>
        
        <?php
        $config_file = '../config/developer_config.php';
        if (file_exists($config_file)) {
            echo '<div class="success">✅ Developer config file exists</div>';
            include $config_file;
            
            echo '<h3>Configuration Details:</h3>';
            echo '<div class="code">';
            echo 'DEVELOPER_SECRET_TOKEN: ' . (defined('DEVELOPER_SECRET_TOKEN') ? DEVELOPER_SECRET_TOKEN : 'NOT DEFINED') . '<br>';
            echo 'CURRENT_ENVIRONMENT: ' . (defined('CURRENT_ENVIRONMENT') ? CURRENT_ENVIRONMENT : 'NOT DEFINED') . '<br>';
            echo 'Allowed IPs: ' . (isset($ALLOWED_DEVELOPER_IPS) ? implode(', ', $ALLOWED_DEVELOPER_IPS) : 'NOT SET') . '<br>';
            echo '</div>';
            
            // Test validation function
            if (function_exists('validateDeveloperAccess')) {
                $test_validation = validateDeveloperAccess(DEVELOPER_SECRET_TOKEN, false);
                if ($test_validation['valid']) {
                    echo '<div class="success">✅ Developer access validation works</div>';
                } else {
                    echo '<div class="error">❌ Developer access validation failed: ' . $test_validation['message'] . '</div>';
                }
            } else {
                echo '<div class="error">❌ validateDeveloperAccess function not found</div>';
            }
        } else {
            echo '<div class="error">❌ Developer config file not found</div>';
        }
        ?>
        
        <a href="?" class="btn">Back to Menu</a>
    </div>
    <?php endif; ?>

    <?php if ($action === 'check_users'): ?>
    <div class="container">
        <h2>👥 Users & Password Analysis</h2>
        
        <?php
        $query = "SELECT id, name, nik, email, password, role_id, is_active FROM users ORDER BY is_active DESC, role_id, name LIMIT 20";
        $result = $conn->query($query);
        
        if ($result && $result->num_rows > 0) {
            echo '<table>';
            echo '<tr><th>ID</th><th>Name</th><th>NIK</th><th>Role</th><th>Status</th><th>Password Analysis</th></tr>';
            
            while ($user = $result->fetch_assoc()) {
                // Test password
                $is_default = password_verify('asdf', $user['password']);
                $is_password = password_verify('password', $user['password']);
                $is_123456 = password_verify('123456', $user['password']);
                
                $pwd_status = 'Unknown';
                if ($is_default) $pwd_status = '<span style="color: red;">DEFAULT (asdf)</span>';
                elseif ($is_password) $pwd_status = '<span style="color: orange;">WEAK (password)</span>';
                elseif ($is_123456) $pwd_status = '<span style="color: orange;">WEAK (123456)</span>';
                else $pwd_status = '<span style="color: green;">Secure/Custom</span>';
                
                echo '<tr>';
                echo '<td>' . $user['id'] . '</td>';
                echo '<td>' . htmlspecialchars($user['name']) . '</td>';
                echo '<td>' . htmlspecialchars($user['nik']) . '</td>';
                echo '<td>' . $user['role_id'] . '</td>';
                echo '<td>' . ($user['is_active'] ? 'Active' : 'Inactive') . '</td>';
                echo '<td>' . $pwd_status . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        } else {
            echo '<div class="warning">No users found</div>';
        }
        ?>
        
        <a href="?" class="btn">Back to Menu</a>
    </div>
    <?php endif; ?>

    <?php if ($action === 'test_access'): ?>
    <div class="container">
        <h2>🔐 Test Developer Access</h2>
        
        <div class="info">
            <h3>Test URLs:</h3>
            <p><strong>Secret Password Viewer:</strong></p>
            <div class="code">
                <a href="secret_password_viewer.php?token=dev_secret_2024_training_system" target="_blank">
                    secret_password_viewer.php?token=dev_secret_2024_training_system
                </a>
            </div>
            
            <p><strong>With Search:</strong></p>
            <div class="code">
                <a href="secret_password_viewer.php?token=dev_secret_2024_training_system&search=admin" target="_blank">
                    secret_password_viewer.php?token=dev_secret_2024_training_system&search=admin
                </a>
            </div>
            
            <p><strong>Specific User ID:</strong></p>
            <div class="code">
                <a href="secret_password_viewer.php?token=dev_secret_2024_training_system&user_id=1" target="_blank">
                    secret_password_viewer.php?token=dev_secret_2024_training_system&user_id=1
                </a>
            </div>
        </div>
        
        <div class="warning">
            <h3>Test Invalid Access:</h3>
            <p><strong>Wrong Token:</strong></p>
            <div class="code">
                <a href="secret_password_viewer.php?token=wrong_token" target="_blank">
                    secret_password_viewer.php?token=wrong_token
                </a>
            </div>
        </div>
        
        <a href="?" class="btn">Back to Menu</a>
    </div>
    <?php endif; ?>

    <?php if ($action === 'create_test_users'): ?>
    <div class="container">
        <h2>👤 Create Test Users</h2>
        
        <?php
        $confirm = $_GET['confirm'] ?? '';
        
        if ($confirm === 'yes') {
            // Create test users with different password types
            $test_users = [
                ['name' => 'Test Default Password', 'nik' => 'TEST001', 'email' => '<EMAIL>', 'password' => 'asdf'],
                ['name' => 'Test Weak Password', 'nik' => 'TEST002', 'email' => '<EMAIL>', 'password' => 'password'],
                ['name' => 'Test Strong Password', 'nik' => 'TEST003', 'email' => '<EMAIL>', 'password' => 'StrongP@ssw0rd123!']
            ];
            
            foreach ($test_users as $test_user) {
                // Check if user already exists
                $check_query = "SELECT id FROM users WHERE nik = ? OR email = ?";
                $check_stmt = $conn->prepare($check_query);
                $check_stmt->bind_param("ss", $test_user['nik'], $test_user['email']);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                
                if ($check_result->num_rows == 0) {
                    // Create user
                    $hashed_password = password_hash($test_user['password'], PASSWORD_DEFAULT);
                    $insert_query = "INSERT INTO users (name, nik, email, password, role_id, dept, bagian, jabatan, is_active) VALUES (?, ?, ?, ?, 1, 'Testing', 'QA', 'Tester', 1)";
                    $insert_stmt = $conn->prepare($insert_query);
                    $insert_stmt->bind_param("ssss", $test_user['name'], $test_user['nik'], $test_user['email'], $hashed_password);
                    
                    if ($insert_stmt->execute()) {
                        echo '<div class="success">✅ Created: ' . $test_user['name'] . ' (Password: ' . $test_user['password'] . ')</div>';
                    } else {
                        echo '<div class="error">❌ Failed to create: ' . $test_user['name'] . '</div>';
                    }
                } else {
                    echo '<div class="warning">⚠️ User already exists: ' . $test_user['name'] . '</div>';
                }
            }
        } else {
            echo '<div class="warning">This will create 3 test users with different password types. Are you sure?</div>';
            echo '<a href="?action=create_test_users&confirm=yes" class="btn btn-danger">Yes, Create Test Users</a>';
        }
        ?>
        
        <a href="?" class="btn">Back to Menu</a>
    </div>
    <?php endif; ?>

    <?php if ($action === 'cleanup'): ?>
    <div class="container">
        <h2>🧹 Cleanup Test Data</h2>
        
        <?php
        $confirm = $_GET['confirm'] ?? '';
        
        if ($confirm === 'yes') {
            // Delete test users
            $delete_query = "DELETE FROM users WHERE nik LIKE 'TEST%' OR email LIKE '<EMAIL>'";
            $result = $conn->query($delete_query);
            
            if ($result) {
                echo '<div class="success">✅ Deleted ' . $conn->affected_rows . ' test users</div>';
            } else {
                echo '<div class="error">❌ Failed to delete test users</div>';
            }
        } else {
            echo '<div class="warning">This will delete all test users (NIK starting with TEST or <NAME_EMAIL>). Are you sure?</div>';
            echo '<a href="?action=cleanup&confirm=yes" class="btn btn-danger">Yes, Delete Test Users</a>';
        }
        ?>
        
        <a href="?" class="btn">Back to Menu</a>
    </div>
    <?php endif; ?>

    <div class="container">
        <h2>📚 Quick Links</h2>
        <a href="manage_user.php" class="btn">Manage Users</a>
        <a href="secret_password_viewer.php?token=dev_secret_2024_training_system" class="btn btn-danger" target="_blank">Secret Password Viewer</a>
        <a href="../logs/developer_access.log" class="btn btn-warning" target="_blank">View Access Logs</a>
    </div>
</body>
</html>
