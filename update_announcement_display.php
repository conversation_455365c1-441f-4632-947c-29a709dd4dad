<?php
/**
 * Script untuk update tampilan announcement di semua dashboard user
 * Menambahkan support untuk gambar dan link
 */

echo "🖼️ UPDATING ANNOUNCEMENT DISPLAY WITH IMAGE & LINK SUPPORT\n";
echo "=========================================================\n\n";

$dashboard_files = [
    'LnD/index.php',
    'Dir/index.php', 
    'dept_head/index.php'
];

$old_announcement_template = '<li class="announcement-item">
                                    <div class="announcement-title">
                                        <?= htmlspecialchars($announcement[\'title\']) ?>
                                        <?php if (!empty($announcement[\'expiry_date\'])): ?>
                                            <span class="expiry-badge">
                                                <i class="fas fa-clock"></i> Berlaku s/d <?= date(\'d M Y\', strtotime($announcement[\'expiry_date\'])) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="announcement-meta">
                                        <span><i class="fas fa-user"></i> <?= htmlspecialchars($announcement[\'creator_name\'] ?? \'Admin\') ?></span>
                                        <span class="ms-2"><i class="fas fa-calendar-alt"></i> <?= date(\'d M Y\', strtotime($announcement[\'created_at\'])) ?></span>
                                    </div>
                                    <div class="announcement-content">
                                        <?= nl2br(htmlspecialchars($announcement[\'content\'])) ?>
                                    </div>
                                </li>';

$new_announcement_template = '<li class="announcement-item">
                                    <div class="announcement-title">
                                        <?= htmlspecialchars($announcement[\'title\']) ?>
                                        <?php if (!empty($announcement[\'expiry_date\'])): ?>
                                            <span class="expiry-badge">
                                                <i class="fas fa-clock"></i> Berlaku s/d <?= date(\'d M Y\', strtotime($announcement[\'expiry_date\'])) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="announcement-meta">
                                        <span><i class="fas fa-user"></i> <?= htmlspecialchars($announcement[\'creator_name\'] ?? \'Admin\') ?></span>
                                        <span class="ms-2"><i class="fas fa-calendar-alt"></i> <?= date(\'d M Y\', strtotime($announcement[\'created_at\'])) ?></span>
                                    </div>
                                    
                                    <!-- Announcement Image -->
                                    <?php if (!empty($announcement[\'image_path\'])): ?>
                                        <div class="announcement-image">
                                            <img src="../<?= htmlspecialchars($announcement[\'image_path\']) ?>" 
                                                 alt="<?= htmlspecialchars($announcement[\'title\']) ?>" 
                                                 class="img-fluid rounded"
                                                 style="max-width: 100%; height: auto; margin: 10px 0;">
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="announcement-content">
                                        <?= nl2br(htmlspecialchars($announcement[\'content\'])) ?>
                                    </div>
                                    
                                    <!-- Announcement Link -->
                                    <?php if (!empty($announcement[\'link_url\'])): ?>
                                        <div class="announcement-link">
                                            <a href="<?= htmlspecialchars($announcement[\'link_url\']) ?>" 
                                               target="_blank" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt"></i> 
                                                <?= htmlspecialchars($announcement[\'link_text\'] ?? \'Buka Link\') ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </li>';

foreach ($dashboard_files as $file) {
    echo "📄 Processing: $file\n";
    
    if (!file_exists($file)) {
        echo "   ❌ File not found: $file\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    if (strpos($content, $old_announcement_template) !== false) {
        $content = str_replace($old_announcement_template, $new_announcement_template, $content);
        
        if (file_put_contents($file, $content)) {
            echo "   ✅ Updated successfully\n";
        } else {
            echo "   ❌ Failed to write file\n";
        }
    } else {
        echo "   ⚠️  Template not found or already updated\n";
    }
    
    echo "\n";
}

echo "📊 SUMMARY:\n";
echo "===========\n";
echo "✅ Updated announcement display templates\n";
echo "✅ Added image support with responsive display\n";
echo "✅ Added link support with external link button\n";
echo "✅ Maintained existing styling and functionality\n";

echo "\n🎯 FEATURES ADDED:\n";
echo "==================\n";
echo "1. 🖼️ **Image Display:**\n";
echo "   - Responsive image display\n";
echo "   - Rounded corners styling\n";
echo "   - Alt text for accessibility\n";
echo "   - Proper path handling\n";

echo "\n2. 🔗 **Link Support:**\n";
echo "   - External link button\n";
echo "   - Custom link text or default 'Buka Link'\n";
echo "   - Opens in new tab/window\n";
echo "   - Bootstrap button styling\n";

echo "\n3. 📱 **Responsive Design:**\n";
echo "   - Images scale properly on mobile\n";
echo "   - Buttons work on touch devices\n";
echo "   - Maintains layout integrity\n";

echo "\n🚀 ANNOUNCEMENT DISPLAY UPDATED SUCCESSFULLY!\n";
?>
