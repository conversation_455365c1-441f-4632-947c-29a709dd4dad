-- Update offline_training table untuk menambahkan kolom approval workflow
-- Training Internal hanya memerlukan approval dari Dept Head dan L&D

-- Tambahkan kolom untuk approval workflow
ALTER TABLE `offline_training` 
ADD COLUMN `approved_dept_head` ENUM('Pending', 'Approved', 'Rejected') DEFAULT 'Pending' AFTER `status`,
ADD COLUMN `approved_lnd` ENUM('Pending', 'Approved', 'Rejected') DEFAULT 'Pending' AFTER `approved_dept_head`,
ADD COLUMN `comments_dept_head` TEXT NULL AFTER `approved_lnd`,
ADD COLUMN `comments_lnd` TEXT NULL AFTER `comments_dept_head`,
ADD COLUMN `approved_by_dept_head` INT NULL AFTER `comments_lnd`,
ADD COLUMN `approved_by_lnd` INT NULL AFTER `approved_by_dept_head`,
ADD COLUMN `approved_at_dept_head` TIMESTAMP NULL AFTER `approved_by_lnd`,
ADD COLUMN `approved_at_lnd` TIMESTAMP NULL AFTER `approved_at_dept_head`,
ADD COLUMN `current_approver` ENUM('dept_head', 'lnd', 'completed') DEFAULT 'dept_head' AFTER `approved_at_lnd`;

-- Update existing records untuk set current_approver
UPDATE `offline_training` 
SET `current_approver` = 'dept_head' 
WHERE `status` = 'Pending';

-- Update status values untuk konsistensi
UPDATE `offline_training` 
SET `status` = 'Pending Dept Head Approval' 
WHERE `status` = 'Pending';

-- Tambahkan index untuk performance
ALTER TABLE `offline_training` 
ADD INDEX `idx_current_approver` (`current_approver`),
ADD INDEX `idx_approved_dept_head` (`approved_dept_head`),
ADD INDEX `idx_approved_lnd` (`approved_lnd`),
ADD INDEX `idx_created_by` (`created_by`);

-- Tambahkan foreign key constraints jika diperlukan
ALTER TABLE `offline_training` 
ADD CONSTRAINT `fk_offline_training_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
ADD CONSTRAINT `fk_offline_training_approved_by_dept_head` FOREIGN KEY (`approved_by_dept_head`) REFERENCES `users` (`id`) ON DELETE SET NULL,
ADD CONSTRAINT `fk_offline_training_approved_by_lnd` FOREIGN KEY (`approved_by_lnd`) REFERENCES `users` (`id`) ON DELETE SET NULL;
