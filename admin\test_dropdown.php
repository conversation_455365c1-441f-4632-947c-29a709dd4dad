<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dropdown</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #BF0000;
            --primary-color-light: rgba(191, 0, 0, 0.1);
            --text-dark: #333333;
            --spacing-xs: 5px;
            --spacing-sm: 10px;
            --spacing-md: 15px;
            --border-radius-md: 8px;
            --transition-fast: 0.2s ease;
            --box-shadow-md: 0 4px 10px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-menu {
            display: flex;
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .nav-menu a {
            color: var(--text-dark);
            text-decoration: none;
            font-weight: 500;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 4px;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .nav-menu a:hover {
            color: var(--primary-color);
            background-color: var(--primary-color-light);
        }

        /* Dropdown Styles */
        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropdown .dropdown-toggle {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            cursor: pointer;
        }

        .nav-dropdown .dropdown-toggle::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            font-size: 12px;
            margin-left: 5px;
            transition: transform var(--transition-fast);
        }

        .nav-dropdown.active .dropdown-toggle::after {
            transform: rotate(180deg);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: var(--border-radius-md);
            box-shadow: var(--box-shadow-md);
            min-width: 220px;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all var(--transition-fast);
            overflow: hidden;
        }

        .nav-dropdown.active .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-menu a {
            display: block;
            padding: var(--spacing-sm) var(--spacing-md);
            color: var(--text-dark);
            text-decoration: none;
            font-weight: 500;
            border-bottom: 1px solid #f0f0f0;
            transition: all var(--transition-fast);
        }

        .dropdown-menu a:last-child {
            border-bottom: none;
        }

        .dropdown-menu a:hover {
            background-color: var(--primary-color-light);
            color: var(--primary-color);
            padding-left: calc(var(--spacing-md) + 5px);
        }

        .dropdown-menu a.disabled {
            color: #999;
            cursor: not-allowed;
            background-color: #f8f9fa;
        }

        .dropdown-menu a.disabled:hover {
            background-color: #f8f9fa;
            color: #999;
            padding-left: var(--spacing-md);
        }

        .dropdown-menu a i {
            width: 16px;
            margin-right: var(--spacing-xs);
        }

        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
        }

        .test-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Dropdown Test Page</h1>
        
        <h2>Test Navigation with Dropdown</h2>
        <nav class="nav-menu">
            <a href="#">
                <i class="fas fa-home"></i> Home
            </a>
            <div class="nav-dropdown">
                <a href="#" class="dropdown-toggle">
                    <i class="fas fa-file-alt"></i> Pengajuan Training
                </a>
                <div class="dropdown-menu">
                    <a href="#">
                        <i class="fas fa-external-link-alt"></i> Training Eksternal
                    </a>
                    <a href="#" class="disabled">
                        <i class="fas fa-building"></i> Training Internal
                        <small style="display: block; font-size: 11px; color: #999; margin-top: 2px;">Coming Soon</small>
                    </a>
                </div>
            </div>
            <a href="#">
                <i class="fas fa-history"></i> Riwayat Training
            </a>
        </nav>

        <h2>Manual Test Controls</h2>
        <button class="test-button" onclick="toggleDropdown()">Toggle Dropdown</button>
        <button class="test-button" onclick="showDropdownInfo()">Show Dropdown Info</button>
        <button class="test-button" onclick="testCSS()">Test CSS</button>

        <div class="debug-info" id="debugInfo">
            <strong>Debug Information:</strong><br>
            Click buttons above to test dropdown functionality.
        </div>
    </div>

    <script>
        // Debug function
        function showDropdownInfo() {
            const dropdowns = document.querySelectorAll('.nav-dropdown');
            const debugInfo = document.getElementById('debugInfo');
            
            let info = '<strong>Debug Information:</strong><br>';
            info += `Found ${dropdowns.length} dropdown(s)<br>`;
            
            dropdowns.forEach((dropdown, index) => {
                const toggle = dropdown.querySelector('.dropdown-toggle');
                const menu = dropdown.querySelector('.dropdown-menu');
                const isActive = dropdown.classList.contains('active');
                
                info += `<br>Dropdown ${index + 1}:<br>`;
                info += `- Toggle element: ${toggle ? 'Found' : 'Not found'}<br>`;
                info += `- Menu element: ${menu ? 'Found' : 'Not found'}<br>`;
                info += `- Active state: ${isActive}<br>`;
                
                if (menu) {
                    const computedStyle = window.getComputedStyle(menu);
                    info += `- Opacity: ${computedStyle.opacity}<br>`;
                    info += `- Visibility: ${computedStyle.visibility}<br>`;
                    info += `- Display: ${computedStyle.display}<br>`;
                }
            });
            
            debugInfo.innerHTML = info;
        }

        function toggleDropdown() {
            const dropdown = document.querySelector('.nav-dropdown');
            if (dropdown) {
                dropdown.classList.toggle('active');
                showDropdownInfo();
            }
        }

        function testCSS() {
            const menu = document.querySelector('.dropdown-menu');
            if (menu) {
                menu.style.opacity = '1';
                menu.style.visibility = 'visible';
                menu.style.transform = 'translateY(0)';
                menu.style.display = 'block';
                showDropdownInfo();
            }
        }

        // Dropdown functionality (same as navbar)
        function initializeDropdowns() {
            console.log('Initializing dropdowns...');
            const dropdowns = document.querySelectorAll('.nav-dropdown');
            console.log('Found dropdowns:', dropdowns.length);
            
            dropdowns.forEach((dropdown, index) => {
                const toggle = dropdown.querySelector('.dropdown-toggle');
                console.log(`Dropdown ${index}:`, dropdown, 'Toggle:', toggle);
                
                if (toggle) {
                    toggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('Dropdown clicked!', dropdown);
                        
                        // Close other dropdowns
                        dropdowns.forEach(otherDropdown => {
                            if (otherDropdown !== dropdown) {
                                otherDropdown.classList.remove('active');
                            }
                        });
                        
                        // Toggle current dropdown
                        dropdown.classList.toggle('active');
                        console.log('Dropdown active state:', dropdown.classList.contains('active'));
                        
                        // Update debug info
                        showDropdownInfo();
                    });
                }
            });
            
            // Close dropdowns when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.nav-dropdown')) {
                    dropdowns.forEach(dropdown => {
                        dropdown.classList.remove('active');
                    });
                }
            });
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeDropdowns);
        } else {
            initializeDropdowns();
        }

        // Show initial info
        setTimeout(showDropdownInfo, 100);
    </script>
</body>
</html>
