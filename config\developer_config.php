<?php
/**
 * Developer Configuration
 * Konfigurasi khusus untuk developer access
 */

// Secret token untuk developer access (ganti dengan token yang aman)
define('DEVELOPER_SECRET_TOKEN', 'dev_secret_2024_training_system');

// Daftar IP yang diizinkan mengakses developer features (opsional)
// Kosongkan array untuk mengizinkan semua IP
$ALLOWED_DEVELOPER_IPS = [
    '127.0.0.1',        // localhost
    '::1',              // localhost IPv6
    '***********/24',   // Local network range
    // Tambahkan IP developer lain di sini
];

// Environment yang diizinkan untuk developer features
$ALLOWED_ENVIRONMENTS = [
    'development',
    'staging',
    'local'
];

// Current environment (sesuaikan dengan setup Anda)
define('CURRENT_ENVIRONMENT', 'development');

/**
 * Fungsi untuk memvalidasi akses developer
 */
function validateDeveloperAccess($provided_token = null, $check_ip = true) {
    global $ALLOWED_DEVELOPER_IPS, $ALLOWED_ENVIRONMENTS;
    
    // Check environment
    if (!in_array(CURRENT_ENVIRONMENT, $ALLOWED_ENVIRONMENTS)) {
        return [
            'valid' => false,
            'message' => 'Developer features tidak tersedia di environment ini'
        ];
    }
    
    // Check admin role
    if (!isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
        return [
            'valid' => false,
            'message' => 'Akses ditolak: Hanya admin yang dapat mengakses fitur developer'
        ];
    }
    
    // Check secret token
    if ($provided_token !== DEVELOPER_SECRET_TOKEN) {
        return [
            'valid' => false,
            'message' => 'Token developer tidak valid'
        ];
    }
    
    // Check IP if enabled
    if ($check_ip && !empty($ALLOWED_DEVELOPER_IPS)) {
        $user_ip = getUserIP();
        $ip_allowed = false;
        
        foreach ($ALLOWED_DEVELOPER_IPS as $allowed_ip) {
            if (strpos($allowed_ip, '/') !== false) {
                // CIDR notation
                if (ipInRange($user_ip, $allowed_ip)) {
                    $ip_allowed = true;
                    break;
                }
            } else {
                // Exact IP match
                if ($user_ip === $allowed_ip) {
                    $ip_allowed = true;
                    break;
                }
            }
        }
        
        if (!$ip_allowed) {
            return [
                'valid' => false,
                'message' => 'IP address tidak diizinkan untuk akses developer'
            ];
        }
    }
    
    return [
        'valid' => true,
        'message' => 'Akses developer valid'
    ];
}

/**
 * Fungsi untuk mendapatkan IP user
 */
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

/**
 * Fungsi untuk mengecek apakah IP dalam range CIDR
 */
function ipInRange($ip, $cidr) {
    list($subnet, $mask) = explode('/', $cidr);
    return (ip2long($ip) & ~((1 << (32 - $mask)) - 1)) == ip2long($subnet);
}

/**
 * Fungsi untuk log aktivitas developer
 */
function logDeveloperActivity($action, $details = []) {
    global $conn;
    
    if (!isset($conn)) {
        return false;
    }
    
    $user_id = $_SESSION['user_id'] ?? 0;
    $user_name = $_SESSION['user_name'] ?? 'Unknown';
    $ip_address = getUserIP();
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    $log_data = [
        'action' => $action,
        'user_id' => $user_id,
        'user_name' => $user_name,
        'ip_address' => $ip_address,
        'user_agent' => $user_agent,
        'timestamp' => date('Y-m-d H:i:s'),
        'details' => $details
    ];
    
    // Insert ke activity_logs jika table ada
    $check_table = "SHOW TABLES LIKE 'activity_logs'";
    $result = $conn->query($check_table);
    
    if ($result && $result->num_rows > 0) {
        $query = "INSERT INTO activity_logs (user_id, action, details, created_at) VALUES (?, ?, ?, NOW())";
        $stmt = $conn->prepare($query);
        if ($stmt) {
            $details_json = json_encode($log_data);
            $stmt->bind_param("iss", $user_id, $action, $details_json);
            $stmt->execute();
            $stmt->close();
        }
    }
    
    // Juga simpan ke file log sebagai backup
    $log_file = __DIR__ . '/../logs/developer_access.log';
    $log_dir = dirname($log_file);
    
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_entry = date('Y-m-d H:i:s') . " | " . json_encode($log_data) . "\n";
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    
    return true;
}

/**
 * Fungsi untuk generate temporary access token
 */
function generateTemporaryToken($duration_minutes = 60) {
    $token_data = [
        'token' => bin2hex(random_bytes(32)),
        'expires' => time() + ($duration_minutes * 60),
        'created_by' => $_SESSION['user_id'] ?? 0,
        'ip' => getUserIP()
    ];
    
    // Simpan token ke session atau database
    $_SESSION['temp_developer_token'] = $token_data;
    
    return $token_data['token'];
}

/**
 * Fungsi untuk validasi temporary token
 */
function validateTemporaryToken($token) {
    if (!isset($_SESSION['temp_developer_token'])) {
        return false;
    }
    
    $token_data = $_SESSION['temp_developer_token'];
    
    // Check if token matches
    if ($token_data['token'] !== $token) {
        return false;
    }
    
    // Check if token expired
    if (time() > $token_data['expires']) {
        unset($_SESSION['temp_developer_token']);
        return false;
    }
    
    // Check if IP matches (optional security)
    if ($token_data['ip'] !== getUserIP()) {
        return false;
    }
    
    return true;
}
?>
