<?php
session_start();
include '../config/config.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

$training_id = $_GET['id'];
$query = "SELECT * FROM training_submissions WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $training_id);
$stmt->execute();
$training = $result = $stmt->get_result()->fetch_assoc();

if (!$training) {
    die("Training tidak ditemukan.");
}

// Debug: Log external vendor information
if (isset($training['provider_type']) && $training['provider_type'] === 'External') {
    error_log("External vendor information for training ID $training_id:");
    error_log("Provider Name: " . ($training['provider_name'] ?? 'NULL'));
    error_log("Provider Address: " . ($training['provider_address'] ?? 'NULL'));
    error_log("Trainer Name External: " . ($training['trainer_name_external'] ?? 'NULL'));
    error_log("Additional Info Provider: " . ($training['additional_info_provider'] ?? 'NULL'));
}
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<!-- Custom Modal CSS -->
<link rel="stylesheet" href="../asset/custom-modal.css">
<!-- Removed duplicate custom-modal.js to prevent "CustomModal already declared" error -->
<style>
.edit-training-container {
    max-width: 1200px;
    margin: 30px auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
}

.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #BF0000, #800000);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSg0NSkiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjA1KSIvPjwvcGF0dGVybj48L2RlZnM+PHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwYXR0ZXJuKSIvPjwvc3ZnPg==');
    opacity: 0.2;
    z-index: 0;
}

.page-header h1 {
    margin: 0;
    font-size: 2.5em;
    font-weight: 700;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.page-header p {
    margin: 10px 0 0;
    font-size: 1.2em;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.form-container {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.form-section {
    margin-bottom: 30px;
    padding: 25px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.form-section:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transform: translateY(-2px);
}

.form-section h2 {
    color: #333;
    margin-bottom: 25px;
    padding-bottom: 12px;
    border-bottom: 2px solid #BF0000;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h2::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 25px;
    background: #BF0000;
    border-radius: 4px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #333;
    font-size: 15px;
    position: relative;
}

.form-group label.required::after {
    content: '*';
    color: #BF0000;
    margin-left: 4px;
}

.form-group .help-text {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    display: block;
}

.form-control {
    width: 100%;
    padding: 14px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 15px;
    transition: all 0.3s ease;
    background-color: #f9f9f9;
}

.form-control:focus {
    border-color: #BF0000;
    box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
    outline: none;
    background-color: #fff;
}

.form-control:hover {
    border-color: #999;
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

.status-select {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    width: 100%;
    background-color: white;
}

.button-group {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
}

.btn {
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: -100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    transition: all 0.6s ease;
}

.btn:hover::after {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #BF0000, #800000);
    color: white;
    box-shadow: 0 4px 15px rgba(191, 0, 0, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(1px);
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .edit-training-container {
        padding: 10px;
    }

    .form-section {
        padding: 15px;
    }

    .button-group {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }
}

/* Alert Styling */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 8px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.tab-container {
    margin-bottom: 30px;
    position: relative;
}

.tab-buttons {
    display: flex;
    gap: 5px;
    margin-bottom: 25px;
    flex-wrap: wrap;
    border-radius: 10px;
    background: #f0f0f0;
    padding: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.tab-button {
    padding: 12px 20px;
    background: transparent;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    color: #555;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    flex: 1;
    min-width: 150px;
    text-align: center;
}

.tab-button:hover {
    background: rgba(191, 0, 0, 0.1);
    color: #BF0000;
}

.tab-button.active {
    background: #BF0000;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(191, 0, 0, 0.2);
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #BF0000;
}

.tab-content {
    display: none;
    animation: fadeIn 0.5s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Trainer search and table styles */
.search-results {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    z-index: 100;
    position: relative;
}

.search-result-item {
    transition: all 0.2s ease;
}

.search-result-item:hover {
    background-color: #f5f5f5;
}

.search-result-item:last-child {
    border-bottom: none;
}

.trainer-table-container {
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
    margin-top: 25px;
    transition: all 0.3s ease;
}

.trainer-table-container:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 0;
    border-radius: 8px;
    overflow: hidden;
}

.table th {
    padding: 18px 15px;
    font-weight: 600;
    text-align: left;
    border: 1px solid #dee2e6;
    border-bottom: 2px solid #BF0000;
    color: #333;
    font-size: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table th:first-child {
    border-top-left-radius: 8px;
}

.table th:last-child {
    border-top-right-radius: 8px;
}

.table td {
    padding: 18px 15px;
    border: 1px solid #dee2e6;
    vertical-align: middle;
    transition: all 0.2s ease;
    font-size: 15px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
}

.table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.table tbody tr:last-child td:first-child {
    border-bottom-left-radius: 8px;
}

.table tbody tr:last-child td:last-child {
    border-bottom-right-radius: 8px;
}

.d-flex {
    display: flex;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}

.mb-3 {
    margin-bottom: 15px;
}

/* Modal styles */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

.modal-header {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    padding: 15px 20px;
}

.modal-header.bg-danger {
    background: linear-gradient(135deg, #BF0000, #800000) !important;
}

.modal-body {
    padding: 20px;
    font-size: 16px;
}

.modal-footer {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    padding: 15px 20px;
    border-top: 1px solid #eee;
}

.close {
    font-size: 1.5rem;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.close:hover {
    opacity: 1;
    transform: scale(1.1);
}

/* Alert notification styles */
.alert {
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    animation: slideDown 0.3s ease-out;
    margin-bottom: 20px;
}

@keyframes slideDown {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
</style>

<body>
    <?php include '../config/navbarb.php'; ?>
    <body class="jarak-form"></body>
    <div class="container-form">
        <div class="edit-training-container">
            <div class="page-header">
                <h1>Edit Training</h1>
                <p>ID Training: <?= htmlspecialchars($training['id']) ?></p>
            </div>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success">
                    <?= $_SESSION['success'] ?>
                    <?php unset($_SESSION['success']); ?>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger">
                    <?= $_SESSION['error'] ?>
                    <?php unset($_SESSION['error']); ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="update_training.php" id="trainingForm" enctype="multipart/form-data" novalidate>
                <input type="hidden" name="id" value="<?= htmlspecialchars($training['id']) ?>">

                <div class="tab-container">
                    <div class="tab-buttons">
                        <button type="button" class="tab-button active" onclick="showTab('basic-info', event)">Informasi Dasar</button>
                        <button type="button" class="tab-button" onclick="showTab('trainer-info', event)">Informasi Trainer</button>
                        <button type="button" class="tab-button" onclick="showTab('participants-info', event)">Peserta Training</button>
                        <button type="button" class="tab-button" onclick="showTab('schedule-info', event)">Jadwal & Lokasi</button>
                        <button type="button" class="tab-button" onclick="showTab('approval-info', event)">Status & Persetujuan</button>
                    </div>
                </div>

                <!-- Tab Informasi Dasar -->
                <div id="basic-info" class="tab-content active">
                    <div class="form-section">
                        <h2>Informasi Dasar</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="required">User ID</label>
                                <input type="number" class="form-control" name="user_id" value="<?= htmlspecialchars($training['user_id']??'') ?>" required>
                                <span class="help-text">ID pengguna yang mengajukan training</span>
                            </div>
                            <div class="form-group">
                                <label class="required">Nama Lengkap</label>
                                <input type="text" class="form-control" name="full_name" value="<?= htmlspecialchars($training['full_name']??'') ?>" required>
                            </div>
                            <div class="form-group">
                                <label>NIK</label>
                                <input type="text" class="form-control" name="nik" value="<?= htmlspecialchars($training['nik']??'') ?>">
                            </div>
                            <div class="form-group">
                                <label>Email</label>
                                <input type="email" class="form-control" name="email" value="<?= htmlspecialchars($training['email']??'') ?>">
                            </div>
                            <div class="form-group">
                                <label>Telepon</label>
                                <input type="text" class="form-control" name="phone" value="<?= htmlspecialchars($training['phone']??'') ?>">
                            </div>
                            <div class="form-group">
                                <label>Departemen</label>
                                <input type="text" class="form-control" name="departemen" value="<?= htmlspecialchars($training['departemen']??'') ?>">
                            </div>
                            <div class="form-group">
                                <label>Bagian</label>
                                <input type="text" class="form-control" name="bagian" value="<?= htmlspecialchars($training['bagian']??'') ?>">
                            </div>
                            <div class="form-group">
                                <label>Jabatan</label>
                                <input type="text" class="form-control" name="jabatan" value="<?= htmlspecialchars($training['jabatan']??'') ?>">
                            </div>
                            <div class="form-group">
                                <label class="required">Topik Training</label>
                                <input type="text" class="form-control" name="training_topic" value="<?= htmlspecialchars($training['training_topic']??'') ?>" required>
                                <span class="help-text">Judul atau topik utama training</span>
                            </div>
                            <div class="form-group">
                                <label class="required">Tipe Training</label>
                                <select class="form-control" name="training_type" required>
                                    <option value="Inhouse" <?= ($training['training_type']??'') == 'Inhouse' ? 'selected' : '' ?>>Inhouse</option>
                                    <option value="Outhouse" <?= ($training['training_type']??'') == 'Outhouse' ? 'selected' : '' ?>>Outhouse</option>
                                    <option value="Hybrid" <?= ($training['training_type']??'') == 'Hybrid' ? 'selected' : '' ?>>Hybrid</option>
                                    <option value="Other" <?= !in_array(($training['training_type']??''), ['Inhouse', 'Outhouse', 'Hybrid']) && !empty($training['training_type']) ? 'selected' : '' ?>>Lainnya</option>
                                </select>
                                <span class="help-text">Kategori training (Inhouse, Outhouse, atau Hybrid)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Informasi Trainer -->
                <div id="trainer-info" class="tab-content">
                    <div class="form-section">
                        <h2>Informasi Provider/Trainer</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label>Tipe Provider</label>
                                <select class="form-control" name="provider_type" id="provider_type_select" onchange="toggleProviderFields()">
                                    <?php if(empty($training['provider_type'])): ?>
                                        <option value="" disabled selected>Pilih Tipe Provider</option>
                                        <option value="Internal">Internal</option>
                                        <option value="External">External</option>
                                    <?php else: ?>
                                        <option value="Internal" <?= $training['provider_type'] == 'Internal' ? 'selected' : '' ?>>Internal</option>
                                        <option value="External" <?= ($training['provider_type'] == 'External' || $training['provider_type'] == 'Eksternal') ? 'selected' : '' ?>>External</option>
                                    <?php endif; ?>
                                </select>
                                <span class="help-text">Pilih tipe provider untuk training</span>
                            </div>
                            <!-- Internal Provider Fields -->
                            <div id="internalFields" style="display: none;">
                                <div class="form-group">
                                    <label class="required">Cari Trainer Internal</label>
                                    <input type="text" id="search_trainer" class="form-control" placeholder="Cari karyawan berdasarkan NIK, nama, departemen, dll...">
                                    <span class="help-text">Ketik untuk mencari karyawan yang akan menjadi trainer</span>

                                    <div id="trainer_results" class="search-results" style="display: none; max-height: 300px; overflow-y: auto; margin-top: 10px; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); z-index: 1000;"></div>

                                    <div class="trainer-table-container" style="margin-top: 30px; width: 100%; max-width: 100%;">
                                        <div class="d-flex justify-content-between align-items-center mb-4">
                                            <div class="trainer-header">
                                                <h4 style="margin: 0; font-weight: 600; color: #333; display: flex; align-items: center;">
                                                    <i class="fas fa-users-cog" style="margin-right: 10px; color: #BF0000;"></i>
                                                    Daftar Trainer
                                                </h4>
                                                <p style="margin: 5px 0 0 0; color: #666; font-size: 13px;">Trainer yang akan mengajar pada training ini</p>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-primary" onclick="addTrainerManual()" style="padding: 8px 15px; border-radius: 6px;">
                                                <i class="fas fa-plus" style="margin-right: 5px;"></i> Tambah Manual
                                            </button>
                                        </div>

                                        <div class="table-responsive" style="width: 100%; overflow-x: auto;">
                                            <table class="table table-bordered" style="min-width: 900px; width: 100%;">
                                                <thead>
                                                    <tr>
                                                        <th width="5%" style="text-align: center;">#</th>
                                                        <th width="25%">Nama Trainer</th>
                                                        <th width="15%">NIK</th>
                                                        <th width="20%">Departemen</th>
                                                        <th width="15%">Bagian</th>
                                                        <th width="15%">Jabatan</th>
                                                        <th width="5%" style="text-align: center;">Aksi</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="trainer_list">
                                                    <?php
                                                    // Parse trainer data if available
                                                    $trainerNames = json_decode($training['trainer_name_internal'] ?? '[]', true);
                                                    $trainerNiks = json_decode($training['trainer_nik_internal'] ?? '[]', true);
                                                    $trainerDepts = json_decode($training['trainer_department_internal'] ?? '[]', true);
                                                    $trainerSubDepts = json_decode($training['trainer_sub_department_internal'] ?? '[]', true);
                                                    $trainerPositions = json_decode($training['trainer_position_internal'] ?? '[]', true);

                                                    // Display existing trainers
                                                    if (is_array($trainerNames) && count($trainerNames) > 0) {
                                                        for ($i = 0; $i < count($trainerNames); $i++) {
                                                            echo '<tr>';
                                                            echo '<td style="text-align: center; font-weight: 600;">'.($i+1).'</td>';
                                                            echo '<td><span style="font-weight: 600; color: #333;">'.$trainerNames[$i].'</span><input type="hidden" name="trainer_name_internal[]" value="'.$trainerNames[$i].'"></td>';
                                                            echo '<td>'.$trainerNiks[$i].'<input type="hidden" name="trainer_nik_internal[]" value="'.$trainerNiks[$i].'"></td>';
                                                            echo '<td>'.$trainerDepts[$i].'<input type="hidden" name="trainer_department_internal[]" value="'.$trainerDepts[$i].'"></td>';
                                                            echo '<td>'.$trainerSubDepts[$i].'<input type="hidden" name="trainer_sub_department_internal[]" value="'.$trainerSubDepts[$i].'"></td>';
                                                            echo '<td>'.$trainerPositions[$i].'<input type="hidden" name="trainer_position_internal[]" value="'.$trainerPositions[$i].'"></td>';
                                                            echo '<td style="text-align: center;"><button type="button" class="btn btn-sm btn-danger" onclick="confirmRemoveTrainer(this)" style="border-radius: 4px;"><i class="fas fa-trash-alt"></i> Hapus</button></td>';
                                                            echo '</tr>';
                                                        }
                                                    } else {
                                                        echo '<tr><td colspan="7" style="text-align: center; padding: 20px; color: #666;">Belum ada trainer yang ditambahkan</td></tr>';
                                                    }
                                                    ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- External Provider Fields -->
                            <div id="externalFields" style="display: none;">
                                <div class="form-group">
                                    <label>Nama Provider</label>
                                    <input type="text" class="form-control" name="provider_name" value="<?= htmlspecialchars($training['provider_name']??'') ?>">
                                    <span class="help-text">Nama perusahaan/lembaga provider training</span>
                                </div>
                                <div class="form-group">
                                    <label>Alamat Provider</label>
                                    <textarea class="form-control" name="provider_address"><?= htmlspecialchars($training['provider_address']??'') ?></textarea>
                                    <span class="help-text">Alamat lengkap provider training</span>
                                </div>
                                <div class="form-group">
                                    <label>Nama Trainer External</label>
                                    <input type="text" class="form-control" name="trainer_name_external" value="<?= htmlspecialchars($training['trainer_name_external']??'') ?>">
                                    <span class="help-text">Nama trainer dari provider (kosongkan jika belum diketahui)</span>
                                    <!-- Debug info -->
                                    <?php echo "<!-- Debug: Trainer Name External = " . htmlspecialchars($training['trainer_name_external']??'NULL') . " -->"; ?>
                                </div>
                                <div class="form-group">
                                    <label>Informasi Tambahan Provider</label>
                                    <textarea class="form-control" name="additional_info_provider"><?= htmlspecialchars($training['additional_info_provider']??'') ?></textarea>
                                    <span class="help-text">Informasi tambahan tentang provider (opsional)</span>
                                    <!-- Debug info -->
                                    <?php echo "<!-- Debug: Additional Info Provider = " . htmlspecialchars($training['additional_info_provider']??'NULL') . " -->"; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Peserta Training -->
                <div id="participants-info" class="tab-content">
                    <div class="form-section">
                        <h2>Peserta Training</h2>
                        <div class="form-grid">
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label class="required">Cari Peserta</label>
                                <input type="text" id="search_participant" class="form-control" placeholder="Cari karyawan berdasarkan NIK, nama, departemen, dll...">
                                <span class="help-text">Ketik untuk mencari karyawan yang akan menjadi peserta training</span>

                                <div id="participant_results" class="search-results" style="display: none; max-height: 300px; overflow-y: auto; margin-top: 10px; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); z-index: 1000;"></div>

                                <div class="trainer-table-container" style="margin-top: 30px; width: 100%; max-width: 100%;">
                                    <div class="d-flex justify-content-between align-items-center mb-4">
                                        <div class="trainer-header">
                                            <h4 style="margin: 0; font-weight: 600; color: #333; display: flex; align-items: center;">
                                                <i class="fas fa-users" style="margin-right: 10px; color: #BF0000;"></i>
                                                Daftar Peserta
                                            </h4>
                                            <p style="margin: 5px 0 0 0; color: #666; font-size: 13px;">Peserta yang akan mengikuti training ini</p>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="addParticipantManual()" style="padding: 8px 15px; border-radius: 6px;">
                                            <i class="fas fa-plus" style="margin-right: 5px;"></i> Tambah Manual
                                        </button>
                                    </div>

                                    <div class="table-responsive" style="width: 100%; overflow-x: auto;">
                                        <table class="table table-bordered" style="min-width: 900px; width: 100%;">
                                            <thead>
                                                <tr>
                                                    <th width="5%" style="text-align: center;">#</th>
                                                    <th width="25%">Nama Peserta</th>
                                                    <th width="15%">NIK</th>
                                                    <th width="15%">Jabatan</th>
                                                    <th width="15%">Bagian</th>
                                                    <th width="20%">Departemen</th>
                                                    <th width="5%" style="text-align: center;">Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody id="participant_list">
                                                <?php
                                                // Fetch participants for this training
                                                $participantQuery = "SELECT * FROM participants WHERE training_id = ?";
                                                $participantStmt = $conn->prepare($participantQuery);
                                                $participantStmt->bind_param("i", $training_id);
                                                $participantStmt->execute();
                                                $participantResult = $participantStmt->get_result();

                                                // Debug information
                                                echo "<!-- Debug: Training ID = $training_id -->";
                                                echo "<!-- Debug: Participant Count = " . $participantResult->num_rows . " -->";

                                                if ($participantResult->num_rows > 0) {
                                                    $counter = 1;
                                                    while ($participant = $participantResult->fetch_assoc()) {
                                                        echo '<tr>';
                                                        echo '<td style="text-align: center; font-weight: 600;">' . $counter . '</td>';
                                                        echo '<td><span style="font-weight: 600; color: #333;">' . htmlspecialchars($participant['nama_participants']) . '</span>';
                                                        echo '<input type="hidden" name="participant_name[]" value="' . htmlspecialchars($participant['nama_participants']) . '"></td>';

                                                        echo '<td>' . htmlspecialchars($participant['nik_participants']) . '';
                                                        echo '<input type="hidden" name="participant_nik[]" value="' . htmlspecialchars($participant['nik_participants']) . '"></td>';

                                                        echo '<td>' . htmlspecialchars($participant['jabatan_participants']) . '';
                                                        echo '<input type="hidden" name="participant_position[]" value="' . htmlspecialchars($participant['jabatan_participants']) . '"></td>';

                                                        echo '<td>' . htmlspecialchars($participant['bagian_participants']) . '';
                                                        echo '<input type="hidden" name="participant_sub_department[]" value="' . htmlspecialchars($participant['bagian_participants']) . '"></td>';

                                                        echo '<td>' . htmlspecialchars($participant['departemen_participants']) . '';
                                                        echo '<input type="hidden" name="participant_department[]" value="' . htmlspecialchars($participant['departemen_participants']) . '"></td>';

                                                        echo '<td style="text-align: center;">';
                                                        echo '<button type="button" class="btn btn-sm btn-danger" onclick="removeParticipantFromDB(' . $participant['id'] . ')" style="border-radius: 4px;">';
                                                        echo '<i class="fas fa-trash-alt"></i> Hapus</button></td>';
                                                        echo '</tr>';
                                                        $counter++;
                                                    }
                                                } else {
                                                    echo '<tr><td colspan="7" style="text-align: center; padding: 20px; color: #666;">Belum ada peserta yang ditambahkan</td></tr>';
                                                }
                                                ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Jadwal & Lokasi -->
                <div id="schedule-info" class="tab-content">
                    <div class="form-section">
                        <h2>Jadwal dan Lokasi</h2>
                        <div class="form-grid">
                            <?php
                            // Determine which date type to show based on available data
                            $dateType = 'regular';
                            if ($training['is_confirmed'] == 1 && !empty($training['start_date']) && empty($training['end_date'])) {
                                $dateType = 'fixed';
                            } elseif (!empty($training['start_date']) && !empty($training['end_date']) && $training['start_date'] != $training['end_date']) {
                                $dateType = 'range';
                            } elseif (!empty($training['start_date'])) {
                                $dateType = 'regular';
                            }
                            ?>

                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label>Jenis Tanggal Training</label>
                                <div class="date-type-selector" style="margin-bottom: 15px;">
                                    <label style="display: inline-block; margin-right: 15px;">
                                        <input type="radio" name="date_type" value="regular" <?= $dateType === 'regular' ? 'checked' : '' ?> onclick="toggleDateFields('regular')">
                                        Tanggal Biasa
                                    </label>
                                    <label style="display: inline-block; margin-right: 15px;">
                                        <input type="radio" name="date_type" value="fixed" <?= $dateType === 'fixed' ? 'checked' : '' ?> onclick="toggleDateFields('fixed')">
                                        Tanggal Fixed (1 Hari)
                                    </label>
                                    <label style="display: inline-block;">
                                        <input type="radio" name="date_type" value="range" <?= $dateType === 'range' ? 'checked' : '' ?> onclick="toggleDateFields('range')">
                                        Tanggal Range (Beberapa Hari)
                                    </label>
                                </div>
                            </div>

                            <div id="regular_date_field" class="form-group date-field" style="display: <?= $dateType === 'regular' ? 'block' : 'none' ?>;">
                                <label class="required">Tanggal Training</label>
                                <input type="date" class="form-control" name="start_date" id="training_date"
                                       value="<?= isset($training['start_date']) ? htmlspecialchars($training['start_date']) : '' ?>" <?= $dateType === 'regular' ? 'required' : '' ?>>
                                <span class="help-text">Tanggal pelaksanaan training</span>
                            </div>

                            <div id="fixed_date_field" class="form-group date-field" style="display: <?= $dateType === 'fixed' ? 'block' : 'none' ?>;">
                                <label class="<?= $dateType === 'fixed' ? 'required' : '' ?>">Tanggal Fixed</label>
                                <input type="date" class="form-control" name="start_date" id="training_date_fixed"
                                       value="<?= isset($training['start_date']) ? htmlspecialchars($training['start_date']) : '' ?>" <?= $dateType === 'fixed' ? 'required' : '' ?>>
                                <span class="help-text">Tanggal pasti pelaksanaan training (1 hari)</span>
                                <input type="hidden" name="is_confirmed" value="1">
                            </div>

                            <div id="date_range_fields" style="display: <?= $dateType === 'range' ? 'grid' : 'none' ?>; grid-column: 1 / -1; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div class="form-group">
                                    <label class="<?= $dateType === 'range' ? 'required' : '' ?>">Tanggal Mulai</label>
                                    <input type="date" class="form-control" name="start_date" id="training_date_start"
                                           value="<?= isset($training['start_date']) ? htmlspecialchars($training['start_date']) : '' ?>" <?= $dateType === 'range' ? 'required' : '' ?>>
                                    <span class="help-text">Tanggal mulai training</span>
                                </div>
                                <div class="form-group">
                                    <label class="<?= $dateType === 'range' ? 'required' : '' ?>">Tanggal Selesai</label>
                                    <input type="date" class="form-control" name="end_date" id="training_date_end"
                                           value="<?= isset($training['end_date']) ? htmlspecialchars($training['end_date']) : '' ?>" <?= $dateType === 'range' ? 'required' : '' ?>>
                                    <span class="help-text">Tanggal selesai training</span>
                                </div>
                            </div>

                            <div class="form-group" style="grid-column: 1 / -1; margin-top: 10px;">
                                <div class="date-info" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; border-left: 4px solid #17a2b8;">
                                    <p style="margin: 0; font-size: 14px;">
                                        <i class="fas fa-info-circle" style="color: #17a2b8;"></i>
                                        <strong>Informasi Tanggal:</strong>
                                        <?php if ($dateType === 'fixed'): ?>
                                            Tanggal sudah ditetapkan untuk 1 hari pada <?= htmlspecialchars($training['start_date'] ?? 'Belum ditentukan') ?>
                                        <?php elseif ($dateType === 'range'): ?>
                                            Tanggal sudah ditetapkan untuk beberapa hari dari <?= htmlspecialchars($training['start_date'] ?? '') ?> sampai <?= htmlspecialchars($training['end_date'] ?? '') ?>
                                        <?php else: ?>
                                            Tanggal training yang diajukan: <?= htmlspecialchars($training['start_date'] ?? 'Belum ditentukan') ?>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Waktu Mulai</label>
                                <input type="time" class="form-control" name="training_time_start"
                                       value="<?= isset($training['training_time_start']) ? htmlspecialchars($training['training_time_start']) : '' ?>">
                                <span class="help-text">Jam mulai training</span>
                            </div>
                            <div class="form-group">
                                <label>Waktu Selesai</label>
                                <input type="time" class="form-control" name="training_time_end"
                                       value="<?= isset($training['training_time_end']) ? htmlspecialchars($training['training_time_end']) : '' ?>">
                                <span class="help-text">Jam selesai training</span>
                            </div>
                            <div class="form-group">
                                <label>Tempat Training</label>
                                <input type="text" class="form-control" name="training_place"
                                       value="<?= isset($training['training_place']) ? htmlspecialchars($training['training_place']) : '' ?>">
                                <span class="help-text">Lokasi pelaksanaan training</span>
                            </div>
                            <div class="form-group">
                                <label>Biaya Training</label>
                                <input type="text" class="form-control" name="training_cost"
                                       value="<?= isset($training['training_cost']) ? htmlspecialchars($training['training_cost']) : '' ?>">
                            </div>
                            <div class="form-group">
                                <label>Contact Person</label>
                                <input type="text" class="form-control" name="contact_person"
                                       value="<?= isset($training['contact_person']) ? htmlspecialchars($training['contact_person']) : '' ?>">
                            </div>
                            <div class="form-group">
                                <label>Nomor Contact Person</label>
                                <input type="text" class="form-control" name="contact_number"
                                       value="<?= isset($training['contact_number']) ? htmlspecialchars($training['contact_number']) : '' ?>">
                            </div>
                            <div class="form-group">
                                <label>Sharing Knowledge</label>
                                <select class="form-control" name="sharing_knowledge">
                                    <option value="Ya" <?= ($training['sharing_knowledge'] ?? '') == 'Ya' ? 'selected' : '' ?>>Ya</option>
                                    <option value="Tidak" <?= ($training['sharing_knowledge'] ?? '') == 'Tidak' ? 'selected' : '' ?>>Tidak</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Status & Persetujuan -->
                <div id="approval-info" class="tab-content">
                    <div class="form-section">
                        <h2>Status dan Persetujuan</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="required">Status</label>
                                <select class="form-control" name="status" required>
                                    <option value="Pending" <?= $training['status'] === 'Pending' ? 'selected' : '' ?>>Pending</option>
                                    <option value="Approved" <?= $training['status'] === 'Approved' ? 'selected' : '' ?>>Approved</option>
                                    <option value="Rejected" <?= $training['status'] === 'Rejected' ? 'selected' : '' ?>>Rejected</option>
                                    <option value="Completed" <?= $training['status'] === 'Completed' ? 'selected' : '' ?>>Completed</option>
                                </select>
                                <span class="help-text">Status persetujuan training</span>
                            </div>
                            <div class="form-group">
                                <label>Current Approver Role ID</label>
                                <input type="number" class="form-control" name="current_approver_role_id"
                                       value="<?= isset($training['current_approver_role_id']) ? htmlspecialchars($training['current_approver_role_id']) : '' ?>">
                            </div>
                            <div class="form-group">
                                <label>Status Dept Head</label>
                                <select class="form-control" name="approved_dept_head">
                                    <option value="Pending" <?= $training['approved_dept_head'] == 'Pending' ? 'selected' : '' ?>>Pending</option>
                                    <option value="Approved" <?= $training['approved_dept_head'] == 'Approved' ? 'selected' : '' ?>>Approved</option>
                                    <option value="Rejected" <?= $training['approved_dept_head'] == 'Rejected' ? 'selected' : '' ?>>Rejected</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Status HRD</label>
                                <select class="form-control" name="approved_hrd">
                                    <option value="Pending" <?= $training['approved_hrd'] == 'Pending' ? 'selected' : '' ?>>Pending</option>
                                    <option value="Approved" <?= $training['approved_hrd'] == 'Approved' ? 'selected' : '' ?>>Approved</option>
                                    <option value="Rejected" <?= $training['approved_hrd'] == 'Rejected' ? 'selected' : '' ?>>Rejected</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Status GA</label>
                                <select class="form-control" name="approved_ga">
                                    <option value="Pending" <?= $training['approved_ga'] == 'Pending' ? 'selected' : '' ?>>Pending</option>
                                    <option value="Approved" <?= $training['approved_ga'] == 'Approved' ? 'selected' : '' ?>>Approved</option>
                                    <option value="Rejected" <?= $training['approved_ga'] == 'Rejected' ? 'selected' : '' ?>>Rejected</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Status FM</label>
                                <select class="form-control" name="approved_fm">
                                    <option value="Pending" <?= $training['approved_fm'] == 'Pending' ? 'selected' : '' ?>>Pending</option>
                                    <option value="Approved" <?= $training['approved_fm'] == 'Approved' ? 'selected' : '' ?>>Approved</option>
                                    <option value="Rejected" <?= $training['approved_fm'] == 'Rejected' ? 'selected' : '' ?>>Rejected</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Status DIR</label>
                                <select class="form-control" name="approved_dir">
                                    <option value="Pending" <?= $training['approved_dir'] == 'Pending' ? 'selected' : '' ?>>Pending</option>
                                    <option value="Approved" <?= $training['approved_dir'] == 'Approved' ? 'selected' : '' ?>>Approved</option>
                                    <option value="Rejected" <?= $training['approved_dir'] == 'Rejected' ? 'selected' : '' ?>>Rejected</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Penugasan</h2>
                        <div class="form-grid">
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label class="required">Penugasan</label>
                                <textarea class="form-control" name="assignment" rows="4" required><?= isset($training['assignment']) ? htmlspecialchars($training['assignment']) : '' ?></textarea>
                                <span class="help-text">Berikan tugas kepada peserta setelah training. Contoh: melakukan Sharing Session ke Team Internal</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Internal Memo</h2>
                        <div class="form-grid">
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label>Upload Internal Memo</label>
                                <input type="file" class="form-control" name="internal_memo_image" id="internal_memo_image" accept="image/jpeg,image/jpg,image/png">
                                <span class="help-text">Upload gambar internal memo dalam format JPG, JPEG, atau PNG (Maksimal 2MB)</span>

                                <?php if (!empty($training['internal_memo_image'])): ?>
                                <div class="existing-image" style="margin: 10px 0;">
                                    <p><strong>Gambar Internal Memo yang sudah diupload:</strong></p>
                                    <img src="../<?= htmlspecialchars($training['internal_memo_image']) ?>" alt="Internal Memo" style="max-width: 100%; max-height: 300px; border: 1px solid #ddd; padding: 5px;">
                                    <p class="mt-2">Upload gambar baru untuk mengganti gambar yang sudah ada.</p>
                                </div>
                                <?php endif; ?>

                                <div id="image_preview" style="margin-top: 10px; display: none;">
                                    <img id="preview_img" src="#" alt="Preview" style="max-width: 100%; max-height: 300px;">
                                    <button type="button" id="remove_image" class="btn btn-sm btn-danger" style="margin-top: 5px;">Hapus Gambar</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Komentar</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label>Komentar Dept Head</label>
                                <textarea class="form-control" name="comments_dept_head"><?= isset($training['comments_dept_head']) ? htmlspecialchars($training['comments_dept_head']) : '' ?></textarea>
                            </div>
                            <div class="form-group">
                                <label>Komentar HRD</label>
                                <textarea class="form-control" name="comments_hrd"><?= isset($training['comments_hrd']) ? htmlspecialchars($training['comments_hrd']) : '' ?></textarea>
                            </div>
                            <div class="form-group">
                                <label>Komentar GA</label>
                                <textarea class="form-control" name="comments_ga"><?= isset($training['comments_ga']) ? htmlspecialchars($training['comments_ga']) : '' ?></textarea>
                            </div>
                            <div class="form-group">
                                <label>Komentar FM</label>
                                <textarea class="form-control" name="comments_fm"><?= isset($training['comments_fm']) ? htmlspecialchars($training['comments_fm']) : '' ?></textarea>
                            </div>
                            <div class="form-group">
                                <label>Komentar DIR</label>
                                <textarea class="form-control" name="comments_dir"><?= isset($training['comments_dir']) ? htmlspecialchars($training['comments_dir']) : '' ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Edit Mode Information -->
            <div class="alert alert-info" style="margin-bottom: 20px;">
                <i class="fas fa-info-circle"></i>
                <strong>Mode Edit:</strong> Anda dapat mengedit field mana saja yang diperlukan.
                Tidak semua field harus diisi - hanya edit yang perlu diubah.
            </div>

            <div class="button-group">
                <a href="training_management.php" class="btn btn-secondary">Kembali</a>
                <button type="button" class="btn btn-primary" id="submitButton">Simpan Perubahan</button>
            </div>

            <script>
                function validateForm() {
                    const providerTypeSelect = document.getElementById('provider_type_select');
                    const providerType = providerTypeSelect ? providerTypeSelect.value : '';

                    if (providerType === 'External' || providerType === 'Eksternal') {
                        // Make sure external fields are required
                        const providerNameField = document.querySelector('input[name="provider_name"]');
                        const providerAddressField = document.querySelector('textarea[name="provider_address"]');

                        if (providerNameField) {
                            providerNameField.setAttribute('required', 'required');

                            if (!providerNameField.value.trim()) {
                                alert('Nama Provider harus diisi!');
                                providerNameField.focus();
                                return false;
                            }
                        }

                        if (providerAddressField) {
                            providerAddressField.setAttribute('required', 'required');

                            if (!providerAddressField.value.trim()) {
                                alert('Alamat Provider harus diisi!');
                                providerAddressField.focus();
                                return false;
                            }
                        }

                        // Log values for debugging
                        console.log('Validating external fields:');
                        console.log('Provider Name:', providerNameField ? providerNameField.value : 'not found');
                        console.log('Provider Address:', providerAddressField ? providerAddressField.value : 'not found');
                    }

                    return true;
                }

                // Safe way to handle CustomModal for confirmations
                function confirmRemoveTrainer(button) {
                    if (typeof window.confirmAction === 'function') {
                        window.confirmAction(
                            'Apakah Anda yakin ingin menghapus trainer ini? Tindakan ini tidak dapat dibatalkan.',
                            function() { // onYes callback
                                removeTrainer(button);
                            },
                            null, // onNo callback
                            'Konfirmasi Hapus Trainer' // title
                        );
                    } else {
                        // Fallback if confirmAction is not available
                        if (confirm('Apakah Anda yakin ingin menghapus trainer ini? Tindakan ini tidak dapat dibatalkan.')) {
                            removeTrainer(button);
                        }
                    }
                }
            </script>
        </form>
    </div>
</div>





<?php include '../config/footer.php'; ?>

    <script>
    // Global arrays to store trainers and participants
    let trainers = [];
    let participants = [];
    let searchTimeout = null;

    // Add required class to labels of required inputs
    document.addEventListener('DOMContentLoaded', function() {
        // Mark required fields
        document.querySelectorAll('input[required], select[required], textarea[required]').forEach(field => {
            const label = field.parentElement.querySelector('label');
            if (label && !label.classList.contains('required')) {
                label.classList.add('required');
            }
        });

        // Add smooth scrolling to tab navigation
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('onclick').match(/'([^']+)'/)[1];
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 100,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Initialize provider type based on database value
        const providerSelect = document.getElementById('provider_type_select');
        if (providerSelect) {
            console.log('Initial provider type:', providerSelect.value);

            // Ensure the correct fields are displayed based on the selected value
            const internalFields = document.getElementById('internalFields');
            const externalFields = document.getElementById('externalFields');

            if (internalFields && externalFields) {
                if (providerSelect.value === 'Internal') {
                    internalFields.style.display = 'block';
                    externalFields.style.display = 'none';
                } else if (providerSelect.value === 'External' || providerSelect.value === 'Eksternal') {
                    internalFields.style.display = 'none';
                    externalFields.style.display = 'block';

                    // Add required attribute to necessary external fields
                    const providerNameField = document.querySelector('#externalFields input[name="provider_name"]');
                    const providerAddressField = document.querySelector('#externalFields textarea[name="provider_address"]');
                    const trainerNameExternalField = document.querySelector('#externalFields input[name="trainer_name_external"]');
                    const additionalInfoProviderField = document.querySelector('#externalFields textarea[name="additional_info_provider"]');

                    if (providerNameField) providerNameField.setAttribute('required', 'required');
                    if (providerAddressField) providerAddressField.setAttribute('required', 'required');

                    // Log external provider field values for debugging
                    console.log('External provider fields on initialization:', {
                        providerName: providerNameField ? providerNameField.value : 'not found',
                        providerAddress: providerAddressField ? providerAddressField.value : 'not found',
                        trainerNameExternal: trainerNameExternalField ? trainerNameExternalField.value : 'not found',
                        additionalInfoProvider: additionalInfoProviderField ? additionalInfoProviderField.value : 'not found'
                    });
                }
            }
        }

        // Initialize trainer search
        initTrainerSearch();

        // Initialize participant search
        initParticipantSearch();

        // Initialize provider type toggle
        toggleProviderFields();

        // Load existing trainers into the trainers array
        loadExistingTrainers();

        // Load existing participants into the participants array
        loadExistingParticipants();
    });

    function showTab(tabId, clickEvent) {
        // Hide all tabs
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });

        // Remove active class from all buttons
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });

        // Show selected tab
        const tabElement = document.getElementById(tabId);
        if (tabElement) {
            tabElement.classList.add('active');
        }

        // Add active class to clicked button if event is provided
        if (clickEvent && clickEvent.target) {
            clickEvent.target.classList.add('active');
        } else {
            // If no event, find the button that corresponds to this tab and activate it
            const button = document.querySelector(`.tab-button[onclick*="${tabId}"]`);
            if (button) {
                button.classList.add('active');
            }
        }

        // Save active tab to session storage
        sessionStorage.setItem('activeTrainingTab', tabId);
    }

    // Restore active tab on page load
    document.addEventListener('DOMContentLoaded', function() {
        const activeTab = sessionStorage.getItem('activeTrainingTab');
        if (activeTab) {
            showTab(activeTab, null);
        }
    });

    // Toggle provider fields based on provider type
    function toggleProviderFields() {
        const providerSelect = document.getElementById('provider_type_select');
        const internalFields = document.getElementById('internalFields');
        const externalFields = document.getElementById('externalFields');

        if (!providerSelect || !internalFields || !externalFields) return;

        const providerType = providerSelect.value;

        // Hide all fields first
        internalFields.style.display = 'none';
        externalFields.style.display = 'none';

        // Show fields based on provider type
        if (providerType === 'Internal') {
            internalFields.style.display = 'block';

            // Remove required attribute from external fields
            document.querySelectorAll('#externalFields input[required], #externalFields textarea[required]').forEach(field => {
                field.removeAttribute('required');
            });
        } else if (providerType === 'External' || providerType === 'Eksternal') {
            externalFields.style.display = 'block';

            // Add required attribute to necessary external fields
            const providerNameField = document.querySelector('#externalFields input[name="provider_name"]');
            const providerAddressField = document.querySelector('#externalFields textarea[name="provider_address"]');

            if (providerNameField) {
                providerNameField.setAttribute('required', 'required');
                // Make sure the field is visible
                const nameContainer = providerNameField.closest('.form-group');
                if (nameContainer) nameContainer.style.display = 'block';
            }

            if (providerAddressField) {
                providerAddressField.setAttribute('required', 'required');
                // Make sure the field is visible
                const addressContainer = providerAddressField.closest('.form-group');
                if (addressContainer) addressContainer.style.display = 'block';
            }

            // Ensure external fields are populated with database values
            const providerNameValue = providerNameField ? providerNameField.value : '';
            const providerAddressValue = providerAddressField ? providerAddressField.value : '';
            const trainerNameExternalField = document.querySelector('#externalFields input[name="trainer_name_external"]');
            const additionalInfoProviderField = document.querySelector('#externalFields textarea[name="additional_info_provider"]');

            console.log('External provider fields:', {
                providerName: providerNameValue,
                providerAddress: providerAddressValue,
                trainerNameExternal: trainerNameExternalField ? trainerNameExternalField.value : '',
                additionalInfoProvider: additionalInfoProviderField ? additionalInfoProviderField.value : ''
            });
        }

        // Log the current provider type for debugging
        console.log('Provider type set to:', providerType);
    }

    // Initialize trainer search functionality
    function initTrainerSearch() {
        const searchInput = document.getElementById('search_trainer');
        const resultsContainer = document.getElementById('trainer_results');

        if (!searchInput || !resultsContainer) return;

        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // Hide results if query is empty
            if (query === '') {
                resultsContainer.style.display = 'none';
                return;
            }

            // Set a timeout to prevent too many requests
            searchTimeout = setTimeout(function() {
                // Make AJAX request to search employees
                fetch(`search_employees.php?search=${encodeURIComponent(query)}&limit=10`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.employees.length > 0) {
                            // Display results
                            resultsContainer.innerHTML = '';
                            resultsContainer.style.display = 'block';

                            data.employees.forEach(employee => {
                                const resultItem = document.createElement('div');
                                resultItem.className = 'search-result-item';
                                resultItem.style.padding = '10px';
                                resultItem.style.borderBottom = '1px solid #eee';
                                resultItem.style.cursor = 'pointer';

                                resultItem.innerHTML = `
                                    <div style="font-weight: 600; color: #333; font-size: 14px;">${employee.nama} <span style="color: #666; font-weight: normal;">(${employee.nik})</span></div>
                                    <div style="color: #666; font-size: 13px; margin-top: 3px;">
                                        <span style="color: #BF0000; font-weight: 500;">${employee.jabatan}</span> - ${employee.dept} / ${employee.bagian}
                                    </div>
                                `;

                                resultItem.addEventListener('click', function() {
                                    addTrainer({
                                        nama: employee.nama,
                                        nik: employee.nik,
                                        jabatan: employee.jabatan,
                                        departemen: employee.dept,
                                        bagian: employee.bagian
                                    });

                                    // Clear search and hide results
                                    searchInput.value = '';
                                    resultsContainer.style.display = 'none';
                                });

                                // Add hover effect
                                resultItem.addEventListener('mouseover', function() {
                                    this.style.backgroundColor = '#f5f5f5';
                                    this.style.transform = 'translateX(3px)';
                                    this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.05)';
                                });

                                resultItem.addEventListener('mouseout', function() {
                                    this.style.backgroundColor = 'transparent';
                                    this.style.transform = 'translateX(0)';
                                    this.style.boxShadow = 'none';
                                });

                                resultsContainer.appendChild(resultItem);
                            });
                        } else {
                            // No results
                            resultsContainer.innerHTML = '<div style="padding: 10px; color: #666;">Tidak ada hasil yang ditemukan</div>';
                            resultsContainer.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error searching employees:', error);
                        resultsContainer.innerHTML = '<div style="padding: 10px; color: red;">Terjadi kesalahan saat mencari karyawan</div>';
                        resultsContainer.style.display = 'block';
                    });
            }, 300);
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(event) {
            if (!searchInput.contains(event.target) && !resultsContainer.contains(event.target)) {
                resultsContainer.style.display = 'none';
            }
        });
    }

    // Add trainer to the list
    function addTrainer(trainer) {
        // Check if trainer already exists
        if (trainers.some(t => t.nik === trainer.nik)) {
            alert('Trainer ini sudah ditambahkan!');
            return;
        }

        trainers.push(trainer);
        updateTrainerTable();
    }

    // Add trainer manually
    function addTrainerManual() {
        const tbody = document.getElementById('trainer_list');

        // Clear empty state message if it exists
        if (trainers.length === 0) {
            tbody.innerHTML = '';
        }

        const index = tbody.getElementsByTagName('tr').length + 1;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="text-align: center; font-weight: 600;">${index}</td>
            <td><input type="text" class="form-control" name="trainer_name_internal[]" placeholder="Nama lengkap trainer" required></td>
            <td><input type="text" class="form-control" name="trainer_nik_internal[]" placeholder="NIK" required></td>
            <td><input type="text" class="form-control" name="trainer_department_internal[]" placeholder="Departemen" required></td>
            <td><input type="text" class="form-control" name="trainer_sub_department_internal[]" placeholder="Bagian" required></td>
            <td><input type="text" class="form-control" name="trainer_position_internal[]" placeholder="Jabatan" required></td>
            <td style="text-align: center;"><button type="button" class="btn btn-sm btn-danger" onclick="removeTrainer(this)" style="border-radius: 4px;"><i class="fas fa-trash-alt"></i> Hapus</button></td>
        `;

        tbody.appendChild(row);

        // Focus on the first input field
        const firstInput = row.querySelector('input');
        if (firstInput) {
            firstInput.focus();
        }
    }

    // Function to remove trainer from the list
    function removeTrainer(button) {
        if (typeof button === 'number') {
            // If called with index
            trainers.splice(button, 1);
            updateTrainerTable();
        } else {
            // If called with button element
            const row = button.closest('tr');
            if (row) {
                row.remove();

                // Update row numbers
                const rows = document.getElementById('trainer_list').getElementsByTagName('tr');
                for (let i = 0; i < rows.length; i++) {
                    rows[i].getElementsByTagName('td')[0].innerText = i + 1;
                }
            }
        }

        // Show success message using alert instead of CustomModal
        alert('Trainer berhasil dihapus');
    }

    // Update trainer table
    function updateTrainerTable() {
        const tbody = document.getElementById('trainer_list');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (trainers.length === 0) {
            // Display empty state message
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td colspan="7" style="text-align: center; padding: 20px; color: #666;">Belum ada trainer yang ditambahkan</td>`;
            tbody.appendChild(emptyRow);
            return;
        }

        trainers.forEach((trainer, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td style="text-align: center; font-weight: 600;">${index + 1}</td>
                <td><span style="font-weight: 600; color: #333;">${trainer.nama}</span><input type="hidden" name="trainer_name_internal[]" value="${trainer.nama}"></td>
                <td>${trainer.nik}<input type="hidden" name="trainer_nik_internal[]" value="${trainer.nik}"></td>
                <td>${trainer.departemen}<input type="hidden" name="trainer_department_internal[]" value="${trainer.departemen}"></td>
                <td>${trainer.bagian}<input type="hidden" name="trainer_sub_department_internal[]" value="${trainer.bagian}"></td>
                <td>${trainer.jabatan}<input type="hidden" name="trainer_position_internal[]" value="${trainer.jabatan}"></td>
                <td style="text-align: center;"><button type="button" class="btn btn-sm btn-danger" onclick="removeTrainer(${index})" style="border-radius: 4px;"><i class="fas fa-trash-alt"></i> Hapus</button></td>
            `;

            tbody.appendChild(row);
        });
    }

    // Load existing trainers into the trainers array
    function loadExistingTrainers() {
        // First clear the trainers array
        trainers = [];

        // Get all trainer rows from the table
        const trainerRows = document.querySelectorAll('#trainer_list tr');

        if (trainerRows.length > 0) {
            console.log('Found existing trainer rows:', trainerRows.length);

            // Process each row to extract trainer data
            trainerRows.forEach(row => {
                const cells = row.getElementsByTagName('td');
                if (cells.length >= 6) {
                    const nameInput = cells[1].querySelector('input[name="trainer_name_internal[]"]');
                    const nikInput = cells[2].querySelector('input[name="trainer_nik_internal[]"]');
                    const deptInput = cells[3].querySelector('input[name="trainer_department_internal[]"]');
                    const subDeptInput = cells[4].querySelector('input[name="trainer_sub_department_internal[]"]');
                    const positionInput = cells[5].querySelector('input[name="trainer_position_internal[]"]');

                    if (nameInput && nikInput && deptInput && subDeptInput && positionInput) {
                        trainers.push({
                            nama: nameInput.value,
                            nik: nikInput.value,
                            departemen: deptInput.value,
                            bagian: subDeptInput.value,
                            jabatan: positionInput.value
                        });
                    }
                }
            });

            console.log('Loaded trainers:', trainers);
        } else {
            console.log('No existing trainer rows found');
        }
    }

    // Initialize participant search functionality
    function initParticipantSearch() {
        const searchInput = document.getElementById('search_participant');
        const resultsContainer = document.getElementById('participant_results');

        if (!searchInput || !resultsContainer) return;

        // Load existing participants from the database
        loadExistingParticipants();

        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // Hide results if query is empty
            if (query === '') {
                resultsContainer.style.display = 'none';
                return;
            }

            // Set a timeout to prevent too many requests
            searchTimeout = setTimeout(function() {
                // Make AJAX request to search employees
                fetch(`search_employees.php?search=${encodeURIComponent(query)}&limit=10`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.employees.length > 0) {
                            // Display results
                            resultsContainer.innerHTML = '';
                            resultsContainer.style.display = 'block';

                            data.employees.forEach(employee => {
                                const resultItem = document.createElement('div');
                                resultItem.className = 'search-result-item';
                                resultItem.style.padding = '10px';
                                resultItem.style.borderBottom = '1px solid #eee';
                                resultItem.style.cursor = 'pointer';

                                resultItem.innerHTML = `
                                    <div style="font-weight: 600; color: #333; font-size: 14px;">${employee.nama} <span style="color: #666; font-weight: normal;">(${employee.nik})</span></div>
                                    <div style="color: #666; font-size: 13px; margin-top: 3px;">
                                        <span style="color: #BF0000; font-weight: 500;">${employee.jabatan}</span> - ${employee.dept} / ${employee.bagian}
                                    </div>
                                `;

                                resultItem.addEventListener('click', function() {
                                    addParticipant({
                                        nama: employee.nama,
                                        nik: employee.nik,
                                        jabatan: employee.jabatan,
                                        departemen: employee.dept,
                                        bagian: employee.bagian,
                                        isNew: true
                                    });

                                    // Clear search and hide results
                                    searchInput.value = '';
                                    resultsContainer.style.display = 'none';
                                });

                                // Add hover effect
                                resultItem.addEventListener('mouseover', function() {
                                    this.style.backgroundColor = '#f5f5f5';
                                    this.style.transform = 'translateX(3px)';
                                    this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.05)';
                                });

                                resultItem.addEventListener('mouseout', function() {
                                    this.style.backgroundColor = 'transparent';
                                    this.style.transform = 'translateX(0)';
                                    this.style.boxShadow = 'none';
                                });

                                resultsContainer.appendChild(resultItem);
                            });
                        } else {
                            // No results
                            resultsContainer.innerHTML = '<div style="padding: 10px; color: #666;">Tidak ada hasil yang ditemukan</div>';
                            resultsContainer.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error searching employees:', error);
                        resultsContainer.innerHTML = '<div style="padding: 10px; color: red;">Terjadi kesalahan saat mencari karyawan</div>';
                        resultsContainer.style.display = 'block';
                    });
            }, 300);
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(event) {
            if (!searchInput.contains(event.target) && !resultsContainer.contains(event.target)) {
                resultsContainer.style.display = 'none';
            }
        });
    }

    // Load existing participants from the database
    function loadExistingParticipants() {
        // Clear the participants array
        participants = [];

        // Get all participant rows from the table
        const participantRows = document.querySelectorAll('#participant_list tr');

        // Skip if there's only one row with the "no participants" message
        if (participantRows.length === 1 && participantRows[0].cells.length === 1 &&
            participantRows[0].cells[0].colSpan === 7) {
            console.log('No existing participants found');
            return;
        }

        participantRows.forEach(row => {
            const cells = row.getElementsByTagName('td');
            if (cells.length >= 6) {
                const nameInput = cells[1].querySelector('input[name="participant_name[]"]');
                const nikInput = cells[2].querySelector('input[name="participant_nik[]"]');
                const deptInput = cells[3].querySelector('input[name="participant_department[]"]');
                const subDeptInput = cells[4].querySelector('input[name="participant_sub_department[]"]');
                const positionInput = cells[5].querySelector('input[name="participant_position[]"]');

                if (nameInput && nikInput && deptInput && subDeptInput && positionInput) {
                    // Get the participant ID if it exists (for database records)
                    const deleteButton = cells[6].querySelector('button');
                    let participantId = null;

                    if (deleteButton) {
                        const onclickAttr = deleteButton.getAttribute('onclick');
                        if (onclickAttr && onclickAttr.includes('removeParticipantFromDB')) {
                            const match = onclickAttr.match(/removeParticipantFromDB\((\d+)\)/);
                            if (match && match[1]) {
                                participantId = parseInt(match[1]);
                            }
                        }
                    }

                    participants.push({
                        id: participantId,
                        nama: nameInput.value,
                        nik: nikInput.value,
                        departemen: deptInput.value,
                        bagian: subDeptInput.value,
                        jabatan: positionInput.value,
                        isNew: false
                    });
                }
            }
        });

        console.log('Loaded participants:', participants);
    }

    // Add participant to the list
    function addParticipant(participant) {
        // Check if participant already exists
        if (participants.some(p => p.nik === participant.nik)) {
            alert('Peserta ini sudah ditambahkan!');
            return;
        }

        participants.push(participant);
        updateParticipantTable();
    }

    // Add participant manually
    function addParticipantManual() {
        const tbody = document.getElementById('participant_list');

        // Clear empty state message if it exists
        if (participants.length === 0) {
            tbody.innerHTML = '';
        }

        const index = tbody.getElementsByTagName('tr').length + 1;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="text-align: center; font-weight: 600;">${index}</td>
            <td><input type="text" class="form-control" name="new_participant_name[]" placeholder="Nama lengkap peserta" required></td>
            <td><input type="text" class="form-control" name="new_participant_nik[]" placeholder="NIK" required></td>
            <td><input type="text" class="form-control" name="new_participant_position[]" placeholder="Jabatan" required></td>
            <td><input type="text" class="form-control" name="new_participant_sub_department[]" placeholder="Bagian" required></td>
            <td><input type="text" class="form-control" name="new_participant_department[]" placeholder="Departemen" required></td>
            <td style="text-align: center;"><button type="button" class="btn btn-sm btn-danger" onclick="removeParticipant(this)" style="border-radius: 4px;"><i class="fas fa-trash-alt"></i> Hapus</button></td>
        `;

        tbody.appendChild(row);

        // Focus on the first input field
        const firstInput = row.querySelector('input');
        if (firstInput) {
            firstInput.focus();
        }
    }

    // Function to remove participant from the list
    function removeParticipant(button) {
        if (confirm('Apakah Anda yakin ingin menghapus peserta ini? Tindakan ini tidak dapat dibatalkan.')) {
            if (typeof button === 'number') {
                // If called with index
                participants.splice(button, 1);
                updateParticipantTable();
            } else {
                // If called with button element
                const row = button.closest('tr');
                if (row) {
                    row.remove();

                    // Update row numbers
                    const rows = document.getElementById('participant_list').getElementsByTagName('tr');
                    for (let i = 0; i < rows.length; i++) {
                        rows[i].getElementsByTagName('td')[0].innerText = i + 1;
                    }
                }
            }

            // Show success message using alert instead of CustomModal
            alert('Peserta berhasil dihapus');
        }
    }

    // Function to delete participant from database
    function removeParticipantFromDB(participantId) {
        if (confirm('Apakah Anda yakin ingin menghapus peserta ini? Tindakan ini tidak dapat dibatalkan.')) {
            // Send AJAX request to delete participant
            console.log('Deleting participant with ID:', participantId);

            fetch('delete_participant.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `participant_id=${participantId}`
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    // Remove from participants array
                    const index = participants.findIndex(p => p.id === participantId);
                    if (index !== -1) {
                        participants.splice(index, 1);
                    }

                    // Update the table
                    updateParticipantTable();

                    // Show success message
                    alert('Peserta berhasil dihapus');
                } else {
                    alert('Gagal menghapus peserta: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error deleting participant:', error);
                alert('Terjadi kesalahan saat menghapus peserta: ' + error.message);
            });
        }
    }



    // Show notification (legacy function, use alert instead)
    function showNotification(type, message) {
        alert(message);
    }

    // Function to toggle date fields based on selected date type
    function toggleDateFields(dateType) {
        // Get all date field containers
        const regularDateField = document.getElementById('regular_date_field');
        const fixedDateField = document.getElementById('fixed_date_field');
        const dateRangeFields = document.getElementById('date_range_fields');

        // Get all date input fields
        const regularDateInput = document.getElementById('training_date');
        const fixedDateInput = document.getElementById('training_date_fixed');
        const startDateInput = document.getElementById('training_date_start');
        const endDateInput = document.getElementById('training_date_end');

        // Hide all date fields first
        regularDateField.style.display = 'none';
        fixedDateField.style.display = 'none';
        dateRangeFields.style.display = 'none';

        // Remove required attribute from all date inputs
        regularDateInput.removeAttribute('required');
        fixedDateInput.removeAttribute('required');
        startDateInput.removeAttribute('required');
        endDateInput.removeAttribute('required');

        // Show the selected date field and set required attribute
        if (dateType === 'regular') {
            regularDateField.style.display = 'block';
            regularDateInput.setAttribute('required', 'required');
        } else if (dateType === 'fixed') {
            fixedDateField.style.display = 'block';
            fixedDateInput.setAttribute('required', 'required');
        } else if (dateType === 'range') {
            dateRangeFields.style.display = 'grid';
            startDateInput.setAttribute('required', 'required');
            endDateInput.setAttribute('required', 'required');
        }

        // Update label classes to show required indicator
        const regularDateLabel = regularDateField.querySelector('label');
        const fixedDateLabel = fixedDateField.querySelector('label');
        const startDateLabel = dateRangeFields.querySelector('.form-group:first-child label');
        const endDateLabel = dateRangeFields.querySelector('.form-group:last-child label');

        // Remove required class from all labels
        regularDateLabel.classList.remove('required');
        fixedDateLabel.classList.remove('required');
        startDateLabel.classList.remove('required');
        endDateLabel.classList.remove('required');

        // Add required class to the active field's label
        if (dateType === 'regular') {
            regularDateLabel.classList.add('required');
        } else if (dateType === 'fixed') {
            fixedDateLabel.classList.add('required');
        } else if (dateType === 'range') {
            startDateLabel.classList.add('required');
            endDateLabel.classList.add('required');
        }
    }

    // Image preview functionality
    function setupImagePreview() {
        const imageInput = document.getElementById('internal_memo_image');
        const imagePreview = document.getElementById('image_preview');
        const previewImg = document.getElementById('preview_img');
        const removeImageBtn = document.getElementById('remove_image');

        if (imageInput && imagePreview && previewImg && removeImageBtn) {
            // Show preview when file is selected
            imageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const file = this.files[0];

                    // Validate file type
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
                    if (!allowedTypes.includes(file.type)) {
                        alert('Format file tidak valid. Hanya file JPG, JPEG, dan PNG yang diperbolehkan.');
                        this.value = '';
                        imagePreview.style.display = 'none';
                        return;
                    }

                    // Validate file size (max 2MB)
                    if (file.size > 2 * 1024 * 1024) {
                        alert('Ukuran file terlalu besar. Maksimal 2MB.');
                        this.value = '';
                        imagePreview.style.display = 'none';
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        previewImg.src = e.target.result;
                        imagePreview.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                } else {
                    imagePreview.style.display = 'none';
                }
            });

            // Remove image when button is clicked
            removeImageBtn.addEventListener('click', function() {
                imageInput.value = '';
                imagePreview.style.display = 'none';
            });
        }
    }

    // Fix form fields on page load and handle form submission
    window.addEventListener('load', function() {
        // Set up image preview
        setupImagePreview();

        // Initialize date fields based on selected date type
        const selectedDateTypeElement = document.querySelector('input[name="date_type"]:checked');
        const selectedDateType = selectedDateTypeElement ? selectedDateTypeElement.value : 'regular';
        toggleDateFields(selectedDateType);

        // Set up the submit button handler
        const submitButton = document.getElementById('submitButton');
        const form = document.getElementById('trainingForm');

        if (submitButton && form) {
            submitButton.addEventListener('click', function() {
                // Flexible validation for edit mode - only validate critical business logic
                const providerTypeSelect = document.getElementById('provider_type_select');
                const providerType = providerTypeSelect ? providerTypeSelect.value : '';
                let isValid = true;
                let warnings = [];

                // Only validate external provider fields if provider type is External AND fields are being used
                if (providerType === 'External' || providerType === 'Eksternal') {
                    const providerNameField = document.querySelector('input[name="provider_name"]');
                    const providerAddressField = document.querySelector('textarea[name="provider_address"]');

                    const providerName = providerNameField ? providerNameField.value.trim() : '';
                    const providerAddress = providerAddressField ? providerAddressField.value.trim() : '';

                    // Only warn if external is selected but no provider info
                    if (!providerName && !providerAddress) {
                        warnings.push('Provider eksternal dipilih tapi informasi provider kosong');
                    }
                }

                // Validate date range logic (if both dates are filled)
                const dateTypeElement = document.querySelector('input[name="date_type"]:checked');
                const dateType = dateTypeElement ? dateTypeElement.value : 'regular';

                if (dateType === 'range') {
                    const trainingDateStartField = document.querySelector('input[name="training_date_start"]');
                    const trainingDateEndField = document.querySelector('input[name="training_date_end"]');
                    const trainingDateStart = trainingDateStartField ? trainingDateStartField.value.trim() : '';
                    const trainingDateEnd = trainingDateEndField ? trainingDateEndField.value.trim() : '';

                    // Only validate if both dates are filled
                    if (trainingDateStart && trainingDateEnd && trainingDateStart > trainingDateEnd) {
                        alert('Tanggal Mulai tidak boleh lebih besar dari Tanggal Selesai!');
                        if (trainingDateStartField) trainingDateStartField.focus();
                        isValid = false;
                    }
                }

                // Validate time logic (if both times are filled)
                const trainingTimeStartField = document.querySelector('input[name="training_time_start"]');
                const trainingTimeEndField = document.querySelector('input[name="training_time_end"]');

                const trainingTimeStart = trainingTimeStartField ? trainingTimeStartField.value.trim() : '';
                const trainingTimeEnd = trainingTimeEndField ? trainingTimeEndField.value.trim() : '';

                // Flexible time validation - allow same time or empty fields
                // No strict validation to allow flexibility in scheduling
                if (trainingTimeStart && trainingTimeEnd && trainingTimeStart > trainingTimeEnd) {
                    // Only show warning, don't block submission
                    console.warn('Note: Training start time is after end time');
                    // Optional: Show non-blocking notification
                    // showNotification('Perhatian: Waktu mulai setelah waktu selesai', 'warning');
                }

                // Show warnings if any (but don't block submission)
                if (warnings.length > 0 && isValid) {
                    const warningMessage = 'Peringatan:\n' + warnings.join('\n') + '\n\nLanjutkan menyimpan?';
                    if (!confirm(warningMessage)) {
                        return;
                    }
                }

                // If validation passes, submit the form
                if (isValid) {
                    form.submit();
                }
            });
        }

        // Fix for required fields not being focusable
        setTimeout(function() {
            // Make sure all required fields are properly initialized
            if (form) {
                const requiredFields = form.querySelectorAll('[required]');
                requiredFields.forEach(field => {
                    // Make sure the field is visible if it's required
                    const fieldContainer = field.closest('.form-group');
                    if (fieldContainer) {
                        fieldContainer.style.display = 'block';
                    }

                    // If the field is empty, set a default value
                    if (field.tagName === 'SELECT' && !field.value) {
                        // For select elements, select the first option
                        if (field.options.length > 0) {
                            field.selectedIndex = 0;
                        }
                    }
                });
            }
        }, 500);
    });

    // Update participant table
    function updateParticipantTable() {
        const tbody = document.getElementById('participant_list');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (participants.length === 0) {
            // Display empty state message
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td colspan="7" style="text-align: center; padding: 20px; color: #666;">Belum ada peserta yang ditambahkan</td>`;
            tbody.appendChild(emptyRow);
            return;
        }

        participants.forEach((participant, index) => {
            const row = document.createElement('tr');

            // Different handling for existing vs new participants
            if (participant.id && !participant.isNew) {
                // Existing participant from database
                row.innerHTML = `
                    <td style="text-align: center; font-weight: 600;">${index + 1}</td>
                    <td><span style="font-weight: 600; color: #333;">${participant.nama}</span>
                    <input type="hidden" name="participant_name[]" value="${participant.nama}">
                    <input type="hidden" name="participant_id[]" value="${participant.id}"></td>
                    <td>${participant.nik}<input type="hidden" name="participant_nik[]" value="${participant.nik}"></td>
                    <td>${participant.jabatan}<input type="hidden" name="participant_position[]" value="${participant.jabatan}"></td>
                    <td>${participant.bagian}<input type="hidden" name="participant_sub_department[]" value="${participant.bagian}"></td>
                    <td>${participant.departemen}<input type="hidden" name="participant_department[]" value="${participant.departemen}"></td>
                    <td style="text-align: center;"><button type="button" class="btn btn-sm btn-danger" onclick="removeParticipantFromDB(${participant.id})" style="border-radius: 4px;"><i class="fas fa-trash-alt"></i> Hapus</button></td>
                `;
            } else {
                // New participant
                row.innerHTML = `
                    <td style="text-align: center; font-weight: 600;">${index + 1}</td>
                    <td><span style="font-weight: 600; color: #333;">${participant.nama}</span>
                    <input type="hidden" name="new_participant_name[]" value="${participant.nama}"></td>
                    <td>${participant.nik}<input type="hidden" name="new_participant_nik[]" value="${participant.nik}"></td>
                    <td>${participant.jabatan}<input type="hidden" name="new_participant_position[]" value="${participant.jabatan}"></td>
                    <td>${participant.bagian}<input type="hidden" name="new_participant_sub_department[]" value="${participant.bagian}"></td>
                    <td>${participant.departemen}<input type="hidden" name="new_participant_department[]" value="${participant.departemen}"></td>
                    <td style="text-align: center;"><button type="button" class="btn btn-sm btn-danger" onclick="removeParticipant(${index})" style="border-radius: 4px;"><i class="fas fa-trash-alt"></i> Hapus</button></td>
                `;
            }

            tbody.appendChild(row);
        });
    }

    // Auto-switch to participants tab if tab parameter is present
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab');

        if (tab === 'participants') {
            // Switch to participants tab
            const participantsTab = document.querySelector('[data-tab="participants"]');
            if (participantsTab) {
                participantsTab.click();

                // Scroll to participants section after a short delay
                setTimeout(() => {
                    const participantsSection = document.getElementById('participants');
                    if (participantsSection) {
                        participantsSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }, 300);
            }

            // Clean URL by removing tab parameter
            const newUrl = window.location.pathname + '?id=' + urlParams.get('id');
            history.replaceState(null, null, newUrl);
        }
    });
    </script>
</body>
</html>

