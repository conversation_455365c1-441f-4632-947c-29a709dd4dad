Stack trace:
Frame         Function      Args
0007FFFFB730  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA630) msys-2.0.dll+0x1FEBA
0007FFFFB730  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA08) msys-2.0.dll+0x67F9
0007FFFFB730  000210046832 (000210285FF9, 0007FFFFB5E8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB730  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB730  0002100690B4 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA10  00021006A49D (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9DBE50000 ntdll.dll
7FF9DACF0000 KERNEL32.DLL
7FF9D9310000 KERNELBASE.dll
7FF9DB660000 USER32.dll
7FF9D9A80000 win32u.dll
7FF9DAA50000 GDI32.dll
7FF9D96F0000 gdi32full.dll
7FF9D99E0000 msvcp_win.dll
7FF9D8F40000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9DB820000 advapi32.dll
7FF9DB260000 msvcrt.dll
7FF9DB160000 sechost.dll
7FF9D9AB0000 bcrypt.dll
7FF9DB530000 RPCRT4.dll
7FF9D8660000 CRYPTBASE.DLL
7FF9D9290000 bcryptPrimitives.dll
7FF9D9C40000 IMM32.DLL
