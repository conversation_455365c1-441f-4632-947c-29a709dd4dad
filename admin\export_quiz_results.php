<?php
/**
 * Export Quiz Results to Excel
 * This script exports quiz results to Excel format using PhpSpreadsheet
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Check if quiz ID is provided
$quiz_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($quiz_id <= 0) {
    header('Location: manage_classes.php');
    exit();
}

// Get quiz information
$quiz_query = "SELECT q.*, c.title as class_title, c.id as class_id, t.training_topic
              FROM training_quizzes q
              JOIN training_classes c ON q.class_id = c.id
              JOIN training_submissions t ON c.training_id = t.id
              WHERE q.id = ?";
$stmt = $conn->prepare($quiz_query);
$stmt->bind_param("i", $quiz_id);
$stmt->execute();
$result = $stmt->get_result();
$quiz = $result->fetch_assoc();
$stmt->close();

if (!$quiz) {
    header('Location: manage_classes.php');
    exit();
}

// Get all attempts for this quiz
$attempts_query = "SELECT a.*, u.name, u.email, u.nik,
                  (SELECT COUNT(*) FROM training_quiz_answers ans WHERE ans.attempt_id = a.id AND ans.is_correct = 1) as correct_answers,
                  (SELECT COUNT(*) FROM training_quiz_answers ans WHERE ans.attempt_id = a.id) as total_answers
                  FROM training_quiz_attempts a
                  JOIN users u ON a.user_id = u.id
                  WHERE a.quiz_id = ? AND (a.status = 'completed' OR a.status = 'graded')
                  ORDER BY a.score DESC, a.end_time ASC";
$stmt = $conn->prepare($attempts_query);
$stmt->bind_param("i", $quiz_id);
$stmt->execute();
$attempts_result = $stmt->get_result();
$attempts = [];
while ($row = $attempts_result->fetch_assoc()) {
    $attempts[] = $row;
}
$stmt->close();

// Require Composer's autoloader
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// Create new Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set document properties
$spreadsheet->getProperties()
    ->setCreator('Training System')
    ->setLastModifiedBy('Training System')
    ->setTitle('Hasil Kuis - ' . $quiz['title'])
    ->setSubject('Hasil Kuis')
    ->setDescription('Ekspor hasil kuis dari sistem pelatihan');

// Set column headers style
$headerStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FFFFFF'],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => '4E73DF'],
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
        ],
    ],
];

// Set quiz information
$sheet->setCellValue('A1', 'Informasi Kuis');
$sheet->mergeCells('A1:B1');
$sheet->setCellValue('A2', 'Judul Kuis:')
      ->setCellValue('B2', $quiz['title'])
      ->setCellValue('A3', 'Kelas:')
      ->setCellValue('B3', $quiz['class_title'])
      ->setCellValue('A4', 'Topik:')
      ->setCellValue('B4', $quiz['training_topic']);

if ($quiz['passing_score']) {
    $sheet->setCellValue('A5', 'Nilai Kelulusan:')
          ->setCellValue('B5', $quiz['passing_score'] . '%');
}

// Style the quiz information section
$sheet->getStyle('A1')->getFont()->setBold(true);
$sheet->getStyle('A2:A5')->getFont()->setBold(true);

// Add some space before the results table
$startRow = 7;

// Set column headers
$headers = [
    'A' => 'No',
    'B' => 'Nama Peserta',
    'C' => 'NIK',
    'D' => 'Email',
    'E' => 'Nilai',
    'F' => 'Jawaban Benar',
    'G' => 'Total Soal',
    'H' => 'Waktu Mulai',
    'I' => 'Waktu Selesai',
    'J' => 'Durasi',
    'K' => 'Status'
];

foreach ($headers as $column => $header) {
    $sheet->setCellValue($column . $startRow, $header);
}

// Apply header style
$sheet->getStyle('A' . $startRow . ':K' . $startRow)->applyFromArray($headerStyle);

// Add data rows
$row = $startRow + 1;
$no = 1;
foreach ($attempts as $attempt) {
    $start = new DateTime($attempt['start_time']);
    $end = new DateTime($attempt['end_time']);
    $interval = $start->diff($end);
    
    $duration = '';
    if ($interval->h > 0) {
        $duration = $interval->format('%h jam %i menit');
    } else {
        $duration = $interval->format('%i menit %s detik');
    }

    $status = ($quiz['passing_score'] && $attempt['score'] >= $quiz['passing_score']) ? 'Lulus' : 'Tidak Lulus';

    $sheet->setCellValue('A' . $row, $no)
          ->setCellValue('B' . $row, $attempt['name'])
          ->setCellValue('C' . $row, $attempt['nik'])
          ->setCellValue('D' . $row, $attempt['email'])
          ->setCellValue('E' . $row, $attempt['score'] . '%')
          ->setCellValue('F' . $row, $attempt['correct_answers'])
          ->setCellValue('G' . $row, $attempt['total_answers'])
          ->setCellValue('H' . $row, $start->format('d M Y H:i'))
          ->setCellValue('I' . $row, $end->format('d M Y H:i'))
          ->setCellValue('J' . $row, $duration)
          ->setCellValue('K' . $row, $status);

    // Style the status cell based on pass/fail
    $statusColor = ($status === 'Lulus') ? '28a745' : 'dc3545';
    $sheet->getStyle('K' . $row)
          ->getFill()
          ->setFillType(Fill::FILL_SOLID)
          ->getStartColor()
          ->setRGB($statusColor);
    $sheet->getStyle('K' . $row)
          ->getFont()
          ->setColor(new \PhpOffice\PhpSpreadsheet\Style\Color('FFFFFF'));

    $row++;
    $no++;
}

// Auto-size columns
foreach (range('A', 'K') as $column) {
    $sheet->getColumnDimension($column)->setAutoSize(true);
}

// Set borders for the data
$sheet->getStyle('A' . $startRow . ':K' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

// Center align specific columns
$sheet->getStyle('A' . $startRow . ':A' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
$sheet->getStyle('E' . $startRow . ':K' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

// Create the Excel file
$writer = new Xlsx($spreadsheet);

// Set the filename
$filename = 'Hasil_Kuis_' . str_replace(' ', '_', $quiz['title']) . '_' . date('Y-m-d_H-i-s') . '.xlsx';

// Set headers for download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');

// Save file to PHP output
$writer->save('php://output');
exit;