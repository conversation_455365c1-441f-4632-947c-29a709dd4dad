<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi variabel untuk search, sort dan pagination
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sort_column = isset($_GET['sort']) ? $_GET['sort'] : 'id';
$sort_order = isset($_GET['order']) && $_GET['order'] == 'desc' ? 'DESC' : 'ASC';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10; // Jumlah data per halaman
$offset = ($page - 1) * $limit;

// Function to build multi-search WHERE clause (same as in search_users.php)
function buildMultiSearchQuery($search_terms) {
    if (empty($search_terms)) {
        return ["1=1", []];
    }

    $where_conditions = [];
    $params = [];

    foreach ($search_terms as $term) {
        if (trim($term) === '') continue;

        $term_param = "%" . trim($term) . "%";
        $where_conditions[] = "(u.name LIKE ? OR u.email LIKE ? OR u.nik LIKE ? OR
                               u.dept LIKE ? OR u.bagian LIKE ? OR u.jabatan LIKE ? OR
                               r.role_name LIKE ?)";

        // Add 7 parameters for each search term (one for each field)
        for ($i = 0; $i < 7; $i++) {
            $params[] = $term_param;
        }
    }

    $where_clause = empty($where_conditions) ? "1=1" : "(" . implode(" AND ", $where_conditions) . ")";
    return [$where_clause, $params];
}

// Parse search input for multiple terms
$search_terms = [];
if (!empty($search)) {
    // Split by spaces and commas, filter empty terms
    $search_terms = array_filter(
        array_map('trim', preg_split('/[\s,]+/', $search)),
        function($term) { return $term !== ''; }
    );
}

// Build WHERE clause and parameters for multi-search
list($where_clause, $search_params) = buildMultiSearchQuery($search_terms);

// Query untuk menghitung total data
$count_query = "SELECT COUNT(*) as total FROM users u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE $where_clause";

$stmt_count = $conn->prepare($count_query);
if (!empty($search_params)) {
    $param_types = str_repeat('s', count($search_params));
    $stmt_count->bind_param($param_types, ...$search_params);
}
$stmt_count->execute();
$total_records = $stmt_count->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total_records / $limit);

// Query untuk mengambil data dengan pagination
// Prioritaskan user dengan is_active=1 di atas
$query = "SELECT u.id, u.name, u.email, u.nik, r.role_name, u.dept, u.bagian, u.jabatan, u.is_active
          FROM users u
          LEFT JOIN roles r ON u.role_id = r.id
          WHERE $where_clause
          ORDER BY u.is_active DESC, $sort_column $sort_order
          LIMIT ? OFFSET ?";

$stmt = $conn->prepare($query);
$all_params = array_merge($search_params, [$limit, $offset]);
$param_types = str_repeat('s', count($search_params)) . 'ii';
$stmt->bind_param($param_types, ...$all_params);
$stmt->execute();
$result = $stmt->get_result();

$users = [];
while ($row = $result->fetch_assoc()) {
    $users[] = $row;
}

// Tambahkan data pagination ke response
$response_data = [
    'users' => $users,
    'pagination' => [
        'current_page' => $page,
        'total_pages' => $total_pages,
        'total_records' => $total_records,
        'limit' => $limit
    ]
];
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    .jarak-form {
        height: 100px;
    }

    .container-form {
        padding: 20px;
        max-width: 1400px;
        margin: 0 auto;
    }

    .form-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .header-buttons {
        display: flex;
        gap: 10px;
    }

    .add-user-btn {
        background: #4CAF50;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .add-user-btn:hover {
        background: #45a049;
    }

    .delete-inactive-btn {
        background: #cc0000;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .delete-inactive-btn:hover {
        background: #a50000;
    }

    .search-container {
        margin-bottom: 20px;
    }

    .search-input {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
        margin-bottom: 8px;
    }

    .search-help {
        margin-bottom: 8px;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 4px;
        border-left: 3px solid #17a2b8;
    }

    .search-terms-display {
        padding: 6px 12px;
        background: #e7f3ff;
        border-radius: 4px;
        border-left: 3px solid #007bff;
        margin-bottom: 10px;
    }

    .search-term-badge {
        display: inline-block;
        background: #007bff;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        margin: 2px;
    }

    /* User status styling */
    .user-row-active {
        background-color: #f8fff8 !important;
        border-left: 3px solid #28a745;
    }

    .user-row-inactive {
        background-color: #fff8f8 !important;
        border-left: 3px solid #dc3545;
        opacity: 0.7;
    }

    .status-badge {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-badge.active {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-badge.inactive {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    th {
        background: #f8f9fa;
        padding: 15px;
        text-align: left;
        font-weight: 600;
        color: #333;
        position: relative;
        white-space: nowrap;
        overflow: hidden;
    }

    th .id-header {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    td {
        padding: 12px 15px;
        border-bottom: 1px solid #eee;
        overflow: hidden;
    }

    .status-toggle {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
    }

    .status-toggle input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 24px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #4CAF50;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    .action-buttons {
        display: flex;
        gap: 8px;
    }

    .btn-edit, .btn-delete {
        padding: 6px 12px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 14px;
    }

    .action-buttons a {
        padding: 6px 8px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .action-buttons a:hover {
        background: rgba(0,0,0,0.1);
        transform: scale(1.1);
    }

    .btn-edit {
        background: #4CAF50;
        color: white;
    }

    .btn-delete {
        background: #cc0000;
        color: white;
    }

    .btn-edit:hover { background: #45a049; }
    .btn-delete:hover { background: #a50000; }

    /* Tambahkan CSS untuk pagination */
    .pagination-container {
        margin-top: 20px !important;
        display: flex !important; /* Override display default */
        justify-content: space-between !important;
        align-items: center;
    }

    .pagination-controls {
        display: flex;
        gap: 5px;
    }

    .pagination-button {
        width: auto !important;
        padding: 6px 10px !important;
        border: 1px solid #4CAF50 !important; /* Override warna default */
        background: #45a049 !important; /* Override warna default */
        cursor: pointer;
        border-radius: 4px;
        font-size: 12px;
        color: white !important;
    }

    .pagination-button:hover {
        background: #3e8e41 !important;
    }

    .pagination-button.active {
        background: #45a049 !important;
        color: white;
        border-color: #45a049 !important;
    }

    .pagination-button:disabled {
        background: #45a049;
        cursor: not-allowed;
    }

    .sort-icons {
        display: inline-flex;
        flex-direction: column;
        margin-left: 5px;
        cursor: pointer;
        line-height: 0.5;
        vertical-align: middle;
    }

    .sort-icon {
        font-size: 12px;
        color: #999;
        padding: 2px;
        transition: color 0.2s;
    }

    .sort-icon.active {
        color: #333;
    }

    .sort-icon:hover {
        color: #666;
    }

    .nik-container {
        display: flex;
        align-items: center;
        gap: 4px;
        max-width: 200px;
    }

    .nik-text {
        overflow-x: auto;
        white-space: nowrap;
        max-width: calc(100% - 24px); /* Sesuaikan dengan lebar button */
        padding: 2px 4px;
        /* Sembunyikan scrollbar tapi tetap bisa scroll */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    .copy-btn {
        flex-shrink: 0;
        background: #45a049 !important; /* Override warna default */
        color: white;
        border: none;
        padding: 2px 4px !important; /* Override padding default */
        border-radius: 4px;
        cursor: pointer;
        font-size: 10px !important; /* Override font size default */
        transition: background-color 0.2s;
        width: 24px !important; /* Override min-width default */
        height: 24px !important;
        min-width: unset !important; /* Hapus min-width */
        margin: 0 !important; /* Hapus margin default */
        display: flex !important; /* Override display default */
        align-items: center;
        justify-content: center;
        font-weight: normal !important; /* Override font-weight default */
    }

    .copy-btn:hover {
        background: #3d8b3d;
    }

    .copy-btn.copied {
        background: #28a745;
    }

    .tooltip {
        position: absolute;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 1000;
        display: none;
    }

    /* Tambahkan style baru untuk cell yang bisa di-scroll */
    .scrollable-cell {
        max-width: 150px;
        overflow-x: auto;
        white-space: nowrap;
        padding: 2px 4px;
        /* Sembunyikan scrollbar tapi tetap bisa scroll */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    .scrollable-cell::-webkit-scrollbar {
        display: none; /* Chrome, Safari and Opera */
    }
    .jarak {
        height: 100px;
    }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container-form">
    <div class="form-header">
        <h2>Manage Users</h2>
        <div class="header-buttons">
            <?php
            // Tampilkan tombol secret hanya untuk developer
            if (file_exists('../config/developer_config.php')) {
                include_once '../config/developer_config.php';
                $dev_validation = validateDeveloperAccess(null, false); // Check tanpa token dan IP
                if ($dev_validation['valid'] || $_SESSION['role_id'] == 99) {
            // ?>
            <!-- // <a href="#" onclick="showDeveloperAccess()" class="add-user-btn" style="color: white; background: #e74c3c;">
            //     <i class="fas fa-key"></i> 🔐 Developer Access
            // </a> -->
            <?php
                }
            }
            ?>
            <a href="delete_inactive_users.php" class="delete-inactive-btn" style="color: white;">
                <i class="fas fa-user-slash"></i> Hapus Pengguna Tidak Aktif
            </a>
            <a href="bulk_select_users.php" class="add-user-btn" style="color: white; background: #FF9800;">
                <i class="fas fa-users"></i> Bulk Select Users
            </a>
            <a href="create_user.php" class="add-user-btn" style="color: white;">
                <i class="fas fa-plus"></i> Add New User
            </a>
        </div>
    </div>

    <div class="search-container">
        <input type="text"
               id="searchInput"
               class="search-input"
               placeholder="Multi-search: 'rizki hrga pemohon' or 'iman helper depthead'..."
               value="<?= htmlspecialchars($search) ?>">
        <div class="search-help">
            <small class="text-muted">
                <i class="fas fa-info-circle"></i>
                <strong>Multi-Search Tips:</strong>
                Use spaces or commas to search multiple terms. Example: "rizki hrga pemohon" will find users matching ALL terms.
                <br>
                <i class="fas fa-star text-success"></i>
                <strong>Priority:</strong> Active users (is_active=1) are displayed first in search results.
            </small>
        </div>
        <div id="searchTermsDisplay" class="search-terms-display" style="display: none;">
            <small><strong>Search Terms:</strong> <span id="searchTermsList"></span></small>
        </div>
    </div>

    <table id="usersTable">
        <thead>
            <tr>
                <th>
                    <div class="id-header">
                        ID
                        <div class="sort-icons">
                            <i class="fas fa-sort-up sort-icon" onclick="sortUsers('id', 'ASC')" id="sort-id-asc"></i>
                            <i class="fas fa-sort-down sort-icon" onclick="sortUsers('id', 'DESC')" id="sort-id-desc"></i>
                        </div>
                    </div>
                </th>
                <th>Name</th>
                <th>Department</th>
                <th>Bagian</th>
                <th>Jabatan</th>
                <th>Email</th>
                <th>NIK</th>
                <th>Role</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody id="usersTableBody">
            <!-- Data will be populated by JavaScript -->
        </tbody>
    </table>
    <div class="pagination-container">
        <div class="pagination-info">
            Showing <span id="showing-start">0</span> to <span id="showing-end">0</span> of <span id="total-records">0</span> entries
        </div>
        <div class="pagination-controls" id="pagination-controls"></div>
    </div>
</div>
<script>
let currentPage = <?php echo $page; ?>;
let totalPages = <?php echo $total_pages; ?>;
let currentQuery = '';
let responseData = <?php echo json_encode($response_data); ?>;
let currentSort = 'id';
let currentOrder = 'DESC';

function loadInitialData() {
    updateTable(responseData.users);
    updatePagination(responseData.pagination);
}

function updateTable(users) {
    const tableBody = document.getElementById('usersTableBody');
    tableBody.innerHTML = users.map(user => {
        const isActive = user.is_active == 1;
        const rowClass = isActive ? 'user-row-active' : 'user-row-inactive';
        const statusBadge = isActive ?
            '<span class="status-badge active">Active</span>' :
            '<span class="status-badge inactive">Inactive</span>';

        return `
        <tr class="${rowClass}">
            <td>${user.id}</td>
            <td>
                <div class="scrollable-cell">
                    ${escapeHtml(user.name)}
                    ${statusBadge}
                </div>
            </td>
            <td><div class="scrollable-cell">${escapeHtml(user.dept)}</div></td>
            <td><div class="scrollable-cell">${escapeHtml(user.bagian)}</div></td>
            <td><div class="scrollable-cell">${escapeHtml(user.jabatan)}</div></td>
            <td><div class="scrollable-cell">${escapeHtml(user.email)}</div></td>
            <td>
                <div class="nik-container">
                    <div class="nik-text" id="nik-${user.id}">${escapeHtml(user.nik)}</div>
                    <button class="copy-btn" onclick="copyNIK('${user.nik}', this)" title="Copy NIK">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </td>
            <td>${escapeHtml(user.role_name)}</td>
            <td>
                <label class="status-toggle">
                    <input type="checkbox"
                           ${isActive ? 'checked' : ''}
                           onchange="toggleUserStatus(${user.id}, this.checked)">
                    <span class="slider"></span>
                </label>
            </td>
            <td>
                <div class="action-buttons">
                    <a href="edit_user.php?id=${user.id}" style="color: #4CAF50;" title="Edit User"><i class="fas fa-edit"></i></a>
                    <a href="#" onclick="showDeleteModal(${user.id}); return false;" style="color: #cc0000;" title="Delete User"><i class="fas fa-trash"></i></a>
                </div>
            </td>
        </tr>
        `;
    }).join('');
}

function updatePagination(pagination) {
    const controls = document.getElementById('pagination-controls');
    const { current_page, total_pages, total_records, limit } = pagination;

    // Update info text
    const start = ((current_page - 1) * limit) + 1;
    const end = Math.min(start + limit - 1, total_records);
    document.getElementById('showing-start').textContent = start;
    document.getElementById('showing-end').textContent = end;
    document.getElementById('total-records').textContent = total_records;

    // Generate pagination buttons
    let buttons = '';

    // Previous button
    buttons += `<button class="pagination-button"
                        onclick="changePage(${current_page - 1})"
                        ${current_page === 1 ? 'disabled' : ''}>
                    Previous
                </button>`;

    // Page numbers
    for (let i = 1; i <= total_pages; i++) {
        if (i === 1 || i === total_pages || (i >= current_page - 2 && i <= current_page + 2)) {
            buttons += `<button class="pagination-button ${i === current_page ? 'active' : ''}"
                               onclick="changePage(${i})">
                        ${i}
                    </button>`;
        } else if (i === current_page - 3 || i === current_page + 3) {
            buttons += `<span class="pagination-ellipsis">...</span>`;
        }
    }

    // Next button
    buttons += `<button class="pagination-button"
                        onclick="changePage(${current_page + 1})"
                        ${current_page === total_pages ? 'disabled' : ''}>
                    Next
                </button>`;

    controls.innerHTML = buttons;
}

// Fungsi untuk escape HTML untuk keamanan
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Event listener untuk pencarian
document.getElementById('searchInput').addEventListener('input', function(e) {
    const searchQuery = e.target.value;
    searchUsers(searchQuery);
});

async function changePage(page) {
    if (page < 1 || page > totalPages) return;

    currentPage = page;
    const searchQuery = document.getElementById('searchInput').value;
    await searchUsers(searchQuery, page);
}

async function searchUsers(query, page = 1) {
    try {
        const response = await fetch(
            `search_users.php?search=${encodeURIComponent(query)}&page=${page}&sort=${currentSort}&order=${currentOrder}`
        );
        const data = await response.json();

        if (data.users) {
            updateTable(data.users);
            updatePagination(data.pagination);
            totalPages = data.pagination.total_pages;

            // Update search terms display
            updateSearchTermsDisplay(data.search_info);

            // Update sort icons based on current sort state
            document.querySelectorAll('.sort-icon').forEach(icon => {
                icon.classList.remove('active');
            });
            document.getElementById(`sort-${data.sort.column}-${data.sort.order.toLowerCase()}`).classList.add('active');
        }
    } catch (error) {
        console.error('Error searching users:', error);
    }
}

function updateSearchTermsDisplay(searchInfo) {
    const searchTermsDisplay = document.getElementById('searchTermsDisplay');
    const searchTermsList = document.getElementById('searchTermsList');

    if (searchInfo && searchInfo.search_terms && searchInfo.search_terms.length > 0) {
        // Create badges for each search term
        const termBadges = searchInfo.search_terms.map(term =>
            `<span class="search-term-badge">${escapeHtml(term)}</span>`
        ).join(' ');

        searchTermsList.innerHTML = `${termBadges} <small class="text-muted">(${searchInfo.terms_count} terms)</small>`;
        searchTermsDisplay.style.display = 'block';
    } else {
        searchTermsDisplay.style.display = 'none';
    }
}

async function toggleUserStatus(userId, status) {
    const checkbox = event.target; // Store checkbox reference
    const originalStatus = checkbox.checked; // Store original status

    try {
        // Log the data being sent
        console.log('Sending data:', { user_id: userId, status: status });

        const response = await fetch('toggle_user_status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                status: status
            }),
            credentials: 'same-origin'
        });

        // Log the response status
        console.log('Response status:', response.status);

        const data = await response.json();
        console.log('Response data:', data);

        if (!response.ok) {
            throw new Error(data.message || `HTTP error! status: ${response.status}`);
        }

        if (data.success) {
            // Refresh data pada halaman yang sama
            const searchQuery = document.getElementById('searchInput').value;
            await searchUsers(searchQuery, currentPage);
        } else {
            throw new Error(data.message || 'Failed to update user status');
        }
    } catch (error) {
        console.error('Error details:', error);
        alert(error.message || 'An error occurred while updating user status');

        // Kembalikan checkbox ke status sebelumnya
        checkbox.checked = originalStatus;
    }
}

// Fungsi untuk menampilkan modal konfirmasi hapus user
function showDeleteModal(userId) {
    // Gunakan CustomModal library yang sudah ada
    CustomModal.confirm(
        'Apakah Anda yakin ingin menghapus pengguna ini?',
        'Konfirmasi Hapus',
        {
            confirmText: 'Hapus',
            cancelText: 'Batal',
            onConfirm: function() {
                // Panggil fungsi deleteUser saat tombol konfirmasi diklik
                deleteUser(userId);
            }
        }
    );
}

async function deleteUser(userId) {
    try {
        const response = await fetch(`delete_user.php?id=${userId}`, {
            method: 'GET'
        });

        const data = await response.json().catch(() => ({}));
        const message = data.message || '';

        if (response.ok) {
            // Tampilkan pesan sukses menggunakan CustomModal
            CustomModal.success(
                message || 'Pengguna berhasil dihapus',
                'Sukses',
                {
                    onConfirm: async function() {
                        // Refresh data setelah modal ditutup
                        const searchQuery = document.getElementById('searchInput').value;
                        await searchUsers(searchQuery, currentPage);
                    }
                }
            );
        } else {
            // Tampilkan pesan error menggunakan CustomModal
            CustomModal.alert(
                message || 'Gagal menghapus pengguna',
                'Error',
                {}
            );
        }
    } catch (error) {
        console.error('Error:', error);
        // Tampilkan pesan error menggunakan CustomModal
        CustomModal.alert(
            'Terjadi kesalahan saat menghapus pengguna',
            'Error',
            {}
        );
    }
}

async function sortUsers(column, order) {
    currentSort = column;
    currentOrder = order;

    // Update visual state of sort icons
    document.querySelectorAll('.sort-icon').forEach(icon => {
        icon.classList.remove('active');
    });
    document.getElementById(`sort-${column}-${order.toLowerCase()}`).classList.add('active');

    // Perform search with new sort parameters
    const searchQuery = document.getElementById('searchInput').value;
    await searchUsers(searchQuery, currentPage);
}

// Load data saat halaman dimuat
document.addEventListener('DOMContentLoaded', function() {
    loadInitialData();

    // Set initial sort state
    document.getElementById('sort-id-desc').classList.add('active');

    // Show search terms if there's an initial search query
    const initialSearch = document.getElementById('searchInput').value;
    if (initialSearch.trim()) {
        // Parse initial search terms
        const searchTerms = initialSearch.trim().split(/[\s,]+/).filter(term => term !== '');
        if (searchTerms.length > 0) {
            updateSearchTermsDisplay({
                original_query: initialSearch,
                search_terms: searchTerms,
                terms_count: searchTerms.length
            });
        }
    }
});

// Tambahkan fungsi untuk menyalin NIK
function copyNIK(nik, button) {
    // Buat temporary textarea untuk menyalin teks
    const textarea = document.createElement('textarea');
    textarea.value = nik;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);

    // Tampilkan feedback visual
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.classList.add('copied');

    // Tampilkan tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = 'NIK disalin!';
    tooltip.style.position = 'absolute';

    // Posisikan tooltip di atas tombol
    const rect = button.getBoundingClientRect();
    tooltip.style.top = `${rect.top - 30}px`;
    tooltip.style.left = `${rect.left}px`;
    tooltip.style.display = 'block';

    document.body.appendChild(tooltip);

    // Hapus tooltip dan kembalikan tampilan tombol setelah 2 detik
    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('copied');
        document.body.removeChild(tooltip);
    }, 2000);
}

// Developer Access Functions
function showDeveloperAccess() {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;

    modal.innerHTML = `
        <div style="
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        ">
            <h2 style="color: #e74c3c; margin-bottom: 20px;">
                <i class="fas fa-shield-alt"></i> Developer Access
            </h2>
            <p style="margin-bottom: 20px; color: #666;">
                Masukkan secret token untuk mengakses password viewer:
            </p>
            <input type="password" id="devToken" placeholder="Developer Secret Token"
                   style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 20px;">
            <div style="display: flex; gap: 10px; justify-content: center;">
                <button onclick="accessPasswordViewer()"
                        style="background: #e74c3c; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
                    <i class="fas fa-key"></i> Access Password Viewer
                </button>
                <button onclick="closeDeveloperModal()"
                        style="background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
                    Cancel
                </button>
            </div>
            <div style="margin-top: 15px; font-size: 12px; color: #999;">
                ⚠️ Semua akses akan dicatat dan dimonitor
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    document.getElementById('devToken').focus();

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeDeveloperModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeDeveloperModal();
        }
    });
}

function accessPasswordViewer() {
    const token = document.getElementById('devToken').value;
    if (!token) {
        alert('Masukkan secret token terlebih dahulu!');
        return;
    }

    // Redirect ke password viewer dengan token
    window.open(`secret_password_viewer.php?token=${encodeURIComponent(token)}`, '_blank');
    closeDeveloperModal();
}

function closeDeveloperModal() {
    const modal = document.querySelector('div[style*="position: fixed"]');
    if (modal) {
        document.body.removeChild(modal);
    }
}
</script>

<!-- Developer Access Modal Styles -->
<style>
.developer-access-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
    border: none !important;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3) !important;
    transition: all 0.3s ease !important;
}

.developer-access-btn:hover {
    background: linear-gradient(135deg, #c0392b, #a93226) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4) !important;
}
</style>

<?php include '../config/footer.php'; ?>
</body>
</html>