# Implementasi Poster Training - Panduan Lengkap

## 📋 Overview
Fitur poster training memungkinkan admin untuk menambahkan gambar poster ke training internal dan eksternal. Poster akan ditampilkan di detail training modal dengan kemampuan preview yang lebih besar.

## 🗄️ Database Changes

### 1. Field Baru yang Ditambahkan:
- **offline_training.poster_image** (VARCHAR 255) - Path ke poster training internal
- **training_submissions.poster_image** (VARCHAR 255) - Path ke poster training eksternal

### 2. Menjalankan Update Database:
```bash
# Jalankan script update database
php database_updates/run_poster_update.php
```

Atau jalankan SQL manual:
```sql
-- Untuk training internal
ALTER TABLE `offline_training` 
ADD COLUMN `poster_image` VARCHAR(255) DEFAULT NULL COMMENT 'Path to training poster image' 
AFTER `training_description`;

-- Untuk training eksternal  
ALTER TABLE `training_submissions` 
ADD COLUMN `poster_image` VARCHAR(255) DEFAULT NULL COMMENT 'Path to training poster image' 
AFTER `internal_memo_image`;
```

## 📁 Struktur Directory Upload

```
uploads/
└── training_posters/
    ├── offline/     # Poster training internal
    └── online/      # Poster training eksternal
```

## 🔧 Implementasi Backend

### 1. API Update (get_training_detail_universal.php)
- Menambahkan field `poster_image` ke query SELECT
- Untuk training eksternal: menggunakan `COALESCE(ts.poster_image, ts.internal_memo_image)` sebagai fallback

### 2. Form Upload (admin/training_management.php)
- Form support `enctype="multipart/form-data"`
- Validasi file type: JPG, JPEG, PNG
- Validasi ukuran: maksimal 2MB
- Auto-generate unique filename
- Delete old poster saat update

### 3. Validasi Upload:
```php
// Validasi type file
$allowed_types = ['image/jpeg', 'image/jpg', 'image/png'];

// Validasi ukuran (max 2MB)
if ($_FILES['poster_image']['size'] > 2 * 1024 * 1024) {
    // Error handling
}
```

## 🎨 Implementasi Frontend

### 1. Modal Detail Training (universal-calendar.js)
- Deteksi apakah training memiliki poster
- Tampilkan poster di sebelah informasi training
- Poster clickable untuk preview lebih besar
- Responsive layout: poster menggantikan badge type jika ada

### 2. Poster Preview Modal
- Modal terpisah untuk menampilkan poster dalam ukuran besar
- Support click outside untuk close
- Responsive design dengan max-width 90%

### 3. Form Upload UI
- File input dengan accept="image/*"
- Preview poster saat ini di form edit
- Informasi format dan ukuran yang didukung

## 📱 Responsive Design

### Desktop:
```
[Informasi Training - 8 cols] [Poster/Badge - 4 cols]
```

### Mobile:
```
[Informasi Training - 12 cols]
[Poster/Badge - 12 cols]
```

## 🔒 Security Features

1. **File Type Validation**: Hanya JPG, JPEG, PNG
2. **File Size Limit**: Maksimal 2MB
3. **Unique Filename**: Prevent file collision
4. **Directory Traversal Protection**: Validated upload path
5. **Old File Cleanup**: Delete old poster saat update

## 🚀 Cara Penggunaan

### 1. Menambah Training dengan Poster:
1. Buka Admin → Training Management
2. Klik "Tambah Training Internal"
3. Isi form training
4. Upload poster di field "Poster Training"
5. Submit form

### 2. Melihat Poster di Calendar:
1. Buka Universal Calendar
2. Klik event training yang memiliki poster
3. Poster akan muncul di modal detail
4. Klik poster untuk preview lebih besar

### 3. Edit Poster:
1. Edit training yang sudah ada
2. Upload poster baru (opsional)
3. Poster lama akan otomatis terhapus

## 🎯 Features

✅ **Upload poster untuk training internal**
✅ **Upload poster untuk training eksternal** 
✅ **Preview poster di detail training**
✅ **Modal popup untuk poster besar**
✅ **Responsive design**
✅ **File validation & security**
✅ **Auto cleanup old files**
✅ **Fallback ke internal_memo_image untuk training eksternal**

## 🔄 Fallback Behavior

- **Training Eksternal**: Jika `poster_image` kosong, gunakan `internal_memo_image`
- **Training Internal**: Jika tidak ada poster, tampilkan badge type training
- **Modal Layout**: Otomatis adjust layout berdasarkan ketersediaan poster

## 📝 Notes

- Poster disimpan di folder terpisah berdasarkan type training
- Filename menggunakan timestamp dan uniqid untuk uniqueness
- Support untuk training yang sudah ada (backward compatibility)
- Modal poster menggunakan z-index tinggi untuk overlay yang proper
