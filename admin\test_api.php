<?php
session_start();
include '../config/config.php';

// Set admin session for testing
$_SESSION['user_id'] = 1;
$_SESSION['role_id'] = 99;

echo "<h2>API Test - get_filtered_training.php</h2>";

// Test 1: Basic connection
echo "<h3>1. Database Connection Test</h3>";
if ($conn) {
    echo "✅ Database connected<br>";
} else {
    echo "❌ Database connection failed<br>";
    exit();
}

// Test 2: Check table exists and has data
echo "<h3>2. Table Check</h3>";
$count_query = "SELECT COUNT(*) as total FROM training_submissions WHERE (deleted_at IS NULL OR deleted_at = '')";
$count_result = $conn->query($count_query);
if ($count_result) {
    $total = $count_result->fetch_assoc()['total'];
    echo "Total active training records: <strong>$total</strong><br>";
    if ($total == 0) {
        echo "❌ No data found in training_submissions table<br>";
    } else {
        echo "✅ Data found in training_submissions table<br>";
    }
} else {
    echo "❌ Query failed: " . $conn->error . "<br>";
}

// Test 3: Test the API endpoint directly
echo "<h3>3. API Endpoint Test</h3>";

// Simulate the API call
$_GET['search'] = '';
$_GET['status'] = '';
$_GET['date_from'] = '';
$_GET['date_to'] = '';

echo "<h4>Simulating API call...</h4>";

// Capture output
ob_start();
include 'get_filtered_training.php';
$api_output = ob_get_clean();

echo "<h4>API Response:</h4>";
echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
echo htmlspecialchars($api_output);
echo "</pre>";

// Test if it's valid JSON
$json_data = json_decode($api_output, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "✅ Valid JSON response<br>";
    if (isset($json_data['error'])) {
        echo "❌ API returned error: " . $json_data['error'] . "<br>";
    } else {
        echo "✅ API returned data successfully<br>";
        echo "Records returned: <strong>" . count($json_data) . "</strong><br>";
        
        if (count($json_data) > 0) {
            echo "<h4>Sample Record:</h4>";
            echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
            print_r($json_data[0]);
            echo "</pre>";
        }
    }
} else {
    echo "❌ Invalid JSON response<br>";
    echo "JSON Error: " . json_last_error_msg() . "<br>";
}

// Test 4: Direct query test
echo "<h3>4. Direct Query Test</h3>";
$test_query = "SELECT ts.id, ts.full_name, ts.training_topic, ts.status, u.name as requester_name
               FROM training_submissions ts
               LEFT JOIN users u ON ts.user_id = u.id
               WHERE (ts.deleted_at IS NULL OR ts.deleted_at = '')
               ORDER BY ts.id DESC
               LIMIT 5";

$test_result = $conn->query($test_query);
if ($test_result) {
    echo "✅ Direct query successful<br>";
    echo "Records found: <strong>" . $test_result->num_rows . "</strong><br>";
    
    if ($test_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Topic</th><th>Status</th><th>Requester</th></tr>";
        while ($row = $test_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['training_topic']) . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . htmlspecialchars($row['requester_name'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "❌ Direct query failed: " . $conn->error . "<br>";
}

$conn->close();
?>

<style>
h3 {
    color: #333;
    border-bottom: 2px solid #ccc;
    padding-bottom: 5px;
}
table {
    font-size: 12px;
}
th {
    background-color: #f0f0f0;
    padding: 5px;
}
td {
    padding: 5px;
    max-width: 200px;
    word-wrap: break-word;
}
</style>
