<?php
include 'security.php';
include '../config/config.php';

$user_id = $_SESSION['user_id'];

// Initialize error and success arrays
$errors = [];
$success = '';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['errors'] = ['Form harus disubmit dengan method POST'];
    header('Location: internal_training_form.php');
    exit();
}

// CSRF Protection
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['token']) {
    $_SESSION['errors'] = ['Token keamanan tidak valid. Silakan coba lagi.'];
    header('Location: internal_training_form.php');
    exit();
}

// Form token validation (prevent duplicate submission)
if (!isset($_POST['form_token']) || $_POST['form_token'] !== $_SESSION['form_token']) {
    $_SESSION['errors'] = ['Form token tidak valid. Silakan refresh halaman dan coba lagi.'];
    header('Location: internal_training_form.php');
    exit();
}

// Check form token expiry (5 minutes)
if (!isset($_SESSION['form_token_time']) || (time() - $_SESSION['form_token_time']) > 300) {
    $_SESSION['errors'] = ['Form token telah expired. Silakan refresh halaman dan coba lagi.'];
    header('Location: internal_training_form.php');
    exit();
}

// Clear form token to prevent reuse
unset($_SESSION['form_token']);
unset($_SESSION['form_token_time']);

try {
    // Validate and sanitize input data
    $full_name = trim($_POST['full_name'] ?? '');
    $nik = trim($_POST['nik'] ?? '');
    $department = trim($_POST['department'] ?? '');
    $bagian = trim($_POST['bagian'] ?? '');
    $jabatan = trim($_POST['jabatan'] ?? '');
    $email = trim($_POST['email'] ?? '');

    $training_topic = trim($_POST['training_topic'] ?? '');
    $training_description = trim($_POST['training_description'] ?? '');
    $start_date = trim($_POST['start_date'] ?? '');
    $end_date = trim($_POST['end_date'] ?? '');
    $training_time_start = trim($_POST['training_time_start'] ?? '');
    $training_time_end = trim($_POST['training_time_end'] ?? '');
    $location = trim($_POST['location'] ?? '');
    $max_participants = trim($_POST['max_participants'] ?? '');
    $trainer_preference = trim($_POST['trainer_preference'] ?? '');
    $training_skill_type = trim($_POST['training_skill_type'] ?? '');
    $urgency_reason = trim($_POST['urgency_reason'] ?? '');

    // Validation
    if (empty($full_name)) $errors[] = 'Nama pemohon harus diisi';
    if (empty($nik)) $errors[] = 'NIK harus diisi';
    if (empty($email)) $errors[] = 'Email harus diisi';
    if (empty($training_topic)) $errors[] = 'Nama/topik training harus diisi';
    if (empty($training_description)) $errors[] = 'Deskripsi training harus diisi';
    if (empty($start_date)) $errors[] = 'Tanggal mulai training harus diisi';
    if (empty($training_skill_type)) $errors[] = 'Jenis kompetensi training harus dipilih';
    if (empty($urgency_reason)) $errors[] = 'Alasan & urgensi training harus diisi';

    // Email validation
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Format email tidak valid';
    }

    // Date validation
    if (!empty($start_date)) {
        $start_date_obj = DateTime::createFromFormat('Y-m-d', $start_date);
        if (!$start_date_obj) {
            $errors[] = 'Format tanggal mulai tidak valid';
        } else {
            // Check if start date is at least 1 week from today
            $min_date = new DateTime();
            $min_date->add(new DateInterval('P7D')); // Add 7 days

            if ($start_date_obj < $min_date) {
                $errors[] = 'Tanggal mulai training harus minimal 1 minggu dari hari ini';
            }
        }
    }

    // End date validation
    if (!empty($end_date)) {
        $end_date_obj = DateTime::createFromFormat('Y-m-d', $end_date);
        if (!$end_date_obj) {
            $errors[] = 'Format tanggal selesai tidak valid';
        } elseif (!empty($start_date) && $start_date_obj) {
            if ($end_date_obj < $start_date_obj) {
                $errors[] = 'Tanggal selesai tidak boleh lebih awal dari tanggal mulai';
            }
        }
    }

    // Flexible time validation - allow same time or overlapping times
    if (!empty($training_time_start) && !empty($training_time_end)) {
        $time_start = DateTime::createFromFormat('H:i', $training_time_start);
        $time_end = DateTime::createFromFormat('H:i', $training_time_end);

        // Only validate format, not the time logic for flexibility
        if (!$time_start) {
            $errors[] = 'Format waktu mulai tidak valid (gunakan format HH:MM)';
        }
        if (!$time_end) {
            $errors[] = 'Format waktu selesai tidak valid (gunakan format HH:MM)';
        }
        // Remove strict time comparison for flexibility
    }

    // Max participants validation
    if (!empty($max_participants)) {
        if (!is_numeric($max_participants) || $max_participants < 1 || $max_participants > 1000) {
            $errors[] = 'Jumlah peserta harus berupa angka antara 1-1000';
        }
    }

    // If there are validation errors, redirect back with errors
    if (!empty($errors)) {
        $_SESSION['errors'] = $errors;
        header('Location: internal_training_form.php');
        exit();
    }

    // Prepare data for database insertion
    $end_date = empty($end_date) ? null : $end_date;
    $training_time_start = empty($training_time_start) ? null : $training_time_start;
    $training_time_end = empty($training_time_end) ? null : $training_time_end;
    $location = empty($location) ? null : $location;
    $max_participants = empty($max_participants) ? null : (int)$max_participants;
    $trainer_preference = empty($trainer_preference) ? null : $trainer_preference;

    // Create comprehensive training description
    $full_description = $training_description;
    if (!empty($trainer_preference)) {
        $full_description .= "\n\nPreferensi Trainer: " . $trainer_preference;
    }
    $full_description .= "\n\nJenis Kompetensi: " . $training_skill_type;
    $full_description .= "\n\nAlasan & Urgensi: " . $urgency_reason;
    $full_description .= "\n\nDiajukan oleh: " . $full_name . " (" . $nik . ")";
    $full_description .= "\nDepartemen: " . $department . " - " . $bagian;
    $full_description .= "\nJabatan: " . $jabatan;

    // Insert into offline_training table with approval workflow
    $query = "INSERT INTO offline_training
              (training_topic, training_description, start_date, end_date,
               training_time_start, training_time_end, location, trainer_name,
               max_participants, status, current_approver, created_by, created_at)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'Pending Dept Head Approval', 'dept_head', ?, NOW())";

    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception('Prepare statement failed: ' . $conn->error);
    }

    // Use "Internal Request" as trainer_name to indicate this is a request
    $trainer_name = "Internal Request - " . $training_skill_type;

    $stmt->bind_param("ssssssssii",
        $training_topic,
        $full_description,
        $start_date,
        $end_date,
        $training_time_start,
        $training_time_end,
        $location,
        $trainer_name,
        $max_participants,
        $user_id
    );

    if ($stmt->execute()) {
        $training_id = $conn->insert_id;

        // Log the successful submission
        error_log("Internal training request submitted successfully. ID: {$training_id}, User: {$user_id}, Topic: {$training_topic}");

        // Create success message
        $success = "Pengajuan training internal berhasil dikirim! ";
        $success .= "ID Pengajuan: #{$training_id}. ";
        $success .= "Pengajuan akan diproses melalui tahapan: Department Head → L&D → Approved. ";
        $success .= "Anda akan dihubungi setelah mendapat persetujuan.";

        $_SESSION['success'] = $success;

        // Redirect to success page or back to form
        header('Location: internal_training_success.php?id=' . $training_id);
        exit();

    } else {
        throw new Exception('Execute failed: ' . $stmt->error);
    }

} catch (Exception $e) {
    // Log the error
    error_log("Internal training submission error: " . $e->getMessage());
    error_log("User ID: " . $user_id);
    error_log("POST data: " . print_r($_POST, true));

    // Set error message
    $_SESSION['errors'] = ['Terjadi kesalahan sistem. Silakan coba lagi atau hubungi administrator.'];
    header('Location: internal_training_form.php');
    exit();

} finally {
    // Close statement and connection
    if (isset($stmt)) {
        $stmt->close();
    }
}
?>
