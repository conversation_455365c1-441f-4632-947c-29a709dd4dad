<?php
/**
 * Halaman Setup Utama
 * 
 * Halaman ini memandu pengguna melalui proses pengaturan awal aplikasi Training,
 * termasuk instalasi Composer dan pengaturan database.
 */

// Aktifkan laporan kesalahan untuk debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Function to check if composer dependencies are installed
function checkComposerDependencies() {
    $vendorDir = __DIR__ . '/vendor';
    return [
        'vendor_exists' => file_exists($vendorDir) && is_dir($vendorDir),
        'autoload_exists' => file_exists($vendorDir . '/autoload.php')
    ];
}

// Function to check database connection
function checkDatabaseConnection() {
    $db_host = 'localhost';
    $db_user = 'root';
    $db_pass = '';
    $db_name = 'db_training';

    try {
        $conn = new mysqli($db_host, $db_user, $db_pass);
        $dbExists = $conn->select_db($db_name);
        $conn->close();
        return ['connected' => true, 'db_exists' => $dbExists];
    } catch (Exception $e) {
        return ['connected' => false, 'db_exists' => false, 'error' => $e->getMessage()];
    }
}

$composerStatus = checkComposerDependencies();
$dbStatus = checkDatabaseConnection();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Pengaturan Aplikasi Training</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .setup-step {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .step-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .step-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #34495e;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4efdf;
            color: #27ae60;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-error {
            background-color: #fadbd8;
            color: #e74c3c;
        }
        .action-button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 10px;
        }
        .action-button:hover {
            background-color: #2980b9;
        }
        .instructions {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        code {
            background: #f8f8f8;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        .setup-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }
        .setup-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .setup-button:hover {
            background-color: #2980b9;
        }
        .setup-button.composer {
            background-color: #27ae60;
        }
        .setup-button.composer:hover {
            background-color: #219a52;
        }
        .setup-button.database {
            background-color: #e74c3c;
        }
        .setup-button.database:hover {
            background-color: #c0392b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Pengaturan Aplikasi Training</h1>
        
        <!-- Setup Buttons -->
        <div class="setup-buttons">
            <a href="setup_composer.php" class="setup-button composer">Pengaturan Composer</a>
            <a href="setupdb.php" class="setup-button database">Pengaturan Database</a>
        </div>
        
        <!-- Step 1: Composer Dependencies -->
        <div class="setup-step">
            <div class="step-header">
                <div class="step-title">Langkah 1: Dependensi Composer</div>
                <div class="status <?php echo ($composerStatus['vendor_exists'] && $composerStatus['autoload_exists']) ? 'status-success' : 'status-error'; ?>">
                    <?php echo ($composerStatus['vendor_exists'] && $composerStatus['autoload_exists']) ? 'Terinstal' : 'Belum Terinstal'; ?>
                </div>
            </div>
            <?php if (!$composerStatus['vendor_exists'] || !$composerStatus['autoload_exists']): ?>
                <p>Dependensi Composer perlu diinstal.</p>
                <div class="instructions">
                    <p><strong>Petunjuk:</strong></p>
                    <ol>
                        <li>Pastikan Composer sudah terinstal di sistem Anda</li>
                        <li>Buka terminal di direktori proyek</li>
                        <li>Jalankan perintah: <code>composer install</code></li>
                    </ol>
                </div>
                <a href="setup_composer.php" class="action-button">Atur Dependensi Composer</a>
            <?php else: ?>
                <p>✅ Dependensi Composer sudah terinstal dengan benar.</p>
            <?php endif; ?>
        </div>

        <!-- Step 2: Database Setup -->
        <div class="setup-step">
            <div class="step-header">
                <div class="step-title">Langkah 2: Pengaturan Database</div>
                <div class="status <?php echo ($dbStatus['connected'] && $dbStatus['db_exists']) ? 'status-success' : 'status-error'; ?>">
                    <?php 
                    if (!$dbStatus['connected']) {
                        echo 'Koneksi Gagal';
                    } elseif (!$dbStatus['db_exists']) {
                        echo 'Database Tidak Ada';
                    } else {
                        echo 'Terhubung';
                    }
                    ?>
                </div>
            </div>
            <?php if (!$dbStatus['connected'] || !$dbStatus['db_exists']): ?>
                <p>Database perlu diatur.</p>
                <div class="instructions">
                    <p><strong>Prasyarat:</strong></p>
                    <ol>
                        <li>Pastikan server MySQL sudah berjalan</li>
                        <li>Kredensial default: root (tanpa password)</li>
                        <li>Database akan dibuat secara otomatis</li>
                    </ol>
                </div>
                <a href="setupdb.php" class="action-button">Atur Database</a>
            <?php else: ?>
                <p>✅ Database sudah terkonfigurasi dan terhubung dengan benar.</p>
            <?php endif; ?>
        </div>

        <?php if ($composerStatus['vendor_exists'] && $composerStatus['autoload_exists'] && $dbStatus['connected'] && $dbStatus['db_exists']): ?>
        <div class="setup-step" style="text-align: center;">
            <div class="step-header">
                <div class="step-title" style="width: 100%; text-align: center;">✨ Pengaturan Selesai!</div>
            </div>
            <p>Semua komponen telah terkonfigurasi dengan benar. Anda sekarang dapat mulai menggunakan aplikasi.</p>
            <a href="index.php" class="action-button">Buka Aplikasi</a>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>