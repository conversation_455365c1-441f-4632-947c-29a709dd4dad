# 🔧 Fix Sembunyikan Semu<PERSON>ali Comments untuk Status Rejected/Revise

## ❌ Masalah yang <PERSON> sudah ada fix sebelumnya, masih ada elements yang tidak tersembunyi dengan benar untuk status **Rejected/Revise**:

### **Elements yang <PERSON><PERSON> (Seharusnya Disembunyikan):**
- ❌ **"Estimasi dari pemohon: 2025-07-07"** (alert-info)
- ❌ **"Durasi Training"** dropdown dan label
- ❌ **"Tentukan apakah training dilaksanakan dalam 1 hari atau beberapa hari"** text
- ❌ Berbagai form groups lainnya yang masih terlewat

### **Yang Seharusnya Ditampilkan HANYA:**
- ✅ **"Berikan Keputusan"** header
- ✅ **Status dropdown** 
- ✅ **"Alasan Penolakan"** header (untuk Rejected)
- ✅ **Comments textarea**
- ✅ **Submit button**

## ✅ Solusi yang Diimplementasi

### **New Approach: Hide All, Show Only What's Needed**

**Strategi Baru:**
1. **Sembunyikan SEMUA elements** terlebih dahulu
2. **Tampilkan HANYA** elements yang benar-benar diperlukan
3. **Lebih robust** daripada mencoba menyembunyikan satu per satu

### **1. Hide ALL Elements First**

```javascript
if (statusSelect.value === "Rejected" || statusSelect.value === "Revise") {
    console.log("Status Rejected/Revise - Hiding all training details");
    
    // 1. Sembunyikan SEMUA form groups terlebih dahulu
    formGroups.forEach(group => {
        group.style.display = "none";
        const inputs = group.querySelectorAll("input, select, textarea");
        inputs.forEach(input => input.removeAttribute("required"));
    });
    
    // 2. Sembunyikan SEMUA h3 headers
    const allH3 = document.querySelectorAll('h3');
    allH3.forEach(h3 => {
        h3.style.display = "none";
    });
    
    // 3. Sembunyikan SEMUA divs dan elements yang mungkin terlewat
    const hideSelectors = [
        '.alert-info',                 // Estimasi dari pemohon
        '.mb-3',                       // Durasi Training section
        '#multi_day_select',           // Durasi Training dropdown
        '#date_fields_container',      // Date fields container
        '#single_date_field',          // Single date field
        '#multi_date_fields',          // Multi date fields
        '.time-section'                // Time section
    ];
    
    hideSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (element) {
                element.style.display = "none";
            }
        });
    });
}
```

### **2. Show ONLY Required Elements**

```javascript
// 4. HANYA tampilkan elements yang benar-benar diperlukan

// Tampilkan form group yang berisi status select
const statusGroup = statusSelect.closest('.form-group');
if (statusGroup) {
    statusGroup.style.display = "block";
    console.log("Status group displayed");
}

// Tampilkan form group yang berisi comments textarea
const commentsGroup = commentsTextArea.closest('.form-group');
if (commentsGroup) {
    commentsGroup.style.display = "block";
    console.log("Comments group displayed");
}

// Pastikan submit button dan parent-nya terlihat
if (submitButton) {
    submitButton.style.display = "block";
    let parent = submitButton.parentElement;
    while (parent && parent !== document.body) {
        parent.style.display = "block";
        parent = parent.parentElement;
    }
    console.log("Submit button and parents displayed");
}
```

### **3. Restore ALL Elements for Approved**

```javascript
} else {
    console.log("Status Approved - Showing all training details");
    
    // Jika status Approved, tampilkan SEMUA elements kembali
    
    // 1. Tampilkan semua form groups
    formGroups.forEach(group => {
        group.style.display = "block";
        let inputs = group.querySelectorAll("input, select, textarea");
        inputs.forEach(input => {
            if (input.hasAttribute("data-original-required")) {
                input.setAttribute("required", "required");
            }
        });
    });
    
    // 2. Tampilkan semua h3 headers
    const allH3 = document.querySelectorAll('h3');
    allH3.forEach(h3 => {
        h3.style.display = "block";
    });
    
    // 3. Tampilkan semua elements yang mungkin disembunyikan
    const showSelectors = [
        '.alert-info',                 // Estimasi dari pemohon
        '.mb-3',                       // Durasi Training section
        '#multi_day_select',           // Durasi Training dropdown
        '#date_fields_container',      // Date fields container
        '#single_date_field',          // Single date field
        '#multi_date_fields',          // Multi date fields
        '.time-section'                // Time section
    ];
    
    showSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (element) {
                element.style.display = "block";
                // Restore required attributes
                const inputs = element.querySelectorAll("input, select, textarea");
                inputs.forEach(input => {
                    if (input.hasAttribute("data-original-required")) {
                        input.setAttribute("required", "required");
                    }
                });
            }
        });
    });
    
    console.log("All elements restored for Approved status");
}
```

## 📋 File yang Diperbaiki

### **LnD/detail_training.php**

**Location: Lines 1699-1825 (Function toggleFields)**

**Strategy Change:**
- ❌ **Old**: Mencoba menyembunyikan elements satu per satu
- ✅ **New**: Sembunyikan semua, lalu tampilkan hanya yang diperlukan

**Fix 1: Hide ALL Elements (Lines 1699-1740)**
```javascript
// APPROACH: Sembunyikan SEMUA elements, lalu tampilkan hanya yang diperlukan

// 1. Sembunyikan SEMUA form groups terlebih dahulu
formGroups.forEach(group => {
    group.style.display = "none";
});

// 2. Sembunyikan SEMUA h3 headers
const allH3 = document.querySelectorAll('h3');
allH3.forEach(h3 => {
    h3.style.display = "none";
});

// 3. Sembunyikan specific elements yang mungkin terlewat
const hideSelectors = [
    '.alert-info',                 // Estimasi dari pemohon
    '.mb-3',                       // Durasi Training section
    '#multi_day_select',           // Durasi Training dropdown
    // ... dll
];
```

**Fix 2: Show ONLY Required Elements (Lines 1741-1768)**
```javascript
// 4. HANYA tampilkan elements yang benar-benar diperlukan

// Status group
const statusGroup = statusSelect.closest('.form-group');
if (statusGroup) {
    statusGroup.style.display = "block";
}

// Comments group
const commentsGroup = commentsTextArea.closest('.form-group');
if (commentsGroup) {
    commentsGroup.style.display = "block";
}

// Submit button dan parents
if (submitButton) {
    submitButton.style.display = "block";
    let parent = submitButton.parentElement;
    while (parent && parent !== document.body) {
        parent.style.display = "block";
        parent = parent.parentElement;
    }
}
```

**Fix 3: Restore ALL for Approved (Lines 1775-1825)**
```javascript
// Tampilkan SEMUA elements kembali untuk status Approved

// 1. Tampilkan semua form groups
formGroups.forEach(group => {
    group.style.display = "block";
});

// 2. Tampilkan semua h3 headers
const allH3 = document.querySelectorAll('h3');
allH3.forEach(h3 => {
    h3.style.display = "block";
});

// 3. Tampilkan semua elements
const showSelectors = [...];
showSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
        element.style.display = "block";
    });
});
```

## 🎯 Testing Scenarios

### **Test Case 1: Status Rejected**
**Expected Result:**
```
✅ Berikan Keputusan
✅ Status [Dropdown: Rejected]
✅ Alasan Penolakan  
✅ Komentar [Textarea]
✅ Submit [Button]

❌ SEMUA YANG LAIN TERSEMBUNYI:
❌ "Estimasi dari pemohon: 2025-07-07"
❌ "Durasi Training"
❌ "Tentukan apakah training dilaksanakan..."
❌ Date fields, Time inputs, dll
```

### **Test Case 2: Status Revise**
**Expected Result:**
```
✅ Berikan Keputusan
✅ Status [Dropdown: Revise]
✅ Alasan Penolakan (atau Revisi)
✅ Komentar [Textarea]
✅ Submit [Button]

❌ SEMUA TRAINING DETAILS TERSEMBUNYI
```

### **Test Case 3: Status Approved**
**Expected Result:**
```
✅ SEMUA ELEMENTS TERLIHAT:
✅ Status dropdown
✅ Estimasi dari pemohon
✅ Durasi Training
✅ Date fields
✅ Time inputs
✅ Tempat dan Waktu
✅ Biaya dan Kontak
✅ Sharing Knowledge
✅ Internal Memo
✅ Comments
✅ Submit button
```

### **Test Case 4: Status Change**
**Expected Result:**
```
✅ Approved → Rejected: Semua tersembunyi kecuali status+comments
✅ Rejected → Approved: Semua ditampilkan kembali
✅ Revise → Approved: Semua ditampilkan kembali
✅ Approved → Revise: Semua tersembunyi kecuali status+comments
```

## 🔍 Debug Information

### **Console Logs yang Ditambahkan:**
```javascript
// Untuk troubleshooting
console.log("Status Rejected/Revise - Hiding all training details");
console.log("Status group displayed");
console.log("Comments group displayed");
console.log("Submit button and parents displayed");
console.log("Status Approved - Showing all training details");
console.log("All elements restored for Approved status");
```

### **Elements yang Ditargetkan:**

**Hidden Elements:**
- `.alert-info` - "Estimasi dari pemohon: 2025-07-07"
- `.mb-3` - "Durasi Training" section
- `#multi_day_select` - Durasi Training dropdown
- `#date_fields_container` - Date fields container
- `#single_date_field` - Single date field
- `#multi_date_fields` - Multi date fields
- `.time-section` - Time inputs
- All `h3` headers
- All `.form-group` elements

**Shown Elements (Only for Rejected/Revise):**
- Status dropdown's `.form-group`
- Comments textarea's `.form-group`
- Submit button and all its parents

## 🛠️ Benefits

### **Approach Benefits:**
- ✅ **Comprehensive**: Tidak ada element yang terlewat
- ✅ **Robust**: Hide all first, show only what's needed
- ✅ **Maintainable**: Mudah ditambah/dikurangi elements
- ✅ **Predictable**: Behavior yang konsisten

### **User Experience:**
- ✅ **Ultra Clean**: Hanya 3 elements yang terlihat untuk Rejected/Revise
- ✅ **No Confusion**: User tidak bingung dengan field yang tidak perlu
- ✅ **Fast Workflow**: Langsung fokus ke status dan comments
- ✅ **Clear Intent**: Sangat jelas bahwa training tidak disetujui

### **Business Logic:**
- ✅ **Perfect Alignment**: Interface 100% sesuai business logic
- ✅ **No Waste**: Tidak ada field yang tidak berguna
- ✅ **Efficient**: User hanya mengisi yang benar-benar diperlukan

## ✅ Hasil

### **Before Fix:**
```
❌ Status: Rejected
❌ Estimasi dari pemohon: 2025-07-07  ← Masih muncul
❌ Durasi Training                    ← Masih muncul
❌ Tentukan apakah training...        ← Masih muncul
✅ Comments
✅ Submit
```

### **After Fix:**
```
✅ Berikan Keputusan
✅ Status: Rejected
✅ Alasan Penolakan
✅ Komentar
✅ Submit

❌ SEMUA YANG LAIN TERSEMBUNYI SEMPURNA ✨
```

---

**💡 KEY STRATEGY**: "Hide All, Show Only What's Needed" approach adalah lebih robust daripada "Hide Specific Elements" karena memastikan tidak ada element yang terlewat dan memberikan kontrol penuh atas apa yang ditampilkan.
