<?php
session_start();
require_once '../config/config.php'; // Database connection and configuration

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Aks<PERSON> ditolak. Anda tidak memiliki izin untuk mengakses data ini.']);
    exit();
}

// Get filter parameters
$filterNik = isset($_GET['nik']) ? $_GET['nik'] : '';
$filterAction = isset($_GET['action']) ? $_GET['action'] : '';
$filterStartDate = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$filterEndDate = isset($_GET['end_date']) ? $_GET['end_date'] : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
$offset = ($page - 1) * $limit;

// Debug mode
$debug = isset($_GET['debug']) && $_GET['debug'] == '1';

// Prepare bindings array
$bindings = [];

// Count total records
$sqlCount = "SELECT COUNT(*) FROM karyawan_history WHERE 1=1";

// Prepare bindings array for count query
$countBindings = [];
$countTypes = '';

// Apply filters for count query
if (!empty($filterNik)) {
    $sqlCount .= " AND nik LIKE ?";
    $countBindings[] = "%{$filterNik}%";
    $countTypes .= 's';
}
if (!empty($filterAction)) {
    $sqlCount .= " AND action_type = ?";
    $countBindings[] = $filterAction;
    $countTypes .= 's';
}
if (!empty($filterStartDate)) {
    $sqlCount .= " AND DATE(change_timestamp) >= ?";
    $countBindings[] = $filterStartDate;
    $countTypes .= 's';
}
if (!empty($filterEndDate)) {
    $sqlCount .= " AND DATE(change_timestamp) <= ?";
    $countBindings[] = $filterEndDate;
    $countTypes .= 's';
}

// Debug info
$debugInfo = [];
if ($debug) {
    $debugInfo['count_sql'] = $sqlCount;
    $debugInfo['count_types'] = $countTypes;
    $debugInfo['count_bindings'] = $countBindings;
}

$stmtCount = $conn->prepare($sqlCount);
if ($stmtCount) {
    if (!empty($countBindings)) {
        $stmtCount->bind_param($countTypes, ...$countBindings);
    }
    $stmtCount->execute();

    // Check for errors
    if ($stmtCount->error) {
        if ($debug) {
            $debugInfo['count_error'] = $stmtCount->error;
        }
        $totalRecords = 0;
    } else {
        $stmtCount->bind_result($totalRecords);
        $stmtCount->fetch();
        $stmtCount->close();
    }
} else {
    if ($debug) {
        $debugInfo['count_prepare_error'] = 'Failed to prepare count SQL statement';
    }
    $totalRecords = 0;
}

// Calculate total pages
$totalPages = ceil($totalRecords / $limit);

// Get data with pagination
$sqlData = "SELECT h.history_id, h.nik, h.action_type, h.change_timestamp,
            u.name as changed_by_name
            FROM karyawan_history h
            LEFT JOIN users u ON h.changed_by = u.id
            WHERE 1=1";

// Tambahkan kondisi filter
if (!empty($filterNik)) {
    $sqlData .= " AND h.nik LIKE ?";
}
if (!empty($filterAction)) {
    $sqlData .= " AND h.action_type = ?";
}
if (!empty($filterStartDate)) {
    $sqlData .= " AND DATE(h.change_timestamp) >= ?";
}
if (!empty($filterEndDate)) {
    $sqlData .= " AND DATE(h.change_timestamp) <= ?";
}

$sqlData .= " ORDER BY h.change_timestamp DESC LIMIT ? OFFSET ?";

// Debug info
if ($debug) {
    $debugInfo['data_sql'] = $sqlData;
}

$stmtData = $conn->prepare($sqlData);
if ($stmtData) {
    // Buat array parameter baru untuk query data
    $dataBindings = [];
    $dataTypes = '';

    // Tambahkan parameter filter
    if (!empty($filterNik)) {
        $dataBindings[] = "%{$filterNik}%";
        $dataTypes .= 's';
    }
    if (!empty($filterAction)) {
        $dataBindings[] = $filterAction;
        $dataTypes .= 's';
    }
    if (!empty($filterStartDate)) {
        $dataBindings[] = $filterStartDate;
        $dataTypes .= 's';
    }
    if (!empty($filterEndDate)) {
        $dataBindings[] = $filterEndDate;
        $dataTypes .= 's';
    }

    // Tambahkan parameter limit dan offset
    $dataBindings[] = $limit;
    $dataBindings[] = $offset;
    $dataTypes .= 'ii';

    // Debug info
    if ($debug) {
        $debugInfo['data_types'] = $dataTypes;
        $debugInfo['data_bindings'] = $dataBindings;
    }

    // Bind parameter
    if (!empty($dataBindings)) {
        $stmtData->bind_param($dataTypes, ...$dataBindings);
    }

    // Execute query
    $stmtData->execute();

    // Check for errors
    if ($stmtData->error) {
        if ($debug) {
            $debugInfo['data_error'] = $stmtData->error;
        }
        $result = false;
    } else {
        $result = $stmtData->get_result();

        // Prepare data for JSON response
        $data = [];
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                // Translate action type
                $actionLabel = '';
                switch($row['action_type']) {
                    case 'INSERT': $actionLabel = 'Penambahan'; break;
                    case 'UPDATE': $actionLabel = 'Perubahan'; break;
                    case 'DELETE': $actionLabel = 'Penghapusan'; break;
                    default: $actionLabel = $row['action_type'];
                }
                
                // Format date
                $formattedDate = date('d M Y H:i:s', strtotime($row['change_timestamp']));
                
                // Add to data array
                $data[] = [
                    'history_id' => $row['history_id'],
                    'nik' => $row['nik'],
                    'action_type' => $row['action_type'],
                    'action_label' => $actionLabel,
                    'change_timestamp' => $formattedDate,
                    'changed_by_name' => $row['changed_by_name'] ?? 'Unknown'
                ];
            }
        }

        // Prepare pagination info
        $pagination = [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_records' => $totalRecords,
            'limit' => $limit
        ];

        // Prepare response
        $response = [
            'success' => true,
            'data' => $data,
            'pagination' => $pagination
        ];

        if ($debug) {
            $response['debug'] = $debugInfo;
        }

        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode($response);
    }
} else {
    if ($debug) {
        $debugInfo['data_prepare_error'] = 'Failed to prepare data SQL statement';
    }
    
    // Return error response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Gagal mempersiapkan query data.',
        'debug' => $debug ? $debugInfo : null
    ]);
}
?>
