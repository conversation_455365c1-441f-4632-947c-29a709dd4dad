<?php
/**
 * Access Control Helper
 * Mengecek level karyawan berdasarkan NIK user dari tabel karyawan
 */

/**
 * Check if user has minimum level access or special position access
 * @param int $user_id - User ID from session
 * @param int $min_level - Minimum level required (default: 4)
 * @param array $special_positions - Special positions allowed regardless of level (default: ['Supervisor', 'Chief'])
 * @param string $redirect_url - URL to redirect if access denied (default: dashboard.php)
 * @return array - Returns user data if access granted
 */
function checkUserLevel($user_id, $min_level = 4, $special_positions = ['Supervisor', 'Chief'], $redirect_url = 'dashboard.php') {
    global $conn;

    // Ambil data user dari database
    $query = "SELECT name, nik, dept, bagian, jabatan, email FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $user_data = $result->fetch_assoc();

    if (!$user_data) {
        error_log("ACCESS DENIED: User ID {$user_id} not found in users table");
        $_SESSION['error_message'] = "Akses ditolak: Data pengguna tidak ditemukan. Hubungi administrator.";
        header("Location: {$redirect_url}");
        exit();
    }

    // Cek level karyawan berdasarkan NIK user
    $user_nik = $user_data['nik'];
    $level_check_query = "SELECT level_karyawan, nama, dept, bagian, jabatan FROM karyawan WHERE nik = ?";

    $level_stmt = $conn->prepare($level_check_query);
    $level_stmt->bind_param("s", $user_nik);
    $level_stmt->execute();
    $level_result = $level_stmt->get_result();
    $karyawan_data = $level_result->fetch_assoc();

    // Jika data karyawan tidak ditemukan, blokir akses
    if (!$karyawan_data) {
        error_log("ACCESS DENIED: User NIK {$user_nik} not found in karyawan table");
        $_SESSION['error_message'] = "Akses ditolak: Data karyawan tidak ditemukan. Hubungi administrator.";
        header("Location: {$redirect_url}");
        exit();
    }

    $user_level = (int)$karyawan_data['level_karyawan'];
    $user_jabatan = $karyawan_data['jabatan'];

    // Cek apakah user memenuhi syarat akses
    $access_granted = false;
    $access_reason = "";

    // 1. Cek berdasarkan level minimum
    if ($user_level >= $min_level) {
        $access_granted = true;
        $access_reason = "level {$user_level}";
    }

    // 2. Cek berdasarkan jabatan khusus (case-insensitive)
    if (!$access_granted && !empty($special_positions)) {
        foreach ($special_positions as $position) {
            if (stripos($user_jabatan, $position) !== false) {
                $access_granted = true;
                $access_reason = "jabatan '{$user_jabatan}' (mengandung '{$position}')";
                break;
            }
        }
    }

    // Jika tidak memenuhi syarat, blokir akses
    if (!$access_granted) {
        $special_positions_str = implode(', ', $special_positions);
        error_log("ACCESS DENIED: User NIK {$user_nik} has jabatan '{$user_jabatan}', requires special position ({$special_positions_str})");
        $_SESSION['error_message'] = "Akses ditolak: Pengajuan training hanya dapat dilakukan oleh Chief Supervior ke atas. Hubungi administrator.";
        header("Location: {$redirect_url}");
        exit();
    }

    // Log successful access
    error_log("ACCESS GRANTED: User NIK {$user_nik} with {$access_reason} accessed protected resource");

    // Update user data dengan data dari tabel karyawan (lebih akurat)
    $user_data['dept'] = $karyawan_data['dept'];
    $user_data['bagian'] = $karyawan_data['bagian'];
    $user_data['jabatan'] = $karyawan_data['jabatan'];
    $user_data['level_karyawan'] = $user_level;
    $user_data['access_reason'] = $access_reason;

    return $user_data;
}

/**
 * Get user level without blocking access
 * @param int $user_id - User ID from session
 * @return array|false - Returns user data with level or false if not found
 */
function getUserLevel($user_id) {
    global $conn;

    // Ambil data user dari database
    $query = "SELECT name, nik, dept, bagian, jabatan, email FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $user_data = $result->fetch_assoc();

    if (!$user_data) {
        return false;
    }

    // Cek level karyawan berdasarkan NIK user
    $user_nik = $user_data['nik'];
    $level_check_query = "SELECT level_karyawan, nama, dept, bagian, jabatan FROM karyawan WHERE nik = ?";

    $level_stmt = $conn->prepare($level_check_query);
    $level_stmt->bind_param("s", $user_nik);
    $level_stmt->execute();
    $level_result = $level_stmt->get_result();
    $karyawan_data = $level_result->fetch_assoc();

    if (!$karyawan_data) {
        return false;
    }

    // Update user data dengan data dari tabel karyawan
    $user_data['dept'] = $karyawan_data['dept'];
    $user_data['bagian'] = $karyawan_data['bagian'];
    $user_data['jabatan'] = $karyawan_data['jabatan'];
    $user_data['level_karyawan'] = (int)$karyawan_data['level_karyawan'];

    return $user_data;
}

/**
 * Check if current user has specific level or higher, or special position
 * @param int $required_level - Required minimum level
 * @param array $special_positions - Special positions allowed regardless of level (default: ['Supervisor', 'Chief'])
 * @return bool - True if user has required level or special position
 */
function hasMinimumLevel($required_level, $special_positions = ['Supervisor', 'Chief']) {
    if (!isset($_SESSION['user_id'])) {
        return false;
    }

    $user_data = getUserLevel($_SESSION['user_id']);
    if (!$user_data) {
        return false;
    }

    // Check level first
    if ($user_data['level_karyawan'] >= $required_level) {
        return true;
    }

    // Check special positions
    if (!empty($special_positions)) {
        $user_jabatan = $user_data['jabatan'];
        foreach ($special_positions as $position) {
            if (stripos($user_jabatan, $position) !== false) {
                return true;
            }
        }
    }

    return false;
}

/**
 * Check if user has special position
 * @param int $user_id - User ID from session
 * @param array $special_positions - Special positions to check (default: ['Supervisor', 'Chief'])
 * @return bool - True if user has any of the special positions
 */
function hasSpecialPosition($user_id, $special_positions = ['Supervisor', 'Chief']) {
    $user_data = getUserLevel($user_id);
    if (!$user_data) {
        return false;
    }

    $user_jabatan = $user_data['jabatan'];
    foreach ($special_positions as $position) {
        if (stripos($user_jabatan, $position) !== false) {
            return true;
        }
    }

    return false;
}

/**
 * Get access eligibility info for user
 * @param int $user_id - User ID from session
 * @param int $min_level - Minimum level required
 * @param array $special_positions - Special positions allowed
 * @return array - Returns eligibility info
 */
function getAccessEligibility($user_id, $min_level = 4, $special_positions = ['Supervisor', 'Chief']) {
    $user_data = getUserLevel($user_id);
    if (!$user_data) {
        return [
            'eligible' => false,
            'reason' => 'Data karyawan tidak ditemukan',
            'user_level' => null,
            'user_jabatan' => null
        ];
    }

    $user_level = $user_data['level_karyawan'];
    $user_jabatan = $user_data['jabatan'];

    // Check level
    if ($user_level >= $min_level) {
        return [
            'eligible' => true,
            'reason' => "Level {$user_level} memenuhi syarat minimum (Level {$min_level})",
            'user_level' => $user_level,
            'user_jabatan' => $user_jabatan,
            'access_type' => 'level'
        ];
    }

    // Check special positions
    foreach ($special_positions as $position) {
        if (stripos($user_jabatan, $position) !== false) {
            return [
                'eligible' => true,
                'reason' => "Jabatan '{$user_jabatan}' memiliki akses khusus (mengandung '{$position}')",
                'user_level' => $user_level,
                'user_jabatan' => $user_jabatan,
                'access_type' => 'position',
                'special_position' => $position
            ];
        }
    }

    return [
        'eligible' => false,
        'reason' => "Level {$user_level} di bawah minimum (Level {$min_level}) dan jabatan '{$user_jabatan}' bukan jabatan khusus",
        'user_level' => $user_level,
        'user_jabatan' => $user_jabatan,
        'required_level' => $min_level,
        'special_positions' => $special_positions
    ];
}

/**
 * Get level description
 * @param int $level - Level number
 * @return string - Level description
 */
function getLevelDescription($level) {
    $level_descriptions = [
        1 => 'Level 1 - Staff',
        2 => 'Level 2 - Senior Staff',
        3 => 'Level 3 - Supervisor',
        4 => 'Level 4 - Assistant Manager',
        5 => 'Level 5 - Manager',
        6 => 'Level 6 - Senior Manager',
        7 => 'Level 7 - General Manager',
        8 => 'Level 8 - Director',
        9 => 'Level 9 - Executive Director',
        10 => 'Level 10 - President Director'
    ];

    return $level_descriptions[$level] ?? "Level {$level}";
}
