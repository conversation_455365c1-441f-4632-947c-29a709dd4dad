<?php
/**
 * Generate Excel Template untuk Import Kuis Training
 * File ini menghasilkan template Excel untuk mengimpor data kuis dan pertanyaan
 */

// <PERSON><PERSON> sesi jika belum dimulai
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include file konfigurasi
include_once '../config/config.php';
include '../includes/functions.php';

// Cek apakah user sudah login dan memiliki role admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Require library PhpSpreadsheet
// Anda perlu menginstal ini melalui Composer: composer require phpoffice/phpspreadsheet
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;

// Buat spreadsheet baru
$spreadsheet = new Spreadsheet();

// Set properti dokumen
$spreadsheet->getProperties()
    ->setCreator('Sistem Training')
    ->setLastModifiedBy('Sistem Training')
    ->setTitle('Template Import Kuis Training')
    ->setSubject('Template Import Kuis Training')
    ->setDescription('Template untuk mengimpor data kuis dan pertanyaan')
    ->setKeywords('training import template kuis')
    ->setCategory('Templates');

// Buat sheet Kuis
$quizzesSheet = $spreadsheet->getActiveSheet();
$quizzesSheet->setTitle('Kuis');

// Set style header
$headerStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FFFFFF'],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => '4472C4'],
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
        ],
    ],
];

// Set style instruksi
$instructionStyle = [
    'font' => [
        'italic' => true,
        'color' => ['rgb' => '808080'],
    ],
];

// Set style kolom wajib
$requiredStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FF0000'],
    ],
];

// Tambahkan instruksi ke sheet Kuis
$quizzesSheet->setCellValue('A1', 'PETUNJUK:');
$quizzesSheet->mergeCells('A1:H1');
$quizzesSheet->setCellValue('A2', '1. Kolom bertanda * wajib diisi');
$quizzesSheet->mergeCells('A2:H2');
$quizzesSheet->setCellValue('A3', '2. Judul Kelas harus sesuai dengan kelas yang sudah ada');
$quizzesSheet->mergeCells('A3:H3');
$quizzesSheet->setCellValue('A4', '3. Kolom Publikasi, Multiple Attempts, dan Acak Pertanyaan diisi dengan 1 (Ya) atau 0 (Tidak)');
$quizzesSheet->mergeCells('A4:H4');

// Terapkan style instruksi
$quizzesSheet->getStyle('A1:H4')->applyFromArray($instructionStyle);

// Tambahkan header ke sheet Kuis
$quizzesHeaders = [
    'A6' => 'Judul Kelas*',
    'B6' => 'Judul Kuis*',
    'C6' => 'Deskripsi',
    'D6' => 'Instruksi',
    'E6' => 'Batas Waktu (menit)',
    'F6' => 'Nilai Kelulusan (%)',
    'G6' => 'Publikasi (1/0)*',
    'H6' => 'Multiple Attempts (1/0)*',
    'I6' => 'Acak Pertanyaan (1/0)*'
];

foreach ($quizzesHeaders as $cell => $value) {
    $quizzesSheet->setCellValue($cell, $value);
}

// Terapkan style header
$quizzesSheet->getStyle('A6:I6')->applyFromArray($headerStyle);

// Tambahkan data contoh
$quizzesSheet->setCellValue('A7', 'Kelas Excel Dasar');
$quizzesSheet->setCellValue('B7', 'Kuis Excel Dasar');
$quizzesSheet->setCellValue('C7', 'Uji pengetahuan dasar Excel');
$quizzesSheet->setCellValue('D7', 'Jawab semua pertanyaan. Anda memiliki waktu 30 menit untuk menyelesaikan kuis ini.');
$quizzesSheet->setCellValue('E7', '30');
$quizzesSheet->setCellValue('F7', '70');
$quizzesSheet->setCellValue('G7', '1');
$quizzesSheet->setCellValue('H7', '0');
$quizzesSheet->setCellValue('I7', '1');

// Set lebar kolom
$quizzesSheet->getColumnDimension('A')->setWidth(30);
$quizzesSheet->getColumnDimension('B')->setWidth(30);
$quizzesSheet->getColumnDimension('C')->setWidth(40);
$quizzesSheet->getColumnDimension('D')->setWidth(40);
$quizzesSheet->getColumnDimension('E')->setWidth(20);
$quizzesSheet->getColumnDimension('F')->setWidth(20);
$quizzesSheet->getColumnDimension('G')->setWidth(20);
$quizzesSheet->getColumnDimension('H')->setWidth(25);
$quizzesSheet->getColumnDimension('I')->setWidth(25);

// Dapatkan daftar kelas yang sudah ada
$classes = [];
$classes_query = "SELECT title FROM training_classes ORDER BY id DESC";
$result = $conn->query($classes_query);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $classes[] = $row['title'];
    }
}

// Tambahkan validasi data untuk judul kelas
if (!empty($classes)) {
    $classList = '"' . implode(',', $classes) . '"';
    $classValidation = $quizzesSheet->getCell('A7')->getDataValidation();
    $classValidation->setType(DataValidation::TYPE_LIST);
    $classValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
    $classValidation->setAllowBlank(false);
    $classValidation->setShowInputMessage(true);
    $classValidation->setShowErrorMessage(true);
    $classValidation->setShowDropDown(true);
    $classValidation->setFormula1($classList);
    
    // Salin validasi ke range
    $quizzesSheet->setDataValidation('A7:A100', $classValidation);
}

// Buat sheet Pertanyaan
$questionsSheet = $spreadsheet->createSheet();
$questionsSheet->setTitle('Pertanyaan');

// Tambahkan instruksi ke sheet Pertanyaan
$questionsSheet->setCellValue('A1', 'PETUNJUK:');
$questionsSheet->mergeCells('A1:F1');
$questionsSheet->setCellValue('A2', '1. Kolom bertanda * wajib diisi');
$questionsSheet->mergeCells('A2:F2');
$questionsSheet->setCellValue('A3', '2. Judul Kuis harus sesuai dengan kuis yang sudah dibuat di sheet Kuis');
$questionsSheet->mergeCells('A3:F3');
$questionsSheet->setCellValue('A4', '3. Tipe Pertanyaan harus salah satu dari: pilihan_ganda, benar_salah, jawaban_singkat, esai');
$questionsSheet->mergeCells('A4:F4');
$questionsSheet->setCellValue('A5', '4. Untuk pertanyaan pilihan_ganda dan benar_salah, berikan opsi di sheet Opsi');
$questionsSheet->mergeCells('A5:F5');

// Terapkan style instruksi
$questionsSheet->getStyle('A1:F5')->applyFromArray($instructionStyle);

// Tambahkan header ke sheet Pertanyaan
$questionsHeaders = [
    'A7' => 'Judul Kuis*',
    'B7' => 'Teks Pertanyaan*',
    'C7' => 'Tipe Pertanyaan*',
    'D7' => 'Poin*',
    'E7' => 'Urutan',
    'F7' => 'Jawaban Benar',
    'G7' => 'Feedback Benar',
    'H7' => 'Feedback Salah'
];

foreach ($questionsHeaders as $cell => $value) {
    $questionsSheet->setCellValue($cell, $value);
}

// Terapkan style header
$questionsSheet->getStyle('A7:H7')->applyFromArray($headerStyle);

// Tambahkan data contoh
$questionsSheet->setCellValue('A8', 'Kuis Excel Dasar');
$questionsSheet->setCellValue('B8', 'Apa fungsi dari rumus SUM di Excel?');
$questionsSheet->setCellValue('C8', 'pilihan_ganda');
$questionsSheet->setCellValue('D8', '10');
$questionsSheet->setCellValue('E8', '1');
$questionsSheet->setCellValue('F8', '');
$questionsSheet->setCellValue('G8', 'Benar! SUM digunakan untuk menjumlahkan nilai.');
$questionsSheet->setCellValue('H8', 'Salah. SUM digunakan untuk menjumlahkan nilai dalam range sel.');

// Set lebar kolom
$questionsSheet->getColumnDimension('A')->setWidth(30);
$questionsSheet->getColumnDimension('B')->setWidth(50);
$questionsSheet->getColumnDimension('C')->setWidth(20);
$questionsSheet->getColumnDimension('D')->setWidth(10);
$questionsSheet->getColumnDimension('E')->setWidth(10);
$questionsSheet->getColumnDimension('F')->setWidth(20);
$questionsSheet->getColumnDimension('G')->setWidth(30);
$questionsSheet->getColumnDimension('H')->setWidth(30);

// Tambahkan validasi data untuk tipe pertanyaan
$typeValidation = $questionsSheet->getCell('C8')->getDataValidation();
$typeValidation->setType(DataValidation::TYPE_LIST);
$typeValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
$typeValidation->setAllowBlank(false);
$typeValidation->setShowInputMessage(true);
$typeValidation->setShowErrorMessage(true);
$typeValidation->setShowDropDown(true);
$typeValidation->setFormula1('"pilihan_ganda,benar_salah,jawaban_singkat,esai"');

// Salin validasi ke range
$questionsSheet->setDataValidation('C8:C100', $typeValidation);

// Buat sheet Opsi
$optionsSheet = $spreadsheet->createSheet();
$optionsSheet->setTitle('Opsi');

// Tambahkan instruksi ke sheet Opsi
$optionsSheet->setCellValue('A1', 'PETUNJUK:');
$optionsSheet->mergeCells('A1:E1');
$optionsSheet->setCellValue('A2', '1. Kolom bertanda * wajib diisi');
$optionsSheet->mergeCells('A2:E2');
$optionsSheet->setCellValue('A3', '2. Teks Pertanyaan harus sesuai dengan pertanyaan yang sudah dibuat di sheet Pertanyaan');
$optionsSheet->mergeCells('A3:E3');
$optionsSheet->setCellValue('A4', '3. Benar harus diisi dengan 1 (Ya) atau 0 (Tidak)');
$optionsSheet->mergeCells('A4:E4');

// Terapkan style instruksi
$optionsSheet->getStyle('A1:E4')->applyFromArray($instructionStyle);

// Tambahkan header ke sheet Opsi
$optionsHeaders = [
    'A6' => 'Teks Pertanyaan*',
    'B6' => 'Teks Opsi*',
    'C6' => 'Benar (1/0)*',
    'D6' => 'Urutan',
    'E6' => 'Feedback'
];

foreach ($optionsHeaders as $cell => $value) {
    $optionsSheet->setCellValue($cell, $value);
}

// Terapkan style header
$optionsSheet->getStyle('A6:E6')->applyFromArray($headerStyle);

// Tambahkan data contoh
$optionsSheet->setCellValue('A7', 'Apa fungsi dari rumus SUM di Excel?');
$optionsSheet->setCellValue('B7', 'Menjumlahkan nilai dalam range sel');
$optionsSheet->setCellValue('C7', '1');
$optionsSheet->setCellValue('D7', '1');
$optionsSheet->setCellValue('E7', 'Ini adalah fungsi yang benar dari SUM');

$optionsSheet->setCellValue('A8', 'Apa fungsi dari rumus SUM di Excel?');
$optionsSheet->setCellValue('B8', 'Menghitung rata-rata nilai');
$optionsSheet->setCellValue('C8', '0');
$optionsSheet->setCellValue('D8', '2');
$optionsSheet->setCellValue('E8', 'Ini adalah fungsi dari AVERAGE, bukan SUM');

$optionsSheet->setCellValue('A9', 'Apa fungsi dari rumus SUM di Excel?');
$optionsSheet->setCellValue('B9', 'Menghitung jumlah sel dalam range');
$optionsSheet->setCellValue('C9', '0');
$optionsSheet->setCellValue('D9', '3');
$optionsSheet->setCellValue('E9', 'Ini adalah fungsi dari COUNT, bukan SUM');

$optionsSheet->setCellValue('A10', 'Apa fungsi dari rumus SUM di Excel?');
$optionsSheet->setCellValue('B10', 'Mencari nilai maksimum dalam range');
$optionsSheet->setCellValue('C10', '0');
$optionsSheet->setCellValue('D10', '4');
$optionsSheet->setCellValue('E10', 'Ini adalah fungsi dari MAX, bukan SUM');

// Set lebar kolom
$optionsSheet->getColumnDimension('A')->setWidth(50);
$optionsSheet->getColumnDimension('B')->setWidth(50);
$optionsSheet->getColumnDimension('C')->setWidth(15);
$optionsSheet->getColumnDimension('D')->setWidth(10);
$optionsSheet->getColumnDimension('E')->setWidth(40);

// Set sheet pertama sebagai aktif
$spreadsheet->setActiveSheetIndex(0);

// Buat writer
$writer = new Xlsx($spreadsheet);

// Set header untuk download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="template_import_kuis.xlsx"');
header('Cache-Control: max-age=0');

// Simpan ke output
$writer->save('php://output');
exit;
?>
