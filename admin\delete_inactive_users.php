<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi variabel
$success_message = '';
$error_message = '';
$inactive_users = [];
$total_inactive = 0;

// Ambil data pengguna tidak aktif
$query = "SELECT id, name, email, nik, dept, bagian, jabatan, role_id
          FROM users
          WHERE is_active = 0
          ORDER BY name ASC";
$result = $conn->query($query);

if ($result) {
    $inactive_users = $result->fetch_all(MYSQLI_ASSOC);
    $total_inactive = count($inactive_users);
}

// Proses penghapusan masal
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_inactive') {
    try {
        // Mulai transaction
        $conn->begin_transaction();

        // Ambil data user yang akan dihapus untuk log
        $user_ids = isset($_POST['user_ids']) ? $_POST['user_ids'] : [];

        if (empty($user_ids)) {
            throw new Exception('Tidak ada pengguna yang dipilih untuk dihapus');
        }

        // Konversi array user_ids menjadi string untuk query
        $ids_string = implode(',', array_map('intval', $user_ids));

        // Ambil data user sebelum dihapus untuk log
        $query = "SELECT id, name, nik FROM users WHERE id IN ($ids_string)";
        $result = $conn->query($query);
        $users_to_delete = [];

        while ($row = $result->fetch_assoc()) {
            $users_to_delete[] = $row;
        }

        // Set NULL pada approved_by sebelum menghapus user
        $updateQuery = "UPDATE training_submissions SET approved_by = NULL WHERE approved_by IN ($ids_string)";
        $conn->query($updateQuery);

        // Hapus user dari tabel users
        $deleteQuery = "DELETE FROM users WHERE id IN ($ids_string)";
        if (!$conn->query($deleteQuery)) {
            throw new Exception("Gagal menghapus pengguna: " . $conn->error);
        }

        $deleted_count = $conn->affected_rows;

        // Log aktivitas untuk setiap user yang dihapus
        if (file_exists('../config/activity_logger.php')) {
            include_once '../config/activity_logger.php';
            if (function_exists('log_activity')) {
                foreach ($users_to_delete as $user) {
                    log_activity($_SESSION['user_id'], "Menghapus pengguna tidak aktif: {$user['name']} (NIK: {$user['nik']})", "user", [
                        'deleted_user_id' => $user['id'],
                        'deleted_user_name' => $user['name'],
                        'deleted_user_nik' => $user['nik'],
                        'bulk_delete' => true
                    ]);
                }

                // Log aktivitas bulk delete
                log_activity($_SESSION['user_id'], "Menghapus $deleted_count pengguna tidak aktif secara masal", "user", [
                    'bulk_delete' => true,
                    'deleted_count' => $deleted_count
                ]);
            }
        }

        // Commit transaction
        $conn->commit();

        $success_message = "Berhasil menghapus $deleted_count pengguna tidak aktif";

        // Refresh data pengguna tidak aktif
        $result = $conn->query("SELECT id, name, email, nik, dept, bagian, jabatan, role_id
                               FROM users
                               WHERE is_active = 0
                               ORDER BY name ASC");

        if ($result) {
            $inactive_users = $result->fetch_all(MYSQLI_ASSOC);
            $total_inactive = count($inactive_users);
        }

    } catch (Exception $e) {
        // Rollback transaction jika terjadi error
        if ($conn->connect_errno === 0) {
            $conn->rollback();
        }

        $error_message = $e->getMessage();
    }
}

// Ambil data role untuk menampilkan nama role
$roles = [];
$role_query = "SELECT id, role_name FROM roles";
$role_result = $conn->query($role_query);

if ($role_result) {
    while ($row = $role_result->fetch_assoc()) {
        $roles[$row['id']] = $row['role_name'];
    }
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    body {
        background-color: #f5f5f5;
        overflow-x: hidden;
    }

    .container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
    }

    .card {
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 20px;
    }

    .card-header {
        background-color: #BF0000 !important;
        color: white;
        padding: 15px 20px;
    }

    .card-header h3 {
        margin: 0;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
    }

    .card-header h3 i {
        margin-right: 10px;
    }

    .card-body {
        padding: 20px;
        background-color: white;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    .alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }

    .alert-warning {
        background-color: #fff3cd;
        border-color: #ffeeba;
        color: #856404;
    }

    .table-container {
        overflow-x: auto;
        margin-top: 20px;
    }

    .table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .table th {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 600;
        text-align: left;
        padding: 12px 15px;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        padding: 12px 15px;
        border-bottom: 1px solid #e9ecef;
        color: #495057;
    }

    .table tr:hover {
        background-color: #f8f9fa;
    }

    .btn-primary {
        background-color: #BF0000;
        border-color: #BF0000;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #a00000;
        border-color: #a00000;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #5a6268;
    }

    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeeba;
        border-left: 4px solid #ffc107;
        color: #856404;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .warning-box h4 {
        color: #856404;
        margin-top: 0;
        margin-bottom: 10px;
    }

    .jarak {
        height: 100px;
    }

    .select-all-container {
        margin-bottom: 15px;
    }

    .action-buttons {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .action-right-buttons {
        display: flex;
        gap: 10px;
    }

    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .scrollable-cell {
        max-width: 150px;
        overflow-x: auto;
        white-space: nowrap;
        padding: 2px 4px;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .scrollable-cell::-webkit-scrollbar {
        display: none;
    }

    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #dee2e6;
    }

    .empty-state h4 {
        margin-bottom: 10px;
        font-size: 1.5rem;
    }

    .empty-state p {
        max-width: 500px;
        margin: 0 auto;
    }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container">
    <div class="card">
        <div class="card-header">
            <h3><i class="fas fa-user-slash"></i> Hapus Pengguna Tidak Aktif</h3>
        </div>
        <div class="card-body">
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <div class="warning-box">
                <h4><i class="fas fa-exclamation-triangle"></i> Perhatian!</h4>
                <p>Fitur ini digunakan untuk menghapus pengguna yang tidak aktif secara masal. Tindakan ini tidak dapat dibatalkan. Pastikan Anda telah memilih pengguna yang benar sebelum melanjutkan.</p>
            </div>

            <?php if ($total_inactive > 0): ?>
                <form method="post" id="deleteForm">
                    <input type="hidden" name="action" value="delete_inactive">

                    <div class="action-buttons">
                        <div>
                            <a href="index.php" class="btn-secondary" style="color: white;">
                                <i class="fas fa-arrow-left"></i> Kembali
                            </a>
                        </div>
                        <div class="action-right-buttons">
                            <a href="delete_all_inactive_users.php" class="btn-danger" >
                                <i class="fas fa-trash-alt"></i> Hapus Semua (<?php echo $total_inactive; ?>)
                            </a>
                            <button type="button" id="deleteButton" class="btn-primary">
                                <i class="fas fa-trash"></i> Hapus Pengguna Terpilih
                            </button>
                        </div>
                    </div>

                    <div class="select-all-container">
                        <label>
                            <input type="checkbox" id="selectAll"> Pilih Semua (<?php echo $total_inactive; ?> pengguna)
                        </label>
                    </div>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="50">Pilih</th>
                                    <th>Nama</th>
                                    <th>Email</th>
                                    <th>NIK</th>
                                    <th>Departemen</th>
                                    <th>Bagian</th>
                                    <th>Jabatan</th>
                                    <th>Role</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($inactive_users as $user): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="user_ids[]" value="<?php echo $user['id']; ?>" class="user-checkbox">
                                        </td>
                                        <td><div class="scrollable-cell"><?php echo htmlspecialchars($user['name']); ?></div></td>
                                        <td><div class="scrollable-cell"><?php echo htmlspecialchars($user['email']); ?></div></td>
                                        <td><div class="scrollable-cell"><?php echo htmlspecialchars($user['nik']); ?></div></td>
                                        <td><div class="scrollable-cell"><?php echo htmlspecialchars($user['dept']); ?></div></td>
                                        <td><div class="scrollable-cell"><?php echo htmlspecialchars($user['bagian']); ?></div></td>
                                        <td><div class="scrollable-cell"><?php echo htmlspecialchars($user['jabatan']); ?></div></td>
                                        <td><?php echo htmlspecialchars($roles[$user['role_id']] ?? 'Unknown'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </form>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-user-check"></i>
                    <h4>Tidak Ada Pengguna Tidak Aktif</h4>
                    <p>Semua pengguna dalam sistem saat ini berstatus aktif. Tidak ada pengguna yang perlu dihapus.</p>
                    <div class="action-buttons" style="justify-content: center; margin-top: 30px;">
                        <a href="index.php" class="btn-secondary" style="color: white; text-decoration: none;">
                            <i class="fas fa-arrow-left"></i> Kembali ke Manajemen Pengguna
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select All functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
            updateDeleteButtonState();
        });
    }

    // Individual checkbox change
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllCheckbox();
            updateDeleteButtonState();
        });
    });

    // Delete button click
    const deleteButton = document.getElementById('deleteButton');
    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;

            if (checkedCount === 0) {
                alert('Silakan pilih minimal satu pengguna untuk dihapus.');
                return;
            }

            if (confirm(`Anda yakin ingin menghapus ${checkedCount} pengguna tidak aktif? Tindakan ini tidak dapat dibatalkan.`)) {
                document.getElementById('deleteForm').submit();
            }
        });

        // Initial state
        updateDeleteButtonState();
    }

    // Update Select All checkbox state
    function updateSelectAllCheckbox() {
        if (!selectAllCheckbox) return;

        const totalCheckboxes = userCheckboxes.length;
        const checkedCheckboxes = document.querySelectorAll('.user-checkbox:checked').length;

        selectAllCheckbox.checked = totalCheckboxes > 0 && checkedCheckboxes === totalCheckboxes;
        selectAllCheckbox.indeterminate = checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes;
    }

    // Update Delete button state
    function updateDeleteButtonState() {
        if (!deleteButton) return;

        const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
        deleteButton.disabled = checkedCount === 0;

        if (checkedCount === 0) {
            deleteButton.innerHTML = '<i class="fas fa-trash"></i> Hapus Pengguna Terpilih';
            deleteButton.classList.add('disabled');
        } else {
            deleteButton.innerHTML = `<i class="fas fa-trash"></i> Hapus ${checkedCount} Pengguna Terpilih`;
            deleteButton.classList.remove('disabled');
        }
    }
});
</script>
</body>
</html>
