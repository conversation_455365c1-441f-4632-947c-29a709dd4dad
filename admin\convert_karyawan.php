<?php
include '../config/config.php';

// Nonaktifkan foreign key checks sementara
$conn->query('SET FOREIGN_KEY_CHECKS=0');

try {
    // Mulai transaction
    $conn->begin_transaction();

    // Ambil data karyawan yang belum ada di users
    $query = "SELECT k.* 
              FROM karyawan k 
              LEFT JOIN users u ON k.nik = u.nik 
              WHERE u.id IS NULL";
    
    $result = $conn->query($query);

    $inserted = 0;
    $errors = [];

    while ($karyawan = $result->fetch_assoc()) {
        // Generate email default dari nik
        $default_email = strtolower($karyawan['nik'] . '@example.com');
        
        // Insert ke tabel users
        $insert_query = "INSERT INTO users (
            name, 
            nama_tetap,
            nik, 
            email,
            dept,
            bagian,
            jabatan,
            is_active,
            role_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, 1)";

        $stmt = $conn->prepare($insert_query);
        
        // Gunakan nama sebagai name dan nama_tetap
        $stmt->bind_param(
            "sssssss",
            $karyawan['nama'],
            $karyawan['nama'],
            $karyawan['nik'],
            $default_email,
            $karyawan['dept'],
            $karyawan['bagian'],
            $karyawan['jabatan']
        );

        if ($stmt->execute()) {
            $inserted++;
        } else {
            $errors[] = "Gagal menambahkan karyawan dengan NIK: {$karyawan['nik']} - Error: " . $stmt->error;
        }
    }

    // Commit transaction jika tidak ada error
    $conn->commit();
    
    echo "Proses konversi selesai!\n";
    echo "Jumlah data yang berhasil ditambahkan: $inserted\n";
    
    if (count($errors) > 0) {
        echo "\nError yang terjadi:\n";
        foreach ($errors as $error) {
            echo "- $error\n";
        }
    }

} catch (Exception $e) {
    // Rollback jika terjadi error
    $conn->rollback();
    echo "Terjadi error: " . $e->getMessage();
} finally {
    // Aktifkan kembali foreign key checks
    $conn->query('SET FOREIGN_KEY_CHECKS=1');
}
?>