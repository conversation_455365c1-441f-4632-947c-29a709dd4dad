<?php
include '../config/config.php';
// session_start() sudah dipanggil di config.php, tidak perlu dipanggil lagi

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// Fetch user details from database
$query = "SELECT id, role_id, name, nik, dept, bagian, jabatan FROM users WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

// If user not found, redirect to login
if (!$user) {
    header('Location: ../view/login.php');
    exit();
}

// Store user details in session
$_SESSION['role_id'] = $user['role_id'];
$_SESSION['full_name'] = $user['name'];     // Store full name
$_SESSION['user_nik'] = $user['nik'];       // Store NIK
$_SESSION['user_id'] = $user['id'];         // Gunakan 'id' sesuai kolom di query
$_SESSION['user_dept'] = $user['dept'];     // Store dept
$_SESSION['user_bagian'] = $user['bagian']; // Store bagian
$_SESSION['user_jabatan'] = $user['jabatan']; // Store jabatan

// Restrict access to specific roles (e.g., role_id 1 or 99)
if ($_SESSION['role_id'] != 3 && $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}
?>