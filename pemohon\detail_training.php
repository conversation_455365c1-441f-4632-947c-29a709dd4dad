<?php
include '../config/config.php';
include 'security.php';  // Pastikan security.php sudah di-include

// Pastikan CSRF token ada, jika tidak, buat baru
if (!isset($_SESSION['token'])) {
    $_SESSION['token'] = bin2hex(random_bytes(32));
}

// Validasi dan ambil ID pengajuan training dari URL
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    die("Invalid or missing training submission ID.");
}

$id = $_GET['id'];

// Ambil detail pengajuan dari database
$query = "SELECT ts.id, ts.full_name, ts.status, ts.training_topic, ts.internal_memo_image,
                 ts.nik, ts.departemen, ts.bagian, ts.jabatan,
                 ts.training_type, ts.training_skill_type, ts.start_date, ts.end_date, ts.is_confirmed,
                 ts.training_place, ts.training_cost,
                 ts.contact_person, ts.contact_number, ts.sharing_knowledge,
                 ts.additional_info, ts.approved_hrd,
                 ts.email, ts.phone, ts.comments_dept_head, ts.comments_hrd,
                 ts.comments_ga, ts.comments_fm, ts.comments_dir,
                 ts.assignment,
                 ts.training_time_start, ts.training_time_end, ts.provider_name,
                 ts.provider_address,
                 ts.provider_type, ts.trainer_name_internal, ts.trainer_nik_internal,
                 ts.trainer_department_internal,
                 ts.trainer_sub_department_internal, ts.trainer_position_internal,
                 ts.trainer_name_external, ts.additional_info_provider,
                 r.role_name AS current_approver,
                 GROUP_CONCAT(p.nama_participants SEPARATOR ', ') AS participant_names,
                 GROUP_CONCAT(p.nik_participants SEPARATOR ', ') AS participant_niks,
                 GROUP_CONCAT(p.jabatan_participants SEPARATOR ', ') AS participant_jabatans,
                 GROUP_CONCAT(p.bagian_participants SEPARATOR ', ') AS participant_bagians,
                 GROUP_CONCAT(p.departemen_participants SEPARATOR ', ') AS participant_departemens
          FROM training_submissions ts
          LEFT JOIN roles r ON ts.current_approver_role_id = r.id
          LEFT JOIN participants p ON ts.id = p.training_id
          WHERE ts.id = ?
          GROUP BY ts.id";

$stmt = $conn->prepare($query);
if ($stmt === false) {
    die("Error preparing query.");
}

$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();

// Periksa apakah data pengajuan ditemukan
if (!$row) {
    die("Training submission not found.");
}
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
    <script>
        function showAlert(message) {
            let alertBox = document.createElement("div");
            alertBox.innerText = message;
            alertBox.style.position = "fixed";
            alertBox.style.top = "20px";
            alertBox.style.left = "50%";
            alertBox.style.transform = "translateX(-50%)";
            alertBox.style.backgroundColor = "#28a745";
            alertBox.style.color = "white";
            alertBox.style.padding = "10px 20px";
            alertBox.style.borderRadius = "5px";
            alertBox.style.boxShadow = "0px 4px 6px rgba(0,0,0,0.1)";
            document.body.appendChild(alertBox);
            setTimeout(() => { alertBox.remove(); }, 3000);
        }
    </script>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container-form">
    <div class="form-container">
        <div class="latest-submissions">
            <h1>Detail Pengajuan Training</h1>
            <?php include '../config/tablea.php'; ?>
            <!-- Tampilkan komentar revisi jika ada -->
            <?php if ($row['status'] == 'Revise'): ?>
                <div class="revise-comments-section" style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #856404; margin-bottom: 15px;">
                        <i class="fas fa-edit"></i> Komentar Revisi
                    </h3>
                    <p style="color: #856404; margin-bottom: 15px;">
                        Training Anda perlu direvisi. Silakan periksa komentar di bawah ini dan lakukan perbaikan yang diperlukan.
                    </p>

                    <?php if (!empty($row['comments_dept_head'])): ?>
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 10px;">
                            <strong style="color: #495057;">Komentar Dept Head:</strong>
                            <p style="margin: 8px 0 0 0; color: #6c757d;"><?= nl2br(htmlspecialchars($row['comments_dept_head'])) ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($row['comments_hrd'])): ?>
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 10px;">
                            <strong style="color: #495057;">Komentar LnD:</strong>
                            <p style="margin: 8px 0 0 0; color: #6c757d;"><?= nl2br(htmlspecialchars($row['comments_hrd'])) ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($row['comments_ga'])): ?>
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 10px;">
                            <strong style="color: #495057;">Komentar HRGA:</strong>
                            <p style="margin: 8px 0 0 0; color: #6c757d;"><?= nl2br(htmlspecialchars($row['comments_ga'])) ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($row['comments_fm'])): ?>
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 10px;">
                            <strong style="color: #495057;">Komentar Factory Manager:</strong>
                            <p style="margin: 8px 0 0 0; color: #6c757d;"><?= nl2br(htmlspecialchars($row['comments_fm'])) ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($row['comments_dir'])): ?>
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 10px;">
                            <strong style="color: #495057;">Komentar Direktur:</strong>
                            <p style="margin: 8px 0 0 0; color: #6c757d;"><?= nl2br(htmlspecialchars($row['comments_dir'])) ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <button style="background-color: #28a745; margin-right: 10px;">
                    <a style="color: white; text-decoration: none;" href="edit_training.php?id=<?= $row['id'] ?>">
                        <i class="fas fa-edit"></i> Edit & Kirim Ulang
                    </a>
                </button>
            <?php endif; ?>

            <?php if ($row['status'] != 'Approved' && $row['status'] != 'Completed'): ?>
                <button onclick="confirmCancel(<?= htmlspecialchars($row['id'] ?? '') ?>)">
                    <i class="fas fa-times-circle"></i>
                    Batalkan Pengajuan
                </button>
            <?php else: ?>
                <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px; margin: 10px 0; color: #0c5460;">
                    <i class="fas fa-info-circle"></i>
                    <strong>Informasi:</strong> Training yang sudah disetujui atau selesai tidak dapat dibatalkan.
                </div>
            <?php endif; ?>

            <button>
            <a style="color: white; text-decoration: none;" href="dashboard.php">
                <i class="fas fa-arrow-circle-left"></i> Kembali ke Daftar Pengajuan</a>
            </button>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>
</body>
<script>
// Fungsi untuk menampilkan toast notification
function showToast(message, type = 'success', duration = 3000) {
    // Hapus toast yang sudah ada jika ada
    const existingToast = document.querySelector('.toast-notification');
    if (existingToast) {
        document.body.removeChild(existingToast);
    }

    // Buat toast baru
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;

    // Set icon berdasarkan type
    let icon = 'check';
    if (type === 'error') icon = 'exclamation-circle';
    if (type === 'warning') icon = 'exclamation-triangle';
    if (type === 'info') icon = 'info-circle';

    toast.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;
    document.body.appendChild(toast);

    // Animasi toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // Hapus toast setelah durasi tertentu
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// Fungsi untuk menampilkan dialog konfirmasi kustom
function showConfirmDialog(message, onConfirm, onCancel) {
    // Hapus dialog yang sudah ada jika ada
    const existingDialog = document.querySelector('.confirm-dialog-container');
    if (existingDialog) {
        document.body.removeChild(existingDialog);
    }

    // Buat dialog konfirmasi
    const dialogContainer = document.createElement('div');
    dialogContainer.className = 'confirm-dialog-container';

    dialogContainer.innerHTML = `
        <div class="confirm-dialog">
            <div class="confirm-dialog-header">
                <i class="fas fa-question-circle"></i>
                <h4>Konfirmasi</h4>
            </div>
            <div class="confirm-dialog-body">
                <p>${message}</p>
            </div>
            <div class="confirm-dialog-footer">
                <button class="btn btn-secondary btn-cancel">Batal</button>
                <button class="btn btn-danger btn-confirm">Ya, Lanjutkan</button>
            </div>
        </div>
    `;

    document.body.appendChild(dialogContainer);

    // Tambahkan event listener untuk tombol
    const cancelBtn = dialogContainer.querySelector('.btn-cancel');
    const confirmBtn = dialogContainer.querySelector('.btn-confirm');

    cancelBtn.addEventListener('click', () => {
        document.body.removeChild(dialogContainer);
        if (onCancel) onCancel();
    });

    confirmBtn.addEventListener('click', () => {
        document.body.removeChild(dialogContainer);
        if (onConfirm) onConfirm();
    });

    // Animasi dialog
    setTimeout(() => {
        dialogContainer.classList.add('show');
    }, 10);
}

// Tambahkan CSS untuk toast notification dan dialog konfirmasi
document.head.insertAdjacentHTML('beforeend', `
<style>
.toast-notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.toast-notification.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.toast-success i {
    color: #4CAF50;
}

.toast-error i {
    color: #F44336;
}

.toast-warning i {
    color: #FF9800;
}

.toast-info i {
    color: #2196F3;
}

.confirm-dialog-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.confirm-dialog-container.show {
    opacity: 1;
}

.confirm-dialog {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.confirm-dialog-container.show .confirm-dialog {
    transform: translateY(0);
}

.confirm-dialog-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px;
}

.confirm-dialog-header i {
    color: #BF0000;
    font-size: 1.2rem;
}

.confirm-dialog-header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: #212529;
}

.confirm-dialog-body {
    padding: 20px;
}

.confirm-dialog-body p {
    margin: 0;
    color: #495057;
}

.confirm-dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

@media screen and (max-width: 576px) {
    .confirm-dialog {
        width: 95%;
    }

    .confirm-dialog-header,
    .confirm-dialog-body,
    .confirm-dialog-footer {
        padding: 12px 15px;
    }
}
</style>
`);

function confirmCancel(id) {
    // Cek status training dari PHP
    const trainingStatus = "<?= $row['status'] ?>";

    if (trainingStatus === 'Approved' || trainingStatus === 'Completed') {
        showToast("Training yang sudah disetujui atau selesai tidak dapat dibatalkan.", 'error', 5000);
        return;
    }

    showConfirmDialog("Apakah Anda yakin ingin membatalkan pengajuan ini?", () => {
        window.location.href = "cancel.php?id=" + id + "&csrf_token=<?= htmlspecialchars($_SESSION['token'] ?? '') ?>";
    });
}
</script>
</html>

<?php $conn->close(); ?>
