
<!-- CSS moved to dashboard-style.css -->

<div class="welcome-section">
        <h1>Dashboard Persetujuan Training</h1>
        <p>Selamat datang, <?= htmlspecialchars($_SESSION['user_name'] ?? 'FM') ?></p>
    </div>

    <div class="stats-section">
        <div class="stat-card">
            <h3>Total Pengajuan</h3>
            <p><?= $stats['total'] ?></p>
        </div>
        <div class="stat-card">
            <h3>Menunggu Persetujuan</h3>
            <p><?= $stats['pending'] ?></p>
        </div>
        <div class="stat-card">
            <h3>Disetujui</h3>
            <p><?= $stats['approved'] ?></p>
        </div>
        <div class="stat-card">
            <h3>Training Hari Ini</h3>
            <p><?= $stats['today'] ?></p>
        </div>
    </div>

    <!-- Desktop Table View -->
    <div class="submissions-table desktop-table">
        <div class="table-responsive">
            <table>
                <thead>
                    <tr>
                        <th>Topik Training</th>
                        <th>Pengaju</th>
                        <th>Departemen</th>
                        <th>Peserta</th>
                        <th>Tanggal</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($result_array)): ?>
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 20px;">
                                Tidak ada training yang perlu di-approve saat ini.
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($result_array as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['training_topic']) ?></td>
                                <td><?= htmlspecialchars($row['requester_name']) ?></td>
                                <td>
                                    <span class="department-badge">
                                        <i class="fas fa-building"></i>
                                        <?= htmlspecialchars($row['departemen'] ?? 'N/A') ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="participants-count">
                                        <i class="fas fa-users"></i>
                                        <?= $row['total_participants'] ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($row['training_date_fixed']): ?>
                                        <span class="date-fixed">
                                            <i class="fas fa-calendar-check"></i>
                                            <?= htmlspecialchars($row['training_date_fixed']) ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="date-pending">
                                            <i class="fas fa-calendar-alt"></i>
                                            <?= htmlspecialchars($row['training_date']) ?>
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="status-badge <?= $row['approved_fm'] == 'Approved' ? 'status-approved' : 'status-pending' ?>">
                                        <?php if ($row['approved_fm'] == 'Approved'): ?>
                                            <i class="fas fa-check-circle"></i>
                                        <?php else: ?>
                                            <i class="fas fa-hourglass-half"></i>
                                        <?php endif; ?>
                                        <?= htmlspecialchars($row['approved_fm']) ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="detail_training.php?id=<?= $row['id'] ?>" class="action-button">
                                        <i class="fas fa-info-circle"></i>
                                        Detail
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Mobile Card View -->
    <div class="mobile-card">
        <?php if (empty($result_array)): ?>
            <div class="empty-state">
                <i class="fas fa-clipboard-list"></i>
                <h3>Tidak Ada Pengajuan</h3>
                <p>Tidak ada training yang perlu di-approve saat ini.</p>
            </div>
        <?php else: ?>
            <?php foreach ($result_array as $row): ?>
                <div class="mobile-submission-card">
                    <div class="mobile-card-header">
                        <?= htmlspecialchars($row['training_topic']) ?>
                    </div>
                    <div class="mobile-card-body">
                        <div class="mobile-card-row">
                            <div class="mobile-card-label">Pengaju:</div>
                            <div class="mobile-card-value">
                                <?= htmlspecialchars($row['requester_name']) ?>
                            </div>
                        </div>
                        <div class="mobile-card-row">
                            <div class="mobile-card-label">Departemen:</div>
                            <div class="mobile-card-value">
                                <span class="department-badge">
                                    <i class="fas fa-building"></i>
                                    <?= htmlspecialchars($row['departemen'] ?? 'N/A') ?>
                                </span>
                            </div>
                        </div>
                        <div class="mobile-card-row">
                            <div class="mobile-card-label">Peserta:</div>
                            <div class="mobile-card-value">
                                <span class="participants-count">
                                    <i class="fas fa-users"></i>
                                    <?= $row['total_participants'] ?>
                                </span>
                            </div>
                        </div>
                        <div class="mobile-card-row">
                            <div class="mobile-card-label">Tanggal:</div>
                            <div class="mobile-card-value">
                                <?php if ($row['training_date_fixed']): ?>
                                    <span class="date-fixed">
                                        <i class="fas fa-calendar-check"></i>
                                        <?= htmlspecialchars($row['training_date_fixed']) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="date-pending">
                                        <i class="fas fa-calendar-alt"></i>
                                        <?= htmlspecialchars($row['training_date']) ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="mobile-card-row">
                            <div class="mobile-card-label">Status:</div>
                            <div class="mobile-card-value">
                                <span class="status-badge <?= $row['approved_fm'] == 'Approved' ? 'status-approved' : 'status-pending' ?>">
                                    <?php if ($row['approved_fm'] == 'Approved'): ?>
                                        <i class="fas fa-check-circle"></i>
                                    <?php else: ?>
                                        <i class="fas fa-hourglass-half"></i>
                                    <?php endif; ?>
                                    <?= htmlspecialchars($row['approved_fm']) ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="mobile-card-footer">
                        <a href="detail_training.php?id=<?= $row['id'] ?>" class="action-button">
                            <i class="fas fa-info-circle"></i> Detail
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>