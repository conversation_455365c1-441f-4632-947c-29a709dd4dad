<?php
class TrainingHelper {
    private $conn;

    public function __construct($conn) {
        $this->conn = $conn;
    }

    public function autoCancelExpiredTrainings($current_date) {
        $update_query = "UPDATE training_submissions
                        SET status = 'canceled',
                            canceled_by = ?,
                            canceled_at = ?
                        WHERE start_date < ? AND status = 'pending'";

        $stmt = $this->conn->prepare($update_query);
        $canceled_at = date('Y-m-d H:i:s');
        $stmt->bind_param("iss", $_SESSION['user_id'], $canceled_at, $current_date);
        $stmt->execute();
        $stmt->close();
    }

    public function approveTraining($id) {
        $stmt = $this->conn->prepare("UPDATE training_submissions
                                    SET status = 'approved',
                                        approved_by = ?
                                    WHERE id = ? AND status = 'pending'");

        $stmt->bind_param("ii", $_SESSION['user_id'], $id);
        if ($stmt->execute() && $stmt->affected_rows > 0) {
            $_SESSION['message'] = "Training berhasil disetujui!";
        } else {
            $_SESSION['error'] = "Gagal menyetujui training atau ID tidak valid.";
        }
        $stmt->close();
        header("Location: approved.php");
        exit();
    }

    public function cancelTraining($id) {
        $stmt = $this->conn->prepare("UPDATE training_submissions
                                    SET status = 'canceled',
                                        canceled_by = ?,
                                        canceled_at = ?
                                    WHERE id = ? AND status = 'approved'");

        $canceled_at = date('Y-m-d H:i:s');
        $stmt->bind_param("isi", $_SESSION['user_id'], $canceled_at, $id);
        if ($stmt->execute() && $stmt->affected_rows > 0) {
            $_SESSION['message'] = "Training berhasil dibatalkan!";
        } else {
            $_SESSION['error'] = "Gagal membatalkan training atau ID tidak valid.";
        }
        $stmt->close();
        header("Location: approved.php");
        exit();
    }

    public function getAllTrainingData($params) {
        $base_query = "FROM training_submissions ts
                      LEFT JOIN users u ON ts.approved_by = u.id
                      LEFT JOIN users c ON ts.canceled_by = c.id
                      WHERE ts.start_date >= ?
                      AND (ts.full_name LIKE ? OR ts.training_topic LIKE ? OR ts.email LIKE ?)";

        $search_param = "%" . $params['search'] . "%";

        $data = [
            'pending' => $this->getTrainingsByStatus($base_query, 'pending', $params, $search_param),
            'approved' => $this->getTrainingsByStatus($base_query, 'approved', $params, $search_param),
            'canceled' => $this->getTrainingsByStatus($base_query, 'canceled', $params, $search_param)
        ];

        return $data;
    }

    private function getTrainingsByStatus($base_query, $status, $params, $search_param) {
        $query = "SELECT ts.*, u.name AS approved_by_name, c.name AS canceled_by_name
                 $base_query AND ts.status = ?
                 ORDER BY {$params['sort_column']} {$params['sort_order']}";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("sssss",
            $params['current_date'],
            $search_param,
            $search_param,
            $search_param,
            $status
        );
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->fetch_all(MYSQLI_ASSOC);
    }

    public function moveExpiredTrainingsToHistory() {
        $current_date = date('Y-m-d');

        // Select expired and canceled trainings
        $select_query = "SELECT ts.*,
                                u1.name AS approved_by_name,
                                u2.name AS canceled_by_name
                         FROM training_submissions ts
                         LEFT JOIN users u1 ON ts.approved_by = u1.id
                         LEFT JOIN users u2 ON ts.canceled_by = u2.id
                         WHERE ts.start_date < ?
                         OR (ts.is_confirmed = 1 AND ts.start_date < ?)
                         OR ts.status = 'canceled'";

        $stmt = $this->conn->prepare($select_query);
        $stmt->bind_param("ss", $current_date, $current_date);
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            // Insert into training_history using the existing column structure
            $insert_query = "INSERT INTO training_history
                           (training_id, user_id, training_topic, training_date,
                            training_date_fixed, status, completed_at,
                            training_type, training_place, training_cost,
                            canceled_by_name, canceled_at, approved_by_name,
                            cancellation_reason)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $hist_stmt = $this->conn->prepare($insert_query);
            $completed_at = date('Y-m-d H:i:s');

            // Map new column structure to old training_history structure
            $training_date = $row['start_date'];
            $training_date_fixed = $row['is_confirmed'] ? $row['start_date'] : null;
            $cancellation_reason = $row['comments'] ?? null;

            $hist_stmt->bind_param("iisssssssdssss",
                $row['id'],
                $row['user_id'],
                $row['training_topic'],
                $training_date,
                $training_date_fixed,
                $row['status'],
                $completed_at,
                $row['training_type'],
                $row['training_place'],
                $row['training_cost'],
                $row['canceled_by_name'],
                $row['canceled_at'],
                $row['approved_by_name'],
                $cancellation_reason
            );
            $hist_stmt->execute();
            $hist_stmt->close();

            // Delete from training_submissions
            $delete_query = "DELETE FROM training_submissions WHERE id = ?";
            $del_stmt = $this->conn->prepare($delete_query);
            $del_stmt->bind_param("i", $row['id']);
            $del_stmt->execute();
            $del_stmt->close();
        }
        $stmt->close();
    }
}

if (!function_exists('renderTrainingTable')) {
function renderTrainingTable($data, $params, $actions, $headers) {
    ob_start();
    ?>
    <table class="training-table">
        <thead>
            <tr>
                <?php foreach ($headers as $header): ?>
                    <th><?= renderSortableHeader($header, $params) ?></th>
                <?php endforeach; ?>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($data)): ?>
                <?php $no = 1; foreach ($data as $row): ?>
                    <tr>
                        <td><?= $no++ ?></td>
                        <td><?= htmlspecialchars($row['full_name']) ?></td>
                        <td><?= htmlspecialchars($row['email']) ?></td>
                        <td><?= htmlspecialchars($row['phone']) ?></td>
                        <td><?= htmlspecialchars($row['training_topic']) ?></td>
                        <td><?= htmlspecialchars($row['start_date']) ?></td>
                        <?php if (isset($row['approved_by_name'])): ?>
                            <td><?= htmlspecialchars($row['approved_by_name'] ?? 'Admin') ?></td>
                        <?php endif; ?>
                        <?php if (isset($row['canceled_by_name'])): ?>
                            <td><?= htmlspecialchars($row['canceled_by_name'] ?? 'Admin') ?></td>
                        <?php endif; ?>
                        <?php if (isset($actions['detail'])): ?>
                            <td><a class="btn-detail" href="training_detail.php?id=<?= $row['id'] ?>">Detail</a></td>
                        <?php endif; ?>
                        <?php if (isset($actions['approve'])): ?>
                            <td><a class="btn-approve" href="javascript:void(0)" onclick="confirmAction('approve', <?= $row['id'] ?>)">Approve</a></td>
                        <?php endif; ?>
                        <?php if (isset($actions['cancel'])): ?>
                            <td><a class="btn-delete" href="javascript:void(0)" onclick="confirmAction('cancel', <?= $row['id'] ?>)">Cancel</a></td>
                        <?php endif; ?>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr><td colspan="<?= count($headers) ?>" class="error-message">Tidak ada data yang tersedia.</td></tr>
            <?php endif; ?>
        </tbody>
    </table>
    <?php
    return ob_get_clean();
}

if (!function_exists('renderSortableHeader')) {
function renderSortableHeader($header, $params) {
    $sortable_columns = ['No' => 'id', 'Nama' => 'full_name', 'Tanggal' => 'start_date'];
    if (isset($sortable_columns[$header])) {
        $column = $sortable_columns[$header];
        $order = ($params['sort_column'] == $column && $params['sort_order'] == 'ASC') ? 'desc' : 'asc';
        $arrow = ($params['sort_column'] == $column) ?
                 ($params['sort_order'] == 'ASC' ? '↑' : '↓') : '';
        return "<a href='?sort={$column}&order={$order}&search={$params['search']}' class='sort-link'>{$header} {$arrow}</a>";
    }
    return $header;
}
}
}

