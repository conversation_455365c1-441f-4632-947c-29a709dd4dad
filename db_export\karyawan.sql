-- MySQL dump 10.13  Distrib 8.0.30, for Win64 (x86_64)
--
-- Host: localhost    Database: db_training
-- ------------------------------------------------------
-- Server version	8.0.30

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `karyawan`
--

DROP TABLE IF EXISTS `karyawan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `karyawan` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nik` varchar(20) NOT NULL,
  `card_number` varchar(50) DEFAULT NULL,
  `normalized_nik` varchar(50) DEFAULT NULL,
  `nama` varchar(100) NOT NULL,
  `tgl_masuk` varchar(20) NOT NULL,
  `jk` varchar(1) NOT NULL COMMENT 'L = Laki-laki, P = Perempuan',
  `level_karyawan` int NOT NULL,
  `tgl_lahir` varchar(20) NOT NULL,
  `agama` varchar(20) NOT NULL,
  `pendidikan_akhir` varchar(50) NOT NULL,
  `no_telp` varchar(255) NOT NULL,
  `dept` varchar(50) NOT NULL,
  `bagian` varchar(50) NOT NULL,
  `jabatan` varchar(50) NOT NULL,
  `group` varchar(50) NOT NULL,
  `status` varchar(20) NOT NULL,
  `pt` varchar(50) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nik` (`nik`),
  KEY `idx_search` (`nik`,`nama`,`dept`,`bagian`,`jabatan`),
  KEY `idx_dept` (`dept`),
  KEY `idx_bagian` (`bagian`),
  KEY `idx_jabatan` (`jabatan`),
  KEY `idx_group` (`group`),
  KEY `idx_normalized_nik` (`normalized_nik`),
  KEY `idx_card_number` (`card_number`)
) ENGINE=InnoDB AUTO_INCREMENT=46310 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `karyawan`
--

LOCK TABLES `karyawan` WRITE;
/*!40000 ALTER TABLE `karyawan` DISABLE KEYS */;
/*!40000 ALTER TABLE `karyawan` ENABLE KEYS */;
UNLOCK TABLES;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = cp850 */ ;
/*!50003 SET character_set_results = cp850 */ ;
/*!50003 SET collation_connection  = cp850_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `karyawan_after_insert` AFTER INSERT ON `karyawan` FOR EACH ROW BEGIN
    DECLARE user_id INT;
    SET user_id = IFNULL((SELECT id FROM users WHERE id = @current_user_id), 0);
    
    SET @new_data = JSON_OBJECT(
        'id', NEW.id,
        'nik', NEW.nik,
        'card_number', NEW.card_number,
        'normalized_nik', NEW.normalized_nik,
        'nama', NEW.nama,
        'tgl_masuk', NEW.tgl_masuk,
        'jk', NEW.jk,
        'level_karyawan', NEW.level_karyawan,
        'tgl_lahir', NEW.tgl_lahir,
        'agama', NEW.agama,
        'pendidikan_akhir', NEW.pendidikan_akhir,
        'no_telp', NEW.no_telp,
        'dept', NEW.dept,
        'bagian', NEW.bagian,
        'jabatan', NEW.jabatan,
        'group', NEW.`group`,
        'status', NEW.status,
        'pt', NEW.pt
    );
    
    INSERT INTO karyawan_history (
        action_type, karyawan_id, nik, card_number, normalized_nik, nama, tgl_masuk, jk,
        level_karyawan, tgl_lahir, agama, pendidikan_akhir, no_telp,
        dept, bagian, jabatan, `group`, status, pt, changed_by,
        old_data, new_data
    )
    VALUES (
        'INSERT', NEW.id, NEW.nik, NEW.card_number, NEW.normalized_nik, NEW.nama, NEW.tgl_masuk, NEW.jk,
        NEW.level_karyawan, NEW.tgl_lahir, NEW.agama, NEW.pendidikan_akhir, NEW.no_telp,
        NEW.dept, NEW.bagian, NEW.jabatan, NEW.`group`, NEW.status, NEW.pt,
        user_id,
        NULL, @new_data
    );
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-23 22:13:51
