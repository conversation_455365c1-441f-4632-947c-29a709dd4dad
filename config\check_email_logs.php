<?php
/**
 * Script untuk memeriksa log email dan status notifikasi
 */

echo "📧 Email Notification Log Checker\n";
echo "==================================\n\n";

// Cari file log PHP
$possible_log_files = [
    'C:\laragon\logs\php_errors.log',
    'C:\laragon\etc\apache2\logs\error.log',
    'C:\xampp\php\logs\php_error_log',
    'C:\wamp\logs\php_error.log',
    ini_get('error_log'),
    __DIR__ . '/../logs/error.log',
    __DIR__ . '/../error.log'
];

$log_file = null;
foreach ($possible_log_files as $file) {
    if ($file && file_exists($file)) {
        $log_file = $file;
        break;
    }
}

if (!$log_file) {
    echo "❌ Could not find PHP error log file\n";
    echo "📝 Checked locations:\n";
    foreach ($possible_log_files as $file) {
        if ($file) {
            echo "   - $file\n";
        }
    }
    echo "\n💡 You can check your PHP configuration with: php -i | grep error_log\n";
    exit(1);
}

echo "📁 Found log file: $log_file\n\n";

// Baca log file dan filter email-related entries
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    
    // Filter untuk email-related logs dari 10 menit terakhir
    $email_logs = [];
    $cutoff_time = time() - (10 * 60); // 10 menit yang lalu
    
    foreach ($lines as $line) {
        if (empty(trim($line))) continue;
        
        // Cek apakah line mengandung keyword email
        $email_keywords = [
            'SENDING EMAIL',
            'ATTEMPTING',
            'Email notification',
            'PHPMailer',
            'SMTP',
            'send_status_update_notification',
            'send_mail',
            'Email test',
            'notification_helper'
        ];
        
        $is_email_log = false;
        foreach ($email_keywords as $keyword) {
            if (stripos($line, $keyword) !== false) {
                $is_email_log = true;
                break;
            }
        }
        
        if ($is_email_log) {
            $email_logs[] = $line;
        }
    }
    
    if (empty($email_logs)) {
        echo "📭 No recent email-related logs found\n";
        echo "💡 Try running the test again: php config/test_notification.php\n";
    } else {
        echo "📬 Recent email-related logs (" . count($email_logs) . " entries):\n";
        echo str_repeat("-", 80) . "\n";
        
        // Tampilkan 20 log terakhir
        $recent_logs = array_slice($email_logs, -20);
        foreach ($recent_logs as $log) {
            // Format log untuk readability
            $formatted_log = trim($log);
            
            // Highlight status
            if (stripos($formatted_log, '✅') !== false || stripos($formatted_log, 'successfully') !== false) {
                echo "✅ $formatted_log\n";
            } elseif (stripos($formatted_log, '❌') !== false || stripos($formatted_log, 'failed') !== false || stripos($formatted_log, 'error') !== false) {
                echo "❌ $formatted_log\n";
            } elseif (stripos($formatted_log, 'ATTEMPTING') !== false || stripos($formatted_log, 'SENDING') !== false) {
                echo "📤 $formatted_log\n";
            } else {
                echo "📝 $formatted_log\n";
            }
        }
    }
} else {
    echo "❌ Log file not accessible: $log_file\n";
}

echo "\n" . str_repeat("-", 80) . "\n";
echo "🔍 Log file location: $log_file\n";
echo "⏰ Showing logs from last 10 minutes\n";
echo "🔄 Run this script again to see updated logs\n";
?>
