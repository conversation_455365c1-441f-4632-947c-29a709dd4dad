<?php
include '../config/config.php';
include 'security.php';
include '../includes/class_role_helper.php';
include '../includes/grade_proposal_helper.php';
include '../includes/notification_helper.php';
include '../includes/permission_helper.php';

// Get class ID from URL
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;

// Check if class exists
$class_query = "SELECT * FROM training_classes WHERE id = ?";
$stmt = $conn->prepare($class_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$class = $result->fetch_assoc();

if (!$class) {
    $_SESSION['error'] = "Kelas tidak ditemukan.";
    header("Location: classroom.php");
    exit();
}

// Check if user is an instructor in this class
if (!isInstructor($_SESSION['user_id'], $class_id)) {
    $_SESSION['error'] = "Anda tidak memiliki akses ke halaman ini.";
    header("Location: classroom_detail.php?id=$class_id");
    exit();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['review_proposal'])) {
    $proposal_id = intval($_POST['proposal_id']);
    $status = $_POST['status'];
    $comments = $_POST['comments'];
    
    if ($status != 'approved' && $status != 'rejected') {
        $_SESSION['error'] = "Status tidak valid.";
    } else {
        $result = reviewGradeProposal($proposal_id, $_SESSION['user_id'], $status, $comments);
        
        if ($result) {
            $_SESSION['success'] = "Pengajuan nilai berhasil " . ($status == 'approved' ? 'disetujui' : 'ditolak') . ".";
        } else {
            $_SESSION['error'] = "Gagal memproses pengajuan nilai.";
        }
    }
    
    // Redirect to refresh the page
    header("Location: review_grade_proposals.php?class_id=$class_id");
    exit();
}

// Get pending grade proposals
$proposals = getPendingGradeProposals($class_id);

// Page title
$page_title = "Tinjau Pengajuan Nilai - " . $class['title'];
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <link rel="stylesheet" href="../asset/css/bootstrap.min.css">
    <link rel="stylesheet" href="../asset/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .proposal-card {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .proposal-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
        }
        .proposal-body {
            padding: 15px;
        }
        .proposal-footer {
            padding: 15px;
            background-color: #f8f9fa;
            border-top: 1px solid #ddd;
        }
        .grade-display {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            text-align: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="classroom.php">Kelas</a></li>
                        <li class="breadcrumb-item"><a href="classroom_detail.php?id=<?= $class_id ?>"><?= $class['title'] ?></a></li>
                        <li class="breadcrumb-item active">Tinjau Pengajuan Nilai</li>
                    </ol>
                </nav>
                
                <h2 class="mb-4">Tinjau Pengajuan Nilai</h2>
                
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success">
                        <?= $_SESSION['success'] ?>
                        <?php unset($_SESSION['success']); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger">
                        <?= $_SESSION['error'] ?>
                        <?php unset($_SESSION['error']); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (empty($proposals)): ?>
                    <div class="alert alert-info">
                        Tidak ada pengajuan nilai yang menunggu peninjauan.
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($proposals as $proposal): ?>
                            <div class="col-md-6">
                                <div class="proposal-card">
                                    <div class="proposal-header">
                                        <h5 class="mb-0"><?= htmlspecialchars($proposal['assignment_title']) ?></h5>
                                        <small class="text-muted">Diajukan oleh <?= htmlspecialchars($proposal['proposed_by_name']) ?> pada <?= date('d M Y H:i', strtotime($proposal['created_at'])) ?></small>
                                    </div>
                                    <div class="proposal-body">
                                        <p><strong>Peserta:</strong> <?= htmlspecialchars($proposal['student_name']) ?></p>
                                        <div class="grade-display"><?= number_format($proposal['proposed_grade'], 2) ?></div>
                                        <h6>Umpan Balik untuk Peserta:</h6>
                                        <p><?= nl2br(htmlspecialchars($proposal['feedback'])) ?></p>
                                        
                                        <form method="post" action="">
                                            <input type="hidden" name="proposal_id" value="<?= $proposal['id'] ?>">
                                            
                                            <div class="mb-3">
                                                <label for="comments-<?= $proposal['id'] ?>" class="form-label">Komentar Peninjauan (opsional):</label>
                                                <textarea class="form-control" id="comments-<?= $proposal['id'] ?>" name="comments" rows="3"></textarea>
                                            </div>
                                            
                                            <div class="d-flex justify-content-between">
                                                <button type="submit" name="review_proposal" class="btn btn-success" onclick="document.querySelector('input[name=status]').value='approved';">
                                                    <i class="fas fa-check"></i> Setujui
                                                </button>
                                                <button type="submit" name="review_proposal" class="btn btn-danger" onclick="document.querySelector('input[name=status]').value='rejected';">
                                                    <i class="fas fa-times"></i> Tolak
                                                </button>
                                                <input type="hidden" name="status" value="">
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="../asset/js/bootstrap.bundle.min.js"></script>
</body>
</html>
