<?php
/**
 * This file handles recording batch employee history
 * It is used by upload_karyawan.php to record a single history entry for the entire batch
 */

// Function to record batch employee history
function recordBatchEmployeeHistory($conn, $action_type, $niks, $mode, $total_success, $total_error, $total_skipped) {
    if (!isset($_SESSION['user_id'])) {
        error_log("Cannot record batch history: User ID not set in session");
        return false;
    }
    
    $user_id = $_SESSION['user_id'];
    $timestamp = date('Y-m-d H:i:s');
    
    // Create batch information as JSON
    $batch_info = [
        'action_type' => $action_type,
        'mode' => $mode,
        'total_success' => $total_success,
        'total_error' => $total_error,
        'total_skipped' => $total_skipped,
        'niks' => $niks,
        'timestamp' => $timestamp,
        'user_id' => $user_id
    ];
    
    $batch_info_json = json_encode($batch_info, JSON_UNESCAPED_UNICODE);
    
    // Insert batch history record
    try {
        // Use PDO for better error handling
        $pdo = new PDO("mysql:host={$GLOBALS['db_host']};dbname={$GLOBALS['db_name']};charset=utf8", $GLOBALS['db_user'], $GLOBALS['db_pass']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Insert into a new table for batch history
        $stmt = $pdo->prepare("
            INSERT INTO karyawan_batch_history (
                action_type, 
                batch_data, 
                changed_by, 
                change_timestamp
            ) VALUES (?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $action_type,
            $batch_info_json,
            $user_id,
            $timestamp
        ]);
        
        if ($result) {
            error_log("Batch history recorded successfully");
            return $pdo->lastInsertId();
        } else {
            error_log("Failed to record batch history");
            return false;
        }
    } catch (PDOException $e) {
        error_log("PDO Error recording batch history: " . $e->getMessage());
        
        // Check if the table doesn't exist and create it
        if (strpos($e->getMessage(), "Table") !== false && strpos($e->getMessage(), "doesn't exist") !== false) {
            try {
                createBatchHistoryTable($pdo);
                // Try again after creating the table
                return recordBatchEmployeeHistory($conn, $action_type, $niks, $mode, $total_success, $total_error, $total_skipped);
            } catch (Exception $e2) {
                error_log("Failed to create batch history table: " . $e2->getMessage());
                return false;
            }
        }
        
        return false;
    } catch (Exception $e) {
        error_log("General Error recording batch history: " . $e->getMessage());
        return false;
    }
}

// Function to create the batch history table if it doesn't exist
function createBatchHistoryTable($pdo) {
    $sql = "
    CREATE TABLE IF NOT EXISTS karyawan_batch_history (
        batch_id INT AUTO_INCREMENT PRIMARY KEY,
        action_type VARCHAR(50) NOT NULL COMMENT 'BATCH_INSERT, BATCH_UPDATE, BATCH_DELETE, BATCH_ROLLBACK, etc.',
        batch_data TEXT NOT NULL COMMENT 'JSON data of the batch operation',
        changed_by INT NOT NULL,
        change_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_rollback_capable BOOLEAN DEFAULT FALSE COMMENT 'Whether this batch can be rolled back',
        rollback_status ENUM('NONE', 'AVAILABLE', 'ROLLED_BACK', 'PARTIAL') DEFAULT 'NONE'
    )
    ";

    return $pdo->exec($sql);
}

// Function to get batch history details
function getBatchHistoryDetails($batch_id) {
    try {
        $pdo = new PDO("mysql:host={$GLOBALS['db_host']};dbname={$GLOBALS['db_name']};charset=utf8", $GLOBALS['db_user'], $GLOBALS['db_pass']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("SELECT * FROM karyawan_batch_history WHERE batch_id = ?");
        $stmt->execute([$batch_id]);
        
        $batch = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($batch) {
            $batch['batch_data'] = json_decode($batch['batch_data'], true);
            return $batch;
        }
        
        return null;
    } catch (Exception $e) {
        error_log("Error getting batch history details: " . $e->getMessage());
        return null;
    }
}

// Function to delete batch history
function deleteBatchHistory($batch_id) {
    try {
        $pdo = new PDO("mysql:host={$GLOBALS['db_host']};dbname={$GLOBALS['db_name']};charset=utf8", $GLOBALS['db_user'], $GLOBALS['db_pass']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $stmt = $pdo->prepare("DELETE FROM karyawan_batch_history WHERE batch_id = ?");
        return $stmt->execute([$batch_id]);
    } catch (Exception $e) {
        error_log("Error deleting batch history: " . $e->getMessage());
        return false;
    }
}

// Function to check if batch can be rolled back
function canRollback($batch_data, $action_type, $batch_id = null) {
    // Check if action type supports rollback
    $rollback_supported_actions = ['BATCH_INSERT', 'BATCH_UPDATE', 'BATCH_DELETE'];

    // Extract base action type (remove _ROLLED_BACK suffix if present)
    $base_action_type = str_replace('_ROLLED_BACK', '', $action_type);

    if (!in_array($base_action_type, $rollback_supported_actions)) {
        return false;
    }

    // Allow rollback even for _ROLLED_BACK batches (rollback again functionality)
    // This enables continuous rollback capability

    // If batch_id is provided, check database flags
    if ($batch_id !== null) {
        try {
            $pdo = new PDO("mysql:host={$GLOBALS['db_host']};dbname={$GLOBALS['db_name']};charset=utf8", $GLOBALS['db_user'], $GLOBALS['db_pass']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            $stmt = $pdo->prepare("SELECT is_rollback_capable, rollback_status FROM karyawan_batch_history WHERE batch_id = ?");
            $stmt->execute([$batch_id]);
            $db_flags = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($db_flags) {
                // Check database rollback status
                if ($db_flags['rollback_status'] === 'ROLLED_BACK') {
                    return false;
                }

                // Check database rollback capability flag
                if ($db_flags['is_rollback_capable']) {
                    return true;
                }
            }
        } catch (Exception $e) {
            error_log("Error checking database rollback flags: " . $e->getMessage());
        }
    }

    // For INSERT operations, always rollback capable (fallback)
    if ($base_action_type === 'BATCH_INSERT') {
        return true;
    }

    // For INDIVIDUAL_DELETE, make it rollback capable (restore functionality)
    if ($action_type === 'INDIVIDUAL_DELETE') {
        return true; // Always allow restore of individual deletes
    }

    // For BATCH_ROLLBACK, check if it can be undone
    if ($action_type === 'BATCH_ROLLBACK') {
        return true; // Always allow undo rollback
    }

    // For UPDATE and DELETE, check if enhanced data is available
    if (isset($batch_data['rollback_capable']) && $batch_data['rollback_capable'] === true) {
        return true;
    }

    // Check for enhanced history data
    if (isset($batch_data['enhanced_history']) && $batch_data['enhanced_history'] === true) {
        return true;
    }

    // Check for before_data (enhanced rollback capability)
    if (isset($batch_data['before_data']) && !empty($batch_data['before_data'])) {
        return true;
    }

    // For BATCH_UPDATE and BATCH_DELETE, allow rollback if NIKs are available
    if (in_array($base_action_type, ['BATCH_UPDATE', 'BATCH_DELETE']) && isset($batch_data['niks']) && !empty($batch_data['niks'])) {
        return true;
    }

    return false;
}

// Function to update rollback status
function updateRollbackStatus($batch_id, $status) {
    try {
        $pdo = new PDO("mysql:host={$GLOBALS['db_host']};dbname={$GLOBALS['db_name']};charset=utf8", $GLOBALS['db_user'], $GLOBALS['db_pass']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $stmt = $pdo->prepare("UPDATE karyawan_batch_history SET rollback_status = ? WHERE batch_id = ?");
        return $stmt->execute([$status, $batch_id]);
    } catch (Exception $e) {
        error_log("Error updating rollback status: " . $e->getMessage());
        return false;
    }
}
