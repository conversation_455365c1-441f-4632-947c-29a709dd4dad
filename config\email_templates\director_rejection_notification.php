<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Training Ditolak - Training Center PAS</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .email-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .header .icon {
            font-size: 48px;
            margin-bottom: 10px;
            display: block;
        }
        .content {
            padding: 30px;
        }
        .training-info {
            background: #f8f9fa;
            border-left: 4px solid #dc3545;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .training-info h3 {
            margin-top: 0;
            color: #dc3545;
            font-size: 18px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            min-width: 140px;
            color: #555;
        }
        .info-value {
            flex: 1;
            color: #333;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .comments-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .comments-section h4 {
            margin-top: 0;
            color: #856404;
            font-size: 16px;
        }
        .comments-text {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            font-style: italic;
            color: #333;
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 600;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
        .footer p {
            margin: 5px 0;
        }
        .next-steps {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .next-steps h4 {
            margin-top: 0;
            color: #004085;
            font-size: 16px;
        }
        .next-steps ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .next-steps li {
            margin-bottom: 8px;
            color: #004085;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <span class="icon">❌</span>
            <h1>Pengajuan Training Ditolak</h1>
            <p>Pengajuan Pengajuan Training Anda telah ditolak oleh Direktur</p>
        </div>
        
        <div class="content">
            <p>Yth. <strong><?= htmlspecialchars($data['recipient_name']) ?></strong>,</p>
            
            <p>Mohon maaf untuk memberitahukan bahwa pengajuan Pengajuan Training Anda telah <strong>ditolak</strong> oleh Direktur.</p>
            
            <div class="training-info">
                <h3>📋 Detail Training</h3>
                <div class="info-row">
                    <div class="info-label">ID Training:</div>
                    <div class="info-value">#<?= htmlspecialchars($data['training_id']) ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Topik Training:</div>
                    <div class="info-value"><strong><?= htmlspecialchars($data['training_topic']) ?></strong></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Tanggal Training:</div>
                    <div class="info-value"><?= htmlspecialchars($data['training_date']) ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Pemohon:</div>
                    <div class="info-value"><?= htmlspecialchars($data['requester_name']) ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Departemen:</div>
                    <div class="info-value"><?= htmlspecialchars($data['departemen']) ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Status:</div>
                    <div class="info-value">
                        <span class="status-badge status-rejected">Ditolak</span>
                    </div>
                </div>
            </div>

            <?php if (!empty($data['comments'])): ?>
            <div class="comments-section">
                <h4>💬 Alasan Penolakan dari Direktur</h4>
                <div class="comments-text">
                    <?= nl2br(htmlspecialchars($data['comments'])) ?>
                </div>
            </div>
            <?php endif; ?>

            <div class="next-steps">
                <h4>🔄 Langkah Selanjutnya</h4>
                <ul>
                    <li>Anda dapat mengajukan Pengajuan Training yang baru dengan topik atau provider yang berbeda</li>
                    <li>Pertimbangkan untuk mengajukan training internal sebagai alternatif</li>
                    <li>Konsultasikan dengan atasan langsung atau L&D untuk saran training yang sesuai</li>
                    <li>Tinjau kembali kebutuhan training dan sesuaikan dengan kebijakan perusahaan</li>
                </ul>
            </div>

            <div class="action-buttons" style="color:white;">
                <a href="<?= htmlspecialchars($data['dashboard_url']) ?>" class="btn">
                    📊 Lihat Dashboard Training
                </a>
            </div>

            <p>Jika Anda memiliki pertanyaan atau memerlukan klarifikasi lebih lanjut, silakan hubungi departemen L&D atau atasan langsung Anda.</p>
            
            <p>Terima kasih atas pengertian Anda.</p>
        </div>
        
        <div class="footer">
            <p><strong>Training Center PAS</strong></p>
            <p>Email ini dikirim secara otomatis oleh sistem. Mohon tidak membalas email ini.</p>
            <p>Tanggal: <?= date('d F Y H:i') ?> WIB</p>
        </div>
    </div>
</body>
</html>
