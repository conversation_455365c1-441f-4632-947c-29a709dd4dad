<?php
session_start();
include '../config/config.php';
include '../config/notification_helper.php';  // Include notification helper

// Validasi token untuk mencegah duplikasi submit
if (!isset($_POST['form_token']) || !isset($_SESSION['form_token']) || $_POST['form_token'] !== $_SESSION['form_token']) {
    // Token tidak valid atau tidak cocok, kemungkinan duplikasi submit
    $_SESSION['errors'] = ['Pengajuan training gagal: Permintaan tidak valid atau duplikat. Silakan coba lagi.'];
    header("Location: form.php");
    exit();
}

// Periksa apakah token sudah kadaluarsa (lebih dari 10 menit)
if (!isset($_SESSION['form_token_time']) || (time() - $_SESSION['form_token_time']) > 600) {
    $_SESSION['errors'] = ['Pengajuan training gagal: Sesi telah kadaluarsa. Silakan coba lagi.'];
    header("Location: form.php");
    exit();
}

// Hapus token setelah digunakan untuk mencegah penggunaan kembali
$used_token = $_SESSION['form_token'];
unset($_SESSION['form_token']);
unset($_SESSION['form_token_time']);

// Validasi CSRF token
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['token']) {
    $_SESSION['errors'] = ['Invalid token'];
    header("Location: form.php");
    exit();
}

$errors = [];

// Validasi tanggal training (minimal 1 bulan ke depan)
$training_date = $_POST['start_date'] ?? '';
$min_date = date('Y-m-d', strtotime('+1 month'));

if (!DateTime::createFromFormat('Y-m-d', $training_date)) {
    $errors[] = "Format tanggal training tidak valid!";
} elseif ($training_date < $min_date) {
    $errors[] = "Tanggal training harus minimal 1 bulan ke depan!";
}

// Validasi email
if (empty($_POST['email'])) {
    $errors[] = "Email wajib diisi!";
} elseif (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
    $errors[] = "Format email tidak valid!";
}

// Validasi nomor telepon
if (empty($_POST['phone'])) {
    $errors[] = "Nomor telepon wajib diisi!";
} elseif (!preg_match("/^\d{10,15}$/", $_POST['phone'])) {
    $errors[] = "Nomor telepon harus berisi 10-15 digit angka!";
}

// Validasi input lainnya
$requiredFields = ['full_name', 'training_topic', 'start_date', 'additional_info'];
foreach ($requiredFields as $field) {
    if (empty($_POST[$field])) {
        $errors[] = "Field " . ucfirst(str_replace('_', ' ', $field)) . " wajib diisi!";
    }
}

// Validasi peserta
if (!isset($_POST['nama']) || !is_array($_POST['nama']) || empty($_POST['nama'])) {
    $errors[] = "Minimal harus ada satu peserta training!";
}

// Validasi data peserta dan hapus duplikat
if (isset($_POST['nama']) && is_array($_POST['nama'])) {
    // Array untuk menyimpan NIK yang sudah diproses
    $processedNIKs = [];

    // Array untuk menyimpan data peserta yang sudah divalidasi (tanpa duplikat)
    $validatedParticipants = [];

    foreach ($_POST['nama'] as $key => $nama) {
        $nik = $_POST['nik'][$key] ?? '';
        $departemen = $_POST['departemen'][$key] ?? '';
        $bagian = $_POST['bagian'][$key] ?? '';
        $jabatan = $_POST['jabatan'][$key] ?? '';

        // Validasi data lengkap
        if (empty($nama) || empty($nik) || empty($departemen) || empty($bagian) || empty($jabatan)) {
            $errors[] = "Data peserta tidak lengkap!";
            break;
        }

        // Normalisasi NIK untuk pengecekan duplikat (hapus karakter non-alphanumeric dan lowercase)
        $normalizedNIK = preg_replace('/[^a-zA-Z0-9]/', '', strtolower($nik));

        // Cek apakah NIK sudah ada dalam daftar yang diproses
        if (!in_array($normalizedNIK, $processedNIKs)) {
            // Tambahkan ke daftar NIK yang sudah diproses
            $processedNIKs[] = $normalizedNIK;

            // Tambahkan ke daftar peserta yang valid
            $validatedParticipants[] = [
                'nama' => $nama,
                'nik' => $nik,
                'departemen' => $departemen,
                'bagian' => $bagian,
                'jabatan' => $jabatan
            ];
        } else {
            // Log duplikat yang ditemukan
            error_log("Duplikat NIK terdeteksi dan dihapus: $nik ($nama)");
        }
    }

    // Jika ada duplikat yang dihapus, perbarui array POST
    if (count($validatedParticipants) < count($_POST['nama'])) {
        // Reset array POST untuk peserta
        $_POST['nama'] = [];
        $_POST['nik'] = [];
        $_POST['departemen'] = [];
        $_POST['bagian'] = [];
        $_POST['jabatan'] = [];

        // Isi ulang dengan data yang sudah divalidasi
        foreach ($validatedParticipants as $participant) {
            $_POST['nama'][] = $participant['nama'];
            $_POST['nik'][] = $participant['nik'];
            $_POST['departemen'][] = $participant['departemen'];
            $_POST['bagian'][] = $participant['bagian'];
            $_POST['jabatan'][] = $participant['jabatan'];
        }

        // Log jumlah peserta setelah penghapusan duplikat
        error_log("Jumlah peserta setelah penghapusan duplikat: " . count($_POST['nama']));
    }
}

// Jika ada error, kembali ke form
if (!empty($errors)) {
    $_SESSION['errors'] = $errors;
    header("Location: form.php");
    exit();
}

try {
    // Mulai transaction
    $conn->begin_transaction();

    // Get the NIK value from session or POST
    $nik = isset($_SESSION['user_nik']) ? $_SESSION['user_nik'] : (isset($_POST['nik']) && !is_array($_POST['nik']) ? $_POST['nik'] : '');

    // Debug NIK value
    error_log("NIK value: " . $nik);
    error_log("SESSION user_nik: " . (isset($_SESSION['user_nik']) ? $_SESSION['user_nik'] : 'not set'));
    error_log("POST nik: " . (isset($_POST['nik']) ? (is_array($_POST['nik']) ? 'is array' : $_POST['nik']) : 'not set'));

    // Prepare all variables for the query
    $user_id = $_SESSION['user_id'];
    $full_name = $_POST['full_name'];
    $department = $_POST['department'];
    $bagian = $_POST['Bagian'];
    $jabatan = $_POST['Jabatan'];
    $email = $_POST['email'];
    $phone = $_POST['phone'];
    $training_topic = $_POST['training_topic'];
    $training_date = $_POST['start_date'];
    $training_skill_type = $_POST['training_skill_type'] ?? 'Soft Skill';
    $additional_info = $_POST['additional_info'];
    $status = 'pending';

    // Escape all variables to prevent SQL injection
    $full_name = $conn->real_escape_string($full_name);
    $nik = $conn->real_escape_string($nik);
    $department = $conn->real_escape_string($department);
    $bagian = $conn->real_escape_string($bagian);
    $jabatan = $conn->real_escape_string($jabatan);
    $email = $conn->real_escape_string($email);
    $phone = $conn->real_escape_string($phone);
    $training_topic = $conn->real_escape_string($training_topic);
    $training_date = $conn->real_escape_string($training_date);
    $training_skill_type = $conn->real_escape_string($training_skill_type);
    $additional_info = $conn->real_escape_string($additional_info);
    $status = $conn->real_escape_string($status);

    // Cari dept head berdasarkan departemen pemohon
    // Pertama, coba cari dept head yang memiliki departemen yang sama persis
    $dept_head_query = "SELECT DISTINCT u.id, u.name, u.email
                       FROM users u
                       INNER JOIN user_departments ud ON u.id = ud.user_id
                       WHERE ud.dept = '$department'
                       AND u.role_id = 2";

    error_log("Mencari dept head untuk departemen: $department");
    error_log("Query pencarian dept head (exact match): $dept_head_query");

    $dept_head_result = $conn->query($dept_head_query);

    // Jika tidak ditemukan, coba cari dept head berdasarkan kecocokan sebagian nama departemen
    if (!$dept_head_result || $dept_head_result->num_rows === 0) {
        error_log("No exact dept head match found for department: $department. Trying partial match...");

        // Coba cari dept head dengan departemen yang mengandung nama departemen pemohon
        // atau departemen pemohon mengandung nama departemen dept head
        $dept_head_query = "SELECT DISTINCT u.id, u.name, u.email, ud.dept as head_dept
                           FROM users u
                           INNER JOIN user_departments ud ON u.id = ud.user_id
                           WHERE (ud.dept LIKE '%$department%' OR '$department' LIKE CONCAT('%', ud.dept, '%'))
                           AND u.role_id = 2";

        error_log("Query pencarian dept head (partial match): $dept_head_query");
        $dept_head_result = $conn->query($dept_head_query);

        // Jika masih tidak ditemukan, cari semua dept head
        if (!$dept_head_result || $dept_head_result->num_rows === 0) {
            error_log("No partial dept head match found for department: $department. Getting all dept heads...");

            // Ambil semua dept head
            $dept_head_query = "SELECT DISTINCT u.id, u.name, u.email
                               FROM users u
                               WHERE u.role_id = 2
                               AND u.email IS NOT NULL
                               AND u.email != ''
                               LIMIT 1";

            error_log("Query pencarian dept head (fallback): $dept_head_query");
            $dept_head_result = $conn->query($dept_head_query);
        }
    }

    // Jika masih tidak ditemukan, cari dept head untuk ITE secara spesifik (Linda)
    if (!$dept_head_result || $dept_head_result->num_rows === 0) {
        error_log("No dept head found using standard methods. Looking for Linda (dept head for ITE)...");

        $dept_head_query = "SELECT DISTINCT u.id, u.name, u.email
                           FROM users u
                           INNER JOIN user_departments ud ON u.id = ud.user_id
                           WHERE ud.dept = 'ITE'
                           AND u.role_id = 2
                           LIMIT 1";

        error_log("Query pencarian Linda (dept head ITE): $dept_head_query");
        $dept_head_result = $conn->query($dept_head_query);
    }

    $dept_head_id = null;
    $dept_head_name = null;
    $dept_head_email = null;
    $dept_heads = [];

    if ($dept_head_result && $dept_head_result->num_rows > 0) {
        // Ambil semua dept head untuk departemen ini
        while ($dept_head_data = $dept_head_result->fetch_assoc()) {
            $dept_heads[] = $dept_head_data;

            // Simpan data dept head pertama untuk digunakan di tempat lain
            if ($dept_head_id === null) {
                $dept_head_id = $dept_head_data['id'];
                $dept_head_name = $dept_head_data['name'];
                $dept_head_email = $dept_head_data['email'];

                // Log dept head yang akan digunakan sebagai next_approver_id
                error_log("SELECTED DEPT HEAD: ID={$dept_head_id}, Name={$dept_head_name}, Email={$dept_head_email}");
            }
        }

        $dept_head_count = count($dept_heads);
        error_log("Found $dept_head_count dept head(s) for department: $department");

        foreach ($dept_heads as $head) {
            $head_dept = isset($head['head_dept']) ? ", Dept: " . $head['head_dept'] : "";
            error_log("Dept head: " . $head['name'] . " (ID: " . $head['id'] . ", Email: " . $head['email'] . $head_dept . ")");
        }
    } else {
        error_log("No dept head found for department: " . $department);

        // Jika tidak ada dept head yang ditemukan, coba cari Linda secara langsung berdasarkan ID
        error_log("Trying to find Linda (ID=51) directly...");
        $linda_query = "SELECT id, name, email FROM users WHERE id = 51 AND role_id = 2 LIMIT 1";
        $linda_result = $conn->query($linda_query);

        if ($linda_result && $linda_result->num_rows > 0) {
            $linda_data = $linda_result->fetch_assoc();
            $dept_head_id = $linda_data['id'];
            $dept_head_name = $linda_data['name'];
            $dept_head_email = $linda_data['email'];

            $dept_heads[] = $linda_data;

            error_log("FALLBACK TO LINDA: ID={$dept_head_id}, Name={$dept_head_name}, Email={$dept_head_email}");
        } else {
            error_log("Could not find Linda (ID=51). No dept head will be assigned.");
        }
    }

    // Set role_id untuk dept head
    $current_approver_role_id = 2; // Role ID untuk Dept Head

    // Untuk pengajuan baru, next_approver_id mengarah ke dept head sesuai dengan departemen pengaju
    $next_approver_id = $dept_head_id; // ID dept head yang sudah dicari sebelumnya

    error_log("Setting next_approver_id to Dept Head (ID: " . ($next_approver_id ? $next_approver_id : "NULL") . ") for department: $department");

    // Insert ke tabel training_submissions with current_approver_role_id and next_approver_id
    $query = "INSERT INTO training_submissions
              (user_id, full_name, nik, departemen, bagian, jabatan, email, phone, training_topic,
               start_date, is_confirmed, training_skill_type, additional_info, status, current_approver_role_id, next_approver_id, approved_dept_head, created_at)
              VALUES
              ($user_id, '$full_name', '$nik', '$department', '$bagian', '$jabatan', '$email', '$phone',
               '$training_topic', '$training_date', '0', '$training_skill_type', '$additional_info', '$status', $current_approver_role_id, " .
               ($next_approver_id ? $next_approver_id : "NULL") . ", 'Pending', NOW())";

    // Log query untuk debugging
    error_log("Query insert training: " . $query);

    if (!$conn->query($query)) {
        error_log("Error executing query: " . $conn->error);
        throw new Exception("Gagal menyimpan data training: " . $conn->error);
    }

    error_log("Training berhasil disimpan dengan next_approver_id: " . ($next_approver_id ? $next_approver_id : "NULL") . " (Dept Head untuk departemen $department)");

    $training_id = $conn->insert_id;

    // Simpan data peserta ke tabel participants
    if (isset($_POST['nama']) && is_array($_POST['nama'])) {
        // Array untuk melacak NIK yang sudah diproses untuk mencegah duplikasi
        $processedNIKs = [];

        for ($i = 0; $i < count($_POST['nama']); $i++) {
            // Check if the arrays exist before accessing them
            $nama = isset($_POST['nama'][$i]) ? $_POST['nama'][$i] : '';
            $nik_participant = isset($_POST['nik'][$i]) ? $_POST['nik'][$i] : '';
            $jabatan = isset($_POST['jabatan'][$i]) ? $_POST['jabatan'][$i] : '';
            $bagian = isset($_POST['bagian'][$i]) ? $_POST['bagian'][$i] : '';
            $departemen = isset($_POST['departemen'][$i]) ? $_POST['departemen'][$i] : '';

            // Normalisasi NIK untuk pengecekan duplikat (hapus karakter non-alphanumeric dan lowercase)
            $normalizedNIK = preg_replace('/[^a-zA-Z0-9]/', '', strtolower($nik_participant));

            // Lewati jika NIK kosong atau sudah diproses sebelumnya (duplikat)
            if (empty($normalizedNIK) || in_array($normalizedNIK, $processedNIKs)) {
                error_log("Skipping duplicate or empty NIK: $nik_participant ($nama)");
                continue;
            }

            // Tambahkan ke daftar NIK yang sudah diproses
            $processedNIKs[] = $normalizedNIK;

            // Escape all variables to prevent SQL injection
            $nama = $conn->real_escape_string($nama);
            $nik_participant = $conn->real_escape_string($nik_participant);
            $jabatan = $conn->real_escape_string($jabatan);
            $bagian = $conn->real_escape_string($bagian);
            $departemen = $conn->real_escape_string($departemen);

            // Insert participant without using bind_param
            $participant_query = "INSERT INTO participants (
                training_id,
                nama_participants,
                nik_participants,
                jabatan_participants,
                bagian_participants,
                departemen_participants
            ) VALUES (
                $training_id,
                '$nama',
                '$nik_participant',
                '$jabatan',
                '$bagian',
                '$departemen'
            )";

            if (!$conn->query($participant_query)) {
                throw new Exception("Error adding participant: " . $conn->error);
            }
        }

        // Log jumlah peserta yang berhasil ditambahkan
        error_log("Successfully added " . count($processedNIKs) . " unique participants to training ID: $training_id");
    }

    // Commit transaction
    $conn->commit();

    // Siapkan pengiriman email asinkron
    $email_token = hash('sha256', $training_id . 'training_email_secret');
    $async_url = "../config/async_email.php?token=$email_token&training_id=$training_id&status=Pending&next_approver_role_id=2";

    // Tambahkan comments jika ada
    if (!empty($additional_info)) {
        $async_url .= "&comments=" . urlencode($additional_info);
    }

    // Log URL untuk debugging
    error_log("Async email URL: $async_url");

    // Kirim request asinkron menggunakan file_get_contents dengan timeout rendah
    // atau gunakan curl jika tersedia
    if (function_exists('curl_init')) {
        // Gunakan metode non-blocking dengan curl
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $async_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 1); // Timeout 1 detik
        curl_setopt($ch, CURLOPT_NOSIGNAL, 1); // Ignore signals
        curl_exec($ch);
        curl_close($ch);
    } else {
        // Fallback ke file_get_contents dengan timeout
        $context = stream_context_create([
            'http' => [
                'timeout' => 0.5, // Timeout 0.5 detik
            ]
        ]);
        @file_get_contents($async_url, false, $context);
    }

    // Log bahwa email akan dikirim secara asinkron
    error_log("Email notifikasi akan dikirim secara asinkron untuk training ID: $training_id");

    // Set pesan sukses dengan informasi dept head jika ada
    if (!empty($dept_heads)) {
        // Jika hanya ada satu dept head
        if (count($dept_heads) === 1) {
            $_SESSION['success'] = "Pengajuan training berhasil disimpan! Status training: Pending Dept Head ($dept_head_name).";
        } else {
            // Jika ada lebih dari satu dept head, tampilkan semua nama
            $dept_head_names = array_map(function($head) {
                return $head['name'];
            }, $dept_heads);

            $dept_head_list = implode(', ', $dept_head_names);
            $_SESSION['success'] = "Pengajuan training berhasil disimpan! Status training: Pending Dept Head ($dept_head_list).";
        }
    } else {
        $_SESSION['success'] = "Pengajuan training berhasil disimpan! Status training: Pending Dept Head.";
    }
    header("Location: dashboard.php");
    exit();

} catch (Exception $e) {
    // Rollback jika terjadi error
    $conn->rollback();

    $_SESSION['errors'] = [$e->getMessage()];
    header("Location: form.php");
    exit();
} finally {
    $conn->close();
}
