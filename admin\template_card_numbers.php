<?php
// File: admin/template_card_numbers.php
// Deskripsi: Script untuk membuat template Excel untuk import nomor kartu RFID

// Aktifkan output buffering
ob_start();

include '../config/config.php';
include 'security.php';

// Security check sudah dilakukan di security.php

// Require library PhpSpreadsheet
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// Buat objek spreadsheet baru
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set judul sheet
$sheet->setTitle('Template Nomor Kartu RFID');

// Set header
$sheet->setCellValue('A1', 'NIK');
$sheet->setCellValue('B1', 'Nomor Kartu RFID');

// Style header
$headerStyle = [
    'font' => [
        'bold' => true,
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
        ],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => [
            'rgb' => 'E2EFDA',
        ],
    ],
];

$sheet->getStyle('A1:B1')->applyFromArray($headerStyle);

// Tambahkan contoh data
$exampleData = [
    ['305000181', '1234567890'],
    ['305000182', '0987654321'],
];

$row = 2;
foreach ($exampleData as $data) {
    $sheet->setCellValue('A' . $row, $data[0]);
    $sheet->setCellValue('B' . $row, $data[1]);
    $row++;
}

// Style contoh data
$dataStyle = [
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
        ],
    ],
];

$sheet->getStyle('A2:B' . ($row - 1))->applyFromArray($dataStyle);

// Set lebar kolom
$sheet->getColumnDimension('A')->setWidth(15);
$sheet->getColumnDimension('B')->setWidth(20);

// Tambahkan instruksi
$sheet->setCellValue('A' . ($row + 1), 'Instruksi:');
$sheet->mergeCells('A' . ($row + 1) . ':B' . ($row + 1));
$sheet->getStyle('A' . ($row + 1))->getFont()->setBold(true);

$instructions = [
    '1. Kolom A: NIK karyawan (wajib diisi)',
    '2. Kolom B: Nomor Kartu RFID (wajib diisi)',
    '3. Baris pertama adalah header, jangan diubah',
    '4. Pastikan NIK karyawan sudah terdaftar di sistem',
    '5. Pastikan nomor kartu RFID belum digunakan oleh karyawan lain',
];

$row++;
foreach ($instructions as $instruction) {
    $row++;
    $sheet->setCellValue('A' . $row, $instruction);
    $sheet->mergeCells('A' . $row . ':B' . $row);
}

// Pastikan tidak ada output sebelum header
ob_clean();

// Buat file Excel
$filename = 'Template_Nomor_Kartu_RFID.xlsx';

// Set header untuk download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');
header('Pragma: public');

// Tulis ke output
$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit;
?>
