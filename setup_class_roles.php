<?php
/**
 * <PERSON><PERSON>t to set up class roles system
 */
include 'config/config.php';

echo "<h1>Setting up Class Roles System</h1>";

// Create grade proposals table
$query = "CREATE TABLE IF NOT EXISTS training_grade_proposals (
    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    class_id INT(11) NOT NULL,
    assignment_id INT(11) NOT NULL,
    student_id INT(11) NOT NULL,
    proposed_by INT(11) NOT NULL,
    proposed_grade DECIMAL(5,2) NOT NULL,
    feedback TEXT,
    status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
    reviewed_by INT(11) NULL,
    review_comments TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES training_classes(id) ON DELETE CASCADE,
    FOREIGN KEY (assignment_id) REFERENCES training_assignments(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (proposed_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($conn->query($query)) {
    echo "<p>Grade proposals table created successfully.</p>";
} else {
    echo "<p>Error creating grade proposals table: " . $conn->error . "</p>";
}

// Create notifications table for role-based notifications
$query = "CREATE TABLE IF NOT EXISTS training_notifications (
    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    class_id INT(11) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'info',
    is_read TINYINT(1) NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES training_classes(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($conn->query($query)) {
    echo "<p>Notifications table created successfully.</p>";
} else {
    echo "<p>Error creating notifications table: " . $conn->error . "</p>";
}

// Create table for role permissions
$query = "CREATE TABLE IF NOT EXISTS training_role_permissions (
    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    role ENUM('instructor', 'assistant', 'student') NOT NULL,
    permission VARCHAR(100) NOT NULL,
    UNIQUE KEY unique_role_permission (role, permission)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($conn->query($query)) {
    echo "<p>Role permissions table created successfully.</p>";
    
    // Insert default permissions
    $permissions = [
        // Instructor permissions
        ['instructor', 'manage_materials'],
        ['instructor', 'create_quiz'],
        ['instructor', 'edit_quiz'],
        ['instructor', 'delete_quiz'],
        ['instructor', 'grade_assignments'],
        ['instructor', 'review_grade_proposals'],
        ['instructor', 'manage_participants'],
        ['instructor', 'send_announcements'],
        ['instructor', 'view_reports'],
        ['instructor', 'moderate_discussions'],
        ['instructor', 'issue_certificates'],
        
        // Assistant permissions
        ['assistant', 'view_materials'],
        ['assistant', 'upload_supplementary_materials'],
        ['assistant', 'view_quiz_before_publish'],
        ['assistant', 'grade_assignments'],
        ['assistant', 'propose_grades'],
        ['assistant', 'view_student_progress'],
        ['assistant', 'moderate_discussions'],
        ['assistant', 'answer_questions'],
        
        // Student permissions
        ['student', 'view_materials'],
        ['student', 'take_quiz'],
        ['student', 'submit_assignments'],
        ['student', 'view_own_grades'],
        ['student', 'participate_discussions'],
        ['student', 'view_own_progress']
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO training_role_permissions (role, permission) VALUES (?, ?)");
    $stmt->bind_param("ss", $role, $permission);
    
    $success = true;
    foreach ($permissions as $perm) {
        $role = $perm[0];
        $permission = $perm[1];
        if (!$stmt->execute()) {
            $success = false;
            echo "<p>Error inserting permission: " . $stmt->error . "</p>";
        }
    }
    
    if ($success) {
        echo "<p>Default permissions inserted successfully.</p>";
    }
} else {
    echo "<p>Error creating role permissions table: " . $conn->error . "</p>";
}

echo "<p>Setup completed. <a href='index.php'>Return to home</a></p>";
