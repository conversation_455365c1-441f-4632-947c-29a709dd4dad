<?php
// <PERSON>lai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Tangani error database dengan try-catch
try {
    // Query untuk mengambil pengajuan training
    $query = "SELECT ts.id, ts.full_name, ts.email, ts.phone, ts.training_topic, ts.training_date, ts.status, u.name AS approved_by_name
              FROM training_submissions ts
              LEFT JOIN users u ON ts.approved_by = u.id
              WHERE ts.status IN ('approved', 'pending', 'completed')";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        throw new Exception("Error querying database: " . mysqli_error($conn));
    }

    $submissions = mysqli_fetch_all($result, MYSQLI_ASSOC);

    // Cek dan update training yang sudah lewat
    foreach ($submissions as $row) {
        $current_date = date("Y-m-d"); // Tanggal saat ini
        if (strtotime($row['start_date']) < strtotime($current_date) && $row['status'] == 'approved') {
            // Update status training yang sudah lewat menjadi 'completed'
            $stmt = $conn->prepare("UPDATE training_submissions SET status='completed' WHERE id=?");
            if (!$stmt) {
                throw new Exception("Prepare statement error: " . $conn->error);
            }
            $stmt->bind_param("i", $row['id']);
            $stmt->execute();
        }
    }

    // Mengambil data training yang sudah selesai (completed)
    $query_completed = "SELECT ts.id, ts.full_name, ts.email, ts.phone, ts.training_topic, ts.start_date, ts.status, u.name AS approved_by_name
                        FROM training_submissions ts
                        LEFT JOIN users u ON ts.approved_by = u.id
                        WHERE ts.status = 'completed'";
    $result_completed = mysqli_query($conn, $query_completed);

    if (!$result_completed) {
        throw new Exception("Error querying completed trainings: " . mysqli_error($conn));
    }

    $submissions_completed = mysqli_fetch_all($result_completed, MYSQLI_ASSOC);

    // Proses Approve Training
    if (isset($_GET['approve_id'])) {
        $id = $_GET['approve_id'];
        $approved_by = $_SESSION['user_id'];

        $stmt = $conn->prepare("UPDATE training_submissions SET status='approved', approved_by=? WHERE id=?");
        if (!$stmt) {
            throw new Exception("Prepare statement error: " . $conn->error);
        }
        $stmt->bind_param("ii", $approved_by, $id);
        if ($stmt->execute()) {
            header("Location: approved.php");
            exit();
        } else {
            throw new Exception("Error approving training: " . $stmt->error);
        }
    }

    // Proses Hapus Training
    if (isset($_GET['delete_id'])) {
        $delete_id = $_GET['delete_id'];

        $stmt = $conn->prepare("DELETE FROM training_submissions WHERE id=?");
        if (!$stmt) {
            throw new Exception("Prepare statement error: " . $conn->error);
        }
        $stmt->bind_param("i", $delete_id);
        if ($stmt->execute()) {
            header("Location: approved.php");
            exit();
        } else {
            throw new Exception("Error deleting training: " . $stmt->error);
        }
    }
} catch (Exception $e) {
    // Simpan error message untuk ditampilkan di halaman
    $error_message = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .form-container {
        max-width: 1000px;
        margin: 20px auto;
        padding: 20px;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        overflow-x: auto;
    }

    .form-header {
        text-align: center;
        margin-bottom: 20px;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    th, td {
        padding: 10px;
        text-align: left;
        border: 1px solid #ddd;
    }

    th {
        background-color: #4CAF50;
        color: white;
    }

    td {
        background-color: #f9f9f9;
    }

    tr:nth-child(even) td {
        background-color: #f2f2f2;
    }

    tr:hover td {
        background-color: #e0e0e0;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }

    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .loading-spinner {
        display: none;
        text-align: center;
        margin: 20px 0;
    }
</style>
<body>

<?php include '../config/navbar.php';?>
<div class="container-form">
    <div class="form-container">
        <div class="form-header">
            <h1>Training yang Sudah Terlaksana</h1>
            <p>Daftar training yang telah terlaksana.</p>
        </div>

        <!-- Tampilkan pesan error jika ada -->
        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <strong>Error:</strong> <?php echo htmlspecialchars($error_message); ?>
        </div>
        <?php endif; ?>

        <!-- Loading spinner -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat data...</p>
        </div>

        <table id="trainingTable">
            <thead>
                <tr>
                    <th>No</th>
                    <th>Nama</th>
                    <th>Email</th>
                    <th>Telepon</th>
                    <th>Jenis Training</th>
                    <th>Tanggal Training</th>
                    <th>Approved By</th>
                </tr>
            </thead>
            <tbody>
                <?php if (isset($submissions_completed) && !empty($submissions_completed)): ?>
                    <?php $no = 1; ?>
                    <?php foreach ($submissions_completed as $row): ?>
                        <tr>
                            <td><?php echo $no; ?></td>
                            <td><?php echo htmlspecialchars($row['full_name']); ?></td>
                            <td><?php echo htmlspecialchars($row['email']); ?></td>
                            <td><?php echo htmlspecialchars($row['phone']); ?></td>
                            <td><?php echo htmlspecialchars($row['training_topic']); ?></td>
                            <td><?php echo htmlspecialchars($row['training_date']); ?></td>
                            <td><?php echo htmlspecialchars($row['approved_by_name'] ?? 'Admin'); ?></td>
                        </tr>
                        <?php $no++; ?>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="7" class="text-center">Tidak ada data training yang sudah terlaksana</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php include '../config/footer.php';?>

<script>
// Fungsi untuk menangani permintaan asinkron
function handleAsyncRequest(url, callback) {
    // Tampilkan loading spinner
    document.getElementById('loadingSpinner').style.display = 'block';

    // Gunakan fetch API dengan timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 detik timeout

    fetch(url, { signal: controller.signal })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            clearTimeout(timeoutId);
            if (typeof callback === 'function') {
                callback(null, data);
            }
        })
        .catch(error => {
            clearTimeout(timeoutId);
            console.error('Error:', error);
            if (typeof callback === 'function') {
                callback(error, null);
            }
        })
        .finally(() => {
            // Sembunyikan loading spinner
            document.getElementById('loadingSpinner').style.display = 'none';
        });
}

// Inisialisasi halaman
document.addEventListener('DOMContentLoaded', function() {
    // Tangani klik pada link di halaman
    document.querySelectorAll('a').forEach(link => {
        link.addEventListener('click', function(e) {
            // Jika link memiliki target atau tidak mengarah ke halaman lain, biarkan default behavior
            if (this.getAttribute('target') || this.getAttribute('href').startsWith('#')) {
                return;
            }

            // Jika link mengarah ke halaman lain, tambahkan parameter untuk mencegah caching
            const url = this.getAttribute('href');
            if (url && !url.includes('javascript:')) {
                const separator = url.includes('?') ? '&' : '?';
                this.setAttribute('href', `${url}${separator}t=${Date.now()}`);
            }
        });
    });
});
</script>
</body>
</html>