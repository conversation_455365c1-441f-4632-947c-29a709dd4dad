<?php
/**
 * Fix internal notification system by making class_id nullable
 */

require_once __DIR__ . '/config.php';

echo "🔧 FIXING INTERNAL NOTIFICATION SYSTEM\n";
echo "=======================================\n\n";

// Option 1: Make class_id nullable for general notifications
echo "1️⃣ Making class_id nullable for general notifications...\n";

try {
    // Drop foreign key constraint first
    $drop_fk_query = "ALTER TABLE training_notifications DROP FOREIGN KEY training_notifications_ibfk_2";
    $conn->query($drop_fk_query);
    echo "   ✅ Dropped foreign key constraint\n";
} catch (Exception $e) {
    echo "   ℹ️  Foreign key constraint may not exist: " . $e->getMessage() . "\n";
}

try {
    // Modify column to allow NULL
    $modify_query = "ALTER TABLE training_notifications MODIFY COLUMN class_id INT NULL";
    if ($conn->query($modify_query)) {
        echo "   ✅ Made class_id nullable\n";
    } else {
        echo "   ❌ Failed to modify class_id: " . $conn->error . "\n";
    }
} catch (Exception $e) {
    echo "   ❌ Error modifying class_id: " . $e->getMessage() . "\n";
}

try {
    // Add foreign key constraint back with NULL support
    $add_fk_query = "ALTER TABLE training_notifications 
                     ADD CONSTRAINT training_notifications_ibfk_2 
                     FOREIGN KEY (class_id) REFERENCES training_classes(id) ON DELETE CASCADE";
    if ($conn->query($add_fk_query)) {
        echo "   ✅ Re-added foreign key constraint with NULL support\n";
    } else {
        echo "   ⚠️  Could not re-add foreign key constraint: " . $conn->error . "\n";
        echo "   ℹ️  This is OK - notifications will work without it\n";
    }
} catch (Exception $e) {
    echo "   ⚠️  Could not re-add foreign key constraint: " . $e->getMessage() . "\n";
    echo "   ℹ️  This is OK - notifications will work without it\n";
}

echo "\n";

// Test the fix
echo "2️⃣ Testing fixed notification system...\n";

require_once __DIR__ . '/../includes/notification_helper.php';

// Find test user
$user_query = "SELECT id, name FROM users WHERE is_active = 1 LIMIT 1";
$user_result = $conn->query($user_query);

if ($user_result && $user_result->num_rows > 0) {
    $test_user = $user_result->fetch_assoc();
    echo "   👤 Test user: {$test_user['name']}\n";
    
    try {
        // Test with NULL class_id
        $created = createNotification(
            $test_user['id'],
            null, // NULL class_id for general notifications
            'Test General Notification',
            'This is a test of the fixed internal notification system',
            'info'
        );
        
        if ($created) {
            echo "   ✅ General notification created successfully\n";
            
            // Test get notifications
            $notifications = getUnreadNotifications($test_user['id'], 1);
            if (!empty($notifications)) {
                echo "   ✅ Can retrieve notifications\n";
                
                // Test mark as read
                $marked = markNotificationAsRead($notifications[0]['id'], $test_user['id']);
                if ($marked) {
                    echo "   ✅ Can mark notifications as read\n";
                } else {
                    echo "   ❌ Cannot mark notifications as read\n";
                }
                
                // Clean up test notification
                $cleanup_query = "DELETE FROM training_notifications WHERE id = ? AND title = 'Test General Notification'";
                $cleanup_stmt = $conn->prepare($cleanup_query);
                $cleanup_stmt->bind_param("i", $notifications[0]['id']);
                $cleanup_stmt->execute();
                echo "   🧹 Test notification cleaned up\n";
            } else {
                echo "   ❌ Cannot retrieve notifications\n";
            }
        } else {
            echo "   ❌ Failed to create general notification\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Error testing notifications: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ❌ No test user found\n";
}

echo "\n";

// Test with valid class_id if available
echo "3️⃣ Testing with valid class_id...\n";

$class_query = "SELECT id, title FROM training_classes WHERE status = 'active' LIMIT 1";
$class_result = $conn->query($class_query);

if ($class_result && $class_result->num_rows > 0) {
    $test_class = $class_result->fetch_assoc();
    echo "   📚 Test class: {$test_class['title']} (ID: {$test_class['id']})\n";
    
    try {
        $created = createNotification(
            $test_user['id'],
            $test_class['id'],
            'Test Class Notification',
            'This is a test notification for a specific class',
            'info'
        );
        
        if ($created) {
            echo "   ✅ Class-specific notification created successfully\n";
            
            // Clean up
            $cleanup_query = "DELETE FROM training_notifications WHERE user_id = ? AND title = 'Test Class Notification'";
            $cleanup_stmt = $conn->prepare($cleanup_query);
            $cleanup_stmt->bind_param("i", $test_user['id']);
            $cleanup_stmt->execute();
            echo "   🧹 Test notification cleaned up\n";
        } else {
            echo "   ❌ Failed to create class-specific notification\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Error testing class notifications: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ℹ️  No active training classes found for testing\n";
}

echo "\n";

// Show current table structure
echo "4️⃣ Current table structure...\n";
$structure_query = "DESCRIBE training_notifications";
$structure_result = $conn->query($structure_query);

if ($structure_result) {
    while ($row = $structure_result->fetch_assoc()) {
        $null_status = $row['Null'] == 'YES' ? '✅ NULL' : '❌ NOT NULL';
        echo "   📋 {$row['Field']}: {$row['Type']} - $null_status\n";
    }
}

echo "\n🏁 INTERNAL NOTIFICATION SYSTEM FIX COMPLETED!\n";
echo "✅ class_id is now nullable for general notifications\n";
echo "✅ System can handle both general and class-specific notifications\n";

$conn->close();
?>
