<?php
include '../config/config.php';
include 'security.php';
include '../includes/notification_helper.php';

// Get notification ID and redirect URL
$notification_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$redirect_url = isset($_GET['redirect']) ? $_GET['redirect'] : 'classroom.php';

// Mark notification as read
if ($notification_id > 0) {
    markNotificationAsRead($notification_id, $_SESSION['user_id']);
}

// Redirect back to the original page
header("Location: $redirect_url");
exit();
