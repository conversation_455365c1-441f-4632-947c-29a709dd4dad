<?php
/**
 * Script untuk menambahkan notifikasi ke semua fallback navbar
 */

echo "🔔 ADDING NOTIFICATIONS TO FALLBACK NAVBARS\n";
echo "============================================\n\n";

// Daftar file yang memiliki fallback navbar
$files_with_fallback = [
    'Dir/assistant_dashboard.php',
    'LnD/assistant_dashboard.php', 
    'dept_head/assistant_dashboard.php',
    'pemohon/assistant_dashboard.php',
    'Dir/instructor_dashboard.php',
    'LnD/instructor_dashboard.php',
    'dept_head/instructor_dashboard.php',
    'pemohon/instructor_dashboard.php',
    'Dir/instructor_page_template.php',
    'LnD/instructor_page_template.php',
    'dept_head/instructor_page_template.php',
    'Dir/manage_materials.php',
    'LnD/manage_materials.php',
    'dept_head/manage_materials.php'
];

$updated_count = 0;
$error_count = 0;

foreach ($files_with_fallback as $file) {
    echo "📝 Processing: $file\n";
    
    if (!file_exists($file)) {
        echo "   ⚠️  File not found: $file\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Cari pattern fallback navbar yang tidak memiliki notifikasi
    $pattern = '/(<ul class="navbar-nav">.*?<\/ul>)(\s*<\/div>\s*<\/div>\s*<\/nav>)/s';
    
    if (preg_match($pattern, $content)) {
        // Tambahkan notifikasi sebelum closing tags
        $notification_code = '
                <ul class="navbar-nav">
                    <?php
                    // Include notifications dropdown for fallback navbar
                    if (isset($_SESSION[\'user_id\'])) {
                        require_once __DIR__ . \'/../includes/notification_helper.php\';
                        $unread_notifications = getUnreadNotifications($_SESSION[\'user_id\'], 5);
                        $notification_count = count($unread_notifications);
                        ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <?php if ($notification_count > 0): ?>
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                        <?= $notification_count ?>
                                        <span class="visually-hidden">notifikasi belum dibaca</span>
                                    </span>
                                <?php endif; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown" style="width: 300px;">
                                <li><h6 class="dropdown-header">Notifikasi</h6></li>
                                <?php if (empty($unread_notifications)): ?>
                                    <li><span class="dropdown-item text-muted">Tidak ada notifikasi baru</span></li>
                                <?php else: ?>
                                    <?php foreach ($unread_notifications as $notification): ?>
                                        <li>
                                            <a class="dropdown-item" href="mark_notification.php?id=<?= $notification[\'id\'] ?>&redirect=<?= urlencode($_SERVER[\'REQUEST_URI\']) ?>">
                                                <div class="d-flex">
                                                    <div class="flex-shrink-0">
                                                        <i class="fas fa-info-circle text-info"></i>
                                                    </div>
                                                    <div class="flex-grow-1 ms-2">
                                                        <h6 class="mb-0 small"><?= htmlspecialchars($notification[\'title\']) ?></h6>
                                                        <p class="mb-0 small text-muted"><?= htmlspecialchars(substr($notification[\'message\'], 0, 50)) ?>...</p>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-center" href="all_notifications.php">Lihat Semua</a></li>
                                <?php endif; ?>
                            </ul>
                        </li>
                        <?php
                    }
                    ?>
                </ul>';
        
        // Replace pattern dengan yang sudah ditambahkan notifikasi
        $new_content = preg_replace(
            $pattern,
            '$1' . $notification_code . '$2',
            $content
        );
        
        if ($new_content !== $content) {
            if (file_put_contents($file, $new_content)) {
                echo "   ✅ Updated successfully\n";
                $updated_count++;
            } else {
                echo "   ❌ Failed to write file\n";
                $error_count++;
            }
        } else {
            echo "   ℹ️  No changes needed (already has notifications or pattern not found)\n";
        }
    } else {
        echo "   ℹ️  Fallback navbar pattern not found\n";
    }
    
    echo "\n";
}

echo "📊 SUMMARY:\n";
echo "   ✅ Files updated: $updated_count\n";
echo "   ❌ Errors: $error_count\n";
echo "   📁 Total processed: " . count($files_with_fallback) . "\n";

if ($updated_count > 0) {
    echo "\n🎉 Notifications have been added to fallback navbars!\n";
    echo "🔔 Users will now see notification bell icon even in fallback navbars\n";
} else {
    echo "\n💡 No files needed updating - notifications may already be present\n";
}
?>
