<?php
// <PERSON>lai output buffering untuk menangkap semua output
ob_start();

// Nonaktifkan error reporting yang bisa merusak JSON
error_reporting(0);
ini_set('display_errors', 0);

// Set header JSON di awal
header('Content-Type: application/json');

// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

try {
    // Validasi input
    if (empty($_GET['column'])) {
        throw new Exception('Column parameter is required');
    }

    $column = $_GET['column'];

    // Validasi nama kolom untuk mencegah SQL injection
    $allowed_columns = [
        'nik', 'nama', 'tgl_masuk', 'jk', 'level_karyawan', 'tgl_lahir',
        'agama', 'pendidikan_akhir', 'no_telp', 'dept', 'bagian', 'jabatan',
        'group', 'status', 'pt'
    ];

    if (!in_array($column, $allowed_columns)) {
        throw new Exception('Invalid column name');
    }

    // Escape column name untuk keamanan tambahan
    $column = "`" . str_replace("`", "``", $column) . "`";

    // Ambil filter tambahan jika ada
    $filters = [];
    $params = [];
    $types = "";
    $whereConditions = ["$column IS NOT NULL AND $column != ''"];

    // Debug: Log request parameters
    error_log("get_unique_values.php - Request: " . json_encode($_GET));

    if (isset($_GET['filters']) && !empty($_GET['filters'])) {
        $filters = json_decode($_GET['filters'], true);

        // Debug: Log decoded filters
        error_log("get_unique_values.php - Decoded filters: " . json_encode($filters));

        if (is_array($filters)) {
            // Urutkan filter untuk memastikan hierarki filter diproses dengan benar
            $columnOrder = ['dept', 'bagian', 'jabatan', 'group', 'nama', 'nik'];
            $orderedFilters = [];

            // Tambahkan filter yang diurutkan terlebih dahulu
            foreach ($columnOrder as $col) {
                if (isset($filters[$col])) {
                    $orderedFilters[$col] = $filters[$col];
                }
            }

            // Tambahkan filter lainnya
            foreach ($filters as $col => $values) {
                if (!in_array($col, $columnOrder)) {
                    $orderedFilters[$col] = $values;
                }
            }

            // Debug: Log ordered filters
            error_log("get_unique_values.php - Ordered filters: " . json_encode($orderedFilters));

            foreach ($orderedFilters as $filterColumn => $filterValues) {
                // Validasi nama kolom
                if (!in_array($filterColumn, $allowed_columns)) {
                    continue; // Skip kolom yang tidak valid
                }

                // Escape kolom untuk keamanan
                $filterColumnEscaped = "`" . str_replace("`", "``", $filterColumn) . "`";

                if (is_array($filterValues) && !empty($filterValues)) {
                    $filterClauses = [];
                    foreach ($filterValues as $value) {
                        $filterClauses[] = "$filterColumnEscaped = ?";
                        $params[] = $value;
                        $types .= "s";
                    }
                    if (!empty($filterClauses)) {
                        $whereConditions[] = '(' . implode(' OR ', $filterClauses) . ')';
                    }
                }
            }
        }
    }

    // Debug: Log where conditions
    error_log("get_unique_values.php - Where conditions: " . json_encode($whereConditions));
    error_log("get_unique_values.php - Params: " . json_encode($params));

    // Buat query dengan kondisi WHERE
    $whereClause = implode(' AND ', $whereConditions);
    $query = "SELECT DISTINCT $column FROM karyawan WHERE $whereClause ORDER BY $column";

    // Debug: Log query
    error_log("get_unique_values.php - Query: " . $query);

    // Debug: Log parameter untuk query
    error_log("get_unique_values.php - Query params: " . json_encode($params));

    // Jika ada filter, gunakan query khusus untuk mendapatkan nilai yang relevan
    if (!empty($filters)) {
        error_log("get_unique_values.php - Using special query for filtered values");

        // Buat kondisi WHERE untuk filter
        $filterConditions = [];
        $filterParams = [];
        $filterTypes = "";

        foreach ($filters as $filterColumn => $filterValues) {
            if (in_array($filterColumn, $allowed_columns) && is_array($filterValues) && !empty($filterValues)) {
                $filterClauses = [];
                foreach ($filterValues as $value) {
                    $filterClauses[] = "`$filterColumn` = ?";
                    $filterParams[] = $value;
                    $filterTypes .= "s";
                }
                if (!empty($filterClauses)) {
                    $filterConditions[] = '(' . implode(' OR ', $filterClauses) . ')';
                }
            }
        }

        // Tambahkan kondisi untuk kolom yang diminta
        $filterConditions[] = "$column IS NOT NULL AND $column != ''";

        // Buat query dengan kondisi filter
        $filterWhereClause = implode(' AND ', $filterConditions);
        $directQuery = "SELECT DISTINCT $column FROM karyawan WHERE $filterWhereClause ORDER BY $column";
        error_log("get_unique_values.php - Direct query: " . $directQuery);
        error_log("get_unique_values.php - Direct query params: " . json_encode($filterParams));

        try {
            // Prepare dan execute query
            $directStmt = $conn->prepare($directQuery);
            if (!$directStmt) {
                error_log("get_unique_values.php - Direct query prepare error: " . $conn->error);
            } else {
                if (!empty($filterParams)) {
                    $directStmt->bind_param($filterTypes, ...$filterParams);
                }

                if (!$directStmt->execute()) {
                    error_log("get_unique_values.php - Direct query execute error: " . $directStmt->error);
                } else {
                    $directResult = $directStmt->get_result();
                    $directValues = [];

                    if ($directResult) {
                        while ($row = $directResult->fetch_array()) {
                            $directValues[] = $row[0];
                        }
                        error_log("get_unique_values.php - Direct query results: " . json_encode($directValues));

                        // Gunakan hasil query langsung
                        echo json_encode([
                            'success' => true,
                            'values' => $directValues
                        ]);

                        // Bersihkan dan keluar
                        if (isset($directStmt)) {
                            $directStmt->close();
                        }
                        if (isset($conn)) {
                            $conn->close();
                        }
                        exit();
                    } else {
                        error_log("get_unique_values.php - Direct query get_result error: " . $directStmt->error);
                    }
                }

                if (isset($directStmt)) {
                    $directStmt->close();
                }
            }
        } catch (Exception $e) {
            error_log("get_unique_values.php - Direct query exception: " . $e->getMessage());
        }
    }

    // Prepare dan execute query
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        error_log("get_unique_values.php - Error preparing query: " . $conn->error);
        throw new Exception("Error preparing query: " . $conn->error);
    }

    if (!empty($params)) {
        try {
            $stmt->bind_param($types, ...$params);
        } catch (Exception $e) {
            error_log("get_unique_values.php - Error binding parameters: " . $e->getMessage());
            error_log("get_unique_values.php - Types: " . $types . ", Params count: " . count($params));
            throw new Exception("Error binding parameters: " . $e->getMessage());
        }
    }

    if (!$stmt->execute()) {
        error_log("get_unique_values.php - Error executing query: " . $stmt->error);
        throw new Exception("Error executing query: " . $stmt->error);
    }

    $result = $stmt->get_result();
    if (!$result) {
        throw new Exception("Error getting result: " . $stmt->error);
    }

    $values = [];
    while ($row = $result->fetch_array()) {
        $values[] = $row[0];
    }

    // Kembalikan response sukses
    echo json_encode([
        'success' => true,
        'values' => $values
    ]);

} catch (Exception $e) {
    // Kembalikan response error
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    // Bersihkan output buffer dan kirim respons
    $output = ob_get_clean();

    // Periksa apakah output adalah JSON yang valid
    json_decode($output);
    if (json_last_error() !== JSON_ERROR_NONE) {
        // Jika bukan JSON valid, ganti dengan pesan error JSON
        echo json_encode([
            'success' => false,
            'message' => 'Server error: Invalid JSON response',
            'debug_info' => 'Output contains non-JSON content'
        ]);
    } else {
        // Jika JSON valid, kirim output asli
        echo $output;
    }

    // Pastikan tidak ada output lain setelah ini
    exit();
}
