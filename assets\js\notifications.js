/**
 * Custom Notification System
 * Provides a more user-friendly alternative to standard JavaScript alerts
 */

// Notification types
const NOTIFICATION_TYPES = {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info'
};

// Store notification container reference
let notificationContainer = null;

/**
 * Initialize the notification system
 * Creates the notification container if it doesn't exist
 */
function initNotifications() {
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.className = 'custom-notifications-container';
        document.body.appendChild(notificationContainer);
    }
}

/**
 * Show a notification
 * @param {string} message - The message to display
 * @param {string} type - The type of notification (success, error, warning, info)
 * @param {number} duration - How long to show the notification in ms (default: 5000)
 * @param {boolean} dismissible - Whether the notification can be dismissed (default: true)
 */
function showNotification(message, type = NOTIFICATION_TYPES.INFO, duration = 5000, dismissible = true) {
    // Initialize notification system if not already done
    initNotifications();
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `custom-notification custom-notification-${type}`;
    notification.setAttribute('role', 'alert');
    
    // Add animation class
    notification.classList.add('notification-slide-in');
    
    // Create icon based on notification type
    let icon = '';
    switch (type) {
        case NOTIFICATION_TYPES.SUCCESS:
            icon = '<i class="fas fa-check-circle"></i>';
            break;
        case NOTIFICATION_TYPES.ERROR:
            icon = '<i class="fas fa-exclamation-circle"></i>';
            break;
        case NOTIFICATION_TYPES.WARNING:
            icon = '<i class="fas fa-exclamation-triangle"></i>';
            break;
        case NOTIFICATION_TYPES.INFO:
        default:
            icon = '<i class="fas fa-info-circle"></i>';
            break;
    }
    
    // Create notification content
    notification.innerHTML = `
        <div class="notification-icon">${icon}</div>
        <div class="notification-content">${message}</div>
        ${dismissible ? '<button class="notification-close" aria-label="Close notification"><i class="fas fa-times"></i></button>' : ''}
    `;
    
    // Add to container
    notificationContainer.appendChild(notification);
    
    // Add event listener for close button if dismissible
    if (dismissible) {
        const closeButton = notification.querySelector('.notification-close');
        closeButton.addEventListener('click', () => {
            closeNotification(notification);
        });
    }
    
    // Auto-close after duration
    if (duration > 0) {
        setTimeout(() => {
            closeNotification(notification);
        }, duration);
    }
    
    // Return the notification element in case it needs to be manipulated later
    return notification;
}

/**
 * Close a notification with animation
 * @param {HTMLElement} notification - The notification element to close
 */
function closeNotification(notification) {
    // Skip if already closing
    if (notification.classList.contains('notification-slide-out')) {
        return;
    }
    
    // Add closing animation
    notification.classList.remove('notification-slide-in');
    notification.classList.add('notification-slide-out');
    
    // Remove from DOM after animation completes
    notification.addEventListener('animationend', () => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    });
}

/**
 * Shorthand function for success notifications
 */
function showSuccessNotification(message, duration = 5000) {
    return showNotification(message, NOTIFICATION_TYPES.SUCCESS, duration);
}

/**
 * Shorthand function for error notifications
 */
function showErrorNotification(message, duration = 5000) {
    return showNotification(message, NOTIFICATION_TYPES.ERROR, duration);
}

/**
 * Shorthand function for warning notifications
 */
function showWarningNotification(message, duration = 5000) {
    return showNotification(message, NOTIFICATION_TYPES.WARNING, duration);
}

/**
 * Shorthand function for info notifications
 */
function showInfoNotification(message, duration = 5000) {
    return showNotification(message, NOTIFICATION_TYPES.INFO, duration);
}

/**
 * Show a confirmation dialog
 * @param {string} message - The message to display
 * @param {Function} onConfirm - Callback function when user confirms
 * @param {Function} onCancel - Callback function when user cancels
 * @param {string} confirmText - Text for confirm button (default: "Ya")
 * @param {string} cancelText - Text for cancel button (default: "Tidak")
 */
function showConfirmDialog(message, onConfirm, onCancel = null, confirmText = "Ya", cancelText = "Tidak") {
    // Create overlay
    const overlay = document.createElement('div');
    overlay.className = 'custom-dialog-overlay';
    
    // Create dialog
    const dialog = document.createElement('div');
    dialog.className = 'custom-dialog';
    dialog.setAttribute('role', 'dialog');
    dialog.setAttribute('aria-modal', 'true');
    
    // Create dialog content
    dialog.innerHTML = `
        <div class="custom-dialog-content">
            <div class="custom-dialog-message">${message}</div>
            <div class="custom-dialog-buttons">
                <button class="custom-dialog-button custom-dialog-cancel">${cancelText}</button>
                <button class="custom-dialog-button custom-dialog-confirm">${confirmText}</button>
            </div>
        </div>
    `;
    
    // Add to DOM
    overlay.appendChild(dialog);
    document.body.appendChild(overlay);
    
    // Add animation
    setTimeout(() => {
        dialog.classList.add('dialog-show');
    }, 10);
    
    // Add event listeners
    const confirmButton = dialog.querySelector('.custom-dialog-confirm');
    const cancelButton = dialog.querySelector('.custom-dialog-cancel');
    
    confirmButton.addEventListener('click', () => {
        closeDialog(overlay);
        if (typeof onConfirm === 'function') {
            onConfirm();
        }
    });
    
    cancelButton.addEventListener('click', () => {
        closeDialog(overlay);
        if (typeof onCancel === 'function') {
            onCancel();
        }
    });
    
    // Close when clicking outside the dialog
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            closeDialog(overlay);
            if (typeof onCancel === 'function') {
                onCancel();
            }
        }
    });
    
    // Close on ESC key
    document.addEventListener('keydown', function escHandler(e) {
        if (e.key === 'Escape') {
            document.removeEventListener('keydown', escHandler);
            closeDialog(overlay);
            if (typeof onCancel === 'function') {
                onCancel();
            }
        }
    });
    
    // Focus the cancel button (safer default)
    cancelButton.focus();
}

/**
 * Close a dialog with animation
 * @param {HTMLElement} overlay - The dialog overlay element
 */
function closeDialog(overlay) {
    const dialog = overlay.querySelector('.custom-dialog');
    dialog.classList.remove('dialog-show');
    dialog.classList.add('dialog-hide');
    
    // Remove from DOM after animation completes
    dialog.addEventListener('animationend', () => {
        if (overlay.parentNode) {
            overlay.parentNode.removeChild(overlay);
        }
    });
}

// Replace standard alert with custom notification
window.originalAlert = window.alert;
window.alert = function(message) {
    showNotification(message, NOTIFICATION_TYPES.INFO);
};

// Replace standard confirm with custom dialog
window.originalConfirm = window.confirm;
window.confirm = function(message) {
    // This is tricky because confirm is synchronous
    // We'll return true by default and show the dialog asynchronously
    let result = true;
    
    showConfirmDialog(message, 
        () => { result = true; }, 
        () => { result = false; }
    );
    
    return result;
};
