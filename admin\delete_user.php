<?php
session_start();
include '../config/config.php';

if (isset($_GET['id'])) {
    $user_id = $_GET['id'];

    try {
        // Ambil data user sebelum dihapus untuk log
        $query = "SELECT name, nik FROM users WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            throw new Exception('User tidak ditemukan');
        }

        $user = $result->fetch_assoc();
        $user_name = $user['name'];
        $user_nik = $user['nik'];

        // Mulai transaction
        $conn->begin_transaction();

        // Set NULL pada approved_by sebelum menghapus user
        $updateQuery = "UPDATE training_submissions SET approved_by = NULL WHERE approved_by = ?";
        $stmtUpdate = $conn->prepare($updateQuery);
        $stmtUpdate->bind_param("i", $user_id);
        $stmtUpdate->execute();

        // Hapus data terkait di tabel user_departments
        $deleteUserDeptQuery = "DELETE FROM user_departments WHERE user_id = ?";
        $stmtDeleteUserDept = $conn->prepare($deleteUserDeptQuery);
        $stmtDeleteUserDept->bind_param("i", $user_id);
        
        if (!$stmtDeleteUserDept->execute()) {
            throw new Exception("Gagal menghapus data departemen user: " . $conn->error);
        }

        // Hapus user dari tabel users
        $deleteQuery = "DELETE FROM users WHERE id = ?";
        $stmtDelete = $conn->prepare($deleteQuery);
        $stmtDelete->bind_param("i", $user_id);

        if (!$stmtDelete->execute()) {
            throw new Exception("Gagal menghapus user: " . $conn->error);
        }

        // Log aktivitas
        if (file_exists('../config/activity_logger.php')) {
            include_once '../config/activity_logger.php';
            if (function_exists('log_activity')) {
                log_activity($_SESSION['user_id'], "Menghapus pengguna: {$user_name} (NIK: {$user_nik})", "user", [
                    'deleted_user_id' => $user_id,
                    'deleted_user_name' => $user_name,
                    'deleted_user_nik' => $user_nik
                ]);
            }
        }

        // Commit transaction
        $conn->commit();

        // Kembalikan respons JSON sukses
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'User berhasil dihapus'
        ]);
    } catch (Exception $e) {
        // Rollback transaction jika terjadi error
        if ($conn->connect_errno === 0) {
            $conn->rollback();
        }

        // Kembalikan respons JSON error
        header('Content-Type: application/json');
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}
?>
