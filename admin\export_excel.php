<?php
session_start();

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../config/config.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;

// Cek apakah pengguna sudah login dan memiliki role admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

try {
    // Get filter parameters
    $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-1 year'));
    $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

    // Prepare query with additional fields
    $query = "SELECT 
        ts.*,
        CASE 
            WHEN ts.provider_type = 'internal' THEN ts.trainer_name_internal
            ELSE ts.trainer_name_external
        END as trainer_name,
        u1.name AS approved_by_name,
        u2.name AS approved_hrd_name,
        u3.name AS approved_fm_name,
        u4.name AS approved_dir_name
    FROM training_submissions ts
    LEFT JOIN users u1 ON ts.approved_by = u1.id
    LEFT JOIN users u2 ON ts.approved_hrd = u2.id
    LEFT JOIN users u3 ON ts.approved_fm = u3.id
    LEFT JOIN users u4 ON ts.approved_dir = u4.id
    WHERE ts.created_at BETWEEN ? AND ?
    ORDER BY ts.created_at DESC";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();

    // Create new Spreadsheet object
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Training Data');

    // Set column headers
    $headers = [
        'No',
        'ID Training',
        'Nama Lengkap',
        'NIK',
        'Email',
        'Telepon',
        'Departemen',
        'Bagian',
        'Jabatan',
        'Topik Training',
        'Jenis Training',
        'Tipe Provider',
        'Nama Provider/Trainer',
        'Tanggal Training',
        'Tanggal Mulai',
        'Tanggal Selesai',
        'Lokasi Training',
        'Biaya Training',
        'Contact Person',
        'No. Telepon CP',
        'Status',
        'Sharing Knowledge',
        'Disetujui Dept Head',
        'Disetujui HRD',
        'Disetujui FM',
        'Disetujui Direktur',
        'Tanggal Pengajuan',
        'Terakhir Diupdate'
    ];

    // Style for headers
    $headerStyle = [
        'font' => [
            'bold' => true,
            'color' => ['rgb' => 'FFFFFF'],
        ],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => ['rgb' => '4472C4']
        ],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER,
            'wrapText' => true
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN
            ]
        ]
    ];

    // Apply headers
    foreach ($headers as $colIndex => $header) {
        $col = Coordinate::stringFromColumnIndex($colIndex + 1);
        $sheet->setCellValue($col . '1', $header);
    }
    $sheet->getStyle('A1:AB1')->applyFromArray($headerStyle);
    $sheet->getRowDimension(1)->setRowHeight(30);

    // Style for data
    $dataStyle = [
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN
            ]
        ],
        'alignment' => [
            'vertical' => Alignment::VERTICAL_CENTER
        ]
    ];

    // Add data rows
    $row = 2;
    $no = 1;
    while ($data = $result->fetch_assoc()) {
        $sheet->setCellValue('A' . $row, $no);
        $sheet->setCellValue('B' . $row, $data['id']);
        $sheet->setCellValue('C' . $row, $data['full_name']);
        $sheet->setCellValue('D' . $row, $data['nik']);
        $sheet->setCellValue('E' . $row, $data['email']);
        $sheet->setCellValue('F' . $row, $data['phone']);
        $sheet->setCellValue('G' . $row, $data['departemen']);
        $sheet->setCellValue('H' . $row, $data['bagian']);
        $sheet->setCellValue('I' . $row, $data['jabatan']);
        $sheet->setCellValue('J' . $row, $data['training_topic']);
        $sheet->setCellValue('K' . $row, $data['training_type']);
        $sheet->setCellValue('L' . $row, $data['provider_type']);
        $sheet->setCellValue('M' . $row, $data['trainer_name']);
        $sheet->setCellValue('N' . $row, $data['training_date']);
        $sheet->setCellValue('O' . $row, $data['training_date_start']);
        $sheet->setCellValue('P' . $row, $data['training_date_end']);
        $sheet->setCellValue('Q' . $row, $data['training_place']);
        $sheet->setCellValue('R' . $row, $data['training_cost']);
        $sheet->setCellValue('S' . $row, $data['contact_person']);
        $sheet->setCellValue('T' . $row, $data['contact_number']);
        $sheet->setCellValue('U' . $row, $data['status']);
        $sheet->setCellValue('V' . $row, $data['sharing_knowledge']);
        $sheet->setCellValue('W' . $row, $data['approved_by_name']);
        $sheet->setCellValue('X' . $row, $data['approved_hrd_name']);
        $sheet->setCellValue('Y' . $row, $data['approved_fm_name']);
        $sheet->setCellValue('Z' . $row, $data['approved_dir_name']);
        $sheet->setCellValue('AA' . $row, $data['created_at']);
        $sheet->setCellValue('AB' . $row, $data['updated_at']);
        
        $row++;
        $no++;
    }

    // Apply style to data cells
    $sheet->getStyle('A2:AB' . ($row - 1))->applyFromArray($dataStyle);

    // Auto-size columns
    foreach (range('A', 'Z') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    $sheet->getColumnDimension('AA')->setAutoSize(true);
    $sheet->getColumnDimension('AB')->setAutoSize(true);

    // Set the filename
    $filename = 'Training_Report_' . date('Y-m-d_His') . '.xlsx';

    // Clear any previous output
    ob_end_clean();

    // Set headers for download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');

    // Create Excel file
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit();

} catch (Exception $e) {
    error_log("Excel Export Error: " . $e->getMessage());
    header('Location: dashboard.php?error=' . urlencode('Export failed: ' . $e->getMessage()));
    exit();
}

