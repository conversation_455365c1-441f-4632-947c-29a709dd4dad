:root {
    --primary-color: rgb(157, 0, 0);
    --primary-color-dark: rgb(127, 0, 0);
    --primary-color-light: rgba(157, 0, 0, 0.1);
    --success-color: #4CAF50;
    --success-color-light: #e8f5e9;
    --warning-color: #ffc107;
    --danger-color: #f44336;
    --text-dark: #333;
    --text-light: #757575;
    --white: #ffffff;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
    --shadow-lg: 0 6px 15px rgba(0,0,0,0.1);
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 10px;
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
    --font-size-xs: 11px;
    --font-size-sm: 13px;
    --font-size-md: 16px;
    --font-size-lg: 20px;
    --font-size-xl: 24px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
}

body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
}

.container-form {
    padding: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
    animation: fadeIn 0.5s ease-in-out;
}

.welcome-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    transform: rotate(30deg);
    pointer-events: none;
}

.welcome-section h1 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.welcome-section p {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-md);
    opacity: 0.9;
}

.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    border-left: 4px solid var(--primary-color);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color-light) 0%, rgba(255,255,255,0) 60%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card h3 {
    margin: 0;
    color: var(--text-dark);
    font-size: var(--font-size-md);
    font-weight: 600;
    position: relative;
}

.stat-card p {
    margin: var(--spacing-sm) 0 0;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    font-weight: bold;
    position: relative;
}

.submissions-table {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
    transition: box-shadow var(--transition-normal);
}

.submissions-table:hover {
    box-shadow: var(--shadow-md);
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) #f1f1f1;
}

.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

.submissions-table table {
    width: 100%;
    border-collapse: collapse;
}

.submissions-table th {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

.submissions-table td {
    background: var(--white);
    padding: var(--spacing-md);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    vertical-align: middle;
}

.submissions-table tr:hover td {
    background-color: rgba(0,0,0,0.02);
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all var(--transition-fast);
}

.status-pending {
    background-color: rgba(255, 193, 7, 0.2);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.4);
}

.status-approved {
    background-color: rgba(76, 175, 80, 0.2);
    color: #155724;
    border: 1px solid rgba(76, 175, 80, 0.4);
}

.status-rejected {
    background-color: rgba(244, 67, 54, 0.2);
    color: #721c24;
    border: 1px solid rgba(244, 67, 54, 0.4);
}

.action-button {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-button:hover {
    background: var(--primary-color-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: var(--white);
    text-decoration: none;
}

.action-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.department-badge {
    background-color: rgba(25, 118, 210, 0.1);
    color: #1976d2;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    display: inline-flex;
    align-items: center;
    gap: 5px;
    border: 1px solid rgba(25, 118, 210, 0.2);
}

.date-fixed {
    color: var(--success-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.date-fixed i {
    color: var(--success-color);
}

.date-pending {
    color: var(--text-dark);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.date-pending i {
    color: var(--warning-color);
}

.participants-count {
    background-color: rgba(123, 31, 162, 0.1);
    color: #7b1fa2;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    display: inline-flex;
    align-items: center;
    gap: 5px;
    border: 1px solid rgba(123, 31, 162, 0.2);
}

.jarak {
    height: 80px;
}

/* Card layout for mobile */
.mobile-card {
    display: none;
}

/* Empty state styling */
.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.empty-state i {
    font-size: 3rem;
    color: var(--primary-color-light);
    margin-bottom: var(--spacing-md);
    display: block;
}

.empty-state h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--text-dark);
}

.empty-state p {
    color: var(--primary-color);
    max-width: 400px;
    margin: 0 auto;
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 90%;
    width: 350px;
}

.error-message,
.success-message {
    padding: 15px;
    border-radius: var(--border-radius-md);
    margin-bottom: 10px;
    font-size: var(--font-size-sm);
    animation: slideIn 0.3s ease-out;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: 10px;
}

.error-message {
    background: var(--danger-color);
    color: white;
    border-left: 5px solid #b71c1c;
}

.success-message {
    background: var(--success-color);
    color: white;
    border-left: 5px solid #1b5e20;
}

.notification-icon {
    font-size: 20px;
}

.notification-content {
    flex: 1;
}

.notification-close {
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.notification-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Mobile Card View */
.mobile-submission-card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.mobile-submission-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.mobile-card-header {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    font-weight: 600;
}

.mobile-card-body {
    padding: var(--spacing-md);
}

.mobile-card-row {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.mobile-card-row:last-child {
    border-bottom: none;
}

.mobile-card-label {
    font-weight: 600;
    color: var(--text-dark);
    font-size: var(--font-size-sm);
}

.mobile-card-value {
    text-align: right;
    font-size: var(--font-size-sm);
}

.mobile-card-footer {
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(0,0,0,0.02);
    text-align: center;
}

/* Responsive styles */
@media screen and (max-width: 992px) {
    .stats-section {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .data-table th, .data-table td {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .action-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .action-buttons .btn {
        margin: var(--spacing-xs);
    }
}

@media screen and (max-width: 768px) {
    .container-form {
        padding: var(--spacing-sm);
        margin-top: 60px;
    }

    .welcome-section {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
        border-radius: var(--border-radius-md);
    }

    .welcome-section h1 {
        font-size: var(--font-size-lg);
    }
    
    .welcome-section p {
        font-size: var(--font-size-sm);
    }

    .stats-section {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--spacing-sm);
    }

    .stat-card {
        padding: var(--spacing-md);
    }
    
    .stat-card-title {
        font-size: var(--font-size-sm);
    }
    
    .stat-card-value {
        font-size: var(--font-size-lg);
    }

    /* Hide regular table on mobile */
    .desktop-table {
        display: none;
    }

    /* Show card layout on mobile */
    .mobile-card {
        display: block;
        margin-bottom: var(--spacing-lg);
        box-shadow: var(--shadow-sm);
    }

    .notifications-container {
        width: 90%;
        max-width: 400px;
    }
    
    .filter-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        margin-bottom: var(--spacing-sm);
    }
    
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media screen and (max-width: 480px) {
    .welcome-section h1 {
        font-size: var(--font-size-md);
    }
    
    .welcome-section p {
        font-size: var(--font-size-xs);
    }
    
    .stats-section {
        grid-template-columns: 1fr;
    }
    
    .mobile-card-header {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .mobile-card-body {
        padding: var(--spacing-sm);
    }
    
    .mobile-card-row {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .mobile-card-footer {
        padding: var(--spacing-sm);
    }
    
    .mobile-card-footer .btn {
        width: 100%;
        margin-bottom: var(--spacing-xs);
    }
    
    .notifications-container {
        width: 95%;
        max-width: 350px;
    }
}

@media screen and (max-width: 480px) {
    .welcome-section h1 {
        font-size: var(--font-size-md);
    }

    .welcome-section p {
        font-size: var(--font-size-sm);
    }

    .stat-card h3 {
        font-size: var(--font-size-sm);
    }

    .stat-card p {
        font-size: var(--font-size-lg);
    }

    .jarak {
        height: 60px;
    }

    .action-button {
        padding: 6px 12px;
        font-size: var(--font-size-xs);
    }
}
