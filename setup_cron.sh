#!/bin/bash

# Setup script untuk Auto-Update Training Status Cron Job
# 
# Script ini akan:
# 1. Membuat direktori logs jika belum ada
# 2. Set permissions yang tepat
# 3. Menambahkan cron job ke crontab
# 4. Test cron job

echo "=== Setup Auto-Update Training Status Cron Job ==="
echo ""

# Get current directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
CRON_SCRIPT="$SCRIPT_DIR/cron/auto_update_training.php"
LOG_DIR="$SCRIPT_DIR/logs"

echo "Script directory: $SCRIPT_DIR"
echo "Cron script: $CRON_SCRIPT"
echo "Log directory: $LOG_DIR"
echo ""

# 1. Create logs directory
echo "1. Creating logs directory..."
if [ ! -d "$LOG_DIR" ]; then
    mkdir -p "$LOG_DIR"
    echo "   ✓ Created logs directory: $LOG_DIR"
else
    echo "   ✓ Logs directory already exists"
fi

# 2. Set permissions
echo ""
echo "2. Setting permissions..."
chmod 755 "$CRON_SCRIPT"
chmod 755 "$LOG_DIR"
echo "   ✓ Set executable permission for cron script"
echo "   ✓ Set write permission for logs directory"

# 3. Test PHP path
echo ""
echo "3. Testing PHP installation..."
PHP_PATH=$(which php)
if [ -z "$PHP_PATH" ]; then
    echo "   ❌ PHP not found in PATH"
    echo "   Please install PHP or update PATH"
    exit 1
else
    echo "   ✓ PHP found at: $PHP_PATH"
    PHP_VERSION=$($PHP_PATH -v | head -n 1)
    echo "   ✓ PHP version: $PHP_VERSION"
fi

# 4. Test cron script
echo ""
echo "4. Testing cron script..."
if [ -f "$CRON_SCRIPT" ]; then
    echo "   ✓ Cron script exists"
    
    # Test syntax
    $PHP_PATH -l "$CRON_SCRIPT" > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "   ✓ PHP syntax check passed"
    else
        echo "   ❌ PHP syntax error in cron script"
        $PHP_PATH -l "$CRON_SCRIPT"
        exit 1
    fi
else
    echo "   ❌ Cron script not found: $CRON_SCRIPT"
    exit 1
fi

# 5. Check existing cron jobs
echo ""
echo "5. Checking existing cron jobs..."
EXISTING_CRON=$(crontab -l 2>/dev/null | grep "auto_update_training.php" || true)
if [ -n "$EXISTING_CRON" ]; then
    echo "   ⚠️  Existing cron job found:"
    echo "   $EXISTING_CRON"
    echo ""
    read -p "   Do you want to replace it? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "   Skipping cron job installation"
        SKIP_CRON=1
    fi
else
    echo "   ✓ No existing cron job found"
fi

# 6. Install cron job
if [ -z "$SKIP_CRON" ]; then
    echo ""
    echo "6. Installing cron job..."
    
    # Backup existing crontab
    crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
    
    # Remove existing auto_update_training cron job if any
    (crontab -l 2>/dev/null | grep -v "auto_update_training.php") | crontab -
    
    # Add new cron job
    CRON_JOB="1 0 * * * $PHP_PATH $CRON_SCRIPT"
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    
    echo "   ✓ Cron job installed:"
    echo "   $CRON_JOB"
    echo ""
    echo "   This will run every day at 00:01"
fi

# 7. Test run (optional)
echo ""
read -p "7. Do you want to test run the cron script now? (y/N): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "   Running test..."
    echo "   Output:"
    echo "   ----------------------------------------"
    $PHP_PATH "$CRON_SCRIPT"
    echo "   ----------------------------------------"
    echo "   ✓ Test run completed"
    
    if [ -f "$LOG_DIR/auto_update_cron.log" ]; then
        echo ""
        echo "   Log file created: $LOG_DIR/auto_update_cron.log"
        echo "   Last 5 lines:"
        tail -5 "$LOG_DIR/auto_update_cron.log"
    fi
fi

# 8. Summary
echo ""
echo "=== Setup Summary ==="
echo "✓ Logs directory: $LOG_DIR"
echo "✓ Cron script: $CRON_SCRIPT"
echo "✓ PHP path: $PHP_PATH"
if [ -z "$SKIP_CRON" ]; then
    echo "✓ Cron job: Installed (runs daily at 00:01)"
else
    echo "⚠️ Cron job: Skipped"
fi
echo ""
echo "=== Next Steps ==="
echo "1. Monitor the auto-update system at: admin/auto_update_monitor.php"
echo "2. Check logs at: $LOG_DIR/auto_update_cron.log"
echo "3. Verify cron job: crontab -l | grep auto_update_training"
echo ""
echo "=== Manual Commands ==="
echo "• Test cron script: $PHP_PATH $CRON_SCRIPT"
echo "• View cron jobs: crontab -l"
echo "• Edit cron jobs: crontab -e"
echo "• View logs: tail -f $LOG_DIR/auto_update_cron.log"
echo ""
echo "Setup completed successfully! 🎉"
