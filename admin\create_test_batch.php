<?php
/**
 * Create Test Batch Data
 * This file creates test batch records for demonstrating rollback functionality
 */

session_start();
require_once '../config/config.php';
require_once 'record_batch_employee_history.php';
require_once 'karyawan_schema_helper.php';

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo 'Unauthorized access';
    exit();
}

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Create Test Batch Data</h2>";
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_test'])) {
        // Create test employees data
        $test_employees = [
            ['nik' => 'TEST001', 'nama' => 'Test Employee 1', 'jabatan' => 'Staff', 'is_active' => 1],
            ['nik' => 'TEST002', 'nama' => 'Test Employee 2', 'jabatan' => 'Staff', 'is_active' => 1],
            ['nik' => 'TEST003', 'nama' => 'Test Employee 3', 'jabatan' => 'Staff', 'is_active' => 1]
        ];
        
        $inserted_niks = [];
        $success_count = 0;
        
        foreach ($test_employees as $emp) {
            try {
                // Check if employee already exists
                $check_stmt = $pdo->prepare("SELECT id FROM karyawan WHERE nik = ?");
                $check_stmt->execute([$emp['nik']]);

                if (!$check_stmt->fetch()) {
                    // Use helper function to get safe INSERT query
                    $insert_info = getKaryawanInsertQuery($pdo, $emp);
                    $insert_stmt = $pdo->prepare($insert_info['query']);

                    if ($insert_stmt->execute($insert_info['data'])) {
                        $inserted_niks[] = $emp['nik'];
                        $success_count++;
                        echo "<p>✓ Created test employee: {$emp['nik']} - {$emp['nama']}</p>";
                    }
                } else {
                    echo "<p>⚠️ Employee {$emp['nik']} already exists, skipping</p>";
                    $inserted_niks[] = $emp['nik']; // Still include in batch for rollback testing
                }
            } catch (Exception $e) {
                echo "<p>❌ Failed to create employee {$emp['nik']}: " . $e->getMessage() . "</p>";
            }
        }
        
        // Record batch history
        if (!empty($inserted_niks)) {
            $batch_id = recordBatchEmployeeHistory(
                $pdo,
                'BATCH_INSERT',
                $inserted_niks,
                'add',
                $success_count,
                0,
                0
            );
            
            if ($batch_id) {
                // Update the batch to be rollback capable
                $update_stmt = $pdo->prepare("
                    UPDATE karyawan_batch_history 
                    SET is_rollback_capable = TRUE, rollback_status = 'AVAILABLE' 
                    WHERE batch_id = ?
                ");
                $update_stmt->execute([$batch_id]);
                
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; margin: 20px 0;'>";
                echo "<h3>✅ Test Batch Created Successfully!</h3>";
                echo "<p><strong>Batch ID:</strong> {$batch_id}</p>";
                echo "<p><strong>Action Type:</strong> BATCH_INSERT</p>";
                echo "<p><strong>Employees Created:</strong> {$success_count}</p>";
                echo "<p><strong>NIKs:</strong> " . implode(', ', $inserted_niks) . "</p>";
                echo "<p><strong>Rollback Status:</strong> AVAILABLE</p>";
                echo "</div>";
                
                echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
                echo "<h4>🎯 Now You Can Test Rollback:</h4>";
                echo "<ol>";
                echo "<li><a href='batch_employee_history.php' target='_blank'>Go to Batch History</a></li>";
                echo "<li>Look for Batch ID {$batch_id}</li>";
                echo "<li>Click the yellow 'Rollback' button</li>";
                echo "<li>Follow the rollback process</li>";
                echo "<li>The test employees will be deleted</li>";
                echo "</ol>";
                echo "</div>";
                
            } else {
                echo "<p style='color: red;'>❌ Failed to record batch history</p>";
            }
        }
        
    } else {
        // Show form to create test data
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; margin: 20px 0;'>";
        echo "<h3>📋 Create Test Data for Rollback Demo</h3>";
        echo "<p>This will create 3 test employees and a BATCH_INSERT record that you can use to test the rollback functionality.</p>";
        echo "<p><strong>Test employees that will be created:</strong></p>";
        echo "<ul>";
        echo "<li>TEST001 - Test Employee 1 (IT Department)</li>";
        echo "<li>TEST002 - Test Employee 2 (HR Department)</li>";
        echo "<li>TEST003 - Test Employee 3 (Finance Department)</li>";
        echo "</ul>";
        echo "</div>";
        
        // Check if test employees already exist
        $existing_query = getKaryawanSelectQuery($pdo, [], "nik LIKE 'TEST%'", 'nik');
        $existing_test = $pdo->prepare($existing_query);
        $existing_test->execute();
        $existing = $existing_test->fetchAll(PDO::FETCH_ASSOC);

        if (!empty($existing)) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
            echo "<h4>⚠️ Existing Test Employees Found:</h4>";
            echo "<ul>";
            foreach ($existing as $emp) {
                $display_name = getEmployeeDisplayName($emp);
                echo "<li>{$emp['nik']} - {$display_name}</li>";
            }
            echo "</ul>";
            echo "<p>Creating test batch will include these existing employees.</p>";
            echo "</div>";
        }
        
        echo "<form method='post'>";
        echo "<button type='submit' name='create_test' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>";
        echo "🚀 Create Test Batch Data";
        echo "</button>";
        echo "</form>";
        
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545; margin: 20px 0;'>";
        echo "<h4>⚠️ Important Notes:</h4>";
        echo "<ul>";
        echo "<li>This creates real employee records in the database</li>";
        echo "<li>Use rollback to remove them after testing</li>";
        echo "<li>Test employees have NIKs starting with 'TEST'</li>";
        echo "<li>Safe to use in development environment</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    // Show existing batch history
    echo "<h3>📊 Current Batch History:</h3>";
    $stmt = $pdo->prepare("
        SELECT batch_id, action_type, is_rollback_capable, rollback_status, change_timestamp
        FROM karyawan_batch_history
        ORDER BY change_timestamp DESC
        LIMIT 10
    ");
    $stmt->execute();
    $batches = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($batches)) {
        echo "<p>No batch history records found.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Batch ID</th><th>Action Type</th><th>Rollback Capable</th><th>Status</th><th>Timestamp</th><th>Action</th></tr>";

        foreach ($batches as $batch) {
            $capable_color = $batch['is_rollback_capable'] ? 'green' : 'red';
            $capable_text = $batch['is_rollback_capable'] ? 'Yes' : 'No';

            echo "<tr>";
            echo "<td>{$batch['batch_id']}</td>";
            echo "<td>{$batch['action_type']}</td>";
            echo "<td style='color: {$capable_color};'>{$capable_text}</td>";
            echo "<td>{$batch['rollback_status']}</td>";
            echo "<td>{$batch['change_timestamp']}</td>";
            echo "<td>";

            // Show fix button for BATCH_UPDATE that are not rollback capable
            if ($batch['action_type'] === 'BATCH_UPDATE' && !$batch['is_rollback_capable']) {
                echo "<button onclick='fixExistingBatch({$batch['batch_id']})' style='background: #ffc107; color: #212529; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer;'>Make Rollback Capable</button>";
            } elseif ($batch['action_type'] === 'BATCH_INSERT' && !$batch['is_rollback_capable']) {
                echo "<button onclick='fixExistingBatch({$batch['batch_id']})' style='background: #28a745; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer;'>Fix Rollback</button>";
            } else {
                echo "<span style='color: green;'>✓ Ready</span>";
            }

            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";

        // Add fix all button if there are non-rollback-capable batches
        $non_capable = array_filter($batches, function($b) { return !$b['is_rollback_capable']; });
        if (!empty($non_capable)) {
            echo "<p><button onclick='fixAllExistingBatches()' style='background: #17a2b8; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 0;'>Fix All Existing Batches</button></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<script>
function fixExistingBatch(batchId) {
    if (confirm('Make batch ID ' + batchId + ' rollback capable?')) {
        fetch('fix_batch_rollback.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'batch_id=' + batchId
        })
        .then(response => response.text())
        .then(data => {
            alert('Batch fixed: ' + data);
            location.reload();
        })
        .catch(error => {
            alert('Error: ' + error);
        });
    }
}

function fixAllExistingBatches() {
    if (confirm('Make all existing batches rollback capable?')) {
        fetch('fix_batch_rollback.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'fix_all=1'
        })
        .then(response => response.text())
        .then(data => {
            alert('All batches fixed: ' + data);
            location.reload();
        })
        .catch(error => {
            alert('Error: ' + error);
        });
    }
}
</script>

<style>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

form {
    margin: 20px 0;
}
</style>
