<?php
/**
 * Generate Excel Template for Training Import
 * This script generates an Excel template with multiple sheets for importing training data
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Require PhpSpreadsheet library
// You'll need to install this via Composer: composer require phpoffice/phpspreadsheet
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;

// Create a new spreadsheet
$spreadsheet = new Spreadsheet();

// Set document properties
$spreadsheet->getProperties()
    ->setCreator('Training System')
    ->setLastModifiedBy('Training System')
    ->setTitle('Training Import Template')
    ->setSubject('Training Import Template')
    ->setDescription('Template for importing training data')
    ->setKeywords('training import template')
    ->setCategory('Templates');

// Create Classes sheet
$classesSheet = $spreadsheet->getActiveSheet();
$classesSheet->setTitle('Classes');

// Set header style
$headerStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FFFFFF'],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => '4472C4'],
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
        ],
    ],
];

// Set instruction style
$instructionStyle = [
    'font' => [
        'italic' => true,
        'color' => ['rgb' => '808080'],
    ],
];

// Set required field style
$requiredStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FF0000'],
    ],
];

// Add instructions to Classes sheet
$classesSheet->setCellValue('A1', 'INSTRUCTIONS:');
$classesSheet->mergeCells('A1:H1');
$classesSheet->setCellValue('A2', '1. Fields marked with * are required');
$classesSheet->mergeCells('A2:H2');
$classesSheet->setCellValue('A3', '2. training_id must be a valid ID from an approved training');
$classesSheet->mergeCells('A3:H3');
$classesSheet->setCellValue('A4', '3. status must be one of: active, inactive, completed');
$classesSheet->mergeCells('A4:H4');
$classesSheet->setCellValue('A5', '4. Dates should be in YYYY-MM-DD format');
$classesSheet->mergeCells('A5:H5');

// Apply instruction style
$classesSheet->getStyle('A1:H5')->applyFromArray($instructionStyle);

// Add headers to Classes sheet
$classesHeaders = [
    'A7' => 'training_id*',
    'B7' => 'title*',
    'C7' => 'description',
    'D7' => 'start_date',
    'E7' => 'end_date',
    'F7' => 'status*',
    'G7' => 'max_participants',
    'H7' => 'enrollment_key'
];

foreach ($classesHeaders as $cell => $value) {
    $classesSheet->setCellValue($cell, $value);
}

// Apply header style
$classesSheet->getStyle('A7:H7')->applyFromArray($headerStyle);

// Add example data
$classesSheet->setCellValue('A8', '1');
$classesSheet->setCellValue('B8', 'Introduction to Excel');
$classesSheet->setCellValue('C8', 'Learn the basics of Microsoft Excel');
$classesSheet->setCellValue('D8', '2023-12-01');
$classesSheet->setCellValue('E8', '2023-12-15');
$classesSheet->setCellValue('F8', 'active');
$classesSheet->setCellValue('G8', '30');
$classesSheet->setCellValue('H8', 'excel123');

// Set column widths
$classesSheet->getColumnDimension('A')->setWidth(12);
$classesSheet->getColumnDimension('B')->setWidth(30);
$classesSheet->getColumnDimension('C')->setWidth(40);
$classesSheet->getColumnDimension('D')->setWidth(15);
$classesSheet->getColumnDimension('E')->setWidth(15);
$classesSheet->getColumnDimension('F')->setWidth(15);
$classesSheet->getColumnDimension('G')->setWidth(18);
$classesSheet->getColumnDimension('H')->setWidth(20);

// Add data validation for status
$statusValidation = $classesSheet->getCell('F8')->getDataValidation();
$statusValidation->setType(DataValidation::TYPE_LIST);
$statusValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
$statusValidation->setAllowBlank(false);
$statusValidation->setShowInputMessage(true);
$statusValidation->setShowErrorMessage(true);
$statusValidation->setShowDropDown(true);
$statusValidation->setFormula1('"active,inactive,completed"');

// Copy validation to the range
$classesSheet->setDataValidation('F8:F100', $statusValidation);

// Create Participants sheet
$participantsSheet = $spreadsheet->createSheet();
$participantsSheet->setTitle('Participants');

// Add instructions to Participants sheet
$participantsSheet->setCellValue('A1', 'INSTRUCTIONS:');
$participantsSheet->mergeCells('A1:D1');
$participantsSheet->setCellValue('A2', '1. Fields marked with * are required');
$participantsSheet->mergeCells('A2:D2');
$participantsSheet->setCellValue('A3', '2. class_id must be a valid class ID');
$participantsSheet->mergeCells('A3:D3');
$participantsSheet->setCellValue('A4', '3. user_id must be a valid user ID');
$participantsSheet->mergeCells('A4:D4');
$participantsSheet->setCellValue('A5', '4. status must be one of: active, inactive, completed');
$participantsSheet->mergeCells('A5:D5');

// Apply instruction style
$participantsSheet->getStyle('A1:D5')->applyFromArray($instructionStyle);

// Add headers to Participants sheet
$participantsHeaders = [
    'A7' => 'class_id*',
    'B7' => 'user_id*',
    'C7' => 'status*',
    'D7' => 'enrollment_date'
];

foreach ($participantsHeaders as $cell => $value) {
    $participantsSheet->setCellValue($cell, $value);
}

// Apply header style
$participantsSheet->getStyle('A7:D7')->applyFromArray($headerStyle);

// Add example data
$participantsSheet->setCellValue('A8', '1');
$participantsSheet->setCellValue('B8', '5');
$participantsSheet->setCellValue('C8', 'active');
$participantsSheet->setCellValue('D8', '2023-11-15');

// Set column widths
$participantsSheet->getColumnDimension('A')->setWidth(12);
$participantsSheet->getColumnDimension('B')->setWidth(12);
$participantsSheet->getColumnDimension('C')->setWidth(15);
$participantsSheet->getColumnDimension('D')->setWidth(20);

// Add data validation for status
$statusValidation = $participantsSheet->getCell('C8')->getDataValidation();
$statusValidation->setType(DataValidation::TYPE_LIST);
$statusValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
$statusValidation->setAllowBlank(false);
$statusValidation->setShowInputMessage(true);
$statusValidation->setShowErrorMessage(true);
$statusValidation->setShowDropDown(true);
$statusValidation->setFormula1('"active,inactive,completed"');

// Copy validation to the range
$participantsSheet->setDataValidation('C8:C100', $statusValidation);

// Create Quizzes sheet
$quizzesSheet = $spreadsheet->createSheet();
$quizzesSheet->setTitle('Quizzes');

// Add instructions to Quizzes sheet
$quizzesSheet->setCellValue('A1', 'INSTRUCTIONS:');
$quizzesSheet->mergeCells('A1:I1');
$quizzesSheet->setCellValue('A2', '1. Fields marked with * are required');
$quizzesSheet->mergeCells('A2:I2');
$quizzesSheet->setCellValue('A3', '2. class_id must be a valid class ID');
$quizzesSheet->mergeCells('A3:I3');
$quizzesSheet->setCellValue('A4', '3. is_published, allow_multiple_attempts, and randomize_questions must be 0 or 1');
$quizzesSheet->mergeCells('A4:I4');

// Apply instruction style
$quizzesSheet->getStyle('A1:I4')->applyFromArray($instructionStyle);

// Add headers to Quizzes sheet
$quizzesHeaders = [
    'A6' => 'class_id*',
    'B6' => 'title*',
    'C6' => 'description',
    'D6' => 'instructions',
    'E6' => 'time_limit',
    'F6' => 'passing_score',
    'G6' => 'is_published*',
    'H6' => 'allow_multiple_attempts*',
    'I6' => 'randomize_questions*'
];

foreach ($quizzesHeaders as $cell => $value) {
    $quizzesSheet->setCellValue($cell, $value);
}

// Apply header style
$quizzesSheet->getStyle('A6:I6')->applyFromArray($headerStyle);

// Add example data
$quizzesSheet->setCellValue('A7', '1');
$quizzesSheet->setCellValue('B7', 'Excel Basics Quiz');
$quizzesSheet->setCellValue('C7', 'Test your knowledge of Excel basics');
$quizzesSheet->setCellValue('D7', 'Answer all questions. You have 30 minutes to complete this quiz.');
$quizzesSheet->setCellValue('E7', '30');
$quizzesSheet->setCellValue('F7', '70');
$quizzesSheet->setCellValue('G7', '1');
$quizzesSheet->setCellValue('H7', '0');
$quizzesSheet->setCellValue('I7', '1');

// Set column widths
$quizzesSheet->getColumnDimension('A')->setWidth(12);
$quizzesSheet->getColumnDimension('B')->setWidth(30);
$quizzesSheet->getColumnDimension('C')->setWidth(40);
$quizzesSheet->getColumnDimension('D')->setWidth(40);
$quizzesSheet->getColumnDimension('E')->setWidth(15);
$quizzesSheet->getColumnDimension('F')->setWidth(15);
$quizzesSheet->getColumnDimension('G')->setWidth(15);
$quizzesSheet->getColumnDimension('H')->setWidth(25);
$quizzesSheet->getColumnDimension('I')->setWidth(25);

// Create Questions sheet
$questionsSheet = $spreadsheet->createSheet();
$questionsSheet->setTitle('Questions');

// Add instructions to Questions sheet
$questionsSheet->setCellValue('A1', 'INSTRUCTIONS:');
$questionsSheet->mergeCells('A1:H1');
$questionsSheet->setCellValue('A2', '1. Fields marked with * are required');
$questionsSheet->mergeCells('A2:H2');
$questionsSheet->setCellValue('A3', '2. quiz_id must be a valid quiz ID');
$questionsSheet->mergeCells('A3:H3');
$questionsSheet->setCellValue('A4', '3. question_type must be one of: multiple_choice, true_false, short_answer, essay');
$questionsSheet->mergeCells('A4:H4');
$questionsSheet->setCellValue('A5', '4. For multiple_choice and true_false questions, provide options in the Options sheet');
$questionsSheet->mergeCells('A5:H5');

// Apply instruction style
$questionsSheet->getStyle('A1:H5')->applyFromArray($instructionStyle);

// Add headers to Questions sheet
$questionsHeaders = [
    'A7' => 'quiz_id*',
    'B7' => 'question_text*',
    'C7' => 'question_type*',
    'D7' => 'points*',
    'E7' => 'order_number',
    'F7' => 'correct_answer',
    'G7' => 'feedback_correct',
    'H7' => 'feedback_incorrect'
];

foreach ($questionsHeaders as $cell => $value) {
    $questionsSheet->setCellValue($cell, $value);
}

// Apply header style
$questionsSheet->getStyle('A7:H7')->applyFromArray($headerStyle);

// Add example data
$questionsSheet->setCellValue('A8', '1');
$questionsSheet->setCellValue('B8', 'What does the function SUM do in Excel?');
$questionsSheet->setCellValue('C8', 'multiple_choice');
$questionsSheet->setCellValue('D8', '10');
$questionsSheet->setCellValue('E8', '1');
$questionsSheet->setCellValue('F8', '');
$questionsSheet->setCellValue('G8', 'Correct! SUM adds up values.');
$questionsSheet->setCellValue('H8', 'Incorrect. SUM is used to add up values in a range of cells.');

// Set column widths
$questionsSheet->getColumnDimension('A')->setWidth(12);
$questionsSheet->getColumnDimension('B')->setWidth(50);
$questionsSheet->getColumnDimension('C')->setWidth(20);
$questionsSheet->getColumnDimension('D')->setWidth(10);
$questionsSheet->getColumnDimension('E')->setWidth(15);
$questionsSheet->getColumnDimension('F')->setWidth(20);
$questionsSheet->getColumnDimension('G')->setWidth(30);
$questionsSheet->getColumnDimension('H')->setWidth(30);

// Add data validation for question_type
$typeValidation = $questionsSheet->getCell('C8')->getDataValidation();
$typeValidation->setType(DataValidation::TYPE_LIST);
$typeValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
$typeValidation->setAllowBlank(false);
$typeValidation->setShowInputMessage(true);
$typeValidation->setShowErrorMessage(true);
$typeValidation->setShowDropDown(true);
$typeValidation->setFormula1('"multiple_choice,true_false,short_answer,essay"');

// Copy validation to the range
$questionsSheet->setDataValidation('C8:C100', $typeValidation);

// Create Options sheet
$optionsSheet = $spreadsheet->createSheet();
$optionsSheet->setTitle('Options');

// Add instructions to Options sheet
$optionsSheet->setCellValue('A1', 'INSTRUCTIONS:');
$optionsSheet->mergeCells('A1:E1');
$optionsSheet->setCellValue('A2', '1. Fields marked with * are required');
$optionsSheet->mergeCells('A2:E2');
$optionsSheet->setCellValue('A3', '2. question_id must be a valid question ID from the Questions sheet');
$optionsSheet->mergeCells('A3:E3');
$optionsSheet->setCellValue('A4', '3. is_correct must be 0 or 1');
$optionsSheet->mergeCells('A4:E4');

// Apply instruction style
$optionsSheet->getStyle('A1:E4')->applyFromArray($instructionStyle);

// Add headers to Options sheet
$optionsHeaders = [
    'A6' => 'question_id*',
    'B6' => 'option_text*',
    'C6' => 'is_correct*',
    'D6' => 'order_number',
    'E6' => 'feedback'
];

foreach ($optionsHeaders as $cell => $value) {
    $optionsSheet->setCellValue($cell, $value);
}

// Apply header style
$optionsSheet->getStyle('A6:E6')->applyFromArray($headerStyle);

// Add example data
$optionsSheet->setCellValue('A7', '1');
$optionsSheet->setCellValue('B7', 'Adds up values in a range of cells');
$optionsSheet->setCellValue('C7', '1');
$optionsSheet->setCellValue('D7', '1');
$optionsSheet->setCellValue('E7', 'This is the correct function of SUM');

$optionsSheet->setCellValue('A8', '1');
$optionsSheet->setCellValue('B8', 'Calculates the average of values');
$optionsSheet->setCellValue('C8', '0');
$optionsSheet->setCellValue('D8', '2');
$optionsSheet->setCellValue('E8', 'This is what AVERAGE does, not SUM');

$optionsSheet->setCellValue('A9', '1');
$optionsSheet->setCellValue('B9', 'Counts the number of cells in a range');
$optionsSheet->setCellValue('C9', '0');
$optionsSheet->setCellValue('D9', '3');
$optionsSheet->setCellValue('E9', 'This is what COUNT does, not SUM');

$optionsSheet->setCellValue('A10', '1');
$optionsSheet->setCellValue('B10', 'Finds the maximum value in a range');
$optionsSheet->setCellValue('C10', '0');
$optionsSheet->setCellValue('D10', '4');
$optionsSheet->setCellValue('E10', 'This is what MAX does, not SUM');

// Set column widths
$optionsSheet->getColumnDimension('A')->setWidth(15);
$optionsSheet->getColumnDimension('B')->setWidth(50);
$optionsSheet->getColumnDimension('C')->setWidth(15);
$optionsSheet->getColumnDimension('D')->setWidth(15);
$optionsSheet->getColumnDimension('E')->setWidth(40);

// Create Materials sheet
$materialsSheet = $spreadsheet->createSheet();
$materialsSheet->setTitle('Materials');

// Add instructions to Materials sheet
$materialsSheet->setCellValue('A1', 'INSTRUCTIONS:');
$materialsSheet->mergeCells('A1:F1');
$materialsSheet->setCellValue('A2', '1. Fields marked with * are required');
$materialsSheet->mergeCells('A2:F2');
$materialsSheet->setCellValue('A3', '2. class_id must be a valid class ID');
$materialsSheet->mergeCells('A3:F3');
$materialsSheet->setCellValue('A4', '3. material_type must be one of: document, video, link, text');
$materialsSheet->mergeCells('A4:F4');
$materialsSheet->setCellValue('A5', '4. For document and video types, you will need to upload the files separately');
$materialsSheet->mergeCells('A5:F5');

// Apply instruction style
$materialsSheet->getStyle('A1:F5')->applyFromArray($instructionStyle);

// Add headers to Materials sheet
$materialsHeaders = [
    'A7' => 'class_id*',
    'B7' => 'title*',
    'C7' => 'description',
    'D7' => 'material_type*',
    'E7' => 'content_or_url',
    'F7' => 'order_number'
];

foreach ($materialsHeaders as $cell => $value) {
    $materialsSheet->setCellValue($cell, $value);
}

// Apply header style
$materialsSheet->getStyle('A7:F7')->applyFromArray($headerStyle);

// Add example data
$materialsSheet->setCellValue('A8', '1');
$materialsSheet->setCellValue('B8', 'Excel Basics PDF');
$materialsSheet->setCellValue('C8', 'Introduction to Excel basics');
$materialsSheet->setCellValue('D8', 'document');
$materialsSheet->setCellValue('E8', 'excel_basics.pdf');
$materialsSheet->setCellValue('F8', '1');

$materialsSheet->setCellValue('A9', '1');
$materialsSheet->setCellValue('B9', 'Excel Tutorial Video');
$materialsSheet->setCellValue('C9', 'Video tutorial for Excel basics');
$materialsSheet->setCellValue('D9', 'video');
$materialsSheet->setCellValue('E9', 'excel_tutorial.mp4');
$materialsSheet->setCellValue('F9', '2');

$materialsSheet->setCellValue('A10', '1');
$materialsSheet->setCellValue('B10', 'Excel Documentation');
$materialsSheet->setCellValue('C10', 'Link to official Excel documentation');
$materialsSheet->setCellValue('D10', 'link');
$materialsSheet->setCellValue('E10', 'https://support.microsoft.com/en-us/excel');
$materialsSheet->setCellValue('F10', '3');

// Set column widths
$materialsSheet->getColumnDimension('A')->setWidth(12);
$materialsSheet->getColumnDimension('B')->setWidth(30);
$materialsSheet->getColumnDimension('C')->setWidth(40);
$materialsSheet->getColumnDimension('D')->setWidth(15);
$materialsSheet->getColumnDimension('E')->setWidth(40);
$materialsSheet->getColumnDimension('F')->setWidth(15);

// Add data validation for material_type
$typeValidation = $materialsSheet->getCell('D8')->getDataValidation();
$typeValidation->setType(DataValidation::TYPE_LIST);
$typeValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
$typeValidation->setAllowBlank(false);
$typeValidation->setShowInputMessage(true);
$typeValidation->setShowErrorMessage(true);
$typeValidation->setShowDropDown(true);
$typeValidation->setFormula1('"document,video,link,text"');

// Copy validation to the range
$materialsSheet->setDataValidation('D8:D100', $typeValidation);

// Create Assignments sheet
$assignmentsSheet = $spreadsheet->createSheet();
$assignmentsSheet->setTitle('Assignments');

// Add instructions to Assignments sheet
$assignmentsSheet->setCellValue('A1', 'INSTRUCTIONS:');
$assignmentsSheet->mergeCells('A1:G1');
$assignmentsSheet->setCellValue('A2', '1. Fields marked with * are required');
$assignmentsSheet->mergeCells('A2:G2');
$assignmentsSheet->setCellValue('A3', '2. class_id must be a valid class ID');
$assignmentsSheet->mergeCells('A3:G3');
$assignmentsSheet->setCellValue('A4', '3. Dates should be in YYYY-MM-DD format');
$assignmentsSheet->mergeCells('A4:G4');

// Apply instruction style
$assignmentsSheet->getStyle('A1:G4')->applyFromArray($instructionStyle);

// Add headers to Assignments sheet
$assignmentsHeaders = [
    'A6' => 'class_id*',
    'B6' => 'title*',
    'C6' => 'description*',
    'D6' => 'due_date',
    'E6' => 'max_points',
    'F6' => 'is_published*',
    'G6' => 'allow_late_submission'
];

foreach ($assignmentsHeaders as $cell => $value) {
    $assignmentsSheet->setCellValue($cell, $value);
}

// Apply header style
$assignmentsSheet->getStyle('A6:G6')->applyFromArray($headerStyle);

// Add example data
$assignmentsSheet->setCellValue('A7', '1');
$assignmentsSheet->setCellValue('B7', 'Excel Practice Assignment');
$assignmentsSheet->setCellValue('C7', 'Create a spreadsheet with formulas and charts');
$assignmentsSheet->setCellValue('D7', '2023-12-10');
$assignmentsSheet->setCellValue('E7', '100');
$assignmentsSheet->setCellValue('F7', '1');
$assignmentsSheet->setCellValue('G7', '1');

// Set column widths
$assignmentsSheet->getColumnDimension('A')->setWidth(12);
$assignmentsSheet->getColumnDimension('B')->setWidth(30);
$assignmentsSheet->getColumnDimension('C')->setWidth(50);
$assignmentsSheet->getColumnDimension('D')->setWidth(15);
$assignmentsSheet->getColumnDimension('E')->setWidth(15);
$assignmentsSheet->getColumnDimension('F')->setWidth(15);
$assignmentsSheet->getColumnDimension('G')->setWidth(25);

// Set the first sheet as active
$spreadsheet->setActiveSheetIndex(0);

// Create writer
$writer = new Xlsx($spreadsheet);

// Set headers for download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="training_import_template.xlsx"');
header('Cache-Control: max-age=0');

// Save to output
$writer->save('php://output');
exit;
?>
