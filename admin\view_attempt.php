<?php
/**
 * View Quiz Attempt Page for Admin
 * This page displays the details of a quiz attempt by a participant
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if attempt ID is provided
$attempt_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($attempt_id <= 0) {
    header('Location: manage_classes.php');
    exit();
}

// Get attempt information
$attempt_query = "SELECT a.*, q.title as quiz_title, q.passing_score, q.show_correct_answers,
                 c.id as class_id, c.title as class_title, t.training_topic,
                 u.name, u.email, u.nik
                 FROM training_quiz_attempts a
                 JOIN training_quizzes q ON a.quiz_id = q.id
                 JOIN training_classes c ON q.class_id = c.id
                 JOIN training_submissions t ON c.training_id = t.id
                 JOIN users u ON a.user_id = u.id
                 WHERE a.id = ?";
$stmt = $conn->prepare($attempt_query);
$stmt->bind_param("i", $attempt_id);
$stmt->execute();
$result = $stmt->get_result();
$attempt = $result->fetch_assoc();
$stmt->close();

if (!$attempt) {
    header('Location: manage_classes.php');
    exit();
}

// Get answers for this attempt
$answers_query = "SELECT a.*, q.question_text, q.question_type, q.points
                 FROM training_quiz_answers a
                 JOIN training_questions q ON a.question_id = q.id
                 WHERE a.attempt_id = ?
                 ORDER BY q.order_number ASC";
$stmt = $conn->prepare($answers_query);
$stmt->bind_param("i", $attempt_id);
$stmt->execute();
$answers_result = $stmt->get_result();
$answers = [];
while ($row = $answers_result->fetch_assoc()) {
    // For multiple choice questions, get selected option
    if ($row['question_type'] == 'multiple_choice' || $row['question_type'] == 'true_false') {
        if (!empty($row['selected_option_id'])) {
            $option_query = "SELECT * FROM training_question_options WHERE id = ?";
            $option_stmt = $conn->prepare($option_query);
            $option_stmt->bind_param("i", $row['selected_option_id']);
            $option_stmt->execute();
            $option_result = $option_stmt->get_result();
            $row['selected_option'] = $option_result->fetch_assoc();
            $option_stmt->close();
        }

        // Get all options for this question
        $options_query = "SELECT * FROM training_question_options WHERE question_id = ? ORDER BY order_number ASC";
        $options_stmt = $conn->prepare($options_query);
        $options_stmt->bind_param("i", $row['question_id']);
        $options_stmt->execute();
        $options_result = $options_stmt->get_result();
        $row['options'] = [];
        while ($option = $options_result->fetch_assoc()) {
            $row['options'][] = $option;
        }
        $options_stmt->close();
    }

    $answers[] = $row;
}
$stmt->close();

// Calculate statistics
$total_questions = count($answers);
$correct_answers = 0;
$total_points = 0;
$earned_points = 0;

foreach ($answers as $answer) {
    $total_points += $answer['points'];
    if (isset($answer['is_correct']) && $answer['is_correct']) {
        $correct_answers++;
    }
    if (isset($answer['points_earned'])) {
        $earned_points += $answer['points_earned'];
    }
}

$score_percentage = ($total_points > 0) ? round(($earned_points / $total_points) * 100) : 0;
$passed = ($attempt['passing_score'] && $score_percentage >= $attempt['passing_score']);
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .attempt-header {
        background: linear-gradient(135deg, #4e73df, #224abe);
        color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .attempt-title {
        font-size: 1.8rem;
        margin-bottom: 10px;
    }

    .attempt-meta {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 15px;
    }

    .score-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }

    .score-value {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .score-high {
        color: #28a745;
    }

    .score-medium {
        color: #ffc107;
    }

    .score-low {
        color: #dc3545;
    }

    .score-label {
        margin-bottom: 15px;
    }

    .score-details {
        display: flex;
        justify-content: center;
        gap: 30px;
        margin-top: 15px;
    }

    .score-detail {
        text-align: center;
    }

    .detail-value {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .detail-label {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .answer-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .answer-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
    }

    .question-number {
        font-weight: 600;
        color: #495057;
    }

    .answer-points {
        font-weight: 600;
    }

    .points-earned {
        color: #28a745;
    }

    .points-lost {
        color: #dc3545;
    }

    .question-text {
        font-size: 1.1rem;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .answer-content {
        margin-bottom: 15px;
    }

    .selected-answer {
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    .correct-answer {
        background-color: #d4edda;
        border-left: 4px solid #28a745;
    }

    .incorrect-answer {
        background-color: #f8d7da;
        border-left: 4px solid #dc3545;
    }

    .text-answer {
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    .all-options {
        margin-top: 15px;
    }

    .option-item {
        padding: 8px 12px;
        margin-bottom: 8px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    }

    .option-correct {
        background-color: #d4edda;
        border-color: #28a745;
    }

    .option-selected {
        border-color: #007bff;
        border-width: 2px;
    }

    .feedback {
        margin-top: 15px;
        padding: 10px;
        border-radius: 4px;
        background-color: #f8f9fa;
        font-style: italic;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h1><i class="fas fa-clipboard-check"></i> Detail Hasil Kuis</h1>
                    <p class="text-muted">Lihat detail jawaban peserta</p>
                </div>
                <div>
                    <a href="quiz_results.php?id=<?= $attempt['quiz_id'] ?>" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Kembali ke Hasil Kuis
                    </a>
                </div>
            </div>

            <div class="attempt-header">
                <h2 class="attempt-title"><?= htmlspecialchars($attempt['quiz_title']) ?></h2>
                <div class="attempt-meta">
                    <div><strong>Kelas:</strong> <?= htmlspecialchars($attempt['class_title']) ?></div>
                    <div><strong>Topik:</strong> <?= htmlspecialchars($attempt['training_topic']) ?></div>
                    <div><strong>Peserta:</strong> <?= htmlspecialchars($attempt['name']) ?></div>
                    <?php if (!empty($attempt['nik'])): ?>
                        <div><strong>NIK:</strong> <?= htmlspecialchars($attempt['nik']) ?></div>
                    <?php endif; ?>
                    <div><strong>Waktu Pengerjaan:</strong> <?= date('d M Y H:i', strtotime($attempt['start_time'])) ?></div>
                    <div><strong>Waktu Selesai:</strong> <?= date('d M Y H:i', strtotime($attempt['end_time'])) ?></div>
                    <div><strong>Durasi:</strong>
                        <?php
                            $start = new DateTime($attempt['start_time']);
                            $end = new DateTime($attempt['end_time']);
                            $interval = $start->diff($end);

                            if ($interval->h > 0) {
                                echo $interval->format('%h jam %i menit %s detik');
                            } else {
                                echo $interval->format('%i menit %s detik');
                            }
                        ?>
                    </div>
                </div>
            </div>

            <div class="score-card">
                <div class="score-value <?= $score_percentage >= 80 ? 'score-high' : ($score_percentage >= 60 ? 'score-medium' : 'score-low') ?>">
                    <?= $score_percentage ?>%
                </div>
                <div class="score-label">
                    <?php if ($attempt['passing_score']): ?>
                        <?php if ($passed): ?>
                            <span class="badge bg-success">Lulus</span> Nilai kelulusan: <?= $attempt['passing_score'] ?>%
                        <?php else: ?>
                            <span class="badge bg-danger">Tidak Lulus</span> Nilai kelulusan: <?= $attempt['passing_score'] ?>%
                        <?php endif; ?>
                    <?php else: ?>
                        Nilai Peserta
                    <?php endif; ?>
                </div>

                <div class="score-details">
                    <div class="score-detail">
                        <div class="detail-value"><?= $correct_answers ?>/<?= $total_questions ?></div>
                        <div class="detail-label">Jawaban Benar</div>
                    </div>
                    <div class="score-detail">
                        <div class="detail-value"><?= $earned_points ?>/<?= $total_points ?></div>
                        <div class="detail-label">Poin</div>
                    </div>
                </div>
            </div>

            <h2>Detail Jawaban</h2>

            <?php foreach ($answers as $index => $answer): ?>
                <div class="answer-card">
                    <div class="answer-header">
                        <div class="question-number">Pertanyaan <?= $index + 1 ?></div>
                        <div class="answer-points">
                            <?php if (isset($answer['points_earned'])): ?>
                                <span class="<?= $answer['points_earned'] > 0 ? 'points-earned' : 'points-lost' ?>">
                                    <?= $answer['points_earned'] ?>/<?= $answer['points'] ?> poin
                                </span>
                            <?php else: ?>
                                <?= $answer['points'] ?> poin
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="question-text"><?= htmlspecialchars($answer['question_text']) ?></div>

                    <div class="answer-content">
                        <?php if ($answer['question_type'] == 'multiple_choice' || $answer['question_type'] == 'true_false'): ?>
                            <?php if (isset($answer['selected_option'])): ?>
                                <div class="selected-answer <?= $answer['is_correct'] ? 'correct-answer' : 'incorrect-answer' ?>">
                                    <strong>Jawaban Peserta:</strong> <?= htmlspecialchars($answer['selected_option']['option_text']) ?>
                                    <?php if ($answer['is_correct']): ?>
                                        <i class="fas fa-check-circle text-success ms-2"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times-circle text-danger ms-2"></i>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="selected-answer incorrect-answer">
                                    <strong>Jawaban Peserta:</strong> <em>Tidak menjawab</em>
                                    <i class="fas fa-times-circle text-danger ms-2"></i>
                                </div>
                            <?php endif; ?>

                            <div class="all-options">
                                <strong>Semua Pilihan:</strong>
                                <?php foreach ($answer['options'] as $option): ?>
                                    <div class="option-item <?= $option['is_correct'] ? 'option-correct' : '' ?> <?= (isset($answer['selected_option']) && $answer['selected_option']['id'] == $option['id']) ? 'option-selected' : '' ?>">
                                        <?= htmlspecialchars($option['option_text']) ?>
                                        <?php if ($option['is_correct']): ?>
                                            <i class="fas fa-check-circle text-success float-end"></i>
                                        <?php endif; ?>
                                        <?php if (isset($answer['selected_option']) && $answer['selected_option']['id'] == $option['id'] && !$option['is_correct']): ?>
                                            <i class="fas fa-times-circle text-danger float-end"></i>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php elseif ($answer['question_type'] == 'short_answer' || $answer['question_type'] == 'essay'): ?>
                            <div class="text-answer">
                                <strong>Jawaban Peserta:</strong><br>
                                <?= !empty($answer['text_answer']) ? nl2br(htmlspecialchars($answer['text_answer'])) : '<em>Tidak menjawab</em>' ?>
                            </div>

                            <?php if ($answer['question_type'] == 'short_answer' && $answer['is_correct']): ?>
                                <div class="text-answer option-correct">
                                    <strong>Jawaban Benar</strong><br>
                                    <i class="fas fa-check-circle text-success"></i> Jawaban dianggap benar
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
