<?php
if (!function_exists('checkAdminRole')) {
    function checkAdminRole() {
        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            header('Location: ../view/login.php');
            exit();
        }

        // Check if user has admin role (role_id = 99)
        if ($_SESSION['role_id'] != 99) {
            header('Location: ../view/login.php');
            exit();
        }
    }
}