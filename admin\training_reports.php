<?php
/**
 * <PERSON><PERSON> laporan training untuk admin
 */

include '../config/config.php';
include 'security.php';

// Get filter parameters
$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-t');
$training_type = $_GET['training_type'] ?? 'all';

// Get statistics
$stats = [];

// Total trainings
$query = "SELECT COUNT(*) as count FROM offline_training WHERE start_date BETWEEN ? AND ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$stats['offline_total'] = $stmt->get_result()->fetch_assoc()['count'];
$stmt->close();

$query = "SELECT COUNT(*) as count FROM training_submissions
          WHERE start_date BETWEEN ? AND ?
          AND status = 'Approved'";
$stmt = $conn->prepare($query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$stats['online_total'] = $stmt->get_result()->fetch_assoc()['count'];
$stmt->close();

// Participants count
$query = "SELECT COUNT(DISTINCT oa.nik) as count FROM offline_attendance oa
          JOIN offline_training ot ON oa.offline_training_id = ot.id
          WHERE ot.start_date BETWEEN ? AND ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$stats['offline_participants'] = $stmt->get_result()->fetch_assoc()['count'];
$stmt->close();

$query = "SELECT COUNT(DISTINCT ts.user_id) as count FROM training_submissions ts
          WHERE ts.start_date BETWEEN ? AND ?
          AND ts.status = 'Approved'";
$stmt = $conn->prepare($query);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$stats['online_participants'] = $stmt->get_result()->fetch_assoc()['count'];
$stmt->close();

// Get detailed training data
$trainings = [];
if ($training_type === 'all' || $training_type === 'offline') {
    $query = "SELECT
                'Offline' as jenis,
                ot.training_topic as judul,
                ot.start_date as tanggal,
                ot.location as lokasi,
                ot.trainer_name as trainer,
                ot.status,
                COUNT(oa.id) as jumlah_peserta
              FROM offline_training ot
              LEFT JOIN offline_attendance oa ON ot.id = oa.offline_training_id
              WHERE ot.start_date BETWEEN ? AND ?
              GROUP BY ot.id
              ORDER BY ot.start_date DESC";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $trainings[] = $row;
    }
    $stmt->close();
}

if ($training_type === 'all' || $training_type === 'online') {
    $query = "SELECT
                'Online' as jenis,
                ts.training_topic as judul,
                ts.start_date as tanggal,
                COALESCE(ts.training_place, 'Online') as lokasi,
                ts.contact_person as trainer,
                ts.status,
                1 as jumlah_peserta
              FROM training_submissions ts
              WHERE ts.start_date BETWEEN ? AND ?
              AND ts.status = 'Approved'
              ORDER BY ts.start_date DESC";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $trainings[] = $row;
    }
    $stmt->close();
}

// Sort by date
usort($trainings, function($a, $b) {
    return strcmp($b['tanggal'], $a['tanggal']);
});
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laporan Training</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #9d0000;
            --primary-dark: #7f0000;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }

        .stat-card {
            text-align: center;
            padding: 2rem 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-bar me-2"></i>
                Laporan Training
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="calendar_management.php">
                    <i class="fas fa-arrow-left me-1"></i>Kembali ke Kalender
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Laporan</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="start_date" class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="end_date" class="form-label">Tanggal Selesai</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="training_type" class="form-label">Jenis Training</label>
                                <select class="form-control" id="training_type" name="training_type">
                                    <option value="all" <?= $training_type === 'all' ? 'selected' : '' ?>>Semua</option>
                                    <option value="offline" <?= $training_type === 'offline' ? 'selected' : '' ?>>Offline</option>
                                    <option value="online" <?= $training_type === 'online' ? 'selected' : '' ?>>Online</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                    <a href="api/export_calendar.php?start=<?= $start_date ?>&end=<?= $end_date ?>" class="btn btn-success">
                                        <i class="fas fa-download me-1"></i>Export Excel
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="stat-number text-success"><?= $stats['offline_total'] ?></div>
                    <div class="stat-label">Training Internal</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="stat-number text-info"><?= $stats['online_total'] ?></div>
                    <div class="stat-label">Training Eksternal</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="stat-number text-warning"><?= $stats['offline_participants'] ?></div>
                    <div class="stat-label">Peserta Offline</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="stat-number text-primary"><?= $stats['online_participants'] ?></div>
                    <div class="stat-label">Peserta Online</div>
                </div>
            </div>
        </div>

        <!-- Training List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            Daftar Training (<?= count($trainings) ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($trainings)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-calendar-times fa-3x mb-3"></i>
                                <p>Tidak ada training dalam periode yang dipilih</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>No</th>
                                            <th>Jenis</th>
                                            <th>Judul Training</th>
                                            <th>Tanggal</th>
                                            <th>Lokasi</th>
                                            <th>Trainer</th>
                                            <th>Peserta</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $no = 1; foreach ($trainings as $training): ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td>
                                                    <span class="badge bg-<?= $training['jenis'] === 'Offline' ? 'success' : 'info' ?>">
                                                        <?= $training['jenis'] ?>
                                                    </span>
                                                </td>
                                                <td><?= htmlspecialchars($training['judul']) ?></td>
                                                <td><?= date('d M Y', strtotime($training['tanggal'])) ?></td>
                                                <td><?= htmlspecialchars($training['lokasi']) ?></td>
                                                <td><?= htmlspecialchars($training['trainer'] ?: '-') ?></td>
                                                <td>
                                                    <span class="badge bg-secondary"><?= $training['jumlah_peserta'] ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?= $training['status'] === 'Active' || $training['status'] === 'Approved' ? 'success' : 'secondary' ?>">
                                                        <?= htmlspecialchars($training['status']) ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
$conn->close();
?>
