<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

$error = "";
$success_message = "";
$results = [];

// Proses form submit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Cek apakah ada data yang dikirim
    if (isset($_POST['selected_employees']) && !empty($_POST['selected_employees'])) {
        $selected_employees = json_decode($_POST['selected_employees'], true);
        $role_id = $_POST['role_id'];

        if (empty($role_id)) {
            $error = "Error: <PERSON><PERSON> pilih role untuk semua karyawan.";
        } else {
            // Mulai transaksi database
            $conn->begin_transaction();

            $success_count = 0;
            $error_count = 0;

            foreach ($selected_employees as $employee) {
                $name = $employee['nama'];
                $nik = $employee['nik'];
                $dept = $employee['dept'];
                $bagian = $employee['bagian'];
                $jabatan = $employee['jabatan'];

                // Generate email
                $cleanName = preg_replace('/[^a-zA-Z0-9]/', '', strtolower($name));
                $email = $cleanName . "@example.com";

                // Cek apakah email sudah ada
                $checkEmailQuery = "SELECT COUNT(*) as count FROM users WHERE email = ?";
                $stmtCheckEmail = $conn->prepare($checkEmailQuery);
                $stmtCheckEmail->bind_param("s", $email);
                $stmtCheckEmail->execute();
                $emailResult = $stmtCheckEmail->get_result()->fetch_assoc();

                if ($emailResult['count'] > 0) {
                    // Jika email sudah ada, tambahkan 4 digit terakhir NIK
                    $uniqueIdentifier = preg_replace('/[^0-9]/', '', $nik);
                    $uniqueIdentifier = substr($uniqueIdentifier, -4);
                    $email = $cleanName . $uniqueIdentifier . "@example.com";
                }

                // Cek apakah email, NIK, atau nama sudah digunakan
                $checkQuery = "SELECT * FROM users WHERE email = ? OR nik = ? OR name = ?";
                $stmtCheck = $conn->prepare($checkQuery);
                $stmtCheck->bind_param("sss", $email, $nik, $name);
                $stmtCheck->execute();
                $resultCheck = $stmtCheck->get_result();

                if ($resultCheck->num_rows > 0) {
                    $row = $resultCheck->fetch_assoc();
                    $error_message = "";

                    if ($row['email'] == $email) {
                        $error_message = "Email sudah digunakan";
                    } elseif ($row['nik'] == $nik) {
                        $error_message = "NIK sudah digunakan";
                    } elseif ($row['name'] == $name) {
                        $error_message = "Nama sudah digunakan";
                    }

                    $results[] = [
                        'status' => 'error',
                        'message' => $error_message,
                        'data' => [
                            'name' => $name,
                            'nik' => $nik,
                            'email' => $email,
                            'dept' => $dept,
                            'bagian' => $bagian,
                            'jabatan' => $jabatan
                        ]
                    ];
                    $error_count++;
                } else {
                    // Generate verification code
                    $verification_code = sprintf("%06d", mt_rand(100000, 999999));
                    $verification_expires = date('Y-m-d H:i:s', strtotime('+15 minutes'));

                    // Jika tidak ada duplikasi, tambahkan pengguna baru
                    $query = "INSERT INTO users (name, email, nik, role_id, dept, bagian, jabatan, is_active, verification_code, verification_expires)
                              VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?)";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("sssisssss", $name, $email, $nik, $role_id, $dept, $bagian, $jabatan, $verification_code, $verification_expires);

                    if ($stmt->execute()) {
                        $user_id = $conn->insert_id;

                        // Tambahkan departemen default
                        $query = "INSERT INTO user_departments (user_id, dept) VALUES (?, ?)";
                        $stmt = $conn->prepare($query);
                        $stmt->bind_param("is", $user_id, $dept);
                        $stmt->execute();

                        // Kirim email aktivasi
                        include_once '../config/mail.php';

                        // Ambil konfigurasi email dari database
                        $settingsQuery = "SELECT smtp_server, smtp_port, smtp_password, sender_email, sender_name, smtp_encryption FROM settings WHERE id = 1";
                        $settingsResult = $conn->query($settingsQuery);

                        if ($settingsResult && $settingsResult->num_rows > 0) {
                            $settings = $settingsResult->fetch_assoc();

                            // Kirim email aktivasi
                            $activation_link = BASE_URL . "view/Aktivasi.php";

                            $mail_result = send_mail(
                                $email,
                                'Aktivasi Akun Training Center',
                                "
                                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 2px solid #BF0000; border-radius: 5px; background-color: #f9f9f9;'>
                                    <h2 style='color: #BF0000; text-align: center;'>Aktivasi Akun Training Center</h2>
                                    <p style='color: #333; font-size: 16px;'>Halo <strong>{$name}</strong>,</p>
                                    <p style='color: #333; font-size: 16px;'>Akun Anda telah dibuat di Training Center. Untuk mengaktifkan akun Anda, silakan gunakan informasi berikut:</p>
                                    <div style='background-color: #ffffff; border: 2px solid #BF0000; border-radius: 5px; padding: 15px; margin: 20px 0;'>
                                        <p style='margin: 5px 0;'><strong>NIK:</strong> {$nik}</p>
                                        <p style='margin: 5px 0;'><strong>Departemen:</strong> {$dept}</p>
                                        <p style='margin: 5px 0;'><strong>Kode Verifikasi:</strong> <span style='font-size: 18px; font-weight: bold; color: #BF0000;'>{$verification_code}</span></p>
                                    </div>
                                    <p style='color: #333; font-size: 16px;'>Untuk mengaktifkan akun Anda, kunjungi <a href='{$activation_link}' style='color: #BF0000; text-decoration: none; font-weight: bold;'>halaman aktivasi</a> dan masukkan NIK, Departemen, dan Kode Verifikasi Anda.</p>
                                    <p style='color: #333; font-size: 14px;'>Kode verifikasi ini akan kadaluarsa dalam <strong>15 menit</strong>.</p>
                                    <p style='color: #333; font-size: 14px;'>Jika Anda tidak meminta pembuatan akun ini, abaikan email ini.</p>
                                    <div style='text-align: center; margin-top: 30px;'>
                                        <p style='color: #333; font-size: 12px;'>Email ini dikirim secara otomatis. Mohon jangan membalas email ini.</p>
                                    </div>
                                </div>
                                ",
                                $settings
                            );
                        }

                        // Log aktivitas
                        if (file_exists('../config/activity_logger.php')) {
                            include_once '../config/activity_logger.php';
                            if (function_exists('log_activity')) {
                                $role_names = [
                                    1 => 'Pemohon',
                                    2 => 'Dept Head',
                                    3 => 'LnD',
                                    4 => 'FM',
                                    5 => 'Direktur',
                                    6 => 'HRGA',
                                    99 => 'Admin'
                                ];
                                $role_name = $role_names[$role_id] ?? 'Unknown';

                                log_activity($_SESSION['user_id'], "Menambahkan pengguna baru (bulk select): {$name} (NIK: {$nik}) dengan role {$role_name}", "user", [
                                    'user_id' => $user_id,
                                    'name' => $name,
                                    'nik' => $nik,
                                    'role_id' => $role_id,
                                    'dept' => $dept,
                                    'bagian' => $bagian,
                                    'jabatan' => $jabatan
                                ]);
                            }
                        }

                        $results[] = [
                            'status' => 'success',
                            'message' => 'Berhasil ditambahkan',
                            'data' => [
                                'name' => $name,
                                'nik' => $nik,
                                'email' => $email,
                                'dept' => $dept,
                                'bagian' => $bagian,
                                'jabatan' => $jabatan
                            ]
                        ];
                        $success_count++;
                    } else {
                        $results[] = [
                            'status' => 'error',
                            'message' => "Gagal menambahkan: " . $stmt->error,
                            'data' => [
                                'name' => $name,
                                'nik' => $nik,
                                'email' => $email,
                                'dept' => $dept,
                                'bagian' => $bagian,
                                'jabatan' => $jabatan
                            ]
                        ];
                        $error_count++;
                    }
                }
            }

            // Commit transaksi jika berhasil
            $conn->commit();

            $success_message = "Proses selesai. Berhasil: $success_count, Gagal: $error_count";
        }
    } else {
        $error = "Error: Tidak ada karyawan yang dipilih.";
    }
}

// Query untuk mengambil daftar role
$query = "SELECT * FROM roles";
$result = $conn->query($query);
$roles = [];
while ($row = $result->fetch_assoc()) {
    $roles[$row['id']] = $row['role_name'];
}
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<!-- Tambahkan file JavaScript untuk memperbaiki masalah -->
<script src="js/CustomModal.js"></script>
<script src="js/bulk_select_users_fix.js"></script>
<style>
    html, body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 0;
        color: #333;
    }
    
    /* Loading Overlay Styles */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        display: none;
    }
    
    .loading-content {
        background-color: white;
        padding: 30px;
        border-radius: 10px;
        text-align: center;
        width: 80%;
        max-width: 500px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }
    
    .progress-container {
        margin: 20px 0;
        background-color: #f1f1f1;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .progress-bar {
        height: 20px;
        background-color: #BF0000;
        width: 0%;
        border-radius: 10px;
        transition: width 0.3s ease;
    }
    
    .progress-text {
        margin-top: 10px;
        font-weight: bold;
        color: #333;
    }
    
    .progress-details {
        margin-top: 15px;
        font-size: 14px;
        color: #666;
    }

    body {
        display: flex;
        flex-direction: column;
    }

    .content-wrapper {
        flex: 1;
        width: 100%;
        display: flex;
        flex-direction: column;
    }

    .container-form {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 80px 20px 40px 20px; /* Increased top padding to prevent navbar overlap */
        width: 100%;
    }

    .form-container {
        background: #fff;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        width: 100%;
        max-width: 800px;
        transition: all 0.3s ease;
    }

    .form-container:hover {
        box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
    }

    h2 {
        text-align: center;
        color: #BF0000;
        margin-bottom: 25px;
        font-weight: 600;
        position: relative;
        padding-bottom: 10px;
    }

    h2:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background-color: #BF0000;
        border-radius: 3px;
    }

    h3 {
        color: #BF0000;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .form-group {
        margin-bottom: 20px;
    }

    label {
        display: block;
        margin: 12px 0 8px;
        font-weight: 500;
        color: #444;
        font-size: 15px;
    }

    input, select {
        width: 100%;
        padding: 12px 15px;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-sizing: border-box;
        font-size: 15px;
        transition: all 0.3s ease;
        background-color: #f9f9f9;
    }

    input:focus, select:focus {
        outline: none;
        border-color: #BF0000;
        box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
        background-color: #fff;
    }

    button {
        width: 100%;
        padding: 14px;
        background-color: #BF0000;
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-top: 10px;
        box-shadow: 0 4px 6px rgba(191, 0, 0, 0.1);
    }

    button:hover {
        background-color: #a00000;
        transform: translateY(-2px);
        box-shadow: 0 6px 8px rgba(191, 0, 0, 0.15);
    }

    button:active {
        transform: translateY(0);
    }

    .success-message {
        color: #2e7d32;
        font-weight: 500;
        text-align: center;
        margin-bottom: 20px;
        padding: 10px;
        background-color: #e8f5e9;
        border-radius: 8px;
        border-left: 4px solid #2e7d32;
    }

    .error {
        color: #d32f2f;
        font-weight: 500;
        text-align: center;
        margin-bottom: 20px;
        padding: 10px;
        background-color: #ffebee;
        border-radius: 8px;
        border-left: 4px solid #d32f2f;
    }

    .back-link {
        display: block;
        text-align: center;
        margin-top: 20px;
        color: #BF0000;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .back-link:hover {
        color: #a00000;
        text-decoration: underline;
    }

    .info-text {
        display: block;
        font-size: 13px;
        color: #666;
        margin-top: 5px;
        font-style: italic;
    }

    .search-container {
        margin-bottom: 25px;
        position: relative;
    }

    .search-input {
        width: 100%;
        padding: 12px 15px;
        padding-left: 40px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s ease;
        background-color: #f9f9f9;
    }

    .search-input:focus {
        outline: none;
        border-color: #BF0000;
        box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
        background-color: #fff;
    }

    .search-icon {
        position: absolute;
        left: 15px;
        top: 14px;
        color: #777;
    }

    .employee-results {
        max-height: 250px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 20px;
        display: none;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        scrollbar-width: thin;
        scrollbar-color: #BF0000 #f1f1f1;
        z-index: 100;
        background-color: white;
    }

    .employee-results::-webkit-scrollbar {
        width: 8px;
    }

    .employee-results::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 8px;
    }

    .employee-results::-webkit-scrollbar-thumb {
        background-color: #BF0000;
        border-radius: 8px;
    }

    .employee-result {
        padding: 12px 15px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
        transition: all 0.2s ease;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .employee-result:last-child {
        border-bottom: none;
    }

    .employee-result:hover {
        background-color: #f5f5f5;
    }

    .selected-employees {
        background-color: #f8f0f0;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 25px;
        border-left: 4px solid #BF0000;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        line-height: 1.6;
    }

    .selected-employee-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }

    .selected-employee-item:last-child {
        border-bottom: none;
    }

    .remove-employee {
        background: #d32f2f;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 4px 8px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;
        width: auto;
        margin: 0;
        box-shadow: none;
    }

    .remove-employee:hover {
        background: #b71c1c;
        transform: none;
        box-shadow: none;
    }

    .add-employee {
        background: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 4px 8px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;
        width: auto;
        margin: 0;
        box-shadow: none;
    }

    .add-employee:hover {
        background: #388E3C;
    }

    /* Mobile responsive styles for search and selected employees */
    @media screen and (max-width: 768px) {
        .search-input {
            padding: 10px 12px 10px 36px;
            font-size: 14px;
        }

        .search-icon {
            left: 12px;
            top: 12px;
            font-size: 14px;
        }

        .employee-result {
            padding: 10px 12px;
            font-size: 14px;
            flex-wrap: wrap;
        }

        .employee-result button {
            margin-top: 5px;
            width: 100%;
            padding: 6px;
        }

        .selected-employee-item {
            flex-direction: column;
            align-items: flex-start;
            padding: 10px 0;
        }

        .selected-employee-item button {
            margin-top: 8px;
            align-self: flex-end;
        }

        .employee-result {
            flex-direction: column;
            align-items: flex-start;
        }

        .employee-result div {
            margin-bottom: 8px;
            width: 100%;
        }

        .employee-result button {
            width: 100%;
            text-align: center;
        }
    }

    .results-container {
        margin-top: 30px;
        border-top: 1px solid #ddd;
        padding-top: 20px;
        overflow-x: auto;
    }

    .results-container table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-radius: 8px;
        overflow: hidden;
    }

    .results-container th, .results-container td {
        border: 1px solid #eee;
        padding: 12px 15px;
        text-align: left;
    }

    .results-container th {
        background-color: #BF0000;
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85em;
        letter-spacing: 0.5px;
    }

    .results-container tr {
        transition: all 0.2s ease;
    }

    .results-container tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .results-container tr:hover {
        background-color: #f1f1f1;
    }

    .results-container tr.success {
        background-color: #e8f5e9;
    }

    .results-container tr.error {
        background-color: #ffebee;
    }

    .results-container tr.success:hover {
        background-color: #d5ecd7;
    }

    .results-container tr.error:hover {
        background-color: #f8d7da;
    }

    /* Status badges */
    .status-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.85em;
        font-weight: 600;
    }

    .status-success {
        background-color: #e8f5e9;
        color: #2e7d32;
        border: 1px solid #c8e6c9;
    }

    .status-error {
        background-color: #ffebee;
        color: #c62828;
        border: 1px solid #ffcdd2;
    }

    /* Styles for Select All and Clear All buttons */
    .btn-secondary {
        padding: 10px 15px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-secondary i {
        margin-right: 8px;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .btn-secondary:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    #loadingIndicator {
        padding: 10px;
        border-radius: 5px;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        font-weight: 500;
        color: #555;
    }

    #loadingIndicator i {
        color: #BF0000;
        margin-right: 8px;
    }

    /* Jarak untuk navbar */
    .jarak {
        height: 100px;
    }

    /* Mobile responsive styles */
    @media screen and (max-width: 768px) {
        .container-form {
            padding: 120px 10px 20px 10px;
        }

        .form-container {
            padding: 20px 15px;
            border-radius: 8px;
        }

        .results-container th, .results-container td {
            padding: 8px 10px;
            font-size: 0.9em;
        }

        /* Stack table on mobile */
        .results-container table,
        .results-container thead,
        .results-container tbody,
        .results-container th,
        .results-container td,
        .results-container tr {
            display: block;
        }

        .results-container thead tr {
            position: absolute;
            top: -9999px;
            left: -9999px;
        }

        .results-container tr {
            border: 1px solid #ddd;
            margin-bottom: 15px;
            border-radius: 8px;
            overflow: hidden;
        }

        .results-container td {
            border: none;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 50%;
            text-align: right;
        }

        .results-container td:last-child {
            border-bottom: 0;
        }

        .results-container td:before {
            position: absolute;
            top: 8px;
            left: 10px;
            width: 45%;
            padding-right: 10px;
            white-space: nowrap;
            font-weight: 600;
            text-align: left;
        }

        /* Add labels for each td */
        .results-container td:nth-of-type(1):before { content: "Nama"; }
        .results-container td:nth-of-type(2):before { content: "NIK"; }
        .results-container td:nth-of-type(3):before { content: "Email"; }
        .results-container td:nth-of-type(4):before { content: "Role"; }
        .results-container td:nth-of-type(5):before { content: "Status"; }
        .results-container td:nth-of-type(6):before { content: "Pesan"; }
    }
</style>
<body>
<?php include '../config/navbarb.php'; ?>
<div class="jarak"></div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay">
    <div class="loading-content">
        <h3 style="color: #BF0000;">Membuat Akun Pengguna</h3>
        <div class="progress-container">
            <div id="progressBar" class="progress-bar"></div>
        </div>
        <div id="progressText" class="progress-text">0%</div>
        <div id="progressDetails" class="progress-details">Memproses: 0 dari 0 karyawan</div>
    </div>
</div>

<div class="content-wrapper">
    <div class="container-form">
        <div class="form-container">
            <h2>Bulk Create Users</h2>

            <?php if (!empty($success_message)) { ?>
                <div class="success-message">
                    <p><?php echo $success_message; ?></p>
                </div>
            <?php } ?>

            <?php if (!empty($error)) { ?>
                <div class="error">
                    <p><?php echo $error; ?></p>
                </div>
            <?php } ?>

            <div class="search-container">
                <input type="text"
                       id="searchEmployee"
                       class="search-input"
                       placeholder="Cari karyawan berdasarkan nama atau NIK...">
                <div id="employeeResults" class="employee-results"></div>
                <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                    <button type="button" id="selectAllButton" class="btn-secondary" style="width: 48%; background-color: #4CAF50; color: white;" title="Hanya memilih karyawan yang belum memiliki akun">
                        <i class="fas fa-check-square"></i> Pilih Semua Karyawan
                        <small style="display: block; font-size: 10px; margin-top: 3px;">(yang belum memiliki akun)</small>
                    </button>
                    <button type="button" id="clearAllButton" class="btn-secondary" style="width: 48%; background-color: #f44336; color: white;">
                        <i class="fas fa-trash"></i> Hapus Semua Pilihan
                    </button>
                </div>
                <div id="loadingIndicator" style="display: none; text-align: center; margin-top: 10px;">
                    <i class="fas fa-spinner fa-spin"></i> Memuat semua karyawan...
                </div>
            </div>

            <div id="selectedEmployees" class="selected-employees" style="display: none;">
    <h3>Karyawan Terpilih</h3>
    
    <!-- Tombol Toggle -->
    <button id="toggleSelectedListBtn" style="margin-bottom: 10px;">Tampilkan Karyawan</button>

    <!-- Container untuk Wrap / Unwrap -->
    <div id="selectedEmployeesContainer">
        <div id="selectedEmployeesList"></div>
    </div>

    <p id="selectedCount" style="margin-top: 10px; font-weight: bold;"></p>
</div>

            <form method="POST" action="bulk_select_users.php" id="bulkCreateForm" onsubmit="return validateForm()">
                <input type="hidden" id="selectedEmployeesInput" name="selected_employees" value="">

                <div class="form-group">
                    <label for="role_id">Role untuk Semua Karyawan</label>
                    <select name="role_id" id="role_id" required>
                        <option value="">Pilih Role</option>
                        <?php foreach ($roles as $id => $name) { ?>
                            <option value="<?php echo $id; ?>"><?php echo $name; ?></option>
                        <?php } ?>
                    </select>
                    <small class="info-text">Role yang dipilih akan diterapkan ke semua karyawan yang dipilih</small>
                </div>

                <button type="submit" id="submitButton" disabled>Create Users</button>
            </form>

            <?php if (!empty($results)) { ?>
                <div class="results-container">
                    <h3>Hasil Proses</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Nama</th>
                                <th>NIK</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Pesan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $result) { ?>
                                <tr class="<?php echo $result['status']; ?>">
                                    <td><?php echo htmlspecialchars($result['data']['name']); ?></td>
                                    <td><?php echo htmlspecialchars($result['data']['nik']); ?></td>
                                    <td><?php echo htmlspecialchars($result['data']['email']); ?></td>
                                    <td><?php echo htmlspecialchars($roles[$_POST['role_id']] ?? 'Unknown'); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $result['status']; ?>">
                                            <?php if ($result['status'] == 'success'): ?>
                                                <i class="fas fa-check-circle"></i> Berhasil
                                            <?php else: ?>
                                                <i class="fas fa-times-circle"></i> Gagal
                                            <?php endif; ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($result['message']); ?></td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            <?php } ?>

            <a href="index.php" class="back-link">Back to List</a>
        </div>
    </div>
</div>
<?php include '../config/footer.php'; ?>
<script>
    document.addEventListener("DOMContentLoaded", function () {
    const toggleBtn = document.getElementById("toggleSelectedListBtn");
    const selectedEmployeesList = document.getElementById("selectedEmployeesList");
    const container = document.getElementById("selectedEmployeesContainer");

    // Status awal: disembunyikan
    let isVisible = true;

    // Sembunyikan list saat halaman dimuat
    selectedEmployeesList.style.display = "none"
    toggleBtn.textContent = "Tampilkan Karyawan";

    toggleBtn.addEventListener("click", function () {
        if (isVisible) {
            // Wrap: sembunyikan list
            selectedEmployeesList.style.display = "none";
            toggleBtn.textContent = "Tampilkan Karyawan";
        } else {
            // Unwrap: tampilkan list
            selectedEmployeesList.style.display = "block";
            toggleBtn.textContent = "Sembunyikan Karyawan";
        }
        isVisible = !isVisible;
    });
});
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchEmployee');
    const resultsDiv = document.getElementById('employeeResults');
    const selectedEmployeesDiv = document.getElementById('selectedEmployees');
    const selectedEmployeesList = document.getElementById('selectedEmployeesList');
    const selectedEmployeesInput = document.getElementById('selectedEmployeesInput');
    const selectedCountElement = document.getElementById('selectedCount');
    const submitButton = document.getElementById('submitButton');
    const selectAllButton = document.getElementById('selectAllButton');
    const clearAllButton = document.getElementById('clearAllButton');
    const loadingIndicator = document.getElementById('loadingIndicator');

    let searchTimeout;
    
    // Pastikan window.selectedEmployees selalu diinisialisasi sebagai array
    if (typeof window.selectedEmployees === 'undefined' || !Array.isArray(window.selectedEmployees)) {
        window.selectedEmployees = [];
    }
    
    // Gunakan variabel global untuk konsistensi dengan bulk_select_users_fix.js
    // let selectedEmployees = [];

    // Fungsi untuk memperbarui tampilan karyawan yang dipilih
    function updateSelectedEmployees() {
        if (window.selectedEmployees.length > 0) {
            selectedEmployeesDiv.style.display = 'block';
            selectedEmployeesList.innerHTML = '';

            window.selectedEmployees.forEach((employee, index) => {
                const div = document.createElement('div');
                div.className = 'selected-employee-item';
                div.innerHTML = `
                    <div>
                        <strong>${employee.nama}</strong> (${employee.nik})<br>
                        <small>${employee.dept} - ${employee.bagian} - ${employee.jabatan}</small>
                    </div>
                    <button type="button" class="remove-employee" data-index="${index}">
                        <i class="fas fa-times"></i> Hapus
                    </button>
                `;
                selectedEmployeesList.appendChild(div);
            });

            selectedCountElement.textContent = `Total: ${window.selectedEmployees.length} karyawan`;
            selectedEmployeesInput.value = JSON.stringify(window.selectedEmployees);
            submitButton.disabled = false;
        } else {
            selectedEmployeesDiv.style.display = 'none';
            selectedEmployeesInput.value = '';
            submitButton.disabled = true;
        }
    }

    // Event listener untuk tombol hapus karyawan
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-employee') || e.target.closest('.remove-employee')) {
            const button = e.target.classList.contains('remove-employee') ? e.target : e.target.closest('.remove-employee');
            const index = parseInt(button.getAttribute('data-index'));

            window.selectedEmployees.splice(index, 1);
            updateSelectedEmployees();
        }
    });

    // Event listener untuk pencarian karyawan
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length < 3) {
            resultsDiv.style.display = 'none';
            return;
        }

        searchTimeout = setTimeout(() => {
            fetch(`search_handler.php?search=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    resultsDiv.innerHTML = '';

                    // Tambahkan informasi tentang hasil pencarian
                    if (data.total_matches && data.existing_accounts) {
                        const infoDiv = document.createElement('div');
                        infoDiv.className = 'employee-result-info';
                        infoDiv.innerHTML = `
                            <div style="padding: 8px; background-color: #f8f9fa; border-bottom: 1px solid #ddd; font-size: 12px; color: #666;">
                                Menampilkan ${data.data ? data.data.length : 0} dari ${data.total_matches} karyawan yang cocok dengan pencarian.
                                <br>
                                <span style="color: #BF0000;">${data.existing_accounts} karyawan sudah memiliki akun dan tidak ditampilkan.</span>
                            </div>
                        `;
                        resultsDiv.appendChild(infoDiv);
                    }

                    if (data.data && data.data.length > 0) {
                        data.data.forEach(employee => {
                            // Cek apakah karyawan sudah dipilih
                            const isSelected = window.selectedEmployees.some(e => e.nik === employee.nik);

                            const div = document.createElement('div');
                            div.className = 'employee-result';
                            div.innerHTML = `
                                <div>${employee.nama} (${employee.nik})<br><small>${employee.dept} - ${employee.bagian} - ${employee.jabatan}</small></div>
                                <button type="button" class="${isSelected ? 'remove-employee' : 'add-employee'}">
                                    <i class="fas ${isSelected ? 'fa-times' : 'fa-plus'}"></i> ${isSelected ? 'Hapus' : 'Tambah'}
                                </button>
                            `;

                            div.querySelector('button').addEventListener('click', function(e) {
                                e.stopPropagation();

                                if (isSelected) {
                                    // Hapus karyawan dari daftar
                                    const index = window.selectedEmployees.findIndex(e => e.nik === employee.nik);
                                    if (index !== -1) {
                                        window.selectedEmployees.splice(index, 1);
                                    }
                                } else {
                                    // Tambahkan karyawan ke daftar
                                    window.selectedEmployees.push({
                                        nama: employee.nama,
                                        nik: employee.nik,
                                        dept: employee.dept,
                                        bagian: employee.bagian,
                                        jabatan: employee.jabatan
                                    });
                                }

                                updateSelectedEmployees();
                                resultsDiv.style.display = 'none';
                                searchInput.value = '';
                            });

                            resultsDiv.appendChild(div);
                        });

                        resultsDiv.style.display = 'block';
                    } else {
                        // Tampilkan pesan yang lebih informatif
                        let noResultsMessage = 'Tidak ada hasil ditemukan';

                        if (data.total_matches === 0) {
                            noResultsMessage = 'Tidak ada karyawan yang cocok dengan pencarian';
                        } else if (data.existing_accounts && data.existing_accounts > 0) {
                            noResultsMessage = `Semua karyawan (${data.existing_accounts}) yang cocok dengan pencarian sudah memiliki akun`;
                        }

                        resultsDiv.innerHTML += `<div class="employee-result">${noResultsMessage}</div>`;
                        resultsDiv.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    resultsDiv.innerHTML = '<div class="employee-result">Terjadi kesalahan saat mencari</div>';
                    resultsDiv.style.display = 'block';
                });
        }, 300);
    });

    // Validasi form sebelum submit
    window.validateForm = function() {
        if (window.selectedEmployees.length === 0) {
            CustomModal.warning('Harap pilih minimal satu karyawan.', 'Validasi');
            return false;
        }

        const roleSelect = document.getElementById('role_id');
        if (!roleSelect.value) {
            CustomModal.warning('Harap pilih role untuk semua karyawan.', 'Validasi');
            roleSelect.focus();
            return false;
        }
        
        // Tampilkan loading overlay dengan progress bar
        showLoadingProgress(window.selectedEmployees.length);
        
        // Submit form dengan AJAX
        submitFormWithProgress();
        
        // Mencegah form submit normal
        return false;
    };
    
    // Fungsi untuk menampilkan loading progress
    function showLoadingProgress(totalEmployees) {
        const loadingOverlay = document.getElementById('loadingOverlay');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const progressDetails = document.getElementById('progressDetails');
        
        // Reset progress
        progressBar.style.width = '0%';
        progressText.textContent = '0%';
        progressDetails.textContent = `Memproses: 0 dari ${totalEmployees} karyawan`;
        
        // Tampilkan overlay
        loadingOverlay.style.display = 'flex';
    }
    
    // Fungsi updateProgress telah dipindahkan ke file bulk_select_users_fix.js
    
    // Gunakan fungsi submitFormWithProgress dari file bulk_select_users_fix.js
    // Fungsi ini telah diperbaiki untuk menangani masalah play() request yang terinterupsi

    // Fungsi untuk memilih semua karyawan
    selectAllButton.addEventListener('click', function() {
        // Tampilkan loading indicator
        loadingIndicator.style.display = 'block';
        selectAllButton.disabled = true;

        // Ambil semua karyawan dari server dengan path yang benar
        fetch('get_all_employees.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data && data.data.length > 0) {
                    // Konfirmasi dengan user jika jumlah karyawan banyak
                    if (data.data.length > 100) {
                        // Gunakan CustomModal.confirm daripada confirm() yang telah dioverride
                        CustomModal.confirm(
                            `Anda akan memilih ${data.data.length} karyawan. Lanjutkan?`,
                            'Konfirmasi Pilih Semua',
                            {
                                onConfirm: function() {
                                    // Lanjutkan dengan pemilihan semua karyawan
                                    processAllEmployees(data.data);
                                },
                                onCancel: function() {
                                    // Batalkan operasi
                                    loadingIndicator.style.display = 'none';
                                    selectAllButton.disabled = false;
                                }
                            }
                        );
                    } else {
                        // Jika jumlah karyawan sedikit, langsung proses
                        processAllEmployees(data.data);
                    }

                    // Fungsi untuk memproses semua karyawan
                    function processAllEmployees(employees) {
                        // Reset daftar karyawan yang dipilih
                        window.selectedEmployees = [];

                        // Tambahkan semua karyawan ke daftar
                        employees.forEach(employee => {
                            window.selectedEmployees.push({
                                nama: employee.nama,
                                nik: employee.nik,
                                dept: employee.dept,
                                bagian: employee.bagian,
                                jabatan: employee.jabatan
                            });
                        });

                        // Update tampilan
                        updateSelectedEmployees();

                        // Tampilkan pesan sukses dengan informasi tambahan
                        let successMessage = `Berhasil memilih ${window.selectedEmployees.length} karyawan.`;

                        // Tambahkan informasi tentang total karyawan dan yang sudah memiliki akun
                        if (data.total_employees && data.existing_accounts) {
                            successMessage += `<br><br><small>Catatan: Hanya menampilkan karyawan yang belum memiliki akun (${data.count} dari total ${data.total_employees} karyawan).</small>`;
                        }

                        CustomModal.success(successMessage, 'Pilih Semua Karyawan');

                        // Sembunyikan loading indicator
                        loadingIndicator.style.display = 'none';
                        selectAllButton.disabled = false;
                    }
                } else {
                    CustomModal.info('Tidak ada karyawan yang ditemukan.', 'Informasi');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                CustomModal.warning('Terjadi kesalahan saat mengambil data karyawan: ' + error.message, 'Error');

                // Log error untuk debugging
                console.log('Fetch error details:', {
                    message: error.message,
                    stack: error.stack,
                    url: 'get_all_employees.php'
                });
            })
            .finally(() => {
                // Sembunyikan loading indicator
                loadingIndicator.style.display = 'none';
                selectAllButton.disabled = false;
            });
    });

    // Fungsi untuk menghapus semua pilihan
    clearAllButton.addEventListener('click', function() {
        if (window.selectedEmployees.length === 0) {
            CustomModal.info('Tidak ada karyawan yang dipilih.');
            return;
        }

        CustomModal.confirm(
            `Anda yakin ingin menghapus semua ${window.selectedEmployees.length} karyawan yang dipilih?`,
            'Konfirmasi Hapus Semua',
            {
                onConfirm: function() {
                    window.selectedEmployees = [];
                    updateSelectedEmployees();
                    CustomModal.success('Semua pilihan karyawan telah dihapus.');
                }
            }
        );
    });
});
</script>
</body>
</html>
