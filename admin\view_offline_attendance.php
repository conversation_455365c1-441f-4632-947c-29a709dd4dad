<?php
// File: admin/view_offline_attendance.php
// Deskripsi: Halaman untuk melihat absensi Training Internal

include '../config/config.php';
include 'security.php';


// Cek apakah ID training ada
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: training_management.php?training_type=internal");
    exit();
}

$training_id = $_GET['id'];
$error = '';
$success = '';

// Proses form update status kehadiran
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update_status') {
        $attendance_id = $_POST['attendance_id'];
        $status = $_POST['status'];
        $keterangan = $_POST['keterangan'];

        $query = "UPDATE offline_training_attendance
                  SET status = ?, keterangan = ?, updated_by = ?
                  WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("ssii", $status, $keterangan, $_SESSION['user_id'], $attendance_id);

        if ($stmt->execute()) {
            $success = "Status kehadiran berhasil diperbarui";
        } else {
            $error = "Error: " . $stmt->error;
        }
        $stmt->close();
    }
    // Proses form hapus peserta
    elseif (isset($_POST['action']) && $_POST['action'] === 'delete_attendance') {
        $attendance_id = $_POST['attendance_id'];

        $query = "DELETE FROM offline_training_attendance WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $attendance_id);

        if ($stmt->execute()) {
            $success = "Data kehadiran berhasil dihapus";
        } else {
            $error = "Error: " . $stmt->error;
        }
        $stmt->close();
    }
    // Proses form tambah peserta manual
    elseif (isset($_POST['action']) && $_POST['action'] === 'add_manual') {
        $nik = $_POST['nik'];
        $nama = $_POST['nama'];
        $status = $_POST['status'];
        $keterangan = $_POST['keterangan'];

        // Cek apakah karyawan sudah terdaftar di database
        $query_check = "SELECT id FROM karyawan WHERE nik = ?";
        $stmt_check = $conn->prepare($query_check);
        $stmt_check->bind_param("s", $nik);
        $stmt_check->execute();
        $result_check = $stmt_check->get_result();

        if ($result_check->num_rows > 0) {
            $karyawan = $result_check->fetch_assoc();
            $karyawan_id = $karyawan['id'];
        } else {
            $karyawan_id = null;
        }
        $stmt_check->close();

        // Cek apakah peserta sudah terdaftar di absensi training ini
        $query_check_attendance = "SELECT id FROM offline_training_attendance WHERE offline_training_id = ? AND nik = ?";
        $stmt_check_attendance = $conn->prepare($query_check_attendance);
        $stmt_check_attendance->bind_param("is", $training_id, $nik);
        $stmt_check_attendance->execute();
        $result_check_attendance = $stmt_check_attendance->get_result();

        if ($result_check_attendance->num_rows > 0) {
            $error = "Peserta dengan NIK $nik sudah terdaftar di absensi training ini";
        } else {
            $query = "INSERT INTO offline_training_attendance
                      (offline_training_id, karyawan_id, nik, nama, status, keterangan, created_by)
                      VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("iissssi", $training_id, $karyawan_id, $nik, $nama, $status, $keterangan, $_SESSION['user_id']);

            if ($stmt->execute()) {
                $success = "Peserta berhasil ditambahkan";
            } else {
                $error = "Error: " . $stmt->error;
            }
            $stmt->close();
        }
        $stmt_check_attendance->close();
    }
}

// Ambil data training
$query_training = "SELECT * FROM offline_training WHERE id = ?";
$stmt_training = $conn->prepare($query_training);
$stmt_training->bind_param("i", $training_id);
$stmt_training->execute();
$result_training = $stmt_training->get_result();

if ($result_training->num_rows === 0) {
    header("Location: training_management.php?training_type=internal");
    exit();
}

$training = $result_training->fetch_assoc();
$stmt_training->close();

// Ambil data absensi
$query_attendance = "SELECT ota.*, k.dept, k.bagian, k.jabatan
                    FROM offline_training_attendance ota
                    LEFT JOIN karyawan k ON ota.karyawan_id = k.id
                    WHERE ota.offline_training_id = ?
                    ORDER BY ota.nama ASC";
$stmt_attendance = $conn->prepare($query_attendance);
$stmt_attendance->bind_param("i", $training_id);
$stmt_attendance->execute();
$result_attendance = $stmt_attendance->get_result();
$stmt_attendance->close();

// Judul halaman
$page_title = "Absensi Training Internal";
include 'header.php';

// Tambahkan CSS dan JS khusus untuk halaman ini
echo '<link rel="stylesheet" href="assets/css/offline-training.css">';
?>

<style>
/* Employee selection styles */
.employee-row {
    transition: background-color 0.2s ease;
}

.employee-row:hover {
    background-color: #f8f9fa !important;
}

.employee-row.table-primary {
    background-color: #cce5ff !important;
    border-color: #99d1ff !important;
}

.employee-row.table-primary:hover {
    background-color: #b3d9ff !important;
}

.employee-checkbox {
    cursor: pointer;
}

/* Pagination styles */
.pagination .page-link {
    color: #007bff;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

/* Search info styles */
#searchInfo {
    font-style: italic;
    color: #6c757d;
}

/* Button disabled state */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>

<div class="container-fluid">
    <div class="justify-content-between mb-4">
        <h1 class="welcome-section  ">Absensi Training Internal</h1>
        <div>
            <a href="training_management.php?training_type=internal" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="rfid_attendance_offline.php?training_id=<?php echo $training_id; ?>" class="btn btn-primary btn-sm">
                <i class="fas fa-id-card"></i> Absensi RFID
            </a>
            <a href="export_offline_attendance.php?id=<?php echo $training_id; ?>" class="btn btn-success btn-sm">
                <i class="fas fa-file-excel"></i> Export Excel
            </a>
        </div>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="">Detail Training</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 150px;">Topik Training</th>
                            <td><?php echo htmlspecialchars($training['training_topic']); ?></td>
                        </tr>
                        <tr>
                            <th>Tanggal</th>
                            <td>
                                <?php
                                if (!empty($training['start_date'])) {
                                    $start_date_formatted = date('d-m-Y', strtotime($training['start_date']));
                                    if (!empty($training['end_date']) && $training['end_date'] !== $training['start_date']) {
                                        $end_date_formatted = date('d-m-Y', strtotime($training['end_date']));
                                        echo $start_date_formatted . ' s/d ' . $end_date_formatted;
                                    } else {
                                        echo $start_date_formatted;
                                    }
                                } else {
                                    echo '-';
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Waktu</th>
                            <td>
                                <?php
                                echo $training['training_time_start'] ? date('H:i', strtotime($training['training_time_start'])) : '-';
                                echo ' s/d ';
                                echo $training['training_time_end'] ? date('H:i', strtotime($training['training_time_end'])) : '-';
                                ?>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 150px;">Lokasi</th>
                            <td><?php echo htmlspecialchars($training['location']); ?></td>
                        </tr>
                        <tr>
                            <th>Trainer</th>
                            <td><?php echo htmlspecialchars($training['trainer_name']); ?></td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                <span class="badge badge-<?php
                                    echo $training['status'] == 'Active' ? 'success' :
                                        ($training['status'] == 'Completed' ? 'primary' : 'danger');
                                ?>">
                                    <?php echo $training['status']; ?>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <?php if (!empty($training['training_description'])): ?>
                <div class="mt-3">
                    <h6 class="font-weight-bold">Deskripsi:</h6>
                    <p><?php echo nl2br(htmlspecialchars($training['training_description'])); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Tab Navigation -->
    <ul class="nav nav-tabs" id="attendanceTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="attendance-tab" data-bs-toggle="tab" data-bs-target="#attendance" type="button" role="tab" aria-controls="attendance" aria-selected="true">
                <i class="fas fa-clipboard-check"></i> Daftar Kehadiran
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="participants-tab" data-bs-toggle="tab" data-bs-target="#participants" type="button" role="tab" aria-controls="participants" aria-selected="false">
                <i class="fas fa-users"></i> Kelola Peserta
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="attendanceTabContent">
        <!-- Attendance Tab -->
        <div class="tab-pane fade show active" id="attendance" role="tabpanel" aria-labelledby="attendance-tab">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="">Daftar Kehadiran</h6>
                    <div>
                     
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="attendanceTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th style="width: 5%;">No</th>
                                    <th style="width: 10%;">NIK</th>
                                    <th style="width: 20%;">Nama</th>
                                    <th style="width: 15%;">Departemen</th>
                                    <th style="width: 10%;">Check In</th>
                                    <th style="width: 10%;">Check Out</th>
                                    <th style="width: 10%;">Status</th>
                                    <th style="width: 20%;">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($result_attendance->num_rows > 0): ?>
                                    <?php $no = 1; while ($row = $result_attendance->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $no++; ?></td>
                                            <td><?php echo htmlspecialchars($row['nik']); ?></td>
                                            <td><?php echo htmlspecialchars($row['nama']); ?></td>
                                            <td><?php echo htmlspecialchars($row['dept'] ?? '-'); ?></td>
                                            <td>
                                                <?php echo $row['check_in'] ? date('d-m-Y H:i', strtotime($row['check_in'])) : '-'; ?>
                                            </td>
                                            <td>
                                                <?php echo $row['check_out'] ? date('d-m-Y H:i', strtotime($row['check_out'])) : '-'; ?>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?php
                                                    echo $row['status'] == 'hadir' ? 'success' :
                                                        ($row['status'] == 'terlambat' ? 'warning' :
                                                            ($row['status'] == 'izin' ? 'info' : 'danger'));
                                                ?>">
                                                    <?php echo ucfirst($row['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-primary btn-sm edit-status"
                                                        data-id="<?php echo $row['id']; ?>"
                                                        data-nik="<?php echo htmlspecialchars($row['nik']); ?>"
                                                        data-nama="<?php echo htmlspecialchars($row['nama']); ?>"
                                                        data-status="<?php echo $row['status']; ?>"
                                                        data-keterangan="<?php echo htmlspecialchars($row['keterangan'] ?? ''); ?>"
                                                        title="Edit Status">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-danger btn-sm delete-attendance"
                                                        data-id="<?php echo $row['id']; ?>"
                                                        data-nama="<?php echo htmlspecialchars($row['nama']); ?>"
                                                        title="Hapus">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center">Belum ada data kehadiran</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Participants Management Tab -->
        <div class="tab-pane fade" id="participants" role="tabpanel" aria-labelledby="participants-tab">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="">Kelola Peserta Training</h6>
                <div>
                    <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addParticipantModal">
                        <i class="fas fa-user-plus"></i> Tambah Peserta
                    </button>
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addManualModal">
                            <i class="fas fa-plus"></i> Tambah Manual
                        </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="syncParticipants()">
                        <i class="fas fa-sync"></i> Sinkron ke Absensi
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="participantsTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th style="width: 5%;">No</th>
                                <th style="width: 10%;">NIK</th>
                                <th style="width: 20%;">Nama</th>
                                <th style="width: 15%;">Departemen</th>
                                <th style="width: 15%;">Jabatan</th>
                                <th style="width: 10%;">Status</th>
                                <th style="width: 15%;">Tgl Daftar</th>
                                <th style="width: 10%;">Aksi</th>
                            </tr>
                        </thead>
                        <tbody id="participantsTableBody">
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Status -->
<div class="modal fade" id="editStatusModal" tabindex="-1" aria-labelledby="editStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editStatusModalLabel">Edit Status Kehadiran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="attendance_id" id="edit_attendance_id">

                    <div class="mb-3">
                        <label class="form-label">NIK</label>
                        <input type="text" class="form-control" id="edit_nik" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Nama</label>
                        <input type="text" class="form-control" id="edit_nama" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-control" id="edit_status" name="status" required>
                            <option value="hadir">Hadir</option>
                            <option value="terlambat">Terlambat</option>
                            <option value="izin">Izin</option>
                            <option value="tidak hadir">Tidak Hadir</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="edit_keterangan" class="form-label">Keterangan</label>
                        <textarea class="form-control" id="edit_keterangan" name="keterangan" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Hapus Kehadiran -->
<div class="modal fade" id="deleteAttendanceModal" tabindex="-1" aria-labelledby="deleteAttendanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteAttendanceModalLabel">Konfirmasi Hapus</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus data kehadiran <strong id="delete_attendance_nama"></strong>?</p>
                <p class="text-danger">Perhatian: Tindakan ini tidak dapat dibatalkan!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <form method="post" action="" style="display: inline;">
                    <input type="hidden" name="action" value="delete_attendance">
                    <input type="hidden" name="attendance_id" id="delete_attendance_id">
                    <button type="submit" class="btn btn-danger">Hapus</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Peserta Manual -->
<div class="modal fade" id="addManualModal" tabindex="-1" aria-labelledby="addManualModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addManualModalLabel">Tambah Peserta Manual</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_manual">

                    <div class="mb-3">
                        <label for="nik" class="form-label">NIK <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nik" name="nik" required>
                    </div>

                    <div class="mb-3">
                        <label for="nama" class="form-label">Nama <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nama" name="nama" required>
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-control" id="status" name="status" required>
                            <option value="hadir">Hadir</option>
                            <option value="terlambat">Terlambat</option>
                            <option value="izin">Izin</option>
                            <option value="tidak hadir">Tidak Hadir</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="keterangan" class="form-label">Keterangan</label>
                        <textarea class="form-control" id="keterangan" name="keterangan" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Tambah Peserta -->
<div class="modal fade" id="addParticipantModal" tabindex="-1" aria-labelledby="addParticipantModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addParticipantModalLabel">Tambah Peserta Training</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-8">
                        <label for="searchEmployee" class="form-label">Cari Karyawan</label>
                        <input type="text" class="form-control" id="searchEmployee" placeholder="Ketik nama, NIK, departemen, atau jabatan...">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Hasil per halaman</label>
                        <select class="form-control" id="limitPerPage">
                            <option value="10">10</option>
                            <option value="25" selected>25</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                </div>

                <!-- Search Results Info -->
                <div class="mb-2">
                    <small class="text-muted" id="searchInfo">Ketik untuk mencari karyawan...</small>
                </div>

                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-sm table-hover">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 5%;">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th style="width: 15%;">NIK</th>
                                <th style="width: 30%;">Nama</th>
                                <th style="width: 25%;">Departemen</th>
                                <th style="width: 25%;">Jabatan</th>
                            </tr>
                        </thead>
                        <tbody id="employeeList">
                            <tr>
                                <td colspan="5" class="text-center text-muted">
                                    <i class="fas fa-search"></i> Mulai ketik untuk mencari karyawan...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <small class="text-muted" id="paginationInfo"></small>
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0" id="pagination">
                            <!-- Pagination will be generated by JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" onclick="addSelectedParticipants()">
                    <i class="fas fa-plus"></i> Tambah Peserta Terpilih
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    const trainingId = <?php echo $training_id; ?>;

    // Custom Alert and Confirm functions to bypass custom-modal.js interference
    function showCustomAlert(message) {
        // Use native alert as fallback, or create custom modal
        if (window.originalAlert) {
            window.originalAlert(message);
        } else {
            // Create simple custom alert modal
            const alertModal = document.createElement('div');
            alertModal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 9999; display: flex;
                align-items: center; justify-content: center;
            `;

            alertModal.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; max-width: 400px; text-align: center;">
                    <p style="margin: 0 0 15px 0; font-size: 16px;">${message}</p>
                    <button onclick="this.closest('div').remove(); document.body.style.overflow='auto';"
                            style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; cursor: pointer;">
                        OK
                    </button>
                </div>
            `;

            document.body.appendChild(alertModal);
            document.body.style.overflow = 'hidden';
        }
    }

    function showCustomConfirm(message, callback) {
        // Create custom confirm modal
        const confirmModal = document.createElement('div');
        confirmModal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.5); z-index: 9999; display: flex;
            align-items: center; justify-content: center;
        `;

        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background: white; padding: 20px; border-radius: 8px;
            max-width: 400px; text-align: center;
        `;

        modalContent.innerHTML = `
            <p style="margin: 0 0 20px 0; font-size: 16px;">${message}</p>
            <div style="display: flex; gap: 10px; justify-content: center;">
                <button id="cancelBtn" style="background: #6c757d; color: white; border: none; padding: 8px 20px; border-radius: 4px; cursor: pointer;">
                    Batal
                </button>
                <button id="confirmBtn" style="background: #dc3545; color: white; border: none; padding: 8px 20px; border-radius: 4px; cursor: pointer;">
                    Ya, Lanjutkan
                </button>
            </div>
        `;

        confirmModal.appendChild(modalContent);

        // Add event listeners
        modalContent.querySelector('#cancelBtn').addEventListener('click', function() {
            document.body.removeChild(confirmModal);
            document.body.style.overflow = 'auto';
        });

        modalContent.querySelector('#confirmBtn').addEventListener('click', function() {
            document.body.removeChild(confirmModal);
            document.body.style.overflow = 'auto';
            callback();
        });

        document.body.appendChild(confirmModal);
        document.body.style.overflow = 'hidden';
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Load participants when participants tab is shown
        const participantsTab = document.getElementById('participants-tab');
        if (participantsTab) {
            participantsTab.addEventListener('shown.bs.tab', function() {
                loadParticipants();
            });
        }

        // Load employee list when add participant modal is shown
        const addParticipantModal = document.getElementById('addParticipantModal');
        if (addParticipantModal) {
            addParticipantModal.addEventListener('shown.bs.modal', function() {
                // Reset search when modal opens
                document.getElementById('searchEmployee').value = '';
                document.getElementById('employeeList').innerHTML = '<tr><td colspan="5" class="text-center text-muted"><i class="fas fa-search"></i> Mulai ketik untuk mencari karyawan...</td></tr>';
                document.getElementById('searchInfo').textContent = 'Ketik untuk mencari karyawan...';
                document.getElementById('pagination').innerHTML = '';
                document.getElementById('paginationInfo').textContent = '';
                currentPage = 1;

                // Reset selected employees
                selectedEmployees.clear();
                updateSelectedCount();

                // Reset select all checkbox
                const selectAllCheckbox = document.getElementById('selectAll');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                    selectAllCheckbox.indeterminate = false;
                }
            });
        }

        // Search functionality with debounce
        const searchEmployee = document.getElementById('searchEmployee');
        if (searchEmployee) {
            searchEmployee.addEventListener('input', function() {
                const query = this.value.trim();

                // Clear previous timeout
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }

                // Set new timeout for debounced search
                searchTimeout = setTimeout(() => {
                    currentPage = 1;
                    if (query.length >= 2 || query.length === 0) {
                        searchEmployees(query, 1);
                    } else if (query.length === 0) {
                        // Reset to initial state
                        document.getElementById('employeeList').innerHTML = '<tr><td colspan="5" class="text-center text-muted"><i class="fas fa-search"></i> Mulai ketik untuk mencari karyawan...</td></tr>';
                        document.getElementById('searchInfo').textContent = 'Ketik untuk mencari karyawan...';
                        document.getElementById('pagination').innerHTML = '';
                        document.getElementById('paginationInfo').textContent = '';
                    }
                }, 500); // 500ms delay
            });
        }

        // Limit per page change
        const limitPerPage = document.getElementById('limitPerPage');
        if (limitPerPage) {
            limitPerPage.addEventListener('change', function() {
                const query = document.getElementById('searchEmployee').value;
                currentPage = 1;
                if (query.trim().length >= 2 || query.trim().length === 0) {
                    searchEmployees(query, 1);
                }
            });
        }

        // Select all checkbox
        const selectAll = document.getElementById('selectAll');
        if (selectAll) {
            selectAll.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('#employeeList .employee-checkbox');
                const rows = document.querySelectorAll('#employeeList .employee-row');

                checkboxes.forEach((checkbox, index) => {
                    const employeeId = checkbox.value;
                    const row = rows[index];
                    const employeeData = {
                        id: employeeId,
                        nik: checkbox.getAttribute('data-nik'),
                        nama: checkbox.getAttribute('data-nama')
                    };

                    if (this.checked) {
                        // Select all visible employees
                        checkbox.checked = true;
                        selectedEmployees.set(employeeId, employeeData);
                        if (row) row.classList.add('table-primary');
                    } else {
                        // Deselect all visible employees
                        checkbox.checked = false;
                        selectedEmployees.delete(employeeId);
                        if (row) row.classList.remove('table-primary');
                    }
                });

                updateSelectedCount();
            });
        }

        // Handle edit status button click
        document.querySelectorAll('.edit-status').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const nik = this.getAttribute('data-nik');
                const nama = this.getAttribute('data-nama');
                const status = this.getAttribute('data-status');
                const keterangan = this.getAttribute('data-keterangan');

                document.getElementById('edit_attendance_id').value = id;
                document.getElementById('edit_nik').value = nik;
                document.getElementById('edit_nama').value = nama;
                document.getElementById('edit_status').value = status;
                document.getElementById('edit_keterangan').value = keterangan;

                const editModal = new bootstrap.Modal(document.getElementById('editStatusModal'));
                editModal.show();
            });
        });

        // Handle delete attendance button click
        document.querySelectorAll('.delete-attendance').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const nama = this.getAttribute('data-nama');

                document.getElementById('delete_attendance_id').value = id;
                document.getElementById('delete_attendance_nama').textContent = nama;

                const deleteModal = new bootstrap.Modal(document.getElementById('deleteAttendanceModal'));
                deleteModal.show();
            });
        });
    });

    // Load participants list
    function loadParticipants() {
        fetch(`manage_participants_api.php?action=get_participants&training_id=${trainingId}`)
            .then(response => response.json())
            .then(data => {
                const tbody = document.getElementById('participantsTableBody');
                tbody.innerHTML = '';

                if (data.success && data.participants.length > 0) {
                    data.participants.forEach((participant, index) => {
                        const registered_by = participant.registered_by ? participant.registered_by : '-';
                        const row = `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${participant.nik}</td>
                                <td>${participant.nama}</td>
                                <td>${participant.departemen || '-'}</td>
                                <td>${participant.jabatan || '-'}</td>
                                <td>
                                    <span class="badge badge-${participant.status === 'registered' ? 'success' :
                                        (participant.status === 'confirmed' ? 'primary' : 'secondary')}">
                                        ${participant.status}
                                    </span>
                                </td>
                                <td>${new Date(participant.registered_at).toLocaleString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
})}</td>
<td>${registered_by}</td>

                                <td>
                                    <button class="btn btn-danger btn-sm" onclick="removeParticipant(${participant.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                } else {
                    tbody.innerHTML = '<tr><td colspan="8" class="text-center">Belum ada peserta terdaftar</td></tr>';
                }
            })
            .catch(error => {
                console.error('Error loading participants:', error);
                alert('Gagal memuat data peserta');
            });
    }

    // Search employees with pagination
    let currentPage = 1;
    let searchTimeout = null;
    let selectedEmployees = new Map(); // Store selected employee data (ID -> {id, nik, nama})

    function searchEmployees(query = '', page = 1) {
        const limit = document.getElementById('limitPerPage').value;
        const searchInfo = document.getElementById('searchInfo');
        const tbody = document.getElementById('employeeList');

        // Show loading
        tbody.innerHTML = '<tr><td colspan="5" class="text-center"><i class="fas fa-spinner fa-spin"></i> Mencari...</td></tr>';
        searchInfo.textContent = 'Mencari karyawan...';

        const url = `search_employees.php?search=${encodeURIComponent(query)}&limit=${limit}&page=${page}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayEmployees(data.employees);
                    updatePagination(data.current_page, Math.ceil(data.total_records / limit), data.total_records);
                    updateSearchInfo(data.total_records, query);
                } else {
                    tbody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">Error: ' + data.message + '</td></tr>';
                    searchInfo.textContent = 'Error dalam pencarian';
                }
            })
            .catch(error => {
                console.error('Error searching employees:', error);
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">Gagal mencari karyawan</td></tr>';
                searchInfo.textContent = 'Error dalam pencarian';
            });
    }

    function displayEmployees(employees) {
        const tbody = document.getElementById('employeeList');
        tbody.innerHTML = '';

        if (employees.length > 0) {
            employees.forEach(employee => {
                const isSelected = selectedEmployees.has(employee.id.toString());
                const checkedAttr = isSelected ? 'checked' : '';
                const selectedClass = isSelected ? 'table-primary' : '';

                const row = document.createElement('tr');
                row.className = `employee-row ${selectedClass}`;
                row.style.cursor = 'pointer';
                row.dataset.employeeId = employee.id;
                row.dataset.nik = employee.nik;
                row.dataset.nama = employee.nama;

                row.innerHTML = `
                    <td onclick="event.stopPropagation()">
                        <input type="checkbox" class="form-check-input employee-checkbox"
                               value="${employee.id}" data-nik="${employee.nik}" data-nama="${employee.nama}" ${checkedAttr}>
                    </td>
                    <td>${employee.nik}</td>
                    <td>${employee.nama}</td>
                    <td>${employee.dept || '-'}</td>
                    <td>${employee.jabatan || '-'}</td>
                `;

                // Add click event to row
                row.addEventListener('click', function() {
                    toggleEmployeeSelection(this);
                });

                // Add change event to checkbox
                const checkbox = row.querySelector('.employee-checkbox');
                checkbox.addEventListener('change', function(e) {
                    e.stopPropagation(); // Prevent row click
                    const employeeId = this.value;
                    const employeeData = {
                        id: employeeId,
                        nik: this.getAttribute('data-nik'),
                        nama: this.getAttribute('data-nama')
                    };

                    if (this.checked) {
                        selectedEmployees.set(employeeId, employeeData);
                        row.classList.add('table-primary');
                    } else {
                        selectedEmployees.delete(employeeId);
                        row.classList.remove('table-primary');
                    }

                    updateSelectAllCheckbox();
                    updateSelectedCount();
                });

                tbody.appendChild(row);
            });

            // Update select all checkbox
            updateSelectAllCheckbox();
        } else {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">Tidak ada karyawan ditemukan</td></tr>';
        }
    }

    function toggleEmployeeSelection(row) {
        const checkbox = row.querySelector('.employee-checkbox');
        const employeeId = checkbox.value;
        const employeeData = {
            id: employeeId,
            nik: checkbox.getAttribute('data-nik'),
            nama: checkbox.getAttribute('data-nama')
        };

        // Toggle checkbox
        checkbox.checked = !checkbox.checked;

        // Update selection
        if (checkbox.checked) {
            selectedEmployees.set(employeeId, employeeData);
            row.classList.add('table-primary');
        } else {
            selectedEmployees.delete(employeeId);
            row.classList.remove('table-primary');
        }

        // Update select all checkbox
        updateSelectAllCheckbox();

        // Update selected count display
        updateSelectedCount();
    }

    function updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const visibleCheckboxes = document.querySelectorAll('.employee-checkbox');
        const checkedVisibleCheckboxes = document.querySelectorAll('.employee-checkbox:checked');

        if (visibleCheckboxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedVisibleCheckboxes.length === visibleCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else if (checkedVisibleCheckboxes.length > 0) {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        } else {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        }
    }

    function updateSelectedCount() {
        const selectedCount = selectedEmployees.size;
        const addButton = document.querySelector('#addParticipantModal .modal-footer .btn-primary');

        if (addButton) {
            if (selectedCount > 0) {
                addButton.innerHTML = `<i class="fas fa-plus"></i> Tambah ${selectedCount} Peserta Terpilih`;
                addButton.disabled = false;
            } else {
                addButton.innerHTML = `<i class="fas fa-plus"></i> Tambah Peserta Terpilih`;
                addButton.disabled = true;
            }
        }
    }

    function updateSearchInfo(totalRecords, query) {
        const searchInfo = document.getElementById('searchInfo');
        if (query.trim()) {
            searchInfo.textContent = `Ditemukan ${totalRecords} karyawan untuk "${query}"`;
        } else {
            searchInfo.textContent = `Menampilkan ${totalRecords} karyawan`;
        }
    }

    function updatePagination(currentPage, totalPages, totalRecords) {
        const pagination = document.getElementById('pagination');
        const paginationInfo = document.getElementById('paginationInfo');

        // Update pagination info
        const limit = parseInt(document.getElementById('limitPerPage').value);
        const start = (currentPage - 1) * limit + 1;
        const end = Math.min(currentPage * limit, totalRecords);
        paginationInfo.textContent = `Menampilkan ${start}-${end} dari ${totalRecords} karyawan`;

        // Generate pagination buttons
        pagination.innerHTML = '';

        if (totalPages <= 1) return;

        // Previous button
        const prevDisabled = currentPage === 1 ? 'disabled' : '';
        pagination.innerHTML += `
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" onclick="changePage(${currentPage - 1})" ${prevDisabled ? 'tabindex="-1"' : ''}>
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;

        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            pagination.innerHTML += '<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>';
            if (startPage > 2) {
                pagination.innerHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const active = i === currentPage ? 'active' : '';
            pagination.innerHTML += `
                <li class="page-item ${active}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                pagination.innerHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            pagination.innerHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a></li>`;
        }

        // Next button
        const nextDisabled = currentPage === totalPages ? 'disabled' : '';
        pagination.innerHTML += `
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" onclick="changePage(${currentPage + 1})" ${nextDisabled ? 'tabindex="-1"' : ''}>
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
    }

    function changePage(page) {
        if (page < 1) return;
        currentPage = page;
        const query = document.getElementById('searchEmployee').value;
        searchEmployees(query, page);
    }


    // Add selected participants
    function addSelectedParticipants() {
        if (selectedEmployees.size === 0) {
            showCustomAlert('Pilih minimal satu karyawan');
            return;
        }

        // Convert Map to Array for API
        const employeesToAdd = Array.from(selectedEmployees.values());

        // Send to server
        fetch('manage_participants_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'add_participants',
                training_id: trainingId,
                employees: employeesToAdd
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showCustomAlert(`Berhasil menambahkan ${data.added_count} peserta`);
                bootstrap.Modal.getInstance(document.getElementById('addParticipantModal')).hide();
                loadParticipants();

                // Clear selections
                selectedEmployees.clear();
            } else {
                showCustomAlert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error adding participants:', error);
            showCustomAlert('Gagal menambahkan peserta');
        });
    }

    // Remove participant
    function removeParticipant(participantId) {
        showCustomConfirm('Apakah Anda yakin ingin menghapus peserta ini?', function() {
            executeRemoveParticipant(participantId);
        });
    }

    function executeRemoveParticipant(participantId) {

        fetch('manage_participants_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'remove_participant',
                participant_id: participantId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showCustomAlert('Peserta berhasil dihapus');
                loadParticipants();
            } else {
                showCustomAlert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error removing participant:', error);
            showCustomAlert('Gagal menghapus peserta');
        });
    }

    // Sync participants to attendance
    function syncParticipants() {
        showCustomConfirm('Sinkronisasi akan menambahkan semua peserta terdaftar ke daftar absensi. Lanjutkan?', function() {
            executeSyncParticipants();
        });
    }

    function executeSyncParticipants() {

        fetch('manage_participants_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'sync_to_attendance',
                training_id: trainingId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showCustomAlert(`Berhasil menyinkronkan ${data.synced_count} peserta ke absensi`);
                location.reload(); // Reload to show updated attendance
            } else {
                showCustomAlert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error syncing participants:', error);
            showCustomAlert('Gagal menyinkronkan peserta');
        });
    }
</script>
</div>
<?php include 'footer.php'; ?>
