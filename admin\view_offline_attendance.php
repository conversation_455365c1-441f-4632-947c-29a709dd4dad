<?php
// File: admin/view_offline_attendance.php
// Deskripsi: Halaman untuk melihat absensi Training Internal

include '../config/config.php';
include 'security.php';


// Cek apakah ID training ada
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: training_management.php?training_type=internal");
    exit();
}

$training_id = $_GET['id'];
$error = '';
$success = '';

// Proses form update status kehadiran
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update_status') {
        $attendance_id = $_POST['attendance_id'];
        $status = $_POST['status'];
        $keterangan = $_POST['keterangan'];

        $query = "UPDATE offline_training_attendance
                  SET status = ?, keterangan = ?, updated_by = ?
                  WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("ssii", $status, $keterangan, $_SESSION['user_id'], $attendance_id);

        if ($stmt->execute()) {
            $success = "Status kehadiran berhasil diperbarui";
        } else {
            $error = "Error: " . $stmt->error;
        }
        $stmt->close();
    }
    // Proses form hapus peserta
    elseif (isset($_POST['action']) && $_POST['action'] === 'delete_attendance') {
        $attendance_id = $_POST['attendance_id'];

        $query = "DELETE FROM offline_training_attendance WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $attendance_id);

        if ($stmt->execute()) {
            $success = "Data kehadiran berhasil dihapus";
        } else {
            $error = "Error: " . $stmt->error;
        }
        $stmt->close();
    }
    // Proses form tambah peserta manual
    elseif (isset($_POST['action']) && $_POST['action'] === 'add_manual') {
        $nik = $_POST['nik'];
        $nama = $_POST['nama'];
        $status = $_POST['status'];
        $keterangan = $_POST['keterangan'];

        // Cek apakah karyawan sudah terdaftar di database
        $query_check = "SELECT id FROM karyawan WHERE nik = ?";
        $stmt_check = $conn->prepare($query_check);
        $stmt_check->bind_param("s", $nik);
        $stmt_check->execute();
        $result_check = $stmt_check->get_result();

        if ($result_check->num_rows > 0) {
            $karyawan = $result_check->fetch_assoc();
            $karyawan_id = $karyawan['id'];
        } else {
            $karyawan_id = null;
        }
        $stmt_check->close();

        // Cek apakah peserta sudah terdaftar di absensi training ini
        $query_check_attendance = "SELECT id FROM offline_training_attendance WHERE offline_training_id = ? AND nik = ?";
        $stmt_check_attendance = $conn->prepare($query_check_attendance);
        $stmt_check_attendance->bind_param("is", $training_id, $nik);
        $stmt_check_attendance->execute();
        $result_check_attendance = $stmt_check_attendance->get_result();

        if ($result_check_attendance->num_rows > 0) {
            $error = "Peserta dengan NIK $nik sudah terdaftar di absensi training ini";
        } else {
            $query = "INSERT INTO offline_training_attendance
                      (offline_training_id, karyawan_id, nik, nama, status, keterangan, created_by)
                      VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("iissssi", $training_id, $karyawan_id, $nik, $nama, $status, $keterangan, $_SESSION['user_id']);

            if ($stmt->execute()) {
                $success = "Peserta berhasil ditambahkan";
            } else {
                $error = "Error: " . $stmt->error;
            }
            $stmt->close();
        }
        $stmt_check_attendance->close();
    }
}

// Ambil data training
$query_training = "SELECT * FROM offline_training WHERE id = ?";
$stmt_training = $conn->prepare($query_training);
$stmt_training->bind_param("i", $training_id);
$stmt_training->execute();
$result_training = $stmt_training->get_result();

if ($result_training->num_rows === 0) {
    header("Location: training_management.php?training_type=internal");
    exit();
}

$training = $result_training->fetch_assoc();
$stmt_training->close();

// Ambil data absensi
$query_attendance = "SELECT ota.*, k.dept, k.bagian, k.jabatan
                    FROM offline_training_attendance ota
                    LEFT JOIN karyawan k ON ota.karyawan_id = k.id
                    WHERE ota.offline_training_id = ?
                    ORDER BY ota.nama ASC";
$stmt_attendance = $conn->prepare($query_attendance);
$stmt_attendance->bind_param("i", $training_id);
$stmt_attendance->execute();
$result_attendance = $stmt_attendance->get_result();
$stmt_attendance->close();

// Judul halaman
$page_title = "Absensi Training Internal";
include 'header.php';

// Tambahkan CSS dan JS khusus untuk halaman ini
echo '<link rel="stylesheet" href="assets/css/offline-training.css">';
?>

<div class="container-fluid">
    <div class="justify-content-between mb-4">
        <h1 class="welcome-section  ">Absensi Training Internal</h1>
        <div>
            <a href="training_management.php?training_type=internal" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="rfid_attendance_offline.php?training_id=<?php echo $training_id; ?>" class="btn btn-primary btn-sm">
                <i class="fas fa-id-card"></i> Absensi RFID
            </a>
            <a href="export_offline_attendance.php?id=<?php echo $training_id; ?>" class="btn btn-success btn-sm">
                <i class="fas fa-file-excel"></i> Export Excel
            </a>
        </div>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="">Detail Training</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 150px;">Topik Training</th>
                            <td><?php echo htmlspecialchars($training['training_topic']); ?></td>
                        </tr>
                        <tr>
                            <th>Tanggal</th>
                            <td>
                                <?php
                                if (!empty($training['start_date'])) {
                                    $start_date_formatted = date('d-m-Y', strtotime($training['start_date']));
                                    if (!empty($training['end_date']) && $training['end_date'] !== $training['start_date']) {
                                        $end_date_formatted = date('d-m-Y', strtotime($training['end_date']));
                                        echo $start_date_formatted . ' s/d ' . $end_date_formatted;
                                    } else {
                                        echo $start_date_formatted;
                                    }
                                } else {
                                    echo '-';
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Waktu</th>
                            <td>
                                <?php
                                echo $training['training_time_start'] ? date('H:i', strtotime($training['training_time_start'])) : '-';
                                echo ' s/d ';
                                echo $training['training_time_end'] ? date('H:i', strtotime($training['training_time_end'])) : '-';
                                ?>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 150px;">Lokasi</th>
                            <td><?php echo htmlspecialchars($training['location']); ?></td>
                        </tr>
                        <tr>
                            <th>Trainer</th>
                            <td><?php echo htmlspecialchars($training['trainer_name']); ?></td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                <span class="badge badge-<?php
                                    echo $training['status'] == 'Active' ? 'success' :
                                        ($training['status'] == 'Completed' ? 'primary' : 'danger');
                                ?>">
                                    <?php echo $training['status']; ?>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <?php if (!empty($training['training_description'])): ?>
                <div class="mt-3">
                    <h6 class="font-weight-bold">Deskripsi:</h6>
                    <p><?php echo nl2br(htmlspecialchars($training['training_description'])); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="">Daftar Kehadiran</h6>
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addManualModal">
                <i class="fas fa-plus"></i> Tambah Peserta Manual
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="attendanceTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th style="width: 5%;">No</th>
                            <th style="width: 10%;">NIK</th>
                            <th style="width: 20%;">Nama</th>
                            <th style="width: 15%;">Departemen</th>
                            <th style="width: 10%;">Check In</th>
                            <th style="width: 10%;">Check Out</th>
                            <th style="width: 10%;">Status</th>
                            <th style="width: 20%;">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result_attendance->num_rows > 0): ?>
                            <?php $no = 1; while ($row = $result_attendance->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $no++; ?></td>
                                    <td><?php echo htmlspecialchars($row['nik']); ?></td>
                                    <td><?php echo htmlspecialchars($row['nama']); ?></td>
                                    <td><?php echo htmlspecialchars($row['dept'] ?? '-'); ?></td>
                                    <td>
                                        <?php echo $row['check_in'] ? date('d-m-Y H:i', strtotime($row['check_in'])) : '-'; ?>
                                    </td>
                                    <td>
                                        <?php echo $row['check_out'] ? date('d-m-Y H:i', strtotime($row['check_out'])) : '-'; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php
                                            echo $row['status'] == 'hadir' ? 'success' :
                                                ($row['status'] == 'terlambat' ? 'warning' :
                                                    ($row['status'] == 'izin' ? 'info' : 'danger'));
                                        ?>">
                                            <?php echo ucfirst($row['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-primary btn-sm edit-status"
                                                data-id="<?php echo $row['id']; ?>"
                                                data-nik="<?php echo htmlspecialchars($row['nik']); ?>"
                                                data-nama="<?php echo htmlspecialchars($row['nama']); ?>"
                                                data-status="<?php echo $row['status']; ?>"
                                                data-keterangan="<?php echo htmlspecialchars($row['keterangan'] ?? ''); ?>"
                                                title="Edit Status">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm delete-attendance"
                                                data-id="<?php echo $row['id']; ?>"
                                                data-nama="<?php echo htmlspecialchars($row['nama']); ?>"
                                                title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center">Belum ada data kehadiran</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Status -->
<div class="modal fade" id="editStatusModal" tabindex="-1" aria-labelledby="editStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editStatusModalLabel">Edit Status Kehadiran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="attendance_id" id="edit_attendance_id">

                    <div class="mb-3">
                        <label class="form-label">NIK</label>
                        <input type="text" class="form-control" id="edit_nik" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Nama</label>
                        <input type="text" class="form-control" id="edit_nama" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-control" id="edit_status" name="status" required>
                            <option value="hadir">Hadir</option>
                            <option value="terlambat">Terlambat</option>
                            <option value="izin">Izin</option>
                            <option value="tidak hadir">Tidak Hadir</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="edit_keterangan" class="form-label">Keterangan</label>
                        <textarea class="form-control" id="edit_keterangan" name="keterangan" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Hapus Kehadiran -->
<div class="modal fade" id="deleteAttendanceModal" tabindex="-1" aria-labelledby="deleteAttendanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteAttendanceModalLabel">Konfirmasi Hapus</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus data kehadiran <strong id="delete_attendance_nama"></strong>?</p>
                <p class="text-danger">Perhatian: Tindakan ini tidak dapat dibatalkan!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <form method="post" action="">
                    <input type="hidden" name="action" value="delete_attendance">
                    <input type="hidden" name="attendance_id" id="delete_attendance_id">
                    <button type="submit" class="btn btn-danger">Hapus</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Peserta Manual -->
<div class="modal fade" id="addManualModal" tabindex="-1" aria-labelledby="addManualModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addManualModalLabel">Tambah Peserta Manual</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_manual">

                    <div class="mb-3">
                        <label for="nik" class="form-label">NIK <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nik" name="nik" required>
                    </div>

                    <div class="mb-3">
                        <label for="nama" class="form-label">Nama <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nama" name="nama" required>
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-control" id="status" name="status" required>
                            <option value="hadir">Hadir</option>
                            <option value="terlambat">Terlambat</option>
                            <option value="izin">Izin</option>
                            <option value="tidak hadir">Tidak Hadir</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="keterangan" class="form-label">Keterangan</label>
                        <textarea class="form-control" id="keterangan" name="keterangan" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle edit status button click
        document.querySelectorAll('.edit-status').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const nik = this.getAttribute('data-nik');
                const nama = this.getAttribute('data-nama');
                const status = this.getAttribute('data-status');
                const keterangan = this.getAttribute('data-keterangan');

                document.getElementById('edit_attendance_id').value = id;
                document.getElementById('edit_nik').value = nik;
                document.getElementById('edit_nama').value = nama;
                document.getElementById('edit_status').value = status;
                document.getElementById('edit_keterangan').value = keterangan;

                const editModal = new bootstrap.Modal(document.getElementById('editStatusModal'));
                editModal.show();
            });
        });

        // Handle delete attendance button click
        document.querySelectorAll('.delete-attendance').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const nama = this.getAttribute('data-nama');

                document.getElementById('delete_attendance_id').value = id;
                document.getElementById('delete_attendance_nama').textContent = nama;

                const deleteModal = new bootstrap.Modal(document.getElementById('deleteAttendanceModal'));
                deleteModal.show();
            });
        });
    });
</script>

<?php include 'footer.php'; ?>
