-- MySQL dump 10.13  Distrib 8.0.30, for Win64 (x86_64)
--
-- Host: localhost    Database: db_training
-- ------------------------------------------------------
-- Server version	8.0.30

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `data_training`
--

DROP TABLE IF EXISTS `data_training`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `data_training` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nik` varchar(20) NOT NULL,
  `nama` varchar(100) NOT NULL,
  `staff_status` varchar(50) DEFAULT NULL,
  `level` varchar(20) DEFAULT NULL,
  `jabatan` varchar(100) DEFAULT NULL,
  `pt` varchar(100) DEFAULT NULL,
  `departemen` varchar(100) DEFAULT NULL,
  `sub_dept` varchar(100) DEFAULT NULL,
  `kode_training` varchar(50) DEFAULT NULL,
  `nama_training` varchar(200) NOT NULL,
  `academy` varchar(100) DEFAULT NULL,
  `tempat_training` varchar(200) DEFAULT NULL,
  `internal_external` enum('Internal','External') DEFAULT NULL,
  `inhouse_outhouse` enum('Inhouse','Outhouse') DEFAULT NULL,
  `training_type` enum('Soft','Tech','Compliance') DEFAULT NULL,
  `trainer_nik` varchar(20) DEFAULT NULL,
  `nama_trainer` varchar(100) DEFAULT NULL,
  `tanggal_mulai` date DEFAULT NULL,
  `tanggal_selesai` date DEFAULT NULL,
  `kode_absen` varchar(20) DEFAULT NULL,
  `target_peserta` int DEFAULT NULL,
  `jam_pembelajaran` int DEFAULT NULL,
  `program_feedback` text,
  `trainer_feedback` text,
  `acomodation_feedback` text,
  `feedback_form` text,
  `status_training` varchar(50) DEFAULT NULL,
  `no_sertifikat` varchar(100) DEFAULT NULL,
  `ikatan_dinas` enum('Ya','Tidak') DEFAULT NULL,
  `tgl_mulai_ikatan` date DEFAULT NULL,
  `akhir_ikatan` date DEFAULT NULL,
  `pre_test` decimal(5,2) DEFAULT NULL,
  `post_test` decimal(5,2) DEFAULT NULL,
  `nilai_praktek` decimal(5,2) DEFAULT NULL,
  `masa_berlaku_sio` date DEFAULT NULL,
  `letak_sertifikat` varchar(200) DEFAULT NULL,
  `letak_perjanjian` varchar(200) DEFAULT NULL,
  `letak_modul` varchar(200) DEFAULT NULL,
  `keterangan` text,
  `biaya_training` decimal(15,2) DEFAULT NULL,
  `backup_staff` varchar(100) DEFAULT NULL,
  `jenis_training` enum('Mandatory','Non Mandatory') DEFAULT NULL,
  `bulan` int DEFAULT NULL,
  `tahun` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_nik` (`nik`),
  KEY `idx_nama` (`nama`),
  KEY `idx_kode_training` (`kode_training`),
  KEY `idx_tanggal` (`tanggal_mulai`,`tanggal_selesai`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `data_training`
--

LOCK TABLES `data_training` WRITE;
/*!40000 ALTER TABLE `data_training` DISABLE KEYS */;
/*!40000 ALTER TABLE `data_training` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-23 22:13:51
