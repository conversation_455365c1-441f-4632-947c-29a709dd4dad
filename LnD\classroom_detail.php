<?php
/**
 * Classroom Detail Page for Pemohon (Applicant)
 * This page displays the details of a training class
 */

include '../config/config.php';
include '../includes/functions.php';
include '../includes/file_type_helper.php';
include 'security.php';

// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['full_name'] ?? 'Pengguna';

// Check if class ID is provided
$class_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($class_id <= 0) {
    header('Location: classroom.php');
    exit();
}

// Get class information
$class_query = "SELECT c.*, t.training_topic, t.training_type
               FROM training_classes c
               JOIN training_submissions t ON c.training_id = t.id
               WHERE c.id = ?";
$stmt = $conn->prepare($class_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$class = $result->fetch_assoc();
$stmt->close();

if (!$class) {
    header('Location: classroom.php');
    exit();
}

// Check if user is a participant in this class
$participant_query = "SELECT * FROM training_participants
                     WHERE class_id = ? AND user_id = ?";
$stmt = $conn->prepare($participant_query);
$stmt->bind_param("ii", $class_id, $user_id);
$stmt->execute();
$participant_result = $stmt->get_result();
$is_participant = $participant_result->num_rows > 0;
$participant = $participant_result->fetch_assoc();
$stmt->close();

if (!$is_participant) {
    header('Location: classroom.php');
    exit();
}

// Get materials
$materials = [];
$materials_query = "SELECT * FROM training_materials
                   WHERE class_id = ? AND is_published = 1
                   ORDER BY order_number ASC";
$stmt = $conn->prepare($materials_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $materials[] = $row;
}
$stmt->close();

// Get quizzes
$quizzes = [];
$quizzes_query = "SELECT q.*,
                 (SELECT COUNT(*) FROM training_questions WHERE quiz_id = q.id) as question_count,
                 (SELECT COUNT(*) FROM training_quiz_attempts WHERE quiz_id = q.id AND user_id = ?) as attempt_count,
                 (SELECT MAX(score) FROM training_quiz_attempts WHERE quiz_id = q.id AND user_id = ? AND status = 'graded') as best_score
                 FROM training_quizzes q
                 WHERE q.class_id = ? AND q.is_published = 1
                 ORDER BY q.created_at DESC";
$stmt = $conn->prepare($quizzes_query);
$stmt->bind_param("iii", $user_id, $user_id, $class_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $quizzes[] = $row;
}
$stmt->close();

// Get assignments
$assignments = [];
$assignments_query = "SELECT a.*,
                     (SELECT COUNT(*) FROM training_assignment_submissions WHERE assignment_id = a.id AND user_id = ?) as submission_count,
                     (SELECT status FROM training_assignment_submissions WHERE assignment_id = a.id AND user_id = ? ORDER BY submitted_at DESC LIMIT 1) as submission_status,
                     (SELECT grade FROM training_assignment_submissions WHERE assignment_id = a.id AND user_id = ? ORDER BY submitted_at DESC LIMIT 1) as submission_grade
                     FROM training_assignments a
                     WHERE a.class_id = ? AND a.is_published = 1
                     ORDER BY a.due_date ASC";

// Check if the training_assignments table exists
$check_assignments_table = $conn->query("SHOW TABLES LIKE 'training_assignments'");
if ($check_assignments_table && $check_assignments_table->num_rows > 0) {
    $stmt = $conn->prepare($assignments_query);
    $stmt->bind_param("iiii", $user_id, $user_id, $user_id, $class_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $assignments[] = $row;
    }
    $stmt->close();
}

// First, let's check the structure of the users table to find the name column for discussions
$user_table_query = "DESCRIBE users";
$user_table_result = $conn->query($user_table_query);
$name_column = 'name'; // Default to name

if ($user_table_result) {
    while ($column = $user_table_result->fetch_assoc()) {
        // Look for common name column patterns
        if (in_array(strtolower($column['Field']), ['full_name', 'name', 'user_name', 'username', 'nama'])) {
            $name_column = $column['Field'];
            // Prefer full_name or name if available
            if (in_array(strtolower($column['Field']), ['full_name', 'name', 'nama'])) {
                break;
            }
        }
    }
}

// Get discussions
$discussions = [];
$discussions_query = "SELECT d.*, u.$name_column as creator_name,
                     (SELECT COUNT(*) FROM training_discussion_comments WHERE discussion_id = d.id) as comment_count
                     FROM training_discussions d
                     JOIN users u ON d.created_by = u.id
                     WHERE d.class_id = ?
                     ORDER BY d.is_pinned DESC, d.created_at DESC";

// Check if the training_discussions table exists
$check_discussions_table = $conn->query("SHOW TABLES LIKE 'training_discussions'");
if ($check_discussions_table && $check_discussions_table->num_rows > 0) {
    $stmt = $conn->prepare($discussions_query);
    $stmt->bind_param("i", $class_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $discussions[] = $row;
    }
    $stmt->close();
}

// Get participants
$participants = [];
$participants_query = "SELECT p.*, u.user_name as participant_name
                      FROM training_participants p
                      JOIN users u ON p.user_id = u.id
                      WHERE p.class_id = ?
                      ORDER BY p.role ASC, u.user_name ASC";

// First, let's check the structure of the users table to find the name column
$user_table_query = "DESCRIBE users";
$user_table_result = $conn->query($user_table_query);
$name_column = 'user_name'; // Default to user_name if we can't find a better column

if ($user_table_result) {
    while ($column = $user_table_result->fetch_assoc()) {
        // Look for common name column patterns
        if (in_array(strtolower($column['Field']), ['full_name', 'name', 'user_name', 'username', 'nama'])) {
            $name_column = $column['Field'];
            // Prefer full_name or name if available
            if (in_array(strtolower($column['Field']), ['full_name', 'name', 'nama'])) {
                break;
            }
        }
    }
}

$participants_query = "SELECT p.*, u.$name_column as participant_name
                      FROM training_participants p
                      JOIN users u ON p.user_id = u.id
                      WHERE p.class_id = ?
                      ORDER BY p.role ASC, u.$name_column ASC";

$stmt = $conn->prepare($participants_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $participants[] = $row;
}
$stmt->close();

// Group participants by role
$instructors = array_filter($participants, function($p) { return $p['role'] == 'instructor'; });
$assistants = array_filter($participants, function($p) { return $p['role'] == 'assistant'; });
$students = array_filter($participants, function($p) { return $p['role'] == 'student'; });
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .class-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
        color: var(--white);
        padding: var(--spacing-xl) var(--spacing-lg);
        border-radius: var(--border-radius-lg);
        margin-bottom: var(--spacing-xl);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-lg);
    }

    .class-title {
        font-size: 2.5rem;
        margin-bottom: var(--spacing-md);
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    @media (max-width: 992px) {
        .class-header {
            padding: 25px 20px;
        }

        .class-title {
            font-size: 2rem;
        }
    }

    @media (max-width: 768px) {
        .class-header {
            padding: 20px 15px;
        }

        .class-title {
            font-size: 1.8rem;
        }
    }

    @media (max-width: 576px) {
        .class-header {
            padding: 15px 12px;
        }

        .class-title {
            font-size: 1.5rem;
        }
    }

    .class-meta {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: var(--spacing-lg);
    }

    .class-description {
        margin-bottom: var(--spacing-lg);
        color: rgba(255, 255, 255, 0.9);
    }

    @media (max-width: 576px) {
        .class-meta {
            font-size: 0.9rem;
        }

        .class-description {
            font-size: 0.9rem;
        }
    }

    .tab-content {
        background-color: #fff;
        border-radius: 0 0 8px 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }


    @media (max-width: 768px) {
        .nav-tabs {
            flex-wrap: nowrap;
            overflow-x: auto;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
        }

        .nav-tabs .nav-link {
            padding: 0.5rem 0.75rem;
            font-size: 0.9rem;
        }

        .nav-tabs .nav-link i {
            margin-right: 5px;
        }
    }

    @media (max-width: 576px) {
        .nav-tabs .nav-link {
            padding: 0.5rem 0.5rem;
            font-size: 0.8rem;
        }

        .nav-tabs .nav-link span {
            display: none;
        }
    }

    .material-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .material-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .material-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .material-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .material-type {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .material-description {
        color: #333;
        margin-bottom: 10px;
    }

    .quiz-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .quiz-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .quiz-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .quiz-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .quiz-stats {
        font-size: 0.85rem;
        color: #6c757d;
        display: flex;
        gap: 15px;
    }

    .quiz-description {
        color: #333;
        margin-bottom: 10px;
    }

    .participant-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .participant-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .participant-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: #6c757d;
    }

    .participant-name {
        font-weight: 600;
    }

    .participant-role {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .role-section {
        margin-bottom: 20px;
    }

    .role-title {
        font-size: 1.2rem;
        margin-bottom: 10px;
        color: #333;
    }

    /* Assignment styles */
    .assignment-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .assignment-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .assignment-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .assignment-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .assignment-meta {
        font-size: 0.85rem;
        color: #6c757d;
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .assignment-description {
        color: #333;
        margin-bottom: 10px;
    }

    .assignment-status {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-right: 10px;
    }

    .assignment-status.submitted {
        background-color: #e8f5e9;
        color: #4CAF50;
    }

    .assignment-status.graded {
        background-color: #e3f2fd;
        color: #2196F3;
    }

    .assignment-status.returned {
        background-color: #fff3e0;
        color: #ff9800;
    }

    .assignment-status.not-submitted {
        background-color: #f5f5f5;
        color: #757575;
    }

    .assignment-status.overdue {
        background-color: #ffebee;
        color: #f44336;
    }

    /* File type button styles */
    .file-type-buttons {
        gap: 8px;
    }

    .file-type-buttons .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-weight: 500;
        transition: all 0.2s ease-in-out;
    }

    .file-type-buttons .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .file-type-buttons .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    /* Custom button colors */
    .btn-purple {
        background-color: #6f42c1;
        border-color: #6f42c1;
        color: #fff;
    }

    .btn-purple:hover {
        background-color: #5a359a;
        border-color: #5a359a;
        color: #fff;
    }

    /* Responsive file buttons */
    @media (max-width: 576px) {
        .file-type-buttons {
            flex-direction: column;
            align-items: stretch;
        }

        .file-type-buttons .btn {
            margin-bottom: 0.25rem;
            font-size: 0.8rem;
        }
    }

    /* Discussion styles */
    .discussion-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .discussion-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .discussion-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .discussion-title {
        font-weight: 600;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
    }

    .discussion-meta {
        font-size: 0.85rem;
        color: #6c757d;
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .discussion-preview {
        color: #333;
        margin-bottom: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        max-height: 3em;
    }

    .discussion-badge {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-left: 10px;
    }

    .discussion-badge.announcement {
        background-color: #fff3e0;
        color: #ff9800;
    }

    .discussion-badge.pinned {
        background-color: #e3f2fd;
        color: #2196F3;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="welcome-section">
                <h1 class=""><i class="fas fa-chalkboard-teacher"></i> Detail Kelas</h1>
                <div class="d-flex flex-wrap gap-2">
                    <a href="classroom.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> <span class="d-none d-sm-inline">Kembali ke Daftar Kelas</span>
                    </a>
                </div>
            </div>

            <div class="class-header">
                <div class="class-title"><?= htmlspecialchars($class['title']) ?></div>
                <div class="class-meta">
                    <div><strong>Topik:</strong> <?= htmlspecialchars($class['training_topic']) ?></div>
                    <div><strong>Tipe:</strong> <?= htmlspecialchars($class['training_type']) ?></div>
                    <?php if (!empty($class['start_date'])): ?>
                        <div><strong>Tanggal Mulai:</strong> <?= date('d M Y', strtotime($class['start_date'])) ?></div>
                    <?php endif; ?>
                    <?php if (!empty($class['end_date'])): ?>
                        <div><strong>Tanggal Selesai:</strong> <?= date('d M Y', strtotime($class['end_date'])) ?></div>
                    <?php endif; ?>
                </div>
                <div class="class-description">
                    <?= !empty($class['description']) ? nl2br(htmlspecialchars($class['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                </div>
                <a href="leave_class.php?id=<?= $class_id ?>" class="btn btn-danger">
                        <i class="fas fa-sign-out-alt"></i> <span class="d-none d-sm-inline">Keluar dari Kelas</span>
                    </a>
            </div>

            <ul class="nav nav-tabs" id="classTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials" type="button" role="tab" aria-controls="materials" aria-selected="true">
                        <i class="fas fa-book"></i> <span>Materi</span> (<?= count($materials) ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="quizzes-tab" data-bs-toggle="tab" data-bs-target="#quizzes" type="button" role="tab" aria-controls="quizzes" aria-selected="false">
                        <i class="fas fa-question-circle"></i> <span>Kuis</span> (<?= count($quizzes) ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="assignments-tab" data-bs-toggle="tab" data-bs-target="#assignments" type="button" role="tab" aria-controls="assignments" aria-selected="false">
                        <i class="fas fa-tasks"></i> <span>Tugas</span> (<?= count($assignments) ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="discussions-tab" data-bs-toggle="tab" data-bs-target="#discussions" type="button" role="tab" aria-controls="discussions" aria-selected="false">
                        <i class="fas fa-comments"></i> <span>Diskusi</span> (<?= count($discussions) ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="participants-tab" data-bs-toggle="tab" data-bs-target="#participants" type="button" role="tab" aria-controls="participants" aria-selected="false">
                        <i class="fas fa-users"></i> <span>Peserta</span> (<?= count($participants) ?>)
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="classTabsContent">
                <div class="tab-pane fade show active" id="materials" role="tabpanel" aria-labelledby="materials-tab">
                    <h3 class="mb-3">Materi Pembelajaran</h3>

                    <?php if (empty($materials)): ?>
                        <div class="alert alert-info">
                            Belum ada materi yang tersedia untuk kelas ini.
                        </div>
                    <?php else: ?>
                        <?php foreach ($materials as $material): ?>
                            <div class="material-card">
                                <div class="material-header">
                                    <div>
                                        <div class="material-title"><?= htmlspecialchars($material['title']) ?></div>
                                        <div class="material-type">
                                            <?php
                                                $type_labels = [
                                                    'document' => '<i class="fas fa-file-alt"></i> Dokumen',
                                                    'video' => '<i class="fas fa-video"></i> Video',
                                                    'link' => '<i class="fas fa-link"></i> Tautan',
                                                    'other' => '<i class="fas fa-file"></i> Lainnya'
                                                ];
                                                echo $type_labels[$material['type']] ?? $material['type'];
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="material-description">
                                    <?= !empty($material['description']) ? nl2br(htmlspecialchars($material['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                                </div>
                                <div class="d-flex justify-content-end file-type-buttons">
                                    <?php if ($material['type'] == 'link' && !empty($material['external_url'])): ?>
                                        <a href="<?= htmlspecialchars($material['external_url']) ?>" class="btn btn-primary" target="_blank">
                                            <i class="fas fa-external-link-alt"></i> Buka Tautan
                                        </a>
                                    <?php elseif (!empty($material['file_path'])): ?>
                                        <?= generateFileButtons($material['id'], $material['file_path']) ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="tab-pane fade" id="assignments" role="tabpanel" aria-labelledby="assignments-tab">
                    <h3 class="mb-3">Tugas</h3>

                    <?php if (empty($assignments)): ?>
                        <div class="alert alert-info">
                            Belum ada tugas yang tersedia untuk kelas ini.
                        </div>
                    <?php else: ?>
                        <?php foreach ($assignments as $assignment): ?>
                            <div class="assignment-card">
                                <div class="assignment-header">
                                    <div>
                                        <div class="assignment-title"><?= htmlspecialchars($assignment['title']) ?></div>
                                        <div class="assignment-meta">
                                            <span><i class="fas fa-calendar-alt"></i> Tenggat: <?= date('d M Y H:i', strtotime($assignment['due_date'])) ?></span>
                                            <?php if ($assignment['points']): ?>
                                                <span><i class="fas fa-star"></i> Poin: <?= $assignment['points'] ?></span>
                                            <?php endif; ?>

                                            <?php
                                                $status_class = 'not-submitted';
                                                $status_text = 'Belum Dikumpulkan';

                                                if ($assignment['submission_count'] > 0) {
                                                    if ($assignment['submission_status'] == 'submitted') {
                                                        $status_class = 'submitted';
                                                        $status_text = 'Dikumpulkan';
                                                    } elseif ($assignment['submission_status'] == 'graded') {
                                                        $status_class = 'graded';
                                                        $status_text = 'Dinilai: ' . $assignment['submission_grade'];
                                                    } elseif ($assignment['submission_status'] == 'returned') {
                                                        $status_class = 'returned';
                                                        $status_text = 'Dikembalikan';
                                                    }
                                                } elseif (strtotime($assignment['due_date']) < time()) {
                                                    $status_class = 'overdue';
                                                    $status_text = 'Terlambat';
                                                }
                                            ?>

                                            <span class="assignment-status <?= $status_class ?>">
                                                <?= $status_text ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="assignment-description">
                                    <?= !empty($assignment['description']) ? nl2br(htmlspecialchars($assignment['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <a href="classroom_assignment.php?id=<?= $assignment['id'] ?>" class="btn btn-primary">
                                        <?php if ($assignment['submission_count'] > 0): ?>
                                            <i class="fas fa-eye"></i> Lihat Tugas
                                        <?php else: ?>
                                            <i class="fas fa-edit"></i> Kerjakan Tugas
                                        <?php endif; ?>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="tab-pane fade" id="discussions" role="tabpanel" aria-labelledby="discussions-tab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>Diskusi</h3>
                        <a href="create_discussion.php?class_id=<?= $class_id ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Buat Diskusi Baru
                        </a>
                    </div>

                    <?php if (empty($discussions)): ?>
                        <div class="alert alert-info">
                            Belum ada diskusi yang tersedia untuk kelas ini.
                        </div>
                    <?php else: ?>
                        <?php foreach ($discussions as $discussion): ?>
                            <div class="discussion-card">
                                <div class="discussion-header">
                                    <div>
                                        <div class="discussion-title">
                                            <?= htmlspecialchars($discussion['title']) ?>
                                            <?php if ($discussion['is_announcement']): ?>
                                                <span class="discussion-badge announcement">Pengumuman</span>
                                            <?php endif; ?>
                                            <?php if ($discussion['is_pinned']): ?>
                                                <span class="discussion-badge pinned">Disematkan</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="discussion-meta">
                                            <span><i class="fas fa-user"></i> <?= htmlspecialchars($discussion['creator_name']) ?></span>
                                            <span><i class="fas fa-calendar-alt"></i> <?= date('d M Y', strtotime($discussion['created_at'])) ?></span>
                                            <span><i class="fas fa-comments"></i> <?= $discussion['comment_count'] ?> komentar</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="discussion-preview">
                                    <?= !empty($discussion['content']) ? nl2br(htmlspecialchars(substr($discussion['content'], 0, 200))) . (strlen($discussion['content']) > 200 ? '...' : '') : '<em>Tidak ada konten</em>' ?>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <a href="classroom_discussion.php?id=<?= $discussion['id'] ?>" class="btn btn-primary">
                                        <i class="fas fa-eye"></i> Lihat Diskusi
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="tab-pane fade" id="quizzes" role="tabpanel" aria-labelledby="quizzes-tab">
                    <h3 class="mb-3">Kuis</h3>

                    <?php if (empty($quizzes)): ?>
                        <div class="alert alert-info">
                            Belum ada kuis yang tersedia untuk kelas ini.
                        </div>
                    <?php else: ?>
                        <?php foreach ($quizzes as $quiz): ?>
                            <div class="quiz-card">
                                <div class="quiz-header">
                                    <div>
                                        <div class="quiz-title"><?= htmlspecialchars($quiz['title']) ?></div>
                                        <div class="quiz-stats">
                                            <span><i class="fas fa-question"></i> <?= $quiz['question_count'] ?> Pertanyaan</span>
                                            <?php if ($quiz['time_limit']): ?>
                                                <span><i class="fas fa-clock"></i> <?= $quiz['time_limit'] ?> menit</span>
                                            <?php endif; ?>
                                            <?php if ($quiz['passing_score']): ?>
                                                <span><i class="fas fa-check-circle"></i> Nilai kelulusan: <?= $quiz['passing_score'] ?>%</span>
                                            <?php endif; ?>
                                            <?php if ($quiz['attempt_count'] > 0): ?>
                                                <span><i class="fas fa-history"></i> <?= $quiz['attempt_count'] ?> percobaan</span>
                                            <?php endif; ?>
                                            <?php if ($quiz['best_score'] !== null): ?>
                                                <span><i class="fas fa-trophy"></i> Nilai terbaik: <?= $quiz['best_score'] ?>%</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="quiz-description">
                                    <?= !empty($quiz['description']) ? nl2br(htmlspecialchars($quiz['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <a href="take_quiz.php?id=<?= $quiz['id'] ?>" class="btn btn-primary">
                                        <?php if ($quiz['attempt_count'] > 0): ?>
                                            <i class="fas fa-redo"></i> Kerjakan Lagi
                                        <?php else: ?>
                                            <i class="fas fa-play"></i> Mulai Kuis
                                        <?php endif; ?>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="tab-pane fade" id="participants" role="tabpanel" aria-labelledby="participants-tab">
                    <div class="d-flex flex-wrap justify-content-between align-items-center mb-3">
                        <h3 class="mb-3 mb-md-0">Peserta Kelas</h3>
                        <a href="leave_class.php?id=<?= $class_id ?>" class="btn btn-danger">
                            <i class="fas fa-sign-out-alt"></i> <span class="d-none d-sm-inline">Keluar dari Kelas</span>
                        </a>
                    </div>

                    <?php if (!empty($instructors)): ?>
                        <div class="role-section">
                            <div class="role-title">Instruktur</div>
                            <?php foreach ($instructors as $instructor): ?>
                                <div class="participant-card">
                                    <div class="participant-info">
                                        <div class="participant-avatar">
                                            <?= strtoupper(substr($instructor['participant_name'], 0, 1)) ?>
                                        </div>
                                        <div>
                                            <div class="participant-name"><?= htmlspecialchars($instructor['participant_name']) ?></div>
                                            <div class="participant-role">Instruktur</div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($assistants)): ?>
                        <div class="role-section">
                            <div class="role-title">Asisten</div>
                            <?php foreach ($assistants as $assistant): ?>
                                <div class="participant-card">
                                    <div class="participant-info">
                                        <div class="participant-avatar">
                                            <?= strtoupper(substr($assistant['participant_name'], 0, 1)) ?>
                                        </div>
                                        <div>
                                            <div class="participant-name"><?= htmlspecialchars($assistant['participant_name']) ?></div>
                                            <div class="participant-role">Asisten</div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($students)): ?>
                        <div class="role-section">
                            <div class="role-title">Peserta</div>
                            <?php foreach ($students as $student): ?>
                                <div class="participant-card">
                                    <div class="participant-info">
                                        <div class="participant-avatar">
                                            <?= strtoupper(substr($student['participant_name'], 0, 1)) ?>
                                        </div>
                                        <div>
                                            <div class="participant-name"><?= htmlspecialchars($student['participant_name']) ?></div>
                                            <div class="participant-role">Peserta</div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add any JavaScript functionality here
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
