<?php
// File: admin/get_attendance_data.php
// Deskripsi: Script untuk mendapatkan data absensi Training Internal secara real-time

// Include config dan security
include '../config/config.php';
include 'security.php';

// Cek apakah training ID ada
if (!isset($_GET['training_id']) || empty($_GET['training_id'])) {
    echo '<div class="alert alert-warning">ID Training tidak valid</div>';
    exit;
}

$training_id = $_GET['training_id'];
$attendance_data = [];

// Ambil data absensi
$query_attendance = "SELECT ota.id, ota.nik, ota.nama, ota.check_in, ota.check_out, ota.status, ota.keterangan,
                     k.dept, k.bagian, k.jabatan
                     FROM offline_training_attendance ota
                     LEFT JOIN karyawan k ON ota.karyawan_id = k.id
                     WHERE ota.offline_training_id = ?
                     ORDER BY ota.nama ASC";
$stmt_attendance = $conn->prepare($query_attendance);
$stmt_attendance->bind_param("i", $training_id);
$stmt_attendance->execute();
$result_attendance = $stmt_attendance->get_result();

while ($row = $result_attendance->fetch_assoc()) {
    $attendance_data[] = $row;
}

// Jika tidak ada data
if (empty($attendance_data)) {
    echo '<div class="alert alert-info">Belum ada data kehadiran untuk training ini</div>';
} else {
    // Tampilkan tabel kehadiran
    ?>
    <div class="table-responsive">
        <table class="table table-bordered table-hover" id="attendance_table" style="width: 100%; table-layout: fixed;">
            <colgroup>
                <col style="width: 5%"> <!-- No -->
                <col style="width: 15%"> <!-- NIK -->
                <col style="width: 20%"> <!-- Nama -->
                <col style="width: 15%"> <!-- Departemen -->
                <col style="width: 15%"> <!-- Check In -->
                <col style="width: 15%"> <!-- Check Out -->
                <col style="width: 15%"> <!-- Status -->
            </colgroup>
            <thead>
                <tr>
                    <th>No</th>
                    <th>NIK</th>
                    <th>Nama</th>
                    <th>Departemen</th>
                    <th>Check In</th>
                    <th>Check Out</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php $no = 1; foreach ($attendance_data as $attendance): ?>
                <tr>
                    <td><?php echo $no++; ?></td>
                    <td><?php echo htmlspecialchars($attendance['nik']); ?></td>
                    <td><?php echo htmlspecialchars($attendance['nama']); ?></td>
                    <td><?php echo htmlspecialchars($attendance['dept']); ?></td>
                    <td>
                        <?php 
                        if ($attendance['check_in']) {
                            echo date('d-m-Y H:i', strtotime($attendance['check_in']));
                        } else {
                            echo '<span class="text-muted">-</span>';
                        }
                        ?>
                    </td>
                    <td>
                        <?php 
                        if ($attendance['check_out']) {
                            echo date('d-m-Y H:i', strtotime($attendance['check_out']));
                        } else {
                            echo '<span class="text-muted">-</span>';
                        }
                        ?>
                    </td>
                    <td>
                        <?php 
                        $status_class = '';
                        switch (strtolower($attendance['status'])) {
                            case 'hadir':
                                $status_class = 'badge bg-success';
                                break;
                            case 'izin':
                                $status_class = 'badge bg-warning';
                                break;
                            case 'sakit':
                                $status_class = 'badge bg-info';
                                break;
                            case 'alpha':
                                $status_class = 'badge bg-danger';
                                break;
                            default:
                                $status_class = 'badge bg-secondary';
                        }
                        echo "<span class='{$status_class}'>" . htmlspecialchars(ucfirst($attendance['status'])) . "</span>";
                        ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>  
    <?php
}
// Akhir file
?>
