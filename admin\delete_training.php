<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

if (isset($_GET['id'])) {
    $training_id = $_GET['id'];

    // Mulai transaction untuk memastikan integritas data
    $conn->begin_transaction();

    try {
        // Ambil data training sebelum dihapus untuk log
        $query = "SELECT training_topic, start_date, end_date, status FROM training_submissions WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            throw new Exception('Training dengan ID tersebut tidak ditemukan');
        }

        $training = $result->fetch_assoc();
        $training_topic = $training['training_topic'];
        $training_date = $training['start_date'] ?? $training['end_date'] ?? 'Tidak ditentukan';
        $training_status = $training['status'];

        // Hapus data dari tabel participants terlebih dahulu (karena foreign key)
        $delete_participants = "DELETE FROM participants WHERE training_id = ?";
        $stmt = $conn->prepare($delete_participants);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();

        // Hapus data dari tabel training_submissions
        $delete_training = "DELETE FROM training_submissions WHERE id = ?";
        $stmt = $conn->prepare($delete_training);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();

        // Log aktivitas
        if (file_exists('../config/activity_logger.php')) {
            include_once '../config/activity_logger.php';
            if (function_exists('log_activity')) {
                log_activity($_SESSION['user_id'], "Menghapus training: {$training_topic} (Tanggal: {$training_date}, Status: {$training_status})", "training", [
                    'training_id' => $training_id,
                    'training_topic' => $training_topic,
                    'training_date' => $training_date,
                    'training_status' => $training_status
                ]);
            }
        }

        // Commit transaction jika semua operasi berhasil
        $conn->commit();

        $_SESSION['success'] = "Training berhasil dihapus.";
        header("Location: training_management.php");
        exit();

    } catch (Exception $e) {
        // Rollback jika terjadi error
        $conn->rollback();

        $_SESSION['error'] = "Gagal menghapus training: " . $e->getMessage();
        header("Location: training_management.php");
        exit();

    } finally {
        $conn->close();
    }

} else {
    $_SESSION['error'] = "ID training tidak valid.";
    header("Location: training_management.php");
    exit();
}
?>
