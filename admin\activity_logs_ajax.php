<?php
session_start();
include '../config/config.php';
include '../config/activity_logger.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Cek apakah request adalah AJAX
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['action'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Invalid request']);
    exit();
}

// Tangani berbagai aksi
$action = $_POST['action'];

switch ($action) {
    case 'get_logs':
        // Ambil parameter
        $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 50;
        $offset = ($page - 1) * $limit;
        $category = isset($_POST['category']) ? $_POST['category'] : 'all';
        $date_from = isset($_POST['date_from']) ? $_POST['date_from'] : '';
        $date_to = isset($_POST['date_to']) ? $_POST['date_to'] : '';
        $search = isset($_POST['search']) ? $_POST['search'] : '';
        
        // Siapkan filter
        $filters = [
            'category' => $category,
            'date_from' => $date_from,
            'date_to' => $date_to,
            'search' => $search
        ];
        
        // Ambil log aktivitas
        $logs = get_activity_logs($filters, $limit, $offset);
        $total_logs = get_activity_logs_count($filters);
        $total_pages = ceil($total_logs / $limit);
        
        // Tambahkan nama kategori ke setiap log
        $categories = get_activity_log_categories();
        foreach ($logs as &$log) {
            if (isset($categories[$log['category']])) {
                $log['category_name'] = $categories[$log['category']];
            }
        }
        
        // Siapkan data pagination
        $pagination = [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'limit' => $limit,
            'total_logs' => $total_logs
        ];
        
        // Kirim response
        header('Content-Type: application/json');
        echo json_encode([
            'logs' => $logs,
            'pagination' => $pagination,
            'total_logs' => $total_logs
        ]);
        break;
        
    default:
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Unknown action']);
        break;
}
?>
