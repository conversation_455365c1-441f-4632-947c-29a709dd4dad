<?php
// File: admin/export_card_numbers.php
// Deskripsi: Script untuk mengekspor data nomor kartu RFID ke Excel

// Aktifkan output buffering
ob_start();

include '../config/config.php';
include 'security.php';

// Security check sudah dilakukan di security.php

// Ambil filter dari query string
$search = isset($_GET['search']) ? $_GET['search'] : '';
$dept = isset($_GET['dept']) ? $_GET['dept'] : '';

// Buat query dasar
$query_karyawan = "SELECT nik, nama, dept, bagian, jabatan, card_number FROM karyawan WHERE 1=1";

// Tambahkan filter pencarian
if (!empty($search)) {
    $search_param = "%{$search}%";
    $query_karyawan .= " AND (nik LIKE ? OR nama LIKE ? OR card_number LIKE ?)";
}

// Tambahkan filter departemen
if (!empty($dept)) {
    $query_karyawan .= " AND dept = ?";
}

// Tambahkan sorting
$query_karyawan .= " ORDER BY nama ASC";

// Prepare statement
$stmt_karyawan = $conn->prepare($query_karyawan);

// Bind parameter
if (!empty($search) && !empty($dept)) {
    $stmt_karyawan->bind_param("ssss", $search_param, $search_param, $search_param, $dept);
} elseif (!empty($search)) {
    $stmt_karyawan->bind_param("sss", $search_param, $search_param, $search_param);
} elseif (!empty($dept)) {
    $stmt_karyawan->bind_param("s", $dept);
}

$stmt_karyawan->execute();
$result_karyawan = $stmt_karyawan->get_result();

// Require library PhpSpreadsheet
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// Buat objek spreadsheet baru
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set judul sheet
$sheet->setTitle('Data Nomor Kartu RFID');

// Set header
$sheet->setCellValue('A1', 'NO');
$sheet->setCellValue('B1', 'NIK');
$sheet->setCellValue('C1', 'NAMA');
$sheet->setCellValue('D1', 'DEPARTEMEN');
$sheet->setCellValue('E1', 'BAGIAN');
$sheet->setCellValue('F1', 'JABATAN');
$sheet->setCellValue('G1', 'NOMOR KARTU RFID');

// Style header
$headerStyle = [
    'font' => [
        'bold' => true,
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
        ],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => [
            'rgb' => 'E2EFDA',
        ],
    ],
];

$sheet->getStyle('A1:G1')->applyFromArray($headerStyle);

// Isi data
$row = 2;
$no = 1;

while ($karyawan = $result_karyawan->fetch_assoc()) {
    $sheet->setCellValue('A' . $row, $no++);
    $sheet->setCellValue('B' . $row, $karyawan['nik']);
    $sheet->setCellValue('C' . $row, $karyawan['nama']);
    $sheet->setCellValue('D' . $row, $karyawan['dept']);
    $sheet->setCellValue('E' . $row, $karyawan['bagian']);
    $sheet->setCellValue('F' . $row, $karyawan['jabatan']);
    $sheet->setCellValue('G' . $row, $karyawan['card_number'] ?: '-');

    $row++;
}

// Style data
$dataStyle = [
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
        ],
    ],
];

$sheet->getStyle('A2:G' . ($row - 1))->applyFromArray($dataStyle);

// Set lebar kolom
$sheet->getColumnDimension('A')->setWidth(5);
$sheet->getColumnDimension('B')->setWidth(15);
$sheet->getColumnDimension('C')->setWidth(25);
$sheet->getColumnDimension('D')->setWidth(15);
$sheet->getColumnDimension('E')->setWidth(15);
$sheet->getColumnDimension('F')->setWidth(20);
$sheet->getColumnDimension('G')->setWidth(20);

// Set alignment
$sheet->getStyle('A2:A' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
$sheet->getStyle('B2:B' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
$sheet->getStyle('G2:G' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

// Tambahkan informasi ekspor
$row += 2;
$sheet->setCellValue('A' . $row, 'Diekspor oleh:');
$sheet->setCellValue('B' . $row, $_SESSION['name']);

$row++;
$sheet->setCellValue('A' . $row, 'Tanggal ekspor:');
$sheet->setCellValue('B' . $row, date('d-m-Y H:i:s'));

// Pastikan tidak ada output sebelum header
ob_clean();

// Buat file Excel
$filename = 'Data_Nomor_Kartu_RFID_' . date('Y-m-d') . '.xlsx';

// Set header untuk download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');
header('Pragma: public');

// Tulis ke output
$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit;
?>
