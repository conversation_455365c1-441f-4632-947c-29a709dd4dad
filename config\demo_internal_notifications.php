<?php
/**
 * Demo untuk menunjukkan cara kerja notifikasi internal
 */

// Disable session untuk testing
define('NO_SESSION', true);

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/../includes/notification_helper.php';

echo "🔔 DEMO NOTIFIKASI INTERNAL SYSTEM\n";
echo "===================================\n\n";

// Step 1: Buat beberapa notifikasi demo untuk user
echo "1️⃣ Membuat notifikasi demo untuk user...\n";

// Cari beberapa user aktif
$users_query = "SELECT id, name, email FROM users WHERE is_active = 1 LIMIT 3";
$users_result = $conn->query($users_query);

$demo_users = [];
while ($user = $users_result->fetch_assoc()) {
    $demo_users[] = $user;
}

if (empty($demo_users)) {
    echo "❌ Tidak ada user aktif ditemukan\n";
    exit(1);
}

// Buat berbagai jenis notifikasi
$notification_types = [
    [
        'title' => 'Training Baru Tersedia',
        'message' => 'Training "Digital Marketing Fundamentals" telah dibuka untuk pendaftaran. Segera daftar sebelum kuota penuh!',
        'type' => 'info'
    ],
    [
        'title' => 'Pengajuan Training Disetujui',
        'message' => 'Selamat! Pengajuan training Anda telah disetujui oleh atasan. Silakan cek detail training di dashboard.',
        'type' => 'success'
    ],
    [
        'title' => 'Deadline Assignment Mendekati',
        'message' => 'Anda memiliki assignment yang akan berakhir dalam 2 hari. Pastikan untuk menyelesaikan tepat waktu.',
        'type' => 'warning'
    ],
    [
        'title' => 'Training Dibatalkan',
        'message' => 'Maaf, training yang dijadwalkan hari ini dibatalkan karena trainer berhalangan. Akan ada penjadwalan ulang.',
        'type' => 'error'
    ]
];

$created_notifications = [];

foreach ($demo_users as $index => $user) {
    $notification = $notification_types[$index % count($notification_types)];
    
    try {
        $created = createNotification(
            $user['id'],
            null, // General notification
            $notification['title'],
            $notification['message'],
            $notification['type']
        );
        
        if ($created) {
            echo "   ✅ Notifikasi '{$notification['title']}' dibuat untuk {$user['name']}\n";
            $created_notifications[] = [
                'user' => $user,
                'notification' => $notification
            ];
        } else {
            echo "   ❌ Gagal membuat notifikasi untuk {$user['name']}\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Error untuk {$user['name']}: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Step 2: Tampilkan cara melihat notifikasi
echo "2️⃣ Cara melihat notifikasi di UI...\n";
echo "   📍 Lokasi notifikasi dalam sistem:\n";
echo "   \n";
echo "   🔔 DROPDOWN NOTIFIKASI (di header):\n";
echo "      - File: includes/notifications_dropdown.php\n";
echo "      - Tampil di: Semua halaman (header navigation)\n";
echo "      - Fitur: Badge merah dengan jumlah notifikasi belum dibaca\n";
echo "      - Aksi: Klik bell icon untuk melihat 5 notifikasi terbaru\n";
echo "   \n";
echo "   📄 HALAMAN SEMUA NOTIFIKASI:\n";
echo "      - URL: [role]/all_notifications.php\n";
echo "      - Contoh: pemohon/all_notifications.php\n";
echo "      - Fitur: Daftar lengkap semua notifikasi\n";
echo "      - Aksi: Mark as read, mark all as read\n";
echo "   \n";

// Step 3: Simulasi penggunaan
echo "3️⃣ Simulasi penggunaan notifikasi...\n";

foreach ($created_notifications as $item) {
    $user = $item['user'];
    $notification = $item['notification'];
    
    echo "   👤 User: {$user['name']}\n";
    
    // Ambil notifikasi belum dibaca
    $unread = getUnreadNotifications($user['id'], 5);
    echo "      📬 Notifikasi belum dibaca: " . count($unread) . "\n";
    
    if (!empty($unread)) {
        $latest = $unread[0];
        echo "      📝 Terbaru: {$latest['title']}\n";
        echo "      🕒 Waktu: " . date('d M Y H:i', strtotime($latest['created_at'])) . "\n";
        
        // Simulasi mark as read
        $marked = markNotificationAsRead($latest['id'], $user['id']);
        if ($marked) {
            echo "      ✅ Notifikasi ditandai sudah dibaca\n";
        }
    }
    echo "\n";
}

// Step 4: Tampilkan statistik
echo "4️⃣ Statistik notifikasi sistem...\n";

$stats_query = "SELECT
                    COUNT(*) as total,
                    COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread,
                    COUNT(CASE WHEN is_read = 1 THEN 1 END) as read_count,
                    COUNT(DISTINCT user_id) as users_with_notifications
                FROM training_notifications";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

echo "   📊 Total notifikasi: {$stats['total']}\n";
echo "   📬 Belum dibaca: {$stats['unread']}\n";
echo "   ✅ Sudah dibaca: {$stats['read_count']}\n";
echo "   👥 User dengan notifikasi: {$stats['users_with_notifications']}\n";

echo "\n";

// Step 5: Cara mengintegrasikan notifikasi dalam kode
echo "5️⃣ Cara mengintegrasikan notifikasi dalam kode...\n";
echo "   \n";
echo "   📝 MEMBUAT NOTIFIKASI:\n";
echo "   ```php\n";
echo "   // General notification\n";
echo "   createNotification(\$user_id, null, 'Title', 'Message', 'info');\n";
echo "   \n";
echo "   // Class-specific notification\n";
echo "   createNotification(\$user_id, \$class_id, 'Title', 'Message', 'success');\n";
echo "   ```\n";
echo "   \n";
echo "   📖 MEMBACA NOTIFIKASI:\n";
echo "   ```php\n";
echo "   // Ambil 5 notifikasi terbaru\n";
echo "   \$notifications = getUnreadNotifications(\$user_id, 5);\n";
echo "   \n";
echo "   // Tampilkan badge count\n";
echo "   \$count = count(\$notifications);\n";
echo "   ```\n";
echo "   \n";
echo "   ✅ MARK AS READ:\n";
echo "   ```php\n";
echo "   markNotificationAsRead(\$notification_id, \$user_id);\n";
echo "   ```\n";
echo "   \n";

// Step 6: Contoh implementasi dalam workflow
echo "6️⃣ Contoh implementasi dalam workflow training...\n";
echo "   \n";
echo "   🔄 SAAT TRAINING DISUBMIT:\n";
echo "   ```php\n";
echo "   // Notifikasi ke dept head\n";
echo "   createNotification(\n";
echo "       \$dept_head_id,\n";
echo "       null,\n";
echo "       'Pengajuan Training Baru',\n";
echo "       'Ada pengajuan training baru yang memerlukan persetujuan Anda',\n";
echo "       'info'\n";
echo "   );\n";
echo "   ```\n";
echo "   \n";
echo "   ✅ SAAT TRAINING DISETUJUI:\n";
echo "   ```php\n";
echo "   // Notifikasi ke pemohon\n";
echo "   createNotification(\n";
echo "       \$requester_id,\n";
echo "       null,\n";
echo "       'Training Disetujui',\n";
echo "       'Pengajuan training Anda telah disetujui',\n";
echo "       'success'\n";
echo "   );\n";
echo "   ```\n";
echo "   \n";

echo "🎯 CARA MELIHAT NOTIFIKASI BEKERJA:\n";
echo "=====================================\n";
echo "1. 🌐 Buka browser dan login ke sistem\n";
echo "2. 🔔 Lihat icon bell di header navigation\n";
echo "3. 🔴 Jika ada badge merah, berarti ada notifikasi baru\n";
echo "4. 👆 Klik icon bell untuk melihat dropdown notifikasi\n";
echo "5. 📄 Klik 'Lihat Semua Notifikasi' untuk halaman lengkap\n";
echo "6. ✅ Klik notifikasi untuk mark as read\n";
echo "7. 🔄 Refresh halaman untuk melihat perubahan badge\n";

echo "\n🧹 Membersihkan notifikasi demo...\n";

// Cleanup demo notifications
$demo_titles = ['Training Baru Tersedia', 'Pengajuan Training Disetujui', 'Deadline Assignment Mendekati', 'Training Dibatalkan'];
$title_placeholders = str_repeat('?,', count($demo_titles) - 1) . '?';
$cleanup_query = "DELETE FROM training_notifications WHERE title IN ($title_placeholders)";
$cleanup_stmt = $conn->prepare($cleanup_query);
$cleanup_stmt->bind_param(str_repeat('s', count($demo_titles)), ...$demo_titles);
$cleanup_stmt->execute();

echo "✅ Demo notifikasi dibersihkan\n";

echo "\n🚀 NOTIFIKASI INTERNAL SIAP DIGUNAKAN!\n";

$conn->close();
?>
