<?php
/**
 * Script untuk mengecek role user yang sedang login
 */

include '../config/config.php';
include 'security.php';

echo "<h2>User Role Check</h2>";

echo "<h3>Session Information:</h3>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Key</th><th>Value</th></tr>";

foreach ($_SESSION as $key => $value) {
    echo "<tr>";
    echo "<td><strong>$key</strong></td>";
    echo "<td>" . htmlspecialchars($value) . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>Database User Information:</h3>";
$query = "SELECT id, name, email, nik, role_id, dept, bagian, jabatan FROM users WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $user = $result->fetch_assoc();
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    
    foreach ($user as $key => $value) {
        echo "<tr>";
        echo "<td><strong>$key</strong></td>";
        echo "<td>" . htmlspecialchars($value) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>User not found in database!</p>";
}

$stmt->close();

echo "<h3>Role Validation Check:</h3>";
$role_id = $_SESSION['role_id'] ?? 'Not set';
echo "<p><strong>Current role_id:</strong> $role_id</p>";

// Check what roles are allowed in security.php
$allowed_roles = [2, 3, 4, 5, 99];
echo "<p><strong>Allowed roles in admin/security.php:</strong> " . implode(', ', $allowed_roles) . "</p>";

if (in_array($role_id, $allowed_roles)) {
    echo "<p style='color: green;'>✅ Your role is allowed in admin/security.php</p>";
} else {
    echo "<p style='color: red;'>❌ Your role is NOT allowed in admin/security.php</p>";
}

// Check what the API expects
echo "<p><strong>API expects:</strong> role_id == 1 (this is the problem!)</p>";

if ($role_id == 1) {
    echo "<p style='color: green;'>✅ Your role matches API expectation</p>";
} else {
    echo "<p style='color: red;'>❌ Your role does NOT match API expectation</p>";
}

echo "<h3>Solution:</h3>";
echo "<p>The API files are checking for <code>role_id == 1</code> but your admin security allows roles [2, 3, 4, 5, 99].</p>";
echo "<p>We need to update the API files to match the security.php validation.</p>";

$conn->close();
?>
