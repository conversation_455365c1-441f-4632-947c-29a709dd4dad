<?php
/**
 * Simple PHP Database Backup
 * 
 * Script sederhana untuk membuat backup database menggunakan PHP murni
 * tanpa bergantung pada mysqldump
 */

// Include configuration
include_once '../config/config.php';

// Set header for browser output
header('Content-Type: text/html; charset=utf-8');
echo '<h1>Simple PHP Database Backup</h1>';

// Function to create backup
function createSimpleBackup($db_host, $db_user, $db_pass, $db_name, $conn, $backupDir = '../backups') {
    try {
        // Check if backup directory exists, create if not
        if (!file_exists($backupDir)) {
            if (!mkdir($backupDir, 0755, true)) {
                throw new Exception("Tidak dapat membuat direktori backup: $backupDir");
            }
        }
        
        // Create backup filename
        $timestamp = date('Y-m-d_H-i-s');
        $backupFileName = "db_{$db_name}_backup_{$timestamp}.sql";
        $backupFilePath = "{$backupDir}/{$backupFileName}";
        
        // Create backup file
        $backup_file = fopen($backupFilePath, 'w');
        if (!$backup_file) {
            throw new Exception("Tidak dapat membuat file backup: $backupFilePath");
        }
        
        // Add header
        fwrite($backup_file, "-- Database Backup for {$db_name}\n");
        fwrite($backup_file, "-- Generated on " . date('Y-m-d H:i:s') . "\n");
        fwrite($backup_file, "-- Simple PHP Backup\n\n");
        
        // Set database
        fwrite($backup_file, "USE `{$db_name}`;\n\n");
        
        // Get all tables
        $tables_result = $conn->query("SHOW TABLES");
        if (!$tables_result) {
            throw new Exception("Gagal mendapatkan daftar tabel: " . $conn->error);
        }
        
        $tables = [];
        while ($row = $tables_result->fetch_array()) {
            $tables[] = $row[0];
        }
        
        echo "<p>Ditemukan " . count($tables) . " tabel.</p>";
        echo "<ul>";
        
        // Process each table
        foreach ($tables as $table) {
            echo "<li>Memproses tabel: {$table}...</li>";
            flush();
            
            // Get create table statement
            $create_table_result = $conn->query("SHOW CREATE TABLE `{$table}`");
            if (!$create_table_result) {
                throw new Exception("Gagal mendapatkan CREATE TABLE untuk {$table}: " . $conn->error);
            }
            
            $create_table_row = $create_table_result->fetch_array();
            $create_table_sql = $create_table_row[1];
            
            // Write create table statement
            fwrite($backup_file, "-- Table structure for table `{$table}`\n");
            fwrite($backup_file, "DROP TABLE IF EXISTS `{$table}`;\n");
            fwrite($backup_file, $create_table_sql . ";\n\n");
            
            // Get table data
            $data_result = $conn->query("SELECT * FROM `{$table}`");
            if (!$data_result) {
                throw new Exception("Gagal mendapatkan data untuk {$table}: " . $conn->error);
            }
            
            if ($data_result->num_rows > 0) {
                fwrite($backup_file, "-- Data for table `{$table}`\n");
                
                // Process rows in batches to avoid memory issues
                $batch_size = 100;
                $row_count = 0;
                $total_rows = $data_result->num_rows;
                
                while ($row = $data_result->fetch_assoc()) {
                    if ($row_count % $batch_size == 0) {
                        // Start a new INSERT statement for each batch
                        if ($row_count > 0) {
                            fwrite($backup_file, ";\n");
                        }
                        fwrite($backup_file, "INSERT INTO `{$table}` VALUES\n");
                    }
                    
                    // Start row
                    fwrite($backup_file, "(");
                    
                    // Process each column
                    $columns = [];
                    foreach ($row as $column) {
                        if ($column === null) {
                            $columns[] = "NULL";
                        } else {
                            $columns[] = "'" . $conn->real_escape_string($column) . "'";
                        }
                    }
                    
                    // Join columns and end row
                    fwrite($backup_file, implode(",", $columns));
                    
                    // If this is the last row in the batch or the last row overall, end with semicolon, otherwise comma
                    $row_count++;
                    if ($row_count == $total_rows) {
                        fwrite($backup_file, ");\n\n");
                    } else if ($row_count % $batch_size == 0) {
                        // End of batch but not end of data
                        fwrite($backup_file, ")");
                    } else {
                        fwrite($backup_file, "),\n");
                    }
                }
            }
        }
        
        echo "</ul>";
        
        // Close file
        fclose($backup_file);
        
        // Check if backup was successful
        if (file_exists($backupFilePath) && filesize($backupFilePath) > 0) {
            $size_bytes = filesize($backupFilePath);
            $size_kb = round($size_bytes / 1024, 2);
            $size_mb = round($size_kb / 1024, 2);
            $size_str = $size_mb >= 1 ? "$size_mb MB" : "$size_kb KB";
            
            echo "<div style='background-color: #e8f5e9; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h2>Backup Berhasil!</h2>";
            echo "<p><strong>File:</strong> {$backupFileName}</p>";
            echo "<p><strong>Lokasi:</strong> {$backupFilePath}</p>";
            echo "<p><strong>Ukuran:</strong> {$size_str}</p>";
            echo "</div>";
            
            return [
                'success' => true,
                'message' => 'Backup berhasil dibuat',
                'file_path' => $backupFilePath,
                'file_name' => $backupFileName,
                'size' => $size_str
            ];
        } else {
            throw new Exception("File backup kosong atau tidak ada");
        }
        
    } catch (Exception $e) {
        echo "<div style='background-color: #ffebee; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h2>Error!</h2>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
        
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

// Create backup
$result = createSimpleBackup($db_host, $db_user, $db_pass, $db_name, $conn);

// Add links
echo "<p>";
echo "<a href='settings.php' style='margin-right: 10px;'>Kembali ke Pengaturan</a>";
echo "<a href='../setupdb.php'>Kembali ke Pengaturan Database</a>";
echo "</p>";
