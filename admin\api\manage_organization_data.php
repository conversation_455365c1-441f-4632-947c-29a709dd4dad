<?php
session_start();
include '../../config/config.php';

// Cek autentikasi
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Get request method and type
$method = $_SERVER['REQUEST_METHOD'];
$type = isset($_POST['type']) ? $_POST['type'] : '';
$action = isset($_POST['action']) ? $_POST['action'] : '';

// Response array
$response = [
    'success' => false,
    'message' => 'Invalid request'
];

// Handle POST requests (create, update, delete)
if ($method === 'POST') {
    // Manage departments
    if ($type === 'department') {
        if ($action === 'create') {
            $code = isset($_POST['code']) ? trim($_POST['code']) : '';
            $name = isset($_POST['name']) ? trim($_POST['name']) : '';
            $description = isset($_POST['description']) ? trim($_POST['description']) : '';
            
            if (empty($code) || empty($name)) {
                $response['message'] = 'Code and name are required';
            } else {
                // Check if code already exists
                $checkQuery = "SELECT id FROM departments WHERE code = ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("s", $code);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    $response['message'] = "Department with code '{$code}' already exists";
                } else {
                    // Insert new department
                    $insertQuery = "INSERT INTO departments (code, name, description) VALUES (?, ?, ?)";
                    $stmt = $conn->prepare($insertQuery);
                    $stmt->bind_param("sss", $code, $name, $description);
                    
                    if ($stmt->execute()) {
                        $response['success'] = true;
                        $response['message'] = "Department '{$name}' created successfully";
                        $response['id'] = $conn->insert_id;
                    } else {
                        $response['message'] = "Error creating department: " . $stmt->error;
                    }
                }
            }
        } elseif ($action === 'update') {
            $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
            $code = isset($_POST['code']) ? trim($_POST['code']) : '';
            $name = isset($_POST['name']) ? trim($_POST['name']) : '';
            $description = isset($_POST['description']) ? trim($_POST['description']) : '';
            
            if ($id <= 0 || empty($code) || empty($name)) {
                $response['message'] = 'ID, code, and name are required';
            } else {
                // Check if code already exists for another department
                $checkQuery = "SELECT id FROM departments WHERE code = ? AND id != ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("si", $code, $id);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    $response['message'] = "Another department with code '{$code}' already exists";
                } else {
                    // Update department
                    $updateQuery = "UPDATE departments SET code = ?, name = ?, description = ? WHERE id = ?";
                    $stmt = $conn->prepare($updateQuery);
                    $stmt->bind_param("sssi", $code, $name, $description, $id);
                    
                    if ($stmt->execute()) {
                        $response['success'] = true;
                        $response['message'] = "Department updated successfully";
                    } else {
                        $response['message'] = "Error updating department: " . $stmt->error;
                    }
                }
            }
        } elseif ($action === 'delete') {
            $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
            
            if ($id <= 0) {
                $response['message'] = 'ID is required';
            } else {
                // Check if department has sub departments
                $checkQuery = "SELECT COUNT(*) as count FROM sub_departments WHERE department_id = ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("i", $id);
                $stmt->execute();
                $result = $stmt->get_result();
                $row = $result->fetch_assoc();
                
                if ($row['count'] > 0) {
                    $response['message'] = "Cannot delete department with sub departments";
                } else {
                    // Delete department
                    $deleteQuery = "DELETE FROM departments WHERE id = ?";
                    $stmt = $conn->prepare($deleteQuery);
                    $stmt->bind_param("i", $id);
                    
                    if ($stmt->execute()) {
                        $response['success'] = true;
                        $response['message'] = "Department deleted successfully";
                    } else {
                        $response['message'] = "Error deleting department: " . $stmt->error;
                    }
                }
            }
        }
    }
    
    // Manage sub departments
    elseif ($type === 'sub_department') {
        if ($action === 'create') {
            $code = isset($_POST['code']) ? trim($_POST['code']) : '';
            $name = isset($_POST['name']) ? trim($_POST['name']) : '';
            $departmentId = isset($_POST['department_id']) ? intval($_POST['department_id']) : 0;
            $description = isset($_POST['description']) ? trim($_POST['description']) : '';
            
            if (empty($code) || empty($name) || $departmentId <= 0) {
                $response['message'] = 'Code, name, and department are required';
            } else {
                // Check if code already exists
                $checkQuery = "SELECT id FROM sub_departments WHERE code = ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("s", $code);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    $response['message'] = "Sub department with code '{$code}' already exists";
                } else {
                    // Insert new sub department
                    $insertQuery = "INSERT INTO sub_departments (code, name, department_id, description) VALUES (?, ?, ?, ?)";
                    $stmt = $conn->prepare($insertQuery);
                    $stmt->bind_param("ssis", $code, $name, $departmentId, $description);
                    
                    if ($stmt->execute()) {
                        $response['success'] = true;
                        $response['message'] = "Sub department '{$name}' created successfully";
                        $response['id'] = $conn->insert_id;
                    } else {
                        $response['message'] = "Error creating sub department: " . $stmt->error;
                    }
                }
            }
        } elseif ($action === 'update') {
            $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
            $code = isset($_POST['code']) ? trim($_POST['code']) : '';
            $name = isset($_POST['name']) ? trim($_POST['name']) : '';
            $departmentId = isset($_POST['department_id']) ? intval($_POST['department_id']) : 0;
            $description = isset($_POST['description']) ? trim($_POST['description']) : '';
            
            if ($id <= 0 || empty($code) || empty($name) || $departmentId <= 0) {
                $response['message'] = 'ID, code, name, and department are required';
            } else {
                // Check if code already exists for another sub department
                $checkQuery = "SELECT id FROM sub_departments WHERE code = ? AND id != ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("si", $code, $id);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    $response['message'] = "Another sub department with code '{$code}' already exists";
                } else {
                    // Update sub department
                    $updateQuery = "UPDATE sub_departments SET code = ?, name = ?, department_id = ?, description = ? WHERE id = ?";
                    $stmt = $conn->prepare($updateQuery);
                    $stmt->bind_param("ssisi", $code, $name, $departmentId, $description, $id);
                    
                    if ($stmt->execute()) {
                        $response['success'] = true;
                        $response['message'] = "Sub department updated successfully";
                    } else {
                        $response['message'] = "Error updating sub department: " . $stmt->error;
                    }
                }
            }
        } elseif ($action === 'delete') {
            $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
            
            if ($id <= 0) {
                $response['message'] = 'ID is required';
            } else {
                // Check if sub department has positions
                $checkQuery = "SELECT COUNT(*) as count FROM positions WHERE sub_department_id = ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("i", $id);
                $stmt->execute();
                $result = $stmt->get_result();
                $row = $result->fetch_assoc();
                
                if ($row['count'] > 0) {
                    $response['message'] = "Cannot delete sub department with positions";
                } else {
                    // Delete sub department
                    $deleteQuery = "DELETE FROM sub_departments WHERE id = ?";
                    $stmt = $conn->prepare($deleteQuery);
                    $stmt->bind_param("i", $id);
                    
                    if ($stmt->execute()) {
                        $response['success'] = true;
                        $response['message'] = "Sub department deleted successfully";
                    } else {
                        $response['message'] = "Error deleting sub department: " . $stmt->error;
                    }
                }
            }
        }
    }
    
    // Manage positions
    elseif ($type === 'position') {
        if ($action === 'create') {
            $name = isset($_POST['name']) ? trim($_POST['name']) : '';
            $subDepartmentId = isset($_POST['sub_department_id']) ? intval($_POST['sub_department_id']) : 0;
            $description = isset($_POST['description']) ? trim($_POST['description']) : '';
            
            if (empty($name) || $subDepartmentId <= 0) {
                $response['message'] = 'Name and sub department are required';
            } else {
                // Check if position already exists in this sub department
                $checkQuery = "SELECT id FROM positions WHERE name = ? AND sub_department_id = ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("si", $name, $subDepartmentId);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    $response['message'] = "Position '{$name}' already exists in this sub department";
                } else {
                    // Insert new position
                    $insertQuery = "INSERT INTO positions (name, sub_department_id, description) VALUES (?, ?, ?)";
                    $stmt = $conn->prepare($insertQuery);
                    $stmt->bind_param("sis", $name, $subDepartmentId, $description);
                    
                    if ($stmt->execute()) {
                        $response['success'] = true;
                        $response['message'] = "Position '{$name}' created successfully";
                        $response['id'] = $conn->insert_id;
                    } else {
                        $response['message'] = "Error creating position: " . $stmt->error;
                    }
                }
            }
        } elseif ($action === 'update') {
            $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
            $name = isset($_POST['name']) ? trim($_POST['name']) : '';
            $subDepartmentId = isset($_POST['sub_department_id']) ? intval($_POST['sub_department_id']) : 0;
            $description = isset($_POST['description']) ? trim($_POST['description']) : '';
            
            if ($id <= 0 || empty($name) || $subDepartmentId <= 0) {
                $response['message'] = 'ID, name, and sub department are required';
            } else {
                // Check if position already exists in this sub department
                $checkQuery = "SELECT id FROM positions WHERE name = ? AND sub_department_id = ? AND id != ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("sii", $name, $subDepartmentId, $id);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    $response['message'] = "Another position with name '{$name}' already exists in this sub department";
                } else {
                    // Update position
                    $updateQuery = "UPDATE positions SET name = ?, sub_department_id = ?, description = ? WHERE id = ?";
                    $stmt = $conn->prepare($updateQuery);
                    $stmt->bind_param("sisi", $name, $subDepartmentId, $description, $id);
                    
                    if ($stmt->execute()) {
                        $response['success'] = true;
                        $response['message'] = "Position updated successfully";
                    } else {
                        $response['message'] = "Error updating position: " . $stmt->error;
                    }
                }
            }
        } elseif ($action === 'delete') {
            $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
            
            if ($id <= 0) {
                $response['message'] = 'ID is required';
            } else {
                // Delete position
                $deleteQuery = "DELETE FROM positions WHERE id = ?";
                $stmt = $conn->prepare($deleteQuery);
                $stmt->bind_param("i", $id);
                
                if ($stmt->execute()) {
                    $response['success'] = true;
                    $response['message'] = "Position deleted successfully";
                } else {
                    $response['message'] = "Error deleting position: " . $stmt->error;
                }
            }
        }
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
?>
