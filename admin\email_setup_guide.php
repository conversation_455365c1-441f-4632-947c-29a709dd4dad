<?php
session_start();
include '../config/config.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .guide-container {
        max-width: 900px;
        margin: 30px auto;
        padding: 20px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .guide-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }
    
    .guide-header h1 {
        color: #BF0000;
        font-size: 28px;
        margin-bottom: 10px;
    }
    
    .guide-section {
        margin-bottom: 40px;
    }
    
    .guide-section h2 {
        color: #333;
        font-size: 22px;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }
    
    .guide-section h3 {
        color: #555;
        font-size: 18px;
        margin: 20px 0 10px;
    }
    
    .guide-section p, .guide-section li {
        font-size: 16px;
        line-height: 1.6;
        color: #444;
    }
    
    .guide-section ul, .guide-section ol {
        margin-bottom: 20px;
        padding-left: 25px;
    }
    
    .guide-section li {
        margin-bottom: 10px;
    }
    
    .guide-section code {
        background: #f5f5f5;
        padding: 2px 5px;
        border-radius: 3px;
        font-family: Consolas, Monaco, 'Andale Mono', monospace;
        color: #BF0000;
    }
    
    .guide-section .note {
        background: #f8f9fa;
        border-left: 4px solid #BF0000;
        padding: 15px;
        margin: 20px 0;
        border-radius: 0 5px 5px 0;
    }
    
    .guide-section .note strong {
        color: #BF0000;
        display: block;
        margin-bottom: 5px;
    }
    
    .guide-section img {
        max-width: 100%;
        height: auto;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin: 15px 0;
    }
    
    .guide-section table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
    }
    
    .guide-section table th, .guide-section table td {
        color: #555;
        padding: 10px 15px;
        border: 1px solid #ddd;
        text-align: left;
    }
    
    .guide-section table th {
        color: #a50000;
        background: #f5f5f5;
        font-weight: 600;
    }
    
    .guide-section table tr:nth-child(even) {
        color: #555;
        background: #f9f9f9;
    }
    
    .back-btn {
        display: inline-flex;
        align-items: center;
        padding: 10px 20px;
        background: #BF0000;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        margin-top: 20px;
        transition: background 0.3s;
    }
    
    .back-btn:hover {
        background: #990000;
        text-decoration: none;
        color: white;
    }
    
    .back-btn i {
        margin-right: 8px;
    }
</style>
<body>
    <?php include '../config/navbarb.php'; ?>
    <div class="container-form">
        <div class="guide-container">
            <div class="guide-header">
                <h1><i class="fas fa-envelope"></i> Panduan Pengaturan Email</h1>
                <p>Panduan lengkap untuk mengonfigurasi pengaturan email di sistem Training</p>
            </div>
            
            <div class="guide-section">
                <h2>Pengaturan Umum</h2>
                <p>Berikut adalah pengaturan umum yang perlu Anda ketahui untuk mengonfigurasi email:</p>
                
                <table>
                    <thead>
                        <tr>
                            <th>Provider Email</th>
                            <th>SMTP Server</th>
                            <th>Port</th>
                            <th>Enkripsi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Gmail</td>
                            <td>smtp.gmail.com</td>
                            <td>587</td>
                            <td>TLS</td>
                        </tr>
                        <tr>
                            <td>Outlook/Hotmail</td>
                            <td>smtp.office365.com</td>
                            <td>587</td>
                            <td>TLS</td>
                        </tr>
                        <tr>
                            <td>Yahoo Mail</td>
                            <td>smtp.mail.yahoo.com</td>
                            <td>587</td>
                            <td>TLS</td>
                        </tr>
                        <tr>
                            <td>Zoho Mail</td>
                            <td>smtp.zoho.com</td>
                            <td>587</td>
                            <td>TLS</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="guide-section">
                <h2>Panduan Khusus Gmail</h2>
                
                <h3>Menggunakan Gmail dengan App Password</h3>
                <p>Jika Anda menggunakan Gmail dengan verifikasi 2 langkah (2FA), Anda perlu membuat App Password:</p>
                
                <ol>
                    <li>Buka <a href="https://myaccount.google.com/security" target="_blank">Pengaturan Keamanan Google</a></li>
                    <li>Pastikan verifikasi 2 langkah sudah diaktifkan</li>
                    <li>Scroll ke bawah dan cari "App passwords" atau "Sandi aplikasi"</li>
                    <li>Pilih "Mail" sebagai aplikasi dan "Other (Custom name)" sebagai perangkat</li>
                    <li>Masukkan nama seperti "Training System" dan klik "Generate"</li>
                    <li>Google akan menampilkan password 16 karakter. Salin password ini dan gunakan sebagai SMTP Password di pengaturan email sistem</li>
                </ol>
                
                <div class="note">
                    <strong>Catatan Penting:</strong>
                    <p>App Password adalah password 16 karakter tanpa spasi yang hanya ditampilkan sekali. Jika Anda kehilangan password ini, Anda perlu membuat yang baru.</p>
                </div>
                
                <h3>Mengaktifkan "Less Secure Apps"</h3>
                <p>Jika Anda tidak menggunakan verifikasi 2 langkah, Anda perlu mengaktifkan "Less secure app access":</p>
                
                <ol>
                    <li>Buka <a href="https://myaccount.google.com/lesssecureapps" target="_blank">Less secure app access</a></li>
                    <li>Aktifkan opsi "Allow less secure apps"</li>
                </ol>
                
                <div class="note">
                    <strong>Catatan:</strong>
                    <p>Google tidak merekomendasikan opsi ini karena alasan keamanan. Sebaiknya gunakan verifikasi 2 langkah dengan App Password.</p>
                </div>
            </div>
            
            <div class="guide-section">
                <h2>Troubleshooting</h2>
                
                <h3>Masalah Umum dan Solusinya</h3>
                <ul>
                    <li><strong>SMTP Error: Could not authenticate</strong>
                        <ul>
                            <li>Pastikan email dan password Anda benar</li>
                            <li>Jika menggunakan Gmail dengan 2FA, pastikan Anda menggunakan App Password</li>
                            <li>Periksa apakah "Less secure app access" diaktifkan (jika tidak menggunakan 2FA)</li>
                            <li>Coba buka <a href="https://accounts.google.com/DisplayUnlockCaptcha" target="_blank">Unlock Captcha</a> dan login dengan akun Gmail Anda</li>
                        </ul>
                    </li>
                    <li><strong>Connection timed out</strong>
                        <ul>
                            <li>Periksa apakah server SMTP yang Anda masukkan benar</li>
                            <li>Periksa apakah port yang Anda gunakan tidak diblokir oleh firewall</li>
                            <li>Coba gunakan port alternatif (465 dengan SSL)</li>
                        </ul>
                    </li>
                    <li><strong>Connection refused</strong>
                        <ul>
                            <li>Periksa apakah server SMTP yang Anda masukkan benar</li>
                            <li>Periksa apakah ISP Anda tidak memblokir port SMTP</li>
                            <li>Coba gunakan VPN atau koneksi internet lain</li>
                        </ul>
                    </li>
                </ul>
            </div>
            
            <a href="settings.php" class="back-btn">
                <i class="fas fa-arrow-left"></i> Kembali ke Pengaturan
            </a>
        </div>
    </div>
    
    <?php include '../config/footer.php'; ?>
</body>
</html>
