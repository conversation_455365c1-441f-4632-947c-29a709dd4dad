<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Pastikan app_settings.php sudah di-include
if (!function_exists('get_app_name')) {
    require_once __DIR__ . '/app_settings.php';
}

// Dapatkan pengaturan aplikasi
$app_name = get_app_name();
$company_logo = get_company_logo();
$current_page = basename($_SERVER['PHP_SELF']);
?>
<style>
:root {
    --primary-color: #BF0000;
    --primary-color-dark: #900000;
    --primary-color-light: rgba(191, 0, 0, 0.1);
    --primary-color-lighter: rgba(191, 0, 0, 0.05);
    --text-dark: #333333;
    --text-light: #ffffff;
    --text-muted: rgba(255, 255, 255, 0.8);
    --border-light: rgba(255, 255, 255, 0.2);
    --accent-color: #FF3333;
    --accent-light: rgba(255, 51, 51, 0.3);
    --accent-lighter: rgba(255, 51, 51, 0.1);
    --dark-accent: #333333;
    --dark-accent-light: rgba(51, 51, 51, 0.3);
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --transition-fast: 0.2s ease;
    --box-shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.1);
    --box-shadow-md: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Header Styles */
.header {
    background-color: #ffffff;
    box-shadow: var(--box-shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all var(--transition-fast);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    position: relative;
}

/* Logo Styles */
.logo-container {
    display: flex;
    align-items: center;
    height: 40px;
    margin-right: var(--spacing-md);
    overflow: hidden;
}

.logo-image {
    max-height: 40px;
    max-width: 150px;
    width: auto;
    height: auto;
    object-fit: contain;
    transition: all var(--transition-fast);
}

.logo {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
    transition: all var(--transition-fast);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.logo:hover {
    color: var(--primary-color-dark);
}

/* Navigation Menu */
.nav-menu {
    display: flex;
    align-items: center;
    margin-left: auto;
    gap: var(--spacing-md);
}

.nav-menu a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
    overflow: hidden;
}

.nav-menu a i {
    font-size: var(--font-size-md);
    transition: all var(--transition-fast);
}

.nav-menu a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width var(--transition-fast);
}

.nav-menu a:hover {
    color: var(--primary-color);
    background-color: var(--primary-color-lighter);
}

.nav-menu a:hover i {
    transform: translateY(-2px);
}

.nav-menu a:hover::before {
    width: 80%;
}

.nav-menu a.active {
    color: var(--primary-color);
    background-color: var(--primary-color-light);
    font-weight: 600;
}

.nav-menu a.active::before {
    width: 80%;
}

/* User Profile and Logout */
.user-profile {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    margin-left: var(--spacing-md);
}


.logout {
    background-color: var(--primary-color);
    color: var(--text-dark) !important;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    font-weight: 600;
    box-shadow: var(--box-shadow-sm);
}

.logout:hover {
    color: var(--text-dark) !important;
    background-color: var(--primary-color-dark);
    box-shadow: var(--box-shadow-md);
    transform: translateY(-2px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background-color: var(--primary-color);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    z-index: 1001;
    box-shadow: var(--box-shadow-sm);
}

.mobile-menu-toggle:hover {
    background-color: var(--primary-color-dark);
}

/* Responsive Styles */
@media screen and (max-width: 992px) {
    .header-content {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .logo {
        max-width: 180px;
        font-size: var(--font-size-md);
    }

    .nav-menu a {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

@media screen and (max-width: 768px) {
    .header {
        box-shadow: none;
    }

    .header-content {
        padding: var(--spacing-sm);
        justify-content: space-between;
    }

    .logo-container {
        height: 36px;
    }

    .logo-image {
        max-height: 36px;
        max-width: 120px;
    }

    .logo {
        max-width: 150px;
        font-size: var(--font-size-sm);
    }

    /* Show mobile menu toggle */
    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        margin-left: auto;
    }

    /* Style mobile navigation menu */
    .nav-menu {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--primary-color);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: var(--spacing-lg);
        gap: var(--spacing-md);
        z-index: 1000;
        overflow-y: auto;
    }

    .nav-menu::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.1;
        pointer-events: none;
    }

    .nav-menu.active {
        display: flex;
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    .nav-menu a {
        color: var(--text-light);
        width: 100%;
        text-align: center;
        padding: var(--spacing-md);
        font-size: var(--font-size-md);
        border-radius: var(--border-radius-md);
        justify-content: center;
    }

    .nav-menu a::before {
        display: none;
    }

    .nav-menu a:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--text-light);
    }

    .nav-menu a.active {
        background-color: rgba(255, 255, 255, 0.2);
        color: var(--text-light);
    }

    .user-profile {
        margin-left: 0;
        margin-top: var(--spacing-md);
        width: 100%;
        justify-content: center;
    }

    .username {
        color: var(--text-light);
    }

    .logout {
        width: 100%;
        color: var(--text-dark)!important;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.3);
        margin-top: var(--spacing-md);
    }

    .logout:hover {
        color: var(--text-dark)!important;
        background-color: rgba(255, 255, 255, 0.4);
    }

    /* Mobile Dropdown Styles */
    .nav-dropdown .dropdown-menu {
        position: static;
        background-color: rgba(255, 255, 255, 0.1);
        border: none;
        box-shadow: none;
        border-radius: 0;
        min-width: auto;
        width: 100%;
        margin-top: var(--spacing-xs);
        opacity: 1;
        visibility: visible;
        transform: none;
        display: none;
    }

    .nav-dropdown.active .dropdown-menu {
        display: block;
    }

    .nav-dropdown .dropdown-menu a {
        color: var(--text-light);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding-left: calc(var(--spacing-md) + var(--spacing-lg));
        font-size: var(--font-size-sm);
    }

    .nav-dropdown .dropdown-menu a:hover {
        background-color: rgba(255, 255, 255, 0.15);
        color: var(--text-light);
        padding-left: calc(var(--spacing-md) + var(--spacing-lg) + 5px);
    }

    .nav-dropdown .dropdown-menu a.disabled {
        color: rgba(255, 255, 255, 0.5);
        background-color: transparent;
    }

    .nav-dropdown .dropdown-menu a.disabled:hover {
        background-color: transparent;
        color: rgba(255, 255, 255, 0.5);
        padding-left: calc(var(--spacing-md) + var(--spacing-lg));
    }
}

@media screen and (max-width: 480px) {
    .header-content {
        padding: var(--spacing-xs);
    }

    .logo-container {
        height: 32px;
    }

    .logo-image {
        max-height: 32px;
        max-width: 100px;
    }

    .logo {
        max-width: 120px;
        font-size: var(--font-size-xs);
    }

    .nav-menu a {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
}

/* Dropdown Styles */
.nav-dropdown {
    position: relative;
    display: inline-block;
}

.nav-dropdown .dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
}

.nav-dropdown .dropdown-toggle::after {
    content: '\f107'; /* FontAwesome chevron-down */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 12px;
    margin-left: 5px;
    transition: transform var(--transition-fast);
}

.nav-dropdown.active .dropdown-toggle::after {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-md);
    min-width: 220px;
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    overflow: hidden;
}

.nav-dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    border-bottom: 1px solid #f0f0f0;
    transition: all var(--transition-fast);
    position: relative;
}

.dropdown-menu a:last-child {
    border-bottom: none;
}

.dropdown-menu a:hover {
    background-color: var(--primary-color-lighter);
    color: var(--primary-color);
    padding-left: calc(var(--spacing-md) + 5px);
}

.dropdown-menu a.disabled {
    color: #999;
    cursor: not-allowed;
    background-color: #f8f9fa;
}

.dropdown-menu a.disabled:hover {
    background-color: #f8f9fa;
    color: #999;
    padding-left: var(--spacing-md);
}

.dropdown-menu a i {
    width: 16px;
    margin-right: var(--spacing-xs);
}

/* Force dropdown visibility for debugging */
.nav-dropdown.active .dropdown-menu {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    background-color: #ffffff !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15) !important;
    min-width: 220px !important;
    z-index: 1001 !important;
}

/* Add spacing to prevent content from being hidden under fixed header */
.header-spacer {
    height: 60px;
}

@media screen and (max-width: 768px) {
    .header-spacer {
        height: 50px;
    }
}

@media screen and (max-width: 576px) {
    .header-spacer {
        height: 48px;
    }
}

@media screen and (max-width: 480px) {
    .header-spacer {
        height: 45px;
    }
}

:root {
    --primary-color: #BF0000;
    --primary-color-dark: #900000;
    --primary-color-light: rgba(191, 0, 0, 0.1);
    --primary-color-lighter: rgba(191, 0, 0, 0.05);
    --text-dark: #333333;
    --text-light: #ffffff;
    --text-muted: rgba(255, 255, 255, 0.8);
    --border-light: rgba(255, 255, 255, 0.2);
    --accent-color: #FF3333;
    --accent-light: rgba(255, 51, 51, 0.3);
    --accent-lighter: rgba(255, 51, 51, 0.1);
    --dark-accent: #333333;
    --dark-accent-light: rgba(51, 51, 51, 0.3);
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --transition-fast: 0.2s ease;
}
.welcome-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--text-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    transform: rotate(30deg);
    pointer-events: none;
}

/* Pattern overlay for welcome section */
.welcome-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
}

.welcome-section h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    position: relative;
}

.welcome-section p {
    margin-bottom: var(--spacing-sm);
    font-size: 16px;
    opacity: 0.9;
    position: relative;
}

</style>

<header class="header">
    <div class="header-content">
        <div class="logo-container">
            <img class="logo-image" src="<?= htmlspecialchars($company_logo) ?>" alt="Company Logo">
        </div>
        <a href="<?= getDashboardUrl() ?>" class="logo">
            <?php
            switch ($current_page) {
                case 'index.php':
                    echo "Home";
                    break;
                case 'form.php':
                    echo "Pengajuan Training";
                    break;
                case 'history.php':
                    echo "Riwayat Training";
                    break;
                default:
                    echo "Knowledge Management System";
                    break;
            }
            ?>
        </a>
        <button class="mobile-menu-toggle" id="mobileMenuToggle" type="button" aria-label="Toggle menu">
            <i class="fas fa-bars"></i>
        </button>
        <nav class="nav-menu" id="navMenu">
            <a href="<?= getDashboardUrl() ?>" class="<?= ($current_page == 'index.php') ? 'active' : ''; ?>">
                <i class="fas fa-home"></i> Home
            </a>
            <div class="nav-dropdown">
                <a href="#" class="dropdown-toggle <?= ($current_page == 'form.php') ? 'active' : ''; ?>">
                    <i class="fas fa-file-alt"></i> Pengajuan Training
                </a>
                <div class="dropdown-menu">
                    <a href="<?= getAdminMenuUrl('form.php') ?>" style="color: black;">
                        <i class="fas fa-external-link-alt"></i> Training Eksternal
                    </a>
                    <a href="<?= getAdminMenuUrl('internal_training_form.php') ?>" style="color: black;">
                        <i class="fas fa-building"></i> Training Internal
                    </a>
                </div>
            </div>
            <?php if (isset($_SESSION['role_id']) && $_SESSION['role_id'] == 1): ?>
            <a href="<?= getAdminMenuUrl('history.php') ?>" class="<?= ($current_page == 'history.php') ? 'active' : ''; ?>">
                <i class="fas fa-history"></i> Riwayat Training
            </a>
            <?php endif; ?>
            <?php if (isset($_SESSION['role_id']) && $_SESSION['role_id'] == 99): ?>
            <a href="<?= getAdminMenuUrl('check_employee_count.php') ?>" class="<?= ($current_page == 'check_employee_count.php') ? 'active' : ''; ?>">
                <i class="fas fa-chart-bar"></i> Statistik Karyawan
            </a>
            <a href="<?= getAdminMenuUrl('manage_announcements.php') ?>" class="<?= ($current_page == 'manage_announcements.php') ? 'active' : ''; ?>">
                <i class="fas fa-bullhorn"></i> Pengumuman
            </a>
            <a href="<?= getAdminMenuUrl('import_template.php') ?>" class="<?= ($current_page == 'import_template.php') ? 'active' : ''; ?>">
                <i class="fas fa-file-import"></i> Import Data
            </a>
            <a href="<?= getAdminMenuUrl('security_monitoring.php?tab=login-logs') ?>" class="<?= ($current_page == 'security_monitoring.php' && isset($_GET['tab']) && $_GET['tab'] == 'login-logs') ? 'active' : ''; ?>">
                <i class="fas fa-sign-in-alt"></i> Log Login
            </a>
            <?php endif; ?>
            <?php
            // Include notifications dropdown
            if (isset($_SESSION['user_id'])) {
                $notifications_path = __DIR__ . '/../includes/notifications_dropdown.php';
                if (file_exists($notifications_path)) {
                    include_once $notifications_path;
                }
            }
            ?>

            <a href="<?= getCorrectUrl('config/userinfo.php') ?>" class="user-profile <?= ($current_page == 'userinfo.php') ? 'active' : ''; ?>">
                <div class="avatar">
                <span class="user-initial"><?= strtoupper(substr($_SESSION['user_name'] ?? 'Pengguna', 0, 1)) ?></span>
                </div>
            </a>
            <a href="<?= getCorrectUrl('view/logout.php') ?>" class="logout">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </nav>
    </div>
</header>

<!-- Add spacing to prevent content from being hidden under fixed header -->
<div class="header-spacer"></div>

<script>
    // Function to toggle mobile menu
    document.getElementById('mobileMenuToggle').addEventListener('click', function() {
        try {
            const navMenu = document.getElementById('navMenu');
            navMenu.classList.toggle('active');

            // Change icon based on menu state
            if (navMenu.classList.contains('active')) {
                this.innerHTML = '<i class="fas fa-times"></i>';
            } else {
                this.innerHTML = '<i class="fas fa-bars"></i>';
            }
        } catch (error) {
            console.error('Error toggling menu:', error);
        }
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        try {
            const navMenu = document.getElementById('navMenu');
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');

            if (navMenu && mobileMenuToggle) {
                if (!navMenu.contains(event.target) && !mobileMenuToggle.contains(event.target)) {
                    navMenu.classList.remove('active');
                    mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';
                }
            }
        } catch (error) {
            console.error('Error handling click event:', error);
        }
    });

    // Simple and robust dropdown functionality
    function setupDropdowns() {
        // Remove any existing event listeners first
        const existingDropdowns = document.querySelectorAll('.nav-dropdown');
        existingDropdowns.forEach(dropdown => {
            const newDropdown = dropdown.cloneNode(true);
            dropdown.parentNode.replaceChild(newDropdown, dropdown);
        });

        // Setup new event listeners
        const dropdowns = document.querySelectorAll('.nav-dropdown');
        console.log('Setting up dropdowns:', dropdowns.length);

        dropdowns.forEach(dropdown => {
            const toggle = dropdown.querySelector('.dropdown-toggle');

            if (toggle) {
                toggle.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    console.log('Dropdown toggle clicked');

                    // Close all other dropdowns
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            otherDropdown.classList.remove('active');
                        }
                    });

                    // Toggle current dropdown
                    const isActive = dropdown.classList.contains('active');
                    if (isActive) {
                        dropdown.classList.remove('active');
                    } else {
                        dropdown.classList.add('active');
                    }

                    console.log('Dropdown is now:', dropdown.classList.contains('active') ? 'open' : 'closed');
                };
            }
        });

        // Close dropdowns when clicking outside
        document.onclick = function(event) {
            if (!event.target.closest('.nav-dropdown')) {
                dropdowns.forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        };

        // Handle disabled links
        document.querySelectorAll('.dropdown-menu a.disabled').forEach(link => {
            link.onclick = function(e) {
                e.preventDefault();
                return false;
            };
        });
    }

    // Multiple initialization attempts
    document.addEventListener('DOMContentLoaded', setupDropdowns);

    // Fallback initialization
    setTimeout(function() {
        setupDropdowns();
        console.log('Fallback dropdown setup completed');
    }, 500);

    // Another fallback for slow loading pages
    window.addEventListener('load', function() {
        setTimeout(setupDropdowns, 100);
    });

    // Add shadow to header on scroll
    window.addEventListener('scroll', function() {
        try {
            const header = document.querySelector('.header');
            if (window.scrollY > 10) {
                header.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.1)';
            }
        } catch (error) {
            console.error('Error handling scroll event:', error);
        }
    });
</script>
