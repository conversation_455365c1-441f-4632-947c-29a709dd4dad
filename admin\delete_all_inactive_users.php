<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi variabel
$success_message = '';
$error_message = '';

// Proses penghapusan masal
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete']) && $_POST['confirm_delete'] === 'yes') {
    try {
        // Mulai transaction
        $conn->begin_transaction();

        // Hitung jumlah pengguna tidak aktif
        $count_query = "SELECT COUNT(*) as total FROM users WHERE is_active = 0";
        $count_result = $conn->query($count_query);
        $total_inactive = $count_result->fetch_assoc()['total'];

        if ($total_inactive === 0) {
            throw new Exception('Tidak ada pengguna tidak aktif untuk dihapus');
        }

        // Get list of inactive user IDs first
        $getUsersQuery = "SELECT id FROM users WHERE is_active = 0";
        $usersResult = $conn->query($getUsersQuery);
        $inactiveUserIds = [];
        while ($row = $usersResult->fetch_assoc()) {
            $inactiveUserIds[] = $row['id'];
        }

        if (empty($inactiveUserIds)) {
            throw new Exception('Tidak ada pengguna tidak aktif untuk dihapus');
        }

        $userIdsList = implode(',', $inactiveUserIds);

        // 1. Set NULL pada approved_by di training_submissions
        $updateQuery1 = "UPDATE training_submissions SET approved_by = NULL WHERE approved_by IN ($userIdsList)";
        $conn->query($updateQuery1);

        // 2. Set NULL pada rejected_by di training_submissions
        $updateQuery2 = "UPDATE training_submissions SET rejected_by = NULL WHERE rejected_by IN ($userIdsList)";
        $conn->query($updateQuery2);

        // 3. Set NULL pada next_approver_id di training_submissions
        $updateQuery3 = "UPDATE training_submissions SET next_approver_id = NULL WHERE next_approver_id IN ($userIdsList)";
        $conn->query($updateQuery3);

        // 4. Hapus dari user_departments (foreign key constraint)
        $deleteUserDepts = "DELETE FROM user_departments WHERE user_id IN ($userIdsList)";
        if (!$conn->query($deleteUserDepts)) {
            throw new Exception("Gagal menghapus user departments: " . $conn->error);
        }

        // 5. Hapus dari training_notifications jika ada
        $deleteNotifications = "DELETE FROM training_notifications WHERE user_id IN ($userIdsList)";
        $conn->query($deleteNotifications); // Optional, tidak error jika tabel tidak ada

        // 6. Hapus dari activity_logs jika ada
        $deleteActivityLogs = "DELETE FROM activity_logs WHERE user_id IN ($userIdsList)";
        $conn->query($deleteActivityLogs); // Optional, tidak error jika tabel tidak ada

        // 7. Clean offline_training references (check columns first)
        $columnsResult = $conn->query("SHOW COLUMNS FROM offline_training");
        $availableColumns = [];
        while ($col = $columnsResult->fetch_assoc()) {
            $availableColumns[] = $col['Field'];
        }

        $possibleColumns = [
            'approved_by_dept_head',
            'approved_by_lnd',
            'approved_dept_head',
            'approved_lnd',
            'approved_hrga',
            'approved_fm',
            'approved_dir',
            'created_by'
        ];

        foreach ($possibleColumns as $column) {
            if (in_array($column, $availableColumns)) {
                $query = "UPDATE offline_training SET $column = NULL WHERE $column IN ($userIdsList)";
                $conn->query($query);
            }
        }

        // 8. Akhirnya hapus users
        $deleteQuery = "DELETE FROM users WHERE is_active = 0";
        if (!$conn->query($deleteQuery)) {
            throw new Exception("Gagal menghapus pengguna: " . $conn->error);
        }

        $deleted_count = $conn->affected_rows;

        // Log aktivitas bulk delete
        if (file_exists('../config/activity_logger.php')) {
            include_once '../config/activity_logger.php';
            if (function_exists('log_activity')) {
                log_activity($_SESSION['user_id'], "Menghapus $deleted_count pengguna tidak aktif secara masal", "user", [
                    'bulk_delete' => true,
                    'deleted_count' => $deleted_count,
                    'delete_all' => true
                ]);
            }
        }

        // Commit transaction
        $conn->commit();

        $success_message = "Berhasil menghapus $deleted_count pengguna tidak aktif";

    } catch (Exception $e) {
        // Rollback transaction jika terjadi error
        if ($conn->connect_errno === 0) {
            $conn->rollback();
        }

        $error_message = $e->getMessage();
    }
}

// Hitung jumlah pengguna tidak aktif
$count_query = "SELECT COUNT(*) as total FROM users WHERE is_active = 0";
$count_result = $conn->query($count_query);
$total_inactive = $count_result->fetch_assoc()['total'];

$conn->close();
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    body {
        background-color: #f5f5f5;
        overflow-x: hidden;
    }

    .container {
        max-width: 800px;
        margin: 20px auto;
        padding: 20px;
    }

    .card {
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 20px;
    }

    .card-header {
        background-color: #BF0000 !important;
        color: white;
        padding: 15px 20px;
    }

    .card-header h3 {
        margin: 0;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
    }

    .card-header h3 i {
        margin-right: 10px;
    }

    .card-body {
        padding: 20px;
        background-color: white;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    .alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }

    .alert-warning {
        background-color: #fff3cd;
        border-color: #ffeeba;
        color: #856404;
    }

    .btn-primary {
        background-color: #BF0000;
        border-color: #BF0000;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #a00000;
        border-color: #a00000;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #5a6268;
    }

    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeeba;
        border-left: 4px solid #ffc107;
        color: #856404;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .warning-box h4 {
        color: #856404;
        margin-top: 0;
        margin-bottom: 10px;
    }

    .danger-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-left: 4px solid #dc3545;
        color: #721c24;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .danger-box h4 {
        color: #721c24;
        margin-top: 0;
        margin-bottom: 10px;
    }

    .jarak {
        height: 100px;
    }

    .action-buttons {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
    }

    .confirmation-text {
        font-size: 1.2rem;
        margin-bottom: 20px;
        text-align: center;
    }

    .confirmation-count {
        font-size: 2.5rem;
        font-weight: bold;
        color: #BF0000;
        text-align: center;
        margin: 20px 0;
    }

    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #dee2e6;
    }

    .empty-state h4 {
        margin-bottom: 10px;
        font-size: 1.5rem;
    }

    .empty-state p {
        max-width: 500px;
        margin: 0 auto;
    }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container">
    <div class="card">
        <div class="card-header">
            <h3><i class="fas fa-user-slash"></i> Hapus Semua Pengguna Tidak Aktif</h3>
        </div>
        <div class="card-body">
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                </div>
                <div class="action-buttons" style="justify-content: center;">
                    <a href="delete_inactive_users.php" class="btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            <?php elseif (!empty($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
                <div class="action-buttons" style="justify-content: center;">
                    <a href="delete_inactive_users.php" class="btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            <?php elseif ($total_inactive > 0): ?>
                <div class="danger-box">
                    <h4><i class="fas fa-exclamation-triangle"></i> Peringatan Penting!</h4>
                    <p>Anda akan menghapus <strong>SEMUA</strong> pengguna tidak aktif dari sistem. Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data pengguna yang terkait.</p>
                </div>

                <div class="confirmation-text">
                    Anda akan menghapus:
                </div>

                <div class="confirmation-count">
                    <?php echo number_format($total_inactive); ?> Pengguna Tidak Aktif
                </div>

                <form method="post" id="deleteAllForm">
                    <input type="hidden" name="confirm_delete" value="yes">

                    <div class="action-buttons">
                        <a href="delete_inactive_users.php" class="btn-secondary">
                            <i class="fas fa-times"></i> Batal
                        </a>
                        <button type="button" id="confirmDeleteButton" class="btn-primary">
                            <i class="fas fa-trash"></i> Hapus Semua Pengguna Tidak Aktif
                        </button>
                    </div>
                </form>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-user-check"></i>
                    <h4>Tidak Ada Pengguna Tidak Aktif</h4>
                    <p>Semua pengguna dalam sistem saat ini berstatus aktif. Tidak ada pengguna yang perlu dihapus.</p>
                    <div class="action-buttons" style="justify-content: center; margin-top: 30px;">
                        <a href="delete_inactive_users.php" class="btn-secondary" style="color: white;">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
// Fungsi untuk menampilkan toast notification
function showToast(message, type = 'success', duration = 3000) {
    // Hapus toast yang sudah ada jika ada
    const existingToast = document.querySelector('.toast-notification');
    if (existingToast) {
        document.body.removeChild(existingToast);
    }

    // Buat toast baru
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;

    // Set icon berdasarkan type
    let icon = 'check';
    if (type === 'error') icon = 'exclamation-circle';
    if (type === 'warning') icon = 'exclamation-triangle';
    if (type === 'info') icon = 'info-circle';

    toast.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;
    document.body.appendChild(toast);

    // Animasi toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // Hapus toast setelah durasi tertentu
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// Fungsi untuk menampilkan dialog konfirmasi kustom
function showConfirmDialog(message, onConfirm, onCancel) {
    // Hapus dialog yang sudah ada jika ada
    const existingDialog = document.querySelector('.confirm-dialog-container');
    if (existingDialog) {
        document.body.removeChild(existingDialog);
    }

    // Buat dialog konfirmasi
    const dialogContainer = document.createElement('div');
    dialogContainer.className = 'confirm-dialog-container';

    dialogContainer.innerHTML = `
        <div class="confirm-dialog">
            <div class="confirm-dialog-header">
                <i class="fas fa-question-circle"></i>
                <h4>Konfirmasi</h4>
            </div>
            <div class="confirm-dialog-body">
                <p>${message}</p>
            </div>
            <div class="confirm-dialog-footer">
                <button class="btn btn-secondary btn-cancel">Batal</button>
                <button class="btn btn-danger btn-confirm">Ya, Lanjutkan</button>
            </div>
        </div>
    `;

    document.body.appendChild(dialogContainer);

    // Tambahkan event listener untuk tombol
    const cancelBtn = dialogContainer.querySelector('.btn-cancel');
    const confirmBtn = dialogContainer.querySelector('.btn-confirm');

    cancelBtn.addEventListener('click', () => {
        document.body.removeChild(dialogContainer);
        if (onCancel) onCancel();
    });

    confirmBtn.addEventListener('click', () => {
        document.body.removeChild(dialogContainer);
        if (onConfirm) onConfirm();
    });

    // Animasi dialog
    setTimeout(() => {
        dialogContainer.classList.add('show');
    }, 10);
}

// Tambahkan CSS untuk toast notification dan dialog konfirmasi
document.head.insertAdjacentHTML('beforeend', `
<style>
.toast-notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.toast-notification.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.toast-success i {
    color: #4CAF50;
}

.toast-error i {
    color: #F44336;
}

.toast-warning i {
    color: #FF9800;
}

.toast-info i {
    color: #2196F3;
}

.confirm-dialog-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.confirm-dialog-container.show {
    opacity: 1;
}

.confirm-dialog {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.confirm-dialog-container.show .confirm-dialog {
    transform: translateY(0);
}

.confirm-dialog-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px;
}

.confirm-dialog-header i {
    color: #BF0000;
    font-size: 1.2rem;
}

.confirm-dialog-header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: #212529;
}

.confirm-dialog-body {
    padding: 20px;
}

.confirm-dialog-body p {
    margin: 0;
    color: #495057;
}

.confirm-dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

@media screen and (max-width: 576px) {
    .confirm-dialog {
        width: 95%;
    }

    .confirm-dialog-header,
    .confirm-dialog-body,
    .confirm-dialog-footer {
        padding: 12px 15px;
    }
}
</style>
`);

document.addEventListener('DOMContentLoaded', function() {
    const confirmDeleteButton = document.getElementById('confirmDeleteButton');

    if (confirmDeleteButton) {
        confirmDeleteButton.addEventListener('click', function() {
            showConfirmDialog('PERINGATAN: Anda akan menghapus SEMUA pengguna tidak aktif (<?php echo $total_inactive; ?> pengguna). Tindakan ini TIDAK DAPAT DIBATALKAN. Apakah Anda yakin ingin melanjutkan?', () => {
                // Konfirmasi kedua untuk memastikan
                showConfirmDialog('Konfirmasi terakhir: Apakah Anda benar-benar yakin ingin menghapus <?php echo $total_inactive; ?> pengguna tidak aktif?', () => {
                    document.getElementById('deleteAllForm').submit();
                });
            });
        });
    }
});
</script>
</body>
</html>
