<?php
session_start();
include '../config/config.php';
require_once '../config/email_helper.php';

// Cek apakah ada parameter token dan email
$token = isset($_GET['token']) ? $_GET['token'] : '';
$email = isset($_GET['email']) ? $_GET['email'] : '';
$user_id = isset($_GET['uid']) ? intval($_GET['uid']) : 0;

$message = '';
$success = false;
$verification_code = '';

// Jika form disubmit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $verification_code = isset($_POST['verification_code']) ? trim($_POST['verification_code']) : '';
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;

    if (empty($verification_code)) {
        $message = '<div class="alert alert-danger">Kode verifikasi tidak boleh kosong.</div>';
    } else {
        // Verifikasi kode
        $query = "SELECT * FROM users WHERE id = ? AND verification_code = ? AND verification_expires > NOW()";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("is", $user_id, $verification_code);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();

            // Update email
            if (updateVerifiedEmail($user_id, $conn)) {
                $success = true;
                $message = '<div class="alert alert-success" style="text-align: left; padding: 20px; background-color: #f0fff4; border-left: 4px solid #0d6832; margin: 15px 0;">
                    <h3 style="color: #0d6832; margin-top: 0;"><i class="fas fa-check-circle"></i> Email Berhasil Diverifikasi!</h3>
                    <p>Alamat email Anda telah berhasil diperbarui menjadi: <strong>' . htmlspecialchars($user['pending_email']) . '</strong></p>
                    <p>Anda akan menerima semua notifikasi sistem di alamat email baru ini.</p>
                    <p>Anda akan dialihkan ke halaman profil dalam beberapa detik...</p>
                    <div style="text-align: center; margin-top: 20px;">
                        <a href="../config/userinfo.php" class="btn" style="display: inline-block; padding: 10px 20px; background-color: #0d6832; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">
                            <i class="fas fa-user"></i> Kembali ke Profil Sekarang
                        </a>
                    </div>
                </div>';

                // Hapus session verifikasi jika ada
                if (isset($_SESSION['email_verification'])) {
                    unset($_SESSION['email_verification']);
                }

                // Redirect ke halaman profil setelah 3 detik
                header('Refresh: 3; URL=../config/userinfo.php');
            } else {
                $message = '<div class="alert alert-danger">Gagal memperbarui email. Silakan coba lagi.</div>';
            }
        } else {
            $message = '<div class="alert alert-danger">Kode verifikasi tidak valid atau sudah kadaluarsa.</div>';
        }
    }
} else if (!empty($token) && !empty($email) && $user_id > 0) {
    // Verifikasi token
    $query = "SELECT * FROM users WHERE id = ? AND verification_code = ? AND pending_email = ? AND verification_expires > NOW()";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("iss", $user_id, $token, $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Token valid, tampilkan form dengan kode sudah terisi
        $verification_code = $token;
    } else {
        $message = '<div class="alert alert-danger">Link verifikasi tidak valid atau sudah kadaluarsa.</div>';
    }
}

// Jika user sudah login, ambil user_id dari session
if (empty($user_id) && isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    body {
        font-family: Arial, sans-serif;
        background-color: #f4f4f4;
        margin: 0;
        padding: 0;
    }
    .container-form {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 80vh;
        margin-top: 70px;
        position: relative;
        z-index: 1;
    }
    .form-container {
        background: #fff;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 500px;
        text-align: center;
        transition: all 0.3s ease;
    }
    h2 {
        color: #BF0000;
        margin-bottom: 20px;
        font-size: 2rem;
        font-weight: 600;
    }
    .verification-code-input {
        text-align: center;
        letter-spacing: 0.5em;
        font-size: 1.2em;
        padding: 10px;
        margin: 20px 0;
        width: 200px;
    }
    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 8px;
        text-align: center;
        font-weight: 500;
    }
    .alert-danger {
        background: #fff2f2;
        color: #c40000;
        border: 1px solid #ffcfcf;
    }
    .alert-success {
        background: #f0fff4;
        color: #0d6832;
        border: 1px solid #c3e6cb;
    }
    .btn {
        padding: 12px 20px;
        color: white;
        background-color: #BF0000;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    .btn:hover {
        background-color: #a00000;
    }
    .info-text {
        font-size: 0.9em;
        color: #666;
        margin: 15px 0;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 5px;
        border-left: 3px solid #BF0000;
    }
    .resend-link {
        color: #BF0000;
        text-decoration: underline;
        cursor: pointer;
        margin-top: 15px;
        display: inline-block;
    }
</style>
<body>
<?php include '../config/navbarb.php'; ?>

<div class="container-form">
    <div class="form-container">
        <h2>Verifikasi Email</h2>

        <?php if (!empty($message)): ?>
            <?php echo $message; ?>
        <?php endif; ?>

        <?php if (!$success): ?>
            <div class="info-text" style="text-align: left; padding: 15px; background-color: #e8f4fd; border-left: 4px solid #0c5460; margin: 15px 0;">
                <strong>Verifikasi Diperlukan untuk Keamanan Akun Anda</strong><br>
                <p>Anda telah meminta untuk mengubah alamat email Anda. Untuk keamanan akun, kami perlu memverifikasi bahwa Anda memiliki akses ke alamat email baru ini.</p>

                <p>Masukkan kode verifikasi 6 digit yang telah dikirim ke: <strong><?php echo htmlspecialchars($email); ?></strong></p>

                <p><i class="fas fa-info-circle"></i> Jika Anda tidak menerima kode, periksa folder spam/junk email Anda atau klik "Kirim ulang kode verifikasi" di bawah.</p>

                <div style="margin-top: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                    <p><strong>Buka Email Anda:</strong></p>
                    <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 8px;">
                        <!-- <a href="https://mail.google.com" target="_blank" style="display: inline-flex; align-items: center; padding: 6px 12px; background-color: #f1f3f4; border-radius: 4px; text-decoration: none; color: #202124; font-size: 14px;">
                            <i class="fab fa-google" style="margin-right: 8px; color: #4285F4;"></i> Gmail
                        </a>
                        <a href="https://outlook.live.com/mail" target="_blank" style="display: inline-flex; align-items: center; padding: 6px 12px; background-color: #f1f3f4; border-radius: 4px; text-decoration: none; color: #202124; font-size: 14px;">
                            <i class="fab fa-microsoft" style="margin-right: 8px; color: #0078D4;"></i> Outlook
                        </a>
                        <a href="https://mail.yahoo.com" target="_blank" style="display: inline-flex; align-items: center; padding: 6px 12px; background-color: #f1f3f4; border-radius: 4px; text-decoration: none; color: #202124; font-size: 14px;">
                            <i class="fab fa-yahoo" style="margin-right: 8px; color: #6001D2;"></i> Yahoo Mail
                        </a> -->
                        <?php
                        // Deteksi domain email dan tambahkan link khusus jika tersedia
                        if (!empty($email)) {
                            $domain = substr(strrchr($email, "@"), 1);
                            $custom_link = '';
                            $domain_name = '';

                            switch(strtolower($domain)) {
                                case 'gmail.com':
                                    $custom_link = 'https://mail.google.com';
                                    $domain_name = 'Gmail';
                                    break;
                                case 'outlook.com':
                                case 'hotmail.com':
                                case 'live.com':
                                    $custom_link = 'https://outlook.live.com/mail';
                                    $domain_name = 'Outlook';
                                    break;
                                case 'yahoo.com':
                                case 'yahoo.co.id':
                                    $custom_link = 'https://mail.yahoo.com';
                                    $domain_name = 'Yahoo Mail';
                                    break;
                                case 'zoho.com':
                                    $custom_link = 'https://mail.zoho.com';
                                    $domain_name = 'Zoho Mail';
                                    break;
                                case 'aol.com':
                                    $custom_link = 'https://mail.aol.com';
                                    $domain_name = 'AOL Mail';
                                    break;
                                case 'protonmail.com':
                                case 'proton.me':
                                    $custom_link = 'https://mail.proton.me';
                                    $domain_name = 'ProtonMail';
                                    break;
                                case 'icloud.com':
                                case 'me.com':
                                    $custom_link = 'https://www.icloud.com/mail';
                                    $domain_name = 'iCloud Mail';
                                    break;
                                case 'yandex.com':
                                case 'yandex.ru':
                                    $custom_link = 'https://mail.yandex.com';
                                    $domain_name = 'Yandex Mail';
                                    break;
                                // Tambahkan domain email lain jika diperlukan
                                default:
                                    $custom_link = '';
                                    $domain_name = $domain;
                            }

                            if (!empty($custom_link)) {
                                echo '<a href="' . $custom_link . '" target="_blank" class="open-email-btn">';
                                echo '<i class="fas fa-envelope" style="margin-right: 8px;"></i> Buka ' . htmlspecialchars($domain_name);
                                echo '</a>';
                            }
                        }
                        ?>
                    </div>
                </div>
            </div>

            <form method="POST" action="verify_email.php">
                <input type="hidden" name="user_id" value="<?php echo $user_id; ?>">

                <div class="form-group">
                    <input type="text" id="verification_code" name="verification_code"
                           class="verification-code-input" maxlength="6" placeholder="000000"
                           value="<?php echo htmlspecialchars($verification_code); ?>">
                </div>

                <button type="submit" class="btn" style="max-width: 300px; margin: 0 auto; display: block; color: white;">
                    <i class="fas fa-check-circle"></i> Verifikasi Email
                </button>
            </form>

            <a href="#" class="btn" id="resendLink" style="margin: 10px auto; width: 100%; max-width: 300px; display: block; color: white;"><i class="fas fa-paper-plane" style="margin-right: 8px;"></i> Kirim ulang kode verifikasi</a>

            <div id="resendStatus" style="margin-top: 10px; display: none;">
                <div class="loading-spinner" style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #BF0000; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                <span id="resendMessage" style="margin-left: 10px; font-style: italic; color: #666;">Mengirim kode verifikasi...</span>
            </div>
        <?php endif; ?>

        <div style="margin-top: 20px;">
            <a href="../config/userinfo.php" class="btn" style="background-color: #6c757d; color: white;">
                <i class="fas fa-arrow-left"></i> Kembali ke Profil
            </a>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<style>
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const resendLink = document.getElementById('resendLink');
    const resendStatus = document.getElementById('resendStatus');
    const resendMessage = document.getElementById('resendMessage');

    if (resendLink) {
        resendLink.addEventListener('click', function(e) {
            e.preventDefault();

            // Tampilkan status loading
            resendLink.style.display = 'none';
            resendStatus.style.display = 'block';

            // Kirim request AJAX ke resend_verification.php
            fetch('../config/resend_verification.php', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resendMessage.textContent = 'Kode verifikasi baru telah dikirim!';
                    setTimeout(() => {
                        resendStatus.style.display = 'none';
                        resendLink.style.display = 'inline-block';
                    }, 2000);

                    alert('Kode verifikasi baru telah dikirim ke email Anda. Silakan cek inbox atau folder spam email Anda.');
                } else {
                    resendStatus.style.display = 'none';
                    resendLink.style.display = 'inline-block';
                    alert('Gagal mengirim kode verifikasi baru: ' + (data.message || 'Terjadi kesalahan'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resendStatus.style.display = 'none';
                resendLink.style.display = 'inline-block';
                alert('Terjadi kesalahan saat mengirim kode verifikasi baru.');
            });
        });
    }
});
</script>
</body>
</html>
