<?php
/**
 * Simple test API to check database connection and basic functionality
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Include necessary files
require_once '../config/config.php';
require_once 'security.php';

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('User not logged in');
    }

    // Check database connection
    if (!$conn) {
        throw new Exception('Database connection failed');
    }

    // Test simple query
    $test_query = "SELECT COUNT(*) as count FROM training_submissions LIMIT 1";
    $result = $conn->query($test_query);
    
    if (!$result) {
        throw new Exception('Query failed: ' . $conn->error);
    }

    $row = $result->fetch_assoc();

    echo json_encode([
        'success' => true,
        'message' => 'API is working',
        'user_id' => $_SESSION['user_id'],
        'training_count' => $row['count'],
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
