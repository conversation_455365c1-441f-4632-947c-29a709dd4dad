:root {
    --primary-color: rgb(157, 0, 0);
    --primary-color-dark: rgb(127, 0, 0);
    --primary-color-light: rgba(157, 0, 0, 0.1);
    --success-color: #4CAF50;
    --success-color-light: #e8f5e9;
    --warning-color: #ffc107;
    --danger-color: #f44336;
    --text-dark: #333;
    --text-light: #757575;
    --white: #ffffff;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
    --shadow-lg: 0 6px 15px rgba(0,0,0,0.1);
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 10px;
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
    --font-size-xs: 11px;
    --font-size-sm: 13px;
    --font-size-md: 16px;
    --font-size-lg: 20px;
    --font-size-xl: 24px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --border-color: #ddd;
    --secondary-color: #f5f5f5;
}

body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
}

/* Loading Overlay */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.loading-spinner {
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--primary-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-text {
    color: white;
    font-size: 18px;
    font-weight: bold;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.container-form {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    animation: fadeIn 0.5s ease-in-out;
}

.form-container {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-top: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-color-dark));
}

.form-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.form-header h1 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    font-weight: 600;
}

.form-header p {
    color: var(--text-light);
    font-size: var(--font-size-md);
}

.form-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.form-section h2 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    padding-left: var(--spacing-md);
}

.form-section h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background-color: var(--primary-color);
    border-radius: var(--border-radius-sm);
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}

.latest-submissions h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    padding-left: var(--spacing-md);
}

.latest-submissions h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background-color: var(--primary-color);
    border-radius: var(--border-radius-sm);
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}

.form-section h3 {
    margin-top: var(--spacing-lg);
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    padding-left: var(--spacing-md);
}

.form-section h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background-color: var(--primary-color);
    border-radius: var(--border-radius-sm);
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}
.form-group {
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-dark);
    font-size: var(--font-size-md);
}

.form-group label.required::after {
    content: '*';
    color: var(--danger-color);
    margin-left: 4px;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"],
.form-group input[type="date"],
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    transition: all var(--transition-normal);
    background-color: var(--white);
}

.form-group input[readonly] {
    background-color: var(--secondary-color);
    cursor: not-allowed;
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(157, 0, 0, 0.1);
}

.form-group small {
    display: block;
    margin-top: var(--spacing-xs);
    color: var(--text-dark);
    font-size: var(--font-size-xs);
}

/* Participant Search Section */
.search-section {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--secondary-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

#search_employee {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-md);
    transition: all var(--transition-normal);
}

#search_employee:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(157, 0, 0, 0.1);
}

#employee_results {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    max-height: 200px;
    overflow-y: auto;
    box-shadow: var(--shadow-sm);
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) #f1f1f1;
}

#employee_results::-webkit-scrollbar {
    width: 8px;
}

#employee_results::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

#employee_results::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

.employee-result {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.employee-result:hover {
    background-color: var(--primary-color-light);
}

.employee-result:last-child {
    border-bottom: none;
}

/* Participant Table */
#participantTable {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-lg);
    background: var(--white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

#participantTable th,
#participantTable td {
    padding: var(--spacing-md);
    text-align: left;
    border: 1px solid var(--border-color);
}

#participantTable th {
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
    white-space: nowrap;
}

#participantTable tr:nth-child(even) {
    background-color: var(--secondary-color);
}

#participantTable tr:hover {
    background-color: var(--primary-color-light);
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

/* Buttons */
button[type="submit"],
.action-button,
.btn-add-participant {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 600;
    font-size: var(--font-size-md);
    box-shadow: var(--shadow-sm);
    width: 100%;
    margin-top: var(--spacing-lg);
}

button[type="submit"]:hover,
.action-button:hover,
.btn-add-participant:hover {
    background: var(--primary-color-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

button[type="submit"]:active,
.action-button:active,
.btn-add-participant:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn-add-participant {
    width: auto;
    margin-top: 0;
    margin-bottom: var(--spacing-lg);
}

.btn-remove {
    background-color: var(--danger-color);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color var(--transition-fast);
    font-size: var(--font-size-xs);
}

.btn-remove:hover {
    background-color: #d32f2f;
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 90%;
    width: 350px;
}

.error-message,
.success-message {
    padding: 15px;
    border-radius: var(--border-radius-md);
    margin-bottom: 10px;
    font-size: var(--font-size-sm);
    animation: slideIn 0.3s ease-out;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: 10px;
}

.error-message {
    background: var(--danger-color);
    color: white;
    border-left: 5px solid #b71c1c;
}

.success-message {
    background: var(--success-color);
    color: white;
    border-left: 5px solid #1b5e20;
}

.notification-icon {
    font-size: 20px;
}

.notification-content {
    flex: 1;
}

.notification-close {
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.notification-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.jarak-form {
    height: 80px;
}

/* Styling untuk modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    overflow-y: auto;
}

.modal-content {
    background-color: var(--white);
    margin: 10% auto;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    width: 80%;
    max-width: 600px;
    position: relative;
    box-shadow: var(--shadow-lg);
    animation: fadeIn 0.3s ease-out;
}

.close {
    position: absolute;
    right: var(--spacing-lg);
    top: var(--spacing-md);
    color: var(--text-light);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.close:hover,
.close:focus {
    color: var(--text-dark);
}

.info-text {
    color: var(--text-light);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: block;
}

.error-text {
    color: var(--danger-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: block;
}

.loading {
    padding: var(--spacing-sm);
    text-align: center;
    color: var(--text-light);
}

.error {
    padding: var(--spacing-sm);
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: var(--border-radius-sm);
}

.no-results {
    padding: var(--spacing-sm);
    text-align: center;
    color: var(--text-light);
}

/* Mobile Card View for Participants */
.mobile-participant-card {
    display: none;
}

/* Responsive Design */
@media (max-width: 992px) {
    .container-form {
        padding: var(--spacing-md);
    }
    
    .form-container {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .container-form {
        padding: var(--spacing-sm);
        margin-top: 60px;
    }

    .form-container {
        padding: var(--spacing-md);
        margin-top: var(--spacing-md);
        border-radius: var(--border-radius-md);
    }

    .form-header h1 {
        font-size: var(--font-size-lg);
    }

    .form-header p {
        font-size: var(--font-size-sm);
    }

    .form-section {
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
    }
    
    .form-section h2 {
        font-size: var(--font-size-md);
        margin-bottom: var(--spacing-sm);
    }

    .form-group {
        margin-bottom: var(--spacing-md);
    }
    
    .form-group label {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-xs);
    }

    .form-group input[type="text"],
    .form-group input[type="email"],
    .form-group input[type="tel"],
    .form-group input[type="date"],
    .form-group textarea,
    .form-group select {
        padding: var(--spacing-sm);
        font-size: 16px; /* Optimal size for mobile inputs to prevent zoom */
        border-radius: var(--border-radius-sm);
        margin-bottom: var(--spacing-xs);
    }
    
    .form-actions {
        flex-direction: column;
        align-items: center;
    }

    button[type="submit"],
    .action-button,
    .btn-add-participant {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
        width: 100%;
        max-width: 300px;
        margin: var(--spacing-xs) 0;
        min-height: 44px; /* Better touch target */
    }

    /* Hide regular table on mobile */
    #participantTable {
        display: none;
    }

    /* Show card layout for participants on mobile */
    .mobile-participant-card {
        display: block;
        margin-bottom: var(--spacing-md);
        box-shadow: var(--shadow-sm);
        border-radius: var(--border-radius-sm);
        overflow: hidden;
        margin-top: var(--spacing-md);
    }
    
    .participant-card {
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-sm);
    }
    
    .participant-card-header {
        font-weight: bold;
        margin-bottom: var(--spacing-xs);
        color: var(--primary-color);
    }
}

@media (max-width: 480px) {
    .container-form {
        padding: var(--spacing-xs);
    }
    
    .form-container {
        padding: var(--spacing-sm);
        border-radius: var(--border-radius-sm);
    }
    
    .form-header h1 {
        font-size: var(--font-size-md);
    }
    
    .form-section {
        padding: var(--spacing-xs);
    }
    
    .form-group {
        margin-bottom: var(--spacing-sm);
    }
    
    .form-group label {
        margin-bottom: 2px;
    }
    
    .form-actions {
        padding: var(--spacing-xs);
    }
    
    button[type="submit"],
    .action-button,
    .btn-add-participant {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
        min-height: 40px;
    }
    
    .participant-card {
        padding: var(--spacing-xs);
        font-size: var(--font-size-xs);
    }

    .mobile-participant-item {
        background: var(--white);
        border-radius: var(--border-radius-md);
        box-shadow: var(--shadow-sm);
        margin-bottom: var(--spacing-md);
        overflow: hidden;
        transition: transform var(--transition-fast), box-shadow var(--transition-fast);
    }

    .mobile-participant-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .mobile-participant-header {
        background: var(--primary-color);
        color: var(--white);
        padding: var(--spacing-sm) var(--spacing-md);
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .mobile-participant-body {
        padding: var(--spacing-md);
    }

    .mobile-participant-row {
        display: flex;
        justify-content: space-between;
        padding: var(--spacing-xs) 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .mobile-participant-row:last-child {
        border-bottom: none;
    }

    .mobile-participant-label {
        font-weight: 600;
        color: var(--text-dark);
        font-size: var(--font-size-sm);
    }

    .mobile-participant-value {
        text-align: right;
        font-size: var(--font-size-sm);
    }

    .mobile-participant-footer {
        padding: var(--spacing-sm) var(--spacing-md);
        background: rgba(0,0,0,0.02);
        text-align: center;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
        padding: var(--spacing-md);
    }

    .notifications-container {
        width: 90%;
        max-width: 400px;
    }
}

@media (max-width: 480px) {
    .form-header h1 {
        font-size: var(--font-size-md);
    }

    .form-section h2 {
        font-size: var(--font-size-sm);
    }

    .jarak-form {
        height: 60px;
    }

    .btn-add-participant {
        width: 100%;
    }
}
