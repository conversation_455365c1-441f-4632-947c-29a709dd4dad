<?php
/**
 * FAQ Manager Class
 * Mengelola FAQ submissions dan FAQ management
 */

class FAQManager {
    private $conn;
    private $activityLogger;

    public function __construct($conn, $activityLogger = null) {
        $this->conn = $conn;
        $this->activityLogger = $activityLogger;
        $this->createTableIfNotExists();
        $this->addHiddenColumnIfNotExists();
    }

    /**
     * Create faq_submissions table if not exists
     */
    private function createTableIfNotExists() {
        $query = "CREATE TABLE IF NOT EXISTS faq_submissions (
            id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            user_id INT(11) NOT NULL,
            question TEXT NOT NULL,
            answer TEXT NULL,
            category VARCHAR(100) NOT NULL,
            status ENUM('pending', 'answered', 'rejected') NOT NULL DEFAULT 'pending',
            answered_by INT(11) NULL,
            created_at DATETIME NOT NULL,
            answered_at DATETIME NULL,
            INDEX (user_id),
            INDEX (status),
            INDEX (category),
            INDEX (answered_by)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        $this->conn->query($query);
    }

    /**
     * Submit new FAQ question
     */
    public function submitQuestion($user_id, $question, $category) {
        $query = "INSERT INTO faq_submissions (user_id, question, category, status, created_at)
                  VALUES (?, ?, ?, 'pending', NOW())";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("iss", $user_id, $question, $category);

        if ($stmt->execute()) {
            $submission_id = $this->conn->insert_id;

            // Log activity
            if ($this->activityLogger) {
                $this->activityLogger->logFAQSubmission($user_id, $question);
            }

            return $submission_id;
        }

        return false;
    }

    /**
     * Get FAQ submissions
     */
    public function getSubmissions($status = null, $limit = 50) {
        try {
            $query = "SELECT fs.*, u.name as submitter_name, u.email as submitter_email,
                             answerer.name as answerer_name
                      FROM faq_submissions fs
                      LEFT JOIN users u ON fs.user_id = u.id
                      LEFT JOIN users answerer ON fs.answered_by = answerer.id
                      WHERE 1=1";

            $params = [];
            $types = "";

            if ($status) {
                $query .= " AND fs.status = ?";
                $params[] = $status;
                $types .= "s";
            }

            $query .= " ORDER BY fs.created_at DESC LIMIT ?";
            $params[] = $limit;
            $types .= "i";

            $stmt = $this->conn->prepare($query);
            if (!empty($params)) {
                $stmt->bind_param($types, ...$params);
            }
            $stmt->execute();

            return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
        } catch (Exception $e) {
            error_log("FAQ getSubmissions error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get user's FAQ submissions
     */
    public function getUserSubmissions($user_id, $limit = 20) {
        $query = "SELECT fs.*, answerer.name as answerer_name
                  FROM faq_submissions fs
                  LEFT JOIN users answerer ON fs.answered_by = answerer.id
                  WHERE fs.user_id = ?
                  ORDER BY fs.created_at DESC LIMIT ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("ii", $user_id, $limit);
        $stmt->execute();

        return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    }

    /**
     * Answer FAQ submission
     */
    public function answerSubmission($submission_id, $answer, $answered_by, $status = 'answered') {
        $query = "UPDATE faq_submissions
                  SET answer = ?, answered_by = ?, answered_at = NOW(), status = ?
                  WHERE id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("sisi", $answer, $answered_by, $status, $submission_id);

        if ($stmt->execute()) {
            // Log activity
            if ($this->activityLogger) {
                $this->activityLogger->log($answered_by, "Answered FAQ submission ID: $submission_id", 'faq');
            }

            return true;
        }

        return false;
    }

    /**
     * Reject FAQ submission
     */
    public function rejectSubmission($submission_id, $answered_by, $reason = null) {
        $answer = $reason ? "Rejected: " . $reason : "Question rejected";

        return $this->answerSubmission($submission_id, $answer, $answered_by, 'rejected');
    }

    /**
     * Approve and add to FAQ
     */
    public function approveAndAddToFAQ($submission_id, $answer, $answered_by) {
        // Get submission details
        $submission = $this->getSubmissionById($submission_id);
        if (!$submission) {
            return false;
        }

        // Start transaction
        $this->conn->begin_transaction();

        try {
            // Answer the submission
            $this->answerSubmission($submission_id, $answer, $answered_by, 'answered');

            // Add to main FAQ table
            $query = "INSERT INTO faq (question, answer, category, created_at)
                      VALUES (?, ?, ?, NOW())";

            $stmt = $this->conn->prepare($query);
            $stmt->bind_param("sss", $submission['question'], $answer, $submission['category']);
            $stmt->execute();

            $faq_id = $this->conn->insert_id;

            // Log activity
            if ($this->activityLogger) {
                $this->activityLogger->log($answered_by, "Added FAQ from submission ID: $submission_id", 'faq');
            }

            $this->conn->commit();
            return $faq_id;

        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }

    /**
     * Get submission by ID
     */
    public function getSubmissionById($submission_id) {
        $query = "SELECT fs.*, u.name as submitter_name, u.email as submitter_email
                  FROM faq_submissions fs
                  LEFT JOIN users u ON fs.user_id = u.id
                  WHERE fs.id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $submission_id);
        $stmt->execute();

        return $stmt->get_result()->fetch_assoc();
    }

    /**
     * Get FAQ categories
     */
    public function getCategories() {
        $query = "SELECT DISTINCT category FROM faq_submissions
                  UNION
                  SELECT DISTINCT category FROM faq
                  ORDER BY category";

        $result = $this->conn->query($query);
        return $result->fetch_all(MYSQLI_ASSOC);
    }

    /**
     * Get submission statistics
     */
    public function getSubmissionStats() {
        try {
            $query = "SELECT
                        status,
                        COUNT(*) as count,
                        category
                      FROM faq_submissions
                      GROUP BY status, category
                      ORDER BY category, status";

            $result = $this->conn->query($query);
            return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        } catch (Exception $e) {
            error_log("FAQ getSubmissionStats error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Search FAQ submissions
     */
    public function searchSubmissions($search_term, $category = null, $status = null) {
        $query = "SELECT fs.*, u.name as submitter_name
                  FROM faq_submissions fs
                  LEFT JOIN users u ON fs.user_id = u.id
                  WHERE (fs.question LIKE ? OR fs.answer LIKE ?)";

        $search_param = "%$search_term%";
        $params = [$search_param, $search_param];
        $types = "ss";

        if ($category) {
            $query .= " AND fs.category = ?";
            $params[] = $category;
            $types .= "s";
        }

        if ($status) {
            $query .= " AND fs.status = ?";
            $params[] = $status;
            $types .= "s";
        }

        $query .= " ORDER BY fs.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();

        return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    }

    /**
     * Delete FAQ submission
     */
    public function deleteSubmission($submission_id, $user_id) {
        $query = "DELETE FROM faq_submissions WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $submission_id);

        if ($stmt->execute()) {
            // Log activity
            if ($this->activityLogger) {
                $this->activityLogger->log($user_id, "Deleted FAQ submission ID: $submission_id", 'faq');
            }

            return true;
        }

        return false;
    }

    /**
     * Update FAQ submission
     */
    public function updateSubmission($submission_id, $question, $answer, $category, $status, $updated_by) {
        try {
            $query = "UPDATE faq_submissions
                      SET question = ?, answer = ?, category = ?, status = ?, answered_by = ?, answered_at = NOW()
                      WHERE id = ?";

            $stmt = $this->conn->prepare($query);
            $stmt->bind_param("ssssii", $question, $answer, $category, $status, $updated_by, $submission_id);

            if ($stmt->execute()) {
                // Log update activity
                if ($this->activityLogger) {
                    $this->activityLogger->log($updated_by, "Updated FAQ submission ID: $submission_id", 'faq');
                }
                return true;
            }
            return false;
        } catch (Exception $e) {
            error_log("FAQ updateSubmission error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get single FAQ submission
     */
    public function getSubmission($submission_id) {
        try {
            $query = "SELECT fs.*, u.name as submitter_name, u.email as submitter_email,
                             answerer.name as answerer_name
                      FROM faq_submissions fs
                      LEFT JOIN users u ON fs.user_id = u.id
                      LEFT JOIN users answerer ON fs.answered_by = answerer.id
                      WHERE fs.id = ?";

            $stmt = $this->conn->prepare($query);
            $stmt->bind_param("i", $submission_id);
            $stmt->execute();

            $result = $stmt->get_result();
            return $result->fetch_assoc();
        } catch (Exception $e) {
            error_log("FAQ getSubmission error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Toggle FAQ visibility (hide/show in public FAQ)
     */
    public function toggleFAQVisibility($faq_id, $is_hidden, $updated_by) {
        try {
            // First check if the FAQ exists
            $checkQuery = "SELECT id FROM faq WHERE id = ?";
            $checkStmt = $this->conn->prepare($checkQuery);
            $checkStmt->bind_param("i", $faq_id);
            $checkStmt->execute();
            $result = $checkStmt->get_result();

            if ($result->num_rows === 0) {
                return false; // FAQ not found
            }

            // Add is_hidden column if it doesn't exist
            $this->addHiddenColumnIfNotExists();

            // Update visibility
            $query = "UPDATE faq SET is_hidden = ? WHERE id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->bind_param("ii", $is_hidden, $faq_id);

            if ($stmt->execute()) {
                // Log visibility change
                if ($this->activityLogger) {
                    $action = $is_hidden ? "Hidden" : "Shown";
                    $this->activityLogger->log($updated_by, "$action FAQ ID: $faq_id in public FAQ", 'faq');
                }
                return true;
            }
            return false;
        } catch (Exception $e) {
            error_log("FAQ toggleFAQVisibility error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Add is_hidden column to faq table if not exists
     */
    private function addHiddenColumnIfNotExists() {
        try {
            // Cek apakah tabel faq ada
            $check_table = "SHOW TABLES LIKE 'faq'";
            $table_result = $this->conn->query($check_table);

            if ($table_result && $table_result->num_rows > 0) {
                // Cek apakah kolom is_hidden sudah ada
                $query = "SHOW COLUMNS FROM faq LIKE 'is_hidden'";
                $result = $this->conn->query($query);

                if ($result && $result->num_rows === 0) {
                    // Tambahkan kolom is_hidden
                    $alterQuery = "ALTER TABLE faq ADD COLUMN is_hidden TINYINT(1) DEFAULT 0 AFTER category";
                    if ($this->conn->query($alterQuery)) {
                        error_log("FAQ: is_hidden column added successfully");
                    } else {
                        error_log("FAQ: Failed to add is_hidden column: " . $this->conn->error);
                    }
                }
            }
        } catch (Exception $e) {
            error_log("FAQ addHiddenColumnIfNotExists error: " . $e->getMessage());
        }
    }
}
