<?php
// <PERSON>lai output buffering untuk menangkap semua output
ob_start();

// Nonaktifkan error reporting yang bisa merusak JSON
error_reporting(0);
ini_set('display_errors', 0);

// Set header JSON di awal
header('Content-Type: application/json');

// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Include fungsi NIK
include 'includes/nik_functions.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

try {
    // Validasi input
    $required_fields = ['nik', 'nama', 'tgl_masuk', 'jk'];
    $missing_fields = [];

    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $field;
        }
    }

    if (!empty($missing_fields)) {
        throw new Exception('Field berikut wajib diisi: ' . implode(', ', $missing_fields));
    }

    // Ambil data dari form
    $original_nik = $_POST['nik'];
    $nama = $_POST['nama'];
    $tgl_masuk = $_POST['tgl_masuk'];
    $jk = $_POST['jk'];
    $level_karyawan = $_POST['level_karyawan'] ?? 0;
    $tgl_lahir = $_POST['tgl_lahir'] ?? null;
    $agama = $_POST['agama'] ?? '';
    $pendidikan_akhir = $_POST['pendidikan_akhir'] ?? '';
    $no_telp = $_POST['no_telp'] ?? '';
    $dept = $_POST['dept'] ?? '';
    $bagian = $_POST['bagian'] ?? '';
    $jabatan = $_POST['jabatan'] ?? '';
    $group = $_POST['group'] ?? '';
    $status = $_POST['status'] ?? 'Aktif';
    $pt = $_POST['pt'] ?? '';

    // Normalisasi NIK (hapus karakter non-angka)
    $normalized_nik = normalizeNIK($original_nik);

    // Validasi NIK setelah normalisasi
    if (empty($normalized_nik)) {
        throw new Exception('NIK tidak valid. NIK harus mengandung angka.');
    }

    // Cek apakah NIK yang sudah dinormalisasi ada di database
    $existing_employee = checkNIKExists($conn, $normalized_nik);
    if ($existing_employee) {
        throw new Exception('NIK sudah terdaftar dengan format yang berbeda: ' . $existing_employee['nik']);
    }

    // Siapkan data karyawan untuk pengecekan duplikasi
    $employee_data = [
        'nik' => $normalized_nik,
        'nama' => $nama,
        'tgl_lahir' => $tgl_lahir,
        'no_telp' => $no_telp,
        'dept' => $dept,
        'bagian' => $bagian,
        'jabatan' => $jabatan
    ];

    // Cek kemungkinan duplikasi berdasarkan nama dan data lainnya
    $possible_duplicates = checkPossibleDuplicates($conn, $employee_data);

    // Jika ada kemungkinan duplikasi, periksa lebih lanjut
    if (!empty($possible_duplicates)) {
        foreach ($possible_duplicates as $duplicate) {
            if (isPossibleSamePerson($employee_data, $duplicate)) {
                // Jika mode force_add tidak diaktifkan, tampilkan peringatan
                if (!isset($_POST['force_add']) || $_POST['force_add'] != 1) {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Kemungkinan duplikasi data karyawan ditemukan',
                        'duplicate_warning' => true,
                        'duplicate_data' => [
                            'nik' => $duplicate['nik'],
                            'nama' => $duplicate['nama'],
                            'dept' => $duplicate['dept'],
                            'bagian' => $duplicate['bagian'],
                            'jabatan' => $duplicate['jabatan']
                        ]
                    ]);
                    exit();
                }
                // Jika force_add diaktifkan, lanjutkan dengan penambahan
                break;
            }
        }
    }

    // Gunakan NIK asli untuk penyimpanan ke database
    $nik = $original_nik;

    // Cek apakah kolom normalized_nik sudah ada
    $check_column_query = "SHOW COLUMNS FROM karyawan LIKE 'normalized_nik'";
    $check_column_result = $conn->query($check_column_query);

    if ($check_column_result->num_rows > 0) {
        // Siapkan query insert dengan normalized_nik
        $query = "INSERT INTO karyawan (nik, normalized_nik, nama, tgl_masuk, jk, level_karyawan, tgl_lahir,
                                      agama, pendidikan_akhir, no_telp, dept, bagian, jabatan,
                                      `group`, status, pt)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($query);
        if (!$stmt) {
            throw new Exception("Error preparing query: " . $conn->error);
        }

        $stmt->bind_param("ssssisssssssssss",
            $original_nik, $normalized_nik, $nama, $tgl_masuk, $jk, $level_karyawan, $tgl_lahir,
            $agama, $pendidikan_akhir, $no_telp, $dept, $bagian, $jabatan,
            $group, $status, $pt);
    } else {
        // Fallback ke query lama jika kolom normalized_nik belum ada
        $query = "INSERT INTO karyawan (nik, nama, tgl_masuk, jk, level_karyawan, tgl_lahir,
                                      agama, pendidikan_akhir, no_telp, dept, bagian, jabatan,
                                      `group`, status, pt)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($query);
        if (!$stmt) {
            throw new Exception("Error preparing query: " . $conn->error);
        }

        $stmt->bind_param("sssisssssssssss",
            $original_nik, $nama, $tgl_masuk, $jk, $level_karyawan, $tgl_lahir,
            $agama, $pendidikan_akhir, $no_telp, $dept, $bagian, $jabatan,
            $group, $status, $pt);
    }

    if (!$stmt->execute()) {
        throw new Exception("Error executing query: " . $stmt->error);
    }

    // Log aktivitas
    $user_id = $_SESSION['user_id'];
    $log_query = "INSERT INTO activity_logs (user_id, action, category, timestamp)
                  VALUES (?, CONCAT('Menambahkan karyawan baru: ', ?), 'employee', NOW())";

    $log_stmt = $conn->prepare($log_query);
    if ($log_stmt) {
        $log_stmt->bind_param("is", $user_id, $nama);
        $log_stmt->execute();
    }

    // Kembalikan response sukses
    echo json_encode([
        'success' => true,
        'message' => 'Karyawan berhasil ditambahkan',
        'employee' => [
            'nik' => $nik,
            'nama' => $nama
        ]
    ]);

} catch (Exception $e) {
    // Kembalikan response error
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    // Bersihkan output buffer dan kirim respons
    $output = ob_get_clean();

    // Periksa apakah output adalah JSON yang valid
    json_decode($output);
    if (json_last_error() !== JSON_ERROR_NONE) {
        // Jika bukan JSON valid, ganti dengan pesan error JSON
        echo json_encode([
            'success' => false,
            'message' => 'Server error: Invalid JSON response',
            'debug_info' => 'Output contains non-JSON content'
        ]);
    } else {
        // Jika JSON valid, kirim output asli
        echo $output;
    }

    // Pastikan tidak ada output lain setelah ini
    exit();
}
