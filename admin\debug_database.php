<?php
session_start();
include '../config/config.php';

// Set admin session for testing
$_SESSION['user_id'] = 1;
$_SESSION['role_id'] = 99;

echo "<h2>Database Debug - Training Submissions</h2>";

// Test 1: Database connection
echo "<h3>1. Database Connection</h3>";
if ($conn) {
    echo "✅ Database connected successfully<br>";
    echo "Connection info: " . $conn->host_info . "<br>";
} else {
    echo "❌ Database connection failed: " . mysqli_connect_error() . "<br>";
    exit();
}

// Test 2: Check if table exists
echo "<h3>2. Table Existence Check</h3>";
$table_check = "SHOW TABLES LIKE 'training_submissions'";
$table_result = $conn->query($table_check);

if ($table_result && $table_result->num_rows > 0) {
    echo "✅ training_submissions table exists<br>";
} else {
    echo "❌ training_submissions table does not exist<br>";
    
    // Show all tables
    $all_tables = $conn->query("SHOW TABLES");
    echo "<h4>Available tables:</h4>";
    while ($table = $all_tables->fetch_array()) {
        echo "- " . $table[0] . "<br>";
    }
    exit();
}

// Test 3: Table structure
echo "<h3>3. Table Structure</h3>";
$structure_query = "DESCRIBE training_submissions";
$structure_result = $conn->query($structure_query);

if ($structure_result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $datetime_columns = [];
    while ($col = $structure_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "<td>" . $col['Extra'] . "</td>";
        echo "</tr>";
        
        // Track datetime columns
        if (strpos(strtolower($col['Type']), 'datetime') !== false || 
            strpos(strtolower($col['Type']), 'timestamp') !== false ||
            strpos(strtolower($col['Type']), 'date') !== false) {
            $datetime_columns[] = $col['Field'];
        }
    }
    echo "</table>";
    
    echo "<h4>Date/Time columns found:</h4>";
    if (!empty($datetime_columns)) {
        foreach ($datetime_columns as $dt_col) {
            echo "- " . $dt_col . "<br>";
        }
    } else {
        echo "No date/time columns found<br>";
    }
} else {
    echo "❌ Could not get table structure: " . $conn->error . "<br>";
}

// Test 4: Count records
echo "<h3>4. Record Count</h3>";
$count_query = "SELECT COUNT(*) as total FROM training_submissions";
$count_result = $conn->query($count_query);

if ($count_result) {
    $total = $count_result->fetch_assoc()['total'];
    echo "Total records: <strong>$total</strong><br>";
    
    if ($total == 0) {
        echo "❌ No data in table<br>";
    } else {
        echo "✅ Data found in table<br>";
    }
} else {
    echo "❌ Count query failed: " . $conn->error . "<br>";
}

// Test 5: Sample data with basic columns only
echo "<h3>5. Sample Data (Basic Columns)</h3>";
$sample_query = "SELECT id, full_name, training_topic, status FROM training_submissions ORDER BY id DESC LIMIT 3";
$sample_result = $conn->query($sample_query);

if ($sample_result) {
    echo "✅ Basic query successful<br>";
    echo "Records returned: " . $sample_result->num_rows . "<br>";
    
    if ($sample_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Full Name</th><th>Training Topic</th><th>Status</th></tr>";
        while ($row = $sample_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['training_topic']) . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "❌ Basic query failed: " . $conn->error . "<br>";
}

// Test 6: Check for problematic datetime values
if (!empty($datetime_columns)) {
    echo "<h3>6. DateTime Column Analysis</h3>";
    
    foreach ($datetime_columns as $dt_col) {
        echo "<h4>Column: $dt_col</h4>";
        
        // Check for empty strings
        $empty_check = "SELECT COUNT(*) as count FROM training_submissions WHERE $dt_col = ''";
        $empty_result = $conn->query($empty_check);
        if ($empty_result) {
            $empty_count = $empty_result->fetch_assoc()['count'];
            echo "Empty string values: $empty_count<br>";
        }
        
        // Check for NULL values
        $null_check = "SELECT COUNT(*) as count FROM training_submissions WHERE $dt_col IS NULL";
        $null_result = $conn->query($null_check);
        if ($null_result) {
            $null_count = $null_result->fetch_assoc()['count'];
            echo "NULL values: $null_count<br>";
        }
        
        // Sample values
        $sample_values = "SELECT $dt_col FROM training_submissions WHERE $dt_col IS NOT NULL AND $dt_col != '' LIMIT 3";
        $values_result = $conn->query($sample_values);
        if ($values_result && $values_result->num_rows > 0) {
            echo "Sample values:<br>";
            while ($val = $values_result->fetch_assoc()) {
                echo "- " . $val[$dt_col] . "<br>";
            }
        }
        echo "<br>";
    }
}

// Test 7: Test the exact problematic query
echo "<h3>7. Test Problematic Query</h3>";
echo "Testing query with empty date parameters...<br>";

$test_query = "SELECT id, full_name, training_topic, status FROM training_submissions WHERE (deleted_at IS NULL OR deleted_at = '') ORDER BY id DESC LIMIT 5";
$test_result = $conn->query($test_query);

if ($test_result) {
    echo "✅ Query with deleted_at condition successful<br>";
    echo "Records: " . $test_result->num_rows . "<br>";
} else {
    echo "❌ Query with deleted_at condition failed: " . $conn->error . "<br>";
}

$conn->close();
?>

<style>
table { margin: 10px 0; font-size: 12px; }
th { background-color: #f0f0f0; padding: 5px; }
td { padding: 5px; max-width: 200px; word-wrap: break-word; }
h3 { color: #333; border-bottom: 2px solid #ccc; padding-bottom: 5px; }
</style>
