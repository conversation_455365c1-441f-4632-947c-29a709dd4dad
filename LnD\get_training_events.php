<?php
/**
 * API untuk mengambil data training events untuk kalender
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Include necessary files
require_once '../config/config.php';
require_once 'security.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    // Get date range from parameters
    $start_date = $_GET['start'] ?? date('Y-m-01');
    $end_date = $_GET['end'] ?? date('Y-m-t');

    // Validate date format
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $start_date) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $end_date)) {
        throw new Exception('Invalid date format');
    }

    $events = [];

    // 1. Get Offline Training Events (only visible ones for users)
    $offline_query = "SELECT
                        id,
                        training_topic as title,
                        training_description as description,
                        start_date as date,
                        end_date,
                        is_confirmed,
                        CONCAT(COALESCE(training_time_start, ''),
                               CASE WHEN training_time_start IS NOT NULL AND training_time_end IS NOT NULL
                                    THEN ' - ' ELSE '' END,
                               COALESCE(training_time_end, '')) as time,
                        location,
                        trainer_name as trainer,
                        max_participants,
                        COALESCE(status, 'Active') as status,
                        'offline' as type
                      FROM offline_training
                      WHERE start_date BETWEEN ? AND ?
                      AND COALESCE(is_hidden, 0) = 0
                      AND (status IS NULL OR status != 'Cancelled')
                      ORDER BY start_date";

    $stmt = $conn->prepare($offline_query);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $events[] = [
            'id' => $row['id'],
            'title' => $row['title'],
            'description' => $row['description'],
            'date' => $row['date'],
            'end_date' => $row['end_date'],
            'is_confirmed' => (bool)$row['is_confirmed'],
            'time' => trim($row['time']),
            'location' => $row['location'],
            'trainer' => $row['trainer'],
            'max_participants' => $row['max_participants'],
            'status' => $row['status'],
            'type' => 'offline'
        ];
    }
    $stmt->close();

    // 2. Get Online Training Events (Training Submissions yang Approved)
    $online_query = "SELECT
                        id,
                        training_topic as title,
                        additional_info as description,
                        start_date as date,
                        end_date,
                        is_confirmed,
                        CONCAT(COALESCE(training_time_start, ''),
                               CASE WHEN training_time_start IS NOT NULL AND training_time_end IS NOT NULL
                                    THEN ' - ' ELSE '' END,
                               COALESCE(training_time_end, '')) as time,
                        COALESCE(training_place, 'Online') as location,
                        contact_person as trainer,
                        NULL as max_participants,
                        status,
                        'online' as type
                     FROM training_submissions
                     WHERE start_date BETWEEN ? AND ?
                     AND status = 'Approved'
                     AND COALESCE(is_hidden, 0) = 0
                     ORDER BY start_date";

    $stmt = $conn->prepare($online_query);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $events[] = [
            'id' => $row['id'],
            'title' => $row['title'],
            'description' => $row['description'],
            'date' => $row['date'],
            'end_date' => $row['end_date'],
            'is_confirmed' => (bool)$row['is_confirmed'],
            'time' => trim($row['time']),
            'location' => $row['location'],
            'trainer' => $row['trainer'],
            'max_participants' => $row['max_participants'],
            'status' => $row['status'],
            'type' => 'online'
        ];
    }
    $stmt->close();

    // Sort events by date
    usort($events, function($a, $b) {
        return strcmp($a['date'], $b['date']);
    });

    echo json_encode([
        'success' => true,
        'events' => $events,
        'count' => count($events)
    ]);

} catch (Exception $e) {
    error_log("Error in get_training_events.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error loading training events: ' . $e->getMessage()
    ]);
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
