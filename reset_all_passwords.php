<?php
/**
 * <PERSON><PERSON>t untuk mengubah semua password akun menjadi "asdf"
 * HANYA UNTUK TESTING - JANGAN GUNAKAN DI PRODUCTION!
 * 
 * Script ini akan:
 * 1. Backup password lama (opsional)
 * 2. Update semua password ke "asdf"
 * 3. Reset password_changed_at ke NULL (untuk trigger password expired)
 * 4. Menampilkan laporan hasil
 */

// Keamanan: <PERSON>ya bisa dijalankan dari localhost atau IP tertentu
$allowed_ips = ['127.0.0.1', '::1', 'localhost'];
$client_ip = $_SERVER['REMOTE_ADDR'] ?? $_SERVER['HTTP_CLIENT_IP'] ?? $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 'unknown';

if (!in_array($client_ip, $allowed_ips) && $client_ip !== 'unknown') {
    die("❌ Access denied. This script can only be run from localhost.");
}

include 'config/config.php';

// Konfirmasi keamanan
$confirm = $_GET['confirm'] ?? '';
$backup = $_GET['backup'] ?? 'yes';
$exclude_admin = $_GET['exclude_admin'] ?? 'yes';

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset All Passwords to Default</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>

<h1>🔐 Reset All Passwords to Default</h1>

<div class="danger">
    <h3>⚠️ PERINGATAN KEAMANAN</h3>
    <ul>
        <li><strong>Script ini SANGAT BERBAHAYA untuk production!</strong></li>
        <li>Hanya gunakan untuk testing atau development environment</li>
        <li>Semua password akan diubah menjadi "asdf"</li>
        <li>Pastikan Anda memiliki backup database sebelum menjalankan</li>
    </ul>
</div>

<?php if ($confirm !== 'yes'): ?>

<div class="info">
    <h3>📋 Informasi Script</h3>
    <p>Script ini akan mengubah semua password user menjadi "asdf" untuk testing fitur password default.</p>
    
    <h4>Yang akan dilakukan:</h4>
    <ul>
        <li>Hash password "asdf" dengan PASSWORD_DEFAULT</li>
        <li>Update semua user passwords</li>
        <li>Reset password_changed_at ke NULL (trigger password expired)</li>
        <li>Opsional: Backup password lama ke tabel terpisah</li>
        <li>Opsional: Exclude admin accounts (role_id = 99)</li>
    </ul>
</div>

<h3>🔍 Preview Users</h3>
<?php
// Tampilkan preview users yang akan diubah
$preview_query = "SELECT id, name, nik, email, role_id, 
                  CASE WHEN role_id = 99 THEN 'Admin' ELSE 'Regular User' END as user_type
                  FROM users 
                  WHERE is_active = 1";

if ($exclude_admin === 'yes') {
    $preview_query .= " AND role_id != 99";
}

$preview_query .= " ORDER BY role_id, name LIMIT 20";

$preview_result = $conn->query($preview_query);

if ($preview_result->num_rows > 0) {
    echo "<table>";
    echo "<tr><th>ID</th><th>Name</th><th>NIK</th><th>Email</th><th>Type</th></tr>";
    
    while ($user = $preview_result->fetch_assoc()) {
        $row_class = $user['role_id'] == 99 ? 'style="background-color: #fff3cd;"' : '';
        echo "<tr {$row_class}>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['nik']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>{$user['user_type']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Count total users
    $count_query = "SELECT COUNT(*) as total FROM users WHERE is_active = 1";
    if ($exclude_admin === 'yes') {
        $count_query .= " AND role_id != 99";
    }
    $count_result = $conn->query($count_query);
    $total_users = $count_result->fetch_assoc()['total'];
    
    echo "<p><strong>Total users yang akan diubah: {$total_users}</strong></p>";
} else {
    echo "<p>Tidak ada user yang ditemukan.</p>";
}
?>

<h3>⚙️ Konfigurasi</h3>
<form method="GET" action="">
    <div class="form-group">
        <label>
            <input type="checkbox" name="backup" value="yes" <?= $backup === 'yes' ? 'checked' : '' ?>>
            Backup password lama sebelum mengubah
        </label>
    </div>
    
    <div class="form-group">
        <label>
            <input type="checkbox" name="exclude_admin" value="yes" <?= $exclude_admin === 'yes' ? 'checked' : '' ?>>
            Exclude admin accounts (role_id = 99)
        </label>
    </div>
    
    <div class="form-group">
        <label>
            <input type="checkbox" name="confirm" value="yes" required>
            <strong>Saya memahami risiko dan ingin melanjutkan</strong>
        </label>
    </div>
    
    <button type="submit" class="btn btn-danger">🚀 EXECUTE RESET</button>
    <a href="?" class="btn btn-secondary">🔄 Refresh Preview</a>
</form>

<?php else: ?>

<div class="warning">
    <h3>🚀 Executing Password Reset...</h3>
</div>

<?php
// Mulai proses reset password
$errors = [];
$success_count = 0;
$backup_count = 0;

try {
    // 1. Buat tabel backup jika diperlukan dan belum ada
    if ($backup === 'yes') {
        $backup_table_query = "CREATE TABLE IF NOT EXISTS password_backup (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            old_password VARCHAR(255) NOT NULL,
            backup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            backup_reason VARCHAR(100) DEFAULT 'mass_reset_to_default'
        )";
        
        if ($conn->query($backup_table_query)) {
            echo "<div class='info'>✅ Backup table created/verified</div>";
        } else {
            $errors[] = "Failed to create backup table: " . $conn->error;
        }
    }
    
    // 2. Hash password "asdf"
    $default_password_hash = password_hash('asdf', PASSWORD_DEFAULT);
    echo "<div class='info'>✅ Default password hashed</div>";
    
    // 3. Ambil semua user yang akan diubah
    $users_query = "SELECT id, COALESCE(password, '') as password FROM users WHERE is_active = 1";
    if ($exclude_admin === 'yes') {
        $users_query .= " AND role_id != 99";
    }
    
    $users_result = $conn->query($users_query);
    
    if ($users_result->num_rows > 0) {
        // 4. Mulai transaksi
        $conn->begin_transaction();
        
        while ($user = $users_result->fetch_assoc()) {
            try {
                // Backup password lama jika diperlukan
                if ($backup === 'yes') {
                    // Handle NULL password - backup as empty string with note
                    $password_to_backup = $user['password'];
                    if (empty($password_to_backup)) {
                        $password_to_backup = '[NULL_OR_EMPTY_PASSWORD]';
                    }

                    $backup_query = "INSERT INTO password_backup (user_id, old_password) VALUES (?, ?)";
                    $backup_stmt = $conn->prepare($backup_query);
                    $backup_stmt->bind_param("is", $user['id'], $password_to_backup);

                    if ($backup_stmt->execute()) {
                        $backup_count++;
                    }
                    $backup_stmt->close();
                }
                
                // Update password ke "asdf"
                $update_query = "UPDATE users SET 
                                password = ?, 
                                password_changed_at = NULL 
                                WHERE id = ?";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bind_param("si", $default_password_hash, $user['id']);
                
                if ($update_stmt->execute()) {
                    $success_count++;
                } else {
                    $errors[] = "Failed to update user ID {$user['id']}: " . $update_stmt->error;
                }
                $update_stmt->close();
                
            } catch (Exception $e) {
                $errors[] = "Error processing user ID {$user['id']}: " . $e->getMessage();
            }
        }
        
        // 5. Commit transaksi jika tidak ada error
        if (empty($errors)) {
            $conn->commit();
            echo "<div class='success'>
                    <h3>✅ Password Reset Completed Successfully!</h3>
                    <ul>
                        <li><strong>Users updated:</strong> {$success_count}</li>";
            if ($backup === 'yes') {
                echo "<li><strong>Passwords backed up:</strong> {$backup_count}</li>";
            }
            echo "</ul></div>";
            
            // Tampilkan instruksi testing
            echo "<div class='info'>
                    <h3>🧪 Testing Instructions</h3>
                    <ol>
                        <li>Semua user sekarang memiliki password: <strong>asdf</strong></li>
                        <li>Login dengan user manapun menggunakan password <strong>asdf</strong></li>
                        <li>User akan otomatis diarahkan ke change_password.php</li>
                        <li>Verifikasi pesan warning muncul</li>
                        <li>Test proses change password</li>
                    </ol>
                  </div>";
            
        } else {
            $conn->rollback();
            echo "<div class='danger'>
                    <h3>❌ Transaction Rolled Back</h3>
                    <p>Errors occurred during password reset:</p>
                    <ul>";
            foreach ($errors as $error) {
                echo "<li>{$error}</li>";
            }
            echo "</ul></div>";
        }
        
    } else {
        echo "<div class='warning'>⚠️ No users found to update</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='danger'>❌ Fatal Error: " . $e->getMessage() . "</div>";
}

// 6. Cleanup dan instruksi selanjutnya
echo "<div class='warning'>
        <h3>🧹 Cleanup Instructions</h3>
        <ul>
            <li>Hapus file ini setelah testing selesai: <code>rm reset_all_passwords.php</code></li>
            <li>Jika ada backup, restore dari backup table jika diperlukan</li>
            <li>Untuk production, pastikan semua user mengubah password mereka</li>
        </ul>
      </div>";

echo "<div class='info'>
        <h3>📊 Database Queries for Verification</h3>
        <p>Untuk verifikasi hasil:</p>
        <pre>-- Cek user dengan password default
SELECT id, name, nik, 
       CASE WHEN password_changed_at IS NULL THEN 'Default/Expired' ELSE 'Custom' END as password_status
FROM users 
WHERE is_active = 1;

-- Cek backup (jika ada)
SELECT COUNT(*) as backup_count FROM password_backup WHERE backup_reason = 'mass_reset_to_default';</pre>
      </div>";

?>

<a href="?" class="btn btn-secondary">🔄 Back to Main Page</a>
<a href="test_default_password.php" class="btn btn-warning">🧪 Go to Testing Page</a>

<?php endif; ?>

</body>
</html>

<?php $conn->close(); ?>
