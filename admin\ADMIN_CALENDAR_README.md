# Sistem Manajemen Kalender Training - Admin

## Deskripsi
Sistem manajemen kalender training untuk admin yang memungkinkan pengelolaan lengkap Training Internal dan online dengan fitur CRUD (Create, Read, Update, Delete) dan manajemen peserta.

## Konsep Training
- **Training Internal**: Data dari tabel `offline_training` - training yang dilakukan secara tatap muka
- **Training Eksternal**: Data dari tabel `training_submissions` yang statusnya "Approved" - training yang diajukan dan disetujui

## Fitur Utama

### 1. Dashboard Kalender Admin
**File**: `admin/calendar_management.php`

#### Fitur:
- **Statistik Real-time**: Total Training Internal/online, upcoming trainings
- **Kalender Interaktif**: Navigasi bulan, tampilan grid kalender
- **Quick Actions**: Tombol cepat untuk menambah training
- **Event Management**: Klik event untuk detail, edit, atau hapus

#### Komponen:
- Statistics cards dengan data real-time
- Calendar grid dengan navigasi bulan
- Modal untuk detail training
- Form untuk tambah/edit training

### 2. Manajemen Training

#### Tambah Training Baru
- **Offline Training**: Disimpan ke tabel `offline_training`
- **Online Training**: Disimpan ke tabel `training_submissions` dengan status "Approved"

#### Edit Training
- Form pre-filled dengan data existing
- Update langsung ke database
- Validasi input dan error handling

#### Hapus Training
- Konfirmasi sebelum hapus
- Cascade delete untuk data terkait (peserta, attendance)
- Transaction untuk data integrity

### 3. Manajemen Peserta
**File**: `admin/manage_participants.php`

#### Fitur:
- **Lihat Peserta**: Daftar peserta per training
- **Tambah Peserta**: 
  - Offline: Input manual NIK dan nama
  - Online: Pilih dari daftar user aktif
- **Hapus Peserta**: Remove peserta dari training
- **Status Tracking**: Monitor status kehadiran

### 4. Laporan Training
**File**: `admin/training_reports.php`

#### Fitur:
- **Filter Laporan**: Berdasarkan tanggal dan jenis training
- **Statistik Lengkap**: Total training, peserta, dll
- **Export Excel**: Download laporan dalam format Excel
- **Detail Training**: Informasi lengkap setiap training

### 5. Export & Import
**File**: `admin/api/export_calendar.php`

#### Fitur:
- Export kalender ke Excel
- Format tabel dengan styling
- Summary statistics
- Filter berdasarkan periode

## Struktur File

### Frontend
```
admin/
├── calendar_management.php     # Halaman utama kalender admin
├── manage_participants.php     # Manajemen peserta training
├── training_reports.php        # Laporan dan statistik
└── calendar_admin.js          # JavaScript untuk kalender
```

### Backend API
```
admin/api/
├── get_admin_training_events.php    # API ambil events training
├── get_admin_training_detail.php    # API detail training + peserta
├── save_training.php               # API simpan/update training
├── delete_training.php             # API hapus training
└── export_calendar.php             # API export Excel
```

## Database Schema

### offline_training
```sql
- id (Primary Key)
- training_topic (Judul training)
- training_description (Deskripsi)
- date_start (Tanggal Mulai)
- date_end (Tanggal selesai)
- is_confirmed (Status konfirmasi)
- training_time_start (Waktu mulai)
- training_time_end (Waktu selesai)
- location (Lokasi)
- trainer_name (Nama trainer)
- max_participants (Maksimal peserta)
- status (Active/Completed/Cancelled)
- created_by (User ID pembuat)
- created_at, updated_at (Timestamps)
```

### training_submissions (untuk Online Training)
```sql
- id (Primary Key)
- user_id (Foreign Key ke users)
- training_topic (Judul training)
- additional_info (Deskripsi)
- training_date_fixed (Tanggal training)
- training_time_start (Waktu mulai)
- training_time_end (Waktu selesai)
- training_place (Lokasi)
- contact_person (Trainer/PIC)
- status (Approved untuk training aktif)
```

### training_attendance (Peserta Offline)
```sql
- id (Primary Key)
- training_id (Foreign Key ke offline_training)
- karyawan_id (ID karyawan)
- nik (NIK karyawan)
- nama (Nama karyawan)
- status (hadir/tidak hadir)
```

### training_participants (Peserta Online)
```sql
- id (Primary Key)
- training_id (Foreign Key ke training_submissions)
- user_id (Foreign Key ke users)
- status (active/inactive/completed)
```

## API Endpoints

### GET /admin/api/get_admin_training_events.php
**Parameter**: `start`, `end` (format: YYYY-MM-DD)
**Response**: Array events dengan detail training

### GET /admin/api/get_admin_training_detail.php
**Parameter**: `id`, `type` (offline/online)
**Response**: Detail training + daftar peserta

### POST /admin/api/save_training.php
**Body**: Form data training
**Response**: Success/error message

### POST /admin/api/delete_training.php
**Body**: JSON `{id, type}`
**Response**: Success/error message

### GET /admin/api/export_calendar.php
**Parameter**: `start`, `end` (optional)
**Response**: Excel file download

## Cara Penggunaan

### 1. Akses Kalender Admin
```
http://localhost/Training/admin/calendar_management.php
```

### 2. Menambah Training
1. Klik tombol "Tambah Training Internal/Online"
2. Isi form dengan lengkap
3. Klik "Simpan"

### 3. Edit Training
1. Klik event training di kalender
2. Klik tombol "Edit Training"
3. Ubah data yang diperlukan
4. Klik "Simpan"

### 4. Kelola Peserta
1. Klik event training di kalender
2. Klik tombol "Kelola Peserta"
3. Tambah/hapus peserta sesuai kebutuhan

### 5. Lihat Laporan
1. Akses menu "Laporan Training"
2. Set filter tanggal dan jenis training
3. Klik "Filter" atau "Export Excel"

## Security Features

### Authentication & Authorization
- Session-based authentication
- Role-based access (admin only)
- CSRF protection pada form

### Input Validation
- Server-side validation semua input
- SQL injection prevention (prepared statements)
- XSS protection (htmlspecialchars)

### Data Integrity
- Database transactions untuk operasi kompleks
- Foreign key constraints
- Cascade delete untuk data consistency

## Performance Optimization

### Database
- Indexed columns untuk query cepat
- Optimized queries dengan LIMIT
- Prepared statements untuk efficiency

### Frontend
- Lazy loading untuk data besar
- Minimal DOM manipulation
- Efficient event handling

### Caching
- Browser caching untuk static assets
- Session caching untuk user data

## Browser Support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Troubleshooting

### Kalender Tidak Muncul
1. Check JavaScript console untuk error
2. Pastikan API endpoints dapat diakses
3. Verify database connection

### Error Saat Simpan Training
1. Check form validation
2. Verify database permissions
3. Check error logs

### Peserta Tidak Muncul
1. Verify training ID dan type
2. Check database relationships
3. Ensure user permissions

## Future Enhancements

1. **Notifikasi Real-time**: WebSocket untuk update live
2. **Bulk Operations**: Import/export peserta via Excel
3. **Calendar Integration**: Sync dengan Google Calendar
4. **Mobile App**: React Native untuk mobile access
5. **Advanced Reporting**: Dashboard analytics dengan charts
6. **Workflow Approval**: Multi-level approval untuk training
7. **Resource Management**: Booking ruangan dan equipment
8. **Attendance Tracking**: QR code untuk absensi
9. **Certificate Generation**: Auto-generate sertifikat
10. **Integration API**: REST API untuk sistem eksternal

## Maintenance

### Regular Tasks
- Backup database harian
- Clean up old logs
- Update dependencies
- Monitor performance

### Monitoring
- Error logging ke file
- Performance metrics
- User activity tracking
- System health checks
