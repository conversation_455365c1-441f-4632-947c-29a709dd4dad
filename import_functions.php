<?php
/**
 * Modified import functions for database management
 */

// Function to import database from backup file
function importDatabase($backup_file, $db_host, $db_user, $db_pass, $db_name) {
    try {
        // Check if backup file exists
        if (!file_exists($backup_file)) {
            return [
                'success' => false,
                'message' => "File backup tidak ditemukan: $backup_file"
            ];
        }

        // Check if file is empty
        if (filesize($backup_file) === 0) {
            return [
                'success' => false,
                'message' => "File backup kosong: $backup_file"
            ];
        }

        // Connect to MySQL server
        $conn = new mysqli($db_host, $db_user, $db_pass);
        if ($conn->connect_error) {
            return [
                'success' => false,
                'message' => "Koneksi gagal: " . $conn->connect_error
            ];
        }

        // Check if database exists, create if not
        $result = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$db_name'");
        $exists = $result && $result->num_rows > 0;

        if (!$exists) {
            if (!$conn->query("CREATE DATABASE `$db_name`")) {
                return [
                    'success' => false,
                    'message' => "Gagal membuat database: " . $conn->error
                ];
            }
        }

        // Close connection
        $conn->close();

        // Build mysql command to import
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            // Windows
            $mysql_cmd = "mysql.bat";
        } else {
            // Linux/Unix/Mac
            $mysql_cmd = "mysql";
        }

        $command = "$mysql_cmd -h $db_host -u $db_user";
        if (!empty($db_pass)) {
            $command .= " -p" . escapeshellarg($db_pass);
        }
        $command .= " $db_name < " . escapeshellarg($backup_file) . " 2>&1";

        // Execute command
        $output = [];
        $return_var = 0;
        $start_time = microtime(true);
        
        // Use cmd.exe to execute the command with redirection
        $cmd_command = 'cmd /c "' . $command . '"';
        exec($cmd_command, $output, $return_var);
        
        $total_time = round(microtime(true) - $start_time, 2);

        if ($return_var !== 0) {
            return [
                'success' => false,
                'message' => "Import gagal. Error code: $return_var",
                'output' => implode("\n", $output),
                'command' => $cmd_command
            ];
        }

        // Get database status after import
        $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
        if ($conn->connect_error) {
            return [
                'success' => true,
                'message' => "Import berhasil, tetapi tidak dapat memeriksa status database.",
                'time' => $total_time
            ];
        }

        // Get table count
        $tables_query = $conn->query("SHOW TABLES");
        $table_count = $tables_query ? $tables_query->num_rows : 0;

        // Get database size
        $size_query = $conn->query("SELECT
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
            FROM information_schema.TABLES
            WHERE table_schema = '$db_name'");
        $size = $size_query ? $size_query->fetch_assoc()['Size (MB)'] : 'Unknown';

        // Close connection
        $conn->close();

        return [
            'success' => true,
            'message' => "Import berhasil diselesaikan.",
            'time' => $total_time,
            'table_count' => $table_count,
            'size' => $size
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "Exception: " . $e->getMessage()
        ];
    }
}

// Function to backup database
function backupDatabase($db_host, $db_user, $db_pass, $db_name) {
    try {
        // Check if database exists
        $conn = new mysqli($db_host, $db_user, $db_pass);
        if ($conn->connect_error) {
            return [
                'success' => false,
                'message' => "Koneksi gagal: " . $conn->connect_error
            ];
        }

        $result = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$db_name'");
        $exists = $result && $result->num_rows > 0;

        if (!$exists) {
            return [
                'success' => false,
                'message' => "Database '$db_name' tidak ditemukan."
            ];
        }

        // Create backup directory if it doesn't exist
        $backup_dir = 'backups';
        if (!file_exists($backup_dir)) {
            if (!mkdir($backup_dir, 0755, true)) {
                return [
                    'success' => false,
                    'message' => "Gagal membuat direktori backup."
                ];
            }
        }

        // Generate backup filename with timestamp
        $timestamp = date('Y-m-d_H-i-s');
        $backup_file = "$backup_dir/{$db_name}_backup_{$timestamp}.sql";

        // Build mysqldump command
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            // Windows
            $mysqldump_cmd = "mysqldump.bat";
        } else {
            // Linux/Unix/Mac
            $mysqldump_cmd = "mysqldump";
        }

        $command = "$mysqldump_cmd -h $db_host -u $db_user";
        if (!empty($db_pass)) {
            $command .= " -p" . escapeshellarg($db_pass);
        }
        $command .= " $db_name > " . escapeshellarg($backup_file) . " 2>&1";

        // Execute command
        $output = [];
        $return_var = 0;
        
        // Use cmd.exe to execute the command with redirection
        $cmd_command = 'cmd /c "' . $command . '"';
        exec($cmd_command, $output, $return_var);

        if ($return_var !== 0) {
            return [
                'success' => false,
                'message' => "Backup gagal. Error code: $return_var",
                'output' => implode("\n", $output),
                'command' => $cmd_command
            ];
        }

        // Check if backup file was created and has content
        if (!file_exists($backup_file) || filesize($backup_file) === 0) {
            return [
                'success' => false,
                'message' => "Backup file tidak dibuat atau kosong.",
                'output' => implode("\n", $output)
            ];
        }

        // Get file size
        $size_bytes = filesize($backup_file);
        $size_kb = round($size_bytes / 1024, 2);
        $size_mb = round($size_kb / 1024, 2);

        $size_str = $size_mb >= 1 ? "$size_mb MB" : "$size_kb KB";

        return [
            'success' => true,
            'message' => "Backup berhasil dibuat.",
            'file' => $backup_file,
            'size' => $size_str,
            'timestamp' => $timestamp
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "Exception: " . $e->getMessage()
        ];
    }
}
?>
