<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Judul halaman
$pageTitle = "Kelola Jabatan";
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    .container-form {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .card-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .btn-add {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .btn-edit {
        background-color: #2196F3;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 5px;
    }
    
    .btn-delete {
        background-color: #F44336;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
    }
    
    table {
        width: 100%;
        border-collapse: collapse;
    }
    
    table th, table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    
    table th {
        background-color: #bf0000;
    }
    
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }
    
    .modal-content {
        background-color: white;
        margin: 10% auto;
        padding: 20px;
        border-radius: 8px;
        width: 50%;
        max-width: 500px;
    }
    
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .modal-close {
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
    }
    
    .form-group input, .form-group textarea, .form-group select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    .form-group textarea {
        height: 100px;
    }
    
    .form-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
    }
    
    .btn-submit {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
        margin-left: 10px;
    }
    
    .btn-cancel {
        background-color: #ccc;
        color: black;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .alert {
        padding: 10px;
        margin-bottom: 15px;
        border-radius: 4px;
    }
    
    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .hidden {
        display: none;
    }
    
    .filter-container {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .filter-group {
        display: flex;
        align-items: center;
    }
    
    .filter-group label {
        margin-right: 10px;
        font-weight: 600;
    }
    
    .filter-group select {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        min-width: 200px;
    }
</style>

<body>
<?php include '../config/navbarb.php'; ?>
<div class="jarak"></div>
<div class="container-form">
<button style="border-radius: 10px; margin-bottom:10px;">
        <a href="manage_organization.php" style="text-decoration:none; color:white">Kembali</a>
    </button>
    <div class="card">
        <div class="card-header">
            <h2><?php echo $pageTitle; ?></h2>
            <button class="btn-add" id="btnAddPosition">Tambah Jabatan</button>
        </div>
        <div class="card-body">
            <div id="alertContainer" class="hidden"></div>
            
            <div class="filter-container">
                <div class="filter-group">
                    <label for="departmentFilter">Departemen:</label>
                    <select id="departmentFilter">
                        <option value="">Semua Departemen</option>
                        <!-- Department options will be loaded here -->
                    </select>
                </div>
                <div class="filter-group">
                    <label for="subDepartmentFilter">Sub Departemen:</label>
                    <select id="subDepartmentFilter">
                        <option value="">Semua Sub Departemen</option>
                        <!-- Sub department options will be loaded here -->
                    </select>
                </div>
            </div>
            
            <table id="positionsTable">
                <thead>
                    <tr>
                        <th>Nama Jabatan</th>
                        <th>Sub Departemen</th>
                        <th>Departemen</th>
                        <th>Deskripsi</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal for adding/editing position -->
<div id="positionModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">Tambah Jabatan</h3>
            <span class="modal-close">&times;</span>
        </div>
        <form id="positionForm">
            <input type="hidden" id="positionId" name="id">
            <div class="form-group">
                <label for="modalDepartmentId">Departemen *</label>
                <select id="modalDepartmentId" required>
                    <option value="">Pilih Departemen</option>
                    <!-- Department options will be loaded here -->
                </select>
            </div>
            <div class="form-group">
                <label for="subDepartmentId">Sub Departemen *</label>
                <select id="subDepartmentId" name="sub_department_id" required>
                    <option value="">Pilih Sub Departemen</option>
                    <!-- Sub department options will be loaded here -->
                </select>
            </div>
            <div class="form-group">
                <label for="positionName">Nama Jabatan *</label>
                <input type="text" id="positionName" name="name" required maxlength="100">
            </div>
            <div class="form-group">
                <label for="positionDescription">Deskripsi</label>
                <textarea id="positionDescription" name="description"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn-cancel" id="btnCancelPosition">Batal</button>
                <button type="submit" class="btn-submit">Simpan</button>
            </div>
        </form>
    </div>
</div>

<!-- Modal for confirmation -->
<div id="confirmationModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Konfirmasi</h3>
            <span class="modal-close">&times;</span>
        </div>
        <p id="confirmationMessage">Apakah Anda yakin ingin menghapus jabatan ini?</p>
        <div class="form-actions">
            <button type="button" class="btn-cancel" id="btnCancelConfirmation">Batal</button>
            <button type="button" class="btn-submit" id="btnConfirm">Ya, Hapus</button>
        </div>
    </div>
</div>

<script>
    // Global variables
    let departments = [];
    let subDepartments = [];
    let positions = [];
    let currentPositionId = null;
    
    // DOM elements
    const positionsTable = document.getElementById('positionsTable');
    const positionModal = document.getElementById('positionModal');
    const confirmationModal = document.getElementById('confirmationModal');
    const positionForm = document.getElementById('positionForm');
    const alertContainer = document.getElementById('alertContainer');
    const departmentFilter = document.getElementById('departmentFilter');
    const subDepartmentFilter = document.getElementById('subDepartmentFilter');
    const modalDepartmentId = document.getElementById('modalDepartmentId');
    const subDepartmentId = document.getElementById('subDepartmentId');
    
    // Buttons
    const btnAddPosition = document.getElementById('btnAddPosition');
    const btnCancelPosition = document.getElementById('btnCancelPosition');
    const btnCancelConfirmation = document.getElementById('btnCancelConfirmation');
    const btnConfirm = document.getElementById('btnConfirm');
    
    // Modal close buttons
    const modalCloseButtons = document.querySelectorAll('.modal-close');
    
    // Load data on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadDepartments();
        loadSubDepartments();
        loadPositions();
    });
    
    // Event listeners
    btnAddPosition.addEventListener('click', showAddPositionModal);
    btnCancelPosition.addEventListener('click', closePositionModal);
    btnCancelConfirmation.addEventListener('click', closeConfirmationModal);
    positionForm.addEventListener('submit', savePosition);
    departmentFilter.addEventListener('change', handleDepartmentFilterChange);
    subDepartmentFilter.addEventListener('change', filterPositions);
    modalDepartmentId.addEventListener('change', updateSubDepartmentDropdown);
    
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            positionModal.style.display = 'none';
            confirmationModal.style.display = 'none';
        });
    });
    
    // Functions
    function loadDepartments() {
        fetch('api/get_organization_data.php?type=departments')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    departments = data.data.departments;
                    populateDepartmentDropdowns();
                } else {
                    showAlert('danger', 'Error loading departments: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'Error loading departments: ' + error.message);
            });
    }
    
    function loadSubDepartments() {
        fetch('api/get_organization_data.php?type=sub_departments')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    subDepartments = data.data.sub_departments;
                    populateSubDepartmentDropdowns();
                } else {
                    showAlert('danger', 'Error loading sub departments: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'Error loading sub departments: ' + error.message);
            });
    }
    
    function loadPositions() {
        fetch('api/get_organization_data.php?type=positions')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    positions = data.data.positions;
                    renderPositionsTable();
                } else {
                    showAlert('danger', 'Error loading positions: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'Error loading positions: ' + error.message);
            });
    }
    
    function populateDepartmentDropdowns() {
        // Clear existing options
        departmentFilter.innerHTML = '<option value="">Semua Departemen</option>';
        modalDepartmentId.innerHTML = '<option value="">Pilih Departemen</option>';
        
        // Add department options
        departments.forEach(department => {
            const filterOption = document.createElement('option');
            filterOption.value = department.id;
            filterOption.textContent = `${department.name} (${department.code})`;
            departmentFilter.appendChild(filterOption);
            
            const modalOption = document.createElement('option');
            modalOption.value = department.id;
            modalOption.textContent = `${department.name} (${department.code})`;
            modalDepartmentId.appendChild(modalOption);
        });
    }
    
    function populateSubDepartmentDropdowns() {
        // Clear existing options in filter dropdown
        subDepartmentFilter.innerHTML = '<option value="">Semua Sub Departemen</option>';
        
        // Add all sub departments to filter dropdown
        subDepartments.forEach(subDepartment => {
            const department = departments.find(d => d.id == subDepartment.department_id);
            const departmentName = department ? department.code : '';
            
            const option = document.createElement('option');
            option.value = subDepartment.id;
            option.textContent = `${subDepartment.name} (${subDepartment.code}) - ${departmentName}`;
            option.dataset.departmentId = subDepartment.department_id;
            subDepartmentFilter.appendChild(option);
        });
        
        // Update sub department dropdown in modal based on selected department
        updateSubDepartmentDropdown();
    }
    
    function updateSubDepartmentDropdown() {
        // Clear existing options
        subDepartmentId.innerHTML = '<option value="">Pilih Sub Departemen</option>';
        
        const departmentId = modalDepartmentId.value;
        if (!departmentId) return;
        
        // Filter sub departments by department
        const filteredSubDepartments = subDepartments.filter(sd => sd.department_id == departmentId);
        
        // Add filtered sub departments to dropdown
        filteredSubDepartments.forEach(subDepartment => {
            const option = document.createElement('option');
            option.value = subDepartment.id;
            option.textContent = `${subDepartment.name} (${subDepartment.code})`;
            subDepartmentId.appendChild(option);
        });
    }
    
    function handleDepartmentFilterChange() {
        // Clear sub department filter
        subDepartmentFilter.value = '';
        
        // Show/hide sub department options based on selected department
        const departmentId = departmentFilter.value;
        const subDepartmentOptions = subDepartmentFilter.querySelectorAll('option');
        
        subDepartmentOptions.forEach(option => {
            if (option.value === '' || !departmentId || option.dataset.departmentId === departmentId) {
                option.style.display = '';
            } else {
                option.style.display = 'none';
            }
        });
        
        // Filter positions
        filterPositions();
    }
    
    function renderPositionsTable() {
        const tbody = positionsTable.querySelector('tbody');
        tbody.innerHTML = '';
        
        // Filter positions
        let filteredPositions = positions;
        
        // Filter by department
        if (departmentFilter.value) {
            filteredPositions = filteredPositions.filter(p => {
                const subDept = subDepartments.find(sd => sd.id == p.sub_department_id);
                return subDept && subDept.department_id == departmentFilter.value;
            });
        }
        
        // Filter by sub department
        if (subDepartmentFilter.value) {
            filteredPositions = filteredPositions.filter(p => p.sub_department_id == subDepartmentFilter.value);
        }
        
        if (filteredPositions.length === 0) {
            const tr = document.createElement('tr');
            tr.innerHTML = '<td colspan="5" style="text-align: center;">Tidak ada data jabatan</td>';
            tbody.appendChild(tr);
            return;
        }
        
        filteredPositions.forEach(position => {
            const subDepartment = subDepartments.find(sd => sd.id == position.sub_department_id);
            const department = subDepartment ? departments.find(d => d.id == subDepartment.department_id) : null;
            
            const subDepartmentName = subDepartment ? `${subDepartment.name} (${subDepartment.code})` : '-';
            const departmentName = department ? `${department.name} (${department.code})` : '-';
            
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${position.name}</td>
                <td>${subDepartmentName}</td>
                <td>${departmentName}</td>
                <td>${position.description || '-'}</td>
                <td>
                    <button class="btn-edit" data-id="${position.id}">Edit</button>
                    <button class="btn-delete" data-id="${position.id}">Hapus</button>
                </td>
            `;
            tbody.appendChild(tr);
        });
        
        // Add event listeners to edit and delete buttons
        const editButtons = tbody.querySelectorAll('.btn-edit');
        const deleteButtons = tbody.querySelectorAll('.btn-delete');
        
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const positionId = this.getAttribute('data-id');
                showEditPositionModal(positionId);
            });
        });
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const positionId = this.getAttribute('data-id');
                showDeleteConfirmation(positionId);
            });
        });
    }
    
    function filterPositions() {
        renderPositionsTable();
    }
    
    function showAddPositionModal() {
        currentPositionId = null;
        document.getElementById('modalTitle').textContent = 'Tambah Jabatan';
        positionForm.reset();
        document.getElementById('positionId').value = '';
        updateSubDepartmentDropdown();
        positionModal.style.display = 'block';
    }
    
    function showEditPositionModal(positionId) {
        const position = positions.find(p => p.id == positionId);
        if (!position) return;
        
        const subDepartment = subDepartments.find(sd => sd.id == position.sub_department_id);
        if (!subDepartment) return;
        
        currentPositionId = positionId;
        document.getElementById('modalTitle').textContent = 'Edit Jabatan';
        document.getElementById('positionId').value = position.id;
        document.getElementById('modalDepartmentId').value = subDepartment.department_id;
        
        // Update sub department dropdown based on selected department
        updateSubDepartmentDropdown();
        
        document.getElementById('subDepartmentId').value = position.sub_department_id;
        document.getElementById('positionName').value = position.name;
        document.getElementById('positionDescription').value = position.description || '';
        
        positionModal.style.display = 'block';
    }
    
    function closePositionModal() {
        positionModal.style.display = 'none';
    }
    
    function showDeleteConfirmation(positionId) {
        const position = positions.find(p => p.id == positionId);
        if (!position) return;
        
        currentPositionId = positionId;
        document.getElementById('confirmationMessage').textContent = `Apakah Anda yakin ingin menghapus jabatan "${position.name}"?`;
        
        btnConfirm.onclick = function() {
            deletePosition(positionId);
        };
        
        confirmationModal.style.display = 'block';
    }
    
    function closeConfirmationModal() {
        confirmationModal.style.display = 'none';
    }
    
    function savePosition(event) {
        event.preventDefault();
        
        const formData = new FormData(positionForm);
        formData.append('type', 'position');
        formData.append('action', currentPositionId ? 'update' : 'create');
        
        fetch('api/manage_organization_data.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                closePositionModal();
                loadPositions();
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Error saving position: ' + error.message);
        });
    }
    
    function deletePosition(positionId) {
        const formData = new FormData();
        formData.append('type', 'position');
        formData.append('action', 'delete');
        formData.append('id', positionId);
        
        fetch('api/manage_organization_data.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                closeConfirmationModal();
                loadPositions();
            } else {
                showAlert('danger', data.message);
                closeConfirmationModal();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Error deleting position: ' + error.message);
            closeConfirmationModal();
        });
    }
    
    function showAlert(type, message) {
        alertContainer.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        alertContainer.classList.remove('hidden');
        
        // Hide alert after 5 seconds
        setTimeout(() => {
            alertContainer.classList.add('hidden');
        }, 5000);
    }
</script>

<?php include '../config/footer.php'; ?>
</body>
</html>
