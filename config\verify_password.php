<?php
session_start();
include 'config.php';

// Pastikan request adalah POST dan user sudah login
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

// Pastikan action adalah verify_password
if (!isset($_POST['action']) || $_POST['action'] !== 'verify_password') {
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
    exit;
}

// Ambil password dari request
$current_password = isset($_POST['current_password']) ? trim($_POST['current_password']) : '';

if (empty($current_password)) {
    echo json_encode(['success' => false, 'message' => 'Password tidak boleh kosong']);
    exit;
}

// Ambil password dari database
$user_id = $_SESSION['user_id'];
$query = "SELECT password FROM users WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'User tidak ditemukan']);
    exit;
}

$user = $result->fetch_assoc();
$stored_password = $user['password'];

// Verifikasi password
$password_verified = password_verify($current_password, $stored_password);

// Kirim response
echo json_encode([
    'success' => $password_verified,
    'message' => $password_verified ? 'Password benar' : 'Password salah'
]);

// Tutup koneksi
$stmt->close();
$conn->close();
