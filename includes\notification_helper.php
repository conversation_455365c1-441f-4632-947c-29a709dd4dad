<?php
/**
 * Helper functions for managing notifications between roles
 */

/**
 * Create a new notification for a user
 *
 * @param int $user_id User ID to notify
 * @param int|null $related_id Related ID (class_id, announcement_id, etc)
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string $type Notification type (info, warning, success, error, announcement)
 * @param string $related_type Type of related content (training, announcement, etc)
 * @return bool True if notification was created successfully
 */
function createNotification($user_id, $related_id, $title, $message, $type = 'info', $related_type = 'training') {
    global $conn;

    try {
        // Check if connection exists and is valid
        if (!isset($conn) || !$conn) {
            // Try to reconnect
            require_once __DIR__ . '/../config/config.php';
            if (!isset($conn) || !$conn) {
                return false; // Return false if connection fails
            }
        }

        // Check for connection errors
        if (isset($conn->connect_error) && $conn->connect_error) {
            return false; // Return false if connection has errors
        }

        // Create notifications table if it doesn't exist (updated schema)
        $create_table_query = "CREATE TABLE IF NOT EXISTS notifications (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            user_id INT(11) NOT NULL,
            related_id INT(11) NULL,
            related_type VARCHAR(50) NULL DEFAULT 'training',
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type ENUM('info', 'success', 'warning', 'error', 'announcement', 'training', 'reminder') DEFAULT 'info',
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            read_at TIMESTAMP NULL,
            INDEX idx_user_id (user_id),
            INDEX idx_related (related_id, related_type),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        $conn->query($create_table_query);

        $query = "INSERT INTO notifications (user_id, related_id, related_type, title, message, type)
                  VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($query);

        if (!$stmt) {
            return false; // Return false if prepare fails
        }

        $stmt->bind_param("iissss", $user_id, $related_id, $related_type, $title, $message, $type);

        $result = $stmt->execute();
        $stmt->close();
        return $result;

    } catch (Error $e) {
        // Handle mysqli object is already closed error
        error_log("Create notification mysqli error: " . $e->getMessage());
        return false;
    } catch (Exception $e) {
        // Handle other exceptions
        error_log("Create notification error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get unread notifications for a user
 * 
 * @param int $user_id User ID
 * @param int $limit Maximum number of notifications to return
 * @return array Array of notification data
 */
function getUnreadNotifications($user_id, $limit = 10) {
    global $conn;

    try {
        // Check if connection exists and is valid
        if (!isset($conn) || !$conn) {
            // Try to reconnect
            require_once __DIR__ . '/../config/config.php';
            if (!isset($conn) || !$conn) {
                return []; // Return empty array if connection fails
            }
        }

        // Check for connection errors
        if (isset($conn->connect_error) && $conn->connect_error) {
            return []; // Return empty array if connection has errors
        }

        $query = "SELECT * FROM training_notifications
                  WHERE user_id = ? AND is_read = 0
                  ORDER BY created_at DESC LIMIT ?";
        $stmt = $conn->prepare($query);

        if (!$stmt) {
            return []; // Return empty array if prepare fails
        }

        $stmt->bind_param("ii", $user_id, $limit);
        $stmt->execute();
        $result = $stmt->get_result();

        $notifications = [];
        while ($row = $result->fetch_assoc()) {
            $notifications[] = $row;
        }

        $stmt->close();
        return $notifications;

    } catch (Error $e) {
        // Handle mysqli object is already closed error
        error_log("Notification mysqli error: " . $e->getMessage());
        return [];
    } catch (Exception $e) {
        // Handle other exceptions
        error_log("Notification error: " . $e->getMessage());
        return [];
    }
}

/**
 * Mark a notification as read
 * 
 * @param int $notification_id Notification ID
 * @param int $user_id User ID (for security check)
 * @return bool True if notification was marked as read
 */
function markNotificationAsRead($notification_id, $user_id) {
    global $conn;

    // Check if connection exists and is valid
    if (!isset($conn) || !$conn || $conn->connect_error) {
        // Try to reconnect
        require_once __DIR__ . '/../config/config.php';
        if (!isset($conn) || !$conn || $conn->connect_error) {
            return false; // Return false if connection fails
        }
    }

    try {
        $query = "UPDATE training_notifications
                  SET is_read = 1
                  WHERE id = ? AND user_id = ?";
        $stmt = $conn->prepare($query);

        if (!$stmt) {
            return false; // Return false if prepare fails
        }

        $stmt->bind_param("ii", $notification_id, $user_id);
        $result = $stmt->execute();
        $stmt->close();

        return $result;
    } catch (Exception $e) {
        // Log error and return false
        error_log("Mark notification as read error: " . $e->getMessage());
        return false;
    }
}

/**
 * Notify all instructors in a class
 * 
 * @param int $class_id Class ID
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string $type Notification type
 * @return bool True if all notifications were created successfully
 */
function notifyInstructors($class_id, $title, $message, $type = 'info') {
    $instructors = getUsersByRoleInClass($class_id, 'instructor');
    $success = true;
    
    foreach ($instructors as $instructor) {
        $result = createNotification($instructor['id'], $class_id, $title, $message, $type);
        if (!$result) {
            $success = false;
        }
    }
    
    return $success;
}

/**
 * Notify all assistants in a class
 * 
 * @param int $class_id Class ID
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string $type Notification type
 * @return bool True if all notifications were created successfully
 */
function notifyAssistants($class_id, $title, $message, $type = 'info') {
    $assistants = getUsersByRoleInClass($class_id, 'assistant');
    $success = true;
    
    foreach ($assistants as $assistant) {
        $result = createNotification($assistant['id'], $class_id, $title, $message, $type);
        if (!$result) {
            $success = false;
        }
    }
    
    return $success;
}

/**
 * Notify all students in a class
 * 
 * @param int $class_id Class ID
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string $type Notification type
 * @return bool True if all notifications were created successfully
 */
function notifyStudents($class_id, $title, $message, $type = 'info') {
    $students = getUsersByRoleInClass($class_id, 'student');
    $success = true;
    
    foreach ($students as $student) {
        $result = createNotification($student['id'], $class_id, $title, $message, $type);
        if (!$result) {
            $success = false;
        }
    }
    
    return $success;
}

/**
 * Notify all teaching staff (instructors and assistants) in a class
 * 
 * @param int $class_id Class ID
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string $type Notification type
 * @return bool True if all notifications were created successfully
 */
function notifyTeachingStaff($class_id, $title, $message, $type = 'info') {
    return notifyInstructors($class_id, $title, $message, $type) && 
           notifyAssistants($class_id, $title, $message, $type);
}

/**
 * Notify everyone in a class
 *
 * @param int $class_id Class ID
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string $type Notification type
 * @return bool True if all notifications were created successfully
 */
function notifyClass($class_id, $title, $message, $type = 'info') {
    return notifyTeachingStaff($class_id, $title, $message, $type) &&
           notifyStudents($class_id, $title, $message, $type);
}

/**
 * Create announcement notifications for multiple users
 *
 * @param int $announcement_id Announcement ID
 * @param array $user_ids Array of user IDs
 * @param string $title Announcement title
 * @param string $content Announcement content (will be truncated)
 * @return int Number of notifications created
 */
function createAnnouncementNotifications($announcement_id, $user_ids, $title, $content) {
    $count = 0;
    $truncated_content = strlen($content) > 200 ? substr($content, 0, 200) . '...' : $content;

    foreach ($user_ids as $user_id) {
        if (createNotification(
            $user_id,
            $announcement_id,
            "📢 Pengumuman: " . $title,
            $truncated_content,
            'announcement',
            'announcement'
        )) {
            $count++;
        }
    }

    error_log("Created $count announcement notifications for announcement ID $announcement_id");
    return $count;
}

/**
 * Create bulk notifications for multiple users
 *
 * @param array $user_ids Array of user IDs
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string $type Notification type
 * @param int|null $related_id Related ID
 * @param string|null $related_type Related type
 * @return int Number of notifications created
 */
function createBulkNotifications($user_ids, $title, $message, $type = 'info', $related_id = null, $related_type = null) {
    $count = 0;

    foreach ($user_ids as $user_id) {
        if (createNotification($user_id, $related_id, $title, $message, $type, $related_type)) {
            $count++;
        }
    }

    error_log("Created $count bulk notifications");
    return $count;
}
