<?php
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    include '../config/config.php';

    $name = $_POST['name'];
    $nik = $_POST['nik'];
    $email = $_POST['email'];

    // Periksa apakah NIK sudah terdaftar
    $query_nik = "SELECT * FROM users WHERE nik = '$nik'";
    $result_nik = mysqli_query($conn, $query_nik);

    if (mysqli_num_rows($result_nik) > 0) {
        $error_message = "NIK sudah terdaftar.";
    } else {
        // Periksa apakah email sudah terdaftar
        $query_email = "SELECT * FROM users WHERE email = '$email'";
        $result_email = mysqli_query($conn, $query_email);

        if (mysqli_num_rows($result_email) > 0) {
            $error_message = "Email sudah terdaftar.";
        } else {
            // Masukkan data pengguna baru ke database
            $query = "INSERT INTO users (name, nik, email) VALUES ('$name', '$nik', '$email')";
            if (mysqli_query($conn, $query)) {
                $_SESSION['success'] = "Pendaftaran berhasil, silakan login.";
                header('Location: login.php');
                exit();
            } else {
                $error_message = "Gagal mendaftar, coba lagi.";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .signup-container {
        width: 100%;
        max-width: 400px;
        margin: 50px auto;
        padding: 20px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .error-message {
        color: red;
        margin-bottom: 10px;
    }
</style>
<body>
    <?php include'../config/navbarb.php'?>
<div class="container-form">
    <div class="signup-container">
        <h2>Daftar Akun</h2>
        <?php if (isset($error_message)) : ?>
            <p class="error-message"><?php echo $error_message; ?></p>
        <?php endif; ?>
        <form method="POST">
            <label for="name">Nama Lengkap:</label>
            <input type="text" name="name" required>
            
            <label for="nik">NIK:</label>
            <input type="text" name="nik" required>

            <label for="email">Email:</label>
            <input type="email" name="email" required>

            <button type="submit">Daftar</button>
        </form>
        <p>Sudah punya akun? <a href="login.php">Login disini</a></p>
    </div>
</div>
    <?php include'../config/footer.php'?>

</body>
</html>
