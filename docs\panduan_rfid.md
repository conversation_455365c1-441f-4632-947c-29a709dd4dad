# Panduan Lengkap Manajemen RFID

## Daftar Isi
1. [Registrasi Kartu RFID](#registrasi-kartu-rfid)
2. [Monitoring Kehadiran](#monitoring-kehadiran)
3. [<PERSON><PERSON><PERSON><PERSON>kat](#manajemen-perangkat)
4. [<PERSON><PERSON><PERSON>](#laporan-kehadiran)
5. [Pengaturan Sistem](#pengaturan-sistem)

## Registrasi Kartu RFID

### Alur Proses Registrasi
![Alur Registrasi Kartu RFID](../assets/docs/rfid/registrasi_kartu_flow.svg)

### Mendaftarkan Kartu Baru
1. Login sebagai admin
2. Buka menu "Manajemen RFID"
3. <PERSON><PERSON> "Registrasi Kartu"
4. Scan kartu RFID pada reader
5. Isi informasi:
   - ID Kartu (otomatis)
   - Nama Pemilik
   - Departemen
   - Tanggal Aktivasi
   - Status Kartu
6. <PERSON><PERSON> "Simpan"

### Mengubah Data Kartu
1. Cari kartu yang akan diubah
2. <PERSON><PERSON> ikon "Edit"
3. Update informasi
4. <PERSON><PERSON> "Simpan"

### Menonaktifkan Kartu
1. <PERSON><PERSON>h kartu target
2. <PERSON><PERSON> "Nonaktifkan"
3. Isi alasan penonaktifan
4. Konfirmasi perubahan

## Monitoring Kehadiran

### Dashboard Monitoring
![Dashboard Monitoring Kehadiran](../assets/docs/rfid/monitoring_kehadiran_flow.svg)

### Melihat Status Real-time
1. Buka dashboard "Monitoring Kehadiran"
2. Lihat informasi:
   - Jumlah Hadir/Tidak Hadir
   - Status per Departemen
   - Log Scan Terakhir
   - Alert Keterlambatan

### Mengatur Alert
1. Buka "Pengaturan Alert"
2. Atur parameter:
   - Batas Keterlambatan
   - Notifikasi Email
   - Eskalasi Alert
3. Simpan pengaturan

## Manajemen Perangkat

### Diagram Manajemen Perangkat
![Diagram Manajemen Perangkat RFID](../assets/docs/rfid/manajemen_perangkat_flow.svg)

### Menambah Reader RFID
1. Buka menu "Perangkat"
2. Klik "Tambah Reader"
3. Isi informasi perangkat:
   - ID Reader
   - Lokasi
   - Tipe Reader
   - IP Address
   - Status
4. Klik "Simpan"

### Monitoring Perangkat
1. Lihat status reader:
   - Online/Offline
   - Jumlah Scan
   - Error Log
2. Atur maintenance:
   - Jadwal Pemeliharaan
   - Notifikasi Status

## Laporan Kehadiran

### Membuat Laporan
1. Buka menu "Laporan"
2. Pilih jenis laporan:
   - Kehadiran Harian
   - Rekap Bulanan
   - Analisis Keterlambatan
   - Status Perangkat
3. Atur filter:
   - Periode
   - Departemen
   - Karyawan
4. Generate laporan

### Export Data
1. Pilih format output:
   - Excel
   - PDF
   - CSV
2. Atur parameter export
3. Klik "Download"

## Pengaturan Sistem

### Konfigurasi Umum
1. Buka "Pengaturan Sistem"
2. Atur parameter:
   - Jam Kerja
   - Toleransi Keterlambatan
   - Format Waktu
   - Zona Waktu
3. Simpan konfigurasi

### Integrasi Sistem
1. Konfigurasi integrasi:
   - HRMS
   - Payroll
   - Sistem Absensi
2. Atur sinkronisasi data
3. Test koneksi

## Tips Tambahan

- Lakukan backup database secara rutin
- Monitor kesehatan perangkat
- Update firmware reader secara berkala
- Dokumentasikan perubahan sistem

## Troubleshooting

### Masalah Umum dan Solusi
1. Kartu Tidak Terbaca
   - Periksa kondisi kartu
   - Test reader dengan kartu lain
   - Cek koneksi reader

2. Error Sinkronisasi
   - Validasi format data
   - Periksa koneksi jaringan
   - Cek log sistem

3. Reader Offline
   - Restart perangkat
   - Periksa koneksi network
   - Update firmware

4. Data Tidak Tercatat
   - Cek status database
   - Verifikasi proses scan
   - Periksa log sistem

Untuk bantuan lebih lanjut, silakan hubungi administrator sistem.