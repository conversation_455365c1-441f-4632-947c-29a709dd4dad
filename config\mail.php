<?php
// Aktifkan pelaporan error untuk debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Tetap matikan display_errors untuk mencegah output HTML error

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;
use <PERSON><PERSON>Mailer\PHPMailer\SMTP;

// Cek apakah autoload tersedia
$autoloadPath = __DIR__ . '/../vendor/autoload.php';
if (!file_exists($autoloadPath)) {
    function send_mail($to, $subject, $body, $settings) {
        return [
            'success' => false,
            'message' => 'PHPMailer tidak ditemukan. Silakan install dengan composer: composer require phpmailer/phpmailer'
        ];
    }
    return; // Keluar dari file jika autoload tidak ditemukan
}

require_once $autoloadPath;

function send_mail($to, $subject, $body, $settings) {
    try {
        // Log informasi email yang akan dikirim
        error_log("Attempting to send email to: $to");
        error_log("Subject: $subject");
        error_log("SMTP Settings: {$settings['smtp_server']}:{$settings['smtp_port']} (Encryption: {$settings['smtp_encryption']})");

        $mail = new PHPMailer(true);

        // Set debug level
        $mail->SMTPDebug = SMTP::DEBUG_SERVER; // Enable verbose debug output

        // Capture SMTP debug output
        $debugOutput = '';
        $mail->Debugoutput = function($str, $level) use (&$debugOutput) {
            $debugOutput .= "$level: $str\n";
            error_log("PHPMailer Debug: $str");
        };

        // Server settings
        $mail->isSMTP();
        $mail->Host = $settings['smtp_server'];
        $mail->SMTPAuth = true;
        $mail->Username = $settings['sender_email'];
        $mail->Password = $settings['smtp_password'];

        // Set encryption berdasarkan input
        if ($settings['smtp_encryption'] === 'tls') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        } elseif ($settings['smtp_encryption'] === 'ssl') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        } else {
            $mail->SMTPSecure = '';
            $mail->SMTPAutoTLS = false;
        }

        // Tambahkan opsi untuk mengatasi masalah "data not accepted"
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            ),
            'tls' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // Tambahkan debug info
        error_log("SMTP Connection: {$settings['smtp_server']}:{$settings['smtp_port']} (Encryption: {$settings['smtp_encryption']})");

        $mail->Port = $settings['smtp_port'];

        // Set timeout lebih lama untuk menghindari timeout pada koneksi lambat
        $mail->Timeout = 60; // 60 detik

        // Tambahkan opsi untuk mengatasi masalah "data not accepted"
        $mail->SMTPKeepAlive = true; // Pertahankan koneksi SMTP
        $mail->Encoding = 'base64'; // Gunakan encoding base64 untuk konten email

        // Tambahkan opsi untuk mengatasi masalah "data not accepted"
        $mail->AllowEmpty = true; // Izinkan email kosong
        $mail->SMTPDebug = 2; // Aktifkan debug level 2 untuk melihat proses SMTP
        $mail->Debugoutput = function($str, $level) use (&$debugOutput) {
            $debugOutput .= "$level: $str\n";
        };

        // Tambahkan header tambahan untuk mengatasi masalah "data not accepted"
        $mail->XMailer = 'Training System Mailer'; // Set X-Mailer header
        $mail->addCustomHeader('X-Application', 'Training System'); // Tambahkan custom header

        // Recipients
        $mail->setFrom($settings['sender_email'], $settings['sender_name']);
        $mail->addAddress($to);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;
        $mail->CharSet = 'UTF-8';

        // Tambahkan AltBody untuk email client yang tidak mendukung HTML
        $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $body));

        // Coba kirim email
        $result = $mail->send();

        // Log hasil pengiriman
        error_log("Email sent successfully to: $to");

        return [
            'success' => true,
            'message' => 'Email berhasil dikirim',
            'debug_output' => $debugOutput
        ];
    } catch (Exception $e) {
        $errorMessage = $e->getMessage();
        $errorInfo = '';

        // Tambahkan informasi error yang lebih spesifik
        if (strpos($errorMessage, 'authenticate') !== false) {
            $errorInfo = "\n\nKemungkinan penyebab:\n" .
                          "1. Password yang Anda masukkan salah\n" .
                          "2. Jika menggunakan Gmail, pastikan Anda menggunakan App Password, bukan password akun biasa\n" .
                          "3. Pastikan 'Less secure app access' diaktifkan di akun email Anda (untuk beberapa provider email)\n" .
                          "4. Cek apakah ada captcha yang perlu diselesaikan: https://accounts.google.com/DisplayUnlockCaptcha";
        } elseif (strpos($errorMessage, 'connect') !== false) {
            $errorInfo = "\n\nKemungkinan penyebab:\n" .
                          "1. Server SMTP yang Anda masukkan salah\n" .
                          "2. Port yang Anda gunakan diblokir oleh firewall\n" .
                          "3. Periksa koneksi internet Anda";
        } elseif (strpos($errorMessage, 'timed out') !== false) {
            $errorInfo = "\n\nKemungkinan penyebab:\n" .
                          "1. Koneksi internet Anda lambat\n" .
                          "2. Server SMTP tidak merespons\n" .
                          "3. Port yang Anda gunakan diblokir oleh firewall";
        } elseif (strpos($errorMessage, 'data not accepted') !== false) {
            $errorInfo = "\n\nKemungkinan penyebab:\n" .
                          "1. Format email tidak valid\n" .
                          "2. Server SMTP menolak konten email\n" .
                          "3. Masalah dengan sertifikat SSL/TLS\n" .
                          "4. Email pengirim dan penerima sama (beberapa server menolak ini)\n" .
                          "5. Konten email terlalu besar atau mengandung karakter yang tidak didukung\n" .
                          "6. Jika menggunakan Gmail, pastikan Anda menggunakan App Password\n" .
                          "7. Coba gunakan port 465 dengan SSL atau port 587 dengan TLS";

            // Tambahkan log khusus untuk error data not accepted
            error_log("SMTP Error 'data not accepted' details: Server: {$settings['smtp_server']}, Port: {$settings['smtp_port']}, Encryption: {$settings['smtp_encryption']}");
            error_log("SMTP Debug Output: " . $debugOutput);
        }

        // Log error
        error_log("Failed to send email to: $to");
        error_log("Error message: $errorMessage");
        if (!empty($errorInfo)) {
            error_log("Error details: $errorInfo");
        }
        error_log("Debug output: $debugOutput");

        return [
            'success' => false,
            'message' => "Gagal mengirim email: {$errorMessage}",
            'error_details' => $errorInfo,
            'debug_output' => $debugOutput,
            'smtp_settings' => [
                'server' => $settings['smtp_server'],
                'port' => $settings['smtp_port'],
                'encryption' => $settings['smtp_encryption'],
                'email' => $settings['sender_email']
            ]
        ];
    }
}



