<?php
/**
 * Test script untuk memastikan API kalender berfungsi dengan baik
 */

require_once '../config/config.php';
require_once 'security.php';

echo "<h2>Testing Calendar API</h2>";

// Test 1: Check required tables
echo "<h3>1. Checking Database Tables</h3>";
$required_tables = [
    'offline_training',
    'training_classes', 
    'training_participants',
    'training_submissions',
    'training_attendance',
    'users'
];

foreach ($required_tables as $table) {
    $query = "SHOW TABLES LIKE '$table'";
    $result = $conn->query($query);
    if ($result && $result->num_rows > 0) {
        echo "✅ Table '$table' exists<br>";
    } else {
        echo "❌ Table '$table' missing<br>";
    }
}

// Test 2: Check sample data
echo "<h3>2. Checking Sample Data</h3>";

// Check offline training
$query = "SELECT COUNT(*) as count FROM offline_training";
$result = $conn->query($query);
$count = $result->fetch_assoc()['count'];
echo "📊 Offline training records: $count<br>";

// Check online training
$query = "SELECT COUNT(*) as count FROM training_classes";
$result = $conn->query($query);
$count = $result->fetch_assoc()['count'];
echo "📊 Online training records: $count<br>";

// Check training submissions
$query = "SELECT COUNT(*) as count FROM training_submissions WHERE status = 'Approved'";
$result = $conn->query($query);
$count = $result->fetch_assoc()['count'];
echo "📊 Approved training submissions: $count<br>";

// Test 3: Test API endpoints
echo "<h3>3. Testing API Endpoints</h3>";

// Test get_training_events.php
$start_date = date('Y-m-01');
$end_date = date('Y-m-t');

echo "<h4>Testing get_training_events.php</h4>";
$api_url = "get_training_events.php?start=$start_date&end=$end_date";

// Simulate API call
$_GET['start'] = $start_date;
$_GET['end'] = $end_date;

ob_start();
include 'get_training_events.php';
$api_response = ob_get_clean();

$response_data = json_decode($api_response, true);
if ($response_data && $response_data['success']) {
    echo "✅ API get_training_events.php working<br>";
    echo "📊 Events found: " . count($response_data['events']) . "<br>";
    
    // Show sample events
    if (!empty($response_data['events'])) {
        echo "<strong>Sample events:</strong><br>";
        foreach (array_slice($response_data['events'], 0, 3) as $event) {
            echo "- {$event['title']} ({$event['type']}) on {$event['date']}<br>";
        }
    }
} else {
    echo "❌ API get_training_events.php failed<br>";
    echo "Response: " . htmlspecialchars($api_response) . "<br>";
}

// Test 4: Check file permissions
echo "<h3>4. Checking File Permissions</h3>";
$files_to_check = [
    'get_training_events.php',
    'get_training_detail.php',
    'index.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "✅ $file is readable<br>";
        } else {
            echo "❌ $file is not readable<br>";
        }
    } else {
        echo "❌ $file does not exist<br>";
    }
}

// Test 5: JavaScript and CSS check
echo "<h3>5. Frontend Components</h3>";
$index_content = file_get_contents('index.php');

if (strpos($index_content, 'calendar-container') !== false) {
    echo "✅ Calendar CSS classes found<br>";
} else {
    echo "❌ Calendar CSS classes missing<br>";
}

if (strpos($index_content, 'renderCalendar') !== false) {
    echo "✅ Calendar JavaScript functions found<br>";
} else {
    echo "❌ Calendar JavaScript functions missing<br>";
}

if (strpos($index_content, 'trainingModal') !== false) {
    echo "✅ Modal HTML found<br>";
} else {
    echo "❌ Modal HTML missing<br>";
}

// Test 6: Sample data for testing
echo "<h3>6. Quick Data Summary</h3>";

// Get upcoming training events
$query = "SELECT 
            'offline' as type,
            training_topic as title,
            training_date as date,
            location
          FROM offline_training 
          WHERE training_date >= CURDATE()
          UNION ALL
          SELECT 
            'online' as type,
            title,
            start_date as date,
            'Online' as location
          FROM training_classes 
          WHERE start_date >= CURDATE()
          ORDER BY date LIMIT 5";

$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    echo "<strong>Upcoming Training Events:</strong><br>";
    while ($row = $result->fetch_assoc()) {
        echo "📅 {$row['title']} ({$row['type']}) - {$row['date']} at {$row['location']}<br>";
    }
} else {
    echo "ℹ️ No upcoming training events found<br>";
    echo "<p><a href='add_sample_training_data.php'>Add Sample Data</a></p>";
}

echo "<h3>✅ Testing Complete!</h3>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li><a href='index.php'>View Calendar</a></li>";
echo "<li><a href='add_sample_training_data.php'>Add Sample Data (if needed)</a></li>";
echo "<li><a href='CALENDAR_README.md'>Read Documentation</a></li>";
echo "</ul>";

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
