<?php
/**
 * Create Quiz Template Excel File
 * This script generates a user-friendly Excel template for importing quiz questions
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Check if PhpSpreadsheet is installed
if (!file_exists('../vendor/autoload.php')) {
    die("PhpSpreadsheet tidak ditemukan. Silakan jalankan 'composer require phpoffice/phpspreadsheet' di direktori root.");
}

require '../vendor/autoload.php';

// Import classes
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// Create a new spreadsheet
$spreadsheet = new Spreadsheet();

// Remove default sheet to avoid confusion
$spreadsheet->removeSheetByIndex(0);

// Create Questions Sheet first (will be the first sheet)
$questionsSheet = $spreadsheet->createSheet();
$questionsSheet->setTitle('Pertanyaan');

// Set column widths for Questions sheet
$questionsSheet->getColumnDimension('A')->setWidth(40); // Pertanyaan
$questionsSheet->getColumnDimension('B')->setWidth(15); // Tipe Pertanyaan
$questionsSheet->getColumnDimension('C')->setWidth(10); // Poin
$questionsSheet->getColumnDimension('D')->setWidth(25); // Opsi A
$questionsSheet->getColumnDimension('E')->setWidth(25); // Opsi B
$questionsSheet->getColumnDimension('F')->setWidth(25); // Opsi C
$questionsSheet->getColumnDimension('G')->setWidth(25); // Opsi D
$questionsSheet->getColumnDimension('H')->setWidth(25); // Opsi E
$questionsSheet->getColumnDimension('I')->setWidth(15); // Jawaban Benar

// Set header row for Questions sheet
$questionsSheet->setCellValue('A1', 'Pertanyaan');
$questionsSheet->setCellValue('B1', 'Tipe Pertanyaan');
$questionsSheet->setCellValue('C1', 'Poin');
$questionsSheet->setCellValue('D1', 'Opsi A');
$questionsSheet->setCellValue('E1', 'Opsi B');
$questionsSheet->setCellValue('F1', 'Opsi C');
$questionsSheet->setCellValue('G1', 'Opsi D');
$questionsSheet->setCellValue('H1', 'Opsi E');
$questionsSheet->setCellValue('I1', 'Jawaban Benar');

// Style header row for Questions sheet
$headerStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FFFFFF'],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => '4472C4'],
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
        ],
    ],
];

$questionsSheet->getStyle('A1:I1')->applyFromArray($headerStyle);
$questionsSheet->getRowDimension(1)->setRowHeight(30);

// Add example data to Questions sheet
$exampleData = [
    ['Apa ibukota Indonesia?', 'multiple_choice', 10, 'Jakarta', 'Bandung', 'Surabaya', 'Yogyakarta', '', 'A'],
    ['Jakarta adalah ibukota Indonesia.', 'true_false', 5, 'Benar', 'Salah', '', '', '', 'A'],
    ['Siapa presiden pertama Indonesia?', 'multiple_choice', 10, 'Ir. Soekarno', 'Mohammad Hatta', 'Soeharto', 'Joko Widodo', '', 'A'],
];

$row = 2;
foreach ($exampleData as $data) {
    $questionsSheet->setCellValue('A' . $row, $data[0]); // Pertanyaan
    $questionsSheet->setCellValue('B' . $row, $data[1]); // Tipe Pertanyaan
    $questionsSheet->setCellValue('C' . $row, $data[2]); // Poin
    $questionsSheet->setCellValue('D' . $row, $data[3]); // Opsi A
    $questionsSheet->setCellValue('E' . $row, $data[4]); // Opsi B
    $questionsSheet->setCellValue('F' . $row, $data[5]); // Opsi C
    $questionsSheet->setCellValue('G' . $row, $data[6]); // Opsi D
    $questionsSheet->setCellValue('H' . $row, $data[7]); // Opsi E
    $questionsSheet->setCellValue('I' . $row, $data[8]); // Jawaban Benar
    $row++;
}

// Add data validation for question type in Questions sheet
try {
    $validation = $questionsSheet->getCell('B2')->getDataValidation();
    $validation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST);
    $validation->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_INFORMATION);
    $validation->setAllowBlank(false);
    $validation->setShowInputMessage(true);
    $validation->setShowErrorMessage(true);
    $validation->setShowDropDown(true);
    $validation->setFormula1('"multiple_choice,true_false"');

    if (method_exists($validation, 'setPromptTitle')) {
        $validation->setPromptTitle('Tipe Pertanyaan');
        $validation->setPrompt('Pilih tipe pertanyaan: multiple_choice atau true_false');
        $validation->setErrorTitle('Tipe Pertanyaan Tidak Valid');
        $validation->setError('Tipe pertanyaan harus multiple_choice atau true_false');
    }

    for ($i = 3; $i <= 50; $i++) {
        $questionsSheet->getCell('B' . $i)->setDataValidation(clone $validation);
    }

    // Add data validation for correct answer
    $answerValidation = $questionsSheet->getCell('I2')->getDataValidation();
    $answerValidation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST);
    $answerValidation->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_INFORMATION);
    $answerValidation->setAllowBlank(false);
    $answerValidation->setShowInputMessage(true);
    $answerValidation->setShowErrorMessage(true);
    $answerValidation->setShowDropDown(true);
    $answerValidation->setFormula1('"A,B,C,D,E"');

    if (method_exists($answerValidation, 'setPromptTitle')) {
        $answerValidation->setPromptTitle('Jawaban Benar');
        $answerValidation->setPrompt('Pilih jawaban benar: A, B, C, D, atau E');
        $answerValidation->setErrorTitle('Jawaban Tidak Valid');
        $answerValidation->setError('Jawaban harus A, B, C, D, atau E');
    }

    for ($i = 3; $i <= 50; $i++) {
        $questionsSheet->getCell('I' . $i)->setDataValidation(clone $answerValidation);
    }
} catch (Exception $e) {
    error_log("Warning: Could not set data validation: " . $e->getMessage());
}

// Auto filter for Questions sheet
$questionsSheet->setAutoFilter('A1:I1');

// Create Instruction Sheet (will be the second sheet)
$instructionSheet = $spreadsheet->createSheet();
$instructionSheet->setTitle('Panduan');

// Set column widths for Instruction sheet
$instructionSheet->getColumnDimension('A')->setWidth(60);
$instructionSheet->getColumnDimension('B')->setWidth(20);

// Add instructions header
$instructionSheet->setCellValue('A1', 'PANDUAN PENGISIAN TEMPLATE PERTANYAAN KUIS');
$instructionSheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
$instructionSheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
$instructionSheet->mergeCells('A1:B1');

// Add instructions content
$instructions = [
    ['A3', 'Cara Mengisi Template:', true],
    ['A4', '1. Buka sheet "Pertanyaan" untuk mengisi pertanyaan kuis', false],
    ['A5', '2. Isi kolom-kolom sesuai petunjuk di bawah ini', false],
    ['A6', '3. Simpan file Excel setelah selesai mengisi', false],
    ['A7', '4. Upload file Excel di halaman import pertanyaan kuis', false],
    ['A9', 'Penjelasan Kolom:', true],
    ['A10', 'Kolom A: Pertanyaan - Isi dengan teks pertanyaan', false],
    ['A11', 'Kolom B: Tipe Pertanyaan - Pilih "multiple_choice" atau "true_false"', false],
    ['A12', 'Kolom C: Poin - Nilai poin untuk pertanyaan ini (angka)', false],
    ['A13', 'Kolom D: Opsi A - Isi dengan teks opsi jawaban pertama', false],
    ['A14', 'Kolom E: Opsi B - Isi dengan teks opsi jawaban kedua', false],
    ['A15', 'Kolom F: Opsi C - Isi dengan teks opsi jawaban ketiga (opsional)', false],
    ['A16', 'Kolom G: Opsi D - Isi dengan teks opsi jawaban keempat (opsional)', false],
    ['A17', 'Kolom H: Opsi E - Isi dengan teks opsi jawaban kelima (opsional)', false],
    ['A18', 'Kolom I: Jawaban Benar - Isi dengan huruf opsi yang benar (A, B, C, D, atau E)', false],
    ['A20', 'Aturan Pengisian:', true],
    ['A21', '1. Untuk pertanyaan pilihan ganda (multiple_choice):', false],
    ['A22', '   - Isi minimal 2 opsi jawaban (Opsi A dan B)', false],
    ['A23', '   - Opsi C, D, dan E boleh dikosongkan jika tidak diperlukan', false],
    ['A24', '   - Pilih jawaban benar dengan menuliskan huruf opsi (A, B, C, D, atau E)', false],
    ['A26', '2. Untuk pertanyaan benar/salah (true_false):', false],
    ['A27', '   - Isi Opsi A dengan "Benar" dan Opsi B dengan "Salah"', false],
    ['A28', '   - Kosongkan Opsi C, D, dan E', false],
    ['A29', '   - Pilih jawaban benar dengan menuliskan A (untuk Benar) atau B (untuk Salah)', false],
    ['A31', 'Contoh:', true],
    ['A32', '1. Pertanyaan pilihan ganda: "Apa ibukota Indonesia?"', false],
    ['A33', '   - Tipe Pertanyaan: multiple_choice', false],
    ['A34', '   - Poin: 10', false],
    ['A35', '   - Opsi A: Jakarta', false],
    ['A36', '   - Opsi B: Bandung', false],
    ['A37', '   - Opsi C: Surabaya', false],
    ['A38', '   - Jawaban Benar: A', false],
    ['A40', '2. Pertanyaan benar/salah: "Jakarta adalah ibukota Indonesia."', false],
    ['A41', '   - Tipe Pertanyaan: true_false', false],
    ['A42', '   - Poin: 5', false],
    ['A43', '   - Opsi A: Benar', false],
    ['A44', '   - Opsi B: Salah', false],
    ['A45', '   - Jawaban Benar: A', false],
];

foreach ($instructions as $instruction) {
    $cell = $instruction[0];
    $text = $instruction[1];
    $isBold = $instruction[2];
    
    $instructionSheet->setCellValue($cell, $text);
    if ($isBold) {
        $instructionSheet->getStyle($cell)->getFont()->setBold(true);
    }
}

// Add background to Instruction sheet header
$instructionSheet->getStyle('A1:B1')->getFill()->setFillType(Fill::FILL_SOLID)->setStartColor(new \PhpOffice\PhpSpreadsheet\Style\Color('4472C4'));
$instructionSheet->getStyle('A1:B1')->getFont()->setColor(new \PhpOffice\PhpSpreadsheet\Style\Color('FFFFFF'));

// Set the active sheet to Questions when opening
$spreadsheet->setActiveSheetIndex(0);

// Create writer and output file
$writer = new Xlsx($spreadsheet);

// Set headers for download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="Template_Pertanyaan_Kuis.xlsx"');
header('Cache-Control: max-age=0');

// Save to output
$writer->save('php://output');
exit;
?>