<?php
/**
 * Edit Question Page for Admin/Trainer
 * This page allows admins/trainers to edit questions and add options
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role or is instructor/assistant for this class
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];
$is_admin = $_SESSION['role_id'] == 99;

// Check if question ID is provided
$question_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($question_id <= 0) {
    header('Location: manage_classes.php');
    exit();
}

// Get question information
$question_query = "SELECT q.*, qz.id as quiz_id, qz.title as quiz_title, c.id as class_id, c.title as class_title
                  FROM training_questions q
                  JOIN training_quizzes qz ON q.quiz_id = qz.id
                  JOIN training_classes c ON qz.class_id = c.id
                  WHERE q.id = ?";
$stmt = $conn->prepare($question_query);
$stmt->bind_param("i", $question_id);
$stmt->execute();
$result = $stmt->get_result();
$question = $result->fetch_assoc();
$stmt->close();

if (!$question) {
    header('Location: manage_classes.php');
    exit();
}

// If not admin, check if user is instructor or assistant for this class
if (!$is_admin) {
    $access_check = "SELECT role FROM training_participants
                    WHERE class_id = ? AND user_id = ? AND role IN ('instructor', 'assistant') AND status = 'active'";
    $stmt = $conn->prepare($access_check);
    $stmt->bind_param("ii", $question['class_id'], $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $access_row = $result->fetch_assoc();
    $stmt->close();

    if (!$access_row) {
        header('Location: ../view/login.php');
        exit();
    }
}

// Handle question update
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_question'])) {
    // Get form data
    $question_text = trim($_POST['question_text']);
    $question_type = $_POST['question_type'];
    $points = intval($_POST['points']);

    // Validate input
    if (empty($question_text)) {
        $error_message = "Teks pertanyaan harus diisi.";
    } else {
        // Update question
        $update_query = "UPDATE training_questions SET
                        question_text = ?, question_type = ?, points = ?
                        WHERE id = ?";

        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("ssii", $question_text, $question_type, $points, $question_id);

        if ($stmt->execute()) {
            $success_message = "Pertanyaan berhasil diperbarui.";

            // Refresh question data
            $stmt->close();
            $stmt = $conn->prepare($question_query);
            $stmt->bind_param("i", $question_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $question = $result->fetch_assoc();
        } else {
            $error_message = "Gagal memperbarui pertanyaan: " . $conn->error;
        }
        $stmt->close();
    }
}

// Handle add option
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_option'])) {
    $option_text = trim($_POST['option_text']);
    $is_correct = isset($_POST['is_correct']) ? 1 : 0;

    // Validate input
    if (empty($option_text)) {
        $error_message = "Teks opsi harus diisi.";
    } else {
        // Get the highest order number
        $order_query = "SELECT MAX(order_number) as max_order FROM training_question_options WHERE question_id = ?";
        $stmt = $conn->prepare($order_query);
        $stmt->bind_param("i", $question_id);
        $stmt->execute();
        $order_result = $stmt->get_result();
        $order_row = $order_result->fetch_assoc();
        $order_number = ($order_row['max_order'] !== null) ? $order_row['max_order'] + 1 : 0;
        $stmt->close();

        // If this is a true/false question, handle differently
        if ($question['question_type'] == 'true_false') {
            // First, delete any existing options
            $delete_query = "DELETE FROM training_question_options WHERE question_id = ?";
            $stmt = $conn->prepare($delete_query);
            $stmt->bind_param("i", $question_id);
            $stmt->execute();
            $stmt->close();

            // Add "True" option
            $true_is_correct = ($option_text == 'true') ? 1 : 0;
            $insert_query = "INSERT INTO training_question_options (
                            question_id, option_text, is_correct, order_number
                        ) VALUES (?, 'True', ?, 0)";
            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("ii", $question_id, $true_is_correct);
            $stmt->execute();
            $stmt->close();

            // Add "False" option
            $false_is_correct = ($option_text == 'false') ? 1 : 0;
            $insert_query = "INSERT INTO training_question_options (
                            question_id, option_text, is_correct, order_number
                        ) VALUES (?, 'False', ?, 1)";
            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("ii", $question_id, $false_is_correct);
            $stmt->execute();
            $stmt->close();

            $success_message = "Opsi benar/salah berhasil diatur.";
        } else {
            // For multiple choice, if this is marked as correct, unmark all others
            if ($is_correct) {
                $update_query = "UPDATE training_question_options SET is_correct = 0 WHERE question_id = ?";
                $stmt = $conn->prepare($update_query);
                $stmt->bind_param("i", $question_id);
                $stmt->execute();
                $stmt->close();
            }

            // Insert option
            $insert_query = "INSERT INTO training_question_options (
                            question_id, option_text, is_correct, order_number
                        ) VALUES (?, ?, ?, ?)";

            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("isii", $question_id, $option_text, $is_correct, $order_number);

            if ($stmt->execute()) {
                $success_message = "Opsi berhasil ditambahkan.";
            } else {
                $error_message = "Gagal menambahkan opsi: " . $conn->error;
            }
            $stmt->close();
        }
    }
}

// Handle delete option
if (isset($_GET['delete_option']) && is_numeric($_GET['delete_option'])) {
    $option_id = intval($_GET['delete_option']);

    // Delete option
    $delete_query = "DELETE FROM training_question_options WHERE id = ? AND question_id = ?";
    $stmt = $conn->prepare($delete_query);
    $stmt->bind_param("ii", $option_id, $question_id);

    if ($stmt->execute()) {
        $success_message = "Opsi berhasil dihapus.";
    } else {
        $error_message = "Gagal menghapus opsi: " . $conn->error;
    }
    $stmt->close();
}

// Handle set correct option
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['set_correct_option']) && is_numeric($_POST['set_correct_option'])) {
    $option_id = intval($_POST['set_correct_option']);

    // Start transaction
    $conn->begin_transaction();

    try {
        // First, set all options for this question to not correct
        $update_all_query = "UPDATE training_question_options SET is_correct = 0 WHERE question_id = ?";
        $stmt = $conn->prepare($update_all_query);
        $stmt->bind_param("i", $question_id);
        $stmt->execute();
        $stmt->close();

        // Then, set the selected option as correct
        $update_query = "UPDATE training_question_options SET is_correct = 1 WHERE id = ? AND question_id = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("ii", $option_id, $question_id);

        if ($stmt->execute()) {
            $success_message = "Jawaban benar berhasil diperbarui.";
            $conn->commit();
        } else {
            throw new Exception("Gagal memperbarui jawaban benar: " . $conn->error);
        }
        $stmt->close();
    } catch (Exception $e) {
        $conn->rollback();
        $error_message = $e->getMessage();
    }
}

// Handle update option
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_option']) && isset($_POST['edit_option_id']) && is_numeric($_POST['edit_option_id'])) {
    $option_id = intval($_POST['edit_option_id']);
    $option_text = trim($_POST['edit_option_text']);

    // Validate input
    if (empty($option_text)) {
        $error_message = "Teks opsi harus diisi.";
    } else {
        // Update option
        $update_query = "UPDATE training_question_options SET option_text = ? WHERE id = ? AND question_id = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("sii", $option_text, $option_id, $question_id);

        if ($stmt->execute()) {
            $success_message = "Opsi jawaban berhasil diperbarui.";
        } else {
            $error_message = "Gagal memperbarui opsi jawaban: " . $conn->error;
        }
        $stmt->close();
    }
}

// Get options for this question
$options = [];
if ($question['question_type'] == 'multiple_choice' || $question['question_type'] == 'true_false') {
    $options_query = "SELECT * FROM training_question_options WHERE question_id = ? ORDER BY order_number ASC";
    $stmt = $conn->prepare($options_query);
    $stmt->bind_param("i", $question_id);
    $stmt->execute();
    $options_result = $stmt->get_result();

    while ($row = $options_result->fetch_assoc()) {
        $options[] = $row;
    }
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .form-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .form-section h3 {
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .option-card {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .correct-option-card {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .option-label {
        background-color: #6c757d;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 15px;
    }

    .correct-option-card .option-label {
        background-color: #28a745;
    }

    .option-text {
        flex-grow: 1;
    }

    .option-actions {
        display: flex;
        align-items: center;
        margin-left: 15px;
    }

    .correct-option {
        color: #155724;
        font-weight: 600;
    }

    .add-option-form {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }

    .true-false-form {
        margin-top: 20px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
    }

    .true-false-option {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .true-false-option:hover {
        background-color: #f1f3f5;
    }

    .correct-option-radio {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .true-false-label {
        background-color: #6c757d;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 10px;
    }

    .correct-option-radio .true-false-label {
        background-color: #28a745;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1 class="welcome-section"><i class="fas fa-edit"></i> Edit Pertanyaan
            <p class="text-white">Edit pertanyaan dan tambahkan opsi jawaban</p>
            </h1>
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="form-section">
                <h3>Informasi Pertanyaan</h3>

                <div class="mb-3">
                    <label class="form-label">Kelas</label>
                    <div class="form-control bg-light"><?= htmlspecialchars($question['class_title']) ?></div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Kuis</label>
                    <div class="form-control bg-light"><?= htmlspecialchars($question['quiz_title']) ?></div>
                </div>

                <form method="post" action="">
                    <div class="mb-3">
                        <label for="question_text" class="form-label">Teks Pertanyaan</label>
                        <textarea class="form-control" id="question_text" name="question_text" rows="3" required><?= htmlspecialchars($question['question_text']) ?></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="question_type" class="form-label">Jenis Pertanyaan</label>
                                <select class="form-select" id="question_type" name="question_type" required>
                                    <option value="multiple_choice" <?= $question['question_type'] == 'multiple_choice' ? 'selected' : '' ?>>Pilihan Ganda</option>
                                    <option value="true_false" <?= $question['question_type'] == 'true_false' ? 'selected' : '' ?>>Benar/Salah</option>
                                    <option value="short_answer" <?= $question['question_type'] == 'short_answer' ? 'selected' : '' ?>>Jawaban Singkat</option>
                                    <option value="essay" <?= $question['question_type'] == 'essay' ? 'selected' : '' ?>>Esai</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="points" class="form-label">Poin</label>
                                <input type="number" class="form-control" id="points" name="points" min="1" value="<?= $question['points'] ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" name="update_question" class="btn btn-primary">
                            <i class="fas fa-save"></i> Perbarui Pertanyaan
                        </button>

                        <a href="edit_quiz.php?id=<?= $question['quiz_id'] ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali ke Kuis
                        </a>
                    </div>
                </form>
            </div>

            <?php if ($question['question_type'] == 'multiple_choice' || $question['question_type'] == 'true_false'): ?>
                <div class="form-section">
                    <h3>Opsi Jawaban</h3>

                    <?php if ($question['question_type'] == 'true_false'): ?>
                        <div class="true-false-form">
                            <h4>Pilih Jawaban yang Benar</h4>
                            <form method="post" action="">
                                <div class="mb-3">
                                    <div class="form-check true-false-option <?= !empty($options) && $options[0]['is_correct'] ? 'correct-option-radio' : '' ?>">
                                        <input class="form-check-input" type="radio" name="option_text" id="option_true" value="true" <?= !empty($options) && $options[0]['is_correct'] ? 'checked' : '' ?> required>
                                        <label class="form-check-label" for="option_true">
                                            <span class="true-false-label">A</span> Benar (True)
                                        </label>
                                    </div>
                                    <div class="form-check true-false-option <?= !empty($options) && $options[1]['is_correct'] ? 'correct-option-radio' : '' ?>">
                                        <input class="form-check-input" type="radio" name="option_text" id="option_false" value="false" <?= !empty($options) && $options[1]['is_correct'] ? 'checked' : '' ?> required>
                                        <label class="form-check-label" for="option_false">
                                            <span class="true-false-label">B</span> Salah (False)
                                        </label>
                                    </div>
                                </div>

                                <button type="submit" name="add_option" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Simpan Jawaban
                                </button>
                            </form>
                        </div>
                    <?php else: ?>
                        <?php if (empty($options)): ?>
                            <div class="alert alert-info">
                                Belum ada opsi jawaban untuk pertanyaan ini. Tambahkan opsi menggunakan form di bawah.
                            </div>
                        <?php else: ?>
                            <form method="post" action="" id="set-correct-form">
                                <input type="hidden" name="set_correct_option" id="set_correct_option" value="">
                            </form>

                            <?php
                            // Get option labels based on order_number
                            $optionLabels = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'];
                            foreach ($options as $index => $option):
                                $optionLabel = isset($optionLabels[$index]) ? $optionLabels[$index] : ($index + 1);
                            ?>
                                <div class="option-card <?= $option['is_correct'] ? 'correct-option-card' : '' ?>">
                                    <div class="option-label"><?= $optionLabel ?></div>
                                    <div class="option-text">
                                        <?= htmlspecialchars($option['option_text']) ?>
                                    </div>
                                    <div class="option-actions">
                                        <?php if ($option['is_correct']): ?>
                                            <span class="badge bg-success me-2">Jawaban Benar</span>
                                        <?php else: ?>
                                            <button type="button" class="btn btn-sm btn-outline-success me-2 set-correct-btn" data-option-id="<?= $option['id'] ?>">
                                                Jadikan Jawaban Benar
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-sm btn-primary me-2 edit-option-btn"
                                                data-option-id="<?= $option['id'] ?>"
                                                data-option-text="<?= htmlspecialchars($option['option_text']) ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="edit_question.php?id=<?= $question_id ?>&delete_option=<?= $option['id'] ?>" class="btn btn-sm btn-danger" onclick="return confirmAction('Apakah Anda yakin ingin menghapus opsi ini?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>

                        <div class="add-option-form">
                            <h4>Tambah Opsi Jawaban</h4>

                            <form method="post" action="">
                                <div class="mb-3">
                                    <label for="option_text" class="form-label">Teks Opsi</label>
                                    <input type="text" class="form-control" id="option_text" name="option_text" required>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="is_correct" name="is_correct">
                                    <label class="form-check-label" for="is_correct">Jawaban Benar</label>
                                    <div class="form-text">Centang jika opsi ini adalah jawaban yang benar</div>
                                </div>

                                <button type="submit" name="add_option" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Tambah Opsi
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>
            <?php elseif ($question['question_type'] == 'short_answer' || $question['question_type'] == 'essay'): ?>
                <div class="form-section">
                    <h3>Informasi Jawaban</h3>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <?php if ($question['question_type'] == 'short_answer'): ?>
                            Pertanyaan ini memerlukan jawaban singkat dari peserta. Jawaban akan dinilai secara otomatis berdasarkan kecocokan teks.
                        <?php else: ?>
                            Pertanyaan ini memerlukan jawaban esai dari peserta. Jawaban akan dinilai secara manual oleh trainer.
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<!-- Modal for editing option -->
<div class="modal fade" id="editOptionModal" tabindex="-1" aria-labelledby="editOptionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editOptionModalLabel">Edit Opsi Jawaban</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="" id="edit-option-form">
                <div class="modal-body">
                    <input type="hidden" name="edit_option_id" id="edit_option_id" value="">
                    <div class="mb-3">
                        <label for="edit_option_text" class="form-label">Teks Opsi</label>
                        <input type="text" class="form-control" id="edit_option_text" name="edit_option_text" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="update_option" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Add any JavaScript functionality here
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);

        // Show warning when changing question type
        const questionTypeSelect = document.getElementById('question_type');
        const originalType = '<?= $question['question_type'] ?>';

        questionTypeSelect.addEventListener('change', function() {
            if (this.value !== originalType) {
                confirmAction('Mengubah jenis pertanyaan akan menghapus semua opsi jawaban yang sudah ada. Lanjutkan?', function() {
                    // Submit the form to save changes
                    document.getElementById('update_question').click();
                });
            }
        });

        // Handle set correct answer buttons
        const setCorrectButtons = document.querySelectorAll('.set-correct-btn');
        const setCorrectForm = document.getElementById('set-correct-form');
        const setCorrectInput = document.getElementById('set_correct_option');

        setCorrectButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const optionId = this.getAttribute('data-option-id');

                // Show confirmation dialog
                confirmAction('Apakah Anda yakin ingin menjadikan ini sebagai jawaban yang benar?', function() {
                    // Set the option ID and submit the form
                    setCorrectInput.value = optionId;
                    setCorrectForm.submit();
                });
            });
        });

        // Handle edit option buttons
        const editOptionButtons = document.querySelectorAll('.edit-option-btn');
        const editOptionModal = new bootstrap.Modal(document.getElementById('editOptionModal'));
        const editOptionIdInput = document.getElementById('edit_option_id');
        const editOptionTextInput = document.getElementById('edit_option_text');

        editOptionButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const optionId = this.getAttribute('data-option-id');
                const optionText = this.getAttribute('data-option-text');

                // Set the values in the modal form
                editOptionIdInput.value = optionId;
                editOptionTextInput.value = optionText;

                // Show the modal
                editOptionModal.show();
            });
        });
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
