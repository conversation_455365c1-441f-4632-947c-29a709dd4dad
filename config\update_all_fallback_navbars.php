<?php
/**
 * Script untuk mengganti semua fallback navbar dengan yang baru (dengan notifikasi)
 */

echo "🔄 UPDATING ALL FALLBACK NAVBARS\n";
echo "=================================\n\n";

// Daftar file yang memiliki fallback navbar
$files_to_update = [
    'Dir/assistant_dashboard.php',
    'LnD/assistant_dashboard.php', 
    'pemohon/assistant_dashboard.php',
    'Dir/instructor_dashboard.php',
    'LnD/instructor_dashboard.php',
    'dept_head/instructor_dashboard.php',
    'pemohon/instructor_dashboard.php'
];

$updated_count = 0;
$error_count = 0;
$skipped_count = 0;

foreach ($files_to_update as $file) {
    echo "📝 Processing: $file\n";
    
    if (!file_exists($file)) {
        echo "   ⚠️  File not found: $file\n";
        $skipped_count++;
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Pattern untuk mencari fallback navbar yang lama
    $old_navbar_pattern = '/\/\/ Fallback navbar if none of the includes work\s*if \(\!?\$navbar_included\) \{\s*\?>\s*<nav class="navbar.*?<\/nav>\s*<\?php \}/s';
    
    if (preg_match($old_navbar_pattern, $content)) {
        // Ganti dengan fallback navbar yang baru
        $new_navbar_code = "// Fallback navbar if none of the includes work
    if (!\$navbar_included) {
        include '../includes/fallback_navbar.php';
    }";
        
        $new_content = preg_replace($old_navbar_pattern, $new_navbar_code, $content);
        
        if ($new_content !== $content) {
            if (file_put_contents($file, $new_content)) {
                echo "   ✅ Updated successfully\n";
                $updated_count++;
            } else {
                echo "   ❌ Failed to write file\n";
                $error_count++;
            }
        } else {
            echo "   ℹ️  No changes made\n";
            $skipped_count++;
        }
    } else {
        echo "   ℹ️  Fallback navbar pattern not found or already updated\n";
        $skipped_count++;
    }
    
    echo "\n";
}

echo "📊 SUMMARY:\n";
echo "   ✅ Files updated: $updated_count\n";
echo "   ❌ Errors: $error_count\n";
echo "   ⏭️  Skipped: $skipped_count\n";
echo "   📁 Total processed: " . count($files_to_update) . "\n";

if ($updated_count > 0) {
    echo "\n🎉 Fallback navbars have been updated!\n";
    echo "🔔 All fallback navbars now include notification functionality\n";
    echo "📱 Users will see notification bell icon even when main navbar fails to load\n";
} else {
    echo "\n💡 No files needed updating - fallback navbars may already be updated\n";
}

echo "\n📋 Next steps:\n";
echo "1. Test the updated files in browser\n";
echo "2. Check that notification bell appears in fallback navbars\n";
echo "3. Verify notification dropdown works correctly\n";
echo "4. Create demo notifications to test: php config/simple_demo_notifications.php\n";
?>
