# Kalender Training - Fitur Baru

## Deskripsi
Kalender Training adalah fitur baru yang memungkinkan pengguna untuk melihat jadwal training dalam format kalender yang interaktif. Kalender ini menampilkan:

1. **Training Internal** - Training yang dilakukan secara tatap muka
2. **Training Eksternal** - Training yang dilakukan secara virtual/online
3. **Training Submission** - Training yang diajukan dan sudah disetujui

## Fitur Utama

### 1. <PERSON><PERSON><PERSON> Kalender
- Navi<PERSON><PERSON> bulan (Previous/Next)
- Tombol "Hari Ini" untuk kembali ke tanggal saat ini
- Legenda warna untuk membedakan jenis training:
  - 🟢 **Hijau**: Training Internal
  - 🔵 **Biru**: Training Eksternal  
  - 🔴 **Merah**: Training Submission

### 2. Event Training
- Setiap event training ditampilkan sebagai kotak kecil di tanggal yang sesuai
- Hover effect untuk interaksi yang lebih baik
- Click event untuk melihat detail

### 3. Modal Detail Training
Ketika event training diklik, akan muncul modal popup yang menampilkan:
- **Informasi Training**:
  - Judul training
  - Tanggal dan waktu
  - Lokasi
  - Trainer/Instruktur
  - Maksimal peserta
  - Status training
  - Deskripsi (jika ada)

- **Daftar Peserta**:
  - Nama peserta
  - NIK
  - Departemen
  - Jabatan
  - Avatar dengan inisial nama
  - Status kehadiran (untuk Training Internal)

## Cara Menggunakan

### 1. Setup Data Sample (Opsional)
Jika ingin menguji dengan data sample:
```
http://localhost/Training/pemohon/add_sample_training_data.php
```

### 2. Akses Kalender
Buka halaman utama pemohon:
```
http://localhost/Training/pemohon/index.php
```

### 3. Navigasi Kalender
- Gunakan tombol ◀ dan ▶ untuk navigasi bulan
- Klik "Hari Ini" untuk kembali ke bulan saat ini
- Klik pada event training untuk melihat detail

## File yang Terlibat

### 1. Frontend
- `pemohon/index.php` - Halaman utama dengan kalender
- CSS styling untuk kalender dan modal

### 2. Backend API
- `pemohon/get_training_events.php` - API untuk mengambil data training events
- `pemohon/get_training_detail.php` - API untuk mengambil detail training dan peserta

### 3. Database Tables
- `offline_training` - Data Training Internal
- `training_classes` - Data Training Eksternal/classroom
- `training_submissions` - Data pengajuan training
- `training_participants` - Data peserta Training Eksternal
- `training_attendance` - Data peserta Training Internal
- `users` - Data pengguna

## Struktur Database

### offline_training
```sql
- id (Primary Key)
- training_topic (Judul training)
- training_description (Deskripsi)
- training_date (Tanggal)
- training_time_start (Waktu mulai)
- training_time_end (Waktu selesai)
- location (Lokasi)
- trainer_name (Nama trainer)
- max_participants (Maksimal peserta)
- status (Status: Active/Completed/Cancelled)
```

### training_classes
```sql
- id (Primary Key)
- title (Judul training)
- description (Deskripsi)
- start_date (Tanggal mulai)
- end_date (Tanggal selesai)
- status (Status: active/inactive/completed)
- max_participants (Maksimal peserta)
- created_by (User ID pembuat)
```

### training_participants
```sql
- id (Primary Key)
- class_id (Foreign Key ke training_classes)
- user_id (Foreign Key ke users)
- role (student/instructor/assistant)
- status (active/inactive/completed)
```

## Customization

### 1. Warna Event
Edit CSS di `pemohon/index.php`:
```css
.calendar-event.online {
    background-color: #2196F3; /* Biru untuk online */
}

.calendar-event.offline {
    background-color: #4CAF50; /* Hijau untuk offline */
}

.calendar-event.submission {
    background-color: rgb(157, 0, 0); /* Merah untuk submission */
}
```

### 2. Format Tanggal
Edit JavaScript di `pemohon/index.php`:
```javascript
const monthNames = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
];
```

### 3. Tambah Jenis Training Baru
1. Tambah query di `get_training_events.php`
2. Tambah case di `get_training_detail.php`
3. Tambah CSS class untuk warna baru
4. Update legenda di HTML

## Troubleshooting

### 1. Kalender Tidak Muncul
- Pastikan JavaScript tidak ada error (check browser console)
- Pastikan file API dapat diakses
- Check database connection

### 2. Event Tidak Muncul
- Check data di database
- Pastikan tanggal dalam format yang benar (YYYY-MM-DD)
- Check API response di Network tab browser

### 3. Modal Tidak Muncul
- Check JavaScript console untuk error
- Pastikan API `get_training_detail.php` berfungsi
- Check CSS modal styling

## Security

- Semua API dilindungi dengan session check
- Input validation untuk parameter tanggal
- Prepared statements untuk mencegah SQL injection
- XSS protection dengan htmlspecialchars

## Performance

- Data dimuat per bulan untuk mengurangi beban
- Lazy loading untuk detail training
- Efficient database queries dengan indexing
- Minimal DOM manipulation

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Future Enhancements

1. Filter berdasarkan jenis training
2. Export kalender ke PDF/Excel
3. Notifikasi reminder training
4. Integration dengan calendar apps (Google Calendar, Outlook)
5. Drag & drop untuk reschedule training
6. Bulk operations untuk training management
