-- Add poster field to offline_training table
ALTER TABLE `offline_training` 
ADD COLUMN `poster_image` VARCHAR(255) DEFAULT NULL COMMENT 'Path to training poster image' AFTER `training_description`;

-- Add poster field to training_submissions table (if not exists)
-- Note: training_submissions already has internal_memo_image, we can use it as poster
-- But let's add a dedicated poster field for clarity
ALTER TABLE `training_submissions` 
ADD COLUMN `poster_image` VARCHAR(255) DEFAULT NULL COMMENT 'Path to training poster image' AFTER `internal_memo_image`;

-- <PERSON>reate uploads directory structure (to be created manually)
-- uploads/training_posters/offline/
-- uploads/training_posters/online/
