# 🔧 Fix Validasi Tanggal untuk Revisi Training

## ❌ Masalah yang Terjadi

Ketika user melakukan **revisi training**, validasi tanggal masih menggunakan **"hari ini"** sebagai referensi, padahal seharusnya menggunakan **"tanggal pengajuan awal"** sebagai referensi.

### **Skenario Masalah:**
1. User mengajukan training pada **1 Januari 2024** dengan tanggal training **15 Februari 2024** ✅
2. Training di-reject dan perlu revisi pada **15 Februari 2024** 
3. User ingin revisi tanggal training menjadi **20 Februari 2024**
4. **ERROR**: "Tanggal training minimal harus 1 bulan ke depan dari hari ini" ❌
5. **Seharusnya**: Tanggal minimal berdasarkan tanggal pengajuan awal (1 Januari + 1 bulan = 1 Februari) ✅

## ✅ Solusi yang Diimplementasi

### **Logika Baru:**
- **Untuk revisi**: Tanggal minimal = Tanggal pengajuan awal + 1 bulan
- **Bukan**: Tanggal minimal = Hari ini + 1 bulan

### **Referensi Tanggal:**
- Menggunakan field `submission_date` atau `created_at` dari table `training_submissions`
- Fallback ke `now` jika kedua field tidak tersedia

## 📋 Perubahan yang Dilakukan

### **1. Server-side Validation (PHP)**

**File**: `pemohon/edit_training.php` - Lines 78-92

```php
// ❌ Sebelum
$min_date = new DateTime();
$min_date->add(new DateInterval('P1M'));

// ✅ Sesudah  
$reference_date = new DateTime($training['submission_date'] ?? $training['created_at'] ?? 'now');
$min_date = clone $reference_date;
$min_date->add(new DateInterval('P1M'));

if ($selected_date < $min_date) {
    $reference_date_formatted = $reference_date->format('d/m/Y');
    $min_date_formatted = $min_date->format('d/m/Y');
    $errors[] = "Tanggal training harus minimal 1 bulan dari tanggal pengajuan awal ({$reference_date_formatted}). Tanggal minimal: {$min_date_formatted}";
}
```

### **2. HTML Input Min Attribute**

**File**: `pemohon/edit_training.php` - Lines 821-830

```php
// ❌ Sebelum
min="<?= date('Y-m-d', strtotime('+1 month')); ?>"

// ✅ Sesudah
<?php
$reference_date = new DateTime($training['submission_date'] ?? $training['created_at'] ?? 'now');
$min_date = clone $reference_date;
$min_date->add(new DateInterval('P1M'));
$min_date_str = $min_date->format('Y-m-d');
?>
min="<?= $min_date_str ?>"
```

### **3. User Interface Text**

**File**: `pemohon/edit_training.php` - Lines 831-834

```html
<!-- ❌ Sebelum -->
<small>Tanggal training minimal harus 1 bulan ke depan dari hari ini</small>

<!-- ✅ Sesudah -->
<small>
    Tanggal training minimal harus 1 bulan dari tanggal pengajuan awal 
    (<?= $reference_date->format('d/m/Y') ?>) - Minimal: <?= $min_date->format('d/m/Y') ?>
</small>
```

### **4. JavaScript Client-side Validation**

**File**: `pemohon/edit_training.php` - Lines 1345-1388

```javascript
// ❌ Sebelum
const today = new Date();
const minDate = new Date(today.setMonth(today.getMonth() + 1));

// ✅ Sesudah
const referenceDate = new Date('<?= $reference_date_js ?>');
const minDate = new Date('<?= $min_date_js ?>');

// Error message juga diperbaiki
alert(`Tanggal training harus minimal 1 bulan dari tanggal pengajuan awal (${referenceDateStr}). Tanggal minimal: ${minDateStr}`);
```

### **5. Info Note Update**

**File**: `pemohon/edit_training.php` - Line 730

```html
<!-- ❌ Sebelum -->
<li>Estimasi Tanggal Training - Tanggal yang diinginkan (minimal 1 bulan ke depan)</li>

<!-- ✅ Sesudah -->
<li>Estimasi Tanggal Training - Tanggal yang diinginkan (minimal 1 bulan dari tanggal pengajuan awal)</li>
```

## 🎯 Contoh Implementasi

### **Skenario 1: Training Diajukan 1 Januari 2024**

```php
// Data training
$training['submission_date'] = '2024-01-01 10:00:00';

// Perhitungan
$reference_date = new DateTime('2024-01-01 10:00:00'); // 1 Jan 2024
$min_date = clone $reference_date;
$min_date->add(new DateInterval('P1M')); // 1 Feb 2024

// Hasil
// Tanggal minimal: 1 Februari 2024
// User bisa pilih tanggal >= 1 Februari 2024
```

### **Skenario 2: Revisi pada 15 Februari 2024**

```php
// Meskipun hari ini 15 Feb 2024
// Tanggal minimal tetap berdasarkan pengajuan awal (1 Jan + 1 bulan = 1 Feb)
// User masih bisa pilih 20 Feb 2024 ✅
```

## 🔍 Validasi Multi-Layer

### **1. PHP Server-side**
- Validasi saat form di-submit
- Error message yang informatif
- Menggunakan tanggal pengajuan awal sebagai referensi

### **2. HTML5 Validation**
- `min` attribute pada input date
- Browser akan mencegah pemilihan tanggal sebelum minimum

### **3. JavaScript Client-side**
- Real-time validation saat user mengubah tanggal
- Alert dengan pesan yang jelas
- Konsisten dengan server-side validation

## 📊 Testing Scenarios

### **Test Case 1: Revisi Normal**
- **Pengajuan**: 1 Jan 2024
- **Tanggal Training Awal**: 15 Feb 2024
- **Revisi pada**: 10 Feb 2024
- **Tanggal Baru**: 20 Feb 2024
- **Expected**: ✅ Valid (20 Feb > 1 Feb)

### **Test Case 2: Tanggal Terlalu Awal**
- **Pengajuan**: 1 Jan 2024
- **Revisi pada**: 10 Feb 2024
- **Tanggal Baru**: 15 Jan 2024
- **Expected**: ❌ Error "Tanggal minimal: 1 Feb 2024"

### **Test Case 3: Edge Case**
- **Pengajuan**: 31 Jan 2024
- **Tanggal Minimal**: 29 Feb 2024 (leap year)
- **Expected**: ✅ Handled correctly

## 🛠️ Fallback Handling

### **Field Priority:**
1. `submission_date` (preferred)
2. `created_at` (fallback)
3. `now` (last resort)

```php
$reference_date = new DateTime($training['submission_date'] ?? $training['created_at'] ?? 'now');
```

### **Error Handling:**
- Graceful degradation jika tanggal tidak valid
- Informative error messages
- Consistent behavior across all validation layers

## 📝 User Experience Improvements

### **Before:**
- ❌ Confusing error: "minimal 1 bulan dari hari ini"
- ❌ User tidak tahu kenapa tanggal yang valid di-reject
- ❌ Tidak ada konteks tanggal pengajuan awal

### **After:**
- ✅ Clear error: "minimal 1 bulan dari tanggal pengajuan awal (1/1/2024)"
- ✅ User tahu tanggal minimal yang diperbolehkan
- ✅ Konteks yang jelas tentang referensi tanggal

## 🔧 Maintenance Notes

### **Database Dependencies:**
- Memerlukan field `submission_date` atau `created_at` di table `training_submissions`
- Pastikan field tersebut selalu terisi saat insert training baru

### **Future Enhancements:**
- Bisa ditambahkan konfigurasi untuk mengubah periode minimal (1 bulan)
- Bisa ditambahkan business rules yang berbeda untuk jenis training tertentu

---

**✅ HASIL**: Validasi tanggal untuk revisi training sekarang menggunakan tanggal pengajuan awal sebagai referensi, bukan hari ini. User dapat melakukan revisi tanggal training dengan logika yang benar dan pesan error yang informatif.
