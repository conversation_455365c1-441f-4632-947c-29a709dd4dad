<?php
/**
 * Take Quiz Page for Pemohon (Applicant)
 * This page allows applicants to take quizzes
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';

// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['full_name'] ?? 'Pengguna';

// Check if quiz ID is provided
$quiz_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($quiz_id <= 0) {
    header('Location: classroom.php');
    exit();
}

// Get quiz information
$quiz_query = "SELECT q.*, c.id as class_id, c.title as class_title, t.training_topic
              FROM training_quizzes q
              JOIN training_classes c ON q.class_id = c.id
              JOIN training_submissions t ON c.training_id = t.id
              WHERE q.id = ? AND q.is_published = 1";
$stmt = $conn->prepare($quiz_query);
$stmt->bind_param("i", $quiz_id);
$stmt->execute();
$result = $stmt->get_result();
$quiz = $result->fetch_assoc();
$stmt->close();

if (!$quiz) {
    header('Location: classroom.php');
    exit();
}

// Check if user is a participant in this class
$participant_query = "SELECT * FROM training_participants
                     WHERE class_id = ? AND user_id = ? AND status = 'active'";
$stmt = $conn->prepare($participant_query);
$stmt->bind_param("ii", $quiz['class_id'], $user_id);
$stmt->execute();
$participant_result = $stmt->get_result();
$is_participant = $participant_result->num_rows > 0;
$stmt->close();

if (!$is_participant) {
    header('Location: classroom.php');
    exit();
}

// Check if user has an active attempt
$active_attempt_query = "SELECT * FROM training_quiz_attempts
                        WHERE quiz_id = ? AND user_id = ? AND status = 'in_progress'";
$stmt = $conn->prepare($active_attempt_query);
$stmt->bind_param("ii", $quiz_id, $user_id);
$stmt->execute();
$active_attempt_result = $stmt->get_result();
$active_attempt = $active_attempt_result->fetch_assoc();
$stmt->close();

// Check if user has completed attempts
$completed_attempts_query = "SELECT * FROM training_quiz_attempts
                           WHERE quiz_id = ? AND user_id = ? AND status IN ('completed', 'graded')
                           ORDER BY end_time DESC";
$stmt = $conn->prepare($completed_attempts_query);
$stmt->bind_param("ii", $quiz_id, $user_id);
$stmt->execute();
$completed_attempts_result = $stmt->get_result();
$completed_attempts = [];
while ($row = $completed_attempts_result->fetch_assoc()) {
    $completed_attempts[] = $row;
}
$stmt->close();

// Check if multiple attempts are allowed
$can_start_new_attempt = empty($completed_attempts); // First attempt is always allowed

// If multiple attempts are allowed, check if max attempts limit is reached
if ($quiz['allow_multiple_attempts']) {
    // If max_attempts is set, check if the user has reached the limit
    if ($quiz['max_attempts'] !== null) {
        $can_start_new_attempt = count($completed_attempts) < $quiz['max_attempts'];
    } else {
        // No limit on attempts
        $can_start_new_attempt = true;
    }
}

// Handle start new attempt
if (isset($_POST['start_attempt']) && $can_start_new_attempt && !$active_attempt) {
    $start_time = date('Y-m-d H:i:s');

    $insert_query = "INSERT INTO training_quiz_attempts (quiz_id, user_id, start_time, status)
                    VALUES (?, ?, ?, 'in_progress')";
    $stmt = $conn->prepare($insert_query);
    $stmt->bind_param("iis", $quiz_id, $user_id, $start_time);

    if ($stmt->execute()) {
        $attempt_id = $stmt->insert_id;
        header("Location: take_quiz.php?id=" . $quiz_id);
        exit();
    }
    $stmt->close();
}

// Handle submit attempt
if (isset($_POST['submit_attempt']) && $active_attempt) {
    // Log submission untuk debugging
    error_log("Quiz submission received for user_id: $user_id, quiz_id: $quiz_id, attempt_id: " . $active_attempt['id']);
    $end_time = date('Y-m-d H:i:s');

    // Get all questions for this quiz
    $questions_query = "SELECT * FROM training_questions WHERE quiz_id = ?";
    $stmt = $conn->prepare($questions_query);
    $stmt->bind_param("i", $quiz_id);
    $stmt->execute();
    $questions_result = $stmt->get_result();
    $questions = [];
    while ($row = $questions_result->fetch_assoc()) {
        $questions[$row['id']] = $row;
    }
    $stmt->close();

    // Process answers
    $total_points = 0;
    $earned_points = 0;
    $answered_questions = 0;
    $total_questions = count($questions);

    foreach ($questions as $question) {
        $question_id = $question['id'];
        $total_points += $question['points'];

        // Check if answer was provided
        if (isset($_POST['answer_' . $question_id])) {
            $answer = $_POST['answer_' . $question_id];
            $answered_questions++;

            // Handle different question types
            if ($question['question_type'] == 'multiple_choice' || $question['question_type'] == 'true_false') {
                $selected_option_id = intval($answer);

                // Check if selected option is correct
                $option_query = "SELECT * FROM training_question_options WHERE id = ? AND question_id = ?";
                $stmt = $conn->prepare($option_query);
                $stmt->bind_param("ii", $selected_option_id, $question_id);
                $stmt->execute();
                $option_result = $stmt->get_result();
                $option = $option_result->fetch_assoc();
                $stmt->close();

                $is_correct = ($option && $option['is_correct']) ? 1 : 0;
                $points_earned = $is_correct ? $question['points'] : 0;
                $earned_points += $points_earned;

                // Save answer
                $insert_answer_query = "INSERT INTO training_quiz_answers
                                      (attempt_id, question_id, selected_option_id, is_correct, points_earned)
                                      VALUES (?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($insert_answer_query);
                $stmt->bind_param("iiiis", $active_attempt['id'], $question_id, $selected_option_id, $is_correct, $points_earned);
                $stmt->execute();
                $stmt->close();
            } else if ($question['question_type'] == 'short_answer' || $question['question_type'] == 'essay') {
                $text_answer = trim($answer);
                $answered_questions++;

                // For short answer and essay, we'll save the answer but not grade it yet
                $insert_answer_query = "INSERT INTO training_quiz_answers
                                      (attempt_id, question_id, text_answer)
                                      VALUES (?, ?, ?)";
                $stmt = $conn->prepare($insert_answer_query);
                $stmt->bind_param("iis", $active_attempt['id'], $question_id, $text_answer);
                $stmt->execute();
                $stmt->close();
            }
        } else {
            // Jika pertanyaan tidak dijawab, tetap simpan sebagai jawaban kosong
            $insert_answer_query = "INSERT INTO training_quiz_answers
                                  (attempt_id, question_id, is_correct, points_earned)
                                  VALUES (?, ?, 0, 0)";
            $stmt = $conn->prepare($insert_answer_query);
            $stmt->bind_param("ii", $active_attempt['id'], $question_id);
            $stmt->execute();
            $stmt->close();
        }
    }

    // Calculate score (as percentage)
    $score = ($total_points > 0) ? round(($earned_points / $total_points) * 100) : 0;

    // Update attempt status
    $update_query = "UPDATE training_quiz_attempts
                    SET end_time = ?, score = ?, status = ?
                    WHERE id = ?";

    // For multiple choice and true/false only quizzes, we can mark as graded
    // For quizzes with short answer or essay, mark as completed (needs manual grading)
    $has_manual_grading = false;
    foreach ($questions as $question) {
        if ($question['question_type'] == 'short_answer' || $question['question_type'] == 'essay') {
            $has_manual_grading = true;
            break;
        }
    }

    $status = $has_manual_grading ? 'completed' : 'graded';

    $stmt = $conn->prepare($update_query);
    $stmt->bind_param("sisi", $end_time, $score, $status, $active_attempt['id']);

    if ($stmt->execute()) {
        header("Location: quiz_result.php?attempt=" . $active_attempt['id']);
        exit();
    }
    $stmt->close();
}

// Get questions for this quiz if there's an active attempt
$questions = [];
if ($active_attempt) {
    $questions_query = "SELECT * FROM training_questions WHERE quiz_id = ?";

    // If randomize is enabled, add ORDER BY RAND()
    if ($quiz['randomize_questions']) {
        $questions_query .= " ORDER BY RAND()";
    } else {
        $questions_query .= " ORDER BY order_number ASC";
    }

    $stmt = $conn->prepare($questions_query);
    $stmt->bind_param("i", $quiz_id);
    $stmt->execute();
    $questions_result = $stmt->get_result();

    while ($row = $questions_result->fetch_assoc()) {
        // For multiple choice questions, get options
        if ($row['question_type'] == 'multiple_choice' || $row['question_type'] == 'true_false') {
            $options_query = "SELECT * FROM training_question_options WHERE question_id = ? ORDER BY order_number ASC";
            $options_stmt = $conn->prepare($options_query);
            $options_stmt->bind_param("i", $row['id']);
            $options_stmt->execute();
            $options_result = $options_stmt->get_result();

            $options = [];
            while ($option = $options_result->fetch_assoc()) {
                $options[] = $option;
            }
            $options_stmt->close();

            $row['options'] = $options;
        }

        $questions[] = $row;
    }
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    body {
        background-color: #f5f5f5;
    }

    .quiz-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .quiz-header {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .quiz-title {
        font-size: 1.8rem;
        margin-bottom: 10px;
        color: #333;
    }

    .quiz-meta {
        color: #6c757d;
        margin-bottom: 15px;
    }

    .quiz-description {
        margin-bottom: 15px;
    }

    .quiz-instructions {
        background-color: #e9ecef;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 15px;
    }

    .question-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .question-number {
        font-size: 1.1rem;
        color: #6c757d;
        margin-bottom: 10px;
        font-weight:bold;
    }

    .question-text {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 15px;
    }

    .question-points {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 15px;
    }

    .options-list {
        list-style-type: none;
        padding: 0;
        margin: 0;
    }

    .option-item {
        margin-bottom: 10px;
    }

    .option-label {
        display: flex;
        align-items: flex-start;
        padding: 10px;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .option-label:hover {
        background-color: #f8f9fa;
    }

    .option-input {
        margin-right: 10px;
        margin-top: 3px;
    }

    .option-text {
        flex-grow: 1;
    }

    .text-answer {
        width: 100%;
    }

    .question-options-container {
        position: relative;
        margin-bottom: 10px;
    }

    .clear-selection-btn {
        margin-top: 10px;
        font-size: 0.85rem;
        padding: 4px 8px;
        opacity: 0.7;
        transition: opacity 0.2s ease;
    }

    .clear-selection-btn:hover {
        opacity: 1;
    }

    .clear-selection-btn.hidden {
        display: none;
    }

    .quiz-actions {
        margin-top: 30px;
        display: flex;
        justify-content: space-between;
    }

    .timer-container {
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #fff;
        padding: 10px 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        z-index: 1000;
    }

    .timer {
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
    }

    .timer.warning {
        color: #ffc107;
    }

    .timer.danger {
        color: #dc3545;
    }

    .attempts-list {
        list-style-type: none;
        padding: 0;
        margin: 0;
    }

    .attempt-item {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .attempt-info {
        flex-grow: 1;
    }

    .attempt-date {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .attempt-score {
        font-size: 1.2rem;
        font-weight: 600;
    }

    .score-high {
        color: #28a745;
    }

    .score-medium {
        color: #ffc107;
    }

    .score-low {
        color: #dc3545;
    }

    /* Styles for quiz mode */
    .quiz-mode {
        padding-top: 20px;
    }

    .quiz-mode .navbar,
    .quiz-mode footer {
        display: none !important;
    }

    .quiz-mode .jarak {
        display: none;
    }

    .quiz-header-bar {
        background-color: rgb(157, 0, 0);
        color: white;
        padding: 15px 20px;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .quiz-header-title {
        font-weight: 600;
        font-size: 1.2rem;
    }

    .blur-content {
        filter: blur(5px);
        pointer-events: none;
    }

    .tab-warning {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(220, 53, 69, 0.9);
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 2000;
        padding: 20px;
        text-align: center;
    }

    .tab-warning h2 {
        margin-bottom: 20px;
    }

    .tab-warning p {
        margin-bottom: 30px;
        font-size: 1.1rem;
    }

    .tab-warning button {
        padding: 10px 20px;
        background-color: white;
        color: #dc3545;
        border: none;
        border-radius: 5px;
        font-weight: 600;
        cursor: pointer;
    }
</style>

<body<?php if ($active_attempt): ?> class="quiz-mode"<?php endif; ?>>
<?php if ($active_attempt): ?>
<div class="quiz-header-bar">
    <div class="quiz-header-title">
        <i class="fas fa-question-circle"></i> <?= htmlspecialchars($quiz['title']) ?>
    </div>
    <div>
        <?php if ($quiz['time_limit']): ?>
            <div class="timer" id="timer">
                <i class="fas fa-clock"></i> <span id="time-remaining"></span>
            </div>
            <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Konversi batas waktu dari menit ke detik
                let timeLimit = <?= $quiz['time_limit'] ?> * 60;
                let warningTime = 300; // 5 menit sebelum habis
                let dangerTime = 60; // 1 menit sebelum habis
                let isSubmitting = false; // Flag untuk mencegah submit berulang

                function updateTimer() {
                    if (isSubmitting) return; // Jangan update timer jika sedang submit

                    const minutes = Math.floor(timeLimit / 60);
                    const seconds = timeLimit % 60;
                    const timerDisplay = document.getElementById('time-remaining');
                    if (!timerDisplay) return; // Pastikan elemen masih ada

                    timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

                    // Tambahkan kelas visual untuk peringatan
                    if (timeLimit <= dangerTime) {
                        timerDisplay.parentElement.classList.add('danger');
                        if (!document.getElementById('warning-message')) {
                            showWarningMessage('Waktu hampir habis! Kuis akan disubmit otomatis dalam 1 menit.');
                        }
                    } else if (timeLimit <= warningTime) {
                        timerDisplay.parentElement.classList.add('warning');
                        if (!document.getElementById('warning-message')) {
                            showWarningMessage('Waktu tersisa 5 menit! Harap selesaikan kuis Anda.');
                        }
                    }

                    if (timeLimit <= 0 && !isSubmitting) {
                        isSubmitting = true; // Set flag submit
                        clearInterval(timerInterval);
                        showWarningMessage('Waktu habis! Kuis akan disubmit otomatis.');

                        // Dapatkan form asli dan semua jawaban yang sudah diisi
                        const originalForm = document.getElementById('quiz-form');
                        const formData = new FormData(originalForm);

                        // Buat form baru untuk submit
                        const tempForm = document.createElement('form');
                        tempForm.method = 'post';
                        tempForm.action = window.location.href;
                        tempForm.style.display = 'none';

                        // Tambahkan semua jawaban dari form asli ke form baru
                        for (const [name, value] of formData.entries()) {
                            // Skip button submit yang asli
                            if (name === 'submit_attempt' && value === 'Selesaikan Kuis') {
                                continue;
                            }

                            const input = document.createElement('input');
                            input.type = 'hidden';
                            input.name = name;
                            input.value = value;
                            tempForm.appendChild(input);
                        }

                        // Tambahkan input hidden untuk menandai submit
                        const autoSubmitInput = document.createElement('input');
                        autoSubmitInput.type = 'hidden';
                        autoSubmitInput.name = 'submit_attempt';
                        autoSubmitInput.value = '1';
                        tempForm.appendChild(autoSubmitInput);

                        // Log untuk debugging
                        console.log('Auto submitting quiz with answers (inline timer):', formData);

                        // Tambahkan form ke body dan submit
                        document.body.appendChild(tempForm);

                        // Submit form setelah delay singkat
                        setTimeout(() => {
                            if (!isSubmitting) return; // Double check flag
                            console.log('Submitting form now (inline timer)...');
                            tempForm.submit();
                        }, 1000);
                        return;
                    }

                    timeLimit--;
                }

                function showWarningMessage(message) {
                    // Hapus pesan peringatan yang ada
                    const existingWarning = document.getElementById('warning-message');
                    if (existingWarning) existingWarning.remove();

                    const warningDiv = document.createElement('div');
                    warningDiv.id = 'warning-message';
                    warningDiv.style.position = 'fixed';
                    warningDiv.style.top = '50%';
                    warningDiv.style.left = '50%';
                    warningDiv.style.transform = 'translate(-50%, -50%)';
                    warningDiv.style.backgroundColor = '#dc3545';
                    warningDiv.style.color = 'white';
                    warningDiv.style.padding = '20px';
                    warningDiv.style.borderRadius = '8px';
                    warningDiv.style.zIndex = '9999';
                    warningDiv.textContent = message;
                    document.body.appendChild(warningDiv);

                    if (!isSubmitting) { // Hanya hapus pesan jika bukan pesan submit terakhir
                        setTimeout(() => {
                            if (warningDiv && warningDiv.parentNode) {
                                warningDiv.remove();
                            }
                        }, 3000);
                    }
                }

                // Tambahkan event listener untuk form submit
                const quizForm = document.getElementById('quiz-form');
                if (quizForm) {
                    quizForm.addEventListener('submit', function(e) {
                        if (isSubmitting) {
                            e.preventDefault(); // Mencegah submit berulang
                            return;
                        }
                        isSubmitting = true;
                    });
                }

                const timerInterval = setInterval(updateTimer, 1000);
                updateTimer(); // Panggil sekali untuk menampilkan waktu awal
            });
            </script>
        <?php endif; ?>
    </div>
</div>

<!-- Tab warning overlay (hidden by default) -->
<div id="tab-warning" class="tab-warning" style="display: none;">
    <h2><i class="fas fa-exclamation-triangle"></i> Peringatan!</h2>
    <p>Anda telah beralih dari tab kuis. <span class="fw-bold">Semua jawaban Anda telah dihapus</span> sebagai tindakan keamanan.</p>
    <p>Hal ini terjadi karena Anda:</p>
    <ul class="text-start mb-4">
        <li>Membuka tab baru</li>
        <li>Beralih ke aplikasi lain</li>
        <li>Meminimalkan browser</li>
        <li>Atau melakukan tindakan lain yang menyebabkan tab ini tidak aktif</li>
    </ul>
    <p class="mb-4">Anda harus mengerjakan kuis ini dengan jujur tanpa mencari jawaban dari sumber lain.</p>
    <button id="continue-quiz-btn" class="btn btn-light btn-lg">Lanjutkan Kuis</button>
</div>
<?php else: ?>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<?php endif; ?>

<div class="quiz-container" id="quiz-content">
    <div class="quiz-content">
        <h1 class="welcome-section"><?= htmlspecialchars($quiz['title']) ?>
        <br>
        <a href="classroom_detail.php" class="btn">Kembali ke Detail kelas</a>
    </h1>
        <div class="form-container">
            <label for="">Informasi Kuis</label>
            <div><strong>Kelas:</strong> <?= htmlspecialchars($quiz['class_title']) ?></div>
            <div><strong>Topik:</strong> <?= htmlspecialchars($quiz['training_topic']) ?></div>
            <?php if ($quiz['time_limit']): ?>
                <div><strong>Batas Waktu:</strong> <?= $quiz['time_limit'] ?> menit</div>
            <?php endif; ?>
            <?php if ($quiz['passing_score']): ?>
                <div><strong>Nilai Kelulusan:</strong> <?= $quiz['passing_score'] ?>%</div>
            <?php endif; ?>
            <?php if ($quiz['allow_multiple_attempts'] && $quiz['max_attempts'] !== null): ?>
                <div><strong>Batas Percobaan:</strong> <?= $quiz['max_attempts'] ?> kali</div>
                <div><strong>Percobaan yang Tersisa:</strong> <?= max(0, $quiz['max_attempts'] - count($completed_attempts)) ?> kali</div>
            <?php endif; ?>
        </div>

        <?php if (!empty($quiz['description'])): ?>
            <div class="form-container" style="margin-bottom:10px;margin-top:10px; ">
                <label>Deskripsi</label>
                <br>
                <?= nl2br(htmlspecialchars($quiz['description'])) ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($quiz['instructions'])): ?>
            <div class="quiz-instructions">
                <h5><i class="fas fa-info-circle"></i> Instruksi Pengerjaan</h5>
                <?= nl2br(htmlspecialchars($quiz['instructions'])) ?>
            </div>
        <?php endif; ?>
    </div>

    <?php if ($active_attempt): ?>

        <form method="post" action="" id="quiz-form">
            <?php foreach ($questions as $index => $question): ?>
                <div class="question-card">
                    <div class="question-number">Pertanyaan <?= $index + 1 ?> dari <?= count($questions) ?></div>
                    <div class="question-text"><?= htmlspecialchars($question['question_text']) ?></div>
                    <div class="question-points"><?= $question['points'] ?> poin</div>

                    <?php if ($question['question_type'] == 'multiple_choice'): ?>
                        <div class="question-options-container">
                            <ul class="options-list">
                                <?php foreach ($question['options'] as $option): ?>
                                    <li class="option-item">
                                        <label class="option-label">
                                            <input type="radio" name="answer_<?= $question['id'] ?>" value="<?= $option['id'] ?>" class="option-input" required>
                                            <span class="option-text"><?= htmlspecialchars($option['option_text']) ?></span>
                                        </label>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn btn-sm btn-outline-secondary clear-selection-btn" data-question-id="<?= $question['id'] ?>">
                                <i class="fas fa-times"></i> Batal Memilih
                            </button>
                        </div>
                    <?php elseif ($question['question_type'] == 'true_false'): ?>
                        <div class="question-options-container">
                            <ul class="options-list">
                                <?php foreach ($question['options'] as $option): ?>
                                    <li class="option-item">
                                        <label class="option-label">
                                            <input type="radio" name="answer_<?= $question['id'] ?>" value="<?= $option['id'] ?>" class="option-input" required>
                                            <span class="option-text"><?= htmlspecialchars($option['option_text']) ?></span>
                                        </label>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn btn-sm btn-outline-secondary clear-selection-btn" data-question-id="<?= $question['id'] ?>">
                                <i class="fas fa-times"></i> Batal Memilih
                            </button>
                        </div>
                    <?php elseif ($question['question_type'] == 'short_answer'): ?>
                        <div class="mb-3">
                            <input type="text" class="form-control text-answer" name="answer_<?= $question['id'] ?>" placeholder="Ketik jawaban singkat Anda di sini" required>
                        </div>
                    <?php elseif ($question['question_type'] == 'essay'): ?>
                        <div class="mb-3">
                            <textarea class="form-control text-answer" name="answer_<?= $question['id'] ?>" rows="5" placeholder="Ketik jawaban esai Anda di sini" required></textarea>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>

            <div class="quiz-actions">
                <a href="#" class="btn btn-secondary" id="cancelQuizBtn">
                    <i class="fas fa-times"></i> Batal
                </a>
                <button type="submit" name="submit_attempt" class="btn btn-primary" id="finishQuizBtn">
                    <i class="fas fa-check"></i> Selesaikan Kuis
                </button>
            </div>
        </form>
    <?php else: ?>
        <?php if (!empty($completed_attempts)): ?>
            <div class="form-container" style="margin-bottom:10px; margin-top:10px;">
                <h3>Riwayat Percobaan</h3>
                <ul class="attempts-list">
                    <?php foreach ($completed_attempts as $index => $attempt): ?>
                        <li class="attempt-item">
                            <div class="attempt-info">
                                <div class="attempt-date">
                                    Percobaan #<?= count($completed_attempts) - $index ?> -
                                    <?= date('d M Y H:i', strtotime($attempt['end_time'])) ?>
                                </div>
                                <div class="attempt-score
                                    <?php
                                        if ($attempt['score'] >= 80) echo 'score-high';
                                        else if ($attempt['score'] >= 60) echo 'score-medium';
                                        else echo 'score-low';
                                    ?>" style="margin-right:10px;">
                                    Nilai: <?= $attempt['score'] ?>%<a href="quiz_result.php?attempt=<?= $attempt['id'] ?>"
                                    <?php if ($quiz['passing_score'] && $attempt['score'] >= $quiz['passing_score']): ?>
                                        <span class="badge bg-success">Lulus</span>
                                    <?php elseif ($quiz['passing_score']): ?>
                                        <span class="badge bg-danger">Tidak Lulus</span>
                                    <?php endif; ?>
                                    </a>
                                </div>
                            </div>
                            <a href="quiz_result.php?attempt=<?= $attempt['id'] ?>" class="btn btn-sm btn-primary">
                                <i class="fas fa-eye"></i> Lihat Hasil
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ($can_start_new_attempt): ?>
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Peringatan Penting</h4>
                </div>
                <div class="card-body">
                    <p class="fw-bold">Harap baca dengan seksama sebelum memulai kuis:</p>
                    <ul class="mb-4">
                        <li>Setelah memulai kuis, Anda tidak dapat meninggalkan halaman ini sampai kuis selesai.</li>
                        <li>Jika Anda beralih ke tab atau aplikasi lain, semua jawaban Anda akan <span class="text-danger fw-bold">dihapus</span> sebagai tindakan keamanan.</li>
                        <li>Klik kanan, copy-paste, dan shortcut keyboard lainnya dinonaktifkan selama kuis.</li>
                        <?php if ($quiz['time_limit']): ?>
                            <li>Kuis ini memiliki batas waktu <span class="fw-bold"><?= $quiz['time_limit'] ?> menit</span>. Jika waktu habis, kuis akan otomatis diserahkan.</li>
                        <?php endif; ?>
                        <li>Pastikan Anda memiliki koneksi internet yang stabil dan tidak akan terganggu selama mengerjakan kuis.</li>
                    </ul>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> Dengan memulai kuis ini, Anda menyetujui aturan di atas dan memahami konsekuensinya.
                    </div>
                </div>
                <div class="card-footer text-center">
                    <form method="post" action="">
                        <div class="form-check mb-3 d-inline-block text-start">
                            <input class="form-check-input" type="checkbox" id="agree-terms" required>
                            <label class="form-check-label" for="agree-terms">
                                Saya telah membaca dan menyetujui aturan kuis
                            </label>
                        </div>
                        <div class="d-grid gap-2 col-md-6 mx-auto">
                            <button type="submit" name="start_attempt" class="btn btn-lg btn-primary" id="start-quiz-btn" disabled>
                                <i class="fas fa-play"></i> Mulai Kuis
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-info text-center">
                <?php if ($quiz['allow_multiple_attempts'] && $quiz['max_attempts'] !== null && count($completed_attempts) >= $quiz['max_attempts']): ?>
                    <i class="fas fa-info-circle"></i> Anda telah mencapai batas maksimum percobaan (<?= $quiz['max_attempts'] ?> kali) untuk kuis ini.
                <?php else: ?>
                    <i class="fas fa-info-circle"></i> Anda telah menyelesaikan kuis ini dan tidak diizinkan untuk mencoba lagi.
                <?php endif; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<?php include '../config/footer.php'; ?>

<audio id="warning-sound" src="../assets/audio/warning.mp3" preload="auto"></audio>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Flag to track intentional form submission
    let isIntentionalSubmit = false;

    <?php if ($active_attempt && $quiz['time_limit']): ?>
        // Set up timer
        const startTime = new Date('<?= $active_attempt['start_time'] ?>');
        const timeLimit = <?= $quiz['time_limit'] ?> * 60 * 1000; // Convert minutes to milliseconds
        const endTime = new Date(startTime.getTime() + timeLimit);

        // Fungsi untuk menampilkan pesan waktu habis
        function showTimeUpMessage() {
            const warningDiv = document.createElement('div');
            warningDiv.id = 'time-up-message';
            warningDiv.style.position = 'fixed';
            warningDiv.style.top = '0';
            warningDiv.style.left = '0';
            warningDiv.style.width = '100%';
            warningDiv.style.height = '100%';
            warningDiv.style.backgroundColor = 'rgba(220, 53, 69, 0.9)';
            warningDiv.style.color = 'white';
            warningDiv.style.display = 'flex';
            warningDiv.style.flexDirection = 'column';
            warningDiv.style.justifyContent = 'center';
            warningDiv.style.alignItems = 'center';
            warningDiv.style.zIndex = '10000';
            warningDiv.style.textAlign = 'center';
            warningDiv.style.padding = '20px';

            const messageTitle = document.createElement('h2');
            messageTitle.innerHTML = '<i class="fas fa-clock"></i> Waktu Habis!';
            messageTitle.style.marginBottom = '20px';

            const messageText = document.createElement('p');
            messageText.textContent = 'Kuis Anda akan disubmit secara otomatis...';
            messageText.style.fontSize = '1.2rem';
            messageText.style.marginBottom = '20px';

            const spinner = document.createElement('div');
            spinner.className = 'spinner-border text-light';
            spinner.setAttribute('role', 'status');
            spinner.style.width = '3rem';
            spinner.style.height = '3rem';

            warningDiv.appendChild(messageTitle);
            warningDiv.appendChild(messageText);
            warningDiv.appendChild(spinner);

            document.body.appendChild(warningDiv);
        }

        function updateTimer() {
            const now = new Date();
            const timeRemaining = endTime - now;

            const timerElements = document.querySelectorAll('.timer');
            if (timeRemaining <= 0) {
                // Time's up, submit the form
                timerElements.forEach(element => {
                    element.innerHTML = '<i class="fas fa-clock"></i> Waktu Habis!';
                });

                // Prevent multiple submissions
                if (!isIntentionalSubmit) {
                    isIntentionalSubmit = true;

                    // Tampilkan pesan waktu habis
                    showTimeUpMessage();

                    // Dapatkan form asli dan semua jawaban yang sudah diisi
                    const originalForm = document.getElementById('quiz-form');
                    const formData = new FormData(originalForm);

                    // Buat form baru untuk submit
                    const tempForm = document.createElement('form');
                    tempForm.method = 'post';
                    tempForm.action = window.location.href;
                    tempForm.style.display = 'none';

                    // Tambahkan semua jawaban dari form asli ke form baru
                    for (const [name, value] of formData.entries()) {
                        // Skip button submit yang asli
                        if (name === 'submit_attempt' && value === 'Selesaikan Kuis') {
                            continue;
                        }

                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = name;
                        input.value = value;
                        tempForm.appendChild(input);
                    }

                    // Tambahkan input hidden untuk menandai submit
                    const autoSubmitInput = document.createElement('input');
                    autoSubmitInput.type = 'hidden';
                    autoSubmitInput.name = 'submit_attempt';
                    autoSubmitInput.value = '1';
                    tempForm.appendChild(autoSubmitInput);

                    // Log untuk debugging
                    console.log('Auto submitting quiz with answers:', formData);

                    // Tambahkan form ke body dan submit
                    document.body.appendChild(tempForm);

                    // Delay submit untuk memastikan pesan ditampilkan
                    setTimeout(function() {
                        console.log('Submitting form now...');
                        tempForm.submit();
                    }, 1500);
                }
                return;
            }

            // Calculate minutes and seconds
            const minutes = Math.floor(timeRemaining / 60000);
            const seconds = Math.floor((timeRemaining % 60000) / 1000);
            const formattedTime = `${minutes}:${seconds.toString().padStart(2, '0')}`;

            // Update timer display and add warning classes
            timerElements.forEach(element => {
                element.innerHTML = `<i class="fas fa-clock"></i> ${formattedTime}`;
                if (timeRemaining < 60000) { // Less than 1 minute
                    element.classList.add('danger');
                    element.classList.remove('warning');
                } else if (timeRemaining < 300000) { // Less than 5 minutes
                    element.classList.add('warning');
                }
            });

            // Update every second
            setTimeout(updateTimer, 1000);
        }

        // Start the timer
        updateTimer();
    <?php endif; ?>

    <?php if ($active_attempt): ?>
        // Tab visibility handling
        let tabHidden = false;
        let answersCleared = false;
        let quizContentElement = document.getElementById('quiz-content');
        let tabWarningElement = document.getElementById('tab-warning');
        let continueQuizBtn = document.getElementById('continue-quiz-btn');
        let warningSound = document.getElementById('warning-sound');

        function playWarningSound() {
            if (warningSound) {
                warningSound.currentTime = 0;
                warningSound.play().catch(error => console.log('Error playing sound:', error));
            }
        }

        function clearFormInputs() {
            if (!answersCleared) {
                const form = document.getElementById('quiz-form');
                const inputs = form.querySelectorAll('input, textarea');

                inputs.forEach(input => {
                    if (input.type === 'radio' || input.type === 'checkbox') {
                        input.checked = false;
                    } else if (input.type === 'text' || input.tagName === 'TEXTAREA') {
                        input.value = '';
                    }
                });

                answersCleared = true;
                quizContentElement.classList.add('blur-content');
                tabWarningElement.style.display = 'flex';
                playWarningSound();
            }
        }

        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'hidden') {
                tabHidden = true;
                clearFormInputs();
            }
        });

        if (continueQuizBtn) {
            continueQuizBtn.addEventListener('click', function() {
                quizContentElement.classList.remove('blur-content');
                tabWarningElement.style.display = 'none';
                answersCleared = false;
            });
        }

        // Prevent right-click
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            playWarningSound();
            return false;
        });

        // Disable keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey && (e.key === 'c' || e.key === 'v' || e.key === 'f')) ||
                e.key === 'F12' || e.key === 'PrintScreen') {
                e.preventDefault();
                playWarningSound();
                return false;
            }
        });

        // Warn before leaving the page
        window.addEventListener('beforeunload', function(e) {
            if (isIntentionalSubmit) return;
            const confirmationMessage = 'PERINGATAN: Jika Anda meninggalkan halaman ini, semua jawaban Anda akan hilang dan tidak dapat dipulihkan!';
            playWarningSound();
            e.returnValue = confirmationMessage;
            return confirmationMessage;
        });
    <?php endif; ?>

    // Handle checkbox for starting quiz
    const agreeTermsCheckbox = document.getElementById('agree-terms');
    const startQuizBtn = document.getElementById('start-quiz-btn');

    if (agreeTermsCheckbox && startQuizBtn) {
        agreeTermsCheckbox.addEventListener('change', function() {
            startQuizBtn.disabled = !this.checked;
        });
    }

    // Handle "Batal Memilih" buttons for radio options
    const clearSelectionButtons = document.querySelectorAll('.clear-selection-btn');

    clearSelectionButtons.forEach(button => {
        const questionId = button.getAttribute('data-question-id');
        const radioInputs = document.querySelectorAll(`input[name="answer_${questionId}"]`);

        // Initially hide the button if no option is selected
        updateClearButtonVisibility(radioInputs, button);

        // Add event listeners to radio inputs to show/hide the clear button
        radioInputs.forEach(input => {
            input.addEventListener('change', function() {
                updateClearButtonVisibility(radioInputs, button);
            });
        });

        // Add click event to the clear button
        button.addEventListener('click', function() {
            // Uncheck all radio inputs for this question
            radioInputs.forEach(input => {
                input.checked = false;
            });

            // Hide the button after clearing
            button.classList.add('hidden');

            // Remove required attribute temporarily to allow form submission
            radioInputs.forEach(input => {
                input.required = false;
            });

            // Add required back after a short delay
            setTimeout(() => {
                radioInputs.forEach(input => {
                    input.required = true;
                });
            }, 100);
        });
    });

    // Function to update clear button visibility
    function updateClearButtonVisibility(radioInputs, button) {
        let anyChecked = false;

        radioInputs.forEach(input => {
            if (input.checked) {
                anyChecked = true;
            }
        });

        if (anyChecked) {
            button.classList.remove('hidden');
        } else {
            button.classList.add('hidden');
        }
    }

    // Handle cancel and finish buttons
    const cancelQuizBtn = document.getElementById('cancelQuizBtn');
    const finishQuizBtn = document.getElementById('finishQuizBtn');
    const quizForm = document.getElementById('quiz-form');

    if (cancelQuizBtn) {
        cancelQuizBtn.addEventListener('click', function(e) {
            e.preventDefault();
            confirmAction(
                'Apakah Anda yakin ingin keluar? Progres Anda tidak akan disimpan.',
                function() {
                    isIntentionalSubmit = true;
                    window.location.href = 'classroom_detail.php?id=<?= $quiz['class_id'] ?>';
                }
            );
        });
    }

    if (finishQuizBtn && quizForm) {
        finishQuizBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const form = document.getElementById('quiz-form');
            const questions = form.querySelectorAll('.question-card');
            let unansweredQuestions = 0;

            questions.forEach(function(question) {
                const inputs = question.querySelectorAll('input[type="radio"], input[type="text"], textarea');
                let isAnswered = false;

                inputs.forEach(function(input) {
                    if ((input.type === 'radio' && input.checked) ||
                        ((input.type === 'text' || input.tagName === 'TEXTAREA') && input.value.trim() !== '')) {
                        isAnswered = true;
                    }
                });

                if (!isAnswered) {
                    unansweredQuestions++;
                }
            });

            let confirmMessage = 'Apakah Anda yakin ingin menyelesaikan kuis ini?';
            if (unansweredQuestions > 0) {
                confirmMessage = `PERHATIAN: Anda memiliki ${unansweredQuestions} pertanyaan yang belum dijawab. Apakah Anda tetap ingin menyelesaikan kuis ini?`;
            }

            confirmAction(
                confirmMessage,
                function() {
                    // Set flag to prevent beforeunload warning
                    isIntentionalSubmit = true;

                    // Create and append hidden input for submission
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'submit_attempt';
                    hiddenInput.value = '1';
                    quizForm.appendChild(hiddenInput);

                    // Add a small delay to ensure the DOM updates before submitting
                    setTimeout(function() {
                        console.log('Submitting quiz form...');
                        quizForm.submit();
                    }, 100);
                }
            );
        });
    }

    // Custom confirm action function with modal
    function confirmAction(message, callback) {
        // Create modal elements
        const modalOverlay = document.createElement('div');
        modalOverlay.className = 'modal-overlay';
        modalOverlay.style.position = 'fixed';
        modalOverlay.style.top = '0';
        modalOverlay.style.left = '0';
        modalOverlay.style.width = '100%';
        modalOverlay.style.height = '100%';
        modalOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        modalOverlay.style.zIndex = '9999';
        modalOverlay.style.display = 'flex';
        modalOverlay.style.justifyContent = 'center';
        modalOverlay.style.alignItems = 'center';

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        modalContent.style.backgroundColor = '#fff';
        modalContent.style.borderRadius = '8px';
        modalContent.style.padding = '20px';
        modalContent.style.width = '400px';
        modalContent.style.maxWidth = '90%';
        modalContent.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

        const modalHeader = document.createElement('div');
        modalHeader.className = 'modal-header';
        modalHeader.style.marginBottom = '15px';

        const modalTitle = document.createElement('h4');
        modalTitle.textContent = 'Konfirmasi';
        modalTitle.style.margin = '0';
        modalTitle.style.color = '#333';

        const modalBody = document.createElement('div');
        modalBody.className = 'modal-body';
        modalBody.style.marginBottom = '20px';
        modalBody.innerHTML = message;

        const modalFooter = document.createElement('div');
        modalFooter.className = 'modal-footer';
        modalFooter.style.display = 'flex';
        modalFooter.style.justifyContent = 'flex-end';
        modalFooter.style.gap = '10px';

        const cancelButton = document.createElement('button');
        cancelButton.textContent = 'Batal';
        cancelButton.className = 'btn btn-secondary';
        cancelButton.style.padding = '8px 16px';
        cancelButton.style.backgroundColor = '#6c757d';
        cancelButton.style.color = '#fff';
        cancelButton.style.border = 'none';
        cancelButton.style.borderRadius = '4px';
        cancelButton.style.cursor = 'pointer';

        const confirmButton = document.createElement('button');
        confirmButton.textContent = 'Ya, Lanjutkan';
        confirmButton.className = 'btn btn-primary';
        confirmButton.style.padding = '8px 16px';
        confirmButton.style.backgroundColor = '#BF0000';
        confirmButton.style.color = '#fff';
        confirmButton.style.border = 'none';
        confirmButton.style.borderRadius = '4px';
        confirmButton.style.cursor = 'pointer';

        // Assemble modal
        modalHeader.appendChild(modalTitle);
        modalFooter.appendChild(cancelButton);
        modalFooter.appendChild(confirmButton);

        modalContent.appendChild(modalHeader);
        modalContent.appendChild(modalBody);
        modalContent.appendChild(modalFooter);
        modalOverlay.appendChild(modalContent);

        // Add modal to body
        document.body.appendChild(modalOverlay);

        // Handle button clicks
        cancelButton.addEventListener('click', function() {
            document.body.removeChild(modalOverlay);
        });

        confirmButton.addEventListener('click', function() {
            document.body.removeChild(modalOverlay);
            callback();
        });
    }
});
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
