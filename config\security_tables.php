<?php
/**
 * File untuk membuat tabel-tabel keamanan di database
 * Jalankan file ini sekali untuk membuat tabel-tabel yang diperlukan
 */

// Include konfigurasi database
require_once 'config.php';

// Fungsi untuk membuat tabel jika belum ada
function create_table_if_not_exists($conn, $table_name, $create_query) {
    // Cek apakah tabel sudah ada
    $check_query = "SHOW TABLES LIKE '$table_name'";
    $result = $conn->query($check_query);

    if ($result->num_rows == 0) {
        // Tabel belum ada, buat tabel baru
        if ($conn->query($create_query) === TRUE) {
            echo "Tabel $table_name berhasil dibuat.<br>";
        } else {
            echo "Error membuat tabel $table_name: " . $conn->error . "<br>";
        }
    } else {
        echo "Tabel $table_name sudah ada.<br>";
    }
}

// 1. Tabel untuk mencatat percobaan login
$create_login_attempts = "CREATE TABLE login_attempts (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent VARCHAR(255) NOT NULL,
    attempt_time DATETIME NOT NULL,
    success TINYINT(1) NOT NULL DEFAULT 0,
    INDEX (user_id),
    INDEX (ip_address),
    INDEX (attempt_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

create_table_if_not_exists($conn, 'login_attempts', $create_login_attempts);

// 2. Tabel untuk rate limiting
$create_rate_limits = "CREATE TABLE rate_limits (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    action_type VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    attempt_time DATETIME NOT NULL,
    INDEX (user_id, action_type),
    INDEX (ip_address),
    INDEX (attempt_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

create_table_if_not_exists($conn, 'rate_limits', $create_rate_limits);

// 3. Tabel untuk log keamanan
$create_security_logs = "CREATE TABLE security_logs (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL,
    INDEX (user_id),
    INDEX (event_type),
    INDEX (ip_address),
    INDEX (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

create_table_if_not_exists($conn, 'security_logs', $create_security_logs);

// 4. Tabel untuk token CSRF
$create_csrf_tokens = "CREATE TABLE csrf_tokens (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    token VARCHAR(64) NOT NULL,
    session_id VARCHAR(128) NOT NULL,
    created_at DATETIME NOT NULL,
    expires_at DATETIME NOT NULL,
    used TINYINT(1) NOT NULL DEFAULT 0,
    UNIQUE KEY (token),
    INDEX (session_id),
    INDEX (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

create_table_if_not_exists($conn, 'csrf_tokens', $create_csrf_tokens);

// 5. Tambahkan kolom keamanan ke tabel users jika belum ada
// Daftar kolom yang akan ditambahkan
$security_columns = [
    'failed_attempts' => 'INT(11) NOT NULL DEFAULT 0',
    'last_failed_attempt' => 'DATETIME NULL',
    'account_locked' => 'TINYINT(1) NOT NULL DEFAULT 0',
    'lock_expires' => 'DATETIME NULL',
    'password_changed_at' => 'DATETIME NULL',
    'reset_token' => 'VARCHAR(64) NULL',
    'reset_token_expires' => 'DATETIME NULL',
    'last_login' => 'DATETIME NULL',
    'last_login_ip' => 'VARCHAR(45) NULL',
    'require_password_change' => 'TINYINT(1) NOT NULL DEFAULT 0',
    'two_factor_enabled' => 'TINYINT(1) NOT NULL DEFAULT 0',
    'two_factor_secret' => 'VARCHAR(64) NULL'
];

// Cek apakah tabel users ada
$check_users_table = "SHOW TABLES LIKE 'users'";
$users_table_result = $conn->query($check_users_table);

if ($users_table_result->num_rows > 0) {
    // Tabel users ada, tambahkan kolom jika belum ada
    foreach ($security_columns as $column_name => $column_definition) {
        // Cek apakah kolom sudah ada
        $check_column_query = "SHOW COLUMNS FROM users LIKE '$column_name'";
        $column_result = $conn->query($check_column_query);

        if ($column_result->num_rows == 0) {
            // Kolom belum ada, tambahkan kolom baru
            $add_column_query = "ALTER TABLE users ADD COLUMN $column_name $column_definition";

            if ($conn->query($add_column_query) === TRUE) {
                echo "Kolom $column_name berhasil ditambahkan ke tabel users.<br>";
            } else {
                echo "Error menambahkan kolom $column_name: " . $conn->error . "<br>";
            }
        } else {
            echo "Kolom $column_name sudah ada di tabel users.<br>";
        }
    }

    // 6. Tambahkan indeks ke tabel users untuk kolom keamanan jika belum ada
    $security_indexes = [
        'idx_users_failed_attempts' => 'failed_attempts',
        'idx_users_account_locked' => 'account_locked',
        'idx_users_reset_token' => 'reset_token',
        'idx_users_two_factor_enabled' => 'two_factor_enabled'
    ];

    foreach ($security_indexes as $index_name => $column_name) {
        // Cek apakah indeks sudah ada
        $check_index_query = "SHOW INDEX FROM users WHERE Key_name = '$index_name'";
        $index_result = $conn->query($check_index_query);

        if ($index_result->num_rows == 0) {
            // Indeks belum ada, tambahkan indeks baru
            $add_index_query = "CREATE INDEX $index_name ON users ($column_name)";

            if ($conn->query($add_index_query) === TRUE) {
                echo "Indeks $index_name berhasil ditambahkan ke tabel users.<br>";
            } else {
                echo "Error menambahkan indeks $index_name: " . $conn->error . "<br>";
            }
        } else {
            echo "Indeks $index_name sudah ada di tabel users.<br>";
        }
    }
} else {
    echo "Tabel users tidak ditemukan. Silakan buat tabel users terlebih dahulu.<br>";
}

echo "<br>Proses pembuatan tabel keamanan selesai.";

// Tutup koneksi database
$conn->close();
?>
