<?php
// Debug form submission
session_start();
include '../config/config.php';

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Debug Form Submission</h2>";

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h3>POST Data:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    echo "<h3>FILES Data:</h3>";
    echo "<pre>";
    print_r($_FILES);
    echo "</pre>";
    
    echo "<h3>Session Data:</h3>";
    echo "<pre>";
    print_r($_SESSION);
    echo "</pre>";
    
    // Test database connection
    echo "<h3>Database Connection Test:</h3>";
    if ($conn) {
        echo "✅ Database connection successful<br>";
        
        // Test query
        $test_query = "SELECT COUNT(*) as count FROM training_submissions";
        $result = $conn->query($test_query);
        if ($result) {
            $row = $result->fetch_assoc();
            echo "✅ Test query successful. Total training submissions: " . $row['count'] . "<br>";
        } else {
            echo "❌ Test query failed: " . $conn->error . "<br>";
        }
    } else {
        echo "❌ Database connection failed<br>";
    }
    
    // Check required fields
    echo "<h3>Required Fields Check:</h3>";
    $required_fields = ['status'];
    foreach ($required_fields as $field) {
        if (isset($_POST[$field]) && !empty($_POST[$field])) {
            echo "✅ $field: " . $_POST[$field] . "<br>";
        } else {
            echo "❌ $field: Missing or empty<br>";
        }
    }
    
    // Check file upload
    if (isset($_FILES['internal_memo_image'])) {
        echo "<h3>File Upload Check:</h3>";
        $file = $_FILES['internal_memo_image'];
        echo "File name: " . $file['name'] . "<br>";
        echo "File size: " . $file['size'] . " bytes<br>";
        echo "File type: " . $file['type'] . "<br>";
        echo "File error: " . $file['error'] . "<br>";
        echo "Temp name: " . $file['tmp_name'] . "<br>";
        
        // Check upload directory
        $upload_dir = '../uploads/internal_memo/';
        if (file_exists($upload_dir)) {
            echo "✅ Upload directory exists<br>";
            if (is_writable($upload_dir)) {
                echo "✅ Upload directory is writable<br>";
            } else {
                echo "❌ Upload directory is not writable<br>";
            }
        } else {
            echo "❌ Upload directory does not exist<br>";
            if (mkdir($upload_dir, 0755, true)) {
                echo "✅ Upload directory created successfully<br>";
            } else {
                echo "❌ Failed to create upload directory<br>";
            }
        }
    }
    
} else {
    echo "<p>No POST data received. Please submit the form from detail_training.php</p>";
}

echo "<br><a href='javascript:history.back()'>← Back to Form</a>";
?>
