<?php
// File: admin/rfid_attendance_offline.php
// Deskripsi: Halaman untuk scan RFID dan mencatat absensi Training Internal

include '../config/config.php';
include 'security.php';

// Ambil daftar Training Internal dengan debug
$query_training = "SELECT id, training_topic, start_date, end_date, training_time_start, training_time_end, location
                  FROM offline_training
                  WHERE status = 'Active' AND start_date <= CURDATE() AND end_date >= CURDATE()
                  ORDER BY start_date DESC";
$result_training = $conn->query($query_training);

// Debug: Jika tidak ada hasil, coba query yang lebih luas
if ($result_training->num_rows == 0) {
    // Coba ambil semua training aktif tanpa filter tanggal
    $debug_query = "SELECT id, training_topic, start_date, end_date, status
                   FROM offline_training
                   WHERE status = 'Active'
                   ORDER BY start_date DESC";
    $debug_result = $conn->query($debug_query);

    if ($debug_result->num_rows == 0) {
        // Tidak ada training aktif sama sekali, ambil semua training
        $query_training = "SELECT id, training_topic, start_date, end_date, training_time_start, training_time_end, location
                          FROM offline_training
                          ORDER BY start_date DESC LIMIT 10";
        $result_training = $conn->query($query_training);
    } else {
        // Ada training aktif tapi tidak dalam range tanggal, tampilkan semua training aktif
        $query_training = "SELECT id, training_topic, start_date, end_date, training_time_start, training_time_end, location
                          FROM offline_training
                          WHERE status = 'Active'
                          ORDER BY start_date DESC";
        $result_training = $conn->query($query_training);
    }
}

// Inisialisasi variabel
$message = '';
$error = '';
$success = '';

// Proses scan RFID
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Inisialisasi array respons untuk AJAX
    $response = [
        'success' => false,
        'message' => '',
        'employee' => null // Add employee details to response
    ];

    if (isset($_POST['card_number']) && !empty($_POST['card_number']) && isset($_POST['training_id']) && !empty($_POST['training_id'])) {
        $card_number = $_POST['card_number'];
        $training_id = $_POST['training_id'];
        $rfid_mode = $_POST['rfid_mode'] ?? 'auto'; // Default ke auto mode

        // Cek apakah card number terdaftar
        $query_karyawan = "SELECT id, nik, nama, dept, jabatan FROM karyawan WHERE card_number = ?";
        $stmt_karyawan = $conn->prepare($query_karyawan);
        $stmt_karyawan->bind_param("s", $card_number);
        $stmt_karyawan->execute();
        $result_karyawan = $stmt_karyawan->get_result();

        if ($result_karyawan->num_rows > 0) {
            $karyawan = $result_karyawan->fetch_assoc();
            $karyawan_id = $karyawan['id'];
            $nik = $karyawan['nik'];
            $nama = $karyawan['nama'];

            // Cek apakah karyawan sudah terdaftar di absensi training ini HARI INI
            $today = date('Y-m-d');

            // Cek apakah kolom created_at ada
            $check_column = "SHOW COLUMNS FROM offline_training_attendance LIKE 'created_at'";
            $column_result = $conn->query($check_column);

            if ($column_result->num_rows > 0) {
                // Kolom created_at ada, gunakan query dengan created_at
                $query_check = "SELECT id, check_in, check_out FROM offline_training_attendance
                               WHERE offline_training_id = ? AND (karyawan_id = ? OR card_number = ?)
                               AND (DATE(check_in) = ? OR (check_in IS NULL AND DATE(created_at) = ?))";
                $stmt_check = $conn->prepare($query_check);
                $stmt_check->bind_param("iisss", $training_id, $karyawan_id, $card_number, $today, $today);
            } else {
                // Kolom created_at tidak ada, gunakan query sederhana (fallback ke sistem lama)
                $query_check = "SELECT id, check_in, check_out FROM offline_training_attendance
                               WHERE offline_training_id = ? AND (karyawan_id = ? OR card_number = ?)
                               AND DATE(check_in) = ?";
                $stmt_check = $conn->prepare($query_check);
                $stmt_check->bind_param("iiss", $training_id, $karyawan_id, $card_number, $today);
            }

            $stmt_check->execute();
            $result_check = $stmt_check->get_result();

            if ($result_check->num_rows > 0) {
                // Karyawan sudah terdaftar
                $attendance = $result_check->fetch_assoc();
                $attendance_id = $attendance['id'];

                if ($rfid_mode === 'checkin_only') {
                    // Mode hanya check-in: selalu update check-in dengan waktu terbaru
                    $query_update = "UPDATE offline_training_attendance
                                    SET check_in = NOW(), status = 'hadir', updated_by = ?
                                    WHERE id = ?";
                    $stmt_update = $conn->prepare($query_update);
                    $stmt_update->bind_param("ii", $_SESSION['user_id'], $attendance_id);

                    if ($stmt_update->execute()) {
                        $success = "Check-in berhasil untuk {$nama} ({$nik}) - Mode: Hanya Check-in";
                        $response['success'] = true;
                        $response['message'] = $success;
                        $response['employee'] = [
                            'nik' => $karyawan['nik'],
                            'nama' => $karyawan['nama'],
                            'jabatan' => $karyawan['jabatan'] ?? '-',
                            'dept' => $karyawan['dept'] ?? '-'
                        ];
                    } else {
                        $error = "Gagal melakukan check-in: " . $conn->error;
                        $response['message'] = $error;
                    }
                } else {
                    // Mode auto: check-in → check-out
                    if ($attendance['check_in'] === null) {
                        // Belum check-in, lakukan check-in
                        $query_update = "UPDATE offline_training_attendance
                                        SET check_in = NOW(), status = 'hadir', updated_by = ?
                                        WHERE id = ?";
                        $stmt_update = $conn->prepare($query_update);
                        $stmt_update->bind_param("ii", $_SESSION['user_id'], $attendance_id);

                        if ($stmt_update->execute()) {
                            $success = "Check-in berhasil untuk {$nama} ({$nik})";
                            $response['success'] = true;
                            $response['message'] = $success;
                            $response['employee'] = [
                                'nik' => $karyawan['nik'],
                                'nama' => $karyawan['nama'],
                                'jabatan' => $karyawan['jabatan'] ?? '-',
                                'dept' => $karyawan['dept'] ?? '-'
                            ];
                        } else {
                            $error = "Gagal melakukan check-in: " . $conn->error;
                            $response['message'] = $error;
                        }
                    } elseif ($attendance['check_out'] === null) {
                        // Sudah check-in tapi belum check-out, lakukan check-out
                        $query_update = "UPDATE offline_training_attendance
                                        SET check_out = NOW(), updated_by = ?
                                        WHERE id = ?";
                        $stmt_update = $conn->prepare($query_update);
                        $stmt_update->bind_param("ii", $_SESSION['user_id'], $attendance_id);

                        if ($stmt_update->execute()) {
                            $success = "Check-out berhasil untuk {$nama} ({$nik})";
                            $response['success'] = true;
                            $response['message'] = $success;
                            $response['employee'] = [
                                'nik' => $karyawan['nik'],
                                'nama' => $karyawan['nama'],
                                'jabatan' => $karyawan['jabatan'] ?? '-',
                                'dept' => $karyawan['dept'] ?? '-'
                            ];
                        } else {
                            $error = "Gagal melakukan check-out: " . $conn->error;
                            $response['message'] = $error;
                        }
                    } else {
                        // Sudah check-in dan check-out
                        if ($rfid_mode === 'auto') {
                            $message = "Karyawan {$nama} ({$nik}) sudah melakukan check-in dan check-out hari ini";
                        } else {
                            $message = "Karyawan {$nama} ({$nik}) sudah melakukan check-in dan check-out";
                        }
                        $response['success'] = true;
                        $response['message'] = $message;
                        $response['employee'] = [
                            'nik' => $karyawan['nik'],
                            'nama' => $karyawan['nama'],
                            'jabatan' => $karyawan['jabatan'] ?? '-',
                            'dept' => $karyawan['dept'] ?? '-'
                        ];
                    }
                }
            } else {
                // Karyawan belum terdaftar HARI INI, tambahkan ke absensi
                if ($column_result->num_rows > 0) {
                    // Kolom created_at ada
                    $query_insert = "INSERT INTO offline_training_attendance
                                    (offline_training_id, karyawan_id, nik, card_number, nama, check_in, status, created_by, created_at)
                                    VALUES (?, ?, ?, ?, ?, NOW(), 'hadir', ?, NOW())";
                } else {
                    // Kolom created_at tidak ada
                    $query_insert = "INSERT INTO offline_training_attendance
                                    (offline_training_id, karyawan_id, nik, card_number, nama, check_in, status, created_by)
                                    VALUES (?, ?, ?, ?, ?, NOW(), 'hadir', ?)";
                }

                $stmt_insert = $conn->prepare($query_insert);
                $stmt_insert->bind_param("iisssi", $training_id, $karyawan_id, $nik, $card_number, $nama, $_SESSION['user_id']);

                if ($stmt_insert->execute()) {
                    $success = "Absensi berhasil ditambahkan untuk {$nama} ({$nik}) - " . date('d-m-Y');
                    $response['success'] = true;
                    $response['message'] = $success;
                    $response['employee'] = [
                        'nik' => $karyawan['nik'],
                        'nama' => $karyawan['nama'],
                        'jabatan' => $karyawan['jabatan'] ?? '-',
                        'dept' => $karyawan['dept'] ?? '-'
                    ];
                } else {
                    $error = "Gagal menambahkan absensi: " . $conn->error;
                    $response['message'] = $error;
                }
            }
        } else {
            $error = "Kartu RFID tidak terdaftar";
            $response['message'] = $error;
        }
    } elseif (isset($_POST['manual_nik']) && !empty($_POST['manual_nik']) && isset($_POST['training_id']) && !empty($_POST['training_id'])) {
        // Proses absensi manual dengan NIK (unchanged)
        $nik = $_POST['manual_nik'];
        $training_id = $_POST['training_id'];

        $query_karyawan = "SELECT id, nik, nama, card_number, dept, jabatan FROM karyawan WHERE nik = ?";
        $stmt_karyawan = $conn->prepare($query_karyawan);
        $stmt_karyawan->bind_param("s", $nik);
        $stmt_karyawan->execute();
        $result_karyawan = $stmt_karyawan->get_result();

        if ($result_karyawan->num_rows > 0) {
            $karyawan = $result_karyawan->fetch_assoc();
            $karyawan_id = $karyawan['id'];
            $nama = $karyawan['nama'];
            $card_number = $karyawan['card_number'];

            $query_check = "SELECT id, check_in, check_out FROM offline_training_attendance
                           WHERE offline_training_id = ? AND (karyawan_id = ? OR nik = ?)";
            $stmt_check = $conn->prepare($query_check);
            $stmt_check->bind_param("iis", $training_id, $karyawan_id, $nik);
            $stmt_check->execute();
            $result_check = $stmt_check->get_result();

            if ($result_check->num_rows > 0) {
                $attendance = $result_check->fetch_assoc();
                $attendance_id = $attendance['id'];

                if ($attendance['check_in'] === null) {
                    $query_update = "UPDATE offline_training_attendance
                                    SET check_in = NOW(), status = 'hadir', updated_by = ?
                                    WHERE id = ?";
                    $stmt_update = $conn->prepare($query_update);
                    $stmt_update->bind_param("ii", $_SESSION['user_id'], $attendance_id);

                    if ($stmt_update->execute()) {
                        $success = "Check-in berhasil untuk {$nama} ({$nik})";
                        $response['success'] = true;
                        $response['message'] = $success;
                        $response['employee'] = [
                            'nik' => $karyawan['nik'],
                            'nama' => $karyawan['nama'],
                            'jabatan' => $karyawan['jabatan'] ?? '-',
                            'dept' => $karyawan['dept'] ?? '-'
                        ];
                    } else {
                        $error = "Gagal melakukan check-in: " . $conn->error;
                        $response['message'] = $error;
                    }
                } elseif ($attendance['check_out'] === null) {
                    $query_update = "UPDATE offline_training_attendance
                                    SET check_out = NOW(), updated_by = ?
                                    WHERE id = ?";
                    $stmt_update = $conn->prepare($query_update);
                    $stmt_update->bind_param("ii", $_SESSION['user_id'], $attendance_id);

                    if ($stmt_update->execute()) {
                        $success = "Check-out berhasil untuk {$nama} ({$nik})";
                        $response['success'] = true;
                        $response['message'] = $success;
                        $response['employee'] = [
                            'nik' => $karyawan['nik'],
                            'nama' => $karyawan['nama'],
                            'jabatan' => $karyawan['jabatan'] ?? '-',
                            'dept' => $karyawan['dept'] ?? '-'
                        ];
                    } else {
                        $error = "Gagal melakukan check-out: " . $conn->error;
                        $response['message'] = $error;
                    }
                } else {
                    $message = "Karyawan {$nama} ({$nik}) sudah melakukan check-in dan check-out";
                    $response['success'] = true;
                    $response['message'] = $message;
                    $response['employee'] = [
                        'nik' => $karyawan['nik'],
                        'nama' => $karyawan['nama'],
                        'jabatan' => $karyawan['jabatan'] ?? '-',
                        'dept' => $karyawan['dept'] ?? '-'
                    ];
                }
            } else {
                $query_insert = "INSERT INTO offline_training_attendance
                                (offline_training_id, karyawan_id, nik, card_number, nama, check_in, status, created_by)
                                VALUES (?, ?, ?, ?, ?, NOW(), 'hadir', ?)";
                $stmt_insert = $conn->prepare($query_insert);
                $stmt_insert->bind_param("iisssi", $training_id, $karyawan_id, $nik, $card_number, $nama, $_SESSION['user_id']);

                if ($stmt_insert->execute()) {
                    $success = "Absensi berhasil ditambahkan untuk {$nama} ({$nik})";
                    $response['success'] = true;
                    $response['message'] = $success;
                    $response['employee'] = [
                        'nik' => $karyawan['nik'],
                        'nama' => $karyawan['nama'],
                        'jabatan' => $karyawan['jabatan'] ?? '-',
                        'dept' => $karyawan['dept'] ?? '-'
                    ];
                } else {
                    $error = "Gagal menambahkan absensi: " . $conn->error;
                    $response['message'] = $error;
                }
            }
        } else {
            $error = "NIK tidak terdaftar";
            $response['message'] = $error;
        }
    } else {
        $error = "Data tidak lengkap";
        $response['message'] = $error;
    }

    // Jika request AJAX, kembalikan respons JSON
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }

    // Jika bukan request AJAX, tetapi ada respons, tampilkan notifikasi
    if (!empty($response['message'])) {
        if ($response['success']) {
            $success = $response['message'];
        } else {
            $error = $response['message'];
        }
    }
}

// Ambil data absensi jika training dipilih
$attendance_data = [];
if (isset($_GET['training_id']) && !empty($_GET['training_id'])) {
    $training_id = $_GET['training_id'];

    $query_attendance = "SELECT ota.id, ota.nik, ota.nama, ota.check_in, ota.check_out, ota.status, ota.keterangan,
                         k.dept, k.bagian, k.jabatan, DATE(ota.check_in) as attendance_date
                         FROM offline_training_attendance ota
                         LEFT JOIN karyawan k ON ota.karyawan_id = k.id
                         WHERE ota.offline_training_id = ?
                         ORDER BY ota.check_in DESC, ota.nama ASC";
    $stmt_attendance = $conn->prepare($query_attendance);
    $stmt_attendance->bind_param("i", $training_id);
    $stmt_attendance->execute();
    $result_attendance = $stmt_attendance->get_result();

    while ($row = $result_attendance->fetch_assoc()) {
        $attendance_data[] = $row;
    }
}

// Judul halaman
$page_title = "Absensi RFID Training Internal";
include 'header.php';

// Tambahkan CSS dan JS khusus untuk halaman ini
echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">';
echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css">';
echo '<link rel="stylesheet" href="assets/css/offline-training.css">';
?>

<div class="container-fluid">
    <!-- Loading Spinner -->
    <div class="loading-spinner d-none" id="loading_spinner">
        <div class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1" role="dialog" aria-labelledby="successModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="successModalLabel">Absensi Berhasil</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="mb-2"><strong id="modal_message"></strong></p>
                        <div class="card bg-light">
                            <div class="card-body p-3">
                                <p class="card-text mb-1"><strong>NIK:</strong> <span id="modal_nik"></span></p>
                                <p class="card-text mb-1"><strong>Nama:</strong> <span id="modal_nama"></span></p>
                                <p class="card-text mb-1"><strong>Jabatan:</strong> <span id="modal_jabatan"></span></p>
                                <p class="card-text mb-0"><strong>Departemen:</strong> <span id="modal_dept"></span></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>
<!-- Failure Modal -->
<div class="modal fade" id="failureModal" tabindex="-1" role="dialog" aria-labelledby="failureModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="failureModalLabel">Absensi Gagal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-times-circle fa-3x text-danger mb-3"></i>
                    <p class="mb-2"><strong id="failure_modal_message"></strong></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>
    <!-- Existing HTML (unchanged) -->
    <div class="justify-content-between mb-4">
        <h1 class="welcome-section">Absensi RFID Training</h1>
        <a href="training_management.php?training_type=internal" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left"></i> Kembali ke Daftar Training
        </a>
        <div>
                            <button id="refresh_data" class="btn btn-info btn-sm">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button id="fullscreen_btn" class="btn btn-success btn-sm">
                                <i class="fas fa-expand"></i> Full Screen
                            </button>
                        </div>
    </div>

    <!-- Notification Area -->
    <div id="notification_area" class="mb-4">
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (!empty($message)): ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
    </div>

    <div class="row">
        <div class="col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="">Absensi Training Internal</h6>
                </div>
                <div class="card-body">
                    <!-- Pilih Training (Satu untuk semua) -->
                    <div class="form-group">
                        <label for="training_id">Pilih Training:</label>
                        <select class="form-control select2-dropdown" id="training_id" name="training_id" required>
                            <option value="">-- Pilih Training --</option>
                            <?php if ($result_training->num_rows > 0): ?>
                                <?php while ($row = $result_training->fetch_assoc()): ?>
                                    <option value="<?php echo $row['id']; ?>" <?php echo (isset($_GET['training_id']) && $_GET['training_id'] == $row['id']) ? 'selected' : ''; ?>>
                                        <?php
                                        $training_display = htmlspecialchars($row['training_topic']);

                                        // Format tanggal untuk training multi-hari
                                        if (empty($row['end_date']) || $row['start_date'] == $row['end_date']) {
                                            // Training satu hari
                                            $training_display .= ' (' . date('d-m-Y', strtotime($row['start_date'])) . ')';
                                        } else {
                                            // Training multi-hari
                                            $training_display .= ' (' . date('d-m-Y', strtotime($row['start_date'])) . ' - ' . date('d-m-Y', strtotime($row['end_date'])) . ')';
                                        }

                                        echo $training_display;
                                        ?>
                                    </option>
                                <?php endwhile; ?>
                            <?php endif; ?>
                        </select>
                        <small class="form-text text-muted">Pilih training untuk mencatat absensi</small>
                    </div>

                    <!-- Mode RFID Setting -->
                    <div class="form-group">
                        <label for="rfid_mode">Mode RFID:</label>
                        <select class="form-control" id="rfid_mode" name="rfid_mode">
                            <option value="auto">Auto (Check-in → Check-out)</option>
                            <option value="checkin_only">Hanya Check-in</option>
                        </select>
                        <small class="form-text text-muted">
                            <strong>Auto:</strong> Tap pertama = Check-in, Tap kedua = Check-out<br>
                            <strong>Hanya Check-in:</strong> Setiap tap = Check-in baru
                        </small>
                    </div>

                    <!-- Tab untuk memilih metode absensi -->
                    <ul class="nav nav-tabs mb-3" id="attendanceTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link active" id="rfid-tab" data-toggle="tab" href="#rfid-content" role="tab" aria-controls="rfid-content" aria-selected="true">
                                <i class="fas fa-id-card mr-1"></i> Scan RFID
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="manual-tab" data-toggle="tab" href="#manual-content" role="tab" aria-controls="manual-content" aria-selected="false">
                                <i class="fas fa-user mr-1"></i> Input Manual (NIK)
                            </a>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="attendanceTabContent">
                        <!-- RFID Tab -->
                        <div class="tab-pane fade show active" id="rfid-content" role="tabpanel" aria-labelledby="rfid-tab">
                            <form method="post" action="" id="rfid_form">
                                <input type="hidden" name="training_id" id="rfid_training_id">
                                <input type="hidden" name="rfid_mode" id="rfid_mode_hidden">
                                <div class="form-group">
                                    <label for="card_number">Nomor Kartu RFID:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="card_number" name="card_number" placeholder="Scan kartu RFID" autofocus>
                                        <div class="input-group-append">
                                            <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">Scan kartu RFID atau masukkan nomor kartu secara manual</small>
                                </div>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-check-circle"></i> Proses Absensi
                                </button>
                            </form>
                        </div>

                        <!-- Manual Tab -->
                        <div class="tab-pane fade" id="manual-content" role="tabpanel" aria-labelledby="manual-tab">
                            <form method="post" action="" id="manual_form">
                                <input type="hidden" name="training_id" id="manual_training_id">
                                <div class="form-group position-relative">
                                    <label for="manual_nik">Cari Karyawan:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="employee_search" placeholder="Cari berdasarkan NIK atau nama">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        </div>
                                    </div>
                                    <input type="hidden" id="manual_nik" name="manual_nik">
                                    <div id="employee_results" class="employee-results d-none"></div>
                                    <small class="form-text text-muted">Ketik minimal 2 karakter untuk mencari karyawan</small>
                                </div>
                                <div id="selected_employee" class="mb-3 d-none">
                                    <div class="card bg-light">
                                        <div class="card-body p-3">
                                            <h6 class="card-title mb-1" id="selected_employee_name">-</h6>
                                            <p class="card-text mb-0">
                                                <small>NIK: <span id="selected_employee_nik">-</span></small><br>
                                                <small>Departemen: <span id="selected_employee_dept">-</span></small>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-check-circle"></i> Proses Absensi
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="">Daftar Kehadiran</h6>
                    <?php if (isset($_GET['training_id']) && !empty($_GET['training_id'])): ?>
                        <div>
                            <button id="refresh_data" class="btn btn-info btn-sm">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button id="fullscreen_btn" class="btn btn-success btn-sm">
                                <i class="fas fa-expand"></i> Full Screen
                            </button>
                            <a href="view_offline_attendance.php?id=<?php echo $_GET['training_id']; ?>" class="btn btn-primary btn-sm" role="button" aria-label="Lihat detail lengkap absensi">
                                <i class="fas fa-list" aria-hidden="true"></i> Detail Lengkap
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <!-- Attendance data will be loaded here -->
                    <div class="attendance-table-container">
                        <?php if (isset($_GET['training_id']) && !empty($_GET['training_id'])): ?>
                            <?php if (count($attendance_data) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover" id="attendance_table" style="width: 100%; table-layout: fixed;">
                                        <colgroup>
                                            <col style="width: 5%"> <!-- No -->
                                            <col style="width: 12%"> <!-- NIK -->
                                            <col style="width: 20%"> <!-- Nama -->
                                            <col style="width: 15%"> <!-- Departemen -->
                                            <col style="width: 12%"> <!-- Tanggal -->
                                            <col style="width: 18%"> <!-- Check In -->
                                            <col style="width: 18%"> <!-- Check Out -->
                                        </colgroup>
                                        <thead class="bg-light">
                                            <tr>
                                                <th class="text-center">No</th>
                                                <th>NIK</th>
                                                <th>Nama</th>
                                                <th>Departemen</th>
                                                <th>Tanggal</th>
                                                <th>Check In</th>
                                                <th class="checkout-column">Check Out</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $no = 1; foreach ($attendance_data as $attendance): ?>
                                                <tr>
                                                    <td class="text-center"><?php echo $no++; ?></td>
                                                    <td><?php echo $attendance['nik']; ?></td>
                                                    <td><?php echo $attendance['nama']; ?></td>
                                                    <td><?php echo isset($attendance['dept']) ? $attendance['dept'] : '-'; ?></td>
                                                    <td><?php echo $attendance['check_in'] ? date('d-m-Y', strtotime($attendance['check_in'])) : '-'; ?></td>
                                                    <td><?php echo $attendance['check_in'] ? date('H:i', strtotime($attendance['check_in'])) : '-'; ?></td>
                                                    <td class="checkout-column"><?php echo $attendance['check_out'] ? date('H:i', strtotime($attendance['check_out'])) : '-'; ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                                    <p class="text-gray-500 mb-0">Belum ada data absensi</p>
                                    <p class="text-gray-500">Scan kartu RFID atau masukkan NIK karyawan untuk mencatat absensi</p>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                                <p class="text-gray-500 mb-0">Pilih training untuk melihat data absensi</p>
                                <p class="text-gray-500">Silakan pilih training dari dropdown di sebelah kiri</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Full Screen Modal (unchanged, included for completeness) -->
<div class="modal fade" id="fullscreenModal" tabindex="-1" role="dialog" aria-labelledby="fullscreenModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fullscreenModalLabel">Absensi Training</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Absensi Section -->
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="">Absensi Training Internal</h6>
                            </div>
                            <div class="card-body">
                                <!-- Pilih Training (Satu untuk semua) -->
                                <div class="form-group">
                                    <label for="fs_training_id">Pilih Training:</label>
                                    <select class="form-control select2-dropdown" id="fs_training_id" name="fs_training_id" required>
                                        <option value="">-- Pilih Training --</option>
                                        <?php if ($result_training->num_rows > 0):
                                            $result_training->data_seek(0); // Reset pointer
                                            while ($row = $result_training->fetch_assoc()): ?>
                                                <option value="<?php echo $row['id']; ?>" <?php echo (isset($_GET['training_id']) && $_GET['training_id'] == $row['id']) ? 'selected' : ''; ?>>
                                                    <?php
                                                    $training_display = htmlspecialchars($row['training_topic']);

                                                    // Format tanggal untuk training multi-hari
                                                    if (empty($row['end_date']) || $row['start_date'] == $row['end_date']) {
                                                        // Training satu hari
                                                        $training_display .= ' (' . date('d-m-Y', strtotime($row['start_date'])) . ')';
                                                    } else {
                                                        // Training multi-hari
                                                        $training_display .= ' (' . date('d-m-Y', strtotime($row['start_date'])) . ' - ' . date('d-m-Y', strtotime($row['end_date'])) . ')';
                                                    }

                                                    echo $training_display;
                                                    ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                    <small class="form-text text-muted">Pilih training untuk mencatat absensi</small>
                                </div>

                                <!-- Tab untuk memilih metode absensi -->
                                <ul class="nav nav-tabs mb-3" id="fsAttendanceTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link active" id="fs-rfid-tab" data-toggle="tab" href="#fs-rfid-content" role="tab" aria-controls="fs-rfid-content" aria-selected="true">
                                            <i class="fas fa-id-card mr-1"></i> Scan RFID
                                        </a>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link" id="fs-manual-tab" data-toggle="tab" href="#fs-manual-content" role="tab" aria-controls="fs-manual-content" aria-selected="false">
                                            <i class="fas fa-user mr-1"></i> Input Manual (NIK)
                                        </a>
                                    </li>
                                </ul>

                                <!-- Tab Content -->
                                <div class="tab-content" id="fsAttendanceTabContent">
                                    <!-- RFID Tab -->
                                    <div class="tab-pane fade show active" id="fs-rfid-content" role="tabpanel" aria-labelledby="fs-rfid-tab">
                                        <form method="post" action="" id="fs_rfid_form">
                                            <input type="hidden" name="training_id" id="fs_rfid_training_id">
                                            <div class="form-group">
                                                <label for="fs_card_number">Nomor Kartu RFID:</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" id="fs_card_number" name="card_number" placeholder="Scan kartu RFID" autofocus>
                                                    <div class="input-group-append">
                                                        <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                                    </div>
                                                </div>
                                                <small class="form-text text-muted">Scan kartu RFID atau masukkan nomor kartu secara manual</small>
                                            </div>
                                            <button type="submit" class="btn btn-primary btn-block">
                                                <i class="fas fa-check-circle"></i> Proses Absensi
                                            </button>
                                        </form>
                                    </div>

                                    <!-- Manual Tab -->
                                    <div class="tab-pane fade" id="fs-manual-content" role="tabpanel" aria-labelledby="fs-manual-tab">
                                        <form method="post" action="" id="fs_manual_form">
                                            <input type="hidden" name="training_id" id="fs_manual_training_id">
                                            <div class="form-group position-relative">
                                                <label for="fs_employee_search">Cari Karyawan:</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" id="fs_employee_search" placeholder="Cari berdasarkan NIK atau nama">
                                                    <div class="input-group-append">
                                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                                    </div>
                                                </div>
                                                <input type="hidden" id="fs_manual_nik" name="manual_nik">
                                                <div id="fs_employee_results" class="employee-results d-none"></div>
                                                <small class="form-text text-muted">Ketik minimal 2 karakter untuk mencari karyawan</small>
                                            </div>
                                            <div id="fs_selected_employee" class="mb-3 d-none">
                                                <div class="card bg-light">
                                                    <div class="card-body p-3">
                                                        <h6 class="card-title mb-1" id="fs_selected_employee_name">-</h6>
                                                        <p class="card-text mb-0">
                                                            <small>NIK: <span id="fs_selected_employee_nik">-</span></small><br>
                                                            <small>Departemen: <span id="fs_selected_employee_dept">-</span></small>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="submit" class="btn btn-primary btn-block">
                                                <i class="fas fa-check-circle"></i> Proses Absensi
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Daftar Kehadiran Section -->
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                                <h6 class="">Daftar Kehadiran</h6>
                                <div>
                                    <button id="fs_refresh_data" class="btn btn-info btn-sm">
                                        <i class="fas fa-sync-alt"></i> Refresh
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" data-bs-dismiss="modal">
                                        <i class="fas fa-compress"></i> Exit Full Screen
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Attendance data will be loaded here -->
                                <div class="fs-attendance-table-container">
                                    <div class="text-center py-5">
                                        <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                                        <p class="text-gray-500 mb-0">Pilih training untuk melihat data absensi</p>
                                        <p class="text-gray-500">Silakan pilih training dari dropdown di sebelah kiri</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<style>
    /* Full screen modal styles */
    .modal-fullscreen {
        width: 100vw;
        max-width: none;
        height: 100vh;
        margin: 0;
        overflow: hidden;
    }

    .modal-fullscreen .modal-content {
        height: 100%;
        border: 0;
        border-radius: 0;
    }

    .modal-fullscreen .modal-body {
        overflow: hidden;
        padding: 1rem;
    }

    #fullscreenModal .card {
        height: calc(100vh - 150px);
    }

    #fullscreenModal .card-body {
        overflow-y: auto;
        max-height: calc(100% - 50px);
    }

    .fs-attendance-table-container {
        overflow-y: auto;
        max-height: calc(100vh - 220px);
    }

    .loading-spinner {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner .spinner-border {
        width: 3rem;
        height: 3rem;
        color: #fff;
    }

    .employee-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        max-height: 300px;
        overflow-y: auto;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
</style>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Select2
        if (typeof $.fn.select2 !== 'undefined') {
            $('.select2-dropdown').select2({
                theme: 'bootstrap4',
                placeholder: 'Pilih opsi...',
                width: '100%'
            }).on('select2:select', function(e) {
                this.dispatchEvent(new Event('change'));
            });
        }

        // Handle training selection change
        const trainingSelect = document.getElementById('training_id');
        const rfidTrainingIdInput = document.getElementById('rfid_training_id');
        const manualTrainingIdInput = document.getElementById('manual_training_id');

        // Handle RFID mode change
        const rfidModeSelect = document.getElementById('rfid_mode');
        const rfidModeHidden = document.getElementById('rfid_mode_hidden');

        if (rfidModeSelect && rfidModeHidden) {
            rfidModeSelect.addEventListener('change', function() {
                rfidModeHidden.value = this.value;

                // Update button text based on mode
                const submitButton = document.querySelector('#rfid_form button[type="submit"]');
                const checkoutColumns = document.querySelectorAll('.checkout-column');

                if (this.value === 'checkin_only') {
                    submitButton.innerHTML = '<i class="fas fa-sign-in-alt"></i> Check-in';
                    submitButton.className = 'btn btn-success btn-block';

                    // Hide checkout columns
                    checkoutColumns.forEach(col => {
                        col.style.display = 'none';
                    });
                } else {
                    submitButton.innerHTML = '<i class="fas fa-check-circle"></i> Proses Absensi';
                    submitButton.className = 'btn btn-primary btn-block';

                    // Show checkout columns
                    checkoutColumns.forEach(col => {
                        col.style.display = '';
                    });
                }
            });

            // Set initial value and trigger change
            rfidModeHidden.value = rfidModeSelect.value;
            rfidModeSelect.dispatchEvent(new Event('change'));
        }

        if (trainingSelect) {
            $(trainingSelect).on('change', function() {
                if (this.value !== '') {
                    if (rfidTrainingIdInput) rfidTrainingIdInput.value = this.value;
                    if (manualTrainingIdInput) manualTrainingIdInput.value = this.value;
                    loadAttendanceData(this.value);
                    window.history.pushState({}, '', 'rfid_attendance_offline.php?training_id=' + this.value);
                }
            });

            if (trainingSelect.value !== '') {
                if (rfidTrainingIdInput) rfidTrainingIdInput.value = trainingSelect.value;
                if (manualTrainingIdInput) manualTrainingIdInput.value = trainingSelect.value;
            }

            const urlParams = new URLSearchParams(window.location.search);
            const trainingIdParam = urlParams.get('training_id');
            if (trainingIdParam) {
                if (trainingSelect.value !== trainingIdParam) {
                    trainingSelect.value = trainingIdParam;
                    if (typeof $.fn.select2 !== 'undefined') {
                        $(trainingSelect).trigger('change.select2');
                    }
                }
                if (rfidTrainingIdInput) rfidTrainingIdInput.value = trainingIdParam;
                if (manualTrainingIdInput) manualTrainingIdInput.value = trainingIdParam;
                document.getElementById('rfid_form').action = 'rfid_attendance_offline.php?training_id=' + trainingIdParam;
                document.getElementById('manual_form').action = 'rfid_attendance_offline.php?training_id=' + trainingIdParam;
            }
        }

        // Function to restore focus to an input
        function restoreFocus(inputId) {
            setTimeout(() => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.focus();
                }
            }, 100); // Small delay to ensure DOM updates are complete
        }

        // Handle RFID form submission via AJAX
        $('#rfid_form').on('submit', function(e) {
            e.preventDefault();
            const trainingId = $('#rfid_training_id').val();
            const cardNumber = $('#card_number').val();
            const rfidMode = $('#rfid_mode_hidden').val();

            if (!trainingId) {
                showNotification('error', 'Silakan pilih training terlebih dahulu');
                restoreFocus('card_number');
                return false;
            }

            if (!cardNumber) {
                showNotification('error', 'Silakan scan kartu RFID');
                restoreFocus('card_number');
                return false;
            }

            $('#loading_spinner').removeClass('d-none');

            $.ajax({
                url: 'rfid_attendance_offline.php',
                type: 'POST',
                data: {
                    training_id: trainingId,
                    card_number: cardNumber,
                    rfid_mode: rfidMode
                },
                dataType: 'json',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    $('#loading_spinner').addClass('d-none');

                    if (response.success) {
                        showNotification('success', response.message);
                        if (response.employee) {
                            $('#modal_message').text(response.message);
                            $('#modal_nik').text(response.employee.nik);
                            $('#modal_nama').text(response.employee.nama);
                            $('#modal_jabatan').text(response.employee.jabatan);
                            $('#modal_dept').text(response.employee.dept);
                            const successModal = new bootstrap.Modal(document.getElementById('successModal'), { focus: false });
                            successModal.show();
                            setTimeout(() => {
                                successModal.hide();
                                restoreFocus('card_number');
                            }, 3000);
                        } else {
                            restoreFocus('card_number');
                        }
                        loadAttendanceData(trainingId);
                    } else {
                        $('#failure_modal_message').text(response.message);
                        const failureModal = new bootstrap.Modal(document.getElementById('failureModal'), { focus: false });
                        failureModal.show();
                        setTimeout(() => {
                            failureModal.hide();
                            restoreFocus('card_number');
                        }, 3000);
                    }

                    $('#card_number').val('');
                    restoreFocus('card_number');
                },
                error: function(xhr, status, error) {
                    $('#loading_spinner').addClass('d-none');
                    $('#failure_modal_message').text('Terjadi kesalahan: ' + error);
                    const failureModal = new bootstrap.Modal(document.getElementById('failureModal'), { focus: false });
                    failureModal.show();
                    setTimeout(() => {
                        failureModal.hide();
                        restoreFocus('card_number');
                    }, 3000);
                    $('#card_number').val('');
                    restoreFocus('card_number');
                }
            });
        });

        // Handle manual form submission via AJAX
        $('#manual_form').on('submit', function(e) {
            e.preventDefault();
            const trainingId = $('#manual_training_id').val();
            const nik = $('#manual_nik').val();

            if (!trainingId) {
                showNotification('error', 'Silakan pilih training terlebih dahulu');
                restoreFocus('employee_search');
                return false;
            }

            if (!nik) {
                showNotification('error', 'Silakan pilih karyawan terlebih dahulu');
                restoreFocus('employee_search');
                return false;
            }

            $('#loading_spinner').removeClass('d-none');

            $.ajax({
                url: 'rfid_attendance_offline.php',
                type: 'POST',
                data: {
                    training_id: trainingId,
                    manual_nik: nik
                },
                dataType: 'json',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    $('#loading_spinner').addClass('d-none');

                    if (response.success) {
                        showNotification('success', response.message);
                        if (response.employee) {
                            $('#modal_message').text(response.message);
                            $('#modal_nik').text(response.employee.nik);
                            $('#modal_nama').text(response.employee.nama);
                            $('#modal_jabatan').text(response.employee.jabatan);
                            $('#modal_dept').text(response.employee.dept);
                            const successModal = new bootstrap.Modal(document.getElementById('successModal'), { focus: false });
                            successModal.show();
                            setTimeout(() => {
                                successModal.hide();
                                restoreFocus('employee_search');
                            }, 3000);
                        } else {
                            restoreFocus('employee_search');
                        }
                        loadAttendanceData(trainingId);
                    } else {
                        $('#failure_modal_message').text(response.message);
                        const failureModal = new bootstrap.Modal(document.getElementById('failureModal'), { focus: false });
                        failureModal.show();
                        setTimeout(() => {
                            failureModal.hide();
                            restoreFocus('employee_search');
                        }, 3000);
                    }

                    $('#employee_search').val('');
                    $('#manual_nik').val('');
                    $('#selected_employee').addClass('d-none');
                    restoreFocus('employee_search');
                },
                error: function(xhr, status, error) {
                    $('#loading_spinner').addClass('d-none');
                    $('#failure_modal_message').text('Terjadi kesalahan: ' + error);
                    const failureModal = new bootstrap.Modal(document.getElementById('failureModal'), { focus: false });
                    failureModal.show();
                    setTimeout(() => {
                        failureModal.hide();
                        restoreFocus('employee_search');
                    }, 3000);
                    $('#employee_search').val('');
                    $('#manual_nik').val('');
                    $('#selected_employee').addClass('d-none');
                    restoreFocus('employee_search');
                }
            });
        });

        // Handle tabs
        document.querySelectorAll('#attendanceTab .nav-link').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();

                document.querySelectorAll('#attendanceTab .nav-link').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('#attendanceTabContent .tab-pane').forEach(p => {
                    p.classList.remove('show');
                    p.classList.remove('active');
                });

                this.classList.add('active');

                const target = this.getAttribute('href');
                const targetPane = document.querySelector(target);

                if (targetPane) {
                    targetPane.classList.add('show');
                    targetPane.classList.add('active');

                    if (target === '#rfid-content') {
                        restoreFocus('card_number');
                    } else if (target === '#manual-content') {
                        restoreFocus('employee_search');
                    }
                }
            });
        });

        // Handle refresh button click
        document.getElementById('refresh_data')?.addEventListener('click', function() {
            const trainingId = trainingSelect.value;
            if (trainingId) {
                loadAttendanceData(trainingId);
            } else {
                window.location.reload();
            }
            // Restore focus based on active tab
            const activeTab = document.querySelector('#attendanceTab .nav-link.active');
            if (activeTab && activeTab.getAttribute('href') === '#rfid-content') {
                restoreFocus('card_number');
            } else {
                restoreFocus('employee_search');
            }
        });

        // Handle fullscreen button click
        document.getElementById('fullscreen_btn')?.addEventListener('click', function() {
            const trainingId = document.getElementById('training_id').value;

            document.getElementById('fs_training_id').value = trainingId;
            document.getElementById('fs_rfid_training_id').value = trainingId;
            document.getElementById('fs_manual_training_id').value = trainingId;

            if (typeof $.fn.select2 !== 'undefined') {
                $('#fs_training_id').select2({
                    theme: 'bootstrap4',
                    placeholder: 'Pilih opsi...',
                    width: '100%',
                    dropdownParent: $('#fullscreenModal')
                });
            }

            if (trainingId) {
                loadFullscreenAttendanceData(trainingId);
            }

            const fullscreenModal = new bootstrap.Modal(document.getElementById('fullscreenModal'), { focus: false });
            fullscreenModal.show();

            disableKeyboardForFullscreen();

            restoreFocus('fs_card_number');
        });

        // Handle fullscreen refresh button click
        document.getElementById('fs_refresh_data')?.addEventListener('click', function() {
            const trainingId = document.getElementById('fs_training_id').value;
            if (trainingId) {
                loadFullscreenAttendanceData(trainingId);
            }
            // Restore focus based on active tab
            const activeTab = document.querySelector('#fsAttendanceTab .nav-link.active');
            if (activeTab && activeTab.getAttribute('href') === '#fs-rfid-content') {
                restoreFocus('fs_card_number');
            } else {
                restoreFocus('fs_employee_search');
            }
        });

        // Handle fullscreen training selection change
        const fsTrainingSelect = document.getElementById('fs_training_id');
        if (fsTrainingSelect) {
            $(fsTrainingSelect).on('change', function() {
                if (this.value !== '') {
                    document.getElementById('fs_rfid_training_id').value = this.value;
                    document.getElementById('fs_manual_training_id').value = this.value;
                    loadFullscreenAttendanceData(this.value);
                }
                // Restore focus based on active tab
                const activeTab = document.querySelector('#fsAttendanceTab .nav-link.active');
                if (activeTab && activeTab.getAttribute('href') === '#fs-rfid-content') {
                    restoreFocus('fs_card_number');
                } else {
                    restoreFocus('fs_employee_search');
                }
            });
        }

        // Handle fullscreen RFID form submission
        $('#fs_rfid_form').on('submit', function(e) {
            e.preventDefault();
            const trainingId = $('#fs_rfid_training_id').val();
            const cardNumber = $('#fs_card_number').val();

            if (!trainingId) {
                showNotification('error', 'Silakan pilih training terlebih dahulu');
                restoreFocus('fs_card_number');
                return false;
            }

            if (!cardNumber) {
                showNotification('error', 'Silakan scan kartu RFID');
                restoreFocus('fs_card_number');
                return false;
            }

            $('#loading_spinner').removeClass('d-none');

            $.ajax({
                url: 'rfid_attendance_offline.php',
                type: 'POST',
                data: {
                    training_id: trainingId,
                    card_number: cardNumber
                },
                dataType: 'json',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    $('#loading_spinner').addClass('d-none');

                    if (response.success) {
                        showNotification('success', response.message);
                        if (response.employee) {
                            $('#modal_message').text(response.message);
                            $('#modal_nik').text(response.employee.nik);
                            $('#modal_nama').text(response.employee.nama);
                            $('#modal_jabatan').text(response.employee.jabatan);
                            $('#modal_dept').text(response.employee.dept);
                            const successModal = new bootstrap.Modal(document.getElementById('successModal'), { focus: false });
                            successModal.show();
                            setTimeout(() => {
                                successModal.hide();
                                restoreFocus('fs_card_number');
                            }, 3000);
                        } else {
                            restoreFocus('fs_card_number');
                        }
                        loadFullscreenAttendanceData(trainingId);
                        loadAttendanceData(trainingId);
                    } else {
                        $('#failure_modal_message').text(response.message);
                        const failureModal = new bootstrap.Modal(document.getElementById('failureModal'), { focus: false });
                        failureModal.show();
                        setTimeout(() => {
                            failureModal.hide();
                            restoreFocus('fs_card_number');
                        }, 3000);
                    }

                    $('#fs_card_number').val('');
                    restoreFocus('fs_card_number');
                },
                error: function(xhr, status, error) {
                    $('#loading_spinner').addClass('d-none');
                    $('#failure_modal_message').text('Terjadi kesalahan: ' + error);
                    const failureModal = new bootstrap.Modal(document.getElementById('failureModal'), { focus: false });
                    failureModal.show();
                    setTimeout(() => {
                        failureModal.hide();
                        restoreFocus('fs_card_number');
                    }, 3000);
                    $('#fs_card_number').val('');
                    restoreFocus('fs_card_number');
                }
            });
        });

        // Handle fullscreen manual form submission
        $('#fs_manual_form').on('submit', function(e) {
            e.preventDefault();
            const trainingId = $('#fs_manual_training_id').val();
            const nik = $('#fs_manual_nik').val();

            if (!trainingId) {
                showNotification('error', 'Silakan pilih training terlebih dahulu');
                restoreFocus('fs_employee_search');
                return false;
            }

            if (!nik) {
                showNotification('error', 'Silakan pilih karyawan terlebih dahulu');
                restoreFocus('fs_employee_search');
                return false;
            }

            $('#loading_spinner').removeClass('d-none');

            $.ajax({
                url: 'rfid_attendance_offline.php',
                type: 'POST',
                data: {
                    training_id: trainingId,
                    manual_nik: nik
                },
                dataType: 'json',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    $('#loading_spinner').addClass('d-none');

                    if (response.success) {
                        showNotification('success', response.message);
                        if (response.employee) {
                            $('#modal_message').text(response.message);
                            $('#modal_nik').text(response.employee.nik);
                            $('#modal_nama').text(response.employee.nama);
                            $('#modal_jabatan').text(response.employee.jabatan);
                            $('#modal_dept').text(response.employee.dept);
                            const successModal = new bootstrap.Modal(document.getElementById('successModal'), { focus: false });
                            successModal.show();
                            setTimeout(() => {
                                successModal.hide();
                                restoreFocus('fs_employee_search');
                            }, 3000);
                        } else {
                            restoreFocus('fs_employee_search');
                        }
                        loadFullscreenAttendanceData(trainingId);
                        loadAttendanceData(trainingId);
                    } else {
                        $('#failure_modal_message').text(response.message);
                        const failureModal = new bootstrap.Modal(document.getElementById('failureModal'), { focus: false });
                        failureModal.show();
                        setTimeout(() => {
                            failureModal.hide();
                            restoreFocus('fs_employee_search');
                        }, 3000);
                    }

                    $('#fs_employee_search').val('');
                    $('#fs_manual_nik').val('');
                    $('#fs_selected_employee').addClass('d-none');
                    restoreFocus('fs_employee_search');
                },
                error: function(xhr, status, error) {
                    $('#loading_spinner').addClass('d-none');
                    $('#failure_modal_message').text('Terjadi kesalahan: ' + error);
                    const failureModal = new bootstrap.Modal(document.getElementById('failureModal'), { focus: false });
                    failureModal.show();
                    setTimeout(() => {
                        failureModal.hide();
                        restoreFocus('fs_employee_search');
                    }, 3000);
                    $('#fs_employee_search').val('');
                    $('#fs_manual_nik').val('');
                    $('#fs_selected_employee').addClass('d-none');
                    restoreFocus('fs_employee_search');
                }
            });
        });

        // Handle fullscreen tabs
        document.querySelectorAll('#fsAttendanceTab .nav-link').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();

                document.querySelectorAll('#fsAttendanceTab .nav-link').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('#fsAttendanceTabContent .tab-pane').forEach(p => {
                    p.classList.remove('show');
                    p.classList.remove('active');
                });

                this.classList.add('active');

                const target = this.getAttribute('href');
                const targetPane = document.querySelector(target);

                if (targetPane) {
                    targetPane.classList.add('show');
                    targetPane.classList.add('active');

                    if (target === '#fs-rfid-content') {
                        restoreFocus('fs_card_number');
                    } else if (target === '#fs-manual-content') {
                        restoreFocus('fs_employee_search');
                    }
                }
            });
        });

        // Handle fullscreen employee search
        document.getElementById('fs_employee_search')?.addEventListener('input', function() {
            const searchTerm = this.value.trim();
            const employeeResults = document.getElementById('fs_employee_results');
            const selectedEmployee = document.getElementById('fs_selected_employee');
            const manualNikInput = document.getElementById('fs_manual_nik');

            if (searchTerm.length >= 2) {
                document.getElementById('loading_spinner')?.classList.remove('d-none');

                fetch(`search_employees.php?search=${encodeURIComponent(searchTerm)}&limit=5`)
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('loading_spinner')?.classList.add('d-none');

                        if (data.success && employeeResults) {
                            if (data.employees.length === 0) {
                                employeeResults.innerHTML = '<div class="p-2 text-center text-muted">Tidak ada karyawan yang ditemukan</div>';
                            } else {
                                let html = '<div class="list-group">';

                                data.employees.forEach(employee => {
                                    html += `
                                        <a href="#" class="list-group-item list-group-item-action"
                                           onclick="selectFullscreenEmployee('${employee.nik}', '${employee.nama}', '${employee.dept}'); return false;">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>${employee.nik}</strong> - ${employee.nama}
                                                </div>
                                                <span class="badge badge-primary badge-pill">${employee.dept}</span>
                                            </div>
                                        </a>
                                    `;
                                });

                                html += '</div>';
                                employeeResults.innerHTML = html;
                            }

                            employeeResults.classList.remove('d-none');
                        }
                        restoreFocus('fs_employee_search');
                    })
                    .catch(error => {
                        console.error('Error searching employees:', error);
                        document.getElementById('loading_spinner')?.classList.add('d-none');
                        restoreFocus('fs_employee_search');
                    });
            } else {
                if (employeeResults) {
                    employeeResults.innerHTML = '';
                    employeeResults.classList.add('d-none');
                }
                restoreFocus('fs_employee_search');
            }
        });

        // Handle employee search
        document.getElementById('employee_search')?.addEventListener('input', function() {
            const searchTerm = this.value.trim();
            const employeeResults = document.getElementById('employee_results');
            const selectedEmployee = document.getElementById('selected_employee');
            const manualNikInput = document.getElementById('manual_nik');

            if (searchTerm.length >= 2) {
                document.getElementById('loading_spinner')?.classList.remove('d-none');

                fetch(`search_employees.php?search=${encodeURIComponent(searchTerm)}&limit=5`)
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('loading_spinner')?.classList.add('d-none');

                        if (data.success && employeeResults) {
                            if (data.employees.length === 0) {
                                employeeResults.innerHTML = '<div class="p-2 text-center text-muted">Tidak ada karyawan yang ditemukan</div>';
                            } else {
                                let html = '<div class="list-group">';

                                data.employees.forEach(employee => {
                                    html += `
                                        <a href="#" class="list-group-item list-group-item-action"
                                           onclick="selectEmployee('${employee.nik}', '${employee.nama}', '${employee.dept}'); return false;">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>${employee.nik}</strong> - ${employee.nama}
                                                </div>
                                                <span class="badge badge-primary badge-pill">${employee.dept}</span>
                                            </div>
                                        </a>
                                    `;
                                });

                                html += '</div>';
                                employeeResults.innerHTML = html;
                            }

                            employeeResults.classList.remove('d-none');
                        }
                        restoreFocus('employee_search');
                    })
                    .catch(error => {
                        console.error('Error searching employees:', error);
                        document.getElementById('loading_spinner')?.classList.add('d-none');
                        restoreFocus('employee_search');
                    });
            } else {
                if (employeeResults) {
                    employeeResults.innerHTML = '';
                    employeeResults.classList.add('d-none');
                }
                restoreFocus('employee_search');
            }
        });
    });

    function selectEmployee(nik, nama, dept) {
        const employeeResults = document.getElementById('employee_results');
        const selectedEmployee = document.getElementById('selected_employee');
        const manualNikInput = document.getElementById('manual_nik');
        const employeeSearch = document.getElementById('employee_search');

        if (manualNikInput) manualNikInput.value = nik;
        if (employeeSearch) employeeSearch.value = nik + ' - ' + nama;

        if (selectedEmployee) {
            document.getElementById('selected_employee_name').textContent = nama;
            document.getElementById('selected_employee_nik').textContent = nik;
            document.getElementById('selected_employee_dept').textContent = dept;
            selectedEmployee.classList.remove('d-none');
        }

        if (employeeResults) {
            employeeResults.innerHTML = '';
            employeeResults.classList.add('d-none');
        }
        restoreFocus('employee_search');
    }

    function loadAttendanceData(trainingId) {
        if (!trainingId) return;

        $('#loading_spinner').removeClass('d-none');

        $.ajax({
            url: 'get_attendance_data.php',
            type: 'GET',
            data: { training_id: trainingId },
            dataType: 'html',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                $('#loading_spinner').addClass('d-none');
                $('.attendance-table-container').html(response);
                // Restore focus based on active tab
                const activeTab = document.querySelector('#attendanceTab .nav-link.active');
                if (activeTab && activeTab.getAttribute('href') === '#rfid-content') {
                    restoreFocus('card_number');
                } else {
                    restoreFocus('employee_search');
                }
            },
            error: function(xhr, status, error) {
                $('#loading_spinner').addClass('d-none');
                showNotification('error', 'Gagal memuat data kehadiran: ' + error);
                // Restore focus based on active tab
                const activeTab = document.querySelector('#attendanceTab .nav-link.active');
                if (activeTab && activeTab.getAttribute('href') === '#rfid-content') {
                    restoreFocus('card_number');
                } else {
                    restoreFocus('employee_search');
                }
            }
        });
    }

    function loadFullscreenAttendanceData(trainingId) {
        if (!trainingId) return;

        $('#loading_spinner').removeClass('d-none');

        $.ajax({
            url: 'get_attendance_data.php',
            type: 'GET',
            data: { training_id: trainingId }, // Fixed typo: trustyId to trainingId
            dataType: 'html',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                $('#loading_spinner').addClass('d-none');
                $('.fs-attendance-table-container').html(response);
                // Restore focus based on active tab
                const activeTab = document.querySelector('#fsAttendanceTab .nav-link.active');
                if (activeTab && activeTab.getAttribute('href') === '#fs-rfid-content') {
                    restoreFocus('fs_card_number');
                } else {
                    restoreFocus('fs_employee_search');
                }
            },
            error: function(xhr, status, error) {
                $('#loading_spinner').addClass('d-none');
                showNotification('error', 'Gagal memuat data kehadiran: ' + error);
                // Restore focus based on active tab
                const activeTab = document.querySelector('#fsAttendanceTab .nav-link.active');
                if (activeTab && activeTab.getAttribute('href') === '#fs-rfid-content') {
                    restoreFocus('fs_card_number');
                } else {
                    restoreFocus('fs_employee_search');
                }
            }
        });
    }

    function showNotification(type, message) {
        let alertClass = 'alert-info';
        if (type === 'success') alertClass = 'alert-success';
        if (type === 'error') alertClass = 'alert-danger';

        const notification = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        $('#notification_area').html(notification);

        setTimeout(function() {
            $('.alert').alert('close');
            // Restore focus based on active tab
            const activeTab = document.querySelector('#attendanceTab .nav-link.active') || document.querySelector('#fsAttendanceTab .nav-link.active');
            if (activeTab) {
                if (activeTab.getAttribute('href') === '#rfid-content') {
                    restoreFocus('card_number');
                } else if (activeTab.getAttribute('href') === '#manual-content') {
                    restoreFocus('employee_search');
                } else if (activeTab.getAttribute('href') === '#fs-rfid-content') {
                    restoreFocus('fs_card_number');
                } else if (activeTab.getAttribute('href') === '#fs-manual-content') {
                    restoreFocus('fs_employee_search');
                }
            }
        }, 5000);
    }

    function selectFullscreenEmployee(nik, nama, dept) {
        const employeeResults = document.getElementById('fs_employee_results');
        const selectedEmployee = document.getElementById('fs_selected_employee');
        const manualNikInput = document.getElementById('fs_manual_nik');
        const employeeSearch = document.getElementById('fs_employee_search');

        if (manualNikInput) manualNikInput.value = nik;
        if (employeeSearch) employeeSearch.value = nik + ' - ' + nama;

        if (selectedEmployee) {
            document.getElementById('fs_selected_employee_name').textContent = nama;
            document.getElementById('fs_selected_employee_nik').textContent = nik;
            document.getElementById('fs_selected_employee_dept').textContent = dept;
            selectedEmployee.classList.remove('d-none');
        }

        if (employeeResults) {
            employeeResults.innerHTML = '';
            employeeResults.classList.add('d-none');
        }
        restoreFocus('fs_employee_search');
    }

    function disableKeyboardForFullscreen() {
        const allInputs = document.querySelectorAll('#fullscreenModal input:not(#fs_card_number):not(#fs_employee_search), #fullscreenModal select, #fullscreenModal textarea');

        allInputs.forEach(input => {
            input.addEventListener('keydown', function(e) {
                e.preventDefault();
                return false;
            });
        });

        document.addEventListener('keydown', function(e) {
            if (document.activeElement.id === 'fs_card_number' || document.activeElement.id === 'fs_employee_search') {
                return true;
            }

            if ([32, 33, 34, 35, 36, 37, 38, 39, 40].indexOf(e.keyCode) > -1) {
                e.preventDefault();
                return false;
            }
        });

        document.body.addEventListener('wheel', function(e) {
            if (!e.target.closest('.fs-attendance-table-container, #fullscreenModal .card-body')) {
                e.preventDefault();
                return false;
            }
        }, { passive: false });
    }

    // Function to restore focus to specified element
    function restoreFocus(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.focus();
        }
    }

    // Improve modal accessibility
    document.addEventListener('DOMContentLoaded', function() {
        const fullscreenModal = document.getElementById('fullscreenModal');
        if (fullscreenModal) {
            fullscreenModal.addEventListener('shown.bs.modal', function() {
                // Instead of removing aria-hidden, set inert on other content
                document.querySelectorAll('body > *:not(#fullscreenModal)').forEach(element => {
                    if (element !== fullscreenModal) {
                        element.setAttribute('inert', '');
                    }
                });
                // Focus the first input field
                const firstInput = this.querySelector('#fs_card_number') || this.querySelector('#fs_employee_search');
                if (firstInput) {
                    firstInput.focus();
                }
            });

            fullscreenModal.addEventListener('hidden.bs.modal', function() {
                // Remove inert attribute from other content
                document.querySelectorAll('[inert]').forEach(element => {
                    element.removeAttribute('inert');
                });
                // Restore focus to the element that opened the modal
                const openButton = document.getElementById('fullscreen_btn');
                if (openButton) {
                    openButton.focus();
                }
            });
        }
    });
</script>
<?php include 'footer.php'; ?>

</body>
</html>
