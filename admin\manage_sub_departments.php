<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Judul halaman
$pageTitle = "Kelola Sub Departemen";
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    .container-form {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .card-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .card-body {
        padding: 20px;
    }

    .btn-add {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
    }

    .btn-edit {
        background-color: #2196F3;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 5px;
    }

    .btn-delete {
        background-color: #F44336;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
    }

    table {
        width: 100%;
        border-collapse: collapse;

    }

    table th, table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }

    table th {
        background-color: #bf0000;
    }

    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
        background-color: white;
        margin: 10% auto;
        padding: 20px;
        border-radius: 8px;
        width: 50%;
        max-width: 500px;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .modal-close {
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .form-group input, .form-group textarea, .form-group select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .form-group textarea {
        height: 100px;
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
    }

    .btn-submit {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
        margin-left: 10px;
    }

    .btn-cancel {
        background-color: #ccc;
        color: black;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
    }

    .alert {
        padding: 10px;
        margin-bottom: 15px;
        border-radius: 4px;
    }

    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .hidden {
        display: none;
    }

    .filter-container {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .filter-container label {
        margin-right: 10px;
        font-weight: 600;
    }

    .filter-container select {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        min-width: 200px;
    }
    .jarak-form{
        height: 80px;
    }
</style>

<body>
<?php include '../config/navbarb.php'; ?>
<div class="jarak-form"></div>
<div class="container-form">

<button style="border-radius: 10px; margin-bottom:10px;">
        <a href="manage_organization.php" style="text-decoration:none; color:white">Kembali</a>
    </button>
    <div class="card">
        <div class="card-header">
            <h2><?php echo $pageTitle; ?></h2>
            <button class="btn-add" id="btnAddSubDepartment">Tambah Sub Departemen</button>
        </div>
        <div class="card-body">
            <div id="alertContainer" class="hidden"></div>

            <div class="filter-container">
                <label for="departmentFilter">Filter Departemen:</label>
                <select id="departmentFilter">
                    <option value="">Semua Departemen</option>
                    <!-- Department options will be loaded here -->
                </select>
            </div>

            <table id="subDepartmentsTable">
                <thead>
                    <tr>
                        <th>Kode</th>
                        <th>Nama</th>
                        <th>Departemen</th>
                        <th>Deskripsi</th>
                        <th style="width:fit-content;">Aksi</th>
                    </tr>
                </thead>
                <tbody>

                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal for adding/editing sub department -->
<div id="subDepartmentModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">Tambah Sub Departemen</h3>
            <span class="modal-close">&times;</span>
        </div>
        <form id="subDepartmentForm">
            <input type="hidden" id="subDepartmentId" name="id">
            <div class="form-group">
                <label for="departmentId">Departemen *</label>
                <select id="departmentId" name="department_id" required>
                    <option value="">Pilih Departemen</option>
                    <!-- Department options will be loaded here -->
                </select>
            </div>
            <div class="form-group">
                <label for="subDepartmentCode">Kode Sub Departemen *</label>
                <input type="text" id="subDepartmentCode" name="code" required maxlength="10">
            </div>
            <div class="form-group">
                <label for="subDepartmentName">Nama Sub Departemen *</label>
                <input type="text" id="subDepartmentName" name="name" required maxlength="100">
            </div>
            <div class="form-group">
                <label for="subDepartmentDescription">Deskripsi</label>
                <textarea id="subDepartmentDescription" name="description"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn-cancel" id="btnCancelSubDepartment">Batal</button>
                <button type="submit" class="btn-submit">Simpan</button>
            </div>
        </form>
    </div>
</div>

<!-- Modal for confirmation -->
<div id="confirmationModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Konfirmasi</h3>
            <span class="modal-close">&times;</span>
        </div>
        <p id="confirmationMessage">Apakah Anda yakin ingin menghapus sub departemen ini?</p>
        <div class="form-actions">
            <button type="button" class="btn-cancel" id="btnCancelConfirmation">Batal</button>
            <button type="button" class="btn-submit" id="btnConfirm">Ya, Hapus</button>
        </div>
    </div>
</div>

<script>
    // Global variables
    let departments = [];
    let subDepartments = [];
    let currentSubDepartmentId = null;

    // DOM elements
    const subDepartmentsTable = document.getElementById('subDepartmentsTable');
    const subDepartmentModal = document.getElementById('subDepartmentModal');
    const confirmationModal = document.getElementById('confirmationModal');
    const subDepartmentForm = document.getElementById('subDepartmentForm');
    const alertContainer = document.getElementById('alertContainer');
    const departmentFilter = document.getElementById('departmentFilter');
    const departmentIdSelect = document.getElementById('departmentId');

    // Buttons
    const btnAddSubDepartment = document.getElementById('btnAddSubDepartment');
    const btnCancelSubDepartment = document.getElementById('btnCancelSubDepartment');
    const btnCancelConfirmation = document.getElementById('btnCancelConfirmation');
    const btnConfirm = document.getElementById('btnConfirm');

    // Modal close buttons
    const modalCloseButtons = document.querySelectorAll('.modal-close');

    // Load data on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadDepartments();
        loadSubDepartments();
    });

    // Event listeners
    btnAddSubDepartment.addEventListener('click', showAddSubDepartmentModal);
    btnCancelSubDepartment.addEventListener('click', closeSubDepartmentModal);
    btnCancelConfirmation.addEventListener('click', closeConfirmationModal);
    subDepartmentForm.addEventListener('submit', saveSubDepartment);
    departmentFilter.addEventListener('change', filterSubDepartments);

    modalCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            subDepartmentModal.style.display = 'none';
            confirmationModal.style.display = 'none';
        });
    });

    // Functions
    function loadDepartments() {
        fetch('api/get_organization_data.php?type=departments')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    departments = data.data.departments;
                    populateDepartmentDropdowns();
                } else {
                    showAlert('danger', 'Error loading departments: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'Error loading departments: ' + error.message);
            });
    }

    function loadSubDepartments() {
        fetch('api/get_organization_data.php?type=sub_departments')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    subDepartments = data.data.sub_departments;
                    renderSubDepartmentsTable();
                } else {
                    showAlert('danger', 'Error loading sub departments: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'Error loading sub departments: ' + error.message);
            });
    }

    function populateDepartmentDropdowns() {
        // Clear existing options
        departmentFilter.innerHTML = '<option value="">Semua Departemen</option>';
        departmentIdSelect.innerHTML = '<option value="">Pilih Departemen</option>';

        // Add department options
        departments.forEach(department => {
            const filterOption = document.createElement('option');
            filterOption.value = department.id;
            filterOption.textContent = `${department.name} (${department.code})`;
            departmentFilter.appendChild(filterOption);

            const selectOption = document.createElement('option');
            selectOption.value = department.id;
            selectOption.textContent = `${department.name} (${department.code})`;
            departmentIdSelect.appendChild(selectOption);
        });
    }

    function renderSubDepartmentsTable() {
        const tbody = subDepartmentsTable.querySelector('tbody');
        tbody.innerHTML = '';

        // Filter sub departments if a department is selected
        const filteredSubDepartments = departmentFilter.value
            ? subDepartments.filter(sd => sd.department_id == departmentFilter.value)
            : subDepartments;

        if (filteredSubDepartments.length === 0) {
            const tr = document.createElement('tr');
            tr.innerHTML = '<td colspan="5" style="text-align: center;">Tidak ada data sub departemen</td>';
            tbody.appendChild(tr);
            return;
        }

        filteredSubDepartments.forEach(subDepartment => {
            const department = departments.find(d => d.id == subDepartment.department_id);
            const departmentName = department ? `${department.name} (${department.code})` : '-';

            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${subDepartment.code}</td>
                <td>${subDepartment.name}</td>
                <td>${departmentName}</td>
                <td>${subDepartment.description || '-'}</td>
                <td>
                    <button class="btn-edit" data-id="${subDepartment.id}">Edit</button>
                    <button class="btn-delete" data-id="${subDepartment.id}">Hapus</button>
                </td>
            `;
            tbody.appendChild(tr);
        });

        // Add event listeners to edit and delete buttons
        const editButtons = tbody.querySelectorAll('.btn-edit');
        const deleteButtons = tbody.querySelectorAll('.btn-delete');

        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const subDepartmentId = this.getAttribute('data-id');
                showEditSubDepartmentModal(subDepartmentId);
            });
        });

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const subDepartmentId = this.getAttribute('data-id');
                showDeleteConfirmation(subDepartmentId);
            });
        });
    }

    function filterSubDepartments() {
        renderSubDepartmentsTable();
    }

    function showAddSubDepartmentModal() {
        currentSubDepartmentId = null;
        document.getElementById('modalTitle').textContent = 'Tambah Sub Departemen';
        subDepartmentForm.reset();
        document.getElementById('subDepartmentId').value = '';
        subDepartmentModal.style.display = 'block';
    }

    function showEditSubDepartmentModal(subDepartmentId) {
        const subDepartment = subDepartments.find(sd => sd.id == subDepartmentId);
        if (!subDepartment) return;

        currentSubDepartmentId = subDepartmentId;
        document.getElementById('modalTitle').textContent = 'Edit Sub Departemen';
        document.getElementById('subDepartmentId').value = subDepartment.id;
        document.getElementById('departmentId').value = subDepartment.department_id;
        document.getElementById('subDepartmentCode').value = subDepartment.code;
        document.getElementById('subDepartmentName').value = subDepartment.name;
        document.getElementById('subDepartmentDescription').value = subDepartment.description || '';

        subDepartmentModal.style.display = 'block';
    }

    function closeSubDepartmentModal() {
        subDepartmentModal.style.display = 'none';
    }

    function showDeleteConfirmation(subDepartmentId) {
        const subDepartment = subDepartments.find(sd => sd.id == subDepartmentId);
        if (!subDepartment) return;

        currentSubDepartmentId = subDepartmentId;
        document.getElementById('confirmationMessage').textContent = `Apakah Anda yakin ingin menghapus sub departemen "${subDepartment.name}" (${subDepartment.code})?`;

        btnConfirm.onclick = function() {
            deleteSubDepartment(subDepartmentId);
        };

        confirmationModal.style.display = 'block';
    }

    function closeConfirmationModal() {
        confirmationModal.style.display = 'none';
    }

    function saveSubDepartment(event) {
        event.preventDefault();

        // Validasi form sebelum submit
        const departmentId = document.getElementById('departmentId').value;
        const code = document.getElementById('subDepartmentCode').value;
        const name = document.getElementById('subDepartmentName').value;

        if (!departmentId) {
            showAlert('danger', 'Silakan pilih departemen');
            return;
        }

        if (!code) {
            showAlert('danger', 'Kode sub departemen harus diisi');
            return;
        }

        if (!name) {
            showAlert('danger', 'Nama sub departemen harus diisi');
            return;
        }

        const formData = new FormData(subDepartmentForm);
        formData.append('type', 'sub_department');
        formData.append('action', currentSubDepartmentId ? 'update' : 'create');

        // Tampilkan loading state
        const submitButton = subDepartmentForm.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Menyimpan...';
        submitButton.disabled = true;

        fetch('api/manage_organization_data.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Reset button state
            submitButton.textContent = originalText;
            submitButton.disabled = false;

            if (data.success) {
                showAlert('success', data.message);
                closeSubDepartmentModal();
                loadSubDepartments();
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Reset button state
            submitButton.textContent = originalText;
            submitButton.disabled = false;
            showAlert('danger', 'Error saving sub department: ' + error.message);
        });
    }

    function deleteSubDepartment(subDepartmentId) {
        const formData = new FormData();
        formData.append('type', 'sub_department');
        formData.append('action', 'delete');
        formData.append('id', subDepartmentId);

        fetch('api/manage_organization_data.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                closeConfirmationModal();
                loadSubDepartments();
            } else {
                showAlert('danger', data.message);
                closeConfirmationModal();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Error deleting sub department: ' + error.message);
            closeConfirmationModal();
        });
    }

    function showAlert(type, message) {
        alertContainer.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        alertContainer.classList.remove('hidden');

        // Hide alert after 5 seconds
        setTimeout(() => {
            alertContainer.classList.add('hidden');
        }, 5000);
    }
</script>

<?php include '../config/footer.php'; ?>
</body>
</html>
