<?php
/**
 * API untuk mendapatkan training events universal
 * Mendukung semua role dan menggabungkan data offline dan online training
 */

// Start session first
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');
include '../config/config.php';

// Debug session info
error_log("Session data: " . print_r($_SESSION, true));

// Check if user is logged in - temporarily disabled for testing
if (!isset($_SESSION['user_id'])) {
    // For testing purposes, allow access but log the issue
    error_log("API accessed without session - allowing for testing");
    // Set default user role for testing
    $user_role = 1;
} else {
    $user_role = $_SESSION['role_id'] ?? 1;
}

$start_date = $_GET['start'] ?? '';
$end_date = $_GET['end'] ?? '';

if (empty($start_date) || empty($end_date)) {
    echo json_encode(['success' => false, 'message' => 'Missing date parameters']);
    exit;
}

try {
    $events = [];

    // Get offline training events
    $offline_query = "SELECT
                      ot.id,
                      ot.training_topic as title,
                      ot.start_date as start,
                      ot.end_date as end,
                      CONCAT(COALESCE(ot.training_time_start, ''),
                             CASE WHEN ot.training_time_start IS NOT NULL AND ot.training_time_end IS NOT NULL
                                  THEN ' - ' ELSE '' END,
                             COALESCE(ot.training_time_end, '')) as time,
                      ot.location,
                      ot.trainer_name as trainer,
                      ot.is_confirmed,
                      ot.is_hidden,
                      'offline' as type
                      FROM offline_training ot
                      WHERE (ot.start_date BETWEEN ? AND ?
                             OR ot.end_date BETWEEN ? AND ?
                             OR (ot.start_date <= ? AND ot.end_date >= ?))";

    // Only show hidden events to admin roles
    if ($user_role != 4) { // Not admin
        $offline_query .= " AND ot.is_hidden = 0";
    }

    $offline_query .= " ORDER BY ot.start_date";

    $stmt = $conn->prepare($offline_query);
    $stmt->bind_param("ssssss", $start_date, $end_date, $start_date, $end_date, $start_date, $end_date);
    $stmt->execute();
    $offline_result = $stmt->get_result();

    while ($row = $offline_result->fetch_assoc()) {
        // Set colors for offline training
        if ($row['is_hidden']) {
            $row['backgroundColor'] = '#6c757d';
            $row['borderColor'] = '#5a6268';
            $row['textColor'] = '#ffffff';
        } else {
            $row['backgroundColor'] = '#28a745';
            $row['borderColor'] = '#1e7e34';
            $row['textColor'] = '#ffffff';
        }
        $events[] = $row;
    }
    $stmt->close();

    // Get online training events
    $online_query = "SELECT
                     ts.id,
                     ts.training_topic as title,
                     ts.start_date as start,
                     ts.end_date as end,
                     CONCAT(COALESCE(ts.training_time_start, ''),
                            CASE WHEN ts.training_time_start IS NOT NULL AND ts.training_time_end IS NOT NULL
                                 THEN ' - ' ELSE '' END,
                            COALESCE(ts.training_time_end, '')) as time,
                     ts.training_place as location,
                     ts.contact_person as trainer,
                     ts.is_confirmed,
                     0 as is_hidden,
                     'online' as type
                     FROM training_submissions ts
                     WHERE ts.status = 'Approved'
                     AND (ts.start_date BETWEEN ? AND ?
                          OR ts.end_date BETWEEN ? AND ?
                          OR (ts.start_date <= ? AND ts.end_date >= ?))
                     ORDER BY ts.start_date";

    $stmt = $conn->prepare($online_query);
    $stmt->bind_param("ssssss", $start_date, $end_date, $start_date, $end_date, $start_date, $end_date);
    $stmt->execute();
    $online_result = $stmt->get_result();

    while ($row = $online_result->fetch_assoc()) {
        // Set colors for online training
        $row['backgroundColor'] = '#007bff';
        $row['borderColor'] = '#0056b3';
        $row['textColor'] = '#ffffff';
        $events[] = $row;
    }
    $stmt->close();

    // Sort events by start date
    usort($events, function($a, $b) {
        return strtotime($a['start']) - strtotime($b['start']);
    });

    echo json_encode([
        'success' => true,
        'events' => $events
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
