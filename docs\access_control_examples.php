<?php
/**
 * Access Control Usage Examples
 * Contoh penggunaan sistem access control yang benar
 */

// Include required files
include '../config/config.php';
include '../config/access_control.php';

// ===== EXAMPLE 1: Basic Protection (Level 4+ OR Supervisor/Chief) =====
function example1_basic_protection() {
    // Default: Level 4+ OR Supervisor/Chief positions
    $user_data = checkUserLevel($_SESSION['user_id'], 4, ['Supervisor', 'Chief'], '../index.php');
    
    // If we reach here, user has access
    echo "Welcome " . $user_data['name'] . "!";
    echo "Access granted because: " . $user_data['access_reason'];
}

// ===== EXAMPLE 2: Manager-Only Features (Level 5+ OR Manager positions) =====
function example2_manager_features() {
    // Level 5+ OR Manager positions
    $user_data = checkUserLevel($_SESSION['user_id'], 5, ['Manager'], '../dashboard.php');
    
    // Manager-only content here
    echo "Manager dashboard content";
}

// ===== EXAMPLE 3: Director-Only Features (Level 8+ only, no special positions) =====
function example3_director_only() {
    // Level 8+ only, no special positions allowed
    $user_data = checkUserLevel($_SESSION['user_id'], 8, [], '../dashboard.php');
    
    // Director-only content here
    echo "Director dashboard content";
}

// ===== EXAMPLE 4: Custom Special Positions =====
function example4_custom_positions() {
    // Level 4+ OR Team Lead/Coordinator positions
    $user_data = checkUserLevel($_SESSION['user_id'], 4, ['Team Lead', 'Coordinator'], '../index.php');
    
    // Content for level 4+ or team leads/coordinators
    echo "Team management content";
}

// ===== EXAMPLE 5: Conditional UI Elements =====
function example5_conditional_ui() {
    ?>
    <div class="dashboard">
        <h1>Dashboard</h1>
        
        <!-- Show for Level 4+ OR Supervisor/Chief -->
        <?php if (hasMinimumLevel(4, ['Supervisor', 'Chief'])): ?>
            <div class="training-section">
                <h2>Training Management</h2>
                <a href="form.php" class="btn btn-primary">Submit Training Request</a>
            </div>
        <?php endif; ?>
        
        <!-- Show for Level 5+ OR Manager positions -->
        <?php if (hasMinimumLevel(5, ['Manager'])): ?>
            <div class="manager-section">
                <h2>Manager Tools</h2>
                <a href="approve_training.php" class="btn btn-success">Approve Training</a>
            </div>
        <?php endif; ?>
        
        <!-- Show for Level 8+ only -->
        <?php if (hasMinimumLevel(8, [])): ?>
            <div class="director-section">
                <h2>Director Dashboard</h2>
                <a href="director_reports.php" class="btn btn-warning">Director Reports</a>
            </div>
        <?php endif; ?>
        
        <!-- Show for specific positions -->
        <?php if (hasSpecialPosition($_SESSION['user_id'], ['Supervisor'])): ?>
            <div class="supervisor-section">
                <h2>Supervisor Tools</h2>
                <a href="supervisor_dashboard.php" class="btn btn-info">Supervisor Dashboard</a>
            </div>
        <?php endif; ?>
    </div>
    <?php
}

// ===== EXAMPLE 6: Get Access Info Without Blocking =====
function example6_access_info() {
    $user_data = getUserLevel($_SESSION['user_id']);
    
    if ($user_data) {
        echo "User: " . $user_data['name'] . "<br>";
        echo "Level: " . $user_data['level_karyawan'] . " (" . getLevelDescription($user_data['level_karyawan']) . ")<br>";
        echo "Position: " . $user_data['jabatan'] . "<br>";
        
        // Check different access levels
        $eligibility = getAccessEligibility($_SESSION['user_id'], 4, ['Supervisor', 'Chief']);
        
        if ($eligibility['eligible']) {
            echo "✅ Can access training form: " . $eligibility['reason'] . "<br>";
        } else {
            echo "❌ Cannot access training form: " . $eligibility['reason'] . "<br>";
        }
    }
}

// ===== EXAMPLE 7: Multiple Protection Levels =====
function example7_multiple_levels() {
    // Check what user can access
    $user_data = getUserLevel($_SESSION['user_id']);
    
    if (!$user_data) {
        echo "User data not found";
        return;
    }
    
    echo "<h3>Access Levels for " . $user_data['name'] . "</h3>";
    
    // Level 3+ OR Supervisor/Chief
    if (hasMinimumLevel(3, ['Supervisor', 'Chief'])) {
        echo "✅ Basic Features<br>";
    }
    
    // Level 4+ OR Supervisor/Chief  
    if (hasMinimumLevel(4, ['Supervisor', 'Chief'])) {
        echo "✅ Training Management<br>";
    }
    
    // Level 5+ OR Manager
    if (hasMinimumLevel(5, ['Manager'])) {
        echo "✅ Manager Tools<br>";
    }
    
    // Level 7+ only
    if (hasMinimumLevel(7, [])) {
        echo "✅ Senior Management<br>";
    }
    
    // Level 8+ only
    if (hasMinimumLevel(8, [])) {
        echo "✅ Director Level<br>";
    }
}

// ===== EXAMPLE 8: Error Handling =====
function example8_error_handling() {
    try {
        // This might redirect if access denied
        $user_data = checkUserLevel($_SESSION['user_id'], 4, ['Supervisor', 'Chief'], '../index.php');
        
        // If we reach here, access is granted
        echo "Access granted!";
        
    } catch (Exception $e) {
        // Handle any errors
        error_log("Access control error: " . $e->getMessage());
        echo "An error occurred. Please try again.";
    }
}

// ===== EXAMPLE 9: Custom Redirect URLs =====
function example9_custom_redirects() {
    // Different redirect URLs for different scenarios
    
    // Redirect to login if not authorized
    $user_data = checkUserLevel($_SESSION['user_id'], 4, ['Supervisor', 'Chief'], '../view/login.php');
    
    // Redirect to dashboard if not authorized  
    $user_data = checkUserLevel($_SESSION['user_id'], 5, ['Manager'], '../dashboard.php');
    
    // Redirect to specific error page
    $user_data = checkUserLevel($_SESSION['user_id'], 8, [], '../errors/insufficient_level.php');
}

// ===== EXAMPLE 10: Position Matching Examples =====
function example10_position_matching() {
    $user_data = getUserLevel($_SESSION['user_id']);
    
    if ($user_data) {
        $jabatan = $user_data['jabatan'];
        echo "User position: " . $jabatan . "<br><br>";
        
        // Test different position matches
        $test_positions = [
            'Supervisor' => ['Supervisor Production', 'Quality Supervisor', 'Production Supervisor'],
            'Chief' => ['Chief Engineer', 'Security Chief', 'Chief Finance'],
            'Manager' => ['Manager HR', 'Production Manager', 'Finance Manager'],
            'Head' => ['Head of IT', 'Department Head', 'Section Head']
        ];
        
        foreach ($test_positions as $search_term => $examples) {
            echo "<strong>Searching for '{$search_term}':</strong><br>";
            
            $has_position = hasSpecialPosition($_SESSION['user_id'], [$search_term]);
            echo "Current user: " . ($has_position ? "MATCH" : "NO MATCH") . "<br>";
            
            echo "Would match: " . implode(', ', $examples) . "<br><br>";
        }
    }
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Access Control Examples</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .example { border: 1px solid #ccc; padding: 15px; margin: 10px 0; }
        .example h3 { margin-top: 0; color: #333; }
        code { background: #f4f4f4; padding: 2px 5px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Access Control System - Usage Examples</h1>
    
    <div class="example">
        <h3>Function Signatures</h3>
        <code>checkUserLevel($user_id, $min_level = 4, $special_positions = ['Supervisor', 'Chief'], $redirect_url = '../index.php')</code><br>
        <code>hasMinimumLevel($required_level, $special_positions = ['Supervisor', 'Chief'])</code><br>
        <code>hasSpecialPosition($user_id, $special_positions = ['Supervisor', 'Chief'])</code><br>
        <code>getAccessEligibility($user_id, $min_level = 4, $special_positions = ['Supervisor', 'Chief'])</code><br>
        <code>getUserLevel($user_id)</code><br>
        <code>getLevelDescription($level)</code>
    </div>
    
    <p><a href="../admin/test_access_simple.php">Test Simple Functions</a></p>
    <p><a href="../admin/test_access_control.php">Full Test Page</a></p>
</body>
</html>
