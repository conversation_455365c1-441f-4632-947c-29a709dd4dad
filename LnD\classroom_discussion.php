<?php
/**
 * Classroom Discussion Page for Pemohon (Applicant)
 * This page allows applicants to view and participate in discussions
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';

// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['full_name'] ?? 'Pengguna';

// Check if discussion ID is provided
$discussion_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($discussion_id <= 0) {
    header('Location: classroom.php');
    exit();
}

// Initialize variables
$discussion = null;
$class = null;
$comments = [];
$success_message = '';
$error_message = '';

// Check if the classroom tables exist
$classroom_tables_exist = true;
$required_tables = [
    'training_discussions',
    'training_discussion_comments',
    'training_classes'
];

foreach ($required_tables as $table) {
    $check_table_query = "SHOW TABLES LIKE '$table'";
    $table_result = $conn->query($check_table_query);
    if (!$table_result || $table_result->num_rows == 0) {
        $classroom_tables_exist = false;
        break;
    }
}

if (!$classroom_tables_exist) {
    $error_message = "Fitur classroom belum diaktifkan. Silakan hubungi administrator sistem.";
} else {
    // First, let's check the structure of the users table to find the name column
    $user_table_query = "DESCRIBE users";
    $user_table_result = $conn->query($user_table_query);
    $name_column = 'name'; // Default to name
    
    if ($user_table_result) {
        while ($column = $user_table_result->fetch_assoc()) {
            // Look for common name column patterns
            if (in_array(strtolower($column['Field']), ['full_name', 'name', 'user_name', 'username', 'nama'])) {
                $name_column = $column['Field'];
                // Prefer full_name or name if available
                if (in_array(strtolower($column['Field']), ['full_name', 'name', 'nama'])) {
                    break;
                }
            }
        }
    }
    
    // Get discussion details
    $discussion_query = "SELECT d.*, c.title as class_title, c.id as class_id, 
                        u.$name_column as creator_name, ud.dept as creator_department
                        FROM training_discussions d
                        JOIN training_classes c ON d.class_id = c.id
                        JOIN users u ON d.created_by = u.id
                        LEFT JOIN user_departments ud ON u.id = ud.user_id
                        JOIN training_participants p ON c.id = p.class_id AND p.user_id = ?
                        WHERE d.id = ?";
    
    $stmt = $conn->prepare($discussion_query);
    $stmt->bind_param("ii", $user_id, $discussion_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $discussion = $result->fetch_assoc();
        $class_id = $discussion['class_id'];
        
        // Get class details
        $class_query = "SELECT c.*, t.training_topic 
                       FROM training_classes c
                       JOIN training_submissions t ON c.training_id = t.id
                       WHERE c.id = ?";
        
        $stmt = $conn->prepare($class_query);
        $stmt->bind_param("i", $class_id);
        $stmt->execute();
        $class_result = $stmt->get_result();
        
        if ($class_result->num_rows > 0) {
            $class = $class_result->fetch_assoc();
        }
        
        // Get comments
        $comments_query = "SELECT c.*, u.$name_column as commenter_name, ud.dept as commenter_department
                          FROM training_discussion_comments c
                          JOIN users u ON c.created_by = u.id
                          LEFT JOIN user_departments ud ON u.id = ud.user_id
                          WHERE c.discussion_id = ? AND c.parent_id IS NULL
                          ORDER BY c.created_at ASC";
        
        $stmt = $conn->prepare($comments_query);
        $stmt->bind_param("i", $discussion_id);
        $stmt->execute();
        $comments_result = $stmt->get_result();
        
        while ($comment = $comments_result->fetch_assoc()) {
            // Get replies for this comment
            $replies_query = "SELECT r.*, u.$name_column as commenter_name, ud.dept as commenter_department
                             FROM training_discussion_comments r
                             JOIN users u ON r.created_by = u.id
                             LEFT JOIN user_departments ud ON u.id = ud.user_id
                             WHERE r.parent_id = ?
                             ORDER BY r.created_at ASC";
            
            $stmt = $conn->prepare($replies_query);
            $stmt->bind_param("i", $comment['id']);
            $stmt->execute();
            $replies_result = $stmt->get_result();
            
            $replies = [];
            while ($reply = $replies_result->fetch_assoc()) {
                $replies[] = $reply;
            }
            
            $comment['replies'] = $replies;
            $comments[] = $comment;
        }
        
        // Handle new comment submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_comment'])) {
            $comment_content = trim($_POST['comment_content']);
            $parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
            
            if (empty($comment_content)) {
                $error_message = "Komentar tidak boleh kosong.";
            } else {
                // Insert new comment
                $insert_query = "INSERT INTO training_discussion_comments 
                               (discussion_id, content, parent_id, created_by) 
                               VALUES (?, ?, ?, ?)";
                
                $stmt = $conn->prepare($insert_query);
                $stmt->bind_param("isis", $discussion_id, $comment_content, $parent_id, $user_id);
                
                if ($stmt->execute()) {
                    $success_message = "Komentar berhasil ditambahkan.";
                    
                    // Redirect to refresh the page and avoid form resubmission
                    header("Location: classroom_discussion.php?id=$discussion_id&success=1");
                    exit();
                } else {
                    $error_message = "Gagal menambahkan komentar: " . $conn->error;
                }
            }
        }
        
        // Handle success message from redirect
        if (isset($_GET['success']) && $_GET['success'] == 1) {
            $success_message = "Komentar berhasil ditambahkan.";
        }
    } else {
        $error_message = "Diskusi tidak ditemukan atau Anda tidak memiliki akses.";
    }
    
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
:root {
    --primary-color: rgb(157, 0, 0);
    --primary-color-dark: rgb(127, 0, 0);
    --primary-color-light: rgba(157, 0, 0, 0.1);
    --secondary-color: #2c3e50;
    --success-color: #4CAF50;
    --success-color-light: #e8f5e9;
    --warning-color: #ffc107;
    --danger-color: #f44336;
    --info-color: #2196F3;
    --text-dark: #333;
    --text-light: #757575;
    --white: #ffffff;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
    --shadow-lg: 0 6px 15px rgba(0,0,0,0.1);
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
}

.jarak {
    height: 80px;
}

.discussion-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.discussion-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--white);
    padding: var(--spacing-xl) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.discussion-header h1 {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    font-weight: 700;
}

.discussion-header .class-info {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: var(--spacing-sm);
}

.discussion-card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.discussion-card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
}

.discussion-card-body {
    padding: var(--spacing-lg);
}

.discussion-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    color: var(--text-dark);
    font-size: 0.9rem;
}

.discussion-content {
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.comments-section {
    margin-top: var(--spacing-xl);
}

.comment-card {
    background-color: #f8f9fa;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    border-left: 4px solid var(--primary-color);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.comment-author {
    font-weight: 600;
    color: var(--text-dark);
}

.comment-date {
    color: var(--text-dark);
    font-size: 0.85rem;
}

.comment-content {
    margin-bottom: var(--spacing-sm);
    line-height: 1.5;
}

.comment-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.reply-form {
    margin-top: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: var(--white);
    border-radius: var(--border-radius-sm);
    border: 1px solid #dee2e6;
    display: none;
}

.replies-container {
    margin-left: var(--spacing-xl);
    margin-top: var(--spacing-md);
}

.reply-card {
    background-color: var(--white);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    border-left: 3px solid var(--secondary-color);
}

.new-comment-form {
    background-color: #f8f9fa;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    margin-top: var(--spacing-xl);
}

.announcement-badge {
    background-color: var(--warning-color);
    color: var(--text-dark);
    padding: 3px 8px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: var(--spacing-sm);
}

.pinned-badge {
    background-color: var(--info-color);
    color: var(--white);
    padding: 3px 8px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: var(--spacing-sm);
}

@media (max-width: 768px) {
    .discussion-meta {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .replies-container {
        margin-left: var(--spacing-md);
    }
}
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="discussion-container">
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $error_message ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $success_message ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($discussion): ?>
        <div class="discussion-header">
            <h1>
                <?= htmlspecialchars($discussion['title']) ?>
                <?php if ($discussion['is_announcement']): ?>
                    <span class="announcement-badge">Pengumuman</span>
                <?php endif; ?>
                <?php if ($discussion['is_pinned']): ?>
                    <span class="pinned-badge">Disematkan</span>
                <?php endif; ?>
            </h1>
            <div class="class-info">
                <i class="fas fa-chalkboard"></i> <?= htmlspecialchars($discussion['class_title']) ?>
            </div>
        </div>
        
        <div class="discussion-card">
            <div class="discussion-card-header">
                <h2>Diskusi</h2>
            </div>
            <div class="discussion-card-body">
                <div class="discussion-meta">
                    <div class="discussion-author">
                        <i class="fas fa-user"></i> <?= htmlspecialchars($discussion['creator_name']) ?>
                        <?php if (!empty($discussion['creator_department'])): ?>
                            <span class="department-badge"><?= htmlspecialchars($discussion['creator_department']) ?></span>
                        <?php endif; ?>
                    </div>
                    <div class="discussion-date" title="<?= date('d F Y H:i:s', strtotime($discussion['created_at'])) ?>">
                        <i class="fas fa-calendar-alt"></i> <?= date('d M Y H:i', strtotime($discussion['created_at'])) ?>
                    </div>
                </div>
                
                <div class="discussion-content">
                    <?= nl2br(htmlspecialchars($discussion['content'])) ?>
                </div>
            </div>
        </div>
        
        <div class="comments-section">
            <h3 class="mb-4">Komentar (<?= count($comments) ?>)</h3>
            
            <?php if (empty($comments)): ?>
                <div class="alert alert-info">
                    Belum ada komentar. Jadilah yang pertama berkomentar!
                </div>
            <?php else: ?>
                <?php foreach ($comments as $comment): ?>
                    <div class="comment-card" id="comment-<?= $comment['id'] ?>">
                        <div class="comment-header">
                            <div class="comment-author">
                                <i class="fas fa-user"></i> <?= htmlspecialchars($comment['commenter_name']) ?>
                                <?php if (!empty($comment['commenter_department'])): ?>
                                    <span class="department-badge"><?= htmlspecialchars($comment['commenter_department']) ?></span>
                                <?php endif; ?>
                            </div>
                            <div class="comment-date" title="<?= date('d F Y H:i:s', strtotime($comment['created_at'])) ?>">
                                <?= date('d M Y H:i', strtotime($comment['created_at'])) ?>
                            </div>
                        </div>
                        
                        <div class="comment-content">
                            <?= nl2br(htmlspecialchars($comment['content'])) ?>
                        </div>
                        
                        <div class="comment-actions">
                            <button class="btn btn-sm btn-outline-secondary reply-btn" data-comment-id="<?= $comment['id'] ?>">
                                <i class="fas fa-reply"></i> Balas
                            </button>
                        </div>
                        
                        <div class="reply-form" id="reply-form-<?= $comment['id'] ?>">
                            <form method="post">
                                <input type="hidden" name="parent_id" value="<?= $comment['id'] ?>">
                                <div class="mb-3">
                                    <label for="reply-content-<?= $comment['id'] ?>" class="form-label">Balas Komentar</label>
                                    <textarea class="form-control" id="reply-content-<?= $comment['id'] ?>" name="comment_content" rows="3" required></textarea>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-sm btn-secondary me-2 cancel-reply-btn" data-comment-id="<?= $comment['id'] ?>">
                                        Batal
                                    </button>
                                    <button type="submit" name="submit_comment" class="btn btn-sm btn-primary">
                                        <i class="fas fa-paper-plane"></i> Kirim Balasan
                                    </button>
                                </div>
                            </form>
                        </div>
                        
                        <?php if (!empty($comment['replies'])): ?>
                            <div class="replies-container">
                                <?php foreach ($comment['replies'] as $reply): ?>
                                    <div class="reply-card" id="comment-<?= $reply['id'] ?>">
                                        <div class="comment-header">
                                            <div class="comment-author">
                                                <i class="fas fa-user"></i> <?= htmlspecialchars($reply['commenter_name']) ?>
                                                <?php if (!empty($reply['commenter_department'])): ?>
                                                    <span class="department-badge"><?= htmlspecialchars($reply['commenter_department']) ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="comment-date" title="<?= date('d F Y H:i:s', strtotime($reply['created_at'])) ?>">
                                                <?= date('d M Y H:i', strtotime($reply['created_at'])) ?>
                                            </div>
                                        </div>
                                        
                                        <div class="comment-content">
                                            <?= nl2br(htmlspecialchars($reply['content'])) ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
            
            <div class="new-comment-form">
                <h4 class="mb-3">Tambahkan Komentar</h4>
                <form method="post">
                    <div class="mb-3">
                        <label for="comment_content" class="form-label">Komentar Anda</label>
                        <textarea class="form-control" id="comment_content" name="comment_content" rows="4" required></textarea>
                    </div>
                    <button type="submit" name="submit_comment" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Kirim Komentar
                    </button>
                </form>
            </div>
        </div>
        
        <div class="d-flex justify-content-between mt-4">
            <a href="classroom_detail.php?id=<?= $discussion['class_id'] ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali ke Kelas
            </a>
        </div>
    <?php else: ?>
        <div class="alert alert-danger">
            <h4><i class="fas fa-exclamation-triangle"></i> Diskusi Tidak Ditemukan</h4>
            <p>Diskusi yang Anda cari tidak ditemukan atau Anda tidak memiliki akses.</p>
            <a href="classroom.php" class="btn btn-primary mt-3">Kembali ke Classroom</a>
        </div>
    <?php endif; ?>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);
        
        // Reply functionality
        const replyButtons = document.querySelectorAll('.reply-btn');
        const cancelReplyButtons = document.querySelectorAll('.cancel-reply-btn');
        
        replyButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const commentId = this.getAttribute('data-comment-id');
                const replyForm = document.getElementById(`reply-form-${commentId}`);
                replyForm.style.display = 'block';
            });
        });
        
        cancelReplyButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const commentId = this.getAttribute('data-comment-id');
                const replyForm = document.getElementById(`reply-form-${commentId}`);
                replyForm.style.display = 'none';
            });
        });
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
