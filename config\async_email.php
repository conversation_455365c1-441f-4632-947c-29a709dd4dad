<?php
/**
 * File untuk menangani pengiriman email secara asinkron
 * File ini akan dipanggil melalui AJAX atau background process
 */

// Display all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Pastikan file ini hanya diakses dengan parameter yang benar
if (!isset($_GET['token']) || !isset($_GET['training_id'])) {
    exit('Unauthorized access');
}

// Validasi token (sederhana, bisa ditingkatkan keamanannya)
$expected_token = hash('sha256', $_GET['training_id'] . 'training_email_secret');
if ($_GET['token'] !== $expected_token) {
    exit('Invalid token');
}

// Include file yang diperlukan
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/notification_helper.php';

// Ambil parameter
$training_id = intval($_GET['training_id']);
$status = $_GET['status'] ?? 'Pending';
$comments = $_GET['comments'] ?? '';
$assignment = $_GET['assignment'] ?? '';
$next_approver_role_id = intval($_GET['next_approver_role_id'] ?? 2);
$next_approver_id = isset($_GET['next_approver_id']) ? intval($_GET['next_approver_id']) : null;

// Log parameter untuk debugging
error_log("Async email parameters: training_id=$training_id, status=$status, next_approver_role_id=$next_approver_role_id, next_approver_id=" . ($next_approver_id ?? 'NULL') . ", comments=" . substr($comments, 0, 50) . "..., assignment=" . substr($assignment, 0, 50) . "...");

// Log start
error_log("Starting async email sending for training ID: $training_id");

try {
    // Kirim email
    $notification_result = send_status_update_notification(
        $training_id,
        $status,
        $comments,
        $assignment,
        $next_approver_role_id,
        $conn,
        $next_approver_id
    );
} catch (Exception $e) {
    error_log("Exception in async_email.php: " . $e->getMessage());
    $notification_result = [
        'success' => false,
        'message' => "Exception occurred: " . $e->getMessage()
    ];
}

// Log hasil
if ($notification_result['success']) {
    error_log("Async email notification sent successfully: " . $notification_result['message']);
} else {
    error_log("Failed to send async email notification: " . $notification_result['message']);
    if (isset($notification_result['errors']) && !empty($notification_result['errors'])) {
        foreach ($notification_result['errors'] as $error) {
            error_log("Error detail: " . $error);
        }
    }
}

// Tutup koneksi database
$conn->close();

// Jika dipanggil langsung, berikan respons
if (php_sapi_name() !== 'cli') {
    header('Content-Type: application/json');
    echo json_encode($notification_result);
}
