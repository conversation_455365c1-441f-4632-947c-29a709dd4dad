<?php
/**
 * Create Discussion Page for Pemohon (Applicant)
 * This page allows applicants to create new discussions
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';

// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['full_name'] ?? 'Pengguna';

// Check if class ID is provided
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;

if ($class_id <= 0) {
    header('Location: classroom.php');
    exit();
}

// Initialize variables
$class = null;
$success_message = '';
$error_message = '';

// Check if the classroom tables exist
$classroom_tables_exist = true;
$required_tables = [
    'training_discussions',
    'training_classes'
];

foreach ($required_tables as $table) {
    $check_table_query = "SHOW TABLES LIKE '$table'";
    $table_result = $conn->query($check_table_query);
    if (!$table_result || $table_result->num_rows == 0) {
        $classroom_tables_exist = false;
        break;
    }
}

if (!$classroom_tables_exist) {
    $error_message = "Fitur classroom belum diaktifkan. Silakan hubungi administrator sistem.";
} else {
    // Get class details
    $class_query = "SELECT c.*, t.training_topic 
                   FROM training_classes c
                   JOIN training_submissions t ON c.training_id = t.id
                   JOIN training_participants p ON c.id = p.class_id AND p.user_id = ?
                   WHERE c.id = ?";
    
    $stmt = $conn->prepare($class_query);
    $stmt->bind_param("ii", $user_id, $class_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $class = $result->fetch_assoc();
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_discussion'])) {
            $title = trim($_POST['title']);
            $content = trim($_POST['content']);
            
            if (empty($title)) {
                $error_message = "Judul diskusi tidak boleh kosong.";
            } elseif (empty($content)) {
                $error_message = "Konten diskusi tidak boleh kosong.";
            } else {
                // Insert new discussion
                $insert_query = "INSERT INTO training_discussions 
                               (class_id, title, content, created_by) 
                               VALUES (?, ?, ?, ?)";
                
                $stmt = $conn->prepare($insert_query);
                $stmt->bind_param("issi", $class_id, $title, $content, $user_id);
                
                if ($stmt->execute()) {
                    $discussion_id = $stmt->insert_id;
                    $success_message = "Diskusi berhasil dibuat.";
                    
                    // Redirect to the new discussion
                    header("Location: classroom_discussion.php?id=$discussion_id&success=1");
                    exit();
                } else {
                    $error_message = "Gagal membuat diskusi: " . $conn->error;
                }
            }
        }
    } else {
        $error_message = "Kelas tidak ditemukan atau Anda tidak memiliki akses.";
    }
    
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
:root {
    --primary-color: rgb(157, 0, 0);
    --primary-color-dark: rgb(127, 0, 0);
    --primary-color-light: rgba(157, 0, 0, 0.1);
    --secondary-color: #2c3e50;
    --success-color: #4CAF50;
    --success-color-light: #e8f5e9;
    --warning-color: #ffc107;
    --danger-color: #f44336;
    --info-color: #2196F3;
    --text-dark: #333;
    --text-light: #757575;
    --white: #ffffff;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
    --shadow-lg: 0 6px 15px rgba(0,0,0,0.1);
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
}

.jarak {
    height: 80px;
}

.discussion-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.discussion-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--white);
    padding: var(--spacing-xl) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.discussion-header h1 {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    font-weight: 700;
}

.discussion-header .class-info {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: var(--spacing-sm);
}

.discussion-card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.discussion-card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
}

.discussion-card-body {
    padding: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-dark);
}

.form-control {
    border-radius: var(--border-radius-sm);
    border: 1px solid #ced4da;
    padding: 10px 15px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(157, 0, 0, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-color-dark);
    border-color: var(--primary-color-dark);
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="discussion-container">
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $error_message ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $success_message ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($class): ?>
        <div class="discussion-header">
            <h1>Buat Diskusi Baru</h1>
            <div class="class-info">
                <i class="fas fa-chalkboard"></i> <?= htmlspecialchars($class['title']) ?>
            </div>
        </div>
        
        <div class="discussion-card">
            <div class="discussion-card-header">
                <h2>Form Diskusi</h2>
            </div>
            <div class="discussion-card-body">
                <form method="post">
                    <div class="form-group">
                        <label for="title" class="form-label">Judul Diskusi</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="content" class="form-label">Konten Diskusi</label>
                        <textarea class="form-control" id="content" name="content" rows="10" required></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="classroom_detail.php?id=<?= $class_id ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                        <button type="submit" name="create_discussion" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> Buat Diskusi
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php else: ?>
        <div class="alert alert-danger">
            <h4><i class="fas fa-exclamation-triangle"></i> Kelas Tidak Ditemukan</h4>
            <p>Kelas yang Anda cari tidak ditemukan atau Anda tidak memiliki akses.</p>
            <a href="classroom.php" class="btn btn-primary mt-3">Kembali ke Classroom</a>
        </div>
    <?php endif; ?>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
