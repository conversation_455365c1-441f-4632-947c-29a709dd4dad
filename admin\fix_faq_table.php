<?php
session_start();
include '../config/config.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

echo "<h2>Fix FAQ Table Structure</h2>";

try {
    // 1. Cek apakah tabel faq ada
    echo "<h3>1. Checking FAQ table...</h3>";
    $check_table = "SHOW TABLES LIKE 'faq'";
    $table_result = $conn->query($check_table);
    
    if ($table_result && $table_result->num_rows > 0) {
        echo "<p>✅ FAQ table exists</p>";
        
        // 2. Cek struktur tabel saat ini
        echo "<h3>2. Current table structure:</h3>";
        $structure = $conn->query("DESCRIBE faq");
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 3. Cek apakah kolom is_hidden sudah ada
        echo "<h3>3. Checking is_hidden column...</h3>";
        $check_column = "SHOW COLUMNS FROM faq LIKE 'is_hidden'";
        $column_result = $conn->query($check_column);
        
        if ($column_result && $column_result->num_rows > 0) {
            echo "<p>✅ is_hidden column already exists</p>";
        } else {
            echo "<p>❌ is_hidden column does not exist</p>";
            echo "<p>Adding is_hidden column...</p>";
            
            // 4. Tambahkan kolom is_hidden
            $add_column = "ALTER TABLE faq ADD COLUMN is_hidden TINYINT(1) DEFAULT 0 AFTER category";
            
            if ($conn->query($add_column)) {
                echo "<p>✅ is_hidden column added successfully</p>";
            } else {
                echo "<p>❌ Failed to add is_hidden column: " . $conn->error . "</p>";
            }
        }
        
        // 5. Tampilkan struktur tabel setelah perubahan
        echo "<h3>4. Updated table structure:</h3>";
        $structure = $conn->query("DESCRIBE faq");
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 6. Test query dengan is_hidden
        echo "<h3>5. Testing query with is_hidden column...</h3>";
        try {
            $test_query = "SELECT id, question, COALESCE(is_hidden, 0) as is_hidden FROM faq LIMIT 5";
            $test_result = $conn->query($test_query);
            
            if ($test_result) {
                echo "<p>✅ Query with is_hidden column works correctly</p>";
                echo "<p>Sample data:</p>";
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>ID</th><th>Question</th><th>Is Hidden</th></tr>";
                while ($row = $test_result->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($row['id']) . "</td>";
                    echo "<td>" . htmlspecialchars(substr($row['question'], 0, 50)) . "...</td>";
                    echo "<td>" . htmlspecialchars($row['is_hidden']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>❌ Query test failed: " . $conn->error . "</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ Query test error: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p>❌ FAQ table does not exist</p>";
        echo "<p>Creating FAQ table...</p>";
        
        $create_table = "CREATE TABLE faq (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            question TEXT NOT NULL,
            answer TEXT NOT NULL,
            category VARCHAR(100) NOT NULL,
            is_hidden TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($create_table)) {
            echo "<p>✅ FAQ table created successfully with is_hidden column</p>";
        } else {
            echo "<p>❌ Failed to create FAQ table: " . $conn->error . "</p>";
        }
    }
    
    echo "<h3>6. Summary</h3>";
    echo "<p>✅ FAQ table structure has been fixed</p>";
    echo "<p>✅ is_hidden column is now available</p>";
    echo "<p>✅ You can now use the FAQ management features</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='manage_faq.php'>Go to FAQ Management</a></p>";
echo "<p><a href='../faq.php'>Go to FAQ Page</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h3 { color: #333; margin-top: 30px; }
</style>
