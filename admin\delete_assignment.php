<?php
/**
 * Delete Assignment Page for Admin
 * This page allows admins to delete an assignment
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if assignment ID and class ID are provided
$assignment_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;

if ($assignment_id <= 0 || $class_id <= 0) {
    $_SESSION['error_message'] = "ID tugas atau kelas tidak valid.";
    header('Location: manage_classes.php');
    exit();
}

// Check if the assignment exists and belongs to the specified class
$assignment_query = "SELECT * FROM training_assignments WHERE id = ? AND class_id = ?";
$stmt = $conn->prepare($assignment_query);
$stmt->bind_param("ii", $assignment_id, $class_id);
$stmt->execute();
$result = $stmt->get_result();
$assignment = $result->fetch_assoc();
$stmt->close();

if (!$assignment) {
    $_SESSION['error_message'] = "Tugas tidak ditemukan atau tidak termasuk dalam kelas yang ditentukan.";
    header("Location: manage_class.php?id=$class_id");
    exit();
}

// Check if there are any submissions for this assignment
$submissions_query = "SELECT COUNT(*) as submission_count FROM training_assignment_submissions WHERE assignment_id = ?";
$stmt = $conn->prepare($submissions_query);
$stmt->bind_param("i", $assignment_id);
$stmt->execute();
$result = $stmt->get_result();
$submission_data = $result->fetch_assoc();
$submission_count = $submission_data['submission_count'];
$stmt->close();

// Handle deletion
if ($submission_count > 0) {
    // Delete all submissions first
    $delete_submissions_query = "DELETE FROM training_assignment_submissions WHERE assignment_id = ?";
    $stmt = $conn->prepare($delete_submissions_query);
    $stmt->bind_param("i", $assignment_id);
    $stmt->execute();
    $stmt->close();
}

// Delete the assignment
$delete_assignment_query = "DELETE FROM training_assignments WHERE id = ?";
$stmt = $conn->prepare($delete_assignment_query);
$stmt->bind_param("i", $assignment_id);

if ($stmt->execute()) {
    $_SESSION['success_message'] = "Tugas berhasil dihapus.";
} else {
    $_SESSION['error_message'] = "Gagal menghapus tugas: " . $conn->error;
}

$stmt->close();

// Redirect back to manage class page
header("Location: manage_class.php?id=$class_id");
exit();
?>
