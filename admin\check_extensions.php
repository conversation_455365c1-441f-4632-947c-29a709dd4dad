<?php
$required_extensions = [
    'zip',
    'xml',
    'gd',
    'fileinfo',
    'libxml',
    'xmlreader',
    'xmlwriter',
    'simplexml'
];

echo "Checking required extensions:\n\n";
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ {$ext} is loaded\n";
    } else {
        echo "❌ {$ext} is NOT loaded\n";
    }
}

echo "\nDetailed XML configuration:\n";
if (function_exists('libxml_get_errors')) {
    echo "LibXML version: " . LIBXML_DOTTED_VERSION . "\n";
}