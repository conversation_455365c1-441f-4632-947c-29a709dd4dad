<?php
/**
 * Classroom Page for Pemohon (Applicant)
 * This page displays the classroom interface for the applicant
 */

include '../config/config.php';
include '../includes/functions.php';
include 'security.php';
include '../includes/class_role_helper.php';
// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['full_name'] ?? 'Pengguna';

// Check if the classroom tables exist
$classroom_tables_exist = true;
$required_tables = [
    'training_classes',
    'training_participants',
    'training_materials',
    'training_assignments',
    'training_assignment_submissions',
    'training_discussions',
    'training_discussion_comments'
];

foreach ($required_tables as $table) {
    $check_table_query = "SHOW TABLES LIKE '$table'";
    $table_result = $conn->query($check_table_query);
    if (!$table_result || $table_result->num_rows == 0) {
        $classroom_tables_exist = false;
        break;
    }
}

// Get active classes where the user is a participant
$active_classes = [];
if ($classroom_tables_exist) {
    $classes_query = "SELECT c.*, t.training_topic, t.training_type,
                      COUNT(DISTINCT p2.id) as participant_count,
                      COUNT(DISTINCT m.id) as material_count,
                      COUNT(DISTINCT a.id) as assignment_count,
                      MAX(p.role) as user_role
                      FROM training_classes c
                      JOIN training_submissions t ON c.training_id = t.id
                      JOIN training_participants p ON c.id = p.class_id AND p.user_id = ?
                      LEFT JOIN training_participants p2 ON c.id = p2.class_id
                      LEFT JOIN training_materials m ON c.id = m.class_id AND m.is_published = 1
                      LEFT JOIN training_assignments a ON c.id = a.class_id AND a.is_published = 1
                      WHERE c.status = 'active'
                      GROUP BY c.id
                      ORDER BY c.start_date DESC";

    $stmt = $conn->prepare($classes_query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $active_classes[] = $row;
    }
    $stmt->close();
}

// Get completed classes
$completed_classes = [];
if ($classroom_tables_exist) {
    $completed_query = "SELECT c.*, t.training_topic, t.training_type,
                        COUNT(DISTINCT p2.id) as participant_count,
                        MAX(p.role) as user_role
                        FROM training_classes c
                        JOIN training_submissions t ON c.training_id = t.id
                        JOIN training_participants p ON c.id = p.class_id AND p.user_id = ?
                        LEFT JOIN training_participants p2 ON c.id = p2.class_id
                        WHERE c.status = 'completed' OR p.status = 'completed'
                        GROUP BY c.id
                        ORDER BY c.end_date DESC";

    $stmt = $conn->prepare($completed_query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $completed_classes[] = $row;
    }
    $stmt->close();
}

// Get upcoming assignments
$upcoming_assignments = [];
if ($classroom_tables_exist) {
    $assignments_query = "SELECT a.*, c.title as class_title,
                         (SELECT COUNT(*) FROM training_assignment_submissions s WHERE s.assignment_id = a.id AND s.user_id = ?) as submission_count
                         FROM training_assignments a
                         JOIN training_classes c ON a.class_id = c.id
                         JOIN training_participants p ON c.id = p.class_id AND p.user_id = ?
                         WHERE a.is_published = 1
                         AND a.due_date >= NOW()
                         AND c.status = 'active'
                         AND NOT EXISTS (
                             SELECT 1 FROM training_assignment_submissions s
                             WHERE s.assignment_id = a.id AND s.user_id = ? AND s.status IN ('submitted', 'graded')
                         )
                         ORDER BY a.due_date ASC
                         LIMIT 5";

    $stmt = $conn->prepare($assignments_query);
    $stmt->bind_param("iii", $user_id, $user_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $upcoming_assignments[] = $row;
    }
    $stmt->close();
}

// Get recent discussions
$recent_discussions = [];
if ($classroom_tables_exist) {
    $discussions_query = "SELECT d.*, c.title as class_title, u.name as creator_name,
                         (SELECT COUNT(*) FROM training_discussion_comments dc WHERE dc.discussion_id = d.id) as comment_count
                         FROM training_discussions d
                         JOIN training_classes c ON d.class_id = c.id
                         JOIN users u ON d.created_by = u.id
                         JOIN training_participants p ON c.id = p.class_id AND p.user_id = ?
                         WHERE c.status = 'active'
                         ORDER BY d.created_at DESC
                         LIMIT 5";

    // First, let's check the structure of the users table to find the name column
    $user_table_query = "DESCRIBE users";
    $user_table_result = $conn->query($user_table_query);
    $name_column = 'user_name'; // Default to user_name if we can't find a better column

    if ($user_table_result) {
        while ($column = $user_table_result->fetch_assoc()) {
            // Look for common name column patterns
            if (in_array(strtolower($column['Field']), ['full_name', 'name', 'user_name', 'username', 'nama'])) {
                $name_column = $column['Field'];
                // Prefer full_name or name if available
                if (in_array(strtolower($column['Field']), ['full_name', 'name', 'nama'])) {
                    break;
                }
            }
        }
    }

    $discussions_query = "SELECT d.*, c.title as class_title, u.$name_column as creator_name,
                         (SELECT COUNT(*) FROM training_discussion_comments dc WHERE dc.discussion_id = d.id) as comment_count
                         FROM training_discussions d
                         JOIN training_classes c ON d.class_id = c.id
                         JOIN users u ON d.created_by = u.id
                         JOIN training_participants p ON c.id = p.class_id AND p.user_id = ?
                         WHERE c.status = 'active'
                         ORDER BY d.created_at DESC
                         LIMIT 5";

    $stmt = $conn->prepare($discussions_query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $recent_discussions[] = $row;
    }
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
:root {
    --primary-color: rgb(157, 0, 0);
    --primary-color-dark: rgb(127, 0, 0);
    --primary-color-light: rgba(157, 0, 0, 0.1);
    --secondary-color: #2c3e50;
    --success-color: #4CAF50;
    --success-color-light: #e8f5e9;
    --warning-color: #ffc107;
    --danger-color: #f44336;
    --info-color: #2196F3;
    --text-dark: #333;
    --text-light: #757575;
    --white: #ffffff;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
    --shadow-lg: 0 6px 15px rgba(0,0,0,0.1);
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
    --font-size-xs: 11px;
    --font-size-sm: 13px;
    --font-size-md: 16px;
    --font-size-lg: 20px;
    --font-size-xl: 24px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
}

.classroom-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.classroom-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--white);
    padding: var(--spacing-xl) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.classroom-header h1 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.classroom-header p {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-lg);
    max-width: 800px;
    opacity: 0.9;
}

.classroom-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.class-card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    display: flex;
    flex-direction: column;
}

.class-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.class-card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
}

.class-card-body {
    padding: var(--spacing-lg);
    flex-grow: 1;
}

.class-card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.class-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.class-topic {
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.class-stats {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.class-stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-dark);
    font-size: var(--font-size-sm);
}

.class-description {
    margin-bottom: var(--spacing-md);
    color: var(--text-dark);
}

.class-dates {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-dark);
}

.class-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.class-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-classroom {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    transition: all var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary-classroom {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
}

.btn-primary-classroom:hover {
    background-color: var(--primary-color-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
    color: var(--white);
}

.btn-outline-classroom {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline-classroom:hover {
    background-color: var(--primary-color-light);
    transform: translateY(-2px);
    color: var(--primary-color);
}

.sidebar-card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.sidebar-card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-card-header h2 {
    margin: 0;
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    gap: 10px;
}

.sidebar-card-body {
    padding: var(--spacing-lg);
}

.assignment-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: background-color var(--transition-fast);
}

.assignment-item:last-child {
    border-bottom: none;
}

.assignment-item:hover {
    background-color: rgba(0,0,0,0.02);
}

.assignment-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 5px;
}

.assignment-class {
    color: var(--text-dark);
    font-size: var(--font-size-xs);
    margin-bottom: 5px;
}

.assignment-due {
    color: var(--danger-color);
    font-size: var(--font-size-xs);
    display: flex;
    align-items: center;
    gap: 5px;
}

.discussion-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: background-color var(--transition-fast);
}

.discussion-item:last-child {
    border-bottom: none;
}

.discussion-item:hover {
    background-color: rgba(0,0,0,0.02);
}

.discussion-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 5px;
}

.discussion-meta {
    display: flex;
    justify-content: space-between;
    color: var(--text-dark);
    font-size: var(--font-size-xs);
    margin-bottom: 5px;
}

.discussion-class {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-dark);
}

.discussion-comments {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-dark);
}

.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
}

.empty-state .icon {
    font-size: 3rem;
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.empty-state .message {
    color: var(--text-dark);
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-md);
}

.setup-required {
    background-color: #f8d7da;
    color: #721c24;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

@media (max-width: 992px) {
    .classroom-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .classroom-header {
        padding: var(--spacing-lg);
    }

    .classroom-header h1 {
        font-size: 2rem;
    }

    .classroom-header p {
        font-size: 1rem;
    }

    .class-actions {
        flex-direction: column;
    }

    .btn-classroom {
        width: 100%;
    }
}
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="classroom-container">
    <div class="welcome-section">
        <h1>Classroom Training</h1>
        <p>Akses materi pembelajaran, tugas, dan diskusi untuk training yang Anda ikuti.</p>
        <a href="join_class.php" class="btn btn-light btn-lg mt-3">
            <i class="fas fa-sign-in-alt"></i> Gabung Kelas dengan Kode
        </a>
    </div>

    <?php if (!$classroom_tables_exist): ?>
    <div class="setup-required">
        <h3><i class="fas fa-exclamation-triangle"></i> Pengaturan Classroom Diperlukan</h3>
        <p>Fitur classroom belum diaktifkan. Silakan hubungi administrator sistem untuk mengaktifkan fitur ini.</p>
    </div>
    <?php else: ?>

    <div class="classroom-grid">
        <div class="main-content">
            <h2 class="">Kelas Aktif</h2>

            <?php if (empty($active_classes)): ?>
                <div class="empty-state">
                    <div class="icon"><i class="fas fa-chalkboard"></i></div>
                    <div class="message">Anda belum terdaftar di kelas training manapun</div>
                    <p>Silakan hubungi administrator atau trainer untuk mendaftarkan Anda ke kelas training.</p>
                </div>
            <?php else: ?>
                <?php foreach ($active_classes as $class): ?>
                    <div class="class-card">
                        <div class="class-card-header">
                            <div class="class-title"><?= htmlspecialchars($class['title']) ?></div>
                            <div class="class-topic"><?= htmlspecialchars($class['training_topic']) ?></div>
                        </div>
                        <div class="class-card-body">
                            <div class="class-stats">
                                <div class="class-stat">
                                    <i class="fas fa-users"></i>
                                    <span><?= $class['participant_count'] ?> Peserta</span>
                                </div>
                                <div class="class-stat">
                                    <i class="fas fa-book"></i>
                                    <span><?= $class['material_count'] ?> Materi</span>
                                </div>
                                <div class="class-stat">
                                    <i class="fas fa-tasks"></i>
                                    <span><?= $class['assignment_count'] ?> Tugas</span>
                                </div>
                            </div>

                            <div class="class-description">
                                <?= !empty($class['description']) ? nl2br(htmlspecialchars($class['description'])) : 'Tidak ada deskripsi' ?>
                            </div>

                            <div class="class-dates">
                                <?php if (!empty($class['start_date'])): ?>
                                <div class="class-date">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Mulai: <?= date('d M Y', strtotime($class['start_date'])) ?></span>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($class['end_date'])): ?>
                                <div class="class-date">
                                    <i class="fas fa-calendar-check"></i>
                                    <span>Selesai: <?= date('d M Y', strtotime($class['end_date'])) ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="class-card-footer">
                            <div class="class-type">
                                <span class="badge"><?= htmlspecialchars($class['training_type']) ?></span>
                            </div>
                            <div class="class-actions">
                                <a href="classroom_detail.php?id=<?= $class['id'] ?>" class="btn-classroom btn-primary-classroom">
                                    <i class="fas fa-chalkboard"></i> Masuk Kelas
                                </a>
                                <a href="classroom_materials.php?id=<?= $class['id'] ?>" class="btn-classroom btn-outline-classroom">
                                    <i class="fas fa-book"></i> Lihat Materi
                                </a>
                                <?php if ($class['user_role'] == 'instructor'): ?>
                                <!-- <a href="instructor_dashboard.php?class_id=<?= $class['id'] ?>" class="btn-classroom btn-outline-classroom" style="background-color: #28a745; color: white; border-color: #28a745;">
                                    <i class="fas fa-chalkboard-teacher"></i> Panel Instruktur
                                </a> -->
                                <?php elseif ($class['user_role'] == 'assistant'): ?>
                                <a href="assistant_dashboard.php?class_id=<?= $class['id'] ?>" class="btn-classroom btn-outline-classroom" style="background-color: #17a2b8; color: white; border-color: #17a2b8;">
                                    <i class="fas fa-user-cog"></i> Panel Asisten
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <?php if (!empty($completed_classes)): ?>
                <h2 class="mb-4 mt-5">Kelas Selesai</h2>

                <?php foreach ($completed_classes as $class): ?>
                    <div class="class-card">
                        <div class="class-card-header">
                            <div class="class-title"><?= htmlspecialchars($class['title']) ?></div>
                            <div class="class-topic"><?= htmlspecialchars($class['training_topic']) ?></div>
                        </div>
                        <div class="class-card-body">
                            <div class="class-stats">
                                <div class="class-stat">
                                    <i class="fas fa-users"></i>
                                    <span><?= $class['participant_count'] ?> Peserta</span>
                                </div>
                            </div>

                            <div class="class-description">
                                <?= !empty($class['description']) ? nl2br(htmlspecialchars($class['description'])) : 'Tidak ada deskripsi' ?>
                            </div>

                            <div class="class-dates">
                                <?php if (!empty($class['end_date'])): ?>
                                <div class="class-date">
                                    <i class="fas fa-flag-checkered"></i>
                                    <span>Selesai pada: <?= date('d M Y', strtotime($class['end_date'])) ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="class-card-footer">
                            <div class="class-type">
                                <span class="badge bg-secondary">Selesai</span>
                                <span class="badge bg-info"><?= htmlspecialchars($class['training_type']) ?></span>
                            </div>
                            <div class="class-actions">
                                <a href="classroom_detail.php?id=<?= $class['id'] ?>" class="btn-classroom btn-outline-classroom">
                                    <i class="fas fa-chalkboard"></i> Lihat Kelas
                                </a>
                                <?php if ($class['user_role'] == 'instructor'): ?>
                                <a href="instructor_dashboard.php?class_id=<?= $class['id'] ?>" class="btn-classroom btn-outline-classroom" style="background-color: #28a745; color: white; border-color: #28a745;">
                                    <i class="fas fa-chalkboard-teacher"></i> Panel Instruktur
                                </a>
                                <?php elseif ($class['user_role'] == 'assistant'): ?>
                                <a href="assistant_dashboard.php?class_id=<?= $class['id'] ?>" class="btn-classroom btn-outline-classroom" style="background-color: #17a2b8; color: white; border-color: #17a2b8;">
                                    <i class="fas fa-user-cog"></i> Panel Asisten
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <div class="sidebar">
            <div class="sidebar-card">
                <div class="sidebar-card-header">
                    <h2><i class="fas fa-tasks"></i> Tugas Mendatang</h2>
                </div>
                <div class="sidebar-card-body">
                    <?php if (empty($upcoming_assignments)): ?>
                        <div class="empty-state">
                            <div class="icon"><i class="fas fa-check-circle"></i></div>
                            <div class="message">Tidak ada tugas yang perlu dikerjakan</div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($upcoming_assignments as $assignment): ?>
                            <div class="form-container">
                                <div class="assignment-title"><?= htmlspecialchars($assignment['title']) ?></div>
                                <div class="assignment-class">
                                    <i class="fas fa-chalkboard"></i> <?= htmlspecialchars($assignment['class_title']) ?>
                                </div>
                                <div class="assignment-due">
                                    <i class="fas fa-clock"></i> Tenggat: <?= date('d M Y H:i', strtotime($assignment['due_date'])) ?>
                                </div>
                                <div class="mt-2">
                                    <a href="classroom_assignment.php?id=<?= $assignment['id'] ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> Kerjakan
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="sidebar-card">
                <div class="sidebar-card-header">
                    <h2><i class="fas fa-comments"></i> Diskusi Terbaru</h2>
                </div>
                <div class="sidebar-card-body">
                    <?php if (empty($recent_discussions)): ?>
                        <div class="empty-state">
                            <div class="icon"><i class="fas fa-comments"></i></div>
                            <div class="message">Belum ada diskusi</div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recent_discussions as $discussion): ?>
                            <div class="form-container">
                                <div class="discussion-title"><?= htmlspecialchars($discussion['title']) ?></div>
                                <div class="discussion-meta">
                                    <div class="discussion-class">
                                        <i class="fas fa-chalkboard"></i> <?= htmlspecialchars($discussion['class_title']) ?>
                                    </div>
                                    <div class="discussion-comments">
                                        <i class="fas fa-comment"></i> <?= $discussion['comment_count'] ?>
                                    </div>
                                </div>
                                <div class="discussion-author">
                                    <small><i class="fas fa-user"></i> <?= htmlspecialchars($discussion['creator_name']) ?></small>
                                    <small class="ms-2"><i class="fas fa-clock"></i> <?= date('d M Y', strtotime($discussion['created_at'])) ?></small>
                                </div>
                                <div class="mt-2">
                                    <a href="classroom_discussion.php?id=<?= $discussion['id'] ?>" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-eye"></i> Lihat Diskusi
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include '../config/footer.php'; ?>

<script>
    // Add any JavaScript functionality here
    document.addEventListener('DOMContentLoaded', function() {
        // Animation for class cards
        const classCards = document.querySelectorAll('.class-card');
        classCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100 * index);
        });
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
