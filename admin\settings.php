<?php
session_start();
include '../config/config.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Cek dan buat tabel activity_logs jika belum ada
try {
    $tableCheckQuery = "SHOW TABLES LIKE 'activity_logs'";
    $tableExists = $conn->query($tableCheckQuery)->num_rows > 0;

    if (!$tableExists) {
        $createTableQuery = "CREATE TABLE activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(255) NOT NULL,
            category VARCHAR(50) NOT NULL,
            timestamp DATETIME NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )";
        $conn->query($createTableQuery);

        // Buat indeks untuk mempercepat query
        $conn->query("CREATE INDEX idx_activity_logs_category ON activity_logs(category)");
        $conn->query("CREATE INDEX idx_activity_logs_timestamp ON activity_logs(timestamp)");
        $conn->query("CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id)");
    }
} catch (Exception $e) {
    // Abaikan error, tidak kritis untuk fungsi utama
}

// Log semua request untuk debugging
error_log("Request method: " . $_SERVER['REQUEST_METHOD']);
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("POST data: " . print_r($_POST, true));
}

// Fungsi untuk memeriksa dan menambahkan kolom jika tidak ada
function check_and_add_column($conn, $table, $column, $definition) {
    $result = $conn->query("SHOW COLUMNS FROM $table LIKE '$column'");
    if ($result->num_rows == 0) {
        error_log("Column '$column' does not exist in the $table table. Attempting to add it...");

        $alter_query = "ALTER TABLE $table ADD COLUMN $column $definition";
        if ($conn->query($alter_query) === TRUE) {
            error_log("Column '$column' added successfully.");
            return true;
        } else {
            error_log("Error adding column '$column': " . $conn->error);
            return false;
        }
    }
    return true; // Kolom sudah ada
}

// Periksa dan tambahkan kolom yang mungkin hilang
check_and_add_column($conn, "settings", "encrypt_password", "TINYINT(1) DEFAULT 1");
check_and_add_column($conn, "settings", "password_reset_days", "INT DEFAULT 90");

// Handle form submission untuk update pengaturan
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Identifikasi tab yang aktif berdasarkan field yang dikirim
        if (isset($_POST['app_name'])) {
            error_log("Processing app_name update. Value: " . $_POST['app_name']);

            // Update pengaturan umum
            $stmt = $conn->prepare("UPDATE settings SET
                app_name = ?,
                timezone = ?
                WHERE id = 1");
            $stmt->bind_param("ss",
                $_POST['app_name'],
                $_POST['timezone']
            );

            error_log("Prepared statement for app_name update");

            // Handle upload logo
            if (isset($_FILES['company_logo']) && $_FILES['company_logo']['error'] == 0) {
                error_log("Processing logo upload");

                $allowed = ['jpg', 'jpeg', 'png'];
                $filename = $_FILES['company_logo']['name'];
                $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

                if (in_array($ext, $allowed)) {
                    // Create logos directory if it doesn't exist
                    $logos_dir = "../asset/picture/logos";
                    if (!file_exists($logos_dir)) {
                        mkdir($logos_dir, 0755, true);
                    }

                    // Generate unique filename to prevent caching issues
                    $filename = "company_logo_" . time() . ".{$ext}";
                    $upload_path = "{$logos_dir}/{$filename}";
                    $relative_path = "../asset/picture/logos/{$filename}";

                    if (move_uploaded_file($_FILES['company_logo']['tmp_name'], $upload_path)) {
                        error_log("Logo uploaded successfully: " . $relative_path);

                        $stmt = $conn->prepare("UPDATE settings SET company_logo = ? WHERE id = 1");
                        $stmt->bind_param("s", $relative_path);
                        $stmt->execute();
                    } else {
                        error_log("Failed to move uploaded logo file");
                    }
                } else {
                    error_log("Invalid logo file extension: " . $ext);
                }
            }
        }
        elseif (isset($_POST['smtp_server'])) {
            // Update pengaturan email
            $stmt = $conn->prepare("UPDATE settings SET
                smtp_server = ?,
                smtp_port = ?,
                smtp_password = ?,
                sender_email = ?,
                sender_name = ?,
                smtp_encryption = ?
                WHERE id = 1");
            $stmt->bind_param("sissss",
                $_POST['smtp_server'],
                $_POST['smtp_port'],
                $_POST['smtp_password'], // Tambahkan smtp_password
                $_POST['sender_email'],
                $_POST['sender_name'],
                $_POST['smtp_encryption']
            );
        }
        elseif (isset($_POST['login_attempts'])) {
            // Update pengaturan keamanan
            // Siapkan nilai untuk checkbox
            $encrypt_password = isset($_POST['encrypt_password']) ? 1 : 0;
            $require_uppercase = isset($_POST['require_uppercase']) ? 1 : 0;
            $require_number = isset($_POST['require_number']) ? 1 : 0;
            $require_special = isset($_POST['require_special']) ? 1 : 0;
            $min_password_length = intval($_POST['min_password_length']);

            // Pastikan kolom yang diperlukan ada
            if (check_and_add_column($conn, "settings", "encrypt_password", "TINYINT(1) DEFAULT 1") &&
                check_and_add_column($conn, "settings", "password_reset_days", "INT DEFAULT 90")) {

                $stmt = $conn->prepare("UPDATE settings SET
                    encrypt_password = ?,
                    password_reset_days = ?,
                    login_attempts = ?,
                    block_time = ?,
                    require_uppercase = ?,
                    require_number = ?,
                    require_special = ?,
                    min_password_length = ?,
                    password_expiry = ?
                    WHERE id = 1");
                $stmt->bind_param("iiiiiiiii",
                    $encrypt_password,
                    $_POST['password_reset_days'],
                    $_POST['login_attempts'],
                    $_POST['block_time'],
                    $require_uppercase,
                    $require_number,
                    $require_special,
                    $min_password_length,
                    $_POST['password_expiry']
                );
            } else {
                throw new Exception("Failed to add required columns to settings table.");
            }
        }
        elseif (isset($_POST['backup_schedule'])) {
            // Update pengaturan backup
            $compress_backup = isset($_POST['compress_backup']) ? 1 : 0;
            $stmt = $conn->prepare("UPDATE settings SET
                backup_schedule = ?,
                backup_location = ?,
                backup_retention = ?,
                backup_time = ?,
                compress_backup = ?
                WHERE id = 1");
            $stmt->bind_param("ssisi",
                $_POST['backup_schedule'],
                $_POST['backup_location'],
                $_POST['backup_retention'],
                $_POST['backup_time'],
                $compress_backup
            );

            // Jika berhasil update, jadwalkan backup otomatis
            if ($stmt->execute()) {
                // Redirect ke halaman schedule_backup.php setelah update berhasil
                $_SESSION['schedule_backup'] = true;
            }
        }

        if (isset($stmt)) {
            // Tambahkan log untuk debugging
            error_log("Executing settings update query...");

            $result = $stmt->execute();

            if ($result) {
                // Jika query berhasil dijalankan, anggap berhasil meskipun tidak ada perubahan
                $_SESSION['success'] = "Pengaturan berhasil diperbarui";
                error_log("Settings update successful. app_name: " . $_POST['app_name']);

                // Force clear cache
                if (function_exists('opcache_reset')) {
                    opcache_reset();
                }
            } else {
                $_SESSION['error'] = "Terjadi kesalahan saat menyimpan pengaturan: " . $stmt->error;
                error_log("Settings update failed: " . $stmt->error);
            }
        }

    } catch (Exception $e) {
        $_SESSION['error'] = "Terjadi kesalahan: " . $e->getMessage();
    }

    header("Location: settings.php");
    exit();
}

// Ambil pengaturan saat ini dari database
$query = "SELECT * FROM settings WHERE id = 1";
$result = $conn->query($query);
$settings = $result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
/* Main Container Styles */
.settings-container {
    max-width: 1200px;
    margin: 70px auto 30px; /* Add top margin to prevent navbar overlap */
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    position: relative;
    z-index: 1; /* Ensure it's above the navbar */
}

@media screen and (max-width: 768px) {
    .settings-container {
        margin-top: 60px; /* Adjust for smaller screens */
        padding: 15px;
    }
}

@media screen and (max-width: 480px) {
    .settings-container {
        margin-top: 55px; /* Adjust for mobile screens */
        padding: 10px;
    }
}

/* Page Header Styles */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 15px;
}

.page-header h1 {
    font-size: 28px;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
}

.page-header h1 i {
    margin-right: 12px;
    color: #BF0000;
}

/* Back Button Styles */
.back-btn {
    display: inline-flex;
    align-items: center;
    padding: 10px 20px;
    background: #666;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.back-btn:hover {
    color: #a50000;
    background: #444;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.back-btn i {
    margin-right: 8px;
}

/* Card Styles */
.settings-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #eaeaea;
}

.settings-card:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.settings-card-header {
    background: #BF0000;
    color: white;
    padding: 18px 25px;
    font-size: 1.2em;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.settings-card-header i {
    margin-right: 10px;
    font-size: 1.1em;
}

.settings-card-body {
    padding: 25px;
}

/* Form Styles */
.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #333;
    font-size: 15px;
}

.form-group .help-text {
    display: block;
    font-size: 13px;
    color: #666;
    margin-top: 5px;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #f9f9f9;
}

.form-control:focus {
    border-color: #BF0000;
    outline: none;
    box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
    background-color: #fff;
}

/* Button Styles */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn i {
    margin-right: 8px;
}

.btn-save {
    background: #BF0000;
    color: white;
}

.btn-save:hover {
    color: white;
    background: #990000;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    color: white;
    background: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-test {
    background: #28a745;
    color: white;
}

.btn-test:hover {
    color: white;
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-reset {
    background: #dc3545;
    color: white;
}

.btn-reset:hover {
    color: white;
    background: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.button-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

/* Alert Styles */
.alert {
    padding: 15px 20px;
    margin-bottom: 25px;
    border: 1px solid transparent;
    border-radius: 8px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.alert i {
    margin-right: 10px;
    font-size: 18px;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Tab Styles */
.tab-container {
    margin-bottom: 30px;
}

.tab-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    border-bottom: 1px solid #eaeaea;
    padding-bottom: 15px;
}

.tab-button {
    padding: 12px 20px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    color: #555;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.tab-button i {
    margin-right: 8px;
    font-size: 16px;
}

.tab-button:hover {
    color: #a50000;
    background: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.tab-button.active {
    background: #BF0000;
    color: white;
    border-color: #BF0000;
    box-shadow: 0 4px 8px rgba(191, 0, 0, 0.2);
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Tooltip Styles */
.tooltip-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background: #6c757d;
    color: white;
    border-radius: 50%;
    font-size: 12px;
    margin-left: 8px;
    cursor: help;
    position: relative;
    z-index: 1991;
}

.tooltip-text {
    visibility: hidden;
    width: 250px;
    background-color: #333;
    color: #fff;
    text-align: left;
    border-radius: 6px;
    padding: 10px;
    position: absolute;
    z-index: 1991;
    bottom: 125%;
    left: 50%;
    margin-left: -125px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 13px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.tooltip-icon:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Logo Preview Styles */
.current-logo {
    margin-bottom: 15px;
    padding: 15px;
    border: 1px dashed #ddd;
    border-radius: 6px;
    text-align: center;
    background: #f9f9f9;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 120px;
}

.logo-preview {
    max-width: 250px;
    max-height: 100px;
    width: auto;
    height: auto;
    display: block;
    margin: 0 auto;
    object-fit: contain;
}

/* Activity Log Styles */
.activity-log {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #eaeaea;
    border-radius: 6px;
    margin-top: 20px;
}

.log-entry {
    padding: 12px 15px;
    border-bottom: 1px solid #eaeaea;
    font-size: 14px;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-entry .timestamp {
    color: #6c757d;
    font-size: 12px;
    margin-left: 10px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .tab-buttons {
        flex-wrap: wrap;
    }

    .tab-button {
        flex: 1 1 calc(50% - 10px);
        justify-content: center;
    }

    .button-group {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}
</style>

<body>
    <?php include '../config/navbarb.php'; ?>
    <div class="container-form">
    <div class="settings-container">
        <div class="page-header">
            <h1><i class="fas fa-cogs"></i> Pengaturan Sistem</h1>
            <!-- <div>
                <a href="setup_settings.php" class="back-btn" style="margin-right: 10px;color: white; background: #28a745;">
                    <i class="fas fa-database"></i> Setup Database
                </a>
                <a href="setup_security_php.php" class="back-btn" style="margin-right: 10px;color: white; background: #6f42c1;">
                    <i class="fas fa-shield-alt"></i> Setup Keamanan
                </a>
                <a href="test_connection.php" class="back-btn" style="margin-right: 10px;color: white; background: #dc3545;">
                    <i class="fas fa-heartbeat"></i> Test Koneksi
                </a>
                <a href="test_settings.php" class="back-btn" style="margin-right: 10px;color: white; background: #007bff;">
                    <i class="fas fa-vial"></i> Test Pengaturan
                </a>
                <a href="test_security.php" class="back-btn" style="margin-right: 10px;color: white; background: #fd7e14;">
                    <i class="fas fa-key"></i> Test Keamanan
                </a>
            </div> -->
        </div>

        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?= $_SESSION['success'] ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <?= $_SESSION['error'] ?>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['info'])): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <?= $_SESSION['info'] ?>
                <?php unset($_SESSION['info']); ?>
            </div>
        <?php endif; ?>

        <div class="tab-container">
            <div class="tab-buttons">
                <button type="button" class="tab-button active" onclick="showTab('general', event)">
                    <i class="fas fa-sliders-h"></i> Umum
                </button>
                <button type="button" class="tab-button" onclick="showTab('email', event)">
                    <i class="fas fa-envelope"></i> Email
                </button>
                <button type="button" class="tab-button" onclick="showTab('email_troubleshooting', event)">
                    <i class="fas fa-wrench"></i> Troubleshooting Email
                </button>
                <button type="button" class="tab-button" onclick="showTab('security', event)">
                    <i class="fas fa-shield-alt"></i> Keamanan
                </button>
                <button type="button" class="tab-button" onclick="showTab('backup', event)">
                    <i class="fas fa-database"></i> Backup
                </button>
                <button type="button" class="tab-button" onclick="showTab('logs', event)">
                    <i class="fas fa-history"></i> Log Aktivitas
                </button>
            </div>

            <!-- Tab Pengaturan Umum -->
            <div id="general" class="tab-content active">
                <div class="settings-card">
                    <div class="settings-card-header">
                        <i class="fas fa-sliders-h"></i> Pengaturan Umum
                    </div>
                    <div class="settings-card-body">
                        <form method="POST" enctype="multipart/form-data" id="generalSettingsForm">
                            <div class="form-group">
                                <label>
                                    Nama Aplikasi
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Nama aplikasi yang akan ditampilkan pada judul halaman dan header aplikasi.</span>
                                    </span>
                                </label>
                                <input type="text"
                                       class="form-control"
                                       name="app_name"
                                       value="<?= htmlspecialchars($settings['app_name'] ?? '') ?>"
                                       placeholder="Masukkan nama aplikasi"
                                       required>
                                <span class="help-text">Nama ini akan ditampilkan di header dan title halaman</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    Logo Perusahaan
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Upload logo perusahaan dalam format JPG atau PNG. Ukuran maksimal 2MB.</span>
                                    </span>
                                </label>
                                <?php if (!empty($settings['company_logo'])): ?>
                                    <div class="current-logo">
                                        <img src="<?= htmlspecialchars($settings['company_logo']) ?>"
                                             alt="Logo"
                                             class="logo-preview">
                                    </div>
                                <?php endif; ?>
                                <div id="logo-preview-container" style="display: none;" class="current-logo">
                                    <img id="logo-preview" class="logo-preview" alt="Preview Logo">
                                </div>
                                <input type="file"
                                       class="form-control"
                                       name="company_logo"
                                       id="company_logo"
                                       accept="image/jpeg,image/png">
                                <span class="help-text">Format yang didukung: JPG, JPEG, PNG. Ukuran maksimal 2MB.<br>
                                Untuk hasil terbaik, gunakan logo dengan rasio 4:1 atau 3:1 (lebar:tinggi) dan latar belakang transparan (PNG).</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    Zona Waktu
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Pilih zona waktu yang sesuai dengan lokasi perusahaan Anda.</span>
                                    </span>
                                </label>
                                <select class="form-control" name="timezone">
                                    <option value="Asia/Jakarta" <?= ($settings['timezone'] ?? '') == 'Asia/Jakarta' ? 'selected' : '' ?>>
                                        Asia/Jakarta (WIB)
                                    </option>
                                    <option value="Asia/Makassar" <?= ($settings['timezone'] ?? '') == 'Asia/Makassar' ? 'selected' : '' ?>>
                                        Asia/Makassar (WITA)
                                    </option>
                                    <option value="Asia/Jayapura" <?= ($settings['timezone'] ?? '') == 'Asia/Jayapura' ? 'selected' : '' ?>>
                                        Asia/Jayapura (WIT)
                                    </option>
                                </select>
                                <span class="help-text">Zona waktu ini akan digunakan untuk semua tanggal dan waktu dalam aplikasi</span>
                            </div>

                            <div class="button-group">
                                <button type="submit" class="btn btn-save" style="color: white;">
                                    <i class="fas fa-save"></i> Simpan Perubahan
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetForm('generalSettingsForm')" style="color: white;">
                                    <i class="fas fa-undo"></i> Reset Form
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Tab Pengaturan Email -->
            <div id="email" class="tab-content">
                <div class="settings-card">
                    <div class="settings-card-header">
                        <i class="fas fa-envelope"></i> Pengaturan Email
                    </div>
                    <div class="settings-card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Panduan Pengaturan Email:</strong>
                            <ul style="margin-top: 10px; margin-bottom: 0;">
                                <li>Untuk Gmail: Gunakan SMTP server <code>smtp.gmail.com</code>, port <code>587</code>, dan enkripsi <code>TLS</code>.</li>
                                <li>Jika menggunakan Gmail dengan 2FA, Anda harus menggunakan <a href="https://support.google.com/accounts/answer/185833" target="_blank">App Password</a>.</li>
                                <li>Untuk Outlook/Hotmail: Gunakan SMTP server <code>smtp.office365.com</code>, port <code>587</code>, dan enkripsi <code>TLS</code>.</li>
                                <li>Untuk Yahoo Mail: Gunakan SMTP server <code>smtp.mail.yahoo.com</code>, port <code>587</code>, dan enkripsi <code>TLS</code>.</li>
                                <li><a href="email_setup_guide.php"><strong>Lihat panduan lengkap pengaturan email</strong></a></li>
                            </ul>
                        </div>
                        <form method="POST" id="emailSettingsForm">
                            <div class="form-group">
                                <label>
                                    SMTP Server
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Alamat server SMTP yang digunakan untuk mengirim email. Contoh: smtp.gmail.com</span>
                                    </span>
                                </label>
                                <input type="text" class="form-control" name="smtp_server" id="smtp_server"
                                       value="<?= htmlspecialchars($settings['smtp_server'] ?? '') ?>"
                                       placeholder="contoh: smtp.gmail.com">
                                <span class="help-text">Server SMTP untuk pengiriman email notifikasi</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    SMTP Port
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Port yang digunakan untuk koneksi SMTP. Biasanya 587 untuk TLS atau 465 untuk SSL.</span>
                                    </span>
                                </label>
                                <input type="number" class="form-control" name="smtp_port" id="smtp_port"
                                       value="<?= htmlspecialchars($settings['smtp_port'] ?? '587') ?>"
                                       placeholder="587 untuk TLS, 465 untuk SSL">
                                <span class="help-text">Port 587 (TLS) atau 465 (SSL)</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    Email Pengirim
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Alamat email yang akan digunakan sebagai pengirim email notifikasi.</span>
                                    </span>
                                </label>
                                <input type="email" class="form-control" name="sender_email" id="sender_email"
                                       value="<?= htmlspecialchars($settings['sender_email'] ?? '') ?>"
                                       placeholder="contoh: <EMAIL>">
                                <span class="help-text">Email ini akan muncul sebagai pengirim di semua notifikasi</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    Password SMTP
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Password untuk akun email pengirim. Untuk Gmail, gunakan App Password jika 2FA diaktifkan.</span>
                                    </span>
                                </label>
                                <div style="position: relative;">
                                    <input type="password" class="form-control" name="smtp_password" id="smtp_password"
                                           value="<?= htmlspecialchars($settings['smtp_password'] ?? '') ?>"
                                           placeholder="Masukkan password email">
                                    <button type="button" onclick="togglePasswordVisibility('smtp_password')"
                                            style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer;">
                                        <i class="fas fa-eye" id="smtp_password_toggle"></i>
                                    </button>
                                </div>
                                <span class="help-text">Password akan disimpan dengan aman</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    Nama Pengirim
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Nama yang akan ditampilkan sebagai pengirim email.</span>
                                    </span>
                                </label>
                                <input type="text" class="form-control" name="sender_name" id="sender_name"
                                       value="<?= htmlspecialchars($settings['sender_name'] ?? '') ?>"
                                       placeholder="Training Center PAS">
                                <span class="help-text">Nama ini akan muncul sebagai pengirim di email</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    Enkripsi SMTP
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Jenis enkripsi yang digunakan untuk koneksi SMTP.</span>
                                    </span>
                                </label>
                                <select class="form-control" name="smtp_encryption">
                                    <option value="tls" <?= ($settings['smtp_encryption'] ?? '') == 'tls' ? 'selected' : '' ?>>TLS</option>
                                    <option value="ssl" <?= ($settings['smtp_encryption'] ?? '') == 'ssl' ? 'selected' : '' ?>>SSL</option>
                                    <option value="none" <?= ($settings['smtp_encryption'] ?? '') == 'none' ? 'selected' : '' ?>>Tidak ada enkripsi</option>
                                </select>
                                <span class="help-text">Pilih sesuai dengan kebutuhan server email Anda</span>
                            </div>

                            <div class="button-group">
                                <button type="submit" class="btn btn-save">
                                    <i class="fas fa-save"></i> Simpan Perubahan
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetForm('emailSettingsForm')">
                                    <i class="fas fa-undo"></i> Reset Form
                                </button>
                            </div>
                        </form>

                        <!-- Test Email Section -->
                        <div class="mt-4 pt-4 border-top">
                            <h5><i class="fas fa-paper-plane"></i> Test Pengiriman Email</h5>
                            <p class="text-muted">Gunakan form di bawah ini untuk menguji konfigurasi SMTP Anda.</p>

                            <form id="testEmailForm" class="mt-3">
                                <div class="form-group">
                                    <label for="test_email">Email Tujuan</label>
                                    <input type="email" class="form-control" id="test_email" name="test_email" required placeholder="Masukkan email tujuan untuk test">
                                    <small class="form-text text-muted">Email test akan dikirim ke alamat ini</small>
                                </div>

                                <button type="button" class="btn btn-primary" onclick="testEmailAdvanced()">
                                    <i class="fas fa-paper-plane"></i> Kirim Email Test
                                </button>
                                <a href="#" onclick="showTab('email_troubleshooting', event); return false;" class="btn btn-outline-secondary">
                                    <i class="fas fa-wrench"></i> Lihat Opsi Lanjutan & Troubleshooting
                                </a>
                            </form>

                            <div id="testEmailResult" class="mt-3" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Troubleshooting Email -->
            <div id="email_troubleshooting" class="tab-content">
                <div class="settings-card">
                    <div class="settings-card-header">
                        <i class="fas fa-wrench"></i> Troubleshooting Email
                    </div>
                    <div class="settings-card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Panduan Troubleshooting Email:</strong>
                            <p>Gunakan halaman ini untuk mendiagnosis dan mengatasi masalah pengiriman email.</p>
                        </div>

                        <!-- Test Email Lanjutan -->
                        <h5><i class="fas fa-paper-plane"></i> Test Email Lanjutan</h5>
                        <p class="text-muted">Gunakan form di bawah ini untuk menguji konfigurasi SMTP dengan opsi lanjutan.</p>

                        <form id="testEmailAdvancedForm" class="mt-3">
                            <div class="form-group">
                                <label for="test_email_adv">Email Tujuan</label>
                                <input type="email" class="form-control" id="test_email_adv" name="test_email" required placeholder="Masukkan email tujuan untuk test">
                                <small class="form-text text-muted">Email test akan dikirim ke alamat ini</small>
                            </div>

                            <div class="form-group">
                                <label for="test_port_adv">Port SMTP (Opsional)</label>
                                <select class="form-control" id="test_port_adv" name="test_port">
                                    <option value="">Gunakan port default (<?= htmlspecialchars($settings['smtp_port'] ?? '587') ?>)</option>
                                    <option value="25">25 (SMTP)</option>
                                    <option value="465">465 (SMTPS/SSL)</option>
                                    <option value="587">587 (SMTP/TLS)</option>
                                    <option value="2525">2525 (Alternatif)</option>
                                </select>
                                <small class="form-text text-muted">Pilih port alternatif jika port default tidak berfungsi</small>
                            </div>

                            <div class="form-group">
                                <label for="test_encryption_adv">Enkripsi SMTP (Opsional)</label>
                                <select class="form-control" id="test_encryption_adv" name="test_encryption">
                                    <option value="">Gunakan enkripsi default (<?= htmlspecialchars($settings['smtp_encryption'] ?? 'tls') ?>)</option>
                                    <option value="ssl">SSL</option>
                                    <option value="tls">TLS</option>
                                    <option value="none">Tanpa Enkripsi</option>
                                </select>
                                <small class="form-text text-muted">Pilih enkripsi alternatif jika enkripsi default tidak berfungsi</small>
                            </div>

                            <div class="form-check mb-3">
                                <input type="checkbox" class="form-check-input" id="debug_mode_adv" name="debug_mode" value="1" checked>
                                <label class="form-check-label" for="debug_mode_adv">Aktifkan Mode Debug (Tampilkan detail teknis)</label>
                            </div>

                            <button type="button" class="btn btn-primary" onclick="testEmailTroubleshooting()">
                                <i class="fas fa-paper-plane"></i> Kirim Email Test
                            </button>
                        </form>

                        <div id="testEmailAdvancedResult" class="mt-3" style="display: none;"></div>
                        <div id="debugOutputAdvanced" class="mt-3" style="display: none;">
                            <h6>Debug Output:</h6>
                            <pre class="bg-light p-3 border rounded" style="max-height: 300px; overflow-y: auto;"></pre>
                        </div>

                        <!-- Troubleshooting Guide -->
                        <div class="mt-5 pt-3 border-top">
                            <h5><i class="fas fa-info-circle"></i> Panduan Troubleshooting</h5>
                            <div class="accordion" id="troubleshootingAccordion">
                                <!-- Gmail Troubleshooting -->
                                <div class="card">
                                    <div class="card-header" id="gmailHeading">
                                        <h2 class="mb-0">
                                            <button class="btn btn-link btn-block text-left" type="button" data-toggle="collapse" data-target="#gmailCollapse" aria-expanded="false" aria-controls="gmailCollapse">
                                                Troubleshooting untuk Gmail
                                            </button>
                                        </h2>
                                    </div>
                                    <div id="gmailCollapse" class="collapse" aria-labelledby="gmailHeading" data-parent="#troubleshootingAccordion">
                                        <div class="card-body">
                                            <ol>
                                                <li>Pastikan Anda menggunakan App Password, bukan password akun biasa</li>
                                                <li>Untuk membuat App Password:
                                                    <ul>
                                                        <li>Aktifkan 2-Step Verification di akun Google Anda</li>
                                                        <li>Kunjungi <a href="https://myaccount.google.com/apppasswords" target="_blank">https://myaccount.google.com/apppasswords</a></li>
                                                        <li>Pilih "App" sebagai "Mail" dan "Device" sebagai "Other (Custom name)"</li>
                                                        <li>Masukkan nama (misalnya "Training Center PAS")</li>
                                                        <li>Klik "Generate" dan gunakan password yang dihasilkan</li>
                                                    </ul>
                                                </li>
                                                <li>Pastikan port 587 dan encryption TLS digunakan untuk Gmail</li>
                                                <li>Periksa apakah akun Gmail Anda tidak dalam status "suspicious activity"</li>
                                                <li>Periksa apakah Anda belum mencapai batas pengiriman harian Gmail (500 email per hari)</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>

                                <!-- Data Not Accepted Troubleshooting -->
                                <div class="card">
                                    <div class="card-header" id="dataNotAcceptedHeading">
                                        <h2 class="mb-0">
                                            <button class="btn btn-link btn-block text-left" type="button" data-toggle="collapse" data-target="#dataNotAcceptedCollapse" aria-expanded="false" aria-controls="dataNotAcceptedCollapse">
                                                Error "data not accepted"
                                            </button>
                                        </h2>
                                    </div>
                                    <div id="dataNotAcceptedCollapse" class="collapse" aria-labelledby="dataNotAcceptedHeading" data-parent="#troubleshootingAccordion">
                                        <div class="card-body">
                                            <ol>
                                                <li>Pastikan email pengirim (sender email) valid dan sesuai dengan akun SMTP</li>
                                                <li>Coba gunakan kombinasi port dan enkripsi yang berbeda:
                                                    <ul>
                                                        <li>Port 465 dengan enkripsi SSL</li>
                                                        <li>Port 587 dengan enkripsi TLS</li>
                                                        <li>Port 25 tanpa enkripsi (jika diizinkan oleh ISP)</li>
                                                    </ul>
                                                </li>
                                                <li>Jika menggunakan Gmail, pastikan Anda menggunakan App Password</li>
                                                <li>Pastikan email pengirim dan penerima tidak sama (beberapa server menolak ini)</li>
                                                <li>Periksa apakah ada batasan ukuran email dari provider</li>
                                                <li>Periksa apakah ada karakter khusus dalam konten email yang mungkin menyebabkan masalah</li>
                                                <li>Pastikan tidak ada firewall atau antivirus yang memblokir koneksi SMTP</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>

                                <!-- Alternative Solutions -->
                                <div class="card">
                                    <div class="card-header" id="alternativesHeading">
                                        <h2 class="mb-0">
                                            <button class="btn btn-link btn-block text-left" type="button" data-toggle="collapse" data-target="#alternativesCollapse" aria-expanded="false" aria-controls="alternativesCollapse">
                                                Solusi Alternatif
                                            </button>
                                        </h2>
                                    </div>
                                    <div id="alternativesCollapse" class="collapse" aria-labelledby="alternativesHeading" data-parent="#troubleshootingAccordion">
                                        <div class="card-body">
                                            <p>Jika Anda masih mengalami masalah dengan Gmail atau provider email lainnya, pertimbangkan untuk menggunakan layanan SMTP pihak ketiga:</p>
                                            <ol>
                                                <li><strong>SendGrid</strong>: Menawarkan 100 email gratis per hari
                                                    <ul>
                                                        <li>SMTP Server: smtp.sendgrid.net</li>
                                                        <li>Port: 587</li>
                                                        <li>Enkripsi: TLS</li>
                                                    </ul>
                                                </li>
                                                <li><strong>Mailgun</strong>: Menawarkan 10,000 email gratis per bulan
                                                    <ul>
                                                        <li>SMTP Server: smtp.mailgun.org</li>
                                                        <li>Port: 587</li>
                                                        <li>Enkripsi: TLS</li>
                                                    </ul>
                                                </li>
                                                <li><strong>Amazon SES</strong>: Sangat murah untuk volume email yang besar
                                                    <ul>
                                                        <li>SMTP Server: email-smtp.us-east-1.amazonaws.com (sesuaikan dengan region Anda)</li>
                                                        <li>Port: 587</li>
                                                        <li>Enkripsi: TLS</li>
                                                    </ul>
                                                </li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Keamanan -->
            <div id="security" class="tab-content">
                <div class="settings-card">
                    <div class="settings-card-header">
                        <i class="fas fa-shield-alt"></i> Pengaturan Keamanan
                    </div>
                    <div class="settings-card-body">
                        <form method="POST" id="securitySettingsForm">
                            <h5><i class="fas fa-lock"></i> Keamanan Aplikasi</h5>
                            <div class="form-group">
                                <a href="security_monitoring.php" class="btn btn-primary" style="text-decoration: none; color: white;">Memantau Keamanan Aplikasi</a>
                            </div>
                            <h5><i class="fas fa-lock"></i> Keamanan Login</h5>
                            <div class="form-group">
                                <label>
                                    Enkripsi Password
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Enkripsi password pengguna untuk keamanan.</span>
                                    </span>
                                </label>
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="encrypt_password" name="encrypt_password" value="1"
                                           <?= ($settings['encrypt_password'] ?? '1') == '1' ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="encrypt_password">Aktifkan enkripsi password</label>
                                </div>
                                <span class="help-text">Rekomendasi: Aktifkan untuk keamanan tingkat lanjut</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    Jangka Waktu Reset Password (hari)
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Jangka waktu untuk mengatur ulang password.</span>
                                    </span>
                                </label>
                                <input type="number" class="form-control" name="password_reset_days"
                                       value="<?= htmlspecialchars($settings['password_reset_days'] ?? '90') ?>"
                                       min="1" max="365">
                                <span class="help-text">Disarankan 90 hari sesuai standar keamanan</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    Batas Percobaan Login
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Jumlah maksimal percobaan login yang diizinkan sebelum akun diblokir sementara.</span>
                                    </span>
                                </label>
                                <input type="number" class="form-control" name="login_attempts"
                                       value="<?= htmlspecialchars($settings['login_attempts'] ?? '3') ?>"
                                       min="1" max="10">
                                <span class="help-text">Disarankan 3-5 kali percobaan</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    Waktu Blokir (menit)
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Durasi waktu akun akan diblokir setelah melebihi batas percobaan login.</span>
                                    </span>
                                </label>
                                <input type="number" class="form-control" name="block_time"
                                       value="<?= htmlspecialchars($settings['block_time'] ?? '15') ?>"
                                       min="5" max="1440">
                                <span class="help-text">Disarankan minimal 15 menit untuk keamanan</span>
                            </div>

                            <h5><i class="fas fa-key"></i> Keamanan Password</h5>
                            <div class="form-group">
                                <label>
                                    Masa Berlaku Password (hari)
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Jumlah hari sebelum pengguna diminta untuk mengganti password mereka.</span>
                                    </span>
                                </label>
                                <input type="number" class="form-control" name="password_expiry"
                                       value="<?= htmlspecialchars($settings['password_expiry'] ?? '30') ?>"
                                       min="30" max="365">
                                <span class="help-text">Disarankan 30 hari sesuai standar keamanan</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    Kompleksitas Password
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Atur persyaratan minimum untuk password pengguna.</span>
                                    </span>
                                </label>
                                <div class="checkbox-group">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="require_uppercase" name="require_uppercase" value="1"
                                               <?= ($settings['require_uppercase'] ?? '1') == '1' ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="require_uppercase">Wajib huruf besar</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="require_number" name="require_number" value="1"
                                               <?= ($settings['require_number'] ?? '1') == '1' ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="require_number">Wajib angka</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="require_special" name="require_special" value="1"
                                               <?= ($settings['require_special'] ?? '1') == '1' ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="require_special">Wajib karakter khusus</label>
                                    </div>
                                </div>
                                <span class="help-text">Persyaratan kompleksitas meningkatkan keamanan password</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    Panjang Minimum Password
                                    <span class="tooltip-icon">?
                                        <span class="tooltip-text">Jumlah minimum karakter yang dibutuhkan untuk password.</span>
                                    </span>
                                </label>
                                <input type="number" class="form-control" name="min_password_length"
                                       value="<?= htmlspecialchars($settings['min_password_length'] ?? '8') ?>"
                                       min="1" max="16">
                                <span class="help-text">Disarankan minimal 8 karakter</span>
                            </div>

                            <div class="button-group">
                                <button type="submit" class="btn btn-save">
                                    <i class="fas fa-save"></i> Simpan Perubahan
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetForm('securitySettingsForm')">
                                    <i class="fas fa-undo"></i> Reset Form
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Tab Backup -->
            <div id="backup" class="tab-content">
                <div class="settings-card">
                    <div class="settings-card-header">
                        <i class="fas fa-database"></i> Pengaturan Backup
                    </div>
                    <div class="settings-card-body">
                        <form method="POST" id="backupSettingsForm">
                            <div class="alert alert-info mb-4">
                                <i class="fas fa-info-circle"></i>
                                <strong>Tentang Backup Database</strong>
                                <p>Backup database sangat penting untuk melindungi data Anda. Atur jadwal backup otomatis atau lakukan backup manual kapan saja.</p>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>
                                            Jadwal Backup
                                        </label>
                                        <select class="form-control" name="backup_schedule">
                                            <option value="daily" <?= ($settings['backup_schedule'] ?? '') == 'daily' ? 'selected' : '' ?>>Setiap Hari</option>
                                            <option value="weekly" <?= ($settings['backup_schedule'] ?? '') == 'weekly' ? 'selected' : '' ?>>Setiap Minggu</option>
                                            <option value="monthly" <?= ($settings['backup_schedule'] ?? '') == 'monthly' ? 'selected' : '' ?>>Setiap Bulan</option>
                                            <option value="disabled" <?= ($settings['backup_schedule'] ?? '') == 'disabled' ? 'selected' : '' ?>>Nonaktifkan</option>
                                        </select>
                                        <span class="help-text">Seberapa sering backup otomatis dijalankan</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>
                                            Waktu Backup
                                        </label>
                                        <input type="time" class="form-control" name="backup_time"
                                               value="<?= htmlspecialchars($settings['backup_time'] ?? '01:00') ?>">
                                        <span class="help-text">Disarankan di luar jam kerja (01:00-05:00)</span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>
                                            Lokasi Backup
                                        </label>
                                        <input type="text" class="form-control" name="backup_location"
                                               value="<?= htmlspecialchars($settings['backup_location'] ?? '../backups') ?>">
                                        <span class="help-text">Folder tempat file backup disimpan</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>
                                            Jumlah Backup Disimpan
                                        </label>
                                        <input type="number" class="form-control" name="backup_retention"
                                               value="<?= htmlspecialchars($settings['backup_retention'] ?? '7') ?>"
                                               min="1" max="30">
                                        <span class="help-text">Backup lama akan dihapus otomatis</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="compress_backup" name="compress_backup" value="1"
                                           <?= ($settings['compress_backup'] ?? '1') == '1' ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="compress_backup">Kompres file backup (menghemat ruang penyimpanan)</label>
                                </div>
                            </div>

                            <div class="button-group">
                                <button type="submit" class="btn btn-save">
                                    <i class="fas fa-save"></i> Simpan Pengaturan
                                </button>
                            </div>

                            <div class="card mt-4 mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="fas fa-database"></i> Operasi Backup</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="d-grid gap-2">
                                                <button type="button" class="btn btn-success btn-lg btn-block mb-3" onclick="confirmManualBackup()">
                                                    <i class="fas fa-download"></i> Backup Sekarang
                                                </button>
                                                <p class="text-muted small">Buat backup database secara manual dengan metode standar</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-grid gap-2">
                                                <button type="button" class="btn btn-primary btn-lg btn-block mb-3" onclick="confirmScheduleBackup()">
                                                    <i class="fas fa-calendar-alt"></i> Atur Jadwal Backup
                                                </button>
                                                <p class="text-muted small">Aktifkan backup otomatis sesuai jadwal yang ditentukan</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" data-toggle="collapse" data-target="#advancedBackupOptions">
                                            <i class="fas fa-cog"></i> Opsi Lanjutan
                                        </button>
                                    </div>

                                    <div class="collapse mt-3" id="advancedBackupOptions">
                                        <div class="card card-body bg-light">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <button type="button" class="btn btn-outline-success btn-sm btn-block mb-2" onclick="confirmSimpleBackup()">
                                                        <i class="fas fa-database"></i> Backup Sederhana (PHP)
                                                    </button>
                                                    <p class="text-muted small">Metode alternatif tanpa mysqldump</p>
                                                </div>
                                                <div class="col-md-4">
                                                    <button type="button" class="btn btn-outline-info btn-sm btn-block mb-2" onclick="confirmAutoBackup()">
                                                        <i class="fas fa-play"></i> Jalankan Backup Otomatis
                                                    </button>
                                                    <p class="text-muted small">Jalankan proses backup otomatis</p>
                                                </div>
                                                <div class="col-md-4">
                                                    <button type="button" class="btn btn-outline-warning btn-sm btn-block mb-2" onclick="window.location.href='../test_auto_backup_run.php'">
                                                        <i class="fas fa-vial"></i> Test Backup Otomatis
                                                    </button>
                                                    <p class="text-muted small">Uji konfigurasi backup</p>
                                                </div>
                                            </div>

                                            <div class="mt-3">
                                                <button type="button" id="resetDbBtn" class="btn btn-outline-danger btn-sm" onclick="confirmResetDb()">
                                                    <i class="fas fa-database"></i> Atur ulang setupdb.sql
                                                </button>
                                                <p class="text-muted small">Ekspor struktur database saat ini ke file setupdb.sql</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Tab Log Aktivitas -->
            <div id="logs" class="tab-content">
                <div class="settings-card">
                    <div class="settings-card-header">
                        <i class="fas fa-history"></i> Log Aktivitas Pengaturan
                    </div>
                    <div class="settings-card-body">
                        <div class="form-group">
                            <label>
                                Filter Log
                                <span class="tooltip-icon">?
                                    <span class="tooltip-text">Filter log berdasarkan jenis aktivitas.</span>
                                </span>
                            </label>
                            <select class="form-control" id="logFilter" onchange="filterLogs()">
                                <option value="all">Semua Aktivitas</option>
                                <option value="general">Pengaturan Umum</option>
                                <option value="email">Pengaturan Email</option>
                                <option value="security">Pengaturan Keamanan</option>
                                <option value="backup">Pengaturan Backup</option>
                            </select>
                        </div>

                        <div class="activity-log" id="activityLogContainer">
                            <?php
                            // Include activity logger jika belum di-include
                            if (!function_exists('get_activity_logs')) {
                                include '../config/activity_logger.php';
                            }

                            // Ambil log aktivitas terbaru (hanya untuk pengaturan)
                            $logs = get_activity_logs(['category' => 'settings'], 10, 0);

                            if (empty($logs)) {
                                echo '<div class="log-entry">Belum ada aktivitas yang tercatat.</div>';
                            } else {
                                foreach ($logs as $log) {
                                    echo '<div class="log-entry" data-category="' . $log['category'] . '">';
                                    echo '<strong>' . htmlspecialchars($log['action']) . '</strong> oleh ' . htmlspecialchars($log['user_name'] ?? 'Unknown');
                                    echo '<span class="timestamp">' . htmlspecialchars(date('d M Y H:i:s', strtotime($log['timestamp']))) . '</span>';
                                    echo '</div>';
                                }
                            }
                            ?>
                        </div>

                        <div class="button-group">
                            <button type="button" class="btn btn-secondary" onclick="refreshLogs()" style="color: white;">
                                <i class="fas fa-sync-alt"></i> Refresh Log
                            </button>
                            <a href="activity_logs.php" class="btn btn-primary">
                                <i class="fas fa-list"></i> Lihat Semua Log
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <a href="dashboard.php" class="back-btn" style="color: white;">
                            <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
                        </a>
            </div>
        </div>
    </div>

    <?php include '../config/footer.php'; ?>

    <?php if (isset($_SESSION['schedule_backup']) && $_SESSION['schedule_backup']): ?>
    <script>
        // Redirect ke halaman schedule_backup.php setelah halaman dimuat
        document.addEventListener('DOMContentLoaded', function() {
            // Tampilkan tab backup
            showTab('backup', null);

            // Tanyakan pengguna apakah ingin mengatur jadwal backup
            if (confirmAction('Pengaturan backup berhasil disimpan. Apakah Anda ingin mengatur jadwal backup otomatis sekarang?')) {
                // Panggil fungsi untuk mengatur jadwal backup
                scheduleBackup();
            }

            // Hapus flag session
            <?php unset($_SESSION['schedule_backup']); ?>
        });
    </script>
    <?php endif; ?>

    <script>
    // Simpan tab aktif di localStorage
    function showTab(tabId, event) {
        // Sembunyikan semua tab content
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });

        // Hapus class active dari semua tab button
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });

        // Tampilkan tab yang dipilih
        document.getElementById(tabId).classList.add('active');

        // Tambah class active ke button yang diklik
        if (event) {
            const clickedButton = event.target.closest('.tab-button');
            if (clickedButton) {
                clickedButton.classList.add('active');
            }
        } else {
            // Jika tidak ada event, cari button berdasarkan tabId
            const buttons = document.querySelectorAll('.tab-button');
            for (let i = 0; i < buttons.length; i++) {
                if (buttons[i].getAttribute('onclick').includes(tabId)) {
                    buttons[i].classList.add('active');
                    break;
                }
            }
        }

        // Simpan tab aktif di localStorage
        localStorage.setItem('activeSettingsTab', tabId);
    }

    // Fungsi untuk konfirmasi reset database
    function confirmResetDb() {
        // Gunakan custom modal untuk konfirmasi
        CustomModal.confirm(
            'Apakah Anda yakin ingin mengatur ulang struktur database menggunakan setupdb.sql? <br><br><strong>Perhatian:</strong> Tindakan ini akan menjalankan script mysqldumpstructure.bat yang akan mengekspor struktur database saat ini ke file setupdb.sql.',
            'Konfirmasi Atur Ulang Database',
            {
                confirmText: 'Ya, Atur Ulang',
                cancelText: 'Batal',
                onConfirm: function() {
                    // Redirect ke run_dump.php
                    window.location.href = '../run_dump.php';
                }
            }
        );
    }

    // Fungsi untuk konfirmasi backup manual
    function confirmManualBackup() {
        CustomModal.confirm(
            'Apakah Anda yakin ingin membuat backup database secara manual sekarang?',
            'Konfirmasi Backup Manual',
            {
                confirmText: 'Ya, Buat Backup',
                cancelText: 'Batal',
                onConfirm: function() {
                    // Panggil fungsi asli tanpa konfirmasi lagi
                    const backupButton = document.querySelector('.btn-test');
                    const originalHTML = backupButton.innerHTML;
                    backupButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Membuat Backup...';
                    backupButton.disabled = true;

                    // Kirim request untuk membuat backup
                    fetch('create_backup.php', {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            CustomModal.success('Backup berhasil dibuat!<br>Lokasi: ' + data.file_path);
                        } else {
                            CustomModal.alert('Gagal membuat backup: ' + data.message, 'Error');
                        }
                    })
                    .catch(error => {
                        CustomModal.alert('Terjadi kesalahan: ' + error.message, 'Error');
                        console.error('Error:', error);
                    })
                    .finally(() => {
                        // Kembalikan button ke keadaan semula
                        backupButton.innerHTML = originalHTML;
                        backupButton.disabled = false;
                    });
                }
            }
        );
    }

    // Fungsi untuk konfirmasi backup sederhana
    function confirmSimpleBackup() {
        CustomModal.confirm(
            'Apakah Anda yakin ingin membuat backup database menggunakan metode PHP sederhana?<br><br>Metode ini tidak memerlukan mysqldump dan lebih kompatibel dengan berbagai lingkungan hosting.',
            'Konfirmasi Backup Sederhana',
            {
                confirmText: 'Ya, Lanjutkan',
                cancelText: 'Batal',
                onConfirm: function() {
                    // Redirect ke simple_backup.php
                    window.location.href = 'simple_backup.php';
                }
            }
        );
    }

    // Fungsi untuk konfirmasi backup otomatis
    function confirmAutoBackup() {
        CustomModal.confirm(
            'Apakah Anda yakin ingin menjalankan proses backup otomatis sekarang?<br><br>Ini akan menjalankan backup sesuai dengan pengaturan yang telah disimpan.',
            'Konfirmasi Backup Otomatis',
            {
                confirmText: 'Ya, Jalankan',
                cancelText: 'Batal',
                onConfirm: function() {
                    // Panggil fungsi asli tanpa konfirmasi lagi
                    const runButton = document.querySelector('.btn-info');
                    const originalHTML = runButton.innerHTML;
                    runButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menjalankan Backup...';
                    runButton.disabled = true;

                    // Kirim request untuk menjalankan backup otomatis
                    fetch('auto_backup.php', {
                        method: 'GET'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            CustomModal.success('Backup otomatis berhasil dijalankan!<br>Lokasi: ' + data.file_path);
                        } else {
                            CustomModal.alert('Gagal menjalankan backup otomatis: ' + data.message, 'Error');
                        }
                    })
                    .catch(error => {
                        CustomModal.alert('Terjadi kesalahan: ' + error.message, 'Error');
                        console.error('Error:', error);
                    })
                    .finally(() => {
                        // Kembalikan button ke keadaan semula
                        runButton.innerHTML = originalHTML;
                        runButton.disabled = false;
                    });
                }
            }
        );
    }

    // Fungsi untuk konfirmasi jadwal backup
    function confirmScheduleBackup() {
        CustomModal.confirm(
            'Apakah Anda yakin ingin mengatur jadwal backup otomatis?<br><br>Ini akan membuka halaman pengaturan jadwal backup yang memerlukan akses ke Task Scheduler (Windows) atau Cron (Linux/Unix).',
            'Konfirmasi Jadwal Backup',
            {
                confirmText: 'Ya, Atur Jadwal',
                cancelText: 'Batal',
                onConfirm: function() {
                    // Panggil fungsi asli tanpa konfirmasi lagi
                    const scheduleButton = document.querySelector('.btn-primary');
                    const originalHTML = scheduleButton.innerHTML;
                    scheduleButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Mengatur Jadwal...';
                    scheduleButton.disabled = true;

                    // Kirim request untuk mengatur jadwal backup
                    fetch('schedule_backup.php', {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            CustomModal.success('Jadwal backup berhasil diatur!<br>' + data.details);
                        } else {
                            CustomModal.alert('Gagal mengatur jadwal backup: ' + data.message, 'Error');
                            if (data.error) {
                                console.error('Error details:', data.error);
                            }
                        }
                    })
                    .catch(error => {
                        CustomModal.alert('Terjadi kesalahan: ' + error.message, 'Error');
                        console.error('Error:', error);
                    })
                    .finally(() => {
                        // Kembalikan button ke keadaan semula
                        scheduleButton.innerHTML = originalHTML;
                        scheduleButton.disabled = false;
                    });
                }
            }
        );
    }

    // Fungsi untuk reset form ke nilai default
    function resetForm(formId) {
        if (confirmAction('Apakah Anda yakin ingin mereset form ini ke nilai awal?')) {
            document.getElementById(formId).reset();
            // Reset logo preview jika ada
            if (formId === 'generalSettingsForm') {
                document.getElementById('logo-preview-container').style.display = 'none';
            }
        }
    }

    // Fungsi untuk toggle visibility password
    function togglePasswordVisibility(inputId) {
        const passwordInput = document.getElementById(inputId);
        const toggleIcon = document.getElementById(inputId + '_toggle');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    // Fungsi untuk preview logo sebelum upload
    function previewLogo(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            const previewContainer = document.getElementById('logo-preview-container');
            const previewImage = document.getElementById('logo-preview');

            reader.onload = function(e) {
                previewImage.src = e.target.result;
                previewContainer.style.display = 'block';
            }

            reader.readAsDataURL(file);
        }
    }

    // Fungsi untuk refresh log aktivitas
    function refreshLogs() {
        location.reload();
    }

    // Fungsi untuk filter log berdasarkan kategori
    function filterLogs() {
        const category = document.getElementById('logFilter').value;
        const logEntries = document.querySelectorAll('.log-entry');

        logEntries.forEach(entry => {
            if (category === 'all' || entry.getAttribute('data-category') === category) {
                entry.style.display = 'block';
            } else {
                entry.style.display = 'none';
            }
        });
    }

    // Tambahkan event listener untuk file input logo
    document.addEventListener('DOMContentLoaded', function() {
        const logoInput = document.getElementById('company_logo');
        if (logoInput) {
            logoInput.addEventListener('change', previewLogo);
        }

        // Cek apakah ada tab aktif di localStorage
        const activeTab = localStorage.getItem('activeSettingsTab');
        if (activeTab) {
            showTab(activeTab, null);
        }

        // Inisialisasi tombol collapse untuk opsi lanjutan backup
        const advancedOptionsBtn = document.querySelector('[data-toggle="collapse"][data-target="#advancedBackupOptions"]');
        if (advancedOptionsBtn) {
            advancedOptionsBtn.addEventListener('click', function() {
                const target = document.querySelector(this.getAttribute('data-target'));
                if (target) {
                    if (target.classList.contains('show')) {
                        target.classList.remove('show');
                        this.innerHTML = '<i class="fas fa-cog"></i> Opsi Lanjutan';
                    } else {
                        target.classList.add('show');
                        this.innerHTML = '<i class="fas fa-times"></i> Sembunyikan Opsi Lanjutan';
                    }
                }
            });
        }
    });



    // Fungsi untuk test email (versi lama - dipertahankan untuk kompatibilitas)
    function testEmail() {
        const emailInput = document.querySelector('input[name="sender_email"]');
        if (!emailInput) {
            alert('Error: Email field not found');
            return;
        }

        const email = emailInput.value.trim();
        if (!email) {
            alert('Silakan isi email pengirim terlebih dahulu');
            return;
        }

        const smtpServer = document.querySelector('input[name="smtp_server"]').value.trim();
        if (!smtpServer) {
            alert('Silakan isi SMTP server terlebih dahulu');
            return;
        }

        const smtpPassword = document.querySelector('input[name="smtp_password"]').value;
        if (!smtpPassword) {
            alert('Silakan isi password SMTP terlebih dahulu');
            return;
        }

        // Kumpulkan semua data SMTP yang diperlukan
        const formData = {
            smtp_server: smtpServer,
            smtp_port: document.querySelector('input[name="smtp_port"]').value,
            sender_email: email,
            smtp_password: smtpPassword,
            sender_name: document.querySelector('input[name="sender_name"]').value,
            smtp_encryption: document.querySelector('select[name="smtp_encryption"]').value
        };

        if (confirmAction('Kirim email test ke ' + email + '?')) {
            // Tampilkan loading indicator
            const testButton = event.target.closest('button');
            const originalHTML = testButton.innerHTML;
            testButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Mengirim...';
            testButton.disabled = true;

            fetch('test_email.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(async response => {
                // Periksa status HTTP response
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                // Ambil text response
                const text = await response.text();

                // Log response mentah untuk debugging
                console.log('Raw server response:', text);

                // Coba parse sebagai JSON
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('JSON parse error:', e);
                    console.error('Raw response:', text);

                    // Cek apakah response mengandung HTML error
                    if (text.includes('<br />') || text.includes('</b>') || text.includes('Fatal error')) {
                        throw new Error('Server mengembalikan error PHP. Silakan cek log server.');
                    } else {
                        throw new Error('Server response tidak valid. Silakan cek log server.');
                    }
                }
            })
            .then(data => {
                if (data.success) {
                    alert('Email test berhasil dikirim!');
                } else {
                    // Tampilkan pesan error yang lebih detail
                    let errorMessage = 'Gagal mengirim email: ' + data.message;

                    // Tambahkan detail error jika ada
                    if (data.error_details) {
                        errorMessage += '\n\n' + data.error_details;
                    }

                    // Tambahkan help message jika ada
                    if (data.help) {
                        errorMessage += '\n\n' + data.help;
                    }

                    // Tampilkan dialog error
                    showErrorDialog('Gagal Mengirim Email', errorMessage);
                }
            })
            .catch(error => {
                alert('Terjadi kesalahan: ' + error.message);
                console.error('Error:', error);
            })
            .finally(() => {
                // Kembalikan button ke keadaan semula
                testButton.innerHTML = originalHTML;
                testButton.disabled = false;
            });
        }
    }

    // Fungsi untuk test email dengan opsi dasar
    function testEmailAdvanced() {
        // Validasi konfigurasi SMTP dasar
        const smtpServer = document.querySelector('input[name="smtp_server"]').value.trim();
        if (!smtpServer) {
            showErrorDialog('Validasi Gagal', 'Silakan isi SMTP server terlebih dahulu di form pengaturan email.');
            return;
        }

        const senderEmail = document.querySelector('input[name="sender_email"]').value.trim();
        if (!senderEmail) {
            showErrorDialog('Validasi Gagal', 'Silakan isi email pengirim terlebih dahulu di form pengaturan email.');
            return;
        }

        const smtpPassword = document.querySelector('input[name="smtp_password"]').value;
        if (!smtpPassword) {
            showErrorDialog('Validasi Gagal', 'Silakan isi password SMTP terlebih dahulu di form pengaturan email.');
            return;
        }

        // Ambil nilai dari form test email
        const testEmail = document.getElementById('test_email').value.trim();
        if (!testEmail) {
            showErrorDialog('Validasi Gagal', 'Silakan isi email tujuan untuk test.');
            return;
        }

        // Kumpulkan semua data SMTP yang diperlukan
        const formData = {
            smtp_server: smtpServer,
            smtp_port: document.querySelector('input[name="smtp_port"]').value,
            sender_email: senderEmail,
            smtp_password: smtpPassword,
            sender_name: document.querySelector('input[name="sender_name"]').value,
            smtp_encryption: document.querySelector('select[name="smtp_encryption"]').value,
            test_email: testEmail,
            debug_mode: false // Mode debug dinonaktifkan untuk versi dasar
        };

        // Tampilkan loading indicator
        const testButton = document.querySelector('#testEmailForm button');
        const originalHTML = testButton.innerHTML;
        testButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Mengirim...';
        testButton.disabled = true;

        // Reset hasil sebelumnya
        const resultContainer = document.getElementById('testEmailResult');
        resultContainer.style.display = 'none';
        resultContainer.innerHTML = '';

        // Kirim request ke server
        fetch('test_email.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(async response => {
            // Periksa status HTTP response
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            // Ambil text response
            const text = await response.text();

            // Log response mentah untuk debugging
            console.log('Raw server response:', text);

            // Coba parse sebagai JSON
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('JSON parse error:', e);
                console.error('Raw response:', text);

                // Cek apakah response mengandung HTML error
                if (text.includes('<br />') || text.includes('</b>') || text.includes('Fatal error')) {
                    throw new Error('Server mengembalikan error PHP. Silakan cek log server.');
                } else {
                    throw new Error('Server response tidak valid. Silakan cek log server.');
                }
            }
        })
        .then(data => {
            // Tampilkan hasil
            resultContainer.style.display = 'block';

            if (data.success) {
                resultContainer.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> Email test berhasil dikirim ke ${testEmail}!
                    </div>
                `;
            } else {
                // Tampilkan pesan error
                let errorHtml = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <strong>Gagal mengirim email:</strong> ${data.message}
                        <p class="mt-2 mb-0">Untuk opsi troubleshooting lanjutan, silakan kunjungi tab <a href="#" onclick="showTab('email_troubleshooting', event); return false;">Troubleshooting Email</a>.</p>
                    </div>
                `;

                resultContainer.innerHTML = errorHtml;
            }
        })
        .catch(error => {
            resultContainer.style.display = 'block';
            resultContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <strong>Terjadi kesalahan:</strong> ${error.message}
                    <p class="mt-2 mb-0">Untuk opsi troubleshooting lanjutan, silakan kunjungi tab <a href="#" onclick="showTab('email_troubleshooting', event); return false;">Troubleshooting Email</a>.</p>
                </div>
            `;
            console.error('Error:', error);
        })
        .finally(() => {
            // Kembalikan button ke keadaan semula
            testButton.innerHTML = originalHTML;
            testButton.disabled = false;
        });
    }

    // Fungsi untuk test email dengan opsi lanjutan (di tab troubleshooting)
    function testEmailTroubleshooting() {
        // Validasi konfigurasi SMTP dasar
        const smtpServer = document.querySelector('input[name="smtp_server"]').value.trim();
        if (!smtpServer) {
            showErrorDialog('Validasi Gagal', 'Silakan isi SMTP server terlebih dahulu di form pengaturan email.');
            return;
        }

        const senderEmail = document.querySelector('input[name="sender_email"]').value.trim();
        if (!senderEmail) {
            showErrorDialog('Validasi Gagal', 'Silakan isi email pengirim terlebih dahulu di form pengaturan email.');
            return;
        }

        const smtpPassword = document.querySelector('input[name="smtp_password"]').value;
        if (!smtpPassword) {
            showErrorDialog('Validasi Gagal', 'Silakan isi password SMTP terlebih dahulu di form pengaturan email.');
            return;
        }

        // Ambil nilai dari form test email lanjutan
        const testEmail = document.getElementById('test_email_adv').value.trim();
        if (!testEmail) {
            showErrorDialog('Validasi Gagal', 'Silakan isi email tujuan untuk test.');
            return;
        }

        // Ambil nilai port dan enkripsi opsional
        const testPort = document.getElementById('test_port_adv').value;
        const testEncryption = document.getElementById('test_encryption_adv').value;
        const debugMode = document.getElementById('debug_mode_adv').checked;

        // Kumpulkan semua data SMTP yang diperlukan
        const formData = {
            smtp_server: smtpServer,
            smtp_port: document.querySelector('input[name="smtp_port"]').value,
            sender_email: senderEmail,
            smtp_password: smtpPassword,
            sender_name: document.querySelector('input[name="sender_name"]').value,
            smtp_encryption: document.querySelector('select[name="smtp_encryption"]').value,
            test_email: testEmail,
            debug_mode: debugMode
        };

        // Override port dan enkripsi jika dipilih
        if (testPort) {
            formData.smtp_port = testPort;
        }

        if (testEncryption) {
            formData.smtp_encryption = testEncryption;
        }

        // Tampilkan loading indicator
        const testButton = document.querySelector('#testEmailAdvancedForm button');
        const originalHTML = testButton.innerHTML;
        testButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Mengirim...';
        testButton.disabled = true;

        // Reset hasil sebelumnya
        const resultContainer = document.getElementById('testEmailAdvancedResult');
        resultContainer.style.display = 'none';
        resultContainer.innerHTML = '';

        const debugContainer = document.getElementById('debugOutputAdvanced');
        debugContainer.style.display = 'none';
        debugContainer.querySelector('pre').innerHTML = '';

        // Kirim request ke server
        fetch('test_email.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(async response => {
            // Periksa status HTTP response
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            // Ambil text response
            const text = await response.text();

            // Log response mentah untuk debugging
            console.log('Raw server response:', text);

            // Coba parse sebagai JSON
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('JSON parse error:', e);
                console.error('Raw response:', text);

                // Cek apakah response mengandung HTML error
                if (text.includes('<br />') || text.includes('</b>') || text.includes('Fatal error')) {
                    throw new Error('Server mengembalikan error PHP. Silakan cek log server.');
                } else {
                    throw new Error('Server response tidak valid. Silakan cek log server.');
                }
            }
        })
        .then(data => {
            // Tampilkan hasil
            resultContainer.style.display = 'block';

            if (data.success) {
                resultContainer.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> Email test berhasil dikirim ke ${testEmail}!
                    </div>
                    <div class="alert alert-info">
                        <strong>Konfigurasi yang digunakan:</strong><br>
                        SMTP Server: ${formData.smtp_server}<br>
                        Port: ${formData.smtp_port}<br>
                        Enkripsi: ${formData.smtp_encryption}<br>
                        Email Pengirim: ${formData.sender_email}
                    </div>
                `;
            } else {
                // Tampilkan pesan error
                let errorHtml = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <strong>Gagal mengirim email:</strong> ${data.message}
                    </div>
                    <div class="alert alert-warning">
                        <strong>Konfigurasi yang digunakan:</strong><br>
                        SMTP Server: ${formData.smtp_server}<br>
                        Port: ${formData.smtp_port}<br>
                        Enkripsi: ${formData.smtp_encryption}<br>
                        Email Pengirim: ${formData.sender_email}
                    </div>
                `;

                // Tambahkan detail error jika ada
                if (data.error_details) {
                    errorHtml += `
                        <div class="alert alert-info">
                            <strong>Detail Error:</strong><br>
                            ${data.error_details.replace(/\n/g, '<br>')}
                        </div>
                    `;
                }

                resultContainer.innerHTML = errorHtml;
            }

            // Tampilkan debug output jika ada dan mode debug aktif
            if (debugMode && data.debug_output) {
                debugContainer.style.display = 'block';
                debugContainer.querySelector('pre').textContent = data.debug_output;
            }
        })
        .catch(error => {
            resultContainer.style.display = 'block';
            resultContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <strong>Terjadi kesalahan:</strong> ${error.message}
                </div>
            `;
            console.error('Error:', error);
        })
        .finally(() => {
            // Kembalikan button ke keadaan semula
            testButton.innerHTML = originalHTML;
            testButton.disabled = false;
        });
    }

    // Fungsi untuk menampilkan dialog error yang lebih baik
    function showErrorDialog(title, message) {
        // Cek apakah Bootstrap tersedia
        if (typeof bootstrap !== 'undefined' || typeof $ !== 'undefined') {
            // Cek apakah sudah ada dialog error
            let errorDialog = document.getElementById('errorDialog');

            // Jika belum ada, buat dialog baru
            if (!errorDialog) {
                errorDialog = document.createElement('div');
                errorDialog.id = 'errorDialog';
                errorDialog.className = 'modal fade';
                errorDialog.setAttribute('tabindex', '-1');
                errorDialog.setAttribute('role', 'dialog');
                errorDialog.setAttribute('aria-labelledby', 'errorDialogTitle');
                errorDialog.setAttribute('aria-hidden', 'true');

                errorDialog.innerHTML = `
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title" id="errorDialogTitle"></h5>
                            </div>
                            <div class="modal-body">
                                <div id="errorDialogMessage" style="white-space: pre-line;"></div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeErrorDialog()">Tutup</button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(errorDialog);
            }

            // Set judul dan pesan error
            document.getElementById('errorDialogTitle').textContent = title;
            document.getElementById('errorDialogMessage').textContent = message;

            // Tampilkan dialog menggunakan Bootstrap 5 atau jQuery
            try {
                if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                    // Bootstrap 5
                    const modal = new bootstrap.Modal(errorDialog);
                    modal.show();
                } else if (typeof $ !== 'undefined' && $.fn.modal) {
                    // Bootstrap 4 dengan jQuery
                    $('#errorDialog').modal('show');
                } else {
                    throw new Error('Bootstrap modal tidak tersedia');
                }
            } catch (e) {
                console.error('Error showing modal:', e);
                // Fallback ke alert jika modal gagal
                alert(`${title}\n\n${message}`);
            }
        } else {
            // Fallback ke alert jika Bootstrap tidak tersedia
            alert(`${title}\n\n${message}`);
        }
    }

    // Fungsi untuk menutup dialog error
    function closeErrorDialog() {
        const errorDialog = document.getElementById('errorDialog');
        if (!errorDialog) return;

        try {
            if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                // Bootstrap 5
                const modal = bootstrap.Modal.getInstance(errorDialog);
                if (modal) modal.hide();
            } else if (typeof $ !== 'undefined' && $.fn.modal) {
                // Bootstrap 4 dengan jQuery
                $('#errorDialog').modal('hide');
            }
        } catch (e) {
            console.error('Error hiding modal:', e);
            // Fallback jika gagal menutup modal
            errorDialog.style.display = 'none';
        }
    }

    // Fungsi untuk test lokasi backup
    function testBackupLocation() {
        const locationInput = document.querySelector('input[name="backup_location"]');
        if (!locationInput) {
            alert('Error: Backup location field not found');
            return;
        }

        const location = locationInput.value.trim();
        if (!location) {
            alert('Silakan isi lokasi penyimpanan backup terlebih dahulu');
            return;
        }

        // Tampilkan loading indicator
        const testButton = event.target.closest('button');
        const originalHTML = testButton.innerHTML;
        testButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
        testButton.disabled = true;

        // Kirim request untuk test lokasi backup
        fetch('test_backup_location.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ location: location })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Lokasi backup valid dan dapat diakses!');
            } else {
                alert('Gagal mengakses lokasi backup: ' + data.message);
            }
        })
        .catch(error => {
            alert('Terjadi kesalahan: ' + error.message);
            console.error('Error:', error);
        })
        .finally(() => {
            // Kembalikan button ke keadaan semula
            testButton.innerHTML = originalHTML;
            testButton.disabled = false;
        });
    }

    // Fungsi untuk membuat backup manual
    function createManualBackup() {
        // Tampilkan loading indicator
        const backupButton = event.target.closest('button');
        const originalHTML = backupButton.innerHTML;
        backupButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Membuat Backup...';
        backupButton.disabled = true;

        // Kirim request untuk membuat backup
        fetch('create_backup.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                CustomModal.success('Backup berhasil dibuat!<br>Lokasi: ' + data.file_path);
            } else {
                CustomModal.alert('Gagal membuat backup: ' + data.message, 'Error');
            }
        })
        .catch(error => {
            CustomModal.alert('Terjadi kesalahan: ' + error.message, 'Error');
            console.error('Error:', error);
        })
        .finally(() => {
            // Kembalikan button ke keadaan semula
            backupButton.innerHTML = originalHTML;
            backupButton.disabled = false;
        });
    }

    // Fungsi untuk mengatur jadwal backup otomatis
    function scheduleBackup() {
        // Tampilkan loading indicator
        const scheduleButton = event.target.closest('button');
        const originalHTML = scheduleButton.innerHTML;
        scheduleButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Mengatur Jadwal...';
        scheduleButton.disabled = true;

        // Kirim request untuk mengatur jadwal backup
        fetch('schedule_backup.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                CustomModal.success('Jadwal backup berhasil diatur!<br>' + data.details);
            } else {
                CustomModal.alert('Gagal mengatur jadwal backup: ' + data.message, 'Error');
                if (data.error) {
                    console.error('Error details:', data.error);
                }
            }
        })
        .catch(error => {
            CustomModal.alert('Terjadi kesalahan: ' + error.message, 'Error');
            console.error('Error:', error);
        })
        .finally(() => {
            // Kembalikan button ke keadaan semula
            scheduleButton.innerHTML = originalHTML;
            scheduleButton.disabled = false;
        });
    }

    // Fungsi untuk menjalankan backup otomatis sekali
    function runAutoBackup() {
        // Tampilkan loading indicator
        const runButton = event.target.closest('button');
        const originalHTML = runButton.innerHTML;
        runButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menjalankan Backup...';
        runButton.disabled = true;

        // Kirim request untuk menjalankan backup otomatis
        fetch('auto_backup.php', {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                CustomModal.success('Backup otomatis berhasil dijalankan!<br>Lokasi: ' + data.file_path);
            } else {
                CustomModal.alert('Gagal menjalankan backup otomatis: ' + data.message, 'Error');
            }
        })
        .catch(error => {
            CustomModal.alert('Terjadi kesalahan: ' + error.message, 'Error');
            console.error('Error:', error);
        })
        .finally(() => {
            // Kembalikan button ke keadaan semula
            runButton.innerHTML = originalHTML;
            runButton.disabled = false;
        });
    }

    // Fungsi untuk filter log aktivitas
    function filterLogs() {
        const filter = document.getElementById('logFilter').value;
        const logEntries = document.querySelectorAll('.log-entry');

        logEntries.forEach(entry => {
            if (filter === 'all' || entry.getAttribute('data-category') === filter) {
                entry.style.display = 'block';
            } else {
                entry.style.display = 'none';
            }
        });
    }

    // Fungsi untuk refresh log aktivitas
    function refreshLogs() {
        // Tampilkan loading indicator
        const refreshButton = event.target.closest('button');
        const originalHTML = refreshButton.innerHTML;
        refreshButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
        refreshButton.disabled = true;

        // Kirim request untuk refresh log
        fetch('get_activity_logs.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update container log dengan data baru
                const logContainer = document.getElementById('activityLogContainer');
                logContainer.innerHTML = '';

                if (data.logs.length === 0) {
                    logContainer.innerHTML = '<div class="log-entry">Belum ada aktivitas yang tercatat.</div>';
                } else {
                    data.logs.forEach(log => {
                        const logEntry = document.createElement('div');
                        logEntry.className = 'log-entry';
                        logEntry.setAttribute('data-category', log.category);

                        const actionText = document.createElement('strong');
                        actionText.textContent = log.action;

                        const userText = document.createTextNode(' oleh ' + log.user);

                        const timestamp = document.createElement('span');
                        timestamp.className = 'timestamp';
                        timestamp.textContent = log.timestamp;

                        logEntry.appendChild(actionText);
                        logEntry.appendChild(userText);
                        logEntry.appendChild(timestamp);

                        logContainer.appendChild(logEntry);
                    });
                }

                // Terapkan filter yang aktif
                filterLogs();
            } else {
                alert('Gagal memuat log: ' + data.message);
            }
        })
        .catch(error => {
            alert('Terjadi kesalahan: ' + error.message);
            console.error('Error:', error);
        })
        .finally(() => {
            // Kembalikan button ke keadaan semula
            refreshButton.innerHTML = originalHTML;
            refreshButton.disabled = false;
        });
    }

    // Fungsi untuk menghapus semua log
    function clearLogs() {
        if (confirmAction('Apakah Anda yakin ingin menghapus semua log aktivitas?\nTindakan ini tidak dapat dibatalkan.')) {
            // Tampilkan loading indicator
            const clearButton = event.target.closest('button');
            const originalHTML = clearButton.innerHTML;
            clearButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menghapus...';
            clearButton.disabled = true;
            // Kirim request untuk menghapus log
            fetch('clear_activity_logs.php', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update container log
                    const logContainer = document.getElementById('activityLogContainer');
                    logContainer.innerHTML = '<div class="log-entry">Belum ada aktivitas yang tercatat.</div>';
                    alert('Semua log aktivitas berhasil dihapus.');
                } else {
                    alert('Gagal menghapus log: ' + data.message);
                }
            })
            .catch(error => {
                alert('Terjadi kesalahan: ' + error.message);
                console.error('Error:', error);
            })
            .finally(() => {
                // Kembalikan button ke keadaan semula
                clearButton.innerHTML = originalHTML;
                clearButton.disabled = false;
            });
        }
    }

    // Event listener untuk preview logo
    document.addEventListener('DOMContentLoaded', function() {
        // Setup logo preview
        const logoInput = document.getElementById('company_logo');
        if (logoInput) {
            logoInput.addEventListener('change', previewLogo);
        }

        // Restore active tab from localStorage
        const activeTab = localStorage.getItem('activeSettingsTab');
        if (activeTab && document.getElementById(activeTab)) {
            // Trigger click pada tab button yang sesuai
            const tabButtons = document.querySelectorAll('.tab-button');
            for (let i = 0; i < tabButtons.length; i++) {
                if (tabButtons[i].getAttribute('onclick').includes(activeTab)) {
                    tabButtons[i].click();
                    break;
                }
            }
        }

        // Hapus semua dialog konfirmasi untuk semua formulir
        // Ini akan memungkinkan semua formulir dikirim tanpa konfirmasi
        console.log('All form submissions will proceed without confirmation dialogs');

        // Tambahkan log untuk debugging dan tambahkan event listener untuk semua formulir
        const allForms = document.querySelectorAll('form');
        allForms.forEach(form => {
            // Tambahkan ID jika belum ada
            if (!form.id) {
                form.id = 'form_' + Math.random().toString(36).substr(2, 9);
            }

            // Log informasi formulir
            console.log('Form found: ' + form.id + ', method: ' + form.method + ', action: ' + (form.action || 'current page'));

            // Tambahkan event listener untuk submit
            form.addEventListener('submit', function(e) {
                // Log ketika formulir dikirim
                console.log('Form submitted: ' + form.id);

                // Log data formulir yang dikirim
                const formData = new FormData(form);
                const formDataObj = {};
                formData.forEach((value, key) => {
                    formDataObj[key] = value;
                });
                console.log('Form data:', formDataObj);

                // Tidak perlu konfirmasi
            });
        });
    });
    </script>
</body>
</html>








