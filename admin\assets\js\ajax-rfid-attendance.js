/**
 * AJAX RFID Attendance System - JavaScript
 *
 * Provides AJAX functionality for the RFID attendance system
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    let selectedTrainingId = '';
    let refreshInterval = null;
    const loadingSpinner = document.getElementById('loading_spinner');

    // Elements
    const trainingSelect = document.getElementById('training_id');
    const rfidTrainingIdInput = document.getElementById('rfid_training_id');
    const manualTrainingIdInput = document.getElementById('manual_training_id');
    const attendanceTableContainer = document.querySelector('.attendance-table-container');
    const scanForm = document.getElementById('rfid_form');
    const manualForm = document.getElementById('manual_form');

    // Initialize tooltips
    function initTooltips() {
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    // Initialize Select2
    function initSelect2() {
        if (typeof $.fn.select2 !== 'undefined') {
            $('.select2-dropdown').select2({
                theme: 'bootstrap4',
                placeholder: 'Pilih opsi...',
                width: '100%',
                language: {
                    noResults: function() {
                        return "Tidak ada hasil yang ditemukan";
                    },
                    searching: function() {
                        return "Mencari...";
                    }
                }
            });
        }
    }

    // Load data from server
    function loadData(trainingId = '') {
        // Show loading spinner
        if (loadingSpinner) loadingSpinner.classList.remove('d-none');

        // Update selected training ID
        selectedTrainingId = trainingId;

        // Build URL with parameters
        const url = `get_attendance_data.php${trainingId ? '?training_id=' + trainingId : ''}`;

        console.log('Loading data from:', url);

        // Make AJAX request
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Data received:', data);

                if (data.success) {
                    updateTrainingDropdowns(data.training_data, data.selected_training_id);
                    updateAttendanceTable(data.attendance_data, data.selected_training_id);

                    // Log debug info if available
                    if (data.debug) {
                        console.log('Debug info:', data.debug);
                    }
                } else {
                    showNotification('Error loading data: ' + (data.message || 'Unknown error'), 'danger');
                }

                // Hide loading spinner
                if (loadingSpinner) loadingSpinner.classList.add('d-none');
            })
            .catch(error => {
                console.error('Error loading data:', error);

                // Show more detailed error message
                let errorMessage = 'Error loading data. ';

                if (error.message) {
                    errorMessage += error.message;
                } else {
                    errorMessage += 'Please try again.';
                }

                // Add fallback data if no data is available
                if (!trainingSelect.options.length || trainingSelect.options.length <= 1) {
                    // Add a dummy option if no options are available
                    const option = document.createElement('option');
                    option.value = "1";
                    option.textContent = "Pelatihan Dasar RFID (01-01-2023)";
                    trainingSelect.appendChild(option);

                    // Update hidden inputs
                    if (rfidTrainingIdInput) rfidTrainingIdInput.value = "1";
                    if (manualTrainingIdInput) manualTrainingIdInput.value = "1";

                    // Show empty attendance table
                    updateAttendanceTable([], "1");
                }

                showNotification(errorMessage, 'danger');

                // Hide loading spinner
                if (loadingSpinner) loadingSpinner.classList.add('d-none');
            });
    }

    // Update training dropdowns
    function updateTrainingDropdowns(trainingData, selectedId) {
        if (!trainingSelect) return;

        // Save current selection
        const currentSelection = selectedId || '';

        // Clear options except the first one
        while (trainingSelect.options.length > 1) {
            trainingSelect.remove(1);
        }

        // Add training options
        trainingData.forEach(training => {
            // Format date based on available fields
            let formattedDate = '';

            if (training.training_date_fixed) {
                const fixedDate = new Date(training.training_date_fixed);
                formattedDate = fixedDate.toLocaleDateString('id-ID', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                });
            } else if (training.training_date_start && training.training_date_end) {
                const startDate = new Date(training.training_date_start);
                const endDate = new Date(training.training_date_end);

                formattedDate = startDate.toLocaleDateString('id-ID', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                }) + ' s/d ' + endDate.toLocaleDateString('id-ID', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                });
            } else if (training.training_date) {
                const trainingDate = new Date(training.training_date);
                formattedDate = trainingDate.toLocaleDateString('id-ID', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                });
            } else {
                formattedDate = 'Tanggal tidak tersedia';
            }

            // Create option for dropdown
            const option = document.createElement('option');
            option.value = training.id;
            option.textContent = `${training.training_topic} (${formattedDate})`;
            if (training.id == currentSelection) {
                option.selected = true;
            }
            trainingSelect.appendChild(option);
        });

        // Update hidden inputs with the selected training ID
        if (rfidTrainingIdInput) rfidTrainingIdInput.value = currentSelection;
        if (manualTrainingIdInput) manualTrainingIdInput.value = currentSelection;

        // Reinitialize Select2
        initSelect2();
    }

    // Update attendance table
    function updateAttendanceTable(attendanceData, selectedId) {
        if (!attendanceTableContainer) return;

        if (!selectedId) {
            // No training selected
            attendanceTableContainer.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                    <p class="text-gray-500 mb-0">Pilih training untuk melihat data absensi</p>
                    <p class="text-gray-500">Silakan pilih training dari dropdown di sebelah kiri</p>
                </div>
            `;
            return;
        }

        if (attendanceData.length === 0) {
            // No attendance data
            attendanceTableContainer.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                    <p class="text-gray-500 mb-0">Belum ada data absensi</p>
                    <p class="text-gray-500">Scan kartu RFID atau masukkan NIK karyawan untuk mencatat absensi</p>
                </div>
            `;
            return;
        }

        // Build table HTML
        let html = `
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="attendance_table" style="width: 100%; table-layout: fixed;">
                    <colgroup>
                        <col style="width: 5%"> <!-- No -->
                        <col style="width: 10%"> <!-- NIK -->
                        <col style="width: 20%"> <!-- Nama -->
                        <col style="width: 15%"> <!-- Departemen -->
                        <col style="width: 15%"> <!-- Check In -->
                        <col style="width: 15%"> <!-- Check Out -->
                        <col style="width: 10%"> <!-- Status -->
                        <col style="width: 10%"> <!-- Aksi -->
                    </colgroup>
                    <thead class="bg-light">
                        <tr>
                            <th class="text-center">No</th>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Departemen</th>
                            <th>Check In</th>
                            <th>Check Out</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        // Add rows
        attendanceData.forEach((attendance, index) => {
            const checkIn = attendance.check_in ? new Date(attendance.check_in).toLocaleString('id-ID') : '-';
            const checkOut = attendance.check_out ? new Date(attendance.check_out).toLocaleString('id-ID') : '-';

            let statusClass = 'secondary';
            if (attendance.status === 'hadir') statusClass = 'success';
            else if (attendance.status === 'terlambat') statusClass = 'warning';
            else if (attendance.status === 'izin') statusClass = 'info';
            else if (attendance.status === 'tidak hadir') statusClass = 'danger';

            html += `
                <tr>
                    <td class="text-center">${index + 1}</td>
                    <td>${attendance.nik}</td>
                    <td>${attendance.nama}</td>
                    <td>${attendance.dept || '-'}</td>
                    <td>${checkIn}</td>
                    <td>${checkOut}</td>
                    <td class="text-center">
                        <span class="badge badge-${statusClass}">
                            ${attendance.status.charAt(0).toUpperCase() + attendance.status.slice(1)}
                        </span>
                    </td>
                    <td class="text-center">
                        <a href="edit_attendance.php?id=${attendance.id}&training_id=${selectedId}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit Absensi">
                            <i class="fas fa-edit"></i>
                        </a>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <small class="text-muted">Terakhir diperbarui: ${new Date().toLocaleString('id-ID')}</small>
                </div>
                <div>
                    <button id="refresh_data" class="btn btn-sm btn-info mr-2">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <a href="export_attendance.php?training_id=${selectedId}" class="btn btn-sm btn-success">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </a>
                </div>
            </div>
        `;

        attendanceTableContainer.innerHTML = html;

        // Add event listener to refresh button
        document.getElementById('refresh_data')?.addEventListener('click', function() {
            loadData(selectedId);
        });

        // Initialize tooltips
        initTooltips();
    }

    // Show notification
    function showNotification(message, type = 'info') {
        const notificationArea = document.getElementById('notification_area');
        if (!notificationArea) return;

        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.role = 'alert';

        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        notificationArea.appendChild(alertDiv);

        // Auto dismiss after 5 seconds
        setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => {
                notificationArea.removeChild(alertDiv);
            }, 150);
        }, 5000);
    }

    // Add event listeners
    if (trainingSelect) {
        trainingSelect.addEventListener('change', function() {
            if (this.value !== '') {
                loadData(this.value);

                // Update hidden inputs
                if (rfidTrainingIdInput) rfidTrainingIdInput.value = this.value;
                if (manualTrainingIdInput) manualTrainingIdInput.value = this.value;
            }
        });
    }

    // Handle form submissions
    if (scanForm) {
        scanForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Check if training is selected
            const trainingId = trainingSelect.value;
            if (!trainingId) {
                showNotification('Silakan pilih training terlebih dahulu', 'warning');
                return;
            }

            // Update hidden input
            if (rfidTrainingIdInput) rfidTrainingIdInput.value = trainingId;

            // Show loading spinner
            if (loadingSpinner) loadingSpinner.classList.remove('d-none');

            const formData = new FormData(this);

            fetch('rfid_attendance.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                // Extract success or error message from the response
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const successAlert = doc.querySelector('.alert-success');
                const errorAlert = doc.querySelector('.alert-danger');

                if (successAlert) {
                    showNotification(successAlert.textContent, 'success');
                    // Clear form
                    document.getElementById('card_number').value = '';
                    // Reload data
                    loadData(trainingId);
                } else if (errorAlert) {
                    showNotification(errorAlert.textContent, 'danger');
                } else {
                    showNotification('Operasi selesai.', 'info');
                }

                // Hide loading spinner
                if (loadingSpinner) loadingSpinner.classList.add('d-none');
            })
            .catch(error => {
                console.error('Error submitting form:', error);
                showNotification('Error submitting form. Please try again.', 'danger');

                // Hide loading spinner
                if (loadingSpinner) loadingSpinner.classList.add('d-none');
            });
        });
    }

    // Handle manual form submission
    if (manualForm) {
        manualForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Check if training is selected
            const trainingId = trainingSelect.value;
            if (!trainingId) {
                showNotification('Silakan pilih training terlebih dahulu', 'warning');
                return;
            }

            // Check if employee is selected
            const manualNik = document.getElementById('manual_nik').value;
            if (!manualNik) {
                showNotification('Silakan pilih karyawan terlebih dahulu', 'warning');
                return;
            }

            // Update hidden input
            if (manualTrainingIdInput) manualTrainingIdInput.value = trainingId;

            // Show loading spinner
            if (loadingSpinner) loadingSpinner.classList.remove('d-none');

            const formData = new FormData(this);

            fetch('rfid_attendance.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                // Extract success or error message from the response
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const successAlert = doc.querySelector('.alert-success');
                const errorAlert = doc.querySelector('.alert-danger');

                if (successAlert) {
                    showNotification(successAlert.textContent, 'success');
                    // Clear form
                    document.getElementById('employee_search').value = '';
                    document.getElementById('manual_nik').value = '';
                    document.getElementById('selected_employee').classList.add('d-none');
                    // Reload data
                    loadData(trainingId);
                } else if (errorAlert) {
                    showNotification(errorAlert.textContent, 'danger');
                } else {
                    showNotification('Operasi selesai.', 'info');
                }

                // Hide loading spinner
                if (loadingSpinner) loadingSpinner.classList.add('d-none');
            })
            .catch(error => {
                console.error('Error submitting form:', error);
                showNotification('Error submitting form. Please try again.', 'danger');

                // Hide loading spinner
                if (loadingSpinner) loadingSpinner.classList.add('d-none');
            });
        });
    }

    // Initial data load
    const urlParams = new URLSearchParams(window.location.search);
    const initialTrainingId = urlParams.get('training_id');
    loadData(initialTrainingId || '');

    // Initialize Select2
    initSelect2();
});
