<?php
/**
 * File constants.php
 * Berisi konstanta global yang digunakan di seluruh aplikasi
 */

// Deteksi base URL aplikasi
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];

    // Deteksi root path aplikasi
    $scriptPath = $_SERVER['SCRIPT_NAME'];
    $documentRoot = $_SERVER['DOCUMENT_ROOT'];
    $appRoot = '';

    // Deteksi nama folder aplikasi (biasanya 'training')
    $appFolder = 'Training'; // Default folder name

    // Metode 1: Cek apakah kita berada di folder aplikasi
    if (stripos($scriptPath, "/{$appFolder}/") !== false) {
        $appRoot = "/{$appFolder}/";
    }
    // Metode 2: Coba deteksi dari document root
    else {
        $currentDir = dirname(dirname(__FILE__)); // Naik satu level dari folder config
        $relativePath = str_replace('\\', '/', str_replace($documentRoot, '', $currentDir));
        if (!empty($relativePath)) {
            $appRoot = "{$relativePath}/";
        } else {
            // Metode 3: Coba deteksi dari script path
            $pathParts = explode('/', trim($scriptPath, '/'));
            $appRoot = !empty($pathParts[0]) ? "/{$pathParts[0]}/" : '/';
        }
    }

    // Pastikan appRoot diawali dengan / dan diakhiri dengan /
    if (substr($appRoot, 0, 1) !== '/') {
        $appRoot = "/{$appRoot}";
    }
    if (substr($appRoot, -1) !== '/') {
        $appRoot .= '/';
    }

    // Perbaikan untuk menghindari path lengkap
    $appRoot = preg_replace('/^\/[A-Z]:\//i', '/', $appRoot);

    // Debug
    error_log("BASE_URL calculated as: {$protocol}{$host}{$appRoot}");

    return "{$protocol}{$host}{$appRoot}";
}

// Fungsi untuk mendapatkan URL navbar yang benar
function getNavbarUrl($path) {
    // Pastikan path tidak diawali dengan /
    $path = ltrim($path, '/');

    // Gabungkan dengan BASE_URL
    return getBaseUrl() . $path;
}

// Fungsi untuk mendeteksi subfolder saat ini
function getCurrentSubfolder() {
    $scriptPath = $_SERVER['SCRIPT_NAME'];
    $appFolder = 'training'; // Default folder name

    // Cek apakah kita berada di subfolder
    if (strpos($scriptPath, "/{$appFolder}/") !== false) {
        $pathAfterApp = substr($scriptPath, strpos($scriptPath, "/{$appFolder}/") + strlen("/{$appFolder}/"));
        $parts = explode('/', $pathAfterApp);
        if (!empty($parts[0])) {
            return $parts[0];
        }
    }

    return '';
}

// Fungsi untuk mendapatkan URL yang benar berdasarkan subfolder saat ini
function getCorrectUrl($path) {
    $currentSubfolder = getCurrentSubfolder();
    $path = ltrim($path, '/');

    // Debug
    error_log("Current subfolder: {$currentSubfolder}, Path: {$path}");

    // Jika kita berada di subfolder
    if (!empty($currentSubfolder)) {
        // Daftar file dan folder global yang tidak perlu ditambahkan subfolder
        $globalPaths = ['config/', 'view/', 'assets/', 'uploads/'];
        $isGlobalPath = false;

        // Cek apakah path adalah path global
        foreach ($globalPaths as $globalPath) {
            if (strpos($path, $globalPath) === 0 || $path === $globalPath) {
                $isGlobalPath = true;
                break;
            }
        }

        // Jika path adalah file di root (seperti dashboard.php) dan bukan file global
        if (strpos($path, '/') === false && !$isGlobalPath) {
            // Tambahkan subfolder ke path
            $url = getBaseUrl() . "{$currentSubfolder}/{$path}";
            // Perbaikan untuk menghindari path lengkap
            $url = preg_replace('/\/[A-Z]:\//i', '/', $url);
            return $url;
        }
        // Jika path sudah termasuk subfolder saat ini atau adalah file global
        else if (strpos($path, "{$currentSubfolder}/") === 0 || $isGlobalPath) {
            $url = getBaseUrl() . $path;
            // Perbaikan untuk menghindari path lengkap
            $url = preg_replace('/\/[A-Z]:\//i', '/', $url);
            return $url;
        }
        // Path ke subfolder lain
        else {
            $url = getBaseUrl() . $path;
            // Perbaikan untuk menghindari path lengkap
            $url = preg_replace('/\/[A-Z]:\//i', '/', $url);
            return $url;
        }
    }

    // Default - gunakan BASE_URL + path
    $url = getBaseUrl() . $path;
    // Perbaikan untuk menghindari path lengkap
    $url = preg_replace('/\/[A-Z]:\//i', '/', $url);
    return $url;
}

// Definisikan konstanta BASE_URL
if (!defined('BASE_URL')) {
    define('BASE_URL', getBaseUrl());
}

// Definisikan konstanta untuk memudahkan debugging
if (!defined('DEBUG_URL')) {
    define('DEBUG_URL', false); // Diubah menjadi false karena debugging sudah selesai
}

// Fungsi untuk mendapatkan subfolder berdasarkan peran pengguna
function getUserDashboardSubfolder() {
    if (!isset($_SESSION['role_id'])) {
        return '';
    }

    // Mapping role_id ke subfolder
    $roleToSubfolder = [
        1 => 'pemohon',    // Pemohon
        2 => 'dept_head',  // Dept Head
        3 => 'LnD',        // LnD
        4 => 'Fm',         // Factory Manager
        5 => 'Dir',        // Direktur
        99 => 'admin'      // Admin
    ];

    return $roleToSubfolder[$_SESSION['role_id']] ?? '';
}

// Fungsi untuk mendapatkan URL dashboard berdasarkan peran pengguna
function getDashboardUrl() {
    $subfolder = getUserDashboardSubfolder();

    if (!empty($subfolder)) {
        $url = getBaseUrl() . $subfolder . '/index.php';
        // Perbaikan untuk menghindari path lengkap
        $url = preg_replace('/\/[A-Z]:\//i', '/', $url);
        return $url;
    }

    // Default fallback
    $url = getBaseUrl() . 'index.php';
    // Perbaikan untuk menghindari path lengkap
    $url = preg_replace('/\/[A-Z]:\//i', '/', $url);
    return $url;
}

// Fungsi untuk mendapatkan URL menu admin
function getAdminMenuUrl($page) {
    // Pastikan page tidak diawali dengan /
    $page = ltrim($page, '/');

    // Jika pengguna adalah admin, gunakan subfolder admin
    if (isset($_SESSION['role_id']) && $_SESSION['role_id'] == 99) {
        $url = getBaseUrl() . 'admin/' . $page;
        // Perbaikan untuk menghindari path lengkap
        $url = preg_replace('/\/[A-Z]:\//i', '/', $url);
        return $url;
    }

    // Jika bukan admin, gunakan subfolder berdasarkan peran
    $subfolder = getUserDashboardSubfolder();
    if (!empty($subfolder)) {
        $url = getBaseUrl() . $subfolder . '/' . $page;
        // Perbaikan untuk menghindari path lengkap
        $url = preg_replace('/\/[A-Z]:\//i', '/', $url);
        return $url;
    }

    // Default fallback
    $url = getBaseUrl() . $page;
    // Perbaikan untuk menghindari path lengkap
    $url = preg_replace('/\/[A-Z]:\//i', '/', $url);
    return $url;
}

// Fungsi untuk menampilkan informasi debugging
function debugUrl() {
    if (DEBUG_URL) {
        echo '<div style="position: fixed; bottom: 0; right: 0; background: rgba(0,0,0,0.8); color: white; padding: 10px; font-size: 12px; z-index: 9999; max-width: 500px; overflow: auto; max-height: 300px;">';
        echo 'BASE_URL: ' . BASE_URL . '<br>';
        echo 'Current Subfolder: ' . getCurrentSubfolder() . '<br>';
        echo 'Script Name: ' . $_SERVER['SCRIPT_NAME'] . '<br>';
        echo 'User Dashboard Subfolder: ' . getUserDashboardSubfolder() . '<br>';
        echo 'Role ID: ' . ($_SESSION['role_id'] ?? 'Not set') . '<br>';
        echo 'Admin Menu URLs:<br>';
        echo '- employee_management.php: ' . getAdminMenuUrl('employee_management.php') . '<br>';
        echo '- training_management.php: ' . getAdminMenuUrl('training_management.php') . '<br>';
        echo '- settings.php: ' . getAdminMenuUrl('settings.php') . '<br>';
        echo '</div>';
    }
}
