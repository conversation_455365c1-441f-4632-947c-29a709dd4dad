<?php
/**
 * <PERSON><PERSON> untuk melihat percobaan login dengan user yang tidak dikenal
 */

// <PERSON><PERSON> session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include konfigurasi database
require_once '../config/config.php';

// Pastikan hanya admin yang bisa akses
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Fungsi untuk format tanggal
function format_date($date) {
    return date('d M Y H:i:s', strtotime($date));
}

// Ambil data percobaan login dengan user yang tidak dikenal
$query = "SELECT la.*, sl.description, sl.event_type
          FROM login_attempts la
          LEFT JOIN security_logs sl ON la.user_id = sl.user_id AND DATE(la.attempt_time) = DATE(sl.created_at) AND la.ip_address = sl.ip_address
          WHERE la.user_id = 0 AND la.success = 0
          ORDER BY la.attempt_time DESC
          LIMIT 100";
$result = $conn->query($query);

$unknown_attempts = [];
while ($row = $result->fetch_assoc()) {
    $unknown_attempts[] = $row;
}

// Ambil data percobaan login berdasarkan IP address
$query = "SELECT ip_address, COUNT(*) as count, MAX(attempt_time) as last_attempt
          FROM login_attempts
          WHERE user_id = 0 AND success = 0
          GROUP BY ip_address
          ORDER BY count DESC
          LIMIT 20";
$result = $conn->query($query);

$ip_stats = [];
while ($row = $result->fetch_assoc()) {
    $ip_stats[] = $row;
}

// Ambil data percobaan login berdasarkan identifier (dari security_logs)
$query = "SELECT description, COUNT(*) as count
          FROM security_logs
          WHERE user_id = 0 AND event_type = 'LOGIN_UNKNOWN_USER'
          GROUP BY description
          ORDER BY count DESC
          LIMIT 20";
$result = $conn->query($query);

$identifier_stats = [];
while ($row = $result->fetch_assoc()) {
    // Ekstrak identifier dari deskripsi
    if (preg_match('/Percobaan login dengan identifier yang tidak dikenal: (.+)/', $row['description'], $matches)) {
        $identifier = $matches[1];
        $identifier_stats[] = [
            'identifier' => $identifier,
            'count' => $row['count']
        ];
    }
}

// Ambil data rate limiting
$query = "SELECT rl.*, sl.description
          FROM rate_limits rl
          LEFT JOIN security_logs sl ON rl.user_id = sl.user_id AND DATE(rl.attempt_time) = DATE(sl.created_at) AND rl.ip_address = sl.ip_address AND sl.event_type = 'RATE_LIMIT_APPLIED'
          WHERE rl.user_id = 0 AND rl.action_type = 'login'
          ORDER BY rl.attempt_time DESC
          LIMIT 20";
$result = $conn->query($query);

$rate_limits = [];
while ($row = $result->fetch_assoc()) {
    $rate_limits[] = $row;
}

// Tampilkan halaman
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    :root {
        --primary-color: #BF0000;
        --primary-color-dark: #900000;
        --primary-color-light: rgba(191, 0, 0, 0.1);
        --primary-color-lighter: rgba(191, 0, 0, 0.05);
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --info-color: #17a2b8;
        --dark-color: #343a40;
        --light-color: #f8f9fa;
        --border-radius: 10px;
        --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        --transition: all 0.3s ease;
    }

    /* Dashboard Container */
    .dashboard-container {
        padding: 20px;
        background-color: #f5f5f5;
        min-height: calc(100vh - 60px);
    }

    /* Stats Cards */
    .stats-card {
        background-color: #fff;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 20px;
        margin-bottom: 20px;
        transition: var(--transition);
        border-left: 4px solid var(--warning-color);
        position: relative;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: radial-gradient(circle, rgba(255, 193, 7, 0.1) 0%, transparent 70%);
        border-radius: 50%;
        opacity: 0.5;
        z-index: 0;
    }

    .stats-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        position: relative;
        z-index: 1;
    }

    .stats-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }

    /* Tables */
    .table-responsive {
        overflow-x: auto;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        background-color: #fff;
        margin-bottom: 20px;
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        background-color: rgba(255, 193, 7, 0.1);
        color: var(--dark-color);
        font-weight: 600;
        border-bottom: 2px solid var(--warning-color);
        padding: 12px 15px;
    }

    .table tbody tr {
        transition: var(--transition);
    }

    .table tbody tr:hover {
        background-color: rgba(255, 193, 7, 0.05);
    }

    .table tbody td {
        padding: 12px 15px;
        vertical-align: middle;
    }

    /* Status Colors */
    .status-success {
        color: var(--success-color);
        font-weight: 600;
    }

    .status-failed {
        color: var(--danger-color);
        font-weight: 600;
    }

    .status-warning {
        color: var(--warning-color);
        font-weight: 600;
    }

    /* Action Buttons */
    .action-buttons {
        margin-bottom: 20px;
    }

    .action-buttons .btn {
        padding: 10px 20px;
        border-radius: 30px;
        font-weight: 600;
        transition: var(--transition);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .action-buttons .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
    }

    .action-buttons .btn i {
        margin-right: 8px;
    }

    /* Badges */
    .badge {
        padding: 6px 10px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 12px;
    }

    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .fade-in {
        animation: fadeIn 0.5s ease forwards;
    }

    /* Responsive Adjustments */
    @media (max-width: 992px) {
        .action-buttons .btn {
            padding: 8px 15px;
            font-size: 14px;
        }
    }

    @media (max-width: 768px) {
        .stats-card {
            margin-bottom: 15px;
        }

        .table thead th, .table tbody td {
            padding: 10px;
        }
    }

    @media (max-width: 576px) {
        .dashboard-container {
            padding: 10px;
        }

        .stats-card {
            padding: 15px;
        }

        .stats-title {
            font-size: 16px;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .action-buttons .btn {
            width: 100%;
            margin-left: 0 !important;
        }
    }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container-fluid dashboard-container">
    <h1 class="mb-4"><i class="fas fa-user-secret"></i> Percobaan Login dengan User Tidak Dikenal</h1>

    <!-- Action buttons -->
    <div class="row mb-4">
        <div class="col-12 action-buttons">
            <a href="security_monitoring.php" class="btn btn-primary">
                <i class="fas fa-shield-alt"></i> Kembali ke Monitoring Keamanan
            </a>
            <button type="button" class="btn btn-info ms-2" id="refreshData">
                <i class="fas fa-sync-alt"></i> Refresh Data
            </button>
            <button type="button" class="btn btn-success ms-2" id="exportData">
                <i class="fas fa-file-export"></i> Export Data
            </button>
            <button type="button" class="btn btn-danger ms-2" id="blockIPs">
                <i class="fas fa-ban"></i> Blokir IP Mencurigakan
            </button>
        </div>
    </div>

    <!-- Alert untuk notifikasi -->
    <div class="alert alert-success alert-dismissible fade show d-none" role="alert" id="notificationAlert">
        <i class="fas fa-info-circle me-2"></i> <span id="notificationMessage"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <!-- Summary cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">Total Percobaan</div>
                    <i class="fas fa-user-secret text-warning"></i>
                </div>
                <div class="stats-value"><?= count($unknown_attempts) ?></div>
                <div class="stats-subtitle">Percobaan login dengan user tidak dikenal</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">IP Unik</div>
                    <i class="fas fa-globe text-info"></i>
                </div>
                <div class="stats-value"><?= count($ip_stats) ?></div>
                <div class="stats-subtitle">IP address yang berbeda</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">Identifier Unik</div>
                    <i class="fas fa-id-card text-danger"></i>
                </div>
                <div class="stats-value"><?= count($identifier_stats) ?></div>
                <div class="stats-subtitle">NIK/username yang tidak dikenal</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">Rate Limits</div>
                    <i class="fas fa-tachometer-alt text-primary"></i>
                </div>
                <div class="stats-value"><?= count($rate_limits) ?></div>
                <div class="stats-subtitle">IP yang dibatasi aksesnya</div>
            </div>
        </div>
    </div>

    <!-- Tabs for different data views -->
    <ul class="nav nav-tabs mb-4" id="unknownLoginTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="ip-stats-tab" data-bs-toggle="tab" data-bs-target="#ip-stats" type="button" role="tab" aria-controls="ip-stats" aria-selected="true">
                <i class="fas fa-globe"></i> IP Address
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="identifier-stats-tab" data-bs-toggle="tab" data-bs-target="#identifier-stats" type="button" role="tab" aria-controls="identifier-stats" aria-selected="false">
                <i class="fas fa-id-card"></i> Identifier
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="attempts-tab" data-bs-toggle="tab" data-bs-target="#attempts" type="button" role="tab" aria-controls="attempts" aria-selected="false">
                <i class="fas fa-list"></i> Semua Percobaan
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="rate-limits-tab" data-bs-toggle="tab" data-bs-target="#rate-limits" type="button" role="tab" aria-controls="rate-limits" aria-selected="false">
                <i class="fas fa-tachometer-alt"></i> Rate Limits
            </button>
        </li>
    </ul>

    <div class="tab-content" id="unknownLoginTabsContent">
        <!-- Tab IP Stats -->
        <div class="tab-pane fade show active" id="ip-stats" role="tabpanel" aria-labelledby="ip-stats-tab">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">IP Address dengan Percobaan Login Terbanyak</div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="ipStatsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="ipStatsDropdown">
                            <li><a class="dropdown-item" href="#" id="exportIpStats"><i class="fas fa-file-export me-2"></i> Export Data</a></li>
                            <li><a class="dropdown-item" href="#" id="blockSelectedIps"><i class="fas fa-ban me-2"></i> Blokir IP Terpilih</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="refreshIpStats"><i class="fas fa-sync-alt me-2"></i> Refresh Data</a></li>
                        </ul>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAllIps" class="form-check-input"></th>
                                <th>IP Address</th>
                                <th>Jumlah Percobaan</th>
                                <th>Percobaan Terakhir</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ip_stats as $ip): ?>
                            <tr>
                                <td><input type="checkbox" class="form-check-input ip-checkbox" data-ip="<?= $ip['ip_address'] ?>"></td>
                                <td><?= $ip['ip_address'] ?></td>
                                <td class="status-failed"><?= $ip['count'] ?></td>
                                <td><?= format_date($ip['last_attempt']) ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-danger block-ip-btn" data-ip="<?= $ip['ip_address'] ?>">
                                        <i class="fas fa-ban"></i> Blokir
                                    </button>
                                    <button type="button" class="btn btn-sm btn-info view-details-btn" data-ip="<?= $ip['ip_address'] ?>">
                                        <i class="fas fa-search"></i> Detail
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Tab Identifier Stats -->
        <div class="tab-pane fade" id="identifier-stats" role="tabpanel" aria-labelledby="identifier-stats-tab">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">Identifier yang Tidak Dikenal</div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="identifierStatsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="identifierStatsDropdown">
                            <li><a class="dropdown-item" href="#" id="exportIdentifierStats"><i class="fas fa-file-export me-2"></i> Export Data</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="refreshIdentifierStats"><i class="fas fa-sync-alt me-2"></i> Refresh Data</a></li>
                        </ul>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Identifier</th>
                                <th>Jumlah Percobaan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($identifier_stats as $identifier): ?>
                            <tr>
                                <td><?= $identifier['identifier'] ?></td>
                                <td class="status-failed"><?= $identifier['count'] ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-info view-identifier-details-btn" data-identifier="<?= $identifier['identifier'] ?>">
                                        <i class="fas fa-search"></i> Detail
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Tab All Attempts -->
        <div class="tab-pane fade" id="attempts" role="tabpanel" aria-labelledby="attempts-tab">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">Percobaan Login dengan User Tidak Dikenal</div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="attemptsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="attemptsDropdown">
                            <li><a class="dropdown-item" href="#" id="exportAttempts"><i class="fas fa-file-export me-2"></i> Export Data</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="refreshAttempts"><i class="fas fa-sync-alt me-2"></i> Refresh Data</a></li>
                        </ul>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>IP Address</th>
                                <th>User Agent</th>
                                <th>Waktu</th>
                                <th>Deskripsi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($unknown_attempts as $attempt): ?>
                            <tr>
                                <td><?= $attempt['id'] ?></td>
                                <td><?= $attempt['ip_address'] ?></td>
                                <td title="<?= $attempt['user_agent'] ?>"><?= substr($attempt['user_agent'], 0, 50) ?>...</td>
                                <td><?= format_date($attempt['attempt_time']) ?></td>
                                <td><?= $attempt['description'] ?? 'Tidak ada deskripsi' ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Tab Rate Limits -->
        <div class="tab-pane fade" id="rate-limits" role="tabpanel" aria-labelledby="rate-limits-tab">
            <div class="stats-card">
                <div class="stats-header">
                    <div class="stats-title">Rate Limiting untuk IP Address</div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="rateLimitsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="rateLimitsDropdown">
                            <li><a class="dropdown-item" href="#" id="exportRateLimits"><i class="fas fa-file-export me-2"></i> Export Data</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="refreshRateLimits"><i class="fas fa-sync-alt me-2"></i> Refresh Data</a></li>
                        </ul>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>IP Address</th>
                                <th>Jenis Aksi</th>
                                <th>Waktu</th>
                                <th>Deskripsi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($rate_limits as $limit): ?>
                            <tr>
                                <td><?= $limit['id'] ?></td>
                                <td><?= $limit['ip_address'] ?></td>
                                <td><?= $limit['action_type'] ?></td>
                                <td><?= format_date($limit['attempt_time']) ?></td>
                                <td><?= $limit['description'] ?? 'Tidak ada deskripsi' ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fungsi untuk menampilkan notifikasi
        function showNotification(message, type = 'success') {
            const alertElement = document.getElementById('notificationAlert');
            const messageElement = document.getElementById('notificationMessage');

            // Set pesan dan tipe alert
            messageElement.textContent = message;
            alertElement.classList.remove('alert-success', 'alert-danger', 'alert-warning', 'alert-info');
            alertElement.classList.add(`alert-${type}`);

            // Tampilkan alert
            alertElement.classList.remove('d-none');

            // Sembunyikan alert setelah 5 detik
            setTimeout(() => {
                alertElement.classList.add('d-none');
            }, 5000);
        }

        // Aktifkan tab pertama
        const firstTab = document.querySelector('#unknownLoginTabs .nav-link');
        const tab = new bootstrap.Tab(firstTab);
        tab.show();

        // Fungsi untuk refresh data
        document.getElementById('refreshData').addEventListener('click', function() {
            // Animasi loading
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memuat Data...';
            this.disabled = true;

            // Reload halaman setelah 1 detik
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });

        // Fungsi untuk export data
        document.getElementById('exportData').addEventListener('click', function() {
            // Tampilkan modal export
            showExportModal();
        });

        // Fungsi untuk blokir IP mencurigakan
        document.getElementById('blockIPs').addEventListener('click', function() {
            // Tampilkan konfirmasi
            if (confirm('Apakah Anda yakin ingin memblokir semua IP yang mencurigakan?')) {
                // Simulasi proses blokir
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';
                this.disabled = true;

                // Simulasi selesai setelah 2 detik
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-ban"></i> Blokir IP Mencurigakan';
                    this.disabled = false;
                    showNotification('IP mencurigakan berhasil diblokir', 'success');
                }, 2000);
            }
        });

        // Fungsi untuk menampilkan modal export
        function showExportModal() {
            // Buat modal
            const modalHTML = `
                <div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="exportModalLabel">Export Data</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="exportForm">
                                    <div class="mb-3">
                                        <label for="exportType" class="form-label">Pilih Jenis Data</label>
                                        <select class="form-select" id="exportType" required>
                                            <option value="">-- Pilih Jenis Data --</option>
                                            <option value="ip_stats">IP Address</option>
                                            <option value="identifier_stats">Identifier</option>
                                            <option value="unknown_attempts">Semua Percobaan</option>
                                            <option value="rate_limits">Rate Limits</option>
                                            <option value="all">Semua Data</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="exportFormat" class="form-label">Format Export</label>
                                        <select class="form-select" id="exportFormat" required>
                                            <option value="csv">CSV</option>
                                            <option value="excel">Excel</option>
                                            <option value="pdf">PDF</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="dateRange" class="form-label">Rentang Waktu</label>
                                        <select class="form-select" id="dateRange" required>
                                            <option value="today">Hari Ini</option>
                                            <option value="yesterday">Kemarin</option>
                                            <option value="last7days">7 Hari Terakhir</option>
                                            <option value="last30days">30 Hari Terakhir</option>
                                            <option value="thismonth">Bulan Ini</option>
                                            <option value="lastmonth">Bulan Lalu</option>
                                            <option value="all">Semua Waktu</option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                <button type="button" class="btn btn-primary" id="doExport">Export</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Tambahkan modal ke body
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Tampilkan modal
            const exportModal = new bootstrap.Modal(document.getElementById('exportModal'));
            exportModal.show();

            // Event listener untuk tombol export
            document.getElementById('doExport').addEventListener('click', function() {
                const exportType = document.getElementById('exportType').value;
                const exportFormat = document.getElementById('exportFormat').value;
                const dateRange = document.getElementById('dateRange').value;

                if (!exportType || !exportFormat || !dateRange) {
                    showNotification('Silakan lengkapi semua field', 'danger');
                    return;
                }

                // Simulasi export
                exportModal.hide();
                showNotification(`Data ${exportType} berhasil diexport dalam format ${exportFormat}`, 'success');

                // Hapus modal setelah ditutup
                document.getElementById('exportModal').addEventListener('hidden.bs.modal', function() {
                    this.remove();
                });
            });
        }

        // Event listener untuk tombol blokir IP
        const blockIpButtons = document.querySelectorAll('.block-ip-btn');
        blockIpButtons.forEach(button => {
            button.addEventListener('click', function() {
                const ip = this.getAttribute('data-ip');

                // Tampilkan konfirmasi
                if (confirm(`Apakah Anda yakin ingin memblokir IP ${ip}?`)) {
                    // Simulasi proses blokir
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    this.disabled = true;

                    // Simulasi selesai setelah 1 detik
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-check"></i> Diblokir';
                        this.classList.remove('btn-danger');
                        this.classList.add('btn-success');
                        showNotification(`IP ${ip} berhasil diblokir`, 'success');
                    }, 1000);
                }
            });
        });

        // Event listener untuk tombol lihat detail
        const viewDetailsButtons = document.querySelectorAll('.view-details-btn');
        viewDetailsButtons.forEach(button => {
            button.addEventListener('click', function() {
                const ip = this.getAttribute('data-ip');

                // Buat modal detail
                const modalHTML = `
                    <div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="detailModalLabel">Detail IP: ${ip}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <h6>Informasi IP</h6>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>IP Address</th>
                                                    <td>${ip}</td>
                                                </tr>
                                                <tr>
                                                    <th>Lokasi</th>
                                                    <td>Jakarta, Indonesia</td>
                                                </tr>
                                                <tr>
                                                    <th>ISP</th>
                                                    <td>PT. Telkom Indonesia</td>
                                                </tr>
                                                <tr>
                                                    <th>Status</th>
                                                    <td><span class="badge bg-danger">Mencurigakan</span></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <h6>Percobaan Login</h6>
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Waktu</th>
                                                        <th>Identifier</th>
                                                        <th>User Agent</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>${new Date().toLocaleString()}</td>
                                                        <td>31102007</td>
                                                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</td>
                                                    </tr>
                                                    <tr>
                                                        <td>${new Date(Date.now() - 60000).toLocaleString()}</td>
                                                        <td>31102007</td>
                                                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</td>
                                                    </tr>
                                                    <tr>
                                                        <td>${new Date(Date.now() - 120000).toLocaleString()}</td>
                                                        <td>31102007</td>
                                                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                    <button type="button" class="btn btn-danger" id="blockIpInModal" data-ip="${ip}">
                                        <i class="fas fa-ban"></i> Blokir IP
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Tambahkan modal ke body
                document.body.insertAdjacentHTML('beforeend', modalHTML);

                // Tampilkan modal
                const detailModal = new bootstrap.Modal(document.getElementById('detailModal'));
                detailModal.show();

                // Event listener untuk tombol blokir IP di dalam modal
                document.getElementById('blockIpInModal').addEventListener('click', function() {
                    const ip = this.getAttribute('data-ip');

                    // Tampilkan konfirmasi
                    if (confirm(`Apakah Anda yakin ingin memblokir IP ${ip}?`)) {
                        // Simulasi proses blokir
                        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';
                        this.disabled = true;

                        // Simulasi selesai setelah 1 detik
                        setTimeout(() => {
                            detailModal.hide();
                            showNotification(`IP ${ip} berhasil diblokir`, 'success');

                            // Update tombol di tabel
                            const blockButton = document.querySelector(`.block-ip-btn[data-ip="${ip}"]`);
                            if (blockButton) {
                                blockButton.innerHTML = '<i class="fas fa-check"></i> Diblokir';
                                blockButton.classList.remove('btn-danger');
                                blockButton.classList.add('btn-success');
                                blockButton.disabled = true;
                            }
                        }, 1000);
                    }
                });

                // Hapus modal setelah ditutup
                document.getElementById('detailModal').addEventListener('hidden.bs.modal', function() {
                    this.remove();
                });
            });
        });

        // Event listener untuk tombol lihat detail identifier
        const viewIdentifierDetailsButtons = document.querySelectorAll('.view-identifier-details-btn');
        viewIdentifierDetailsButtons.forEach(button => {
            button.addEventListener('click', function() {
                const identifier = this.getAttribute('data-identifier');

                // Buat modal detail
                const modalHTML = `
                    <div class="modal fade" id="identifierDetailModal" tabindex="-1" aria-labelledby="identifierDetailModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="identifierDetailModalLabel">Detail Identifier: ${identifier}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <h6>Informasi Identifier</h6>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>Identifier</th>
                                                    <td>${identifier}</td>
                                                </tr>
                                                <tr>
                                                    <th>Jenis</th>
                                                    <td>NIK</td>
                                                </tr>
                                                <tr>
                                                    <th>Status</th>
                                                    <td><span class="badge bg-danger">Tidak Ditemukan</span></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <h6>Percobaan Login</h6>
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Waktu</th>
                                                        <th>IP Address</th>
                                                        <th>User Agent</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>${new Date().toLocaleString()}</td>
                                                        <td>***********</td>
                                                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</td>
                                                    </tr>
                                                    <tr>
                                                        <td>${new Date(Date.now() - 60000).toLocaleString()}</td>
                                                        <td>***********</td>
                                                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</td>
                                                    </tr>
                                                    <tr>
                                                        <td>${new Date(Date.now() - 120000).toLocaleString()}</td>
                                                        <td>***********</td>
                                                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Tambahkan modal ke body
                document.body.insertAdjacentHTML('beforeend', modalHTML);

                // Tampilkan modal
                const identifierDetailModal = new bootstrap.Modal(document.getElementById('identifierDetailModal'));
                identifierDetailModal.show();

                // Hapus modal setelah ditutup
                document.getElementById('identifierDetailModal').addEventListener('hidden.bs.modal', function() {
                    this.remove();
                });
            });
        });

        // Event listener untuk checkbox select all IPs
        const selectAllIpsCheckbox = document.getElementById('selectAllIps');
        if (selectAllIpsCheckbox) {
            selectAllIpsCheckbox.addEventListener('change', function() {
                const ipCheckboxes = document.querySelectorAll('.ip-checkbox');
                ipCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
        }

        // Event listener untuk tombol blokir IP terpilih
        const blockSelectedIpsButton = document.getElementById('blockSelectedIps');
        if (blockSelectedIpsButton) {
            blockSelectedIpsButton.addEventListener('click', function() {
                const selectedIps = [];
                const ipCheckboxes = document.querySelectorAll('.ip-checkbox:checked');

                ipCheckboxes.forEach(checkbox => {
                    selectedIps.push(checkbox.getAttribute('data-ip'));
                });

                if (selectedIps.length === 0) {
                    showNotification('Silakan pilih IP yang ingin diblokir', 'warning');
                    return;
                }

                // Tampilkan konfirmasi
                if (confirm(`Apakah Anda yakin ingin memblokir ${selectedIps.length} IP terpilih?`)) {
                    // Simulasi proses blokir
                    showNotification(`Memblokir ${selectedIps.length} IP...`, 'info');

                    // Simulasi selesai setelah 2 detik
                    setTimeout(() => {
                        showNotification(`${selectedIps.length} IP berhasil diblokir`, 'success');

                        // Update tombol di tabel
                        ipCheckboxes.forEach(checkbox => {
                            const ip = checkbox.getAttribute('data-ip');
                            const blockButton = document.querySelector(`.block-ip-btn[data-ip="${ip}"]`);

                            if (blockButton) {
                                blockButton.innerHTML = '<i class="fas fa-check"></i> Diblokir';
                                blockButton.classList.remove('btn-danger');
                                blockButton.classList.add('btn-success');
                                blockButton.disabled = true;
                            }
                        });

                        // Uncheck semua checkbox
                        selectAllIpsCheckbox.checked = false;
                        ipCheckboxes.forEach(checkbox => {
                            checkbox.checked = false;
                        });
                    }, 2000);
                }
            });
        }

        // Tambahkan pencarian untuk setiap tabel
        const tables = document.querySelectorAll('.table');
        tables.forEach((table, index) => {
            // Buat input pencarian
            const searchContainer = document.createElement('div');
            searchContainer.className = 'mb-3 position-relative';
            searchContainer.innerHTML = `
                <input type="text" class="form-control" placeholder="Cari..." id="searchTable${index}">
                <i class="fas fa-search position-absolute" style="right: 10px; top: 10px;"></i>
            `;

            // Tambahkan input pencarian sebelum tabel
            table.parentNode.insertBefore(searchContainer, table);

            // Event listener untuk input pencarian
            const searchInput = document.getElementById(`searchTable${index}`);
            searchInput.addEventListener('keyup', function() {
                const searchText = this.value.toLowerCase();
                const rows = table.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(searchText)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        });

        // Tambahkan animasi fade-in untuk semua elemen
        const fadeElements = document.querySelectorAll('.stats-card, .table-responsive');
        fadeElements.forEach((element, index) => {
            element.classList.add('fade-in');
            element.style.animationDelay = `${index * 0.1}s`;
        });
    });
</script>
</body>
</html>
