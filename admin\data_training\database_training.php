<?php
session_start();
include '../../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../../view/login.php');
    exit();
}

// Inisialisasi variabel pesan
$success_message = '';
$error_message = '';

// Initialize variables with default values
$defaults = [
    'nik' => '',
    'nama' => '',
    'staff_status' => '',
    'level' => '',
    'jabatan' => '',
    'pt' => '',
    'departemen' => '',
    'sub_dept' => '',
    'kode_training' => '',
    'nama_training' => '',
    'academy' => '',
    'tempat_training' => '',
    'internal_external' => '',
    'inhouse_outhouse' => '',
    'training_type' => '',
    'trainer_nik' => '',
    'nama_trainer' => '',
    'tanggal_mulai' => '',
    'tanggal_selesai' => '',
    'kode_absen' => '',
    'target_peserta' => '',
    'jam_pembelajaran' => '',
    'program_feedback' => '',
    'trainer_feedback' => '',
    'acomodation_feedback' => '',
    'feedback_form' => '',
    'status_training' => '',
    'no_sertifikat' => '',
    'ikatan_dinas' => '',
    'tgl_mulai_ikatan' => '',
    'akhir_ikatan' => '',
    'pre_test' => '',
    'post_test' => '',
    'nilai_praktek' => '',
    'masa_berlaku_sio' => '',
    'letak_sertifikat' => '',
    'letak_perjanjian' => '',
    'letak_modul' => '',
    'keterangan' => '',
    'biaya_training' => '',
    'backup_staff' => '',
    'jenis_training' => '',
    'bulan' => '',
    'tahun' => ''
];

// Merge POST data with defaults
$input = array_merge($defaults, $_POST);

// Jika form disubmit
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $conn->begin_transaction();

        // Ambil data dari form
        $nik = $input['nik'];
        $nama = $input['nama'];
        $staff_status = $input['staff_status'];
        $level = $input['level'];
        $jabatan = $input['jabatan'];
        $pt = $input['pt'];
        $departemen = $input['departemen'];
        $sub_dept = $input['sub_dept'];
        $kode_training = $input['kode_training'];
        $nama_training = $input['nama_training'];
        $academy = $input['academy'];
        $tempat_training = $input['tempat_training'];
        $internal_external = $input['internal_external'];
        $inhouse_outhouse = $input['inhouse_outhouse'];
        $training_type = $input['training_type'];
        $trainer_nik = $input['trainer_nik'];
        $nama_trainer = $input['nama_trainer'];
        $tanggal_mulai = $input['tanggal_mulai'];
        $tanggal_selesai = $input['tanggal_selesai'];
        $kode_absen = $input['kode_absen'];
        $target_peserta = $input['target_peserta'];
        $jam_pembelajaran = $input['jam_pembelajaran'];
        $program_feedback = $input['program_feedback'];
        $trainer_feedback = $input['trainer_feedback'];
        $acomodation_feedback = $input['acomodation_feedback'];
        $feedback_form = $input['feedback_form'];
        $status_training = $input['status_training'];
        $no_sertifikat = $input['no_sertifikat'];
        $ikatan_dinas = $input['ikatan_dinas'];
        $tgl_mulai_ikatan = $input['tgl_mulai_ikatan'];
        $akhir_ikatan = $input['akhir_ikatan'];
        $pre_test = $input['pre_test'];
        $post_test = $input['post_test'];
        $nilai_praktek = $input['nilai_praktek'];
        $masa_berlaku_sio = $input['masa_berlaku_sio'];
        $letak_sertifikat = $input['letak_sertifikat'];
        $letak_perjanjian = $input['letak_perjanjian'];
        $letak_modul = $input['letak_modul'];
        $keterangan = $input['keterangan'];
        $biaya_training = $input['biaya_training'];
        $backup_staff = $input['backup_staff'];
        $jenis_training = $input['jenis_training'];
        $bulan = $input['bulan'];
        $tahun = $input['tahun'];

        // Query insert
        $query = "INSERT INTO data_training (
            nik, nama, staff_status, level, jabatan, pt, departemen, sub_dept,
            kode_training, nama_training, academy, tempat_training,
            internal_external, inhouse_outhouse, training_type,
            trainer_nik, nama_trainer, tanggal_mulai, tanggal_selesai,
            kode_absen, target_peserta, jam_pembelajaran,
            program_feedback, trainer_feedback, acomodation_feedback, feedback_form,
            status_training, no_sertifikat, ikatan_dinas,
            tgl_mulai_ikatan, akhir_ikatan, pre_test, post_test,
            nilai_praktek, masa_berlaku_sio, letak_sertifikat,
            letak_perjanjian, letak_modul, keterangan, biaya_training,
            backup_staff, jenis_training, bulan, tahun,
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $stmt = $conn->prepare($query);
        $stmt->bind_param(
            "ssssssssssssssssssssssssssssssssssssssssss",
            $nik, $nama, $staff_status, $level, $jabatan, $pt, $departemen, $sub_dept,
            $kode_training, $nama_training, $academy, $tempat_training,
            $internal_external, $inhouse_outhouse, $training_type,
            $trainer_nik, $nama_trainer, $tanggal_mulai, $tanggal_selesai,
            $kode_absen, $target_peserta, $jam_pembelajaran,
            $program_feedback, $trainer_feedback, $acomodation_feedback, $feedback_form,
            $status_training, $no_sertifikat, $ikatan_dinas,
            $tgl_mulai_ikatan, $akhir_ikatan, $pre_test, $post_test,
            $nilai_praktek, $masa_berlaku_sio, $letak_sertifikat,
            $letak_perjanjian, $letak_modul, $keterangan, $biaya_training,
            $backup_staff, $jenis_training, $bulan, $tahun
        );

        if ($stmt->execute()) {
            $conn->commit();
            $success_message = "Training data has been successfully saved!";
        }
    } catch (Exception $e) {
        $conn->rollback();
        $error_message = "Error: " . $e->getMessage();
    }
}

// Ambil data untuk dropdowns
$dept_query = "SELECT DISTINCT dept FROM karyawan WHERE dept IS NOT NULL ORDER BY dept";
$dept_result = $conn->query($dept_query);

$bagian_query = "SELECT DISTINCT bagian FROM karyawan WHERE bagian IS NOT NULL ORDER BY bagian";
$bagian_result = $conn->query($bagian_query);

$jabatan_query = "SELECT DISTINCT jabatan FROM karyawan WHERE jabatan IS NOT NULL ORDER BY jabatan";
$jabatan_result = $conn->query($jabatan_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Training Data</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .navbar {
            background-color: #f8f9fa;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
        }
        .content-wrapper {
            padding: 20px;
        }
        .form-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            background-color: #fff;
        }
        .form-section h4 {
            margin-bottom: 1.5rem;
            color: #0d6efd;
        }
        .alert {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar">
        <div class="container">
            <span class="navbar-brand">Add New Training Data</span>
            <div>
                <a href="view_training.php" class="btn btn-secondary">Back to Training List</a>
            </div>
        </div>
    </nav>

    <div class="container content-wrapper">
        <?php if ($success_message): ?>
            <div class="alert alert-success"><?= htmlspecialchars($success_message) ?></div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger"><?= htmlspecialchars($error_message) ?></div>
        <?php endif; ?>

        <form method="POST" class="needs-validation" novalidate>
            <!-- Participant Data Section -->
            <div class="form-section">
                <h4>Participant Data</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="nik" class="form-label">NIK</label>
                            <input type="text" class="form-control" id="nik" name="nik" required>
                        </div>
                        <div class="mb-3">
                            <label for="nama" class="form-label">Name</label>
                            <input type="text" class="form-control" id="nama" name="nama" required>
                        </div>
                        <div class="mb-3">
                            <label for="staff_status" class="form-label">Staff Status</label>
                            <select class="form-select" id="staff_status" name="staff_status" required>
                                <option value="Staff">Staff</option>
                                <option value="Non Staff">Non Staff</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="departemen" class="form-label">Department</label>
                            <select class="form-select" id="departemen" name="departemen" required>
                                <?php while ($row = $dept_result->fetch_assoc()): ?>
                                    <option value="<?= htmlspecialchars($row['dept']) ?>">
                                        <?= htmlspecialchars($row['dept']) ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="jabatan" class="form-label">Position</label>
                            <select class="form-select" id="jabatan" name="jabatan" required>
                                <?php while ($row = $jabatan_result->fetch_assoc()): ?>
                                    <option value="<?= htmlspecialchars($row['jabatan']) ?>">
                                        <?= htmlspecialchars($row['jabatan']) ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Training Data Section -->
            <div class="form-section">
                <h4>Training Information</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="kode_training" class="form-label">Training Code</label>
                            <input type="text" class="form-control" id="kode_training" name="kode_training" required>
                        </div>
                        <div class="mb-3">
                            <label for="nama_training" class="form-label">Training Name</label>
                            <input type="text" class="form-control" id="nama_training" name="nama_training" required>
                        </div>
                        <div class="mb-3">
                            <label for="training_type" class="form-label">Training Type</label>
                            <select class="form-select" id="training_type" name="training_type" required>
                                <option value="Soft">Soft</option>
                                <option value="Tech">Tech</option>
                                <option value="Compliance">Compliance</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="tanggal_mulai" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="tanggal_mulai" name="tanggal_mulai" required>
                        </div>
                        <div class="mb-3">
                            <label for="tanggal_selesai" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="tanggal_selesai" name="tanggal_selesai" required>
                        </div>
                        <div class="mb-3">
                            <label for="biaya_training" class="form-label">Training Cost</label>
                            <input type="number" class="form-control" id="biaya_training" name="biaya_training" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information Section -->
            <div class="form-section">
                <h4>Additional Information</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="level" class="form-label">Level</label>
                            <input type="text" class="form-control" id="level" name="level">
                        </div>
                        <div class="mb-3">
                            <label for="pt" class="form-label">PT</label>
                            <input type="text" class="form-control" id="pt" name="pt">
                        </div>
                        <div class="mb-3">
                            <label for="sub_dept" class="form-label">Sub Department</label>
                            <input type="text" class="form-control" id="sub_dept" name="sub_dept">
                        </div>
                        <!-- Add other missing fields similarly -->
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="bulan" class="form-label">Month</label>
                            <select class="form-select" id="bulan" name="bulan" required>
                                <?php for($i = 1; $i <= 12; $i++): ?>
                                    <option value="<?= $i ?>"><?= date('F', mktime(0, 0, 0, $i, 1)) ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="tahun" class="form-label">Year</label>
                            <select class="form-select" id="tahun" name="tahun" required>
                                <?php 
                                $currentYear = date('Y');
                                for($i = $currentYear - 5; $i <= $currentYear + 5; $i++): 
                                ?>
                                    <option value="<?= $i ?>" <?= $i == $currentYear ? 'selected' : '' ?>><?= $i ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">Save Training Data</button>
                <a href="view_training.php" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()
                        }
                        form.classList.add('was-validated')
                    }, false)
                })
        })()
    </script>
</body>
</html>






