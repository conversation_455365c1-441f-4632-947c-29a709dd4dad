<?php
// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php untuk koneksi database
include '../config/config.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi variabel
$upload_success = false;
$comparison_results = [];
$total_rows = 0;
$total_new = 0;
$total_changed = 0;
$total_unchanged = 0;
$total_error = 0;
$error_message = '';
$temp_file = '';

// Fungsi untuk membersihkan temporary file
function cleanupTempFile($file) {
    if (file_exists($file)) {
        unlink($file);
    }
}

// Proses upload file
if (isset($_POST['upload']) && isset($_FILES['excel_file'])) {
    // Validasi file
    $allowed_extensions = ['xls', 'xlsx', 'csv'];
    $file_extension = strtolower(pathinfo($_FILES['excel_file']['name'], PATHINFO_EXTENSION));

    if (!in_array($file_extension, $allowed_extensions)) {
        $error_message = "Format file tidak valid. Hanya file Excel (.xls, .xlsx) dan CSV (.csv) yang diperbolehkan.";
    } else {
        // Buat direktori temporary jika belum ada
        $temp_dir = '../temp';
        if (!file_exists($temp_dir)) {
            mkdir($temp_dir, 0777, true);
        }

        // Simpan file ke direktori temporary
        $temp_file = $temp_dir . '/' . time() . '_' . $_FILES['excel_file']['name'];
        if (move_uploaded_file($_FILES['excel_file']['tmp_name'], $temp_file)) {
            $upload_success = true;

            // Proses file Excel
            require_once '../vendor/autoload.php';

            try {
                // Baca file Excel
                $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($temp_file);
                $reader->setReadDataOnly(true);
                $spreadsheet = $reader->load($temp_file);
                $worksheet = $spreadsheet->getActiveSheet();
                $rows = $worksheet->toArray();

                // Hapus header
                $header = array_shift($rows);

                // Tidak perlu validasi header yang ketat
                // Kita akan mengidentifikasi kolom berdasarkan posisi dan konten

                // Cek minimal ada 2 kolom (NIK dan Nama)
                if (count($header) < 2) {
                    $error_message = "Format file tidak valid. Minimal harus ada kolom NIK dan Nama.";
                    $upload_success = false;
                } else {
                    // Deteksi header berdasarkan konten
                    $header_mapping = [];

                    // Daftar kemungkinan nama kolom untuk setiap field
                    $field_patterns = [
                        'nik' => ['nik', 'nomor induk', 'id karyawan', 'employee id', 'no. karyawan', 'no karyawan'],
                        'nama' => ['nama', 'name', 'nama karyawan', 'employee name'],
                        'tgl_masuk' => ['tgl masuk', 'tanggal masuk', 'join date', 'tgl bergabung', 'tanggal bergabung'],
                        'jk' => ['jk', 'jenis kelamin', 'gender', 'sex'],
                        'level_karyawan' => ['level', 'level karyawan', 'lvl', 'lvl karyawan', 'employee level'],
                        'tgl_lahir' => ['tgl lahir', 'tanggal lahir', 'birth date', 'dob', 'date of birth'],
                        'agama' => ['agama', 'religion'],
                        'pendidikan_akhir' => ['pendidikan', 'pendidikan akhir', 'education', 'last education'],
                        'no_telp' => ['no telp', 'no. telp', 'telepon', 'no telepon', 'phone', 'phone number'],
                        'dept' => ['dept', 'departemen', 'department'],
                        'bagian' => ['bagian', 'sub dept', 'sub departemen', 'section'],
                        'jabatan' => ['jabatan', 'position', 'title'],
                        'group' => ['group', 'grup', 'team'],
                        'status' => ['status', 'employee status', 'status karyawan'],
                        'pt' => ['pt', 'perusahaan', 'company']
                    ];

                    // Cari indeks kolom berdasarkan header
                    foreach ($header as $index => $column_name) {
                        if (empty($column_name)) continue;

                        $column_name = strtolower(trim($column_name));
                        foreach ($field_patterns as $field => $patterns) {
                            foreach ($patterns as $pattern) {
                                if (strpos($column_name, $pattern) !== false) {
                                    $header_mapping[$field] = $index;
                                    break 2; // Keluar dari 2 loop jika ditemukan
                                }
                            }
                        }
                    }

                    // Jika tidak ditemukan kolom NIK, gunakan kolom pertama
                    if (!isset($header_mapping['nik'])) {
                        $header_mapping['nik'] = 0;
                    }

                    // Jika tidak ditemukan kolom Nama, gunakan kolom kedua
                    if (!isset($header_mapping['nama'])) {
                        $header_mapping['nama'] = 1;
                    }
                    // Proses data
                    $total_rows = count($rows);

                    // Batasi jumlah baris yang diproses untuk preview (maksimal 100)
                    $preview_limit = min(100, $total_rows);
                    $preview_rows = array_slice($rows, 0, $preview_limit);

                    // Bandingkan data dengan database
                    foreach ($preview_rows as $index => $row) {
                        // Skip baris kosong
                        if (empty(array_filter($row))) {
                            continue;
                        }

                        // Ambil NIK dari baris berdasarkan mapping
                        $nik_index = $header_mapping['nik'];
                        $nama_index = $header_mapping['nama'];

                        $nik = isset($row[$nik_index]) ? trim($row[$nik_index]) : '';
                        $nama = isset($row[$nama_index]) ? trim($row[$nama_index]) : 'Tidak ada nama';

                        if (empty($nik)) {
                            $comparison_results[] = [
                                'row' => $index + 2, // +2 karena index dimulai dari 0 dan ada header
                                'nik' => 'Kosong',
                                'nama' => $nama,
                                'status' => 'error',
                                'message' => 'NIK tidak boleh kosong'
                            ];
                            $total_error++;
                            continue;
                        }

                        // Cek apakah NIK sudah ada di database
                        $stmt = $conn->prepare("SELECT * FROM karyawan WHERE nik = ?");
                        $stmt->bind_param("s", $nik);
                        $stmt->execute();
                        $result = $stmt->get_result();

                        if ($result->num_rows > 0) {
                            // NIK sudah ada, bandingkan data
                            $existing_data = $result->fetch_assoc();

                            // Buat array data dari Excel dengan penanganan yang lebih fleksibel menggunakan header mapping
                            $excel_data = [
                                'nik' => $nik,
                                'nama' => $nama,
                                'tgl_masuk' => isset($header_mapping['tgl_masuk']) && isset($row[$header_mapping['tgl_masuk']]) ? trim($row[$header_mapping['tgl_masuk']]) : '',
                                'jk' => isset($header_mapping['jk']) && isset($row[$header_mapping['jk']]) ? trim($row[$header_mapping['jk']]) : '',
                                'level_karyawan' => isset($header_mapping['level_karyawan']) && isset($row[$header_mapping['level_karyawan']]) ? trim($row[$header_mapping['level_karyawan']]) : '',
                                'tgl_lahir' => isset($header_mapping['tgl_lahir']) && isset($row[$header_mapping['tgl_lahir']]) ? trim($row[$header_mapping['tgl_lahir']]) : '',
                                'agama' => isset($header_mapping['agama']) && isset($row[$header_mapping['agama']]) ? trim($row[$header_mapping['agama']]) : '',
                                'pendidikan_akhir' => isset($header_mapping['pendidikan_akhir']) && isset($row[$header_mapping['pendidikan_akhir']]) ? trim($row[$header_mapping['pendidikan_akhir']]) : '',
                                'no_telp' => isset($header_mapping['no_telp']) && isset($row[$header_mapping['no_telp']]) ? trim($row[$header_mapping['no_telp']]) : '',
                                'dept' => isset($header_mapping['dept']) && isset($row[$header_mapping['dept']]) ? trim($row[$header_mapping['dept']]) : '',
                                'bagian' => isset($header_mapping['bagian']) && isset($row[$header_mapping['bagian']]) ? trim($row[$header_mapping['bagian']]) : '',
                                'jabatan' => isset($header_mapping['jabatan']) && isset($row[$header_mapping['jabatan']]) ? trim($row[$header_mapping['jabatan']]) : '',
                                'group' => isset($header_mapping['group']) && isset($row[$header_mapping['group']]) ? trim($row[$header_mapping['group']]) : '',
                                'status' => isset($header_mapping['status']) && isset($row[$header_mapping['status']]) ? trim($row[$header_mapping['status']]) : 'Aktif', // Default ke 'Aktif' jika kosong
                                'pt' => isset($header_mapping['pt']) && isset($row[$header_mapping['pt']]) ? trim($row[$header_mapping['pt']]) : ''
                            ];

                            // Bandingkan data
                            $changes = [];
                            $has_changes = false;

                            foreach ($excel_data as $key => $value) {
                                if ($key != 'nik' && $existing_data[$key] != $value && !empty($value)) {
                                    $changes[$key] = [
                                        'old' => $existing_data[$key],
                                        'new' => $value
                                    ];
                                    $has_changes = true;
                                }
                            }

                            if ($has_changes) {
                                $comparison_results[] = [
                                    'row' => $index + 2,
                                    'nik' => $nik,
                                    'nama' => $excel_data['nama'],
                                    'status' => 'changed',
                                    'changes' => $changes
                                ];
                                $total_changed++;
                            } else {
                                $comparison_results[] = [
                                    'row' => $index + 2,
                                    'nik' => $nik,
                                    'nama' => $excel_data['nama'],
                                    'status' => 'unchanged',
                                    'message' => 'Data tidak berubah'
                                ];
                                $total_unchanged++;
                            }
                        } else {
                            // NIK belum ada, data baru
                            $comparison_results[] = [
                                'row' => $index + 2,
                                'nik' => $nik,
                                'nama' => $nama,
                                'status' => 'new',
                                'message' => 'Data baru'
                            ];
                            $total_new++;
                        }
                    }
                }
            } catch (Exception $e) {
                $error_message = "Error saat memproses file: " . $e->getMessage();
                $upload_success = false;
            }
        } else {
            $error_message = "Gagal mengupload file. Silakan coba lagi.";
        }
    }
}

// Proses import jika tombol import ditekan
if (isset($_POST['import']) && isset($_POST['temp_file']) && file_exists($_POST['temp_file'])) {
    // Redirect ke halaman upload_karyawan.php dengan parameter file
    $temp_file = $_POST['temp_file'];
    $import_mode = isset($_POST['import_mode']) ? $_POST['import_mode'] : 'update';
    $import_only_changed = isset($_POST['import_only_changed']) ? 1 : 0;

    // Debug info
    error_log("Redirecting to upload_karyawan.php with mode: " . $import_mode);

    // Gunakan POST form untuk mengirim data ke upload_karyawan.php
    // Ini lebih reliable daripada menggunakan parameter URL
    ?>
    <form id="redirectForm" method="post" action="upload_karyawan.php">
        <input type="hidden" name="temp_file" value="<?php echo htmlspecialchars($temp_file); ?>">
        <input type="hidden" name="upload_mode" value="<?php echo htmlspecialchars($import_mode); ?>">
        <input type="hidden" name="import_only_changed" value="<?php echo $import_only_changed; ?>">
        <input type="hidden" name="action" value="upload">
    </form>
    <script>
        document.getElementById('redirectForm').submit();
    </script>
    <?php
    exit;
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    body {
        background-color: #f5f5f5;
        overflow-x: hidden;
    }

    .container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
    }

    .card {
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 20px;
    }

    .card-header {
        background-color: #BF0000 !important;
        color: white;
        padding: 15px 20px;
    }

    .card-header h3 {
        margin: 0;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
    }

    .card-header h3 i {
        margin-right: 10px;
    }

    .card-body {
        padding: 20px;
        background-color: white;
    }

    .upload-section {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
    }

    .form-control {
        width: 100%;
        padding: 10px;
        border: 1px solid #ced4da;
        border-radius: 5px;
    }

    .btn-primary {
        background-color: #BF0000;
        border-color: #BF0000;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #a00000;
        border-color: #a00000;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #5a6268;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 5px;
    }

    .alert-info {
        color: #0c5460;
        background-color: #d1ecf1;
        border-color: #bee5eb;
    }

    .alert-warning {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeeba;
    }

    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .comparison-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }

    .comparison-table th, .comparison-table td {
        padding: 10px;
        border: 1px solid #dee2e6;
    }

    .comparison-table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .comparison-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    .comparison-table tr:hover {
        background-color: #f1f1f1;
    }

    .status-new {
        color: #28a745;
        font-weight: 600;
    }

    .status-changed {
        color: #ffc107;
        font-weight: 600;
    }

    .status-unchanged {
        color: #6c757d;
    }

    .status-error {
        color: #dc3545;
        font-weight: 600;
    }

    .changes-list {
        margin: 0;
        padding: 0;
        list-style: none;
    }

    .changes-list li {
        margin-bottom: 5px;
        font-size: 14px;
    }

    .summary-box {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .summary-label {
        font-weight: 600;
    }

    .summary-value {
        font-weight: 600;
    }

    .summary-value.new {
        color: #28a745;
    }

    .summary-value.changed {
        color: #ffc107;
    }

    .summary-value.unchanged {
        color: #6c757d;
    }

    .summary-value.error {
        color: #dc3545;
    }

    .import-options {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-top: 20px;
    }

    .import-option {
        margin-bottom: 15px;
    }

    .import-option label {
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .import-option input {
        margin-right: 10px;
    }

    .jarak {
        height: 100px;
    }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container">
    <div class="card">
        <div class="card-header">
            <h3><i class="fas fa-sync-alt"></i> Perbandingan Data Sebelum Import</h3>
        </div>
        <div class="card-body">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <?php if (!$upload_success): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Gunakan fitur ini untuk membandingkan data Excel dengan data di database sebelum melakukan import. Sistem akan menampilkan preview perubahan yang akan terjadi.
                </div>

                <form method="post" action="" enctype="multipart/form-data">
                    <div class="upload-section">
                        <div class="form-group">
                            <label for="excel_file">Pilih File Excel</label>
                            <input type="file" name="excel_file" id="excel_file" class="form-control" required>
                            <small class="text-muted">Format file: .xls, .xlsx, .csv</small>
                        </div>
                        <button type="submit" name="upload" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Upload dan Bandingkan
                        </button>
                        <a href="employee_management.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </form>
            <?php else: ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> File berhasil diupload. Berikut adalah hasil perbandingan data.
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> <strong>Informasi Header:</strong> Sistem mendeteksi kolom-kolom berikut dari file Excel Anda:
                    <ul class="mt-2 mb-0">
                        <?php foreach ($header_mapping as $field => $index): ?>
                            <li><strong><?php echo ucfirst(str_replace('_', ' ', $field)); ?>:</strong> Kolom <?php echo $index + 1; ?> (<?php echo isset($header[$index]) ? htmlspecialchars($header[$index]) : 'Tidak ada judul'; ?>)</li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-chart-pie"></i> Ringkasan Data</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="summary-box p-3 border rounded">
                                    <h5>Statistik File Excel</h5>
                                    <div class="summary-item">
                                        <span class="summary-label">Total Baris:</span>
                                        <span class="summary-value"><?php echo number_format($total_rows); ?></span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">Data Baru:</span>
                                        <span class="summary-value new"><?php echo number_format($total_new); ?></span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">Data Berubah:</span>
                                        <span class="summary-value changed"><?php echo number_format($total_changed); ?></span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">Data Tidak Berubah:</span>
                                        <span class="summary-value unchanged"><?php echo number_format($total_unchanged); ?></span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">Error:</span>
                                        <span class="summary-value error"><?php echo number_format($total_error); ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="summary-box p-3 border rounded">
                                    <h5>Perkiraan Hasil Import</h5>
                                    <div class="alert alert-info">
                                        <strong>Mode Lengkap:</strong>
                                        <ul class="mb-0 mt-2">
                                            <li><span class="badge bg-success"><?php echo number_format($total_new); ?></span> data baru akan ditambahkan</li>
                                            <li><span class="badge bg-warning"><?php echo number_format($total_changed); ?></span> data akan diupdate</li>
                                            <li><span class="badge bg-secondary"><?php echo number_format($total_unchanged); ?></span> data tidak berubah (bisa dilewati)</li>
                                        </ul>
                                    </div>
                                    <div class="alert alert-info">
                                        <strong>Mode Hanya Tambah:</strong>
                                        <ul class="mb-0 mt-2">
                                            <li><span class="badge bg-success"><?php echo number_format($total_new); ?></span> data baru akan ditambahkan</li>
                                            <li><span class="badge bg-secondary"><?php echo number_format($total_changed + $total_unchanged); ?></span> data akan dilewati (NIK sudah ada)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if (count($comparison_results) < $total_rows): ?>
                            <div class="alert alert-warning mt-3 mb-0">
                                <i class="fas fa-exclamation-triangle"></i> Hanya menampilkan <?php echo count($comparison_results); ?> baris pertama dari total <?php echo number_format($total_rows); ?> baris.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <form method="post" action="">
                    <input type="hidden" name="temp_file" value="<?php echo htmlspecialchars($temp_file); ?>">
                    <?php
                    // Debug info
                    error_log("Form in compare_import.php with temp_file: " . $temp_file);
                    ?>

                    <div class="alert alert-warning mb-4">
                        <h5><i class="fas fa-exclamation-triangle"></i> Penting: Pilih Mode Import dengan Benar</h5>
                        <p class="mb-2">Untuk <strong>mengupdate data karyawan yang sudah ada</strong>, pastikan Anda memilih <strong>Mode Lengkap</strong>.</p>
                        <p class="mb-0">Jika Anda memilih <strong>Mode Hanya Tambah</strong>, data karyawan yang sudah ada <strong>tidak akan diupdate</strong>.</p>
                    </div>

                    <div class="import-options">
                        <h4>Opsi Import</h4>

                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">1. Pilih Mode Import</h5>
                            </div>
                            <div class="card-body">
                                <div class="import-option mb-3">
                                    <label class="d-flex align-items-start">
                                        <input type="radio" name="import_mode" value="update" checked class="mt-1 me-2">
                                        <div>
                                            <strong>Mode Lengkap:</strong> Update data yang sudah ada dan tambahkan data baru
                                            <div class="text-muted small">
                                                <i class="fas fa-info-circle"></i> Data dengan NIK yang sudah ada di database akan diupdate, data dengan NIK baru akan ditambahkan.
                                            </div>
                                        </div>
                                    </label>
                                </div>
                                <div class="import-option">
                                    <label class="d-flex align-items-start">
                                        <input type="radio" name="import_mode" value="add" class="mt-1 me-2">
                                        <div>
                                            <strong>Mode Hanya Tambah:</strong> Hanya tambahkan data baru
                                            <div class="text-muted small">
                                                <i class="fas fa-info-circle"></i> Hanya data dengan NIK yang belum ada di database yang akan ditambahkan. Data dengan NIK yang sudah ada akan dilewati.
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">2. Opsi Tambahan</h5>
                            </div>
                            <div class="card-body">
                                <div class="import-option">
                                    <label class="d-flex align-items-start">
                                        <input type="checkbox" name="import_only_changed" value="1" checked class="mt-1 me-2">
                                        <div>
                                            <strong>Optimasi Import:</strong> Hanya import data yang berubah
                                            <div class="text-muted small">
                                                <i class="fas fa-info-circle"></i> Jika dicentang, sistem hanya akan mengupdate data yang benar-benar berubah. Data yang sama persis dengan yang sudah ada di database akan dilewati.
                                                <br>
                                                <i class="fas fa-exclamation-triangle text-warning"></i> Opsi ini hanya berpengaruh pada Mode Lengkap.
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle"></i> <strong>Mode yang dipilih:</strong>
                            <span id="selectedModeText" class="fw-bold">Mode Lengkap</span>
                            <span id="onlyChangedText" class="ms-2">(Hanya import data yang berubah)</span>
                        </div>

                        <button type="submit" name="import" class="btn btn-primary btn-lg">
                            <i class="fas fa-file-import"></i> Lanjutkan Import Sesuai Pengaturan
                        </button>
                        <a href="compare_import.php" class="btn btn-secondary">
                            <i class="fas fa-redo"></i> Upload File Lain
                        </a>
                    </div>
                </form>

                <h4 class="mt-4">Preview Data</h4>
                <div class="table-responsive">
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Baris</th>
                                <th>NIK</th>
                                <th>Nama</th>
                                <th>Status</th>
                                <th>Detail</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($comparison_results as $result): ?>
                                <tr>
                                    <td><?php echo $result['row']; ?></td>
                                    <td><?php echo htmlspecialchars($result['nik']); ?></td>
                                    <td><?php echo htmlspecialchars($result['nama']); ?></td>
                                    <td>
                                        <?php if ($result['status'] == 'new'): ?>
                                            <span class="status-new">Data Baru</span>
                                        <?php elseif ($result['status'] == 'changed'): ?>
                                            <span class="status-changed">Data Berubah</span>
                                        <?php elseif ($result['status'] == 'unchanged'): ?>
                                            <span class="status-unchanged">Tidak Berubah</span>
                                        <?php elseif ($result['status'] == 'error'): ?>
                                            <span class="status-error">Error</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($result['status'] == 'changed'): ?>
                                            <ul class="changes-list">
                                                <?php foreach ($result['changes'] as $field => $change): ?>
                                                    <li>
                                                        <strong><?php echo ucfirst($field); ?>:</strong>
                                                        <span class="text-muted"><?php echo htmlspecialchars($change['old']); ?></span>
                                                        <i class="fas fa-arrow-right mx-1"></i>
                                                        <span class="text-success"><?php echo htmlspecialchars($change['new']); ?></span>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        <?php elseif (isset($result['message'])): ?>
                                            <?php echo htmlspecialchars($result['message']); ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
// JavaScript untuk menangani perubahan mode import
document.addEventListener('DOMContentLoaded', function() {
    // Ambil elemen radio button mode import
    const modeUpdateRadio = document.querySelector('input[name="import_mode"][value="update"]');
    const modeAddRadio = document.querySelector('input[name="import_mode"][value="add"]');
    const onlyChangedCheckbox = document.querySelector('input[name="import_only_changed"]');
    const onlyChangedContainer = onlyChangedCheckbox ? onlyChangedCheckbox.closest('.import-option') : null;

    // Fungsi untuk mengupdate visibilitas dan status checkbox "Hanya import data yang berubah"
    function updateOnlyChangedVisibility() {
        if (!onlyChangedContainer) return;

        if (modeAddRadio && modeAddRadio.checked) {
            // Jika mode "Hanya Tambah" dipilih, disable checkbox "Hanya import data yang berubah"
            onlyChangedCheckbox.disabled = true;
            onlyChangedContainer.classList.add('text-muted');
            // Tambahkan pesan peringatan
            const warningElement = document.querySelector('.import-option .text-warning');
            if (warningElement) {
                warningElement.style.display = 'inline';
            }

            // Highlight mode Hanya Tambah
            document.querySelectorAll('.alert-info').forEach(function(el, index) {
                if (index === 0) { // Mode Lengkap
                    el.classList.remove('alert-info');
                    el.classList.add('alert-secondary');
                } else if (index === 1) { // Mode Hanya Tambah
                    el.classList.remove('alert-info');
                    el.classList.add('alert-primary');
                }
            });
        } else {
            // Jika mode "Lengkap" dipilih, enable checkbox "Hanya import data yang berubah"
            onlyChangedCheckbox.disabled = false;
            onlyChangedContainer.classList.remove('text-muted');
            // Sembunyikan pesan peringatan
            const warningElement = document.querySelector('.import-option .text-warning');
            if (warningElement) {
                warningElement.style.display = 'inline';
            }

            // Highlight mode Lengkap
            document.querySelectorAll('.alert-info, .alert-secondary, .alert-primary').forEach(function(el, index) {
                if (index === 0) { // Mode Lengkap
                    el.classList.remove('alert-secondary');
                    el.classList.add('alert-primary');
                } else if (index === 1) { // Mode Hanya Tambah
                    el.classList.remove('alert-primary');
                    el.classList.add('alert-secondary');
                }
            });
        }
    }

    // Fungsi untuk mengupdate tampilan berdasarkan status checkbox "Hanya import data yang berubah"
    function updateOnlyChangedStatus() {
        if (!onlyChangedCheckbox || onlyChangedCheckbox.disabled) return;

        // Ambil elemen alert untuk Mode Lengkap
        const modeLengkapAlert = document.querySelectorAll('.alert-info, .alert-primary, .alert-secondary')[0];
        if (!modeLengkapAlert) return;

        // Update tampilan berdasarkan status checkbox
        if (onlyChangedCheckbox.checked) {
            // Jika checkbox dicentang, highlight baris "data tidak berubah (bisa dilewati)"
            const listItems = modeLengkapAlert.querySelectorAll('li');
            if (listItems.length >= 3) {
                listItems[2].classList.add('fw-bold');
                listItems[2].style.color = '#0d6efd';
            }
        } else {
            // Jika checkbox tidak dicentang, hapus highlight
            const listItems = modeLengkapAlert.querySelectorAll('li');
            if (listItems.length >= 3) {
                listItems[2].classList.remove('fw-bold');
                listItems[2].style.color = '';
            }
        }
    }

    // Fungsi untuk mengupdate teks mode yang dipilih
    function updateSelectedModeText() {
        const selectedModeText = document.getElementById('selectedModeText');
        const onlyChangedText = document.getElementById('onlyChangedText');

        if (!selectedModeText || !onlyChangedText) return;

        // Update teks mode yang dipilih
        if (modeAddRadio && modeAddRadio.checked) {
            selectedModeText.textContent = "Mode Hanya Tambah";
            onlyChangedText.style.display = "none";
        } else {
            selectedModeText.textContent = "Mode Lengkap";

            // Update teks opsi tambahan
            if (onlyChangedCheckbox && onlyChangedCheckbox.checked) {
                onlyChangedText.style.display = "inline";
                onlyChangedText.textContent = "(Hanya import data yang berubah)";
            } else {
                onlyChangedText.style.display = "inline";
                onlyChangedText.textContent = "(Import semua data)";
            }
        }
    }

    // Tambahkan event listener untuk perubahan mode import
    if (modeUpdateRadio) {
        modeUpdateRadio.addEventListener('change', function() {
            updateOnlyChangedVisibility();
            updateSelectedModeText();
        });
    }
    if (modeAddRadio) {
        modeAddRadio.addEventListener('change', function() {
            updateOnlyChangedVisibility();
            updateSelectedModeText();
        });
    }

    // Tambahkan event listener untuk checkbox "Hanya import data yang berubah"
    if (onlyChangedCheckbox) {
        onlyChangedCheckbox.addEventListener('change', function() {
            updateOnlyChangedStatus();
            updateSelectedModeText();
        });
    }

    // Jalankan fungsi saat halaman dimuat
    updateOnlyChangedVisibility();
    updateOnlyChangedStatus();
    updateSelectedModeText();

    // Tambahkan konfirmasi sebelum submit form
    const importForm = document.querySelector('form');
    if (importForm) {
        importForm.addEventListener('submit', function() {
            // Ambil mode yang dipilih
            const selectedMode = document.querySelector('input[name="import_mode"]:checked').value;
            const onlyChanged = onlyChangedCheckbox && !onlyChangedCheckbox.disabled && onlyChangedCheckbox.checked;

            // Debug info
            console.log("Form submit with mode:", selectedMode, "Only changed:", onlyChanged);

            // Tambahkan debug info ke form
            const debugInfo = document.createElement('input');
            debugInfo.type = 'hidden';
            debugInfo.name = 'debug_info';
            debugInfo.value = "Mode: " + selectedMode + ", Only changed: " + (onlyChanged ? "Yes" : "No");
            this.appendChild(debugInfo);

            let confirmMessage = "";

            if (selectedMode === "update") {
                confirmMessage = "Anda akan mengimport data dengan Mode Lengkap:\n\n";
                confirmMessage += "- Data dengan NIK baru akan ditambahkan\n";
                confirmMessage += "- Data dengan NIK yang sudah ada akan diupdate\n";

                if (onlyChanged) {
                    confirmMessage += "- Data yang tidak berubah akan dilewati\n";
                }
            } else {
                confirmMessage = "Anda akan mengimport data dengan Mode Hanya Tambah:\n\n";
                confirmMessage += "- Hanya data dengan NIK baru yang akan ditambahkan\n";
                confirmMessage += "- Data dengan NIK yang sudah ada akan dilewati\n";
            }

            confirmMessage += "\nLanjutkan import?";

            confirmAction(confirmMessage, function() {
                // Submit the form to continue with the import process
                importForm.submit();
            });
        });
    }
});
</script>
</body>
</html>

<?php
// Cleanup temporary file jika user tidak melanjutkan ke import
if (isset($_POST['upload']) && $upload_success && !isset($_POST['import'])) {
    register_shutdown_function('cleanupTempFile', $temp_file);
}
?>
