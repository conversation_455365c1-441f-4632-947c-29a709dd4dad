# Panduan Sistem Manajemen Peserta dan Absensi Training

## 📋 Overview
Sistem ini memungkinkan admin untuk mengatur siapa saja yang bisa datang dan absen pada training internal. Sistem terdiri dari dua komponen utama:
1. **Manajemen Peserta** - Mendaftarkan karyawan yang diizinkan mengikuti training
2. **Sistem Absensi** - Mencatat kehadiran peserta yang sudah terdaftar

## 🗄️ Database Schema

### Tabel `offline_training_participants`
```sql
CREATE TABLE `offline_training_participants` (
  `id` int NOT NULL AUTO_INCREMENT,
  `offline_training_id` int NOT NULL,
  `karyawan_id` int NOT NULL,
  `nik` varchar(20) NOT NULL,
  `nama` varchar(100) NOT NULL,
  `jabatan` varchar(100) DEFAULT NULL,
  `departemen` varchar(100) DEFAULT NULL,
  `bagian` varchar(100) DEFAULT NULL,
  `status` enum('registered','confirmed','cancelled') NOT NULL DEFAULT 'registered',
  `registered_by` int DEFAULT NULL,
  `registered_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `notes` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_participant` (`offline_training_id`,`karyawan_id`)
);
```

## 🎯 Fitur Utama

### 1. **Kelola Peserta Training**
- ✅ Tambah peserta dari database karyawan
- ✅ Pencarian karyawan berdasarkan nama/NIK
- ✅ Filter berdasarkan departemen
- ✅ Pilih multiple karyawan sekaligus
- ✅ Hapus peserta dari daftar
- ✅ Lihat status pendaftaran peserta

### 2. **Sinkronisasi ke Absensi**
- ✅ Otomatis menambahkan peserta terdaftar ke daftar absensi
- ✅ Status default "tidak hadir" untuk tracking
- ✅ Mencegah duplikasi data absensi
- ✅ Bulk sync semua peserta

### 3. **Validasi Absensi (Opsional)**
- ✅ Cek apakah karyawan terdaftar sebagai peserta
- ✅ Peringatan jika karyawan tidak terdaftar
- ✅ Fleksibilitas untuk mengizinkan/menolak non-peserta

## 🚀 Cara Penggunaan

### **A. Mengelola Peserta Training**

1. **Akses Halaman Absensi:**
   ```
   Admin → Training Management → View Attendance → Tab "Kelola Peserta"
   ```

2. **Menambah Peserta:**
   - Klik tombol "Tambah Peserta"
   - Cari karyawan berdasarkan nama/NIK
   - Filter berdasarkan departemen (opsional)
   - Centang karyawan yang ingin ditambahkan
   - Klik "Tambah Peserta Terpilih"

3. **Menghapus Peserta:**
   - Di tab "Kelola Peserta", klik tombol hapus (🗑️)
   - Konfirmasi penghapusan

4. **Sinkronisasi ke Absensi:**
   - Klik tombol "Sinkron ke Absensi"
   - Sistem akan menambahkan semua peserta terdaftar ke daftar absensi
   - Status default: "tidak hadir"

### **B. Sistem Absensi RFID**

1. **Absensi Normal:**
   - Scan kartu RFID atau input NIK manual
   - Sistem akan cek apakah karyawan terdaftar sebagai peserta
   - Jika terdaftar: absensi berhasil
   - Jika tidak terdaftar: peringatan (tapi masih bisa absen)

2. **Mode Validasi Ketat (Opsional):**
   ```php
   // Uncomment di rfid_attendance_offline.php line 77-83
   if (!$is_registered_participant) {
       $error = "Karyawan tidak terdaftar sebagai peserta training ini";
       // Tolak absensi
   }
   ```

## 📱 Interface Pengguna

### **Tab Navigation**
```
[Daftar Kehadiran] [Kelola Peserta]
```

### **Tab Kelola Peserta**
- **Header:** Judul + Tombol "Tambah Peserta" + "Sinkron ke Absensi"
- **Tabel:** NIK, Nama, Departemen, Jabatan, Status, Tanggal Daftar, Aksi
- **Aksi:** Hapus peserta

### **Modal Tambah Peserta**
- **Search:** Input pencarian nama/NIK
- **Filter:** Dropdown departemen
- **Tabel:** Checkbox + NIK + Nama + Departemen + Jabatan
- **Select All:** Checkbox untuk pilih semua
- **Button:** "Tambah Peserta Terpilih"

## 🔧 API Endpoints

### **GET Requests:**
```
manage_participants_api.php?action=get_participants&training_id=X
manage_participants_api.php?action=get_employees
manage_participants_api.php?action=get_departments
```

### **POST Requests:**
```json
// Add participants
{
  "action": "add_participants",
  "training_id": 123,
  "employees": [
    {"id": 1, "nik": "12345", "nama": "John Doe"},
    {"id": 2, "nik": "67890", "nama": "Jane Smith"}
  ]
}

// Remove participant
{
  "action": "remove_participant",
  "participant_id": 456
}

// Sync to attendance
{
  "action": "sync_to_attendance",
  "training_id": 123
}
```

## 🛡️ Security & Validation

### **Access Control:**
- ✅ Hanya admin yang bisa mengelola peserta
- ✅ Session validation untuk semua operasi
- ✅ SQL injection protection dengan prepared statements

### **Data Validation:**
- ✅ Unique constraint untuk mencegah duplikasi peserta
- ✅ Foreign key constraints untuk data integrity
- ✅ Input sanitization untuk semua user input

### **Error Handling:**
- ✅ Transaction rollback untuk operasi batch
- ✅ Detailed error messages untuk debugging
- ✅ Graceful fallback untuk missing data

## 🔄 Workflow Lengkap

### **1. Persiapan Training:**
```
Admin → Buat Training → Tambah Peserta → Sinkron ke Absensi
```

### **2. Hari Training:**
```
Peserta → Scan RFID → Validasi Peserta → Record Absensi
```

### **3. Monitoring:**
```
Admin → View Attendance → Monitor Real-time → Export Report
```

## 📊 Status Peserta

### **Status Pendaftaran:**
- **`registered`** - Terdaftar sebagai peserta
- **`confirmed`** - Konfirmasi kehadiran
- **`cancelled`** - Dibatalkan

### **Status Absensi:**
- **`hadir`** - Hadir tepat waktu
- **`terlambat`** - Hadir terlambat
- **`izin`** - Izin tidak hadir
- **`tidak hadir`** - Tidak hadir tanpa keterangan

## 🎯 Benefits

### **Untuk Admin:**
- ✅ Kontrol penuh atas peserta training
- ✅ Tracking yang akurat dan real-time
- ✅ Laporan kehadiran yang detail
- ✅ Mencegah peserta tidak diundang

### **Untuk Peserta:**
- ✅ Proses absensi yang cepat dengan RFID
- ✅ Konfirmasi real-time saat absen
- ✅ Status kehadiran yang jelas

### **Untuk Organisasi:**
- ✅ Data training yang terstruktur
- ✅ Compliance dengan prosedur training
- ✅ Analytics untuk evaluasi program training
- ✅ Audit trail yang lengkap

## 🔧 Customization Options

### **Validasi Peserta:**
- Mode fleksibel: Peringatan tapi tetap bisa absen
- Mode ketat: Hanya peserta terdaftar yang bisa absen

### **Auto-sync:**
- Manual sync: Admin trigger sinkronisasi
- Auto sync: Otomatis saat training dimulai

### **Notification:**
- Email reminder untuk peserta terdaftar
- SMS notification untuk absensi
- Dashboard alerts untuk admin
