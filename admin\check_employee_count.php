<?php
// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php untuk koneksi database
include '../config/config.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi array untuk menyimpan hasil query
$results = [];

// Query untuk menghitung total karyawan
$total_query = "SELECT COUNT(*) as total FROM karyawan";
$total_result = $conn->query($total_query);
$results['total'] = $total_result->fetch_assoc()['total'];

// Query untuk menghitung karyawan berdasarkan status
$status_query = "SELECT status, COUNT(*) as count FROM karyawan GROUP BY status ORDER BY count DESC";
$status_result = $conn->query($status_query);
$results['status'] = [];
while ($row = $status_result->fetch_assoc()) {
    $results['status'][] = $row;
}

// Query untuk menghitung karyawan dengan status NULL atau kosong
$null_status_query = "SELECT COUNT(*) as count FROM karyawan WHERE status IS NULL OR status = ''";
$null_status_result = $conn->query($null_status_query);
$results['null_status'] = $null_status_result->fetch_assoc()['count'];

// Query untuk menghitung karyawan berdasarkan PT
$pt_query = "SELECT pt, COUNT(*) as count FROM karyawan GROUP BY pt ORDER BY count DESC";
$pt_result = $conn->query($pt_query);
$results['pt'] = [];
while ($row = $pt_result->fetch_assoc()) {
    $results['pt'][] = $row;
}

// Query untuk menghitung karyawan dengan PT NULL atau kosong
$null_pt_query = "SELECT COUNT(*) as count FROM karyawan WHERE pt IS NULL OR pt = ''";
$null_pt_result = $conn->query($null_pt_query);
$results['null_pt'] = $null_pt_result->fetch_assoc()['count'];

// Query untuk menghitung karyawan berdasarkan departemen
$dept_query = "SELECT dept, COUNT(*) as count FROM karyawan GROUP BY dept ORDER BY count DESC LIMIT 10";
$dept_result = $conn->query($dept_query);
$results['dept'] = [];
while ($row = $dept_result->fetch_assoc()) {
    $results['dept'][] = $row;
}

// Query untuk menghitung karyawan dengan departemen NULL atau kosong
$null_dept_query = "SELECT COUNT(*) as count FROM karyawan WHERE dept IS NULL OR dept = ''";
$null_dept_result = $conn->query($null_dept_query);
$results['null_dept'] = $null_dept_result->fetch_assoc()['count'];

// Query untuk melihat 10 karyawan terakhir yang ditambahkan (berdasarkan ID)
$recent_query = "SELECT id, nik, nama, status, pt FROM karyawan ORDER BY id DESC LIMIT 10";
$recent_result = $conn->query($recent_query);
$results['recent'] = [];
while ($row = $recent_result->fetch_assoc()) {
    $results['recent'][] = $row;
}

// Tutup koneksi database
$conn->close();
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    body {
        background-color: #f5f5f5;
        overflow-x: hidden;
    }

    .container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
    }

    .card {
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 20px;
    }

    .card-header {
        background-color: #BF0000 !important;
        color: white;
        padding: 15px 20px;
    }

    .card-header h3 {
        margin: 0;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
    }

    .card-header h3 i {
        margin-right: 10px;
    }

    .card-body {
        padding: 20px;
        background-color: white;
    }

    .stats-container {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 20px;
    }

    .stat-card {
        flex: 1;
        min-width: 200px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 20px;
        text-align: center;
    }

    .stat-card h4 {
        margin-top: 0;
        color: #333;
        font-size: 1.2rem;
    }

    .stat-card .number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #BF0000;
        margin: 10px 0;
    }

    .stat-card .description {
        color: #666;
        font-size: 0.9rem;
    }

    .table-container {
        overflow-x: auto;
        margin-top: 20px;
    }

    .table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .table th {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 600;
        text-align: left;
        padding: 12px 15px;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        padding: 12px 15px;
        border-bottom: 1px solid #e9ecef;
        color: #495057;
    }

    .table tr:hover {
        background-color: #f8f9fa;
    }

    .btn-primary {
        background-color: #BF0000;
        border-color: #BF0000;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #a00000;
        border-color: #a00000;
    }

    .jarak {
        height: 100px;
    }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>
<div class="container">
    <div class="card">
        <div class="card-header">
            <h3><i class="fas fa-database"></i> Statistik Data Karyawan</h3>
        </div>
        <div class="card-body">
            <div class="stats-container">
                <div class="stat-card">
                    <h4>Total Karyawan</h4>
                    <div class="number"><?php echo number_format($results['total']); ?></div>
                    <div class="description">Jumlah total karyawan dalam database</div>
                </div>
                
                <div class="stat-card">
                    <h4>Status Kosong</h4>
                    <div class="number"><?php echo number_format($results['null_status']); ?></div>
                    <div class="description">Karyawan tanpa status</div>
                </div>
                
                <div class="stat-card">
                    <h4>PT Kosong</h4>
                    <div class="number"><?php echo number_format($results['null_pt']); ?></div>
                    <div class="description">Karyawan tanpa PT</div>
                </div>
                
                <div class="stat-card">
                    <h4>Dept Kosong</h4>
                    <div class="number"><?php echo number_format($results['null_dept']); ?></div>
                    <div class="description">Karyawan tanpa departemen</div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-pie"></i> Distribusi Status</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Status</th>
                                            <th>Jumlah</th>
                                            <th>Persentase</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($results['status'] as $status): ?>
                                            <tr>
                                                <td><?php echo $status['status'] ? htmlspecialchars($status['status']) : '<em>NULL/Empty</em>'; ?></td>
                                                <td><?php echo number_format($status['count']); ?></td>
                                                <td><?php echo number_format(($status['count'] / $results['total']) * 100, 2); ?>%</td>
                                            </tr>
                                        <?php endforeach; ?>
                                        <?php if ($results['null_status'] > 0): ?>
                                            <tr>
                                                <td><em>NULL/Empty</em></td>
                                                <td><?php echo number_format($results['null_status']); ?></td>
                                                <td><?php echo number_format(($results['null_status'] / $results['total']) * 100, 2); ?>%</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-building"></i> Distribusi PT</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>PT</th>
                                            <th>Jumlah</th>
                                            <th>Persentase</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($results['pt'] as $pt): ?>
                                            <tr>
                                                <td><?php echo $pt['pt'] ? htmlspecialchars($pt['pt']) : '<em>NULL/Empty</em>'; ?></td>
                                                <td><?php echo number_format($pt['count']); ?></td>
                                                <td><?php echo number_format(($pt['count'] / $results['total']) * 100, 2); ?>%</td>
                                            </tr>
                                        <?php endforeach; ?>
                                        <?php if ($results['null_pt'] > 0): ?>
                                            <tr>
                                                <td><em>NULL/Empty</em></td>
                                                <td><?php echo number_format($results['null_pt']); ?></td>
                                                <td><?php echo number_format(($results['null_pt'] / $results['total']) * 100, 2); ?>%</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-sitemap"></i> Top 10 Departemen</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Departemen</th>
                                            <th>Jumlah</th>
                                            <th>Persentase</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($results['dept'] as $dept): ?>
                                            <tr>
                                                <td><?php echo $dept['dept'] ? htmlspecialchars($dept['dept']) : '<em>NULL/Empty</em>'; ?></td>
                                                <td><?php echo number_format($dept['count']); ?></td>
                                                <td><?php echo number_format(($dept['count'] / $results['total']) * 100, 2); ?>%</td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-clock"></i> 10 Karyawan Terakhir Ditambahkan</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>NIK</th>
                                            <th>Nama</th>
                                            <th>Status</th>
                                            <th>PT</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($results['recent'] as $employee): ?>
                                            <tr>
                                                <td><?php echo $employee['id']; ?></td>
                                                <td><?php echo htmlspecialchars($employee['nik']); ?></td>
                                                <td><?php echo htmlspecialchars($employee['nama']); ?></td>
                                                <td><?php echo $employee['status'] ? htmlspecialchars($employee['status']) : '<em>NULL/Empty</em>'; ?></td>
                                                <td><?php echo $employee['pt'] ? htmlspecialchars($employee['pt']) : '<em>NULL/Empty</em>'; ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <a href="employee_management.php" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Manajemen Karyawan
                </a>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>
</body>
</html>
