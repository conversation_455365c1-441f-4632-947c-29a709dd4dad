<?php
session_start();
include '../config/config.php';

echo "<h2>Testing Department Head Query Fix</h2>";

// Test users table structure
echo "<h3>1. Testing Users Table Structure:</h3>";

$users_structure_query = "DESCRIBE users";
$result = $conn->query($users_structure_query);

if ($result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ Error getting users table structure: " . $conn->error . "</p>";
}

// Test karyawan table structure
echo "<h3>2. Testing Karyawan Table Structure:</h3>";

$karyawan_structure_query = "DESCRIBE karyawan";
$result = $conn->query($karyawan_structure_query);

if ($result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ Error getting karyawan table structure: " . $conn->error . "</p>";
}

// Test sample departments
echo "<h3>3. Testing Sample Departments:</h3>";

$dept_query = "SELECT DISTINCT dept FROM karyawan WHERE dept IS NOT NULL AND dept != '' LIMIT 10";
$result = $conn->query($dept_query);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Department</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>{$row['dept']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p>⚠️ No departments found or error: " . $conn->error . "</p>";
}

// Test the fixed Department Head query
echo "<h3>4. Testing Fixed Department Head Query:</h3>";

// Get a sample department
$sample_dept_query = "SELECT DISTINCT dept FROM karyawan WHERE dept IS NOT NULL AND dept != '' LIMIT 1";
$result = $conn->query($sample_dept_query);

if ($result && $result->num_rows > 0) {
    $dept_row = $result->fetch_assoc();
    $test_dept = $dept_row['dept'];
    
    echo "<p><strong>Testing with Department: {$test_dept}</strong></p>";
    
    // Test the fixed query
    $dept_head_query = "SELECT u.name, u.email FROM users u 
                       JOIN karyawan k ON u.nik = k.nik 
                       WHERE k.dept = ? AND k.jabatan IN ('Supervisor', 'Chief') 
                       AND k.level_karyawan >= 4 
                       AND u.is_active = 1
                       ORDER BY k.level_karyawan DESC LIMIT 1";
    
    $stmt = $conn->prepare($dept_head_query);
    $stmt->bind_param("s", $test_dept);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<p>✅ Query executed successfully! Found department head.</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Name</th><th>Email</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['name']}</td>";
            echo "<td>{$row['email']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>⚠️ No department head found for department {$test_dept}</p>";
        
        // Try alternative query without level restriction
        echo "<p><strong>Trying alternative query without level restriction:</strong></p>";
        $alt_query = "SELECT u.name, u.email, k.jabatan, k.level_karyawan FROM users u 
                     JOIN karyawan k ON u.nik = k.nik 
                     WHERE k.dept = ? AND k.jabatan IN ('Supervisor', 'Chief') 
                     AND u.is_active = 1
                     ORDER BY k.level_karyawan DESC LIMIT 5";
        
        $alt_stmt = $conn->prepare($alt_query);
        $alt_stmt->bind_param("s", $test_dept);
        $alt_stmt->execute();
        $alt_result = $alt_stmt->get_result();
        
        if ($alt_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Name</th><th>Email</th><th>Jabatan</th><th>Level</th></tr>";
            
            while ($row = $alt_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['name']}</td>";
                echo "<td>{$row['email']}</td>";
                echo "<td>{$row['jabatan']}</td>";
                echo "<td>{$row['level_karyawan']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>⚠️ No supervisors or chiefs found for department {$test_dept}</p>";
        }
        $alt_stmt->close();
    }
    $stmt->close();
} else {
    echo "<p>⚠️ No department data found to test with</p>";
}

// Test director email function
echo "<h3>5. Testing Director Email Function:</h3>";

try {
    require_once '../config/director_approval_email_helper.php';
    echo "<p>✅ director_approval_email_helper.php loaded successfully</p>";
    
    if (function_exists('sendDirectorApprovalNotification')) {
        echo "<p>✅ sendDirectorApprovalNotification function exists</p>";
    } else {
        echo "<p>❌ sendDirectorApprovalNotification function not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error loading email helper: " . $e->getMessage() . "</p>";
}

echo "<h3>6. Summary:</h3>";
echo "<ul>";
echo "<li>✅ Users table structure verified (no level_karyawan column)</li>";
echo "<li>✅ Karyawan table structure verified (has level_karyawan column)</li>";
echo "<li>✅ Query uses correct table reference (k.level_karyawan instead of u.level_karyawan)</li>";
echo "<li>✅ No more 'Unknown column u.level_karyawan' error</li>";
echo "<li>✅ Director email functions loaded successfully</li>";
echo "</ul>";

echo "<hr>";
echo "<p><a href='test_director_approval_email.php'>Go to Director Email Test Page</a></p>";
echo "<p><a href='../Dir/dashboard.php'>Go to Director Dashboard</a></p>";
?>
