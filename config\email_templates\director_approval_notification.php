<!DOCTYPE html>
<html>
<head>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            line-height: 1.6; 
            margin: 0; 
            padding: 0; 
            background-color: #f4f4f4; 
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            padding: 20px; 
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header { 
            background: linear-gradient(135deg, #BF0000 0%, #8B0000 100%);
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 20px -20px;
        }
        .header h2 {
            margin: 0;
            font-size: 24px;
        }
        .header .subtitle {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .content { 
            padding: 0 20px 20px 20px; 
            color: #333;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
        }
        .approval-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin: 20px 0;
            display: inline-block;
            width: 100%;
            box-sizing: border-box;
        }
        .details { 
            background-color: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
            border-left: 4px solid #BF0000;
        }
        .details h3 {
            margin-top: 0;
            color: #BF0000;
            font-size: 18px;
        }
        .detail-row {
            display: flex;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .detail-label {
            font-weight: 600;
            min-width: 150px;
            color: #555;
        }
        .detail-value {
            flex: 1;
            color: #333;
        }
        .participants-section {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .participants-section h4 {
            margin-top: 0;
            color: #155724;
        }
        .participant-list {
            list-style: none;
            padding: 0;
            margin: 10px 0;
        }
        .participant-item {
            background: white;
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
        .approval-timeline {
            margin: 20px 0;
        }
        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 16px;
            color: white;
            background: #28a745;
        }
        .timeline-content {
            flex: 1;
        }
        .timeline-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .timeline-desc {
            color: #666;
            font-size: 14px;
        }
        .comments-section {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-left: 4px solid #2196f3;
        }
        .comments-section h4 {
            margin-top: 0;
            color: #1976d2;
        }
        .button { 
            display: inline-block; 
            background: linear-gradient(135deg, #BF0000 0%, #8B0000 100%);
            color: white; 
            padding: 12px 24px; 
            text-decoration: none; 
            border-radius: 8px; 
            margin-top: 20px; 
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(191, 0, 0, 0.3);
        }
        .footer { 
            text-align: center; 
            padding: 20px; 
            font-size: 12px; 
            color: #666; 
            border-top: 1px solid #eee;
            margin-top: 30px;
        }
        .footer p {
            margin: 5px 0;
        }
        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .success-message h4 {
            margin-top: 0;
            color: #155724;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            .detail-row {
                flex-direction: column;
            }
            .detail-label {
                min-width: auto;
                margin-bottom: 5px;
            }
            .timeline-item {
                flex-direction: column;
                text-align: center;
            }
            .timeline-icon {
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>🎉 Pengajuan Training Disetujui Direktur</h2>
            <p class="subtitle">Training Center - PT PAS</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                <p>Halo <strong><?php echo $data['recipient_name']; ?></strong>,</p>
            </div>

            <div class="approval-badge">
                ✅ TRAINING TELAH DISETUJUI DIREKTUR
            </div>

            <div class="success-message">
                <h4>🎉 Selamat!</h4>
                <p>Pengajuan training Anda telah mendapat persetujuan final dari Direktur dan siap untuk dilaksanakan.</p>
            </div>
            
            <div class="details">
                <h3>📋 Detail Pengajuan Training</h3>
                <div class="detail-row">
                    <div class="detail-label">ID Pengajuan:</div>
                    <div class="detail-value">#<?php echo $data['training_id']; ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Nama Training:</div>
                    <div class="detail-value"><?php echo $data['training_topic']; ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Tanggal Training:</div>
                    <div class="detail-value"><?php echo $data['training_date']; ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Pemohon:</div>
                    <div class="detail-value"><?php echo $data['requester_name']; ?> (<?php echo $data['requester_nik']; ?>)</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Departemen:</div>
                    <div class="detail-value"><?php echo $data['departemen']; ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Bagian:</div>
                    <div class="detail-value"><?php echo $data['bagian']; ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Jabatan:</div>
                    <div class="detail-value"><?php echo $data['jabatan']; ?></div>
                </div>
                <?php if (!empty($data['training_type'])): ?>
                <div class="detail-row">
                    <div class="detail-label">Jenis Training:</div>
                    <div class="detail-value"><?php echo $data['training_type']; ?></div>
                </div>
                <?php endif; ?>
                <?php if (!empty($data['provider_name'])): ?>
                <div class="detail-row">
                    <div class="detail-label">Provider:</div>
                    <div class="detail-value"><?php echo $data['provider_name']; ?></div>
                </div>
                <?php endif; ?>
                <?php if (!empty($data['training_place'])): ?>
                <div class="detail-row">
                    <div class="detail-label">Tempat:</div>
                    <div class="detail-value"><?php echo $data['training_place']; ?></div>
                </div>
                <?php endif; ?>
                <div class="detail-row">
                    <div class="detail-label">Status:</div>
                    <div class="detail-value">
                        <span style="background: #28a745; color: white; padding: 4px 12px; border-radius: 15px; font-size: 12px; font-weight: 600;">
                            ✅ DISETUJUI DIREKTUR
                        </span>
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Disetujui Pada:</div>
                    <div class="detail-value"><?php echo $data['approved_at']; ?></div>
                </div>
            </div>

            <?php if (!empty($data['participants'])): ?>
            <div class="participants-section">
                <h4>👥 Peserta Training</h4>
                <ul class="participant-list">
                    <?php foreach ($data['participants'] as $participant): ?>
                    <li class="participant-item">
                        <strong><?php echo $participant['nama']; ?></strong> (<?php echo $participant['nik']; ?>)<br>
                        <small><?php echo $participant['jabatan']; ?> - <?php echo $participant['departemen']; ?></small>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <div class="details">
                <h3>📈 Timeline Approval</h3>
                <div class="approval-timeline">
                    <?php if (!empty($data['timeline'])): ?>
                        <?php foreach ($data['timeline'] as $item): ?>
                        <div class="timeline-item">
                            <div class="timeline-icon">
                                <?php echo $item['icon']; ?>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-title"><?php echo $item['title']; ?></div>
                                <div class="timeline-desc"><?php echo $item['description']; ?></div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if (!empty($data['comments'])): ?>
            <div class="comments-section">
                <h4>💬 Komentar Direktur</h4>
                <p><?php echo nl2br(htmlspecialchars($data['comments'])); ?></p>
            </div>
            <?php endif; ?>

            <?php if (!empty($data['assignment'])): ?>
            <div class="comments-section">
                <h4>📋 Penugasan</h4>
                <p><?php echo nl2br(htmlspecialchars($data['assignment'])); ?></p>
            </div>
            <?php endif; ?>

            <div class="success-message">
                <h4>🚀 Langkah Selanjutnya</h4>
                <p>Training telah mendapat persetujuan final. Silakan koordinasi dengan L&D untuk pelaksanaan training.</p>
            </div>

            <div style="text-align: center; margin: 30px 0; color: white;">
                <a href="<?php echo $data['dashboard_url']; ?>" class="button">
                    Lihat Dashboard Training
                </a>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>Training Center PT PAS</strong></p>
            <p>Email ini dikirim secara otomatis oleh sistem</p>
            <p>Mohon jangan membalas email ini</p>
        </div>
    </div>
</body>
</html>
