<?php
// Test file untuk debug HTTP 500 error
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "=== FORM SUBMISSION TEST ===<br>";

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h3>POST Data Received:</h3>";
    echo "<pre>";
    var_dump($_POST);
    echo "</pre>";
    
    echo "<h3>FILES Data Received:</h3>";
    echo "<pre>";
    var_dump($_FILES);
    echo "</pre>";
    
    // Test database connection
    try {
        include '../config/config.php';
        echo "<h3>Database Connection: SUCCESS</h3>";
        
        // Test simple query
        $result = $conn->query("SELECT COUNT(*) as count FROM training_submissions");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "Training submissions count: " . $row['count'] . "<br>";
        }
        
        // Test specific record
        $id = 126; // Use the ID from your test
        $stmt = $conn->prepare("SELECT * FROM training_submissions WHERE id = ?");
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            echo "<h3>Record Found for ID $id:</h3>";
            $row = $result->fetch_assoc();
            echo "<pre>";
            print_r($row);
            echo "</pre>";
        } else {
            echo "<h3>No record found for ID $id</h3>";
        }
        
    } catch (Exception $e) {
        echo "<h3>Database Error:</h3>";
        echo $e->getMessage();
    }
    
    echo "<h3>Test completed successfully!</h3>";
    
} else {
    // Show form
    ?>
    <form method="POST" enctype="multipart/form-data">
        <h3>Test Form Submission</h3>
        
        <label>Status:</label>
        <select name="status">
            <option value="Approved">Approved</option>
            <option value="Rejected">Rejected</option>
        </select><br><br>
        
        <label>Comments:</label>
        <textarea name="comments">Test comment</textarea><br><br>
        
        <label>Training Type:</label>
        <input type="text" name="training_type" value="Test Training"><br><br>
        
        <label>Start Date:</label>
        <input type="date" name="start_date" value="2025-06-01"><br><br>
        
        <label>Training Cost:</label>
        <input type="text" name="training_cost" value="1000000"><br><br>
        
        <label>File Upload:</label>
        <input type="file" name="internal_memo_image"><br><br>
        
        <button type="submit">Test Submit</button>
    </form>
    <?php
}
?>
