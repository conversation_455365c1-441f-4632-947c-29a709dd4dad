<?php
// Konfigurasi email untuk sistem verifikasi
require_once __DIR__ . '/config.php';

// Ambil konfigurasi email dari database
$email_config_query = "SELECT smtp_server, smtp_port, smtp_password, sender_email, sender_name, smtp_encryption FROM settings WHERE id = 1";
$email_config_result = $conn->query($email_config_query);

if ($email_config_result && $email_config_result->num_rows > 0) {
    $email_config = $email_config_result->fetch_assoc();

    define('SMTP_HOST', $email_config['smtp_server']);
    define('SMTP_PORT', $email_config['smtp_port']);
    define('SMTP_USERNAME', $email_config['sender_email']);
    define('SMTP_PASSWORD', $email_config['smtp_password']);
    define('SMTP_FROM_EMAIL', $email_config['sender_email']);
    define('SMTP_FROM_NAME', $email_config['sender_name']);
} else {
    // Default values if database settings are not available
    define('SMTP_HOST', 'smtp.gmail.com');
    define('SMTP_PORT', 587);
    define('SMTP_USERNAME', '<EMAIL>');
    define('SMTP_PASSWORD', 'xoexfurpjbbknila');
    define('SMTP_FROM_EMAIL', '<EMAIL>');
    define('SMTP_FROM_NAME', 'TRAINING PT PAS');

    // Log error
    error_log("Failed to load email configuration from database. Using default values.");
}