<?php
session_start();
require_once '../config/config.php';
require_once '../config/auto_update_helper.php';

// Validate admin access
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Handle manual trigger
if (isset($_POST['manual_trigger'])) {
    $results = forceAutoUpdateTrainingStatus($conn);
    $_SESSION['manual_trigger_result'] = $results;
    header('Location: auto_update_monitor.php');
    exit();
}

// Get statistics
$stats = getAutoUpdateStats($conn, 30);

// Get current status
$today = date('Y-m-d');
$is_run_today = isAutoUpdateRunToday($conn);

// Get pending training counts
$pending_query = "SELECT 
    COUNT(CASE WHEN status = 'Approved' AND (start_date < ? OR (end_date IS NOT NULL AND end_date < ?)) THEN 1 END) as pending_completed,
    COUNT(CASE WHEN status = 'Pending' AND start_date < DATE_SUB(?, INTERVAL 1 DAY) THEN 1 END) as pending_canceled
    FROM training_submissions";

$stmt = $conn->prepare($pending_query);
$stmt->bind_param("sss", $today, $today, $today);
$stmt->execute();
$pending_counts = $stmt->get_result()->fetch_assoc();
$stmt->close();
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>

<style>
.dashboard-container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 15px;
}

.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    background-color: white;
}

.card-header {
    background-color: #BF0000;
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
}

.card-body {
    padding: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #BF0000;
}

.stat-card h3 {
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
}

.stat-card .number {
    font-size: 28px;
    font-weight: 700;
    color: #BF0000;
    margin-bottom: 5px;
}

.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.status-success { background: #e8f5e9; color: #2e7d32; }
.status-warning { background: #fff3e0; color: #f57c00; }
.status-danger { background: #ffebee; color: #c62828; }

.btn-primary {
    background-color: #BF0000;
    border-color: #BF0000;
}

.btn-primary:hover {
    background-color: #a00000;
    border-color: #a00000;
}

.log-entry {
    padding: 10px;
    border-left: 3px solid #BF0000;
    margin-bottom: 10px;
    background-color: #f8f9fa;
}

.log-date {
    font-weight: bold;
    color: #BF0000;
}

.log-details {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}
</style>

<body>
<?php include '../config/navbar.php'; ?>
<div class="jarak"></div>

<div class="container-fluid dashboard-container">
    <div class="row">
        <div class="col-12">
            <h1><i class="fas fa-robot"></i> Auto-Update Training Status Monitor</h1>
            <p class="text-muted">Monitor dan kelola sistem auto-update status training</p>
        </div>
    </div>

    <!-- Manual Trigger Result -->
    <?php if (isset($_SESSION['manual_trigger_result'])): ?>
        <?php $result = $_SESSION['manual_trigger_result']; unset($_SESSION['manual_trigger_result']); ?>
        <div class="alert <?= $result['success'] ? 'alert-success' : 'alert-danger' ?>" role="alert">
            <h5><i class="fas <?= $result['success'] ? 'fa-check-circle' : 'fa-exclamation-triangle' ?>"></i> Manual Trigger Result</h5>
            <p><?= htmlspecialchars($result['message']) ?></p>
            <?php if ($result['success'] && $result['total_updated'] > 0): ?>
                <ul>
                    <li>Approved → Completed: <?= $result['approved_to_completed'] ?></li>
                    <li>Pending → Canceled: <?= $result['pending_to_canceled'] ?></li>
                    <li>Total Updated: <?= $result['total_updated'] ?></li>
                </ul>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Current Status -->
    <div class="stats-grid">
        <div class="stat-card">
            <h3>Status Hari Ini</h3>
            <div class="number">
                <span class="status-badge <?= $is_run_today ? 'status-success' : 'status-warning' ?>">
                    <?= $is_run_today ? 'Sudah Dijalankan' : 'Belum Dijalankan' ?>
                </span>
            </div>
            <small><?= date('Y-m-d') ?></small>
        </div>

        <div class="stat-card">
            <h3>Pending Completed</h3>
            <div class="number"><?= $pending_counts['pending_completed'] ?></div>
            <small>Training Approved yang sudah lewat tanggal</small>
        </div>

        <div class="stat-card">
            <h3>Pending Canceled</h3>
            <div class="number"><?= $pending_counts['pending_canceled'] ?></div>
            <small>Training Pending yang expired</small>
        </div>

        <div class="stat-card">
            <h3>Total Runs (30 hari)</h3>
            <div class="number"><?= $stats['total_runs'] ?></div>
            <small>Last run: <?= $stats['last_run'] ?? 'Never' ?></small>
        </div>
    </div>

    <!-- Manual Controls -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-cogs"></i> Manual Controls</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Force Manual Update</h6>
                    <p>Jalankan auto-update secara manual (akan mengabaikan flag "sudah dijalankan hari ini")</p>
                    <form method="POST" onsubmit="return confirm('Yakin ingin menjalankan auto-update manual?')">
                        <button type="submit" name="manual_trigger" class="btn btn-primary">
                            <i class="fas fa-play"></i> Jalankan Sekarang
                        </button>
                    </form>
                </div>
                <div class="col-md-6">
                    <h6>Cron Job Setup</h6>
                    <p>Tambahkan baris berikut ke crontab server:</p>
                    <code>1 0 * * * /usr/bin/php <?= dirname(__DIR__) ?>/cron/auto_update_training.php</code>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-chart-bar"></i> Statistik 30 Hari Terakhir</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <h6>Total Completed</h6>
                    <div class="display-4 text-success"><?= $stats['total_completed'] ?></div>
                    <small>Training yang diubah dari Approved ke Completed</small>
                </div>
                <div class="col-md-4">
                    <h6>Total Canceled</h6>
                    <div class="display-4 text-warning"><?= $stats['total_canceled'] ?></div>
                    <small>Training yang diubah dari Pending ke Canceled</small>
                </div>
                <div class="col-md-4">
                    <h6>Success Rate</h6>
                    <div class="display-4 text-primary">
                        <?= $stats['total_runs'] > 0 ? '100%' : 'N/A' ?>
                    </div>
                    <small>Tingkat keberhasilan auto-update</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-history"></i> Aktivitas Terbaru</h5>
        </div>
        <div class="card-body">
            <?php if (empty($stats['recent_activity'])): ?>
                <p class="text-muted">Belum ada aktivitas auto-update yang tercatat.</p>
            <?php else: ?>
                <?php foreach (array_slice($stats['recent_activity'], 0, 10) as $activity): ?>
                    <div class="log-entry">
                        <div class="log-date"><?= date('d M Y', strtotime($activity['update_date'])) ?></div>
                        <div class="log-details">
                            Completed: <?= $activity['approved_to_completed'] ?> | 
                            Canceled: <?= $activity['pending_to_canceled'] ?> | 
                            Total: <?= $activity['total_updated'] ?>
                            <?php if ($activity['details']): ?>
                                <br><small><?= htmlspecialchars($activity['details']) ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- System Info -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-info-circle"></i> Informasi Sistem</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>File Locations</h6>
                    <ul>
                        <li><strong>Helper:</strong> config/auto_update_helper.php</li>
                        <li><strong>Cron Job:</strong> cron/auto_update_training.php</li>
                        <li><strong>Hook:</strong> config/config.php (line 127-158)</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>How It Works</h6>
                    <ol>
                        <li>Cron job berjalan setiap hari jam 00:01</li>
                        <li>Backup hook di config.php untuk user access</li>
                        <li>Update Approved → Completed jika tanggal lewat</li>
                        <li>Update Pending → Canceled jika expired</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
