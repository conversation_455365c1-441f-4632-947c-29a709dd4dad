<?php
// File: admin/training_registration_admin_api.php
// API untuk admin mengelola pendaftaran training

include '../config/config.php';
include 'security.php';

header('Content-Type: application/json');

$response = ['success' => false, 'message' => '', 'data' => null];

// Check if user is admin
if ($_SESSION['role_id'] != 1) {
    $response['message'] = 'Access denied';
    echo json_encode($response);
    exit();
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'process_registration':
                $registration_id = $input['registration_id'] ?? 0;
                $status = $input['status'] ?? '';
                $admin_notes = $input['admin_notes'] ?? '';
                
                if (!in_array($status, ['approved', 'rejected'])) {
                    $response['message'] = 'Invalid status';
                    break;
                }
                
                // Update registration status
                $update_query = "UPDATE training_registrations 
                                SET status = ?, admin_notes = ?, approved_by = ?, approved_at = NOW() 
                                WHERE id = ?";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bind_param("ssii", $status, $admin_notes, $_SESSION['user_id'], $registration_id);
                
                if ($update_stmt->execute()) {
                    // If approved, add to actual training participants
                    if ($status === 'approved') {
                        addToTrainingParticipants($conn, $registration_id);
                    }
                    
                    // Mark notification as read
                    $notif_query = "UPDATE training_registration_notifications 
                                   SET is_read = 1 
                                   WHERE registration_id = ? AND admin_id = ?";
                    $notif_stmt = $conn->prepare($notif_query);
                    $notif_stmt->bind_param("ii", $registration_id, $_SESSION['user_id']);
                    $notif_stmt->execute();
                    
                    $response['success'] = true;
                    $response['message'] = $status === 'approved' ? 
                        'Pendaftaran berhasil disetujui dan peserta ditambahkan ke training' : 
                        'Pendaftaran berhasil ditolak';
                } else {
                    $response['message'] = 'Gagal memproses pendaftaran';
                }
                break;
                
            default:
                $response['message'] = 'Invalid action';
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'get_registrations':
                $status = $_GET['status'] ?? 'pending';
                
                $query = "SELECT tr.*, 
                                 CASE 
                                     WHEN tr.training_type = 'internal' THEN ot.judul_training
                                     ELSE ts.training_name
                                 END as training_title,
                                 u.name as approved_by_name
                          FROM training_registrations tr
                          LEFT JOIN offline_training ot ON tr.training_id = ot.id AND tr.training_type = 'internal'
                          LEFT JOIN training_submissions ts ON tr.training_id = ts.id AND tr.training_type = 'external'
                          LEFT JOIN users u ON tr.approved_by = u.id
                          WHERE tr.status = ?
                          ORDER BY tr.created_at DESC";
                
                $stmt = $conn->prepare($query);
                $stmt->bind_param("s", $status);
                $stmt->execute();
                $result = $stmt->get_result();
                
                $registrations = [];
                while ($row = $result->fetch_assoc()) {
                    $registrations[] = $row;
                }
                
                // Get counts for all statuses
                $count_query = "SELECT status, COUNT(*) as count FROM training_registrations GROUP BY status";
                $count_result = $conn->query($count_query);
                $counts = ['pending' => 0, 'approved' => 0, 'rejected' => 0];
                
                while ($count_row = $count_result->fetch_assoc()) {
                    $counts[$count_row['status']] = $count_row['count'];
                }
                
                $response['success'] = true;
                $response['registrations'] = $registrations;
                $response['counts'] = $counts;
                break;
                
            case 'get_registration_detail':
                $registration_id = $_GET['id'] ?? 0;
                
                $query = "SELECT tr.*, 
                                 CASE 
                                     WHEN tr.training_type = 'internal' THEN ot.judul_training
                                     ELSE ts.training_name
                                 END as training_title
                          FROM training_registrations tr
                          LEFT JOIN offline_training ot ON tr.training_id = ot.id AND tr.training_type = 'internal'
                          LEFT JOIN training_submissions ts ON tr.training_id = ts.id AND tr.training_type = 'external'
                          WHERE tr.id = ?";
                
                $stmt = $conn->prepare($query);
                $stmt->bind_param("i", $registration_id);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    $response['success'] = true;
                    $response['registration'] = $result->fetch_assoc();
                } else {
                    $response['message'] = 'Registration not found';
                }
                break;
                
            case 'get_notification_count':
                $count_query = "SELECT COUNT(*) as count 
                               FROM training_registration_notifications trn
                               JOIN training_registrations tr ON trn.registration_id = tr.id
                               WHERE trn.admin_id = ? AND trn.is_read = 0 AND tr.status = 'pending'";
                $count_stmt = $conn->prepare($count_query);
                $count_stmt->bind_param("i", $_SESSION['user_id']);
                $count_stmt->execute();
                $count_result = $count_stmt->get_result();
                $count_data = $count_result->fetch_assoc();
                
                $response['success'] = true;
                $response['count'] = $count_data['count'];
                break;
                
            default:
                $response['message'] = 'Invalid action';
        }
    }
    
} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
}

function addToTrainingParticipants($conn, $registration_id) {
    // Get registration details
    $reg_query = "SELECT * FROM training_registrations WHERE id = ?";
    $reg_stmt = $conn->prepare($reg_query);
    $reg_stmt->bind_param("i", $registration_id);
    $reg_stmt->execute();
    $reg_result = $reg_stmt->get_result();
    
    if ($reg_result->num_rows > 0) {
        $reg_data = $reg_result->fetch_assoc();
        
        if ($reg_data['training_type'] === 'internal') {
            // Add to offline_training_participants
            $check_query = "SELECT id FROM offline_training_participants 
                           WHERE offline_training_id = ? AND karyawan_id = ?";
            $check_stmt = $conn->prepare($check_query);
            $check_stmt->bind_param("ii", $reg_data['training_id'], $reg_data['participant_id']);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows == 0) {
                $insert_query = "INSERT INTO offline_training_participants 
                                (offline_training_id, karyawan_id, nik, nama, status, registered_by, registered_at) 
                                VALUES (?, ?, ?, ?, 'registered', ?, NOW())";
                $insert_stmt = $conn->prepare($insert_query);
                $insert_stmt->bind_param("iissi", 
                    $reg_data['training_id'], 
                    $reg_data['participant_id'], 
                    $reg_data['participant_nik'], 
                    $reg_data['participant_nama'], 
                    $reg_data['registered_by']
                );
                $insert_stmt->execute();
            }
        } else {
            // For external training, you might want to add to a different table
            // or handle differently based on your system design
        }
    }
}

echo json_encode($response);
?>
