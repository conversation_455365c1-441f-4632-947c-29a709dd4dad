<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #BF0000; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        .details { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .button { display: inline-block; background-color: #BF0000; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2><?php echo $data['email_title']; ?></h2>
        </div>
        <div class="content">
            <p>Yth. <strong><?php echo $data['recipient_name']; ?></strong>,</p>
            
            <p><?php echo $data['intro_message']; ?></p>
            
            <div class="details">
                <h3>Detail Pengajuan Training:</h3>
                <p><strong>ID Pengajuan:</strong> <?php echo $data['training_id']; ?></p>
                <p><strong>Topik Training:</strong> <?php echo $data['training_topic']; ?></p>
                <p><strong>Tanggal Training:</strong> <?php echo $data['training_date']; ?></p>
                <p><strong>Pemohon:</strong> <?php echo $data['requester_name']; ?> (<?php echo $data['requester_nik']; ?>)</p>
                <p><strong>Departemen:</strong> <?php echo $data['departemen']; ?></p>
                <p><strong>Status:</strong> <strong style="color: <?php echo $data['status_color']; ?>;"><?php echo $data['status']; ?></strong></p>
                
                <?php if (!empty($data['comments'])): ?>
                <p><strong>Komentar:</strong> <?php echo $data['comments']; ?></p>
                <?php endif; ?>
                
                <?php if (!empty($data['participants'])): ?>
                <h4>Peserta Training:</h4>
                <ul>
                    <?php foreach ($data['participants'] as $participant): ?>
                    <li><?php echo $participant['nama']; ?> (<?php echo $participant['nik']; ?>) - <?php echo $participant['jabatan']; ?>, <?php echo $participant['departemen']; ?></li>
                    <?php endforeach; ?>
                </ul>
                <?php endif; ?>
            </div>
            
            <?php if ($data['action_required']): ?>
            <p><strong>Tindakan yang diperlukan:</strong> <?php echo $data['action_message']; ?></p>
            <a href="<?php echo $data['action_url']; ?>" class="button">Lihat Detail Pengajuan</a>
            <?php endif; ?>
        </div>
        <div class="footer">
            <p>Email ini dikirim secara otomatis oleh sistem Training Center PAS</p>
            <p>Mohon jangan membalas email ini</p>
        </div>
    </div>
</body>
</html>
