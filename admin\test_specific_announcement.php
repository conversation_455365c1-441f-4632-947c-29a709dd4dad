<?php
/**
 * Test Specific Announcement Creation
 * Quick test to create a specific announcement manually
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header("Location: ../login.php");
    exit();
}

$message = '';

if (isset($_POST['create_test'])) {
    $user_id = $_SESSION['user_id'];
    $selected_user_id = $_POST['selected_user_id'];
    
    try {
        // Start transaction
        $conn->begin_transaction();
        
        // Create test announcement
        $insert_query = "INSERT INTO announcements (title, content, created_by, active, target_role, expiry_date)
                        VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insert_query);
        
        $title = "Test Pengumuman Perorangan - " . date('Y-m-d H:i:s');
        $content = "Ini adalah test pengumuman yang dikirim khusus untuk user tertentu.";
        $active = 1;
        $target_role = NULL;
        $expiry_date = NULL;
        
        $stmt->bind_param("ssiiss", $title, $content, $user_id, $active, $target_role, $expiry_date);
        
        if ($stmt->execute()) {
            $announcement_id = $conn->insert_id;
            $stmt->close();
            
            // Add specific recipient
            $insert_recipient_query = "INSERT INTO announcement_recipients (announcement_id, user_id) VALUES (?, ?)";
            $stmt_recipient = $conn->prepare($insert_recipient_query);
            $stmt_recipient->bind_param("ii", $announcement_id, $selected_user_id);
            
            if ($stmt_recipient->execute()) {
                $stmt_recipient->close();
                
                // Commit transaction
                $conn->commit();
                
                $message = "<div class='alert alert-success'>
                    <strong>Success!</strong> Test announcement created successfully!<br>
                    <strong>Announcement ID:</strong> $announcement_id<br>
                    <strong>Recipient User ID:</strong> $selected_user_id
                </div>";
            } else {
                throw new Exception("Failed to insert recipient: " . $conn->error);
            }
        } else {
            throw new Exception("Failed to insert announcement: " . $conn->error);
        }
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        $message = "<div class='alert alert-danger'>
            <strong>Error:</strong> " . $e->getMessage() . "
        </div>";
    }
}

// Get all active users for selection
$users_query = "SELECT id, name, email, role_id FROM users WHERE is_active = 1 ORDER BY name";
$users_result = $conn->query($users_query);
$users = [];
while ($user = $users_result->fetch_assoc()) {
    $users[] = $user;
}

// Get recent announcements with recipients
$recent_query = "SELECT a.id, a.title, a.created_at, 
                        GROUP_CONCAT(u.name SEPARATOR ', ') as recipients,
                        COUNT(ar.user_id) as recipient_count
                 FROM announcements a
                 LEFT JOIN announcement_recipients ar ON a.id = ar.announcement_id
                 LEFT JOIN users u ON ar.user_id = u.id
                 WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                 GROUP BY a.id
                 ORDER BY a.created_at DESC
                 LIMIT 10";
$recent_result = $conn->query($recent_query);
$recent_announcements = [];
while ($row = $recent_result->fetch_assoc()) {
    $recent_announcements[] = $row;
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Specific Announcement - Training Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-vial"></i> Test Specific Announcement Creation</h2>
                <p class="text-muted">Test manual creation of specific announcements.</p>
                
                <?= $message ?>
                
                <!-- Test Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-plus"></i> Create Test Announcement</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">Select User to Receive Announcement:</label>
                                <select name="selected_user_id" class="form-select" required>
                                    <option value="">-- Select User --</option>
                                    <?php foreach ($users as $user): ?>
                                        <option value="<?= $user['id'] ?>">
                                            <?= htmlspecialchars($user['name']) ?> (<?= htmlspecialchars($user['email']) ?>) - Role <?= $user['role_id'] ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <button type="submit" name="create_test" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Test Announcement
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Recent Announcements -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Recent Announcements (Last 24 Hours)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_announcements)): ?>
                            <div class="alert alert-info">No recent announcements found.</div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Title</th>
                                            <th>Recipients</th>
                                            <th>Count</th>
                                            <th>Created</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_announcements as $announcement): ?>
                                            <tr>
                                                <td><?= $announcement['id'] ?></td>
                                                <td><?= htmlspecialchars($announcement['title']) ?></td>
                                                <td>
                                                    <?php if ($announcement['recipients']): ?>
                                                        <small><?= htmlspecialchars($announcement['recipients']) ?></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">No recipients</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?= $announcement['recipient_count'] > 0 ? 'success' : 'warning' ?>">
                                                        <?= $announcement['recipient_count'] ?>
                                                    </span>
                                                </td>
                                                <td><?= date('d M Y H:i', strtotime($announcement['created_at'])) ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Database Check -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-database"></i> Database Check</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // Check announcement_recipients table structure
                        $structure_query = "DESCRIBE announcement_recipients";
                        $structure_result = $conn->query($structure_query);
                        
                        echo '<h6>announcement_recipients table structure:</h6>';
                        echo '<div class="table-responsive">';
                        echo '<table class="table table-sm table-bordered">';
                        echo '<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>';
                        echo '<tbody>';
                        while ($column = $structure_result->fetch_assoc()) {
                            echo '<tr>';
                            echo '<td>' . $column['Field'] . '</td>';
                            echo '<td>' . $column['Type'] . '</td>';
                            echo '<td>' . $column['Null'] . '</td>';
                            echo '<td>' . $column['Key'] . '</td>';
                            echo '<td>' . ($column['Default'] ?: 'NULL') . '</td>';
                            echo '</tr>';
                        }
                        echo '</tbody></table>';
                        echo '</div>';
                        
                        // Check recent recipients
                        $recipients_query = "SELECT ar.*, a.title, u.name as user_name 
                                           FROM announcement_recipients ar
                                           JOIN announcements a ON ar.announcement_id = a.id
                                           JOIN users u ON ar.user_id = u.id
                                           WHERE ar.assigned_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                                           ORDER BY ar.assigned_at DESC
                                           LIMIT 10";
                        $recipients_result = $conn->query($recipients_query);
                        
                        echo '<h6 class="mt-3">Recent recipients (Last 24 Hours):</h6>';
                        if ($recipients_result->num_rows > 0) {
                            echo '<div class="table-responsive">';
                            echo '<table class="table table-sm table-bordered">';
                            echo '<thead><tr><th>Ann ID</th><th>Title</th><th>User</th><th>Assigned</th></tr></thead>';
                            echo '<tbody>';
                            while ($recipient = $recipients_result->fetch_assoc()) {
                                echo '<tr>';
                                echo '<td>' . $recipient['announcement_id'] . '</td>';
                                echo '<td>' . htmlspecialchars($recipient['title']) . '</td>';
                                echo '<td>' . htmlspecialchars($recipient['user_name']) . '</td>';
                                echo '<td>' . date('d M Y H:i', strtotime($recipient['assigned_at'])) . '</td>';
                                echo '</tr>';
                            }
                            echo '</tbody></table>';
                            echo '</div>';
                        } else {
                            echo '<div class="alert alert-warning">No recent recipients found.</div>';
                        }
                        ?>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="manage_announcements.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Manage Announcements
                    </a>
                    <a href="debug_announcements.php" class="btn btn-info">
                        <i class="fas fa-bug"></i> Debug Announcements
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
