<?php
/**
 * Script untuk memperbaiki foreign key constraints saat menghapus user
 */

echo "🔧 FIXING FOREIGN KEY CONSTRAINTS\n";
echo "=================================\n\n";

require_once __DIR__ . '/../config/config.php';

// Function untuk mendapatkan semua foreign key constraints yang mereferensi tabel users
function getForeignKeyConstraints($conn) {
    $query = "
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            CONSTRAINT_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM 
            INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE 
            REFERENCED_TABLE_SCHEMA = DATABASE()
            AND REFERENCED_TABLE_NAME = 'users'
            AND REFERENCED_COLUMN_NAME = 'id'
    ";
    
    $result = $conn->query($query);
    $constraints = [];
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $constraints[] = $row;
        }
    }
    
    return $constraints;
}

// Function untuk menghapus user dengan aman
function safeDeleteInactiveUsers($conn) {
    try {
        // Mulai transaction
        $conn->begin_transaction();
        
        echo "1️⃣ Getting inactive users...\n";
        
        // Get list of inactive user IDs
        $getUsersQuery = "SELECT id, name, nik, email FROM users WHERE is_active = 0";
        $usersResult = $conn->query($getUsersQuery);
        $inactiveUsers = [];
        
        while ($row = $usersResult->fetch_assoc()) {
            $inactiveUsers[] = $row;
        }
        
        if (empty($inactiveUsers)) {
            echo "   ℹ️  No inactive users found\n";
            return 0;
        }
        
        $inactiveUserIds = array_column($inactiveUsers, 'id');
        $userIdsList = implode(',', $inactiveUserIds);
        
        echo "   📊 Found " . count($inactiveUsers) . " inactive users\n";
        foreach ($inactiveUsers as $user) {
            echo "      - {$user['name']} (NIK: {$user['nik']}, Email: {$user['email']})\n";
        }
        echo "\n";
        
        echo "2️⃣ Checking foreign key constraints...\n";
        $constraints = getForeignKeyConstraints($conn);
        
        foreach ($constraints as $constraint) {
            echo "   🔗 {$constraint['TABLE_NAME']}.{$constraint['COLUMN_NAME']} → users.id\n";
        }
        echo "\n";
        
        echo "3️⃣ Cleaning up foreign key references...\n";
        
        // 1. Training submissions - set approver fields to NULL
        echo "   📝 Cleaning training_submissions...\n";
        $queries = [
            "UPDATE training_submissions SET approved_by = NULL WHERE approved_by IN ($userIdsList)",
            "UPDATE training_submissions SET rejected_by = NULL WHERE rejected_by IN ($userIdsList)",
            "UPDATE training_submissions SET next_approver_id = NULL WHERE next_approver_id IN ($userIdsList)"
        ];
        
        foreach ($queries as $query) {
            $result = $conn->query($query);
            if ($result) {
                echo "      ✅ Updated " . $conn->affected_rows . " records\n";
            } else {
                echo "      ⚠️  Query failed: " . $conn->error . "\n";
            }
        }
        
        // 2. User departments - delete records
        echo "   🏢 Cleaning user_departments...\n";
        $deleteUserDepts = "DELETE FROM user_departments WHERE user_id IN ($userIdsList)";
        $result = $conn->query($deleteUserDepts);
        if ($result) {
            echo "      ✅ Deleted " . $conn->affected_rows . " department assignments\n";
        } else {
            throw new Exception("Failed to delete user departments: " . $conn->error);
        }
        
        // 3. Training notifications - delete records
        echo "   🔔 Cleaning training_notifications...\n";
        $deleteNotifications = "DELETE FROM training_notifications WHERE user_id IN ($userIdsList)";
        $result = $conn->query($deleteNotifications);
        if ($result) {
            echo "      ✅ Deleted " . $conn->affected_rows . " notifications\n";
        } else {
            echo "      ⚠️  Notifications table might not exist\n";
        }
        
        // 4. Activity logs - delete records
        echo "   📋 Cleaning activity_logs...\n";
        $deleteActivityLogs = "DELETE FROM activity_logs WHERE user_id IN ($userIdsList)";
        $result = $conn->query($deleteActivityLogs);
        if ($result) {
            echo "      ✅ Deleted " . $conn->affected_rows . " activity logs\n";
        } else {
            echo "      ⚠️  Activity logs table might not exist\n";
        }
        
        // 5. Offline training - set approver fields to NULL (check columns first)
        echo "   🏫 Cleaning offline_training...\n";

        // Get offline_training table structure
        $columnsResult = $conn->query("SHOW COLUMNS FROM offline_training");
        $availableColumns = [];
        while ($col = $columnsResult->fetch_assoc()) {
            $availableColumns[] = $col['Field'];
        }

        $offlineQueries = [];
        $possibleColumns = [
            'approved_by_dept_head',
            'approved_by_lnd',
            'approved_dept_head',
            'approved_lnd',
            'approved_hrga',
            'approved_fm',
            'approved_dir',
            'created_by'
        ];

        foreach ($possibleColumns as $column) {
            if (in_array($column, $availableColumns)) {
                $offlineQueries[] = "UPDATE offline_training SET $column = NULL WHERE $column IN ($userIdsList)";
            }
        }

        foreach ($offlineQueries as $query) {
            $result = $conn->query($query);
            if ($result) {
                echo "      ✅ Updated " . $conn->affected_rows . " offline training records\n";
            } else {
                echo "      ⚠️  Query failed: " . $conn->error . "\n";
            }
        }
        
        echo "\n4️⃣ Deleting inactive users...\n";
        
        // Finally delete users
        $deleteQuery = "DELETE FROM users WHERE is_active = 0";
        $result = $conn->query($deleteQuery);
        
        if (!$result) {
            throw new Exception("Failed to delete users: " . $conn->error);
        }
        
        $deleted_count = $conn->affected_rows;
        echo "   ✅ Successfully deleted $deleted_count users\n";
        
        // Commit transaction
        $conn->commit();
        echo "\n✅ Transaction committed successfully!\n";
        
        return $deleted_count;
        
    } catch (Exception $e) {
        // Rollback transaction
        $conn->rollback();
        echo "\n❌ Error occurred: " . $e->getMessage() . "\n";
        echo "🔄 Transaction rolled back\n";
        throw $e;
    }
}

// Main execution
echo "🚀 Starting safe deletion of inactive users...\n\n";

try {
    $deleted_count = safeDeleteInactiveUsers($conn);
    
    echo "\n📊 SUMMARY:\n";
    echo "===========\n";
    echo "✅ Foreign key constraints handled properly\n";
    echo "✅ Related data cleaned up\n";
    echo "✅ $deleted_count inactive users deleted\n";
    echo "✅ Database integrity maintained\n";
    
} catch (Exception $e) {
    echo "\n❌ FAILED:\n";
    echo "==========\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "Please check the error and try again.\n";
}

echo "\n🔧 FOREIGN KEY CONSTRAINT FIX COMPLETE!\n";
?>
