<?php
session_start();
include '../config/config.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Ambil filter dari query string jika ada
$category = $_GET['category'] ?? 'all';
$limit = intval($_GET['limit'] ?? 50);

// Validasi limit
if ($limit <= 0 || $limit > 200) {
    $limit = 50;
}

// Query untuk mengambil log aktivitas
$query = "SELECT al.id, al.action, al.category, al.timestamp, u.username as user 
          FROM activity_logs al 
          JOIN users u ON al.user_id = u.id";

// Tambahkan filter kategori jika bukan 'all'
if ($category !== 'all') {
    $query .= " WHERE al.category = ?";
}

// Tambahkan order dan limit
$query .= " ORDER BY al.timestamp DESC LIMIT ?";

// Prepare statement
$stmt = $conn->prepare($query);

// Bind parameter
if ($category !== 'all') {
    $stmt->bind_param("si", $category, $limit);
} else {
    $stmt->bind_param("i", $limit);
}

// Execute query
$stmt->execute();
$result = $stmt->get_result();

// Ambil semua log
$logs = [];
while ($row = $result->fetch_assoc()) {
    $logs[] = [
        'id' => $row['id'],
        'action' => $row['action'],
        'category' => $row['category'],
        'timestamp' => $row['timestamp'],
        'user' => $row['user']
    ];
}

// Kembalikan response
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'logs' => $logs,
    'count' => count($logs)
]);
