<?php
session_start();
require_once '../includes/auth_check.php'; // Ensure user is logged in and is an admin
require_once '../config/config.php'; // Database connection and configuration

// Konversi koneksi mysqli ke PDO
$pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo "<p class='text-danger'>Akses ditolak. Anda tidak memiliki izin untuk melihat detail ini.</p>";
    exit;
}

if (!isset($_POST['history_id'])) {
    echo "<p class='text-danger'>ID Riwayat tidak disediakan.</p>";
    exit;
}

$historyId = intval($_POST['history_id']);

try {
    $stmt = $pdo->prepare("SELECT nik, action_type as action, old_data, new_data, change_timestamp, changed_by FROM karyawan_history WHERE history_id = ?");
    $stmt->execute([$historyId]);
    $history = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$history) {
        echo "<p class='text-danger'>Data riwayat tidak ditemukan.</p>";
        exit;
    }

    $oldData = !empty($history['old_data']) ? json_decode($history['old_data'], true) : null;
    $newData = !empty($history['new_data']) ? json_decode($history['new_data'], true) : null;

    // Fungsi untuk menerjemahkan nama kolom ke bahasa Indonesia
    function translateFieldName($fieldName) {
        $translations = [
            'id' => 'ID',
            'nik' => 'NIK',
            'normalized_nik' => 'NIK Normalisasi',
            'nama' => 'Nama',
            'tgl_masuk' => 'Tanggal Masuk',
            'jk' => 'Jenis Kelamin',
            'level_karyawan' => 'Level Karyawan',
            'tgl_lahir' => 'Tanggal Lahir',
            'agama' => 'Agama',
            'pendidikan_akhir' => 'Pendidikan Akhir',
            'no_telp' => 'No. Telepon',
            'dept' => 'Departemen',
            'bagian' => 'Bagian',
            'jabatan' => 'Jabatan',
            'group' => 'Grup',
            'status' => 'Status',
            'pt' => 'PT'
        ];

        return $translations[$fieldName] ?? $fieldName;
    }

    // Fungsi untuk memformat nilai jenis kelamin
    function formatJenisKelamin($value) {
        if ($value === 'L') return 'Laki-laki';
        if ($value === 'P') return 'Perempuan';
        return $value;
    }

    // Fungsi untuk memformat nilai
    function formatValue($key, $value) {
        if ($key === 'jk') return formatJenisKelamin($value);
        if ($value === '' || $value === null) return '<span class="text-muted">Kosong</span>';
        return htmlspecialchars($value);
    }

    // Dapatkan nama user yang melakukan perubahan
    $userQuery = $pdo->prepare("SELECT name FROM users WHERE id = ?");
    $userQuery->execute([$history['changed_by']]);
    $userName = $userQuery->fetchColumn() ?: 'Unknown';

    // Tentukan jenis aksi dalam bahasa Indonesia
    $actionText = '';
    switch($history['action']) {
        case 'INSERT': $actionText = 'Penambahan'; break;
        case 'UPDATE': $actionText = 'Perubahan'; break;
        case 'DELETE': $actionText = 'Penghapusan'; break;
        default: $actionText = $history['action'];
    }

    // Start building the HTML output
    $output = "<div class='detail-header'>";
    $output .= "<p><strong>ID Riwayat:</strong> {$historyId}</p>";
    $output .= "<p><strong>NIK Karyawan:</strong> " . htmlspecialchars($history['nik']) . "</p>";
    $output .= "<p><strong>Jenis Aksi:</strong> <span class='action-" . strtolower($history['action']) . "'>{$actionText}</span></p>";
    $output .= "<p><strong>Waktu:</strong> " . date('d M Y H:i:s', strtotime($history['change_timestamp'])) . "</p>";
    $output .= "<p><strong>Dilakukan Oleh:</strong> " . htmlspecialchars($userName) . "</p>";
    $output .= "</div><hr>";

    $output .= "<h4>Detail Perubahan:</h4>";

    if ($history['action'] === 'INSERT') {
        $output .= "<div class='alert alert-success'><i class='fas fa-plus-circle'></i> Data karyawan baru telah ditambahkan</div>";
        $output .= "<h5>Data Baru:</h5>";
        if (is_array($newData) && !empty($newData)) {
            $output .= "<table class='table table-bordered table-sm table-hover'>";
            $output .= "<thead class='table-light'><tr><th width='30%'>Kolom</th><th>Nilai</th></tr></thead><tbody>";
            foreach ($newData as $key => $value) {
                if ($key === 'id') continue; // Skip ID field
                $output .= "<tr>";
                $output .= "<td><strong>" . translateFieldName($key) . "</strong></td>";
                $output .= "<td>" . formatValue($key, $value) . "</td>";
                $output .= "</tr>";
            }
            $output .= "</tbody></table>";
        } else {
            $output .= "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> Tidak ada detail data baru yang tersedia.</div>";
        }
    } elseif ($history['action'] === 'DELETE') {
        $output .= "<div class='alert alert-danger'><i class='fas fa-trash-alt'></i> Data karyawan telah dihapus</div>";
        $output .= "<h5>Data yang Dihapus:</h5>";
        if (is_array($oldData) && !empty($oldData)) {
            $output .= "<table class='table table-bordered table-sm table-hover'>";
            $output .= "<thead class='table-light'><tr><th width='30%'>Kolom</th><th>Nilai</th></tr></thead><tbody>";
            foreach ($oldData as $key => $value) {
                if ($key === 'id') continue; // Skip ID field
                $output .= "<tr>";
                $output .= "<td><strong>" . translateFieldName($key) . "</strong></td>";
                $output .= "<td>" . formatValue($key, $value) . "</td>";
                $output .= "</tr>";
            }
            $output .= "</tbody></table>";
        } else {
            $output .= "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> Tidak ada detail data lama yang tersedia.</div>";
        }
    } elseif ($history['action'] === 'UPDATE') {
        $output .= "<div class='alert alert-warning'><i class='fas fa-edit'></i> Data karyawan telah diubah</div>";
        $output .= "<h5>Perubahan:</h5>";

        if (is_array($oldData) && is_array($newData)) {
            $output .= "<table class='table table-bordered table-sm table-hover'>";
            $output .= "<thead class='table-light'><tr><th width='25%'>Kolom</th><th width='37.5%'>Nilai Lama</th><th width='37.5%'>Nilai Baru</th></tr></thead><tbody>";

            // Get all unique keys from both old and new data
            $allKeys = array_unique(array_merge(array_keys($oldData), array_keys($newData)));
            sort($allKeys); // Optional: sort keys alphabetically

            $hasChanges = false;
            foreach ($allKeys as $key) {
                if ($key === 'id') continue; // Skip ID field

                $oldValue = $oldData[$key] ?? null;
                $newValue = $newData[$key] ?? null;

                // Only show rows where there was a change
                if ($oldValue !== $newValue) {
                    $hasChanges = true;
                    $output .= "<tr>";
                    $output .= "<td><strong>" . translateFieldName($key) . "</strong></td>";
                    $output .= "<td class='bg-light-red'>" . formatValue($key, $oldValue) . "</td>";
                    $output .= "<td class='bg-light-green'>" . formatValue($key, $newValue) . "</td>";
                    $output .= "</tr>";
                }
            }
            $output .= "</tbody></table>";

            if (!$hasChanges) {
                $output .= "<div class='alert alert-info'><i class='fas fa-info-circle'></i> Tidak ada perubahan yang terdeteksi antara data lama dan baru (ini mungkin menunjukkan pembaruan dengan nilai yang identik).</div>";
            }
        } else {
            $output .= "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> Data perubahan tidak lengkap.</div>";
        }
    } else {
        $output .= "<div class='alert alert-secondary'><i class='fas fa-question-circle'></i> Jenis aksi tidak dikenal.</div>";
    }

    // Tambahkan CSS untuk mempercantik tampilan
    $output .= "<style>
        .detail-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .action-insert {
            color: #28a745;
            font-weight: bold;
        }
        .action-update {
            color: #ffc107;
            font-weight: bold;
        }
        .action-delete {
            color: #dc3545;
            font-weight: bold;
        }
        .bg-light-red {
            background-color: #ffeeee;
        }
        .bg-light-green {
            background-color: #eeffee;
        }
        .table-hover tbody tr:hover {
            background-color: #f5f5f5;
        }
    </style>";


    echo $output;

} catch (PDOException $e) {
    // Log error properly in a real application
    error_log("Database Error in get_history_details_ajax.php: " . $e->getMessage());
    echo "<p class='text-danger'>Terjadi kesalahan saat mengambil detail riwayat. Silakan coba lagi nanti.</p>";
} catch (Exception $e) {
    error_log("General Error in get_history_details_ajax.php: " . $e->getMessage());
    echo "<p class='text-danger'>Terjadi kesalahan yang tidak terduga.</p>";
}
?>
