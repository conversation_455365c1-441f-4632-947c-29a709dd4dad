<?php
session_start();
include '../config/config.php';

// Matikan output buffering untuk memungkinkan streaming respons
@ob_end_clean(); // @ untuk menghindari warning jika buffer belum dimulai
ob_start();
set_time_limit(600); // Tambah batas waktu eksekusi menjadi 10 menit
ini_set('max_execution_time', 600); // Pastikan batas waktu eksekusi juga diatur di level PHP
ini_set('memory_limit', '256M'); // Tambah batas memori
header('Content-Type: application/json');
header('Cache-Control: no-cache');
header('X-Accel-Buffering: no'); // Untuk NGINX

// Fungsi untuk mengirim respons error dan keluar
function send_error_response($message, $log_message = '') {
    // Log error untuk debugging
    if (!empty($log_message)) {
        error_log("Bulk user error: " . $log_message);
    } else {
        error_log("Bulk user error: " . $message);
    }
    
    // <PERSON><PERSON> respons error ke client
    header('Content-Type: application/json');
    echo json_encode(['status' => 'error', 'message' => $message]);
    exit();
}

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    send_error_response('Anda tidak memiliki akses', 'User not logged in');
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    send_error_response('Anda tidak memiliki akses', 'User not admin (role_id: ' . $_SESSION['role_id'] . ')');
}

// Inisialisasi variabel
$results = [];
$success_count = 0;
$error_count = 0;

// Proses form submit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Cek apakah ada data yang dikirim
    if (isset($_POST['selected_employees']) && !empty($_POST['selected_employees'])) {
        try {
            $selected_employees = json_decode($_POST['selected_employees'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                send_error_response('Format data karyawan tidak valid', 'JSON decode error: ' . json_last_error_msg());
            }
            
            // Validasi jumlah karyawan
            $total_employees = count($selected_employees);
            if ($total_employees <= 0) {
                send_error_response('Tidak ada karyawan yang dipilih', 'Empty employees array');
            }
            
            // Validasi role_id
            if (!isset($_POST['role_id']) || empty($_POST['role_id'])) {
                send_error_response('Harap pilih role untuk semua karyawan', 'Empty role_id');
            }
            
            $role_id = $_POST['role_id'];
            
            // Log informasi batch untuk debugging
            error_log("Processing batch with {$total_employees} employees, role_id: {$role_id}");
            
            // Mulai transaksi database
            $conn->begin_transaction();
            
            // Simpan hasil di session untuk ditampilkan setelah redirect
                $_SESSION['bulk_create_results'] = [];
            
            // Proses setiap karyawan satu per satu dan kirim update progress
            foreach ($selected_employees as $index => $employee) {
                $current = $index + 1;
                $name = $employee['nama'];
                $nik = $employee['nik'];
                $dept = $employee['dept'];
                $bagian = $employee['bagian'];
                $jabatan = $employee['jabatan'];
                
                // Kirim update progress ke client
                echo json_encode([
                    'status' => 'progress',
                    'current' => $current,
                    'total' => $total_employees,
                    'message' => "Memproses: {$name} ({$nik})"
                ]) . "\n\n";
                
                // Logging untuk debug proses bulk
                error_log("Bulk user progress: {$current}/{$total_employees} - {$name} ({$nik})");
                
                // Flush output buffer untuk mengirim data segera
                ob_flush();
                flush();
                
                // Generate email
                $cleanName = preg_replace('/[^a-zA-Z0-9]/', '', strtolower($name));
                $email = $cleanName . "@example.com";

                // Cek apakah email sudah ada
                $checkEmailQuery = "SELECT COUNT(*) as count FROM users WHERE email = ?";
                $stmtCheckEmail = $conn->prepare($checkEmailQuery);
                $stmtCheckEmail->bind_param("s", $email);
                $stmtCheckEmail->execute();
                $emailResult = $stmtCheckEmail->get_result()->fetch_assoc();

                if ($emailResult['count'] > 0) {
                    // Jika email sudah ada, tambahkan 4 digit terakhir NIK
                    $uniqueIdentifier = preg_replace('/[^0-9]/', '', $nik);
                    $uniqueIdentifier = substr($uniqueIdentifier, -4);
                    $email = $cleanName . $uniqueIdentifier . "@example.com";
                }

                // Cek apakah NIK sudah digunakan
                $checkNikQuery = "SELECT * FROM users WHERE nik = ?";
                $stmtCheckNik = $conn->prepare($checkNikQuery);
                $stmtCheckNik->bind_param("s", $nik);
                $stmtCheckNik->execute();
                $resultCheckNik = $stmtCheckNik->get_result();

                if ($resultCheckNik->num_rows > 0) {
                    // NIK sudah digunakan
                    $result_item = [
                        'status' => 'error',
                        'message' => "NIK sudah digunakan",
                        'data' => [
                            'name' => $name,
                            'nik' => $nik,
                            'email' => $email,
                            'dept' => $dept,
                            'bagian' => $bagian,
                            'jabatan' => $jabatan
                        ]
                    ];
                    
                    $_SESSION['bulk_create_results'][] = $result_item;
                    $error_count++;
                } else {
                    // Allow duplicate names - names can be the same as long as NIK and email are unique
                    $originalName = $name;
                    // Keep original name without modification
                    
                    // Cek apakah email sudah digunakan
                    $checkEmailQuery = "SELECT * FROM users WHERE email = ?";
                    $stmtCheckEmail = $conn->prepare($checkEmailQuery);
                    $stmtCheckEmail->bind_param("s", $email);
                    $stmtCheckEmail->execute();
                    $resultCheckEmail = $stmtCheckEmail->get_result();

                    if ($resultCheckEmail->num_rows > 0) {
                        // Email sudah digunakan
                        $result_item = [
                            'status' => 'error',
                            'message' => "Email sudah digunakan",
                            'data' => [
                                'name' => $originalName,
                                'nik' => $nik,
                                'email' => $email,
                                'dept' => $dept,
                                'bagian' => $bagian,
                                'jabatan' => $jabatan
                            ]
                        ];
                        
                        $_SESSION['bulk_create_results'][] = $result_item;
                        $error_count++;
                    } else {
                    // Generate verification code
                    $verification_code = sprintf("%06d", mt_rand(100000, 999999));
                    $verification_expires = date('Y-m-d H:i:s', strtotime('+15 minutes'));

                    // Jika tidak ada duplikasi, tambahkan pengguna baru
                    $query = "INSERT INTO users (name, email, nik, role_id, dept, bagian, jabatan, is_active, verification_code, verification_expires)
                              VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?)";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("sssisssss", $name, $email, $nik, $role_id, $dept, $bagian, $jabatan, $verification_code, $verification_expires);

                    if ($stmt->execute()) {
                        $user_id = $conn->insert_id;

                        // Tambahkan departemen default
                        $query = "INSERT INTO user_departments (user_id, dept) VALUES (?, ?)";
                        $stmt = $conn->prepare($query);
                        $stmt->bind_param("is", $user_id, $dept);
                        $stmt->execute();

                        // Kirim email aktivasi
                        include_once '../config/mail.php';

                        // Ambil konfigurasi email dari database
                        $settingsQuery = "SELECT smtp_server, smtp_port, smtp_password, sender_email, sender_name, smtp_encryption FROM settings WHERE id = 1";
                        $settingsResult = $conn->query($settingsQuery);

                        if ($settingsResult && $settingsResult->num_rows > 0) {
                            $settings = $settingsResult->fetch_assoc();

                            // Kirim email aktivasi
                            $activation_link = BASE_URL . "view/Aktivasi.php";

                            $mail_result = send_mail(
                                $email,
                                'Aktivasi Akun Training Center',
                                "
                                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 2px solid #BF0000; border-radius: 5px; background-color: #f9f9f9;'>
                                    <h2 style='color: #BF0000; text-align: center;'>Aktivasi Akun Training Center</h2>
                                    <p style='color: #333; font-size: 16px;'>Halo <strong>{$name}</strong>,</p>
                                    <p style='color: #333; font-size: 16px;'>Akun Anda telah dibuat di Training Center. Untuk mengaktifkan akun Anda, silakan gunakan informasi berikut:</p>
                                    <div style='background-color: #ffffff; border: 2px solid #BF0000; border-radius: 5px; padding: 15px; margin: 20px 0;'>
                                        <p style='margin: 5px 0;'><strong>NIK:</strong> {$nik}</p>
                                        <p style='margin: 5px 0;'><strong>Departemen:</strong> {$dept}</p>
                                        <p style='margin: 5px 0;'><strong>Kode Verifikasi:</strong> <span style='font-size: 18px; font-weight: bold; color: #BF0000;'>{$verification_code}</span></p>
                                    </div>
                                    <p style='color: #333; font-size: 16px;'>Untuk mengaktifkan akun Anda, kunjungi <a href='{$activation_link}' style='color: #BF0000; text-decoration: none; font-weight: bold;'>halaman aktivasi</a> dan masukkan NIK, Departemen, dan Kode Verifikasi Anda.</p>
                                    <p style='color: #333; font-size: 14px;'>Kode verifikasi ini akan kadaluarsa dalam <strong>15 menit</strong>.</p>
                                    <p style='color: #333; font-size: 14px;'>Jika Anda tidak meminta pembuatan akun ini, abaikan email ini.</p>
                                    <div style='text-align: center; margin-top: 30px;'>
                                        <p style='color: #333; font-size: 12px;'>Email ini dikirim secara otomatis. Mohon jangan membalas email ini.</p>
                                    </div>
                                </div>
                                ",
                                $settings
                            );
                        }

                        // Log aktivitas
                        if (file_exists('../config/activity_logger.php')) {
                            include_once '../config/activity_logger.php';
                            if (function_exists('log_activity')) {
                                $role_names = [
                                    1 => 'Pemohon',
                                    2 => 'Dept Head',
                                    3 => 'LnD',
                                    4 => 'FM',
                                    5 => 'Direktur',
                                    6 => 'HRGA',
                                    99 => 'Admin'
                                ];
                                $role_name = $role_names[$role_id] ?? 'Unknown';

                                log_activity($_SESSION['user_id'], "Menambahkan pengguna baru (bulk select): {$name} (NIK: {$nik}) dengan role {$role_name}", "user", [
                                    'user_id' => $user_id,
                                    'name' => $name,
                                    'nik' => $nik,
                                    'role_id' => $role_id,
                                    'dept' => $dept,
                                    'bagian' => $bagian,
                                    'jabatan' => $jabatan
                                ]);
                            }
                        }

                        $success_message = 'Berhasil ditambahkan';
                        // Names are allowed to be duplicate, no modification needed
                        
                        $result_item = [
                            'status' => 'success',
                            'message' => $success_message,
                            'data' => [
                                'name' => $name,
                                'nik' => $nik,
                                'email' => $email,
                                'dept' => $dept,
                                'bagian' => $bagian,
                                'jabatan' => $jabatan
                            ]
                        ];
                        
                        $_SESSION['bulk_create_results'][] = $result_item;
                        $success_count++;
                    } else {
                        $result_item = [
                            'status' => 'error',
                            'message' => "Gagal menambahkan: " . $stmt->error,
                            'data' => [
                                'name' => $name,
                                'nik' => $nik,
                                'email' => $email,
                                'dept' => $dept,
                                'bagian' => $bagian,
                                'jabatan' => $jabatan
                            ]
                        ];
                        
                        // Logging error insert user
                        error_log("Bulk user error: {$name} ({$nik}) - " . $stmt->error);
                        
                        $_SESSION['bulk_create_results'][] = $result_item;
                        $error_count++;
                    }
                    }
                }
                
                // Beri waktu untuk browser memproses update
                usleep(50000); // 50ms delay
            }

            // Commit transaksi jika berhasil
            $conn->commit();
            
            // Simpan pesan sukses dan role_id di session
            $_SESSION['bulk_create_success_message'] = "Proses selesai. Berhasil: $success_count, Gagal: $error_count";
            $_SESSION['bulk_create_role_id'] = $role_id;
            
            // Kirim respons batch_complete
            echo json_encode([
                'status' => 'batch_complete',
                'processed' => count($selected_employees),
                'success' => $success_count,
                'error' => $error_count
            ]) . "\n\n";
            
            // Flush output buffer untuk mengirim data segera
            ob_flush();
            flush();
            
            // Flush output buffer terakhir
            ob_flush();
            flush();
            exit();
        } catch (Exception $e) {
            // Rollback transaksi jika terjadi error
            if (isset($conn) && $conn->ping()) {
                $conn->rollback();
            }
            send_error_response('Terjadi kesalahan saat memproses data', 'Exception: ' . $e->getMessage());
        }
    } else {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error', 
            'message' => 'Tidak ada karyawan yang dipilih.'
        ]);
        exit();
    }
} else {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error', 
        'message' => 'Metode request tidak valid.'
    ]);
    exit();
}
?>