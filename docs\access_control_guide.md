# Access Control System - Level Karyawan

## Overview
Sistem access control ini menggunakan field `level_karyawan` dari tabel `karyawan` untuk mengontrol akses ke halaman-halaman tertentu berdasarkan level karyawan.

## Database Structure
```sql
-- Ta<PERSON> karyawan
CREATE TABLE `karyawan` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nik` varchar(20) NOT NULL,
  `nama` varchar(100) NOT NULL,
  `level_karyawan` int NOT NULL,  -- Field untuk level akses
  `dept` varchar(50) NOT NULL,
  `bagian` varchar(50) NOT NULL,
  `jabatan` varchar(50) NOT NULL,
  -- ... other fields
  PRIMARY KEY (`id`),
  UNIQUE KEY `nik` (`nik`)
);
```

## Level Hierarchy
- **Level 1**: Staff
- **Level 2**: Senior Staff  
- **Level 3**: Supervisor
- **Level 4**: Assistant Manager ⭐ (Minimum untuk form pengajuan training)
- **Level 5**: Manager
- **Level 6**: Senior Manager
- **Level 7**: General Manager
- **Level 8**: Director
- **Level 9**: Executive Director
- **Level 10**: President Director

## Implementation

### 1. Basic Usage
```php
<?php
include 'security.php';
include '../config/config.php';
include '../config/access_control.php';

// Check if user has minimum level 4
$user_id = $_SESSION['user_id'];
$user_data = checkUserLevel($user_id, 4, '../index.php');

// User passed access control, continue with page logic
echo "Welcome " . $user_data['name'] . " (Level " . $user_data['level_karyawan'] . ")";
?>
```

### 2. Different Minimum Levels
```php
// For level 5+ (Manager and above)
$user_data = checkUserLevel($user_id, 5, '../index.php');

// For level 7+ (General Manager and above)  
$user_data = checkUserLevel($user_id, 7, '../index.php');
```

### 3. Custom Redirect URL
```php
// Redirect to specific page on access denied
$user_data = checkUserLevel($user_id, 4, '../dashboard.php');

// Redirect to login page
$user_data = checkUserLevel($user_id, 4, '../view/login.php');
```

### 4. Check Level Without Blocking
```php
// Get user level without redirecting
$user_data = getUserLevel($user_id);
if ($user_data && $user_data['level_karyawan'] >= 4) {
    // Show advanced features
    echo '<button>Advanced Feature</button>';
}
```

### 5. Conditional Features Based on Level
```php
// Check if user has minimum level
if (hasMinimumLevel(5)) {
    // Show manager-only features
    echo '<a href="manager_reports.php">Manager Reports</a>';
}

if (hasMinimumLevel(8)) {
    // Show director-only features
    echo '<a href="director_dashboard.php">Director Dashboard</a>';
}
```

## Functions Available

### checkUserLevel($user_id, $min_level, $redirect_url)
- **Purpose**: Check access and redirect if denied
- **Parameters**:
  - `$user_id`: User ID from session
  - `$min_level`: Minimum level required (default: 4)
  - `$redirect_url`: Where to redirect if access denied (default: ../index.php)
- **Returns**: User data array if access granted
- **Behavior**: Redirects and exits if access denied

### getUserLevel($user_id)
- **Purpose**: Get user level without blocking
- **Parameters**: `$user_id`: User ID from session
- **Returns**: User data array with level or false if not found
- **Behavior**: Never redirects, just returns data

### hasMinimumLevel($required_level)
- **Purpose**: Check if current session user has minimum level
- **Parameters**: `$required_level`: Required minimum level
- **Returns**: Boolean true/false
- **Behavior**: Uses current session, no redirect

### getLevelDescription($level)
- **Purpose**: Get human-readable level description
- **Parameters**: `$level`: Level number
- **Returns**: String description (e.g., "Level 4 - Assistant Manager")

## Error Handling

### Access Denied Messages
When access is denied, users see user-friendly error messages:

1. **Data not found**: "Akses ditolak: Data karyawan tidak ditemukan. Hubungi administrator."
2. **Insufficient level**: "Akses ditolak: Level karyawan Anda (2) tidak memenuhi syarat minimum (Level 4). Hubungi administrator."

### Error Display
Errors are displayed on the index.php page with:
- ⚠️ Warning icon
- Clear error title
- Detailed error message
- Auto-hide after 10 seconds
- Manual close button

## Security Features

### Logging
All access attempts are logged:
```
ACCESS DENIED: User NIK 1234567890 has level 2, requires level 4+
ACCESS GRANTED: User NIK 1234567890 with level 5 accessed form.php
```

### Data Validation
- NIK matching between `users` and `karyawan` tables
- Level validation as integer
- SQL injection prevention with prepared statements

### Session Security
- Requires valid session
- Cross-references user data between tables
- Updates session with accurate karyawan data

## Files Modified

### Core Files
- `config/access_control.php` - Main access control functions
- `pemohon/form.php` - Protected with level 4+ requirement
- `index.php` - Error message display

### Usage Examples
```php
// In any protected file
include '../config/access_control.php';

// Method 1: Block access if insufficient level
$user_data = checkUserLevel($_SESSION['user_id'], 4);

// Method 2: Conditional features
if (hasMinimumLevel(5)) {
    // Manager features
}

// Method 3: Get level info
$user_data = getUserLevel($_SESSION['user_id']);
echo "Your level: " . getLevelDescription($user_data['level_karyawan']);
```

## Testing

### Test Cases
1. **User with level 4+**: Should access form.php successfully
2. **User with level < 4**: Should be redirected with error message
3. **User not in karyawan table**: Should be redirected with error message
4. **Invalid session**: Should be handled by existing security.php

### Test Data
Ensure test users have appropriate level_karyawan values in the database:
```sql
-- Test user with sufficient level
UPDATE karyawan SET level_karyawan = 5 WHERE nik = '1234567890';

-- Test user with insufficient level  
UPDATE karyawan SET level_karyawan = 2 WHERE nik = '0987654321';
```

## Maintenance

### Adding New Protected Pages
1. Include the access control file
2. Add checkUserLevel() call with appropriate minimum level
3. Test with users of different levels

### Changing Level Requirements
Simply modify the minimum level parameter:
```php
// Change from level 4 to level 5
$user_data = checkUserLevel($user_id, 5, '../index.php');
```

### Monitoring Access
Check error logs for access attempts:
```bash
tail -f /path/to/php/error.log | grep "ACCESS"
```
