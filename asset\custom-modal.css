
/* Custom Modal Styling */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5) !important;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.custom-modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.custom-modal {
    background-color: #fff !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    width: 90%;
    max-width: 400px;
    padding: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 30px !important;
    -webkit-border-radius: 30px !important;
    -moz-border-radius: 30px !important;
    -ms-border-radius: 30px !important;
    -o-border-radius: 30px !important;
}

.custom-modal-overlay.active .custom-modal {
    transform: translateY(0);
}

.custom-modal-header {
    color: white !important;
    padding: 15px 20px;
    font-weight: 600;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 30px !important;
    -webkit-border-radius: 30px !important;
    -moz-border-radius: 30px !important;
    -ms-border-radius: 30px !important;
    -o-border-radius: 30px !important;
}

.custom-modal-header .modal-title {
    color: white !important;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.custom-modal-close {
    background: transparent !important;
    border: none;
    color: white !important;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    margin: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.custom-modal-close:hover {
    opacity: 1;
    color: white !important;
    background-color: transparent !important;
}

.custom-modal-body {
    padding: 20px;
    font-size: 16px;
    color: #333;
    line-height: 1.5;
}

.custom-modal-footer {
    padding: 15px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa !important;
    border-radius: 30px !important;
    -webkit-border-radius: 30px !important;
    -moz-border-radius: 30px !important;
    -ms-border-radius: 30px !important;
    -o-border-radius: 30px !important;
}

.custom-modal-btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
}

.custom-modal-btn-primary {
    background-color: green !important;
    color: white !important;
}

.custom-modal-btn-primary:hover {
    background-color: rgb(2, 104, 2) !important;
    color: white !important;
}

.custom-modal-btn-secondary {
    background-color: grey !important;
    color: white;
    border-color: #ddd;
}

.custom-modal-btn-secondary:hover {
    background-color: rgb(96, 95, 95) !important;
    color: white;
}

/* Modal types */
.custom-modal-icon {
    font-size: 24px;
    margin-right: 10px;
}

.custom-modal.alert .custom-modal-header {
    background-color: #BF0000 !important;
    color: white !important;
}

.custom-modal.confirm .custom-modal-header {
    color: white !important;
    background-color: #007bff;
}

.custom-modal.info .custom-modal-header {
    color: white !important;
    background-color: #17a2b8;
}

.custom-modal.success .custom-modal-header {
    color: white !important;
    background-color: #28a745;
}

.custom-modal.warning .custom-modal-header {
    color: #333;
    background-color: #ffc107;
}

.custom-modal.warning .custom-modal-close {
    color: #333;
}

/* Animation */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

.modal-fade-in {
    animation: modalFadeIn 0.3s forwards;
}

.modal-fade-out {
    animation: modalFadeOut 0.3s forwards;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .custom-modal {
        width: 90%;
        max-height: 85vh;
        overflow-y: auto;
    }
    
    .custom-modal-header {
        padding: 14px 16px;
        font-size: 18px;
        position: sticky;
        top: 0;
        background-color: white;
        z-index: 10;
    }
    
    .custom-modal-body {
        padding: 16px;
        font-size: 15px;
        max-height: calc(85vh - 120px);
        overflow-y: auto;
    }
    
    .custom-modal-footer {
        padding: 14px 16px;
        position: sticky;
        bottom: 0;
        background-color: white;
        z-index: 10;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .custom-modal-btn {
        padding: 10px 16px;
        font-size: 15px;
        min-height: 44px;
        min-width: 100px;
        margin: 5px;
        flex: 1;
        max-width: 150px;
    }
}

@media (max-width: 480px) {
    .custom-modal {
        width: 95%;
        border-radius: 8px;
    }
    
    .custom-modal-header {
        padding: 12px 14px;
        font-size: 16px;
    }
    
    .custom-modal-body {
        padding: 14px;
        font-size: 14px;
    }
    
    .custom-modal-footer {
        padding: 12px 14px;
    }
    
    .custom-modal-btn {
        padding: 8px 14px;
        font-size: 14px;
        min-height: 40px;
        flex-basis: 100%;
        max-width: none;
    }
}
