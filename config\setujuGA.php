
<style>
.container-form {
    padding: 20px;
    max-width: 1200px;
    margin-top: 100px;
    margin: 0 auto;
}

.welcome-section {
    background: rgb(157, 0, 0);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    text-align: center;
}

.welcome-section h1 {
    margin: 0;
    font-size: 24px;
}

.welcome-section p {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
}

.stat-card h3 {
    margin: 0;
    color: #333;
    font-size: 16px;
}

.stat-card p {
    margin: 10px 0 0;
    font-size: 24px;
    color: rgb(157, 0, 0);
    font-weight: bold;
}

.submissions-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.submissions-table table, .submissions-table th, .submissions-table td {
    width: 100%;
    border-collapse: collapse;
}

.submissions-table th {
    background: #a50000;
    color: white;
    padding: 12px;
    text-align: left;
    width: 100%;
}

.submissions-table td {
    padding: 12px;
    border-bottom: 1px solid #eee;
    width: 100%;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.status-pending {
    background-color: #fff3e0;
    color: #f57c00;
}

.status-approved {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.action-button {
    display: inline-block;
    padding: 8px 16px;
    background: #a50000;
    color: white;
    border-radius: 4px;
    text-decoration: none;
    transition: background 0.3s ease;
}

.action-button:hover {
    background: #800000;
}

.department-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}
.date-fixed {
    background: #e3f2fd;
    color:rgb(63, 224, 18);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}
.date-pending{
  background: #e3f2fd;
    color:rgb(63, 224, 18);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}
.participants-count {
    background: #f3e5f5;
    color: #7b1fa2;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

</style>

<div class="welcome-section">
        <h1>Dashboard Persetujuan Training</h1>
        <p>Selamat datang, <?= htmlspecialchars($_SESSION['user_name'] ?? 'GA') ?></p>
    </div>

    <div class="stats-section">
        <div class="stat-card">
            <h3>Total Pengajuan</h3>
            <p><?= $stats['total'] ?></p>
        </div>
        <div class="stat-card">
            <h3>Menunggu Persetujuan</h3>
            <p><?= $stats['pending'] ?></p>
        </div>
        <div class="stat-card">
            <h3>Disetujui</h3>
            <p><?= $stats['approved'] ?></p>
        </div>
        <div class="stat-card">
            <h3>Training Hari Ini</h3>
            <p><?= $stats['today'] ?></p>
        </div>
    </div>

    <div class="submissions-table">
        <table>
            <tr>
                <th>Topik Training</th>
                <th>Pengaju</th>
                <th>Departemen</th>
                <th>Peserta</th>
                <th>Tanggal</th>
                <th>Status</th>
                <th>Aksi</th>
            </tr>
            <?php if (empty($result_array)): ?>
                <tr>
                    <td colspan="7" style="text-align: center; padding: 20px;">
                        Tidak ada training yang perlu di-approve saat ini.
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($result_array as $row): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['training_topic']) ?></td>
                        <td><?= htmlspecialchars($row['requester_name']) ?></td>
                        <td>
                            <span class="department-badge">
                                <i class="fas fa-building"></i>
                                <?= htmlspecialchars($row['departemen'] ?? 'N/A') ?>
                            </span>
                        </td>
                        <td>
                            <span class="participants-count">
                                <i class="fas fa-users"></i>
                                <?= $row['total_participants'] ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($row['training_date_fixed']): ?>
                                <span class="date-fixed">
                                    <i class="fas fa-calendar-check"></i>
                                    <?= htmlspecialchars($row['training_date_fixed']) ?>
                                </span>
                            <?php else: ?>
                                <span class="date-pending">
                                    <i class="fas fa-calendar-alt"></i>
                                    <?= htmlspecialchars($row['training_date']) ?>
                                </span>
                            <?php endif; ?>
                        </td>
                        <td>
                          <i class="fas fa-hourglass-half" style="color: #f57c00;"></i>
                            <span class="status-badge <?= $row['approved_ga'] == 'Approved' ? 'status-approved' : 'status-pending' ?>">
                                <?= htmlspecialchars($row['approved_ga']) ?>
                            </span>
                        </td>
                        <td>
                            <a href="detail_training.php?id=<?= $row['id'] ?>" class="action-button">
                                <i class="fas fa-info-circle"></i>
                                Detail
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </table>
    </div>