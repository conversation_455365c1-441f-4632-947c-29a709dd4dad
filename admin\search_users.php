<?php
session_start();
include '../config/config.php';

// Cek autentikasi
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

$search = isset($_GET['search']) ? $_GET['search'] : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'id';
$order = isset($_GET['order']) ? strtoupper($_GET['order']) : 'DESC';
$limit = 10;
$offset = ($page - 1) * $limit;

// Validasi sort dan order untuk mencegah SQL injection
$allowed_sorts = ['id'];
$allowed_orders = ['ASC', 'DESC'];
if (!in_array($sort, $allowed_sorts)) $sort = 'id';
if (!in_array($order, $allowed_orders)) $order = 'DESC';

// Function to build multi-search WHERE clause
function buildMultiSearchQuery($search_terms) {
    if (empty($search_terms)) {
        return ["1=1", []];
    }

    $where_conditions = [];
    $params = [];

    foreach ($search_terms as $term) {
        if (trim($term) === '') continue;

        $term_param = "%" . trim($term) . "%";
        $where_conditions[] = "(u.name LIKE ? OR u.email LIKE ? OR u.nik LIKE ? OR
                               u.dept LIKE ? OR u.bagian LIKE ? OR u.jabatan LIKE ? OR
                               r.role_name LIKE ?)";

        // Add 7 parameters for each search term (one for each field)
        for ($i = 0; $i < 7; $i++) {
            $params[] = $term_param;
        }
    }

    $where_clause = empty($where_conditions) ? "1=1" : "(" . implode(" AND ", $where_conditions) . ")";
    return [$where_clause, $params];
}

// Parse search input for multiple terms
$search_terms = [];
if (!empty($search)) {
    // Split by spaces and commas, filter empty terms
    $search_terms = array_filter(
        array_map('trim', preg_split('/[\s,]+/', $search)),
        function($term) { return $term !== ''; }
    );
}

// Build WHERE clause and parameters for multi-search
list($where_clause, $search_params) = buildMultiSearchQuery($search_terms);

// Query untuk menghitung total records
$count_query = "SELECT COUNT(*) as total FROM users u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE $where_clause";

$stmt_count = $conn->prepare($count_query);
if (!empty($search_params)) {
    $param_types = str_repeat('s', count($search_params));
    $stmt_count->bind_param($param_types, ...$search_params);
}
$stmt_count->execute();
$total_records = $stmt_count->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total_records / $limit);

// Query untuk mengambil data dengan pagination dan sorting
// Prioritaskan user dengan is_active=1 di atas
$query = "SELECT u.id, u.name, u.email, u.nik, r.role_name, u.dept, u.bagian, u.jabatan, u.is_active
          FROM users u
          LEFT JOIN roles r ON u.role_id = r.id
          WHERE $where_clause
          ORDER BY u.is_active DESC, u.$sort $order
          LIMIT ? OFFSET ?";

$stmt = $conn->prepare($query);
$all_params = array_merge($search_params, [$limit, $offset]);
$param_types = str_repeat('s', count($search_params)) . 'ii';
$stmt->bind_param($param_types, ...$all_params);
$stmt->execute();
$result = $stmt->get_result();

$users = [];
while ($row = $result->fetch_assoc()) {
    $users[] = $row;
}

header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'users' => $users,
    'pagination' => [
        'current_page' => $page,
        'total_pages' => $total_pages,
        'total_records' => $total_records,
        'limit' => $limit
    ],
    'sort' => [
        'column' => $sort,
        'order' => $order
    ],
    'search_info' => [
        'original_query' => $search,
        'search_terms' => $search_terms,
        'terms_count' => count($search_terms)
    ]
]);

?>
