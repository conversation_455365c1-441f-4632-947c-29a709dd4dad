<?php
/**
 * AJAX Search Users for Announcement Recipients
 * This file handles AJAX requests for searching users to be announcement recipients
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Get parameters
$search_term = isset($_GET['search']) ? trim($_GET['search']) : '';
$role_filter = isset($_GET['role']) ? trim($_GET['role']) : '';
$announcement_id = isset($_GET['announcement_id']) ? intval($_GET['announcement_id']) : 0;
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 20; // Show 20 users per page
$offset = ($page - 1) * $limit;

// Get existing recipients if editing
$existing_recipients = [];
if ($announcement_id > 0) {
    $recipients_query = "SELECT user_id FROM announcement_recipients WHERE announcement_id = ?";
    $stmt = $conn->prepare($recipients_query);
    $stmt->bind_param("i", $announcement_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $existing_recipients[] = $row['user_id'];
    }
    $stmt->close();
}

// First, let's check the structure of the users table to find the name column
$user_table_query = "DESCRIBE users";
$user_table_result = $conn->query($user_table_query);
$name_column = 'name'; // Default to name

if ($user_table_result) {
    while ($column = $user_table_result->fetch_assoc()) {
        // Look for common name column patterns
        if (in_array(strtolower($column['Field']), ['full_name', 'name', 'user_name', 'username', 'nama'])) {
            $name_column = $column['Field'];
            // Prefer full_name or name if available
            if (in_array(strtolower($column['Field']), ['full_name', 'name', 'nama'])) {
                break;
            }
        }
    }
}

// Function to build multi-search WHERE clause
function buildMultiSearchQuery($search_terms, $name_column) {
    if (empty($search_terms)) {
        return ["1=1", []];
    }

    $where_conditions = [];
    $params = [];

    foreach ($search_terms as $term) {
        if (trim($term) === '') continue;

        $term_param = "%" . trim($term) . "%";
        $where_conditions[] = "(u.$name_column LIKE ? OR u.email LIKE ? OR u.nik LIKE ? OR
                               u.dept LIKE ? OR u.bagian LIKE ? OR u.jabatan LIKE ? OR
                               r.role_name LIKE ?)";

        // Add 7 parameters for each search term (one for each field)
        for ($i = 0; $i < 7; $i++) {
            $params[] = $term_param;
        }
    }

    $where_clause = empty($where_conditions) ? "1=1" : "(" . implode(" AND ", $where_conditions) . ")";
    return [$where_clause, $params];
}

// Parse search input for multiple terms
$search_terms = [];
if (!empty($search_term)) {
    // Split by spaces and commas, filter empty terms
    $search_terms = array_filter(
        array_map('trim', preg_split('/[\s,]+/', $search_term)),
        function($term) { return $term !== ''; }
    );
}

// Build WHERE clause and parameters for multi-search
list($where_clause, $search_params) = buildMultiSearchQuery($search_terms, $name_column);

// Prepare the query
$users = [];
$query_params = $search_params;
$param_types = str_repeat('s', count($search_params));

$query = "SELECT u.id, u.$name_column as user_name, u.email, u.role_id, u.dept, u.bagian, u.jabatan, u.is_active, r.role_name
          FROM users u
          LEFT JOIN roles r ON u.role_id = r.id
          WHERE u.is_active = 1 AND $where_clause";

// Add role filter if specified
if (!empty($role_filter)) {
    $query .= " AND u.role_id = ?";
    $query_params[] = $role_filter;
    $param_types .= 's';
}

// Count total records for pagination
$count_query = str_replace("SELECT u.id, u.$name_column as user_name, u.email, u.role_id, u.dept, u.bagian, u.jabatan, u.is_active, r.role_name", "SELECT COUNT(*)", $query);
$stmt_count = $conn->prepare($count_query);
if (!empty($query_params)) {
    $stmt_count->bind_param($param_types, ...$query_params);
}
$stmt_count->execute();
$total_records = $stmt_count->get_result()->fetch_assoc()['COUNT(*)'];
$total_pages = ceil($total_records / $limit);
$stmt_count->close();

// Add order by and pagination
$query .= " ORDER BY u.is_active DESC, u.$name_column ASC LIMIT ? OFFSET ?";
$query_params[] = $limit;
$query_params[] = $offset;
$param_types .= 'ii';

// Prepare and execute the query
$stmt = $conn->prepare($query);

if (!empty($query_params)) {
    $stmt->bind_param($param_types, ...$query_params);
}

$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $users[] = $row;
}
$stmt->close();

// Define role labels
$role_labels = [
    '1' => 'Pemohon',
    '2' => 'Dept Head',
    '3' => 'HRD',
    '4' => 'GA',
    '5' => 'Factory Manager',
    '6' => 'Direktur',
    '99' => 'Admin'
];

// Prepare HTML output
$html = '';

if (empty($users)) {
    $html = '<div class="alert alert-info text-center">
                <i class="fas fa-search"></i> 
                Tidak ada pengguna yang ditemukan untuk pencarian "' . htmlspecialchars($search_term) . '"
             </div>';
} else {
    foreach ($users as $user) {
        $role_label = isset($role_labels[$user['role_id']]) ? $role_labels[$user['role_id']] : ($user['role_name'] ?? 'Role ' . $user['role_id']);
        $is_selected = in_array($user['id'], $existing_recipients);
        
        $html .= '
        <div class="form-check mb-2 user-item" data-role="' . $user['role_id'] . '">
            <input type="checkbox" class="form-check-input user-checkbox" 
                   id="recipient_' . $user['id'] . '" 
                   name="recipients[]" 
                   value="' . $user['id'] . '"
                   ' . ($is_selected ? 'checked' : '') . '>
            <label class="form-check-label" for="recipient_' . $user['id'] . '">
                <div class="user-info">
                    <div class="user-name"><strong>' . htmlspecialchars($user['user_name']) . '</strong></div>
                    <div class="user-details">
                        <small class="text-muted">
                            ' . htmlspecialchars($user['email']) . ' | 
                            ' . htmlspecialchars($role_label) . ' | 
                            ' . htmlspecialchars($user['dept']) . ' - ' . htmlspecialchars($user['bagian']) . ' | 
                            ' . htmlspecialchars($user['jabatan']) . '
                        </small>
                    </div>
                </div>
            </label>
        </div>';
    }
}

// Pagination HTML
$pagination_html = '';
if ($total_pages > 1) {
    $pagination_html = '<div class="d-flex justify-content-between align-items-center mt-3">
        <small class="text-muted">Menampilkan ' . (($page - 1) * $limit + 1) . '-' . min($page * $limit, $total_records) . ' dari ' . $total_records . ' pengguna</small>
        <div class="btn-group btn-group-sm" role="group">';
    
    // Previous button
    if ($page > 1) {
        $pagination_html .= '<button type="button" class="btn btn-outline-primary" onclick="loadUsers(' . ($page - 1) . ')">
            <i class="fas fa-chevron-left"></i> Sebelumnya
        </button>';
    }
    
    // Page numbers (show max 5 pages)
    $start_page = max(1, $page - 2);
    $end_page = min($total_pages, $start_page + 4);
    
    for ($i = $start_page; $i <= $end_page; $i++) {
        $active_class = ($i == $page) ? 'btn-primary' : 'btn-outline-primary';
        $pagination_html .= '<button type="button" class="btn ' . $active_class . '" onclick="loadUsers(' . $i . ')">' . $i . '</button>';
    }
    
    // Next button
    if ($page < $total_pages) {
        $pagination_html .= '<button type="button" class="btn btn-outline-primary" onclick="loadUsers(' . ($page + 1) . ')">
            Selanjutnya <i class="fas fa-chevron-right"></i>
        </button>';
    }
    
    $pagination_html .= '</div></div>';
}

// Return the response
echo json_encode([
    'html' => $html,
    'pagination' => $pagination_html,
    'count' => count($users),
    'total_records' => $total_records,
    'total_pages' => $total_pages,
    'current_page' => $page,
    'search_info' => [
        'original_query' => $search_term,
        'search_terms' => $search_terms,
        'terms_count' => count($search_terms),
        'role_filter' => $role_filter
    ]
]);

// Close database connection
$conn->close();
?>
