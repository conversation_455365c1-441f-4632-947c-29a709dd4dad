<?php
session_start();
include '../config/config.php';

// Cek apakah user adalah admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

$error = '';
$success = '';
$nik = $_GET['nik'] ?? '';

// Ambil data karyawan
$query = "SELECT * FROM karyawan WHERE nik = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $nik);
$stmt->execute();
$result = $stmt->get_result();
$karyawan = $result->fetch_assoc();

if (!$karyawan) {
    header('Location: employee_management.php');
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nama = $_POST['nama'];
    $tgl_masuk = $_POST['tgl_masuk'];
    $jk = $_POST['jk'];
    $level_karyawan = $_POST['level_karyawan'];
    $tgl_lahir = $_POST['tgl_lahir'];
    $agama = $_POST['agama'];
    $pendidikan_akhir = $_POST['pendidikan_akhir'];
    $no_telp = $_POST['no_telp'];
    $dept = $_POST['dept'];
    $bagian = $_POST['bagian'];
    $jabatan = $_POST['jabatan'];
    $group = $_POST['group'];
    $status = $_POST['status'];
    $pt = $_POST['pt'];

    $query = "UPDATE karyawan SET
              nama = ?, tgl_masuk = ?, jk = ?, level_karyawan = ?,
              tgl_lahir = ?, agama = ?, pendidikan_akhir = ?, no_telp = ?,
              dept = ?, bagian = ?, jabatan = ?, `group` = ?, status = ?, pt = ?
              WHERE nik = ?";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("sssssssssssssss",
        $nama, $tgl_masuk, $jk, $level_karyawan,
        $tgl_lahir, $agama, $pendidikan_akhir, $no_telp,
        $dept, $bagian, $jabatan, $group, $status, $pt, $nik);

    if ($stmt->execute()) {
        $success = "Data karyawan berhasil diupdate!";

        // Log aktivitas
        if (file_exists('../config/activity_logger.php')) {
            include_once '../config/activity_logger.php';
            if (function_exists('log_activity')) {
                log_activity($_SESSION['user_id'], "Mengupdate data karyawan: {$nama} (NIK: {$nik})", "employee", [
                    'nik' => $nik,
                    'nama' => $nama,
                    'status' => $status
                ]);
            }
        }

        // Refresh data
        $stmt = $conn->prepare("SELECT * FROM karyawan WHERE nik = ?");
        $stmt->bind_param("s", $nik);
        $stmt->execute();
        $result = $stmt->get_result();
        $karyawan = $result->fetch_assoc();
    } else {
        $error = "Gagal mengupdate data karyawan: " . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
.edit-employee-container {
    max-width: 1200px;
    margin: 30px auto;
    padding: 20px;
}

.page-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 20px;
    background: linear-gradient(135deg, #BF0000, #800000);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.page-header h1 {
    margin: 0;
    font-size: 2.5em;
    font-weight: 700;
}

.form-container {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.btn-container {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #BF0000;
    color: white;
    border: none;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: none;
}

/* Status styling */
.status-select option.inactive-option {
    color: #dc3545;
    font-weight: bold;
}

/* Styling for inactive employees in the table */
.inactive-employee {
    background-color: #f8f9fa !important;
    color: #6c757d !important;
    text-decoration: line-through;
}
</style>

<body>
    <?php include '../config/navbar.php'; ?>
    <div class="container-form">
        <div class="edit-employee-container">
            <div class="page-header">
                <h1>Edit Karyawan</h1>
                <p>NIK: <?= htmlspecialchars($karyawan['nik']) ?></p>
            </div>

            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?= htmlspecialchars($success) ?></div>
            <?php endif; ?>

            <div class="form-container">
                <form method="POST" onsubmit="return validateForm()">
                    <div class="form-section">
                        <h2>Informasi Pribadi</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label>NIK</label>
                                <input type="text" class="form-control" name="nik" value="<?= htmlspecialchars($karyawan['nik']) ?>">
                            </div>
                            <div class="form-group">
                                <label>Nama Lengkap</label>
                                <input type="text" class="form-control" name="nama" value="<?= htmlspecialchars($karyawan['nama']) ?>" required>
                            </div>
                            <div class="form-group">
                                <label>Tanggal Masuk</label>
                                <input type="text" class="form-control" name="tgl_masuk" value="<?= htmlspecialchars(date('d F Y', strtotime($karyawan['tgl_masuk'] ?? ''))) ?>">
                            </div>
                            <div class="form-group">
                                <label>Jenis Kelamin</label>
                                <select class="form-control" name="jk" required>
                                    <option value="L" <?= $karyawan['jk'] == 'L' ? 'selected' : '' ?>>Laki-laki</option>
                                    <option value="P" <?= $karyawan['jk'] == 'P' ? 'selected' : '' ?>>Perempuan</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Tanggal Lahir</label>
                                <input type="text" class="form-control" name="tgl_lahir" value="<?= htmlspecialchars(date('d F Y', strtotime($karyawan['tgl_lahir'] ?? ''))) ?>">
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Informasi Tambahan</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label>Agama</label>
                                <input type="text" class="form-control" name="agama" value="<?= htmlspecialchars($karyawan['agama']) ?>" required>
                            </div>
                            <div class="form-group">
                                <label>Pendidikan Terakhir</label>
                                <input type="text" class="form-control" name="pendidikan_akhir" value="<?= htmlspecialchars($karyawan['pendidikan_akhir']) ?>" required>
                            </div>
                            <div class="form-group">
                                <label>No. Telepon</label>
                                <input type="text" class="form-control" name="no_telp" value="<?= htmlspecialchars($karyawan['no_telp']) ?>" required>
                            </div>
                            <div class="form-group">
                                <label>Level Karyawan</label>
                                <input type="text" class="form-control" name="level_karyawan" value="<?= htmlspecialchars($karyawan['level_karyawan']) ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Informasi Pekerjaan</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label>Departemen</label>
                                <select class="form-control" name="dept" id="dept" required onchange="updateBagian()">
                                    <option value="">Pilih Departemen</option>
                                    <?php
                                    $dept_query = "SELECT DISTINCT dept FROM karyawan";
                                    $dept_result = $conn->query($dept_query);
                                    while ($dept_row = $dept_result->fetch_assoc()) {
                                        $dept_value = $dept_row['dept'];
                                        $selected = ($karyawan['dept'] == $dept_value) ? 'selected' : '';
                                        echo "<option value='" . htmlspecialchars($dept_value) . "' $selected>" . htmlspecialchars($dept_value) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Bagian</label>
                                <select class="form-control" name="bagian" id="bagian" required onchange="updateJabatan()">
                                    <option value="">Pilih Bagian</option>
                                    <?php
                                    $bagian_query = "SELECT DISTINCT bagian FROM karyawan WHERE dept = ?";
                                    $stmt = $conn->prepare($bagian_query);
                                    $stmt->bind_param("s", $karyawan['dept']);
                                    $stmt->execute();
                                    $bagian_result = $stmt->get_result();
                                    while ($bagian_row = $bagian_result->fetch_assoc()) {
                                        $bagian_value = $bagian_row['bagian'];
                                        $selected = ($karyawan['bagian'] == $bagian_value) ? 'selected' : '';
                                        echo "<option value='" . htmlspecialchars($bagian_value) . "' $selected>" . htmlspecialchars($bagian_value) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Jabatan</label>
                                <select class="form-control" name="jabatan" id="jabatan" required>
                                    <option value="">Pilih Jabatan</option>
                                    <?php
                                    $jabatan_query = "SELECT DISTINCT jabatan FROM karyawan WHERE dept = ? AND bagian = ?";
                                    $stmt = $conn->prepare($jabatan_query);
                                    $stmt->bind_param("ss", $karyawan['dept'], $karyawan['bagian']);
                                    $stmt->execute();
                                    $jabatan_result = $stmt->get_result();
                                    while ($jabatan_row = $jabatan_result->fetch_assoc()) {
                                        $jabatan_value = $jabatan_row['jabatan'];
                                        $selected = ($karyawan['jabatan'] == $jabatan_value) ? 'selected' : '';
                                        echo "<option value='" . htmlspecialchars($jabatan_value) . "' $selected>" . htmlspecialchars($jabatan_value) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Group</label>
                                <input type="text" class="form-control" name="group" value="<?= htmlspecialchars($karyawan['group']) ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Status & Perusahaan</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label>Status</label>
                                <select class="form-control status-select" name="status" required>
                                    <option value="Aktif" <?= ($karyawan['status'] == 'Aktif' || empty($karyawan['status'])) ? 'selected' : '' ?>>Aktif</option>
                                    <option value="Tidak Aktif" <?= $karyawan['status'] == 'Tidak Aktif' ? 'selected' : '' ?> class="inactive-option">Tidak Aktif</option>
                                    <option value="Resign" <?= $karyawan['status'] == 'Resign' ? 'selected' : '' ?> class="inactive-option">Resign</option>
                                    <option value="Habis Kontrak" <?= $karyawan['status'] == 'Habis Kontrak' ? 'selected' : '' ?> class="inactive-option">Habis Kontrak</option>
                                    <?php
                                    // Tambahkan status lain yang mungkin sudah ada di database
                                    $status_query = "SELECT DISTINCT status FROM karyawan WHERE status NOT IN ('Aktif', 'Tidak Aktif', 'Resign', 'Habis Kontrak') AND status IS NOT NULL AND status != ''";
                                    $status_result = $conn->query($status_query);
                                    while ($status_row = $status_result->fetch_assoc()) {
                                        $status_value = $status_row['status'];
                                        $selected = ($karyawan['status'] == $status_value) ? 'selected' : '';
                                        echo "<option value='" . htmlspecialchars($status_value) . "' $selected>" . htmlspecialchars($status_value) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Perusahan/PT</label>
                                <select class="form-control" name="pt" required>
                                    <?php
                                    $pt_query = "SELECT DISTINCT pt FROM karyawan";
                                    $pt_result = $conn->query($pt_query);
                                    while ($pt_row = $pt_result->fetch_assoc()) {
                                        $pt_values = $pt_row['pt'];
                                        $selected = ($karyawan['pt'] == $pt_values) ? 'selected' : '';
                                        echo "<option value='" . htmlspecialchars($pt_values) . "' $selected>" . htmlspecialchars($pt_values) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="btn-container">
                        <a href="employee_management.php" class="btn btn-secondary">Kembali</a>
                        <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php include '../config/footer.php'; ?>
    <script src="js/employee_form.js"></script>
</body>
</html>

