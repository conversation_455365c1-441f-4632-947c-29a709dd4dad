<?php
session_start();
include 'config/config.php';

// Cek apakah user sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: view/login.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars(get_app_name()) ?></title>
    <link rel="icon" href="<?= BASE_URL ?>asset/picture/image-removebg-preview (1).ico" width="100%">
    <link rel="stylesheet" href="<?= BASE_URL ?>asset/style.css">
    <!-- Dashboard Style -->
    <link rel="stylesheet" href="<?= BASE_URL ?>assets/css/dashboard-style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>

<style>
    .help-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 40px 20px;
    }

    .help-header {
        text-align: center;
        margin-bottom: 40px;
    }

    .help-header h1 {
        color: #BF0000;
        font-size: 32px;
        margin-bottom: 10px;
    }

    .help-header p {
        color: #666;
        font-size: 16px;
    }

    .help-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 30px;
        margin-bottom: 50px;
    }

    .help-card {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .help-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }

    .card-icon {
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f9f9f9;
        color: #BF0000;
        font-size: 48px;
    }

    .card-content {
        padding: 20px;
    }

    .card-content h3 {
        color: #333;
        margin-bottom: 10px;
        font-size: 20px;
    }

    .card-content p {
        color: #666;
        margin-bottom: 15px;
        line-height: 1.5;
    }

    .card-link {
        display: inline-block;
        color: #BF0000;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }

    .card-link:hover {
        color: #a00000;
    }

    .help-section {
        margin-bottom: 50px;
    }

    .help-section h2 {
        color: #BF0000;
        font-size: 24px;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f0f0f0;
    }

    .help-steps {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .help-step {
        display: flex;
        gap: 20px;
        align-items: flex-start;
    }

    .step-number {
        width: 40px;
        height: 40px;
        background-color: #BF0000;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        flex-shrink: 0;
    }

    .step-content {
        flex: 1;
    }

    .step-content h3 {
        color: #333;
        margin-bottom: 10px;
        font-size: 18px;
    }

    .step-content p {
        color: #666;
        line-height: 1.5;
    }

    .help-image {
        max-width: 100%;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin: 15px 0;
    }

    .contact-support {
        text-align: center;
        margin-top: 50px;
        padding: 30px;
        background-color: #f9f9f9;
        border-radius: 8px;
    }

    .contact-support h3 {
        color: #333;
        margin-bottom: 15px;
    }

    .contact-support p {
        color: #666;
        margin-bottom: 20px;
    }

    .contact-button {
        display: inline-block;
        padding: 12px 25px;
        background-color: #BF0000;
        color: white;
        border-radius: 5px;
        text-decoration: none;
        transition: background-color 0.3s ease;
    }

    .contact-button:hover {
        background-color: #a00000;
    }

    @media (max-width: 768px) {
        .help-header h1 {
            font-size: 28px;
        }

        .help-cards {
            grid-template-columns: 1fr;
        }

        .help-step {
            flex-direction: column;
            align-items: flex-start;
        }

        .step-number {
            margin-bottom: 10px;
        }
    }
</style>
<body>
    <?php include 'config/navbarb.php'; ?>

    <div class="help-container">
        <div class="help-header">
            <h1>Pusat Bantuan</h1>
            <p>Temukan panduan dan bantuan untuk menggunakan Training Center</p>
        </div>

        <div class="help-cards">
            <div class="help-card">
                <div class="card-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <div class="card-content">
                    <h3>FAQ</h3>
                    <p>Temukan jawaban untuk pertanyaan yang sering diajukan tentang Training Center.</p>
                    <a href="faq.php" class="card-link">Lihat FAQ <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>

            <div class="help-card">
                <div class="card-icon">
                    <i class="fas fa-book"></i>
                </div>
                <div class="card-content">
                    <h3>Panduan Pengguna</h3>
                    <p>Pelajari cara menggunakan fitur-fitur Training Center dengan panduan lengkap.</p>
                    <a href="#panduan" class="card-link">Lihat Panduan <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>

            <div class="help-card">
                <div class="card-icon">
                    <i class="fas fa-video"></i>
                </div>
                <div class="card-content">
                    <h3>Video Tutorial</h3>
                    <p>Tonton video tutorial untuk memahami cara menggunakan Training Center.</p>
                    <a href="#tutorial" class="card-link">Lihat Tutorial <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </div>

        <div id="panduan" class="help-section">
            <h2>Panduan Pengajuan Training</h2>

            <div class="help-steps">
                <div class="help-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>Login ke Akun Anda</h3>
                        <p>Masuk ke Training Center menggunakan NIK dan password Anda. Jika Anda belum memiliki akun, hubungi departemen HR untuk mendapatkan akun.</p>
                    </div>
                </div>

                <div class="help-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Akses Menu Pengajuan Training</h3>
                        <p>Setelah login, klik menu "Pengajuan Training" di navbar atau di dashboard. Anda akan diarahkan ke halaman formulir pengajuan training.</p>
                    </div>
                </div>

                <div class="help-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Isi Formulir Pengajuan</h3>
                        <p>Isi semua informasi yang diperlukan pada formulir, termasuk:</p>
                        <ul>
                            <li>Jenis training</li>
                            <li>Tanggal pelaksanaan</li>
                            <li>Tujuan training</li>
                            <li>Peserta training</li>
                            <li>Dokumen pendukung (jika diperlukan)</li>
                        </ul>
                    </div>
                </div>

                <div class="help-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>Tambahkan Peserta Training</h3>
                        <p>Klik tombol "Tambah Peserta" untuk menambahkan karyawan yang akan mengikuti training. Anda dapat mencari karyawan berdasarkan nama atau NIK.</p>
                    </div>
                </div>

                <div class="help-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h3>Kirim Pengajuan</h3>
                        <p>Setelah semua informasi terisi dengan benar, klik tombol "Kirim Pengajuan Training". Sistem akan memproses pengajuan Anda dan mengirimkannya ke approver yang sesuai.</p>
                    </div>
                </div>

                <div class="help-step">
                    <div class="step-number">6</div>
                    <div class="step-content">
                        <h3>Pantau Status Pengajuan</h3>
                        <p>Anda dapat memantau status pengajuan training di menu "Riwayat Training". Status akan berubah sesuai dengan tahapan persetujuan.</p>
                    </div>
                </div>
            </div>
        </div>

        <div id="tutorial" class="help-section">
            <h2>Video Tutorial</h2>

            <div class="help-steps">
                <div class="help-step">
                    <div class="step-number"><i class="fas fa-play"></i></div>
                    <div class="step-content">
                        <h3>Cara Mengajukan Training</h3>
                        <p>Video tutorial lengkap tentang cara mengajukan permintaan training di Training Center.</p>
                        <p><em>Video tutorial akan segera tersedia.</em></p>
                    </div>
                </div>

                <div class="help-step">
                    <div class="step-number"><i class="fas fa-play"></i></div>
                    <div class="step-content">
                        <h3>Cara Menyetujui Pengajuan Training</h3>
                        <p>Panduan untuk approver tentang cara menyetujui atau menolak pengajuan training.</p>
                        <p><em>Video tutorial akan segera tersedia.</em></p>
                    </div>
                </div>

                <div class="help-step">
                    <div class="step-number"><i class="fas fa-play"></i></div>
                    <div class="step-content">
                        <h3>Cara Menggunakan Dashboard</h3>
                        <p>Pelajari cara memanfaatkan dashboard untuk memantau status training dan aktivitas lainnya.</p>
                        <p><em>Video tutorial akan segera tersedia.</em></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="contact-support">
            <h3>Butuh bantuan lebih lanjut?</h3>
            <p>Jika Anda memiliki pertanyaan atau masalah yang tidak tercakup dalam panduan ini, jangan ragu untuk menghubungi tim dukungan kami.</p>
            <a href="mailto:<EMAIL>" class="contact-button">
                <i class="fas fa-envelope"></i> Hubungi Dukungan
            </a>
        </div>
    </div>

    <?php include 'config/footer.php'; ?>
</body>
</html>
