/* Responsive CSS for Mobile Devices */

/* General Responsive Styles */
@media screen and (max-width: 992px) {
    h1 {
        font-size: 1.8rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    h3 {
        font-size: 1.3rem;
    }
    
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
}

/* Tablet Devices */
@media screen and (max-width: 768px) {
    h1 {
        font-size: 1.6rem;
    }
    
    h2 {
        font-size: 1.4rem;
    }
    
    h3 {
        font-size: 1.2rem;
    }
    
    .jarak {
        height: 70px;
    }
    
    /* Button Styles */
    .btn {
        padding: 10px 15px;
        font-size: 14px;
    }
    
    /* Form Elements */
    input, select, textarea {
        font-size: 14px;
    }
    
    /* Table Styles */
    table th, table td {
        padding: 8px;
        font-size: 14px;
    }
    
    /* Card Styles */
    .card {
        padding: 15px;
    }
    
    /* Footer Adjustments */
    .footer-content {
        flex-direction: column;
    }
    
    .footer-section {
        width: 100%;
        max-width: 100%;
        margin-bottom: 20px;
    }
}

/* Small Devices */
@media screen and (max-width: 576px) {
    h1 {
        font-size: 1.4rem;
    }
    
    h2 {
        font-size: 1.3rem;
    }
    
    h3 {
        font-size: 1.1rem;
    }
    
    .jarak {
        height: 60px;
    }
    
    /* Button Styles */
    .btn {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    /* Hide text on buttons, show only icons */
    .btn .d-none {
        display: none !important;
    }
    
    /* Form Elements */
    input, select, textarea {
        font-size: 13px;
        padding: 8px;
    }
    
    /* Table Styles */
    table th, table td {
        padding: 6px;
        font-size: 13px;
    }
    
    /* Card Styles */
    .card {
        padding: 12px;
    }
    
    /* Leave Class Page */
    .leave-class-header {
        padding: 20px 15px;
    }
    
    .leave-class-header h1 {
        font-size: 1.3rem;
    }
    
    .leave-class-body {
        padding: 15px;
    }
    
    /* Class Detail Page */
    .class-header {
        padding: 15px;
    }
    
    .class-title {
        font-size: 1.4rem;
    }
    
    .class-meta, .class-description {
        font-size: 0.9rem;
    }
    
    /* Tab Navigation */
    .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .nav-tabs .nav-link {
        padding: 8px 10px;
        font-size: 0.9rem;
    }
}

/* Extra Small Devices */
@media screen and (max-width: 480px) {
    h1 {
        font-size: 1.3rem;
    }
    
    h2 {
        font-size: 1.2rem;
    }
    
    h3 {
        font-size: 1rem;
    }
    
    .jarak {
        height: 50px;
    }
    
    /* Button Styles */
    .btn {
        padding: 6px 10px;
        font-size: 12px;
        min-width: auto !important;
    }
    
    /* Form Elements */
    input, select, textarea {
        font-size: 12px;
        padding: 6px;
    }
    
    /* Table Styles */
    table th, table td {
        padding: 5px;
        font-size: 12px;
    }
    
    /* Card Styles */
    .card {
        padding: 10px;
    }
    
    /* Leave Class Page */
    .leave-class-header {
        padding: 15px 10px;
    }
    
    .leave-class-header h1 {
        font-size: 1.2rem;
    }
    
    .leave-class-body {
        padding: 10px;
    }
    
    /* Class Detail Page */
    .class-header {
        padding: 12px;
    }
    
    .class-title {
        font-size: 1.3rem;
    }
    
    .class-meta, .class-description {
        font-size: 0.85rem;
    }
    
    /* Tab Navigation */
    .nav-tabs .nav-link {
        padding: 6px 8px;
        font-size: 0.8rem;
    }
    
    /* Hide text in tabs, show only icons */
    .nav-tabs .nav-link span {
        display: none;
    }
}
