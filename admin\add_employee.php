<?php
session_start();
include '../config/config.php';

// Cek apakah user adalah admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nik = $_POST['nik'];
    $nama = $_POST['nama'];
    $tgl_masuk = $_POST['tgl_masuk'];
    $jk = $_POST['jk'];
    $level_karyawan = $_POST['level_karyawan'];
    $tgl_lahir = $_POST['tgl_lahir'];
    $agama = $_POST['agama'];
    $pendidikan_akhir = $_POST['pendidikan_akhir'];
    $no_telp = $_POST['no_telp'];
    $dept = $_POST['dept'];
    $bagian = $_POST['bagian'];
    $jabatan = $_POST['jabatan'];
    $group = $_POST['group'];
    $status = $_POST['status'];
    $pt = $_POST['pt'];

    // Cek apakah NIK sudah ada
    $check_query = "SELECT nik FROM karyawan WHERE nik = ?";
    $stmt = $conn->prepare($check_query);
    $stmt->bind_param("s", $nik);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $error = "NIK sudah terdaftar!";
    } else {
        $query = "INSERT INTO karyawan (nik, nama, tgl_masuk, jk, level_karyawan, tgl_lahir,
                                      agama, pendidikan_akhir, no_telp, dept, bagian, jabatan,
                                      `group`, status, pt)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("sssssssssssssss",
            $nik, $nama, $tgl_masuk, $jk, $level_karyawan, $tgl_lahir,
            $agama, $pendidikan_akhir, $no_telp, $dept, $bagian, $jabatan,
            $group, $status, $pt);

        if ($stmt->execute()) {
            $success = "Data karyawan berhasil ditambahkan!";
        } else {
            $error = "Gagal menambahkan data karyawan: " . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
:root {
    --primary-color: #BF0000;
    --primary-color-dark: #900000;
    --primary-color-light: rgba(191, 0, 0, 0.1);
    --primary-color-lighter: rgba(191, 0, 0, 0.05);
    --text-dark: #333333;
    --text-light: #ffffff;
    --text-muted: rgba(255, 255, 255, 0.8);
    --border-light: rgba(255, 255, 255, 0.2);
    --accent-color: #FF3333;
    --accent-light: rgba(255, 51, 51, 0.3);
    --accent-lighter: rgba(255, 51, 51, 0.1);
    --dark-accent: #333333;
    --dark-accent-light: rgba(51, 51, 51, 0.3);
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --transition-fast: 0.2s ease;
}

.form-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 25px;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-color-dark));
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 14px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #f9f9f9;
}

.form-group input:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-color-light);
    outline: none;
    background-color: #fff;
}

.form-row {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.form-row .form-group {
    flex: 1 1 250px;
}

.alert {
    padding: 15px;
    border-radius: var(--border-radius-md);
    margin-bottom: 20px;
    position: relative;
    padding-left: 45px;
    font-weight: 500;
}

.alert::before {
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
}

.alert-danger {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.alert-danger::before {
    content: '\f071';
    color: #dc3545;
}

.alert-success {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
    color: #155724;
}

.alert-success::before {
    content: '\f00c';
    color: #28a745;
}

.welcome-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--text-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    transform: rotate(30deg);
    pointer-events: none;
}

/* Pattern overlay for welcome section */
.welcome-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
}

.welcome-section h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    position: relative;
}

.welcome-section p {
    margin-bottom: var(--spacing-sm);
    font-size: 16px;
    opacity: 0.9;
    position: relative;
}

/* Container styling */
.container-form {
    padding: var(--spacing-lg);
    max-width: 1200px;
    margin: 80px auto 0;
    animation: fadeIn 0.5s ease-in-out;
}

/* Button styling */
button[type="submit"],
.btn {
    padding: 12px 24px;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

button[type="submit"],
.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

button[type="submit"]:hover,
.btn-primary:hover {
    background-color: var(--primary-color-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-secondary {
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    flex-wrap: wrap;
    justify-content: center;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
    .container-form {
        padding: 10px;
        margin-top: 60px;
    }

    .form-container {
        padding: 20px 15px;
        margin: 10px auto;
    }

    .welcome-section {
        padding: 20px 15px;
        margin-bottom: 20px;
    }

    .welcome-section h1 {
        font-size: 22px;
    }

    .welcome-section p {
        font-size: 14px;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-row .form-group {
        flex: 1 1 100%;
    }

    .form-group label {
        font-size: 13px;
    }

    .form-group input,
    .form-group select {
        padding: 10px;
        font-size: 14px;
    }

    button[type="submit"],
    .btn {
        width: 100%;
        padding: 12px 15px;
    }

    .form-actions {
        flex-direction: column;
        gap: 10px;
    }
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Small device optimization */
@media screen and (max-width: 480px) {
    .container-form {
        margin-top: 50px;
        padding: 8px;
    }

    .welcome-section h1 {
        font-size: 20px;
    }

    .alert {
        padding: 12px 12px 12px 40px;
        font-size: 13px;
    }

    .alert::before {
        left: 12px;
        font-size: 16px;
    }
}
</style>

<body>
    <?php include '../config/navbar.php'; ?>
    <div class="container-form">
        <div class="welcome-section">
            <h1>Tambah Karyawan Baru</h1>
            <p>Isi formulir berikut untuk menambahkan karyawan baru</p>
        </div>

        <div class="form-container">
            <?php if ($error): ?>
                <div class="alert alert-danger"><?= $error ?></div>
            <?php endif; ?>
            <?php if ($success): ?>
                <div class="alert alert-success"><?= $success ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="form-row">
                    <div class="form-group">
                        <label for="nik">NIK</label>
                        <input type="text" id="nik" name="nik" required placeholder="Masukkan NIK">
                    </div>
                    <div class="form-group">
                        <label for="nama">Nama Lengkap</label>
                        <input type="text" id="nama" name="nama" required placeholder="Masukkan nama lengkap">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="tgl_masuk">Tanggal Masuk</label>
                        <input type="date" id="tgl_masuk" name="tgl_masuk" required>
                    </div>
                    <div class="form-group">
                        <label for="tgl_lahir">Tanggal Lahir</label>
                        <input type="date" id="tgl_lahir" name="tgl_lahir" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="jk">Jenis Kelamin</label>
                        <select id="jk" name="jk" required>
                            <option value="">Pilih jenis kelamin</option>
                            <option value="L">Laki-laki</option>
                            <option value="P">Perempuan</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="agama">Agama</label>
                        <select id="agama" name="agama" required>
                            <option value="">Pilih agama</option>
                            <option value="Islam">Islam</option>
                            <option value="Kristen">Kristen</option>
                            <option value="Katolik">Katolik</option>
                            <option value="Hindu">Hindu</option>
                            <option value="Buddha">Buddha</option>
                            <option value="Konghucu">Konghucu</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="dept">Departemen</label>
                        <select id="dept" name="dept" required onchange="updateBagian()">
                            <option value="">Pilih Departemen</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="bagian">Bagian</label>
                        <select id="bagian" name="bagian" required onchange="updateJabatan()">
                            <option value="">Pilih Sub Dept</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="jabatan">Jabatan</label>
                        <select id="jabatan" name="jabatan" required>
                            <option value="">Pilih Jabatan</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="level_karyawan">Level Karyawan</label>
                        <input type="text" id="level_karyawan" name="level_karyawan" required placeholder="Masukkan level karyawan">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="group">Group</label>
                        <input type="text" id="group" name="group" required placeholder="Masukkan group">
                    </div>
                    <div class="form-group">
                        <label for="pendidikan_akhir">Pendidikan Terakhir</label>
                        <select name="pendidikan_akhir" id="pendidikan_akhir">
                            <option value="">Pilih pendidikan terakhir</option>
                            <option value="SD">SD</option>
                            <option value="SMP">SMP</option>
                            <option value="SMA">SMA</option>
                            <option value="D1">D1</option>
                            <option value="D2">D2</option>
                            <option value="D3">D3</option>
                            <option value="D4">D4</option>
                            <option value="S1">S1</option>
                            <option value="S2">S2</option>
                            <option value="S3">S3</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="no_telp">No. Telepon</label>
                        <input type="text" id="no_telp" name="no_telp" required placeholder="Masukkan nomor telepon">
                    </div>
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status" required>
                            <option value="">Pilih status</option>
                            <?php
                            $status_query = "SELECT DISTINCT status FROM karyawan";
                            $status_result = $conn->query($status_query);
                            while ($status_row = $status_result->fetch_assoc()) {
                                $status_value = $status_row['status'];
                                echo "<option value='" . htmlspecialchars($status_value) . "'>" . htmlspecialchars($status_value) . "</option>";
                            }
                            ?>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="pt">Perusahan/PT</label>
                        <select id="pt" name="pt" required>
                            <option value="">Pilih perusahaan</option>
                            <?php
                            $pt_query = "SELECT DISTINCT pt FROM karyawan";
                            $pt_result = $conn->query($pt_query);
                            while ($pt_row = $pt_result->fetch_assoc()) {
                                $pt_values = $pt_row['pt'];
                                echo "<option value='" . htmlspecialchars($pt_values) . "'>" . htmlspecialchars($pt_values) . "</option>";
                            }
                            ?>
                        </select>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan
                    </button>
                    <a href="employee_management.php" style="text-decoration: none; color: black;" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </form>
        </div>
    </div>
    <?php include '../config/footer.php'; ?>
    <script src="../config/script.js"></script>
    <script>
        
document.addEventListener('DOMContentLoaded', function() {
    const deptSelect = document.getElementById('dept');
    
    // Populate initial departemen options
    departemenOptions.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        deptSelect.appendChild(option);
    });
});

function updateBagian() {
    const deptSelect = document.getElementById('dept');
    const bagianSelect = document.getElementById('bagian');
    const selectedDept = deptSelect.value;

    // Clear existing options
    bagianSelect.innerHTML = '<option value="">Pilih Sub Dept</option>';

    if (Bagian[selectedDept]) {
        Bagian[selectedDept].forEach(bagian => {
            const option = document.createElement('option');
            option.value = bagian;
            option.textContent = bagian;
            bagianSelect.appendChild(option);
        });
    }
    
    // Reset jabatan when department changes
    updateJabatan();
}

function updateJabatan() {
    const bagianSelect = document.getElementById('bagian');
    const jabatanSelect = document.getElementById('jabatan');
    const selectedBagian = bagianSelect.value;

    // Clear existing options
    jabatanSelect.innerHTML = '<option value="">Pilih Jabatan</option>';

    if (Jabatan[selectedBagian]) {
        Jabatan[selectedBagian].forEach(jabatan => {
            const option = document.createElement('option');
            option.value = jabatan;
            option.textContent = jabatan;
            jabatanSelect.appendChild(option);
        });
    }
}
    </script>
</body>
</html>

