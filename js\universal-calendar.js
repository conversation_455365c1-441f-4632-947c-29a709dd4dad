/**
 * Universal Calendar JavaScript untuk semua role
 */

// Calendar variables
let currentDate = new Date();
let currentMonth = currentDate.getMonth();
let currentYear = currentDate.getFullYear();
let userRole = 1; // Will be set from server

// Make variables global for navigation function
window.currentMonth = currentMonth;
window.currentYear = currentYear;

function initializeUniversalCalendar() {
    const monthNames = [
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Mare<PERSON>', 'April', '<PERSON>', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];

    const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

    // Calendar elements
    const calendarTitle = document.getElementById('calendarTitle');
    const calendarGrid = document.getElementById('calendarGrid');
    const prevMonthBtn = document.getElementById('prevMonth');
    const nextMonthBtn = document.getElementById('nextMonth');
    const todayBtn = document.getElementById('todayBtn');

    if (!calendarGrid) {
        console.error('Calendar grid element not found');
        return;
    }

    // Event listeners
    if (prevMonthBtn) {
        prevMonthBtn.addEventListener('click', () => {
            currentMonth--;
            if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            }
            // Update global variables
            window.currentMonth = currentMonth;
            window.currentYear = currentYear;
            renderCalendar();
        });
    }

    if (nextMonthBtn) {
        nextMonthBtn.addEventListener('click', () => {
            currentMonth++;
            if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            }
            // Update global variables
            window.currentMonth = currentMonth;
            window.currentYear = currentYear;
            renderCalendar();
        });
    }

    if (todayBtn) {
        todayBtn.addEventListener('click', () => {
            const today = new Date();
            currentMonth = today.getMonth();
            currentYear = today.getFullYear();
            // Update global variables
            window.currentMonth = currentMonth;
            window.currentYear = currentYear;
            renderCalendar();
        });
    }

    // Modal event listeners
    const modal = document.getElementById('trainingModal');
    const modalClose = document.getElementById('modalClose');

    if (modalClose) {
        modalClose.addEventListener('click', closeModal);
    }

    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
            }
        });
    }

    // Functions
    function renderCalendar() {
        if (calendarTitle) {
            calendarTitle.textContent = `${monthNames[currentMonth]} ${currentYear}`;
        }

        // Clear calendar grid
        calendarGrid.innerHTML = '';

        // Add day headers
        dayNames.forEach(day => {
            const dayHeader = document.createElement('div');
            dayHeader.className = 'calendar-day-header';
            dayHeader.textContent = day;
            calendarGrid.appendChild(dayHeader);
        });

        // Get first day of month and number of days
        const firstDay = new Date(currentYear, currentMonth, 1).getDay();
        const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
        const daysInPrevMonth = new Date(currentYear, currentMonth, 0).getDate();

        // Add empty cells for previous month
        for (let i = firstDay - 1; i >= 0; i--) {
            const dayCell = createDayCell(daysInPrevMonth - i, true);
            calendarGrid.appendChild(dayCell);
        }

        // Add days of current month
        for (let day = 1; day <= daysInMonth; day++) {
            const dayCell = createDayCell(day, false);
            calendarGrid.appendChild(dayCell);
        }

        // Add empty cells for next month
        const totalCells = calendarGrid.children.length - 7; // Subtract day headers
        const remainingCells = 42 - totalCells; // 6 rows * 7 days
        for (let day = 1; day <= remainingCells; day++) {
            const dayCell = createDayCell(day, true);
            calendarGrid.appendChild(dayCell);
        }

        // Load training events
        loadTrainingEvents();
    }

    function createDayCell(day, isOtherMonth) {
        const dayCell = document.createElement('div');
        dayCell.className = 'calendar-day';

        if (isOtherMonth) {
            dayCell.classList.add('other-month');
        }

        // Check if it's today
        const today = new Date();
        if (!isOtherMonth &&
            day === today.getDate() &&
            currentMonth === today.getMonth() &&
            currentYear === today.getFullYear()) {
            dayCell.classList.add('today');
        }

        const dayNumber = document.createElement('div');
        dayNumber.className = 'calendar-day-number';
        dayNumber.textContent = day;
        dayCell.appendChild(dayNumber);

        // Store date for event loading
        let cellDate;
        if (isOtherMonth) {
            if (day > 15) {
                // Previous month
                cellDate = new Date(currentYear, currentMonth - 1, day);
            } else {
                // Next month
                cellDate = new Date(currentYear, currentMonth + 1, day);
            }
        } else {
            cellDate = new Date(currentYear, currentMonth, day);
        }

        // Use local date format to avoid timezone issues (same as calendar_admin.js)
        const year = cellDate.getFullYear();
        const month = String(cellDate.getMonth() + 1).padStart(2, '0');
        const dayOfMonth = String(cellDate.getDate()).padStart(2, '0');
        const dateStr = `${year}-${month}-${dayOfMonth}`;
        dayCell.dataset.date = dateStr;

        return dayCell;
    }

    function loadTrainingEvents() {
        const startDate = new Date(currentYear, currentMonth, 1);
        const endDate = new Date(currentYear, currentMonth + 1, 0);

        // Format dates for API
        const startDateStr = formatDateForAPI(startDate);
        const endDateStr = formatDateForAPI(endDate);

        // Use configured API URL or fallback
        const apiUrl = window.calendarConfig?.eventsApiUrl || 'get_training_events.php';

        fetch(`${apiUrl}?start=${startDateStr}&end=${endDateStr}&role=${userRole}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.events) {
                    displayEvents(data.events);
                } else {
                    console.error('Error loading events:', data.message);
                    if (data.debug) console.error('Debug info:', data.debug);
                    // Show empty calendar instead of breaking
                    displayEvents([]);
                }
            })
            .catch(error => {
                console.error('Error fetching events:', error);
                // Show empty calendar instead of breaking
                displayEvents([]);
            });
    }

    function displayEvents(events) {
        // Clear existing events
        document.querySelectorAll('.calendar-event').forEach(event => event.remove());

        // Safety check
        if (!Array.isArray(events)) {
            console.warn('Events is not an array:', events);
            return;
        }

        // Track unique events for color legend
        const uniqueEvents = new Map();

        events.forEach(event => {
            // Safety check for event object
            if (!event || !event.id || !event.start) {
                console.warn('Invalid event object:', event);
                return;
            }

            // Track unique events for legend
            const eventKey = `${event.id}_${event.type}`;
            if (!uniqueEvents.has(eventKey)) {
                uniqueEvents.set(eventKey, {
                    id: event.id,
                    title: event.title,
                    type: event.type,
                    colors: generateEventColors(event.id, event.title, event.type),
                    is_hidden: event.is_hidden,
                    is_confirmed: event.is_confirmed
                });
            }

            // Handle multi-day events
            if (event.end && event.end !== event.start) {
                displayMultiDayEvent(event);
            } else {
                displaySingleDayEvent(event);
            }
        });

        // Update color legend
        updateColorLegend(Array.from(uniqueEvents.values()));
    }

    function displaySingleDayEvent(event) {
        // Use same simple parsing as multi-day events
        function parseSimpleDate(dateStr) {
            if (!dateStr) return null;
            if (typeof dateStr === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                const [year, month, day] = dateStr.split('-').map(Number);
                return new Date(year, month - 1, day, 12, 0, 0, 0);
            }
            return null;
        }

        const eventDate = parseSimpleDate(event.start);
        if (!eventDate) {
            console.warn('Invalid date for single day event:', event);
            return;
        }

        const dateStr = formatDateForAPI(eventDate);
        const dayCell = document.querySelector(`[data-date="${dateStr}"]`);

        console.log(`Single day event: ${event.title}, Date: ${event.start} -> ${dateStr}, Cell found: ${!!dayCell}`);

        if (dayCell) {
            const eventElement = createEventElement(event, 'single');
            dayCell.appendChild(eventElement);
        } else {
            console.warn(`No calendar cell found for single day event: ${dateStr}`);
        }
    }

    function displayMultiDayEvent(event) {
        console.log(`Multi-day event: ${event.title}`);
        console.log(`Raw start: ${event.start}, Raw end: ${event.end}`);

        // Simple, direct date parsing - avoid complex timezone handling
        function parseSimpleDate(dateStr) {
            if (!dateStr) return null;

            // Handle YYYY-MM-DD format directly
            if (typeof dateStr === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                const [year, month, day] = dateStr.split('-').map(Number);
                return new Date(year, month - 1, day, 12, 0, 0, 0); // Use noon to avoid timezone issues
            }

            return null;
        }

        const startDate = parseSimpleDate(event.start);
        const endDate = parseSimpleDate(event.end);

        if (!startDate || !endDate) {
            console.error('Invalid dates for multi-day event:', event);
            return;
        }

        console.log(`Parsed start: ${formatDateForAPI(startDate)}`);
        console.log(`Parsed end: ${formatDateForAPI(endDate)}`);

        // Calculate total days
        const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
        console.log(`Total days for event: ${totalDays}`);

        // Simple loop through dates
        const currentDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), 12, 0, 0, 0);
        let dayIndex = 0;

        console.log(`Loop will start from: ${formatDateForAPI(currentDate)}`);
        console.log(`Loop will end at: ${formatDateForAPI(endDate)}`);

        while (currentDate <= endDate && dayIndex < 31) {
            const dateStr = formatDateForAPI(currentDate);
            const dayCell = document.querySelector(`[data-date="${dateStr}"]`);

            console.log(`Day ${dayIndex + 1}: ${dateStr} (${currentDate.toLocaleDateString('id-ID')}), Cell found: ${!!dayCell}`);

            if (dayCell) {
                const isFirstDay = dayIndex === 0;
                const isLastDay = formatDateForAPI(currentDate) === formatDateForAPI(endDate);
                const isMiddleDay = !isFirstDay && !isLastDay;

                let eventType = 'single';
                if (totalDays > 1) {
                    if (isFirstDay) eventType = 'multi-start';
                    else if (isLastDay) eventType = 'multi-end';
                    else if (isMiddleDay) eventType = 'multi-middle';
                }

                console.log(`Creating event element: ${eventType} for day ${dayIndex + 1}`);
                const eventElement = createEventElement(event, eventType, dayIndex);
                dayCell.appendChild(eventElement);
            } else {
                console.warn(`No calendar cell found for date: ${dateStr}`);
            }

            // Move to next day
            currentDate.setDate(currentDate.getDate() + 1);
            dayIndex++;
        }
    }



    function createEventElement(event, displayType = 'single', dayIndex = 0) {
        const eventElement = document.createElement('div');
        eventElement.className = `calendar-event ${event.type} ${displayType}`;

        // Add training data attributes
        eventElement.dataset.trainingId = event.id;
        eventElement.dataset.trainingType = event.type;
        eventElement.dataset.displayType = displayType;
        eventElement.dataset.confirmed = event.is_confirmed;

        // Generate unique color for this event (like admin calendar)
        const eventColors = generateEventColors(event.id, event.title, event.type);
        applyEventColors(eventElement, eventColors, displayType);

        // Add hidden class if event is hidden
        if (event.is_hidden) {
            eventElement.classList.add('event-hidden');
        }

        // Create event content based on display type
        let eventText = event.title;
        if (displayType === 'multi-start') {
            eventText = `📅 ${event.title}`;
        } else if (displayType === 'multi-middle') {
            eventText = `↔️ ${event.title}`;
        } else if (displayType === 'multi-end') {
            eventText = `🏁 ${event.title}`;
        }

        eventElement.textContent = eventText;

        // Create tooltip text
        let tooltipText = `${event.title}\n${event.time || 'Waktu belum ditentukan'}\n${event.location || 'Lokasi belum ditentukan'}`;

        if (event.trainer) {
            tooltipText += `\n👤 ${event.trainer}`;
        }

        // Add multi-day info to tooltip
        if (event.end && event.end !== event.start) {
            const startDate = new Date(event.start).toLocaleDateString('id-ID');
            const endDate = new Date(event.end).toLocaleDateString('id-ID');
            tooltipText += `\n📅 Multi-hari: ${startDate} - ${endDate}`;

            if (displayType === 'multi-start') {
                tooltipText += '\n🚀 Hari pertama';
            } else if (displayType === 'multi-middle') {
                tooltipText += `\n📍 Hari ke-${dayIndex + 1}`;
            } else if (displayType === 'multi-end') {
                tooltipText += '\n🏁 Hari terakhir';
            }
        }

        if (event.is_confirmed === 0) {
            tooltipText += '\n⚠️ Tanggal belum dikonfirmasi';
        }

        if (event.is_hidden) {
            tooltipText += '\n🔒 Training ini disembunyikan dari tampilan publik';
        }

        eventElement.title = tooltipText;

        // Add click event
        eventElement.addEventListener('click', (e) => {
            e.stopPropagation();
            showTrainingDetail(event.id, event.type);
        });

        return eventElement;
    }

    // Color generation functions for unique event colors (from admin calendar)
    function generateEventColors(eventId, eventTitle, eventType) {
        // Create a unique seed from event ID and title
        const seed = hashString(eventId + eventTitle);

      // Define more colorful and vibrant color palettes for different types
const colorPalettes = {
    online: [
        { primary: '#ff6f61', secondary: '#ffab91', accent: '#d84315' }, // Coral
        { primary: '#42a5f5', secondary: '#90caf9', accent: '#1565c0' }, // Sky blue
        { primary: '#ab47bc', secondary: '#ce93d8', accent: '#6a1b9a' }, // Orchid
        { primary: '#26c6da', secondary: '#80deea', accent: '#00838f' }, // Cyan
        { primary: '#ec407a', secondary: '#f48fb1', accent: '#ad1457' }, // Pink
        { primary: '#66bb6a', secondary: '#a5d6a7', accent: '#2e7d32' }, // Spring green
        { primary: '#ffee58', secondary: '#fff59d', accent: '#f9a825' }, // Yellow
        { primary: '#ffa726', secondary: '#ffcc80', accent: '#ef6c00' }  // Orange
    ],
    offline: [
        { primary: '#ef5350', secondary: '#ef9a9a', accent: '#c62828' }, // Red
        { primary: '#7e57c2', secondary: '#b39ddb', accent: '#512da8' }, // Purple
        { primary: '#26a69a', secondary: '#80cbc4', accent: '#004d40' }, // Teal
        { primary: '#ff7043', secondary: '#ffab91', accent: '#d84315' }, // Deep orange
        { primary: '#8d6e63', secondary: '#bcaaa4', accent: '#4e342e' }, // Brown
        { primary: '#78909c', secondary: '#b0bec5', accent: '#37474f' }, // Blue grey
        { primary: '#9ccc65', secondary: '#dce775', accent: '#558b2f' }, // Lime green
        { primary: '#f06292', secondary: '#f8bbd0', accent: '#c2185b' }  // Rose pink
    ]
};


        // Select palette based on event type
        const palette = colorPalettes[eventType] || colorPalettes.offline;
        const colorIndex = seed % palette.length;
        const selectedColors = palette[colorIndex];

        // Generate variations for multi-day events
        return {
            primary: selectedColors.primary,
            secondary: selectedColors.secondary,
            accent: selectedColors.accent,
            light: lightenColor(selectedColors.primary, 20),
            dark: darkenColor(selectedColors.primary, 20),
            gradient: {
                start: selectedColors.primary,
                middle: selectedColors.secondary,
                end: selectedColors.accent
            }
        };
    }

    function hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }

    function lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    function darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
            (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
            (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }

    function applyEventColors(element, colors, displayType) {
        // Apply colors based on display type
        if (displayType === 'single') {
            element.style.background = `linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 100%)`;
            element.style.borderLeft = `3px solid ${colors.accent}`;
        } else if (displayType === 'multi-start') {
            element.style.background = `linear-gradient(90deg, ${colors.gradient.start} 0%, ${colors.gradient.middle} 100%)`;
            element.style.borderLeft = `3px solid ${colors.accent}`;
        } else if (displayType === 'multi-middle') {
            element.style.background = `linear-gradient(90deg, ${colors.gradient.middle} 0%, ${colors.gradient.middle} 100%)`;
        } else if (displayType === 'multi-end') {
            element.style.background = `linear-gradient(90deg, ${colors.gradient.middle} 0%, ${colors.gradient.end} 100%)`;
            element.style.borderRight = `3px solid ${colors.accent}`;
        }

        // Set text color based on background brightness
        const textColor = getContrastColor(colors.primary);
        element.style.color = textColor;

        // Add subtle shadow for depth
        element.style.boxShadow = `0 2px 4px ${colors.primary}33`;

        // Store colors in dataset for later use
        element.dataset.primaryColor = colors.primary;
        element.dataset.secondaryColor = colors.secondary;
        element.dataset.accentColor = colors.accent;
    }

    function getContrastColor(hexColor) {
        // Convert hex to RGB
        const r = parseInt(hexColor.slice(1, 3), 16);
        const g = parseInt(hexColor.slice(3, 5), 16);
        const b = parseInt(hexColor.slice(5, 7), 16);

        // Calculate luminance
        const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

        // Return black or white based on luminance
        return luminance > 0.5 ? '#000000' : '#ffffff';
    }

    function formatDateForAPI(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    function updateColorLegend(uniqueEvents) {
        // Find or create color legend container
        let legendContainer = document.getElementById('trainingColorLegend');
        if (!legendContainer) {
            // Create legend container if it doesn't exist
            const calendarContainer = document.querySelector('.calendar-container');
            if (calendarContainer) {
                legendContainer = document.createElement('div');
                legendContainer.id = 'trainingColorLegend';
                legendContainer.className = 'training-color-legend';

                // Insert after calendar container
                calendarContainer.parentNode.insertBefore(legendContainer, calendarContainer.nextSibling);
            }
        }

        if (!legendContainer || uniqueEvents.length === 0) {
            if (legendContainer) {
                legendContainer.style.display = 'none';
            }
            return;
        }

        // Sort events by type and title
        uniqueEvents.sort((a, b) => {
            if (a.type !== b.type) {
                return a.type === 'online' ? -1 : 1;
            }
            return a.title.localeCompare(b.title);
        });

        // Generate legend HTML
        let legendHTML = `
            <div class="legend-header">
                <h6><i class="fas fa-palette"></i> Event Colors</h6>
            </div>
        `;

        // Group by type
        const onlineEvents = uniqueEvents.filter(e => e.type === 'online');
        const offlineEvents = uniqueEvents.filter(e => e.type === 'offline');

        if (onlineEvents.length > 0) {
            legendHTML += `
                <div class="legend-section">
                    <div class="legend-type-header">
                        <i class="fas fa-laptop"></i> Eksternal Training
                    </div>
                    <div class="legend-items">
            `;

            onlineEvents.forEach(event => {
                const statusIcons = [];
                if (event.is_hidden) statusIcons.push('🔒');
                if (!event.is_confirmed) statusIcons.push('⚠️');
                const statusText = statusIcons.length > 0 ? ` ${statusIcons.join(' ')}` : '';

                legendHTML += `
                    <div class="legend-item" data-event-id="${event.id}" data-event-type="${event.type}">
                        <div class="legend-color" style="background: linear-gradient(135deg, ${event.colors.primary} 0%, ${event.colors.secondary} 100%); border-left: 3px solid ${event.colors.accent};"></div>
                        <span class="legend-text" title="${event.title}${statusText}">${event.title}${statusText}</span>
                    </div>
                `;
            });

            legendHTML += `
                    </div>
                </div>
            `;
        }

        if (offlineEvents.length > 0) {
            legendHTML += `
                <div class="legend-section">
                    <div class="legend-type-header">
                        <i class="fas fa-building"></i> Internal Training
                    </div>
                    <div class="legend-items">
            `;

            offlineEvents.forEach(event => {
                const statusIcons = [];
                if (event.is_hidden) statusIcons.push('🔒');
                if (!event.is_confirmed) statusIcons.push('⚠️');
                const statusText = statusIcons.length > 0 ? ` ${statusIcons.join(' ')}` : '';

                legendHTML += `
                    <div class="legend-item" data-event-id="${event.id}" data-event-type="${event.type}">
                        <div class="legend-color" style="background: linear-gradient(135deg, ${event.colors.primary} 0%, ${event.colors.secondary} 100%); border-left: 3px solid ${event.colors.accent};"></div>
                        <span class="legend-text" title="${event.title}${statusText}">${event.title}${statusText}</span>
                    </div>
                `;
            });

            legendHTML += `
                    </div>
                </div>
            `;
        }

        legendContainer.innerHTML = legendHTML;
        legendContainer.style.display = 'block';

        // Add click events to legend items
        legendContainer.querySelectorAll('.legend-item').forEach(item => {
            item.addEventListener('click', () => {
                const eventId = item.dataset.eventId;
                const eventType = item.dataset.eventType;
                showTrainingDetail(eventId, eventType);
            });
        });
    }

    function showTrainingDetail(eventId, eventType) {
        const modal = document.getElementById('trainingModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');

        if (!modal || !modalTitle || !modalBody) {
            console.error('Modal elements not found');
            return;
        }

        modalTitle.textContent = 'Loading...';
        modalBody.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
        modal.style.display = 'block';

        // Use configured API URL or fallback
        const apiUrl = window.calendarConfig?.detailApiUrl || 'get_training_detail.php';

        fetch(`${apiUrl}?id=${eventId}&type=${eventType}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.training) {
                    displayTrainingDetail(data.training);
                } else {
                    const errorMsg = data.message || 'Training data not found';
                    modalBody.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: red;">
                            <i class="fas fa-exclamation-triangle"></i><br>
                            Error: ${errorMsg}
                        </div>`;
                    console.error('API Error:', data.message);
                    if (data.debug) console.error('Debug info:', data.debug);
                }
            })
            .catch(error => {
                console.error('Error fetching training detail:', error);
                modalBody.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: red;">
                        <i class="fas fa-exclamation-triangle"></i><br>
                        Error loading training detail: ${error.message}
                    </div>`;
            });
    }

    function displayTrainingDetail(training) {
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');

        modalTitle.textContent = training.title;

        // Format date display for multi-day events
        let dateDisplay = training.date || training.formatted_date;
        let durationInfo = '';
        let isMultiDay = false;

        if (training.end_date && training.end_date !== training.raw_date) {
            const startDate = new Date(training.raw_date).toLocaleDateString('id-ID');
            const endDate = new Date(training.end_date).toLocaleDateString('id-ID');
            dateDisplay = `${startDate} s/d ${endDate}`;
            isMultiDay = true;

            // Calculate duration
            const start = new Date(training.raw_date);
            const end = new Date(training.end_date);
            const diffTime = Math.abs(end - start);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
            durationInfo = `<tr><td><strong>Durasi:</strong></td><td><span class="badge bg-info">${diffDays} hari</span></td></tr>`;
        }

        // Check if training has poster
        let posterSection = '';
        if (training.poster_image) {
            posterSection = `
                <div class="col-md-4 mb-3">
                    <div class="training-poster text-center">
                        <h6><i class="fas fa-image text-info"></i> Poster Training</h6>
                        <div class="poster-container">
                            <img src="../${training.poster_image}"
                                 alt="Poster ${training.title}"
                                 class="img-fluid training-poster-img"
                                 style="max-height: 300px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); cursor: pointer;"
                                 onclick="showPosterModal(this.src, '${training.title}')">
                        </div>
                    </div>
                </div>
            `;
        }

        let html = `
            <div class="row mb-4">
                <div class="${training.poster_image ? 'col-md-8' : 'col-md-8'}">
                    <h6><i class="fas fa-info-circle text-primary"></i> Informasi Training</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Tanggal:</strong></td><td>${dateDisplay}</td></tr>
                        ${durationInfo}
                        <tr><td><strong>Waktu:</strong></td><td>${training.time || 'Belum ditentukan'}</td></tr>
                        <tr><td><strong>Lokasi:</strong></td><td>${training.location || 'Belum ditentukan'}</td></tr>
                        <tr><td><strong>Trainer:</strong></td><td>${training.trainer || 'Belum ditentukan'}</td></tr>
                        ${training.max_participants ? `<tr><td><strong>Max Peserta:</strong></td><td>${training.max_participants}</td></tr>` : ''}
                        <tr><td><strong>Status:</strong></td><td><span class="badge bg-${getStatusColor(training.status)}">${training.status}</span></td></tr>
                        <tr><td><strong>Konfirmasi:</strong></td><td><span class="badge bg-${training.is_confirmed ? 'success' : 'warning'}">${training.is_confirmed ? 'Dikonfirmasi' : 'Belum Dikonfirmasi'}</span></td></tr>
                    </table>
                </div>
                ${posterSection}
                ${!training.poster_image ? `
                <div class="col-md-4">
                    <div class="training-type-badge text-center">
                        <div class="badge bg-${training.type === 'offline' ? 'success' : 'info'} p-3 w-100">
                            <i class="fas fa-${training.type === 'offline' ? 'building' : 'laptop'} fa-2x d-block mb-2"></i>
                            <h6 class="mb-0">Training ${training.type === 'offline' ? 'Internal' : 'Eksternal'}</h6>
                        </div>
                    </div>
                </div>
                ` : ''}
            </div>
        `;

        if (training.description) {
            html += `<div class="mb-3"><h6><i class="fas fa-file-alt text-info"></i> Deskripsi</h6><p class="text-muted">${training.description}</p></div>`;
        }

        // Multi-day training info
        if (isMultiDay) {
            html += `
                <div class="alert alert-info mb-3">
                    <h6><i class="fas fa-calendar-alt"></i> Training Multi-Hari</h6>
                    <p class="mb-0">Training ini berlangsung selama beberapa hari dari ${dateDisplay}</p>
                </div>
            `;
        }

        // Add registration section for users
        if (window.userRole && window.userRole > 0) {
            html += `
                <div class="mb-3">
                    <h6><i class="fas fa-user-plus text-success"></i> Pendaftaran Training</h6>
                    <div class="registration-buttons">
                        <button class="btn btn-primary btn-sm me-2" onclick="registerSelf(${training.id}, '${training.type === 'offline' ? 'internal' : 'external'}')">
                            <i class="fas fa-user-check"></i> Daftarkan Diri Sendiri
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="showEmployeeRegistration(${training.id}, '${training.type === 'offline' ? 'internal' : 'external'}')">
                            <i class="fas fa-users"></i> Daftarkan Karyawan
                        </button>
                    </div>
                    <small class="text-muted d-block mt-2">
                        <i class="fas fa-info-circle"></i> Pendaftaran memerlukan konfirmasi admin
                    </small>
                </div>
            `;
        }

        if (training.participants && training.participants.length > 0) {
            html += `<div class="mb-3"><h6><i class="fas fa-users text-warning"></i> Peserta Training (${training.participants.length})</h6>`;

            if (training.type === 'offline' && isMultiDay) {
                // For multi-day offline training, show attendance summary if available
                html += `<div class="table-responsive" style="max-height: 300px; overflow-y: auto;"><table class="table table-sm table-striped">`;
                html += `<thead class="table-dark"><tr><th>Nama</th><th>NIK</th><th>Departemen</th><th>Jabatan</th><th>Kehadiran</th><th>Status</th></tr></thead><tbody>`;

                training.participants.forEach(participant => {
                    // Show attendance summary for multi-day training
                    const attendanceSummary = participant.attendance_summary || 'Belum ada data';
                    const statusColor = getParticipantStatusColor(participant.status || 'active');

                    html += `<tr>
                        <td>${participant.name}</td>
                        <td>${participant.nik}</td>
                        <td>${participant.dept}</td>
                        <td>${participant.jabatan}</td>
                        <td><span class="badge bg-info">${attendanceSummary}</span></td>
                        <td><span class="badge bg-${statusColor}">${participant.status || 'Active'}</span></td>
                    </tr>`;
                });
            } else {
                // For single day or online training
                html += `<div class="table-responsive" style="max-height: 300px; overflow-y: auto;"><table class="table table-sm table-striped">`;
                html += `<thead class="table-dark"><tr><th>Nama</th><th>NIK</th><th>Departemen</th><th>Jabatan</th><th>Status</th></tr></thead><tbody>`;

                training.participants.forEach(participant => {
                    const statusColor = getParticipantStatusColor(participant.status || 'active');
                    html += `<tr>
                        <td>${participant.name}</td>
                        <td>${participant.nik}</td>
                        <td>${participant.dept}</td>
                        <td>${participant.jabatan}</td>
                        <td><span class="badge bg-${statusColor}">${participant.status || 'Active'}</span></td>
                    </tr>`;
                });
            }

            html += `</tbody></table></div></div>`;
        }

        modalBody.innerHTML = html;
    }

    function getStatusColor(status) {
        switch(status?.toLowerCase()) {
            case 'active': return 'success';
            case 'completed': return 'primary';
            case 'cancelled': return 'danger';
            case 'approved': return 'success';
            default: return 'secondary';
        }
    }

    function getParticipantStatusColor(status) {
        switch(status?.toLowerCase()) {
            case 'active': return 'success';
            case 'hadir': return 'success';
            case 'tidak hadir': return 'danger';
            case 'completed': return 'primary';
            default: return 'secondary';
        }
    }

    function closeModal() {
        const modal = document.getElementById('trainingModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // Function to show poster in larger modal
    function showPosterModal(imageSrc, title) {
        // Create poster modal if it doesn't exist
        let posterModal = document.getElementById('posterModal');
        if (!posterModal) {
            posterModal = document.createElement('div');
            posterModal.id = 'posterModal';
            posterModal.className = 'modal';
            posterModal.innerHTML = `
                <div class="modal-content" style="max-width: 90%; width: auto; text-align: center;">
                    <div class="modal-header">
                        <h3 class="modal-title" id="posterModalTitle">Poster Training</h3>
                        <button class="modal-close" onclick="closePosterModal()">&times;</button>
                    </div>
                    <div class="modal-body" style="padding: 20px;">
                        <img id="posterModalImage" src="" alt="" style="max-width: 100%; max-height: 70vh; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.2);">
                    </div>
                </div>
            `;
            document.body.appendChild(posterModal);
        }

        // Set image and title
        document.getElementById('posterModalTitle').textContent = `Poster: ${title}`;
        document.getElementById('posterModalImage').src = imageSrc;
        document.getElementById('posterModalImage').alt = `Poster ${title}`;

        // Show modal
        posterModal.style.display = 'block';

        // Close modal when clicking outside
        posterModal.onclick = function(e) {
            if (e.target === posterModal) {
                closePosterModal();
            }
        };
    }

    function closePosterModal() {
        const posterModal = document.getElementById('posterModal');
        if (posterModal) {
            posterModal.style.display = 'none';
        }
    }

    // Registration functions
    function registerSelf(trainingId, trainingType) {
        if (!confirmAction('Apakah Anda yakin ingin mendaftar untuk training ini?')) {
            return;
        }

        const data = {
            action: 'register_self',
            training_id: trainingId,
            training_type: trainingType
        };

        fetch('training_registration_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                closeModal();
            } else {
                alert('❌ ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('❌ Terjadi kesalahan saat mendaftar');
        });
    }

    function showEmployeeRegistration(trainingId, trainingType) {
        // Create employee registration modal
        let empModal = document.getElementById('employeeRegistrationModal');
        if (!empModal) {
            empModal = document.createElement('div');
            empModal.id = 'employeeRegistrationModal';
            empModal.className = 'modal';
            empModal.innerHTML = `
                <div class="modal-content" style="max-width: 800px;">
                    <div class="modal-header">
                        <h3 class="modal-title">Daftarkan Karyawan</h3>
                        <button class="modal-close" onclick="closeEmployeeRegistrationModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Cari Karyawan (Departemen Anda)</label>
                            <input type="text" id="empSearchInput" class="form-control" placeholder="Ketik nama atau NIK karyawan...">
                        </div>
                        <div id="empSearchResults" style="max-height: 300px; overflow-y: auto;">
                            <p class="text-muted">Ketik untuk mencari karyawan...</p>
                        </div>
                        <div class="mt-3">
                            <h6>Karyawan Terpilih:</h6>
                            <div id="selectedEmployees"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeEmployeeRegistrationModal()">Batal</button>
                        <button class="btn btn-primary" onclick="registerSelectedEmployees(${trainingId}, '${trainingType}')">
                            <i class="fas fa-user-plus"></i> Daftarkan
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(empModal);
        }

        // Reset modal
        document.getElementById('empSearchInput').value = '';
        document.getElementById('empSearchResults').innerHTML = '<p class="text-muted">Ketik untuk mencari karyawan...</p>';
        document.getElementById('selectedEmployees').innerHTML = '';
        window.selectedEmployeeIds = [];

        // Show modal
        empModal.style.display = 'block';

        // Add search functionality
        const searchInput = document.getElementById('empSearchInput');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length < 2) {
                document.getElementById('empSearchResults').innerHTML = '<p class="text-muted">Ketik minimal 2 karakter...</p>';
                return;
            }

            searchTimeout = setTimeout(() => {
                searchEmployeesForRegistration(query);
            }, 500);
        });
    }

    function searchEmployeesForRegistration(query) {
        const resultsDiv = document.getElementById('empSearchResults');
        resultsDiv.innerHTML = '<p class="text-muted"><i class="fas fa-spinner fa-spin"></i> Mencari...</p>';

        fetch(`../admin/search_employees.php?search=${encodeURIComponent(query)}&limit=20`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.employees.length > 0) {
                    let html = '<div class="list-group">';
                    data.employees.forEach(emp => {
                        const isSelected = window.selectedEmployeeIds.includes(emp.id);
                        html += `
                            <div class="list-group-item ${isSelected ? 'active' : ''}"
                                 onclick="toggleEmployeeSelection(${emp.id}, '${emp.nik}', '${emp.nama}', this)">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>${emp.nama}</strong><br>
                                        <small class="text-muted">NIK: ${emp.nik} | ${emp.dept} - ${emp.jabatan}</small>
                                    </div>
                                    <i class="fas fa-${isSelected ? 'check-circle text-success' : 'plus-circle'}"></i>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = '<p class="text-muted">Tidak ada karyawan ditemukan</p>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultsDiv.innerHTML = '<p class="text-danger">Error mencari karyawan</p>';
            });
    }

    function toggleEmployeeSelection(empId, empNik, empNama, element) {
        if (!window.selectedEmployeeIds) {
            window.selectedEmployeeIds = [];
        }

        const index = window.selectedEmployeeIds.indexOf(empId);
        if (index > -1) {
            // Remove from selection
            window.selectedEmployeeIds.splice(index, 1);
            element.classList.remove('active');
            element.querySelector('i').className = 'fas fa-plus-circle';
        } else {
            // Add to selection
            window.selectedEmployeeIds.push(empId);
            element.classList.add('active');
            element.querySelector('i').className = 'fas fa-check-circle text-success';
        }

        updateSelectedEmployeesDisplay();
    }

    function updateSelectedEmployeesDisplay() {
        const selectedDiv = document.getElementById('selectedEmployees');
        if (window.selectedEmployeeIds.length === 0) {
            selectedDiv.innerHTML = '<p class="text-muted">Belum ada karyawan dipilih</p>';
        } else {
            selectedDiv.innerHTML = `<span class="badge bg-primary">${window.selectedEmployeeIds.length} karyawan dipilih</span>`;
        }
    }

    function registerSelectedEmployees(trainingId, trainingType) {
        if (!window.selectedEmployeeIds || window.selectedEmployeeIds.length === 0) {
            alert('Pilih minimal satu karyawan');
            return;
        }

        if (!confirm(`Daftarkan ${window.selectedEmployeeIds.length} karyawan untuk training ini?`)) {
            return;
        }

        const data = {
            action: 'register_employee',
            training_id: trainingId,
            training_type: trainingType,
            employee_ids: window.selectedEmployeeIds
        };

        fetch('training_registration_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                closeEmployeeRegistrationModal();
                closeModal();
            } else {
                alert('❌ ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('❌ Terjadi kesalahan saat mendaftarkan karyawan');
        });
    }

    function closeEmployeeRegistrationModal() {
        const modal = document.getElementById('employeeRegistrationModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // Make functions available globally
    window.renderCalendar = renderCalendar;
    window.showTrainingDetail = showTrainingDetail;
    window.displayTrainingDetail = displayTrainingDetail;
    window.closeModal = closeModal;
    window.showPosterModal = showPosterModal;
    window.closePosterModal = closePosterModal;
    window.registerSelf = registerSelf;
    window.showEmployeeRegistration = showEmployeeRegistration;
    window.toggleEmployeeSelection = toggleEmployeeSelection;
    window.registerSelectedEmployees = registerSelectedEmployees;
    window.closeEmployeeRegistrationModal = closeEmployeeRegistrationModal;

    // Listen for custom navigation events
    document.addEventListener('calendarNavigate', (e) => {
        currentMonth = e.detail.month;
        currentYear = e.detail.year;
        window.currentMonth = currentMonth;
        window.currentYear = currentYear;
        renderCalendar();
    });

    // Initialize calendar
    renderCalendar();
}

// Make function available globally
window.initializeUniversalCalendar = initializeUniversalCalendar;
