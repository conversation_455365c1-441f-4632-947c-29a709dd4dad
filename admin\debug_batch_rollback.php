<?php
/**
 * Debug Batch Rollback
 * This file helps debug why rollback is not available
 */

session_start();
require_once '../config/config.php';
require_once 'record_batch_employee_history.php';

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo 'Unauthorized access';
    exit();
}

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Debug Batch Rollback</h2>";
    
    // Get all batch history
    $stmt = $pdo->prepare("SELECT * FROM karyawan_batch_history ORDER BY change_timestamp DESC LIMIT 10");
    $stmt->execute();
    $batches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Recent Batch History (Last 10):</h3>";
    
    foreach ($batches as $batch) {
        echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
        echo "<h4>Batch ID: " . $batch['batch_id'] . "</h4>";
        echo "<p><strong>Action Type:</strong> " . $batch['action_type'] . "</p>";
        echo "<p><strong>Timestamp:</strong> " . $batch['change_timestamp'] . "</p>";
        
        $batch_data = json_decode($batch['batch_data'], true);
        echo "<p><strong>Batch Data:</strong></p>";
        echo "<pre>" . print_r($batch_data, true) . "</pre>";
        
        // Test canRollback function
        $can_rollback = canRollback($batch_data, $batch['action_type']);
        $is_rolled_back = strpos($batch['action_type'], '_ROLLED_BACK') !== false;
        
        echo "<p><strong>Rollback Analysis:</strong></p>";
        echo "<ul>";
        echo "<li>Action Type: " . $batch['action_type'] . "</li>";
        echo "<li>Is Rolled Back: " . ($is_rolled_back ? 'Yes' : 'No') . "</li>";
        echo "<li>Can Rollback: " . ($can_rollback ? 'Yes' : 'No') . "</li>";
        
        // Detailed analysis
        $rollback_supported_actions = ['BATCH_INSERT', 'BATCH_UPDATE', 'BATCH_DELETE'];
        echo "<li>Action Type Supported: " . (in_array($batch['action_type'], $rollback_supported_actions) ? 'Yes' : 'No') . "</li>";
        
        if ($batch['action_type'] === 'BATCH_INSERT') {
            echo "<li>BATCH_INSERT should be rollback capable</li>";
        } elseif ($batch['action_type'] === 'BATCH_UPDATE' || $batch['action_type'] === 'BATCH_DELETE') {
            $has_rollback_capable = isset($batch_data['rollback_capable']) && $batch_data['rollback_capable'] === true;
            echo "<li>Has rollback_capable flag: " . ($has_rollback_capable ? 'Yes' : 'No') . "</li>";
        }
        
        echo "</ul>";
        
        // Show NIKs if available
        if (isset($batch_data['niks']) && is_array($batch_data['niks'])) {
            echo "<p><strong>NIKs (" . count($batch_data['niks']) . "):</strong> " . implode(', ', array_slice($batch_data['niks'], 0, 10));
            if (count($batch_data['niks']) > 10) {
                echo " ... and " . (count($batch_data['niks']) - 10) . " more";
            }
            echo "</p>";
        }
        
        echo "</div>";
    }
    
    // Check database schema
    echo "<h3>Database Schema Check:</h3>";
    $stmt = $pdo->prepare("DESCRIBE karyawan_batch_history");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test creating a sample batch for rollback
    echo "<h3>Test Rollback Function:</h3>";
    
    $test_batch_data = [
        'action_type' => 'BATCH_INSERT',
        'mode' => 'add',
        'total_success' => 5,
        'total_error' => 0,
        'total_skipped' => 0,
        'niks' => ['TEST001', 'TEST002', 'TEST003'],
        'timestamp' => date('Y-m-d H:i:s'),
        'user_id' => $_SESSION['user_id']
    ];
    
    echo "<p><strong>Test Data:</strong></p>";
    echo "<pre>" . print_r($test_batch_data, true) . "</pre>";
    
    $test_can_rollback = canRollback($test_batch_data, 'BATCH_INSERT');
    echo "<p><strong>Test Result:</strong> " . ($test_can_rollback ? 'Can Rollback' : 'Cannot Rollback') . "</p>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
