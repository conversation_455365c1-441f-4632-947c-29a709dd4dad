<?php
session_start();
include '../config/config.php';
include '../config/activity_logger.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi variabel
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 50;
$offset = ($page - 1) * $limit;
$category = isset($_GET['category']) ? $_GET['category'] : 'all';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Siapkan filter
$filters = [
    'category' => $category,
    'date_from' => $date_from,
    'date_to' => $date_to,
    'search' => $search
];

// Ambil log aktivitas
$logs = get_activity_logs($filters, $limit, $offset);
$total_logs = get_activity_logs_count($filters);
$total_pages = ceil($total_logs / $limit);

// Ambil kategori log
$categories = get_activity_log_categories();

// Handle aksi hapus log
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'clear_logs') {
        // Siapkan filter untuk hapus log
        $clear_filters = [];

        if (isset($_POST['clear_category']) && $_POST['clear_category'] !== 'all') {
            $clear_filters['category'] = $_POST['clear_category'];
        }

        if (isset($_POST['clear_date_from']) && !empty($_POST['clear_date_from'])) {
            $clear_filters['date_from'] = $_POST['clear_date_from'];
        }

        if (isset($_POST['clear_date_to']) && !empty($_POST['clear_date_to'])) {
            $clear_filters['date_to'] = $_POST['clear_date_to'];
        }

        // Hapus log
        $result = clear_activity_logs($clear_filters);

        if ($result) {
            $_SESSION['success'] = "Log aktivitas berhasil dihapus.";

            // Catat aktivitas penghapusan log
            log_activity($_SESSION['user_id'], "Menghapus log aktivitas", "settings", [
                'filters' => $clear_filters,
                'count' => $total_logs
            ]);
        } else {
            $_SESSION['error'] = "Gagal menghapus log aktivitas.";
        }

        // Redirect untuk refresh halaman
        header("Location: activity_logs.php");
        exit();
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .logs-container {
        max-width: 1200px;
        margin: 30px auto;
        padding: 20px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }

    .logs-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .logs-header h1 {
        color: #333;
        margin: 0;
        font-size: 24px;
    }

    .filter-container {
        background-color: #f9f9f9;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 15px;
    }

    .filter-group {
        flex: 1;
        min-width: 200px;
    }

    .filter-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #555;
    }

    .filter-group select,
    .filter-group input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .filter-buttons {
        display: flex;
        gap: 10px;
    }

    .btn {
        padding: 8px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
        transition: background-color 0.3s;
    }

    .btn-primary {
        background-color: #BF0000;
        color: white;
    }

    .btn-primary:hover {
        background-color: #990000;
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
    }

    .btn-danger {
        background-color: #dc3545;
        color: white;
    }

    .btn-danger:hover {
        background-color: #c82333;
    }

    .logs-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .logs-table th,
    .logs-table td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    .logs-table th {
        background-color: #f5f5f5;
        font-weight: 600;
        color: #333;
    }

    .logs-table tr:hover {
        background-color: #f9f9f9;
    }

    .timestamp {
        color: #6c757d;
        font-size: 0.9em;
    }

    .user-info {
        display: flex;
        align-items: center;
    }

    .user-info .nik {
        color: #6c757d;
        font-size: 0.9em;
        margin-left: 5px;
    }

    .category-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8em;
        font-weight: 600;
        text-transform: uppercase;
    }

    .category-settings {
        background-color: #e3f2fd;
        color: #0d47a1;
    }

    .category-training {
        background-color: #e8f5e9;
        color: #1b5e20;
    }

    .category-employee {
        background-color: #fff3e0;
        color: #e65100;
    }

    .category-user {
        background-color: #f3e5f5;
        color: #6a1b9a;
    }

    .category-login {
        background-color: #e8eaf6;
        color: #283593;
    }

    .category-other {
        background-color: #f5f5f5;
        color: #424242;
    }

    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }

    .pagination a,
    .pagination span {
        display: inline-block;
        padding: 8px 12px;
        margin: 0 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        text-decoration: none;
        color: #333;
    }

    .pagination a:hover {
        background-color: #f5f5f5;
    }

    .pagination .active {
        background-color: #BF0000;
        color: white;
        border-color: #BF0000;
    }

    .pagination .disabled {
        color: #aaa;
        cursor: not-allowed;
    }

    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
        background-color: #fff;
        margin: 10% auto;
        padding: 20px;
        border-radius: 8px;
        width: 80%;
        max-width: 500px;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
    }

    .close {
        color: #aaa;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }

    .close:hover {
        color: #333;
    }

    .modal-body {
        margin-bottom: 20px;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 4px;
    }

    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .no-logs {
        text-align: center;
        padding: 30px;
        color: #6c757d;
        font-style: italic;
    }

    .back-btn {
        display: inline-flex;
        align-items: center;
        padding: 8px 15px;
        background-color: #6c757d;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        margin-top: 20px;
    }

    .back-btn i {
        margin-right: 5px;
    }

    .back-btn:hover {
        background-color: #5a6268;
    }

    .details-btn {
        background: none;
        border: none;
        color: #BF0000;
        cursor: pointer;
        text-decoration: underline;
        padding: 0;
    }

    .details-btn:hover {
        color: #990000;
    }

    .details-content {
        background-color: #f9f9f9;
        padding: 10px;
        border-radius: 4px;
        margin-top: 10px;
        font-family: monospace;
        white-space: pre-wrap;
        max-height: 300px;
        overflow-y: auto;
    }

    /* Loading Indicator */
    #loading-indicator {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    #loading-indicator .spinner {
        font-size: 3rem;
        color: #BF0000;
        margin-bottom: 15px;
    }

    #loading-indicator p {
        font-size: 1.2rem;
        color: #333;
    }

    /* Auto-refresh button */
    #auto-refresh-btn {
        background-color: #17a2b8;
        color: white;
    }

    #auto-refresh-btn.active {
        background-color: #138496;
    }

    /* Responsive styles */
    @media (max-width: 992px) {
        .logs-container {
            padding: 15px;
            margin: 15px auto;
        }

        .filter-row {
            flex-direction: column;
            gap: 10px;
        }

        .filter-group {
            width: 100%;
        }

        .logs-table th,
        .logs-table td {
            padding: 8px 10px;
        }

        .logs-header {
            flex-direction: column;
            gap: 15px;
        }

        .logs-header h1 {
            margin-bottom: 10px;
        }
    }

    @media (max-width: 768px) {
        .logs-table {
            display: block;
            overflow-x: auto;
            white-space: nowrap;
        }

        .pagination a,
        .pagination span {
            padding: 6px 10px;
            margin: 0 2px;
        }

        .filter-buttons {
            flex-wrap: wrap;
            gap: 5px;
        }

        .filter-buttons .btn {
            flex: 1;
            min-width: 120px;
            margin-bottom: 5px;
        }
    }
</style>

<body>
    <?php include '../config/navbarb.php'; ?>
<div class="jarak"></div>
    <div class="logs-container">
        <div class="logs-header">
            <h1><i class="fas fa-history"></i> Log Aktivitas Sistem</h1>
            <button type="button" class="btn btn-danger" onclick="openClearModal()">
                <i class="fas fa-trash-alt"></i> Hapus Log
            </button>
        </div>

        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <?= $_SESSION['success'] ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <?= $_SESSION['error'] ?>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <div class="filter-container">
            <form id="filter-form" method="GET" action="">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="category">Kategori</label>
                        <select id="category" name="category">
                            <option value="all" <?= $category === 'all' ? 'selected' : '' ?>>Semua Kategori</option>
                            <?php foreach ($categories as $cat_key => $cat_name): ?>
                                <option value="<?= $cat_key ?>" <?= $category === $cat_key ? 'selected' : '' ?>>
                                    <?= $cat_name ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="date_from">Tanggal Dari</label>
                        <input type="date" id="date_from" name="date_from" value="<?= $date_from ?>">
                    </div>

                    <div class="filter-group">
                        <label for="date_to">Tanggal Sampai</label>
                        <input type="date" id="date_to" name="date_to" value="<?= $date_to ?>">
                    </div>

                    <div class="filter-group">
                        <label for="search">Pencarian</label>
                        <input type="text" id="search" name="search" placeholder="Cari aktivitas atau pengguna..." value="<?= $search ?>">
                    </div>
                </div>

                <div class="filter-row">
                    <div class="filter-group">
                        <label for="limit">Tampilkan</label>
                        <select id="limit" name="limit">
                            <option value="25" <?= $limit === 25 ? 'selected' : '' ?>>25 per halaman</option>
                            <option value="50" <?= $limit === 50 ? 'selected' : '' ?>>50 per halaman</option>
                            <option value="100" <?= $limit === 100 ? 'selected' : '' ?>>100 per halaman</option>
                            <option value="200" <?= $limit === 200 ? 'selected' : '' ?>>200 per halaman</option>
                        </select>
                    </div>

                    <div class="filter-buttons">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                        <button type="button" id="auto-refresh-btn" class="btn btn-info" onclick="toggleAutoRefresh()">
                            <i class="fas fa-sync-alt"></i> Auto Refresh
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Loading Indicator -->
        <div id="loading-indicator" style="display: none;">
            <div class="spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>Memuat data...</p>
        </div>

        <?php if (empty($logs)): ?>
            <div class="no-logs">
                <i class="fas fa-info-circle"></i> Tidak ada log aktivitas yang ditemukan.
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="logs-table">
                    <thead>
                        <tr>
                            <th>Waktu</th>
                            <th>Pengguna</th>
                            <th>Aktivitas</th>
                            <th>Kategori</th>
                            <th>Detail</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td class="timestamp">
                                    <?= date('d M Y H:i:s', strtotime($log['timestamp'])) ?>
                                </td>
                                <td>
                                    <div class="user-info">
                                        <?= htmlspecialchars($log['user_name']) ?>
                                        <?php if (!empty($log['user_nik'])): ?>
                                            <span class="nik">(<?= htmlspecialchars($log['user_nik']) ?>)</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td><?= htmlspecialchars($log['action']) ?></td>
                                <td>
                                    <span class="category-badge category-<?= $log['category'] ?>">
                                        <?= $categories[$log['category']] ?? ucfirst($log['category']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if (!empty($log['details'])): ?>
                                        <button type="button" class="details-btn" onclick="toggleDetails(<?= $log['id'] ?>)">
                                            Lihat Detail
                                        </button>
                                        <div id="details-<?= $log['id'] ?>" class="details-content" style="display: none;">
                                            <?= json_encode($log['details'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) ?>
                                        </div>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=1&limit=<?= $limit ?>&category=<?= $category ?>&date_from=<?= $date_from ?>&date_to=<?= $date_to ?>&search=<?= $search ?>">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                    <a href="?page=<?= $page - 1 ?>&limit=<?= $limit ?>&category=<?= $category ?>&date_from=<?= $date_from ?>&date_to=<?= $date_to ?>&search=<?= $search ?>">
                        <i class="fas fa-angle-left"></i>
                    </a>
                <?php else: ?>
                    <span class="disabled"><i class="fas fa-angle-double-left"></i></span>
                    <span class="disabled"><i class="fas fa-angle-left"></i></span>
                <?php endif; ?>

                <?php
                $start_page = max(1, $page - 2);
                $end_page = min($total_pages, $page + 2);

                for ($i = $start_page; $i <= $end_page; $i++):
                ?>
                    <?php if ($i == $page): ?>
                        <span class="active"><?= $i ?></span>
                    <?php else: ?>
                        <a href="?page=<?= $i ?>&limit=<?= $limit ?>&category=<?= $category ?>&date_from=<?= $date_from ?>&date_to=<?= $date_to ?>&search=<?= $search ?>">
                            <?= $i ?>
                        </a>
                    <?php endif; ?>
                <?php endfor; ?>

                <?php if ($page < $total_pages): ?>
                    <a href="?page=<?= $page + 1 ?>&limit=<?= $limit ?>&category=<?= $category ?>&date_from=<?= $date_from ?>&date_to=<?= $date_to ?>&search=<?= $search ?>">
                        <i class="fas fa-angle-right"></i>
                    </a>
                    <a href="?page=<?= $total_pages ?>&limit=<?= $limit ?>&category=<?= $category ?>&date_from=<?= $date_from ?>&date_to=<?= $date_to ?>&search=<?= $search ?>">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                <?php else: ?>
                    <span class="disabled"><i class="fas fa-angle-right"></i></span>
                    <span class="disabled"><i class="fas fa-angle-double-right"></i></span>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <div class="logs-footer">
            <p>Total: <span id="total-logs"><?= $total_logs ?></span> log aktivitas</p>
            <a href="dashboard.php" class="back-btn" style="color: white;">
                <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
            </a>
        </div>
    </div>

    <!-- Modal Hapus Log -->
    <div id="clearModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Hapus Log Aktivitas</h2>
                <span class="close" onclick="closeClearModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="clearForm" method="POST" action="">
                    <input type="hidden" name="action" value="clear_logs">

                    <div class="filter-group" style="margin-bottom: 15px;">
                        <label for="clear_category">Kategori</label>
                        <select id="clear_category" name="clear_category">
                            <option value="all">Semua Kategori</option>
                            <?php foreach ($categories as $cat_key => $cat_name): ?>
                                <option value="<?= $cat_key ?>"><?= $cat_name ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="filter-group" style="margin-bottom: 15px;">
                        <label for="clear_date_from">Tanggal Dari</label>
                        <input type="date" id="clear_date_from" name="clear_date_from">
                    </div>

                    <div class="filter-group" style="margin-bottom: 15px;">
                        <label for="clear_date_to">Tanggal Sampai</label>
                        <input type="date" id="clear_date_to" name="clear_date_to">
                    </div>

                    <p style="color: #dc3545; font-weight: bold;">
                        <i class="fas fa-exclamation-triangle"></i> Peringatan: Tindakan ini tidak dapat dibatalkan!
                    </p>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeClearModal()">Batal</button>
                <button type="button" class="btn btn-danger" onclick="confirmClear()">Hapus Log</button>
            </div>
        </div>
    </div>

    <?php include '../config/footer.php'; ?>

    <script>
        // Variabel global untuk menyimpan status loading dan timer auto-refresh
        let isLoading = false;
        let autoRefreshTimer = null;
        let autoRefreshEnabled = false;
        let currentPage = <?= $page ?>;
        let currentLimit = <?= $limit ?>;

        // Fungsi untuk toggle detail log
        function toggleDetails(logId) {
            const detailsElement = document.getElementById(`details-${logId}`);
            if (detailsElement.style.display === 'none') {
                detailsElement.style.display = 'block';
            } else {
                detailsElement.style.display = 'none';
            }
        }

        // Fungsi untuk membuka modal hapus log
        function openClearModal() {
            document.getElementById('clearModal').style.display = 'block';
        }

        // Fungsi untuk menutup modal hapus log
        function closeClearModal() {
            document.getElementById('clearModal').style.display = 'none';
        }

        // Fungsi untuk konfirmasi hapus log
        function confirmClear() {
            confirmAction('Apakah Anda yakin ingin menghapus log aktivitas? Tindakan ini tidak dapat dibatalkan.', function() {
                document.getElementById('clearForm').submit();
            });
        }

        // Tutup modal jika user klik di luar modal
        window.onclick = function(event) {
            const modal = document.getElementById('clearModal');
            if (event.target == modal) {
                closeClearModal();
            }
        }

        // Fungsi untuk memuat log aktivitas dengan AJAX
        function loadLogs(page = 1, showLoading = true) {
            if (isLoading) return;
            isLoading = true;

            // Tampilkan loading indicator jika diperlukan
            if (showLoading) {
                document.getElementById('loading-indicator').style.display = 'flex';
            }

            // Ambil nilai filter
            const category = document.getElementById('category').value;
            const date_from = document.getElementById('date_from').value;
            const date_to = document.getElementById('date_to').value;
            const search = document.getElementById('search').value;
            const limit = document.getElementById('limit').value;

            // Simpan nilai page dan limit saat ini
            currentPage = page;
            currentLimit = limit;

            // Buat objek FormData untuk mengirim data
            const formData = new FormData();
            formData.append('action', 'get_logs');
            formData.append('page', page);
            formData.append('limit', limit);
            formData.append('category', category);
            formData.append('date_from', date_from);
            formData.append('date_to', date_to);
            formData.append('search', search);

            // Kirim request AJAX
            fetch('activity_logs_ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Update tabel log
                updateLogsTable(data.logs);

                // Update pagination
                updatePagination(data.pagination);

                // Update total logs
                document.getElementById('total-logs').textContent = data.total_logs;

                // Sembunyikan loading indicator
                document.getElementById('loading-indicator').style.display = 'none';

                // Update URL dengan parameter filter
                updateURL(page, limit, category, date_from, date_to, search);

                isLoading = false;
            })
            .catch(error => {
                console.error('Error loading logs:', error);
                document.getElementById('loading-indicator').style.display = 'none';
                isLoading = false;
                alert('Terjadi kesalahan saat memuat log aktivitas. Silakan coba lagi.');
            });
        }

        // Fungsi untuk update tabel log
        function updateLogsTable(logs) {
            const tableBody = document.querySelector('.logs-table tbody');
            const noLogsDiv = document.querySelector('.no-logs');

            if (logs.length === 0) {
                // Tampilkan pesan jika tidak ada log
                tableBody.innerHTML = '';
                if (noLogsDiv) {
                    noLogsDiv.style.display = 'block';
                } else {
                    const tableContainer = document.querySelector('.table-responsive');
                    tableContainer.innerHTML = '<div class="no-logs"><i class="fas fa-info-circle"></i> Tidak ada log aktivitas yang ditemukan.</div>';
                }
                return;
            }

            // Sembunyikan pesan tidak ada log jika ada
            if (noLogsDiv) {
                noLogsDiv.style.display = 'none';
            }

            // Buat HTML untuk baris tabel
            let html = '';
            logs.forEach(log => {
                const timestamp = new Date(log.timestamp).toLocaleString('id-ID', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                const categoryClass = `category-${log.category}`;
                const categoryName = log.category_name || log.category;

                html += `
                <tr>
                    <td class="timestamp">${timestamp}</td>
                    <td>
                        <div class="user-info">
                            ${log.user_name || 'Unknown'}
                            ${log.user_nik ? `<span class="nik">(${log.user_nik})</span>` : ''}
                        </div>
                    </td>
                    <td>${log.action}</td>
                    <td>
                        <span class="category-badge ${categoryClass}">
                            ${categoryName}
                        </span>
                    </td>
                    <td>
                        ${log.details ? `
                            <button type="button" class="details-btn" onclick="toggleDetails(${log.id})">
                                Lihat Detail
                            </button>
                            <div id="details-${log.id}" class="details-content" style="display: none;">
                                ${JSON.stringify(log.details, null, 2)}
                            </div>
                        ` : '-'}
                    </td>
                </tr>
                `;
            });

            tableBody.innerHTML = html;
        }

        // Fungsi untuk update pagination
        function updatePagination(pagination) {
            const paginationContainer = document.querySelector('.pagination');

            if (!pagination || pagination.total_pages <= 1) {
                paginationContainer.innerHTML = '';
                return;
            }

            let html = '';

            // First and previous buttons
            if (pagination.current_page > 1) {
                html += `<a href="javascript:void(0)" onclick="loadLogs(1)"><i class="fas fa-angle-double-left"></i></a>`;
                html += `<a href="javascript:void(0)" onclick="loadLogs(${pagination.current_page - 1})"><i class="fas fa-angle-left"></i></a>`;
            } else {
                html += `<span class="disabled"><i class="fas fa-angle-double-left"></i></span>`;
                html += `<span class="disabled"><i class="fas fa-angle-left"></i></span>`;
            }

            // Page numbers
            const startPage = Math.max(1, pagination.current_page - 2);
            const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

            for (let i = startPage; i <= endPage; i++) {
                if (i === pagination.current_page) {
                    html += `<span class="active">${i}</span>`;
                } else {
                    html += `<a href="javascript:void(0)" onclick="loadLogs(${i})">${i}</a>`;
                }
            }

            // Next and last buttons
            if (pagination.current_page < pagination.total_pages) {
                html += `<a href="javascript:void(0)" onclick="loadLogs(${pagination.current_page + 1})"><i class="fas fa-angle-right"></i></a>`;
                html += `<a href="javascript:void(0)" onclick="loadLogs(${pagination.total_pages})"><i class="fas fa-angle-double-right"></i></a>`;
            } else {
                html += `<span class="disabled"><i class="fas fa-angle-right"></i></span>`;
                html += `<span class="disabled"><i class="fas fa-angle-double-right"></i></span>`;
            }

            paginationContainer.innerHTML = html;
        }

        // Fungsi untuk update URL dengan parameter filter
        function updateURL(page, limit, category, date_from, date_to, search) {
            const url = new URL(window.location.href);
            url.searchParams.set('page', page);
            url.searchParams.set('limit', limit);

            if (category && category !== 'all') {
                url.searchParams.set('category', category);
            } else {
                url.searchParams.delete('category');
            }

            if (date_from) {
                url.searchParams.set('date_from', date_from);
            } else {
                url.searchParams.delete('date_from');
            }

            if (date_to) {
                url.searchParams.set('date_to', date_to);
            } else {
                url.searchParams.delete('date_to');
            }

            if (search) {
                url.searchParams.set('search', search);
            } else {
                url.searchParams.delete('search');
            }

            // Update URL tanpa refresh halaman
            window.history.pushState({}, '', url);
        }

        // Fungsi untuk reset filter
        function resetFilters() {
            // Reset semua filter ke nilai default
            document.getElementById('category').value = 'all';
            document.getElementById('date_from').value = '';
            document.getElementById('date_to').value = '';
            document.getElementById('search').value = '';
            document.getElementById('limit').value = '50';

            // Load logs dengan filter default
            loadLogs(1);
        }

        // Fungsi untuk toggle auto-refresh
        function toggleAutoRefresh() {
            autoRefreshEnabled = !autoRefreshEnabled;
            const button = document.getElementById('auto-refresh-btn');

            if (autoRefreshEnabled) {
                button.classList.add('active');
                button.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Auto Refresh (30s)';

                // Set timer untuk auto-refresh setiap 30 detik
                autoRefreshTimer = setInterval(() => {
                    loadLogs(currentPage, false);
                }, 30000);
            } else {
                button.classList.remove('active');
                button.innerHTML = '<i class="fas fa-sync-alt"></i> Auto Refresh';

                // Clear timer jika auto-refresh dimatikan
                if (autoRefreshTimer) {
                    clearInterval(autoRefreshTimer);
                    autoRefreshTimer = null;
                }
            }
        }

        // Event listener untuk form filter
        document.addEventListener('DOMContentLoaded', function() {
            // Tambahkan event listener untuk form filter
            const filterForm = document.getElementById('filter-form');
            if (filterForm) {
                filterForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    loadLogs(1);
                });
            }

            // Tambahkan event listener untuk input pencarian (dengan debounce)
            const searchInput = document.getElementById('search');
            if (searchInput) {
                let debounceTimer;
                searchInput.addEventListener('input', function() {
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        loadLogs(1);
                    }, 500); // Delay 500ms
                });
            }

            // Tambahkan event listener untuk perubahan dropdown filter
            const filterDropdowns = document.querySelectorAll('#category, #limit');
            filterDropdowns.forEach(dropdown => {
                dropdown.addEventListener('change', function() {
                    loadLogs(1);
                });
            });

            // Tambahkan event listener untuk input tanggal
            const dateInputs = document.querySelectorAll('#date_from, #date_to');
            dateInputs.forEach(input => {
                input.addEventListener('change', function() {
                    loadLogs(1);
                });
            });

            // Load logs saat halaman dimuat
            // Uncomment baris di bawah jika ingin menggunakan AJAX untuk memuat data
            loadLogs(currentPage);
        });
    </script>
</body>
</html>
