<?php
session_start();
include '../config/config.php';
include '../config/access_control.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "Please login first";
    exit();
}

$user_id = $_SESSION['user_id'];

echo "<h2>Testing Access Control Functions</h2>";

// Test 1: Get user data
echo "<h3>1. Get User Data</h3>";
$user_data = getUserLevel($user_id);
if ($user_data) {
    echo "<pre>";
    print_r($user_data);
    echo "</pre>";
} else {
    echo "User data not found!";
}

// Test 2: Check access eligibility
echo "<h3>2. Check Access Eligibility</h3>";
$eligibility = getAccessEligibility($user_id, 4, ['Supervisor', 'Chief']);
echo "<pre>";
print_r($eligibility);
echo "</pre>";

// Test 3: Test hasMinimumLevel
echo "<h3>3. Test hasMinimumLevel</h3>";
for ($level = 3; $level <= 6; $level++) {
    $has_access = hasMinimumLevel($level, ['Supervisor', 'Chief']);
    echo "Level {$level}+: " . ($has_access ? "YES" : "NO") . "<br>";
}

// Test 4: Test hasSpecialPosition
echo "<h3>4. Test hasSpecialPosition</h3>";
$positions = ['Supervisor', 'Chief', 'Manager', 'Head'];
foreach ($positions as $position) {
    $has_position = hasSpecialPosition($user_id, [$position]);
    echo "{$position}: " . ($has_position ? "YES" : "NO") . "<br>";
}

// Test 5: Test checkUserLevel (without redirect)
echo "<h3>5. Test checkUserLevel (simulation)</h3>";
try {
    // This would normally redirect, so we'll just show the eligibility
    if ($eligibility['eligible']) {
        echo "✅ Access would be GRANTED<br>";
        echo "Reason: " . $eligibility['reason'] . "<br>";
    } else {
        echo "❌ Access would be DENIED<br>";
        echo "Reason: " . $eligibility['reason'] . "<br>";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}

echo "<br><a href='test_access_control.php'>Go to Full Test Page</a>";
echo "<br><a href='../pemohon/form.php'>Try Access Form</a>";
?>
