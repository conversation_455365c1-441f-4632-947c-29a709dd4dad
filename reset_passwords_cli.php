<?php
/**
 * Command Line Script untuk reset semua password ke "asdf"
 * 
 * Usage:
 * php reset_passwords_cli.php [options]
 * 
 * Options:
 * --confirm          : Konfirmasi untuk menjalankan script
 * --exclude-admin    : Exclude admin accounts (role_id = 99)
 * --backup          : Backup password lama
 * --help            : <PERSON><PERSON><PERSON><PERSON> help
 */

// <PERSON>ya bisa dijalankan dari command line
if (php_sapi_name() !== 'cli') {
    die("This script can only be run from command line.\n");
}

// Parse command line arguments
$options = getopt('', ['confirm', 'exclude-admin', 'backup', 'help']);

if (isset($options['help'])) {
    echo "Reset All Passwords to Default (asdf)\n";
    echo "=====================================\n\n";
    echo "Usage: php reset_passwords_cli.php [options]\n\n";
    echo "Options:\n";
    echo "  --confirm          Confirm to execute the script\n";
    echo "  --exclude-admin    Exclude admin accounts (role_id = 99)\n";
    echo "  --backup          Backup old passwords before reset\n";
    echo "  --help            Show this help message\n\n";
    echo "Example:\n";
    echo "  php reset_passwords_cli.php --confirm --exclude-admin --backup\n\n";
    echo "WARNING: This script will change ALL user passwords to 'asdf'!\n";
    echo "Only use in development/testing environment!\n";
    exit(0);
}

if (!isset($options['confirm'])) {
    echo "❌ ERROR: You must use --confirm flag to execute this script.\n";
    echo "This is a safety measure to prevent accidental execution.\n\n";
    echo "Use: php reset_passwords_cli.php --help for more information.\n";
    exit(1);
}

// Include database config
require_once 'config/config.php';

echo "🔐 Reset All Passwords to Default\n";
echo "=================================\n\n";

$exclude_admin = isset($options['exclude-admin']);
$backup = isset($options['backup']);

echo "Configuration:\n";
echo "- Exclude Admin: " . ($exclude_admin ? 'YES' : 'NO') . "\n";
echo "- Backup Passwords: " . ($backup ? 'YES' : 'NO') . "\n";
echo "- Target Password: asdf\n\n";

// Konfirmasi final
echo "⚠️  WARNING: This will change ALL user passwords to 'asdf'!\n";
echo "Are you sure you want to continue? (type 'YES' to confirm): ";
$handle = fopen("php://stdin", "r");
$confirmation = trim(fgets($handle));
fclose($handle);

if ($confirmation !== 'YES') {
    echo "❌ Operation cancelled.\n";
    exit(1);
}

echo "\n🚀 Starting password reset process...\n\n";

try {
    // 1. Create backup table if needed
    if ($backup) {
        echo "📦 Creating backup table...\n";
        $backup_table_query = "CREATE TABLE IF NOT EXISTS password_backup (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            old_password VARCHAR(255) NOT NULL,
            backup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            backup_reason VARCHAR(100) DEFAULT 'cli_mass_reset'
        )";
        
        if ($conn->query($backup_table_query)) {
            echo "✅ Backup table ready\n";
        } else {
            throw new Exception("Failed to create backup table: " . $conn->error);
        }
    }
    
    // 2. Hash default password
    echo "🔒 Hashing default password...\n";
    $default_password_hash = password_hash('asdf', PASSWORD_DEFAULT);
    echo "✅ Password hashed\n";
    
    // 3. Get users to update
    echo "👥 Fetching users...\n";
    $users_query = "SELECT id, name, nik, COALESCE(password, '') as password FROM users WHERE is_active = 1";
    if ($exclude_admin) {
        $users_query .= " AND role_id != 99";
    }
    
    $users_result = $conn->query($users_query);
    $total_users = $users_result->num_rows;
    
    echo "📊 Found {$total_users} users to update\n\n";
    
    if ($total_users == 0) {
        echo "⚠️  No users found to update.\n";
        exit(0);
    }
    
    // 4. Start transaction
    $conn->begin_transaction();
    
    $success_count = 0;
    $backup_count = 0;
    $errors = [];
    
    echo "🔄 Processing users:\n";
    
    while ($user = $users_result->fetch_assoc()) {
        try {
            // Backup old password
            if ($backup) {
                // Handle NULL password - backup as empty string with note
                $password_to_backup = $user['password'];
                if (empty($password_to_backup)) {
                    $password_to_backup = '[NULL_OR_EMPTY_PASSWORD]';
                }

                $backup_query = "INSERT INTO password_backup (user_id, old_password) VALUES (?, ?)";
                $backup_stmt = $conn->prepare($backup_query);
                $backup_stmt->bind_param("is", $user['id'], $password_to_backup);

                if ($backup_stmt->execute()) {
                    $backup_count++;
                }
                $backup_stmt->close();
            }
            
            // Update password
            $update_query = "UPDATE users SET 
                            password = ?, 
                            password_changed_at = NULL 
                            WHERE id = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("si", $default_password_hash, $user['id']);
            
            if ($update_stmt->execute()) {
                $success_count++;
                echo "✅ Updated: {$user['name']} ({$user['nik']})\n";
            } else {
                $error_msg = "Failed to update user {$user['name']}: " . $update_stmt->error;
                $errors[] = $error_msg;
                echo "❌ {$error_msg}\n";
            }
            $update_stmt->close();
            
        } catch (Exception $e) {
            $error_msg = "Error processing user {$user['name']}: " . $e->getMessage();
            $errors[] = $error_msg;
            echo "❌ {$error_msg}\n";
        }
    }
    
    echo "\n";
    
    // 5. Commit or rollback
    if (empty($errors)) {
        $conn->commit();
        echo "✅ SUCCESS: Password reset completed!\n\n";
        echo "📊 Summary:\n";
        echo "- Users updated: {$success_count}\n";
        if ($backup) {
            echo "- Passwords backed up: {$backup_count}\n";
        }
        echo "- Errors: 0\n\n";
        
        echo "🧪 Testing Instructions:\n";
        echo "1. All users now have password: 'asdf'\n";
        echo "2. Login with any user using password 'asdf'\n";
        echo "3. User should be redirected to change_password.php\n";
        echo "4. Verify warning message appears\n";
        echo "5. Test password change process\n\n";
        
        echo "🧹 Cleanup:\n";
        echo "- Delete this script: rm reset_passwords_cli.php\n";
        echo "- Delete web script: rm reset_all_passwords.php\n";
        if ($backup) {
            echo "- Backup data available in 'password_backup' table\n";
        }
        
    } else {
        $conn->rollback();
        echo "❌ FAILED: Transaction rolled back due to errors:\n";
        foreach ($errors as $error) {
            echo "   - {$error}\n";
        }
        echo "\nNo changes were made to the database.\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "❌ FATAL ERROR: " . $e->getMessage() . "\n";
    exit(1);
}

$conn->close();
echo "\n🎉 Script completed successfully!\n";
exit(0);
?>
