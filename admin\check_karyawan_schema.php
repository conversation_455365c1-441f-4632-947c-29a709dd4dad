<?php
/**
 * Check Karyawan Table Schema
 * This file checks the structure of the karyawan table
 */

session_start();
require_once '../config/config.php';

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo 'Unauthorized access';
    exit();
}

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Karyawan Table Schema</h2>";
    
    // Check table structure
    $stmt = $pdo->prepare("DESCRIBE karyawan");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Table Structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td><strong>" . $column['Field'] . "</strong></td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show sample data
    echo "<h3>Sample Data (First 5 records):</h3>";
    $stmt = $pdo->prepare("SELECT * FROM karyawan LIMIT 5");
    $stmt->execute();
    $sample_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($sample_data)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        
        // Header
        echo "<tr>";
        foreach (array_keys($sample_data[0]) as $column) {
            echo "<th>" . $column . "</th>";
        }
        echo "</tr>";
        
        // Data
        foreach ($sample_data as $row) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No data found in karyawan table.</p>";
    }
    
    // Check for name-related columns
    echo "<h3>Name-related Columns Analysis:</h3>";
    $name_columns = array_filter($columns, function($col) {
        return stripos($col['Field'], 'name') !== false || 
               stripos($col['Field'], 'nama') !== false ||
               stripos($col['Field'], 'full') !== false;
    });
    
    if (!empty($name_columns)) {
        echo "<p>Found name-related columns:</p>";
        echo "<ul>";
        foreach ($name_columns as $col) {
            echo "<li><strong>" . $col['Field'] . "</strong> (" . $col['Type'] . ")</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>No obvious name columns found. Common alternatives:</p>";
        echo "<ul>";
        echo "<li>nama</li>";
        echo "<li>full_name</li>";
        echo "<li>employee_name</li>";
        echo "<li>first_name + last_name</li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}
</style>
