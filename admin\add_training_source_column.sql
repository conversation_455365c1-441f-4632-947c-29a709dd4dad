-- ============================================================================
-- Script untuk menambahkan kolom training_source ke tabel training_classes
-- ============================================================================

-- Cek apakah kolom training_source sudah ada
-- Jika belum ada, tambahkan kolom tersebut

-- ============================================================================
-- 1. BACKUP DATA TERLEBIH DAHULU
-- ============================================================================

-- Backup tabel training_classes
CREATE TABLE training_classes_backup AS SELECT * FROM training_classes;

-- ============================================================================
-- 2. TAMBAHKAN KOLOM TRAINING_SOURCE
-- ============================================================================

-- Tambahkan kolom training_source untuk membedakan external dan internal training
ALTER TABLE training_classes 
ADD COLUMN training_source ENUM('external', 'internal') DEFAULT 'external' 
COMMENT 'Source of training: external (training_submissions) or internal (offline_training)';

-- ============================================================================
-- 3. UPDATE EXISTING DATA
-- ============================================================================

-- Set semua data existing sebagai external (default)
UPDATE training_classes 
SET training_source = 'external' 
WHERE training_source IS NULL;

-- ============================================================================
-- 4. VERIFIKASI HASIL
-- ============================================================================

-- Cek struktur tabel training_classes
DESCRIBE training_classes;

-- Cek data training_classes
SELECT id, training_id, title, training_source, created_at 
FROM training_classes 
ORDER BY created_at DESC 
LIMIT 10;

-- ============================================================================
-- 5. ROLLBACK JIKA DIPERLUKAN
-- ============================================================================

-- Jika ada masalah, rollback dengan:
-- DROP TABLE training_classes;
-- RENAME TABLE training_classes_backup TO training_classes;

-- ============================================================================
-- 6. CLEANUP BACKUP (SETELAH YAKIN TIDAK ADA MASALAH)
-- ============================================================================

-- Hapus backup setelah yakin tidak ada masalah
-- DROP TABLE training_classes_backup;

-- ============================================================================
-- CATATAN PENTING
-- ============================================================================

/*
1. Script ini menambahkan kolom training_source ke tabel training_classes
2. Kolom ini digunakan untuk membedakan training dari:
   - external: training_submissions (training eksternal)
   - internal: offline_training (training internal)
3. Default value adalah 'external' untuk backward compatibility
4. Existing data akan di-set sebagai 'external'
5. Sistem akan otomatis detect dan handle kedua jenis training
*/
