<?php
session_start();
include '../config/config.php';

// Cek autentikasi
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

$action = isset($_GET['action']) ? $_GET['action'] : '';

header('Content-Type: application/json');

try {
    switch ($action) {
        case 'departments':
            // Get unique departments from karyawan table
            $query = "SELECT DISTINCT dept FROM karyawan WHERE dept IS NOT NULL AND dept != '' ORDER BY dept";
            $result = $conn->query($query);
            
            $departments = [];
            while ($row = $result->fetch_assoc()) {
                $departments[] = $row['dept'];
            }
            
            echo json_encode([
                'success' => true,
                'data' => $departments
            ]);
            break;
            
        case 'bagian':
            $dept = isset($_GET['dept']) ? $_GET['dept'] : '';
            if (empty($dept)) {
                echo json_encode(['success' => false, 'message' => 'Department required']);
                exit();
            }
            
            // Get unique bagian for specific department
            $query = "SELECT DISTINCT bagian FROM karyawan WHERE dept = ? AND bagian IS NOT NULL AND bagian != '' ORDER BY bagian";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("s", $dept);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $bagian = [];
            while ($row = $result->fetch_assoc()) {
                $bagian[] = $row['bagian'];
            }
            
            echo json_encode([
                'success' => true,
                'data' => $bagian
            ]);
            break;
            
        case 'jabatan':
            $dept = isset($_GET['dept']) ? $_GET['dept'] : '';
            $bagian = isset($_GET['bagian']) ? $_GET['bagian'] : '';
            
            if (empty($dept)) {
                echo json_encode(['success' => false, 'message' => 'Department required']);
                exit();
            }
            
            // Build query based on available parameters
            if (!empty($bagian)) {
                // Get jabatan for specific department and bagian
                $query = "SELECT DISTINCT jabatan FROM karyawan WHERE dept = ? AND bagian = ? AND jabatan IS NOT NULL AND jabatan != '' ORDER BY jabatan";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("ss", $dept, $bagian);
            } else {
                // Get jabatan for specific department only
                $query = "SELECT DISTINCT jabatan FROM karyawan WHERE dept = ? AND jabatan IS NOT NULL AND jabatan != '' ORDER BY jabatan";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("s", $dept);
            }
            
            $stmt->execute();
            $result = $stmt->get_result();
            
            $jabatan = [];
            while ($row = $result->fetch_assoc()) {
                $jabatan[] = $row['jabatan'];
            }
            
            echo json_encode([
                'success' => true,
                'data' => $jabatan
            ]);
            break;
            
        case 'all_data':
            // Get all unique combinations for initial load
            $query = "SELECT DISTINCT dept, bagian, jabatan FROM karyawan 
                     WHERE dept IS NOT NULL AND dept != '' 
                     AND bagian IS NOT NULL AND bagian != '' 
                     AND jabatan IS NOT NULL AND jabatan != '' 
                     ORDER BY dept, bagian, jabatan";
            $result = $conn->query($query);
            
            $data = [
                'departments' => [],
                'bagian_by_dept' => [],
                'jabatan_by_dept_bagian' => []
            ];
            
            while ($row = $result->fetch_assoc()) {
                $dept = $row['dept'];
                $bagian = $row['bagian'];
                $jabatan = $row['jabatan'];
                
                // Collect unique departments
                if (!in_array($dept, $data['departments'])) {
                    $data['departments'][] = $dept;
                }
                
                // Collect bagian by department
                if (!isset($data['bagian_by_dept'][$dept])) {
                    $data['bagian_by_dept'][$dept] = [];
                }
                if (!in_array($bagian, $data['bagian_by_dept'][$dept])) {
                    $data['bagian_by_dept'][$dept][] = $bagian;
                }
                
                // Collect jabatan by department and bagian
                $key = $dept . '|' . $bagian;
                if (!isset($data['jabatan_by_dept_bagian'][$key])) {
                    $data['jabatan_by_dept_bagian'][$key] = [];
                }
                if (!in_array($jabatan, $data['jabatan_by_dept_bagian'][$key])) {
                    $data['jabatan_by_dept_bagian'][$key][] = $jabatan;
                }
            }
            
            echo json_encode([
                'success' => true,
                'data' => $data
            ]);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
