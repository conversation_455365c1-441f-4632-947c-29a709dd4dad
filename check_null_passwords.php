<?php
/**
 * <PERSON>ript untuk mengecek dan memperbaiki user dengan password NULL
 * Jalankan sebelum reset_all_passwords.php jika ada error
 */

include 'config/config.php';

echo "<h2>🔍 Check NULL Passwords</h2>";

// 1. Cek user dengan password NULL atau empty
echo "<h3>1. Users with NULL or Empty Passwords</h3>";

$null_query = "SELECT id, name, nik, email, role_id, password, 
               CASE 
                   WHEN password IS NULL THEN 'NULL'
                   WHEN password = '' THEN 'EMPTY'
                   ELSE 'HAS_PASSWORD'
               END as password_status
               FROM users 
               WHERE is_active = 1 
               AND (password IS NULL OR password = '')
               ORDER BY role_id, name";

$null_result = $conn->query($null_query);

if ($null_result->num_rows > 0) {
    echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0;'>";
    echo "<p><strong>⚠️ Found {$null_result->num_rows} users with NULL/empty passwords!</strong></p>";
    echo "</div>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>NIK</th><th>Email</th><th>Role ID</th><th>Password Status</th></tr>";
    
    $null_users = [];
    while ($user = $null_result->fetch_assoc()) {
        $null_users[] = $user;
        $bg_color = $user['password_status'] === 'NULL' ? '#f8d7da' : '#fff3cd';
        echo "<tr style='background-color: {$bg_color};'>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['nik']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>{$user['role_id']}</td>";
        echo "<td><strong>{$user['password_status']}</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. Opsi untuk memperbaiki
    echo "<h3>2. Fix NULL Passwords</h3>";
    
    $fix = $_GET['fix'] ?? '';
    if ($fix === 'yes') {
        echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #0066cc; margin: 10px 0;'>";
        echo "<h4>🔧 Fixing NULL passwords...</h4>";
        
        $default_password_hash = password_hash('asdf', PASSWORD_DEFAULT);
        $fixed_count = 0;
        $errors = [];
        
        foreach ($null_users as $user) {
            try {
                $update_query = "UPDATE users SET password = ? WHERE id = ?";
                $stmt = $conn->prepare($update_query);
                $stmt->bind_param("si", $default_password_hash, $user['id']);
                
                if ($stmt->execute()) {
                    $fixed_count++;
                    echo "<p>✅ Fixed user: {$user['name']} (ID: {$user['id']})</p>";
                } else {
                    $errors[] = "Failed to fix user {$user['name']}: " . $stmt->error;
                    echo "<p>❌ Failed to fix user: {$user['name']} - {$stmt->error}</p>";
                }
                $stmt->close();
                
            } catch (Exception $e) {
                $errors[] = "Error fixing user {$user['name']}: " . $e->getMessage();
                echo "<p>❌ Error fixing user: {$user['name']} - {$e->getMessage()}</p>";
            }
        }
        
        echo "<div style='background-color: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>📊 Summary:</h4>";
        echo "<ul>";
        echo "<li><strong>Users fixed:</strong> {$fixed_count}</li>";
        echo "<li><strong>Errors:</strong> " . count($errors) . "</li>";
        echo "</ul>";
        echo "</div>";
        
        if ($fixed_count > 0) {
            echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>";
            echo "<h4>✅ Success!</h4>";
            echo "<p>All NULL passwords have been fixed. You can now run the password reset script safely.</p>";
            echo "<p><a href='reset_all_passwords.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Go to Password Reset</a></p>";
            echo "</div>";
        }
        
        echo "</div>";
        
    } else {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
        echo "<h4>⚠️ Action Required</h4>";
        echo "<p>These users have NULL/empty passwords which will cause errors in the password reset script.</p>";
        echo "<p><strong>Options:</strong></p>";
        echo "<ol>";
        echo "<li><strong>Automatic Fix:</strong> Set all NULL passwords to 'asdf' (recommended)</li>";
        echo "<li><strong>Manual Fix:</strong> Update passwords manually in database</li>";
        echo "<li><strong>Skip:</strong> Exclude these users from password reset</li>";
        echo "</ol>";
        echo "<p><a href='?fix=yes' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Auto Fix NULL Passwords</a></p>";
        echo "</div>";
    }
    
} else {
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>";
    echo "<p><strong>✅ No users with NULL/empty passwords found!</strong></p>";
    echo "<p>You can safely run the password reset script.</p>";
    echo "<p><a href='reset_all_passwords.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Go to Password Reset</a></p>";
    echo "</div>";
}

// 3. Statistics
echo "<h3>3. Password Statistics</h3>";

$stats_query = "SELECT 
                COUNT(*) as total_users,
                SUM(CASE WHEN password IS NULL THEN 1 ELSE 0 END) as null_passwords,
                SUM(CASE WHEN password = '' THEN 1 ELSE 0 END) as empty_passwords,
                SUM(CASE WHEN password IS NOT NULL AND password != '' THEN 1 ELSE 0 END) as valid_passwords,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users,
                SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_users
                FROM users";

$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Metric</th><th>Count</th><th>Percentage</th></tr>";

$total = $stats['total_users'];
echo "<tr><td>Total Users</td><td>{$stats['total_users']}</td><td>100%</td></tr>";
echo "<tr><td>Active Users</td><td>{$stats['active_users']}</td><td>" . round(($stats['active_users']/$total)*100, 1) . "%</td></tr>";
echo "<tr><td>Inactive Users</td><td>{$stats['inactive_users']}</td><td>" . round(($stats['inactive_users']/$total)*100, 1) . "%</td></tr>";
echo "<tr style='background-color: #f8d7da;'><td>NULL Passwords</td><td>{$stats['null_passwords']}</td><td>" . round(($stats['null_passwords']/$total)*100, 1) . "%</td></tr>";
echo "<tr style='background-color: #fff3cd;'><td>Empty Passwords</td><td>{$stats['empty_passwords']}</td><td>" . round(($stats['empty_passwords']/$total)*100, 1) . "%</td></tr>";
echo "<tr style='background-color: #d4edda;'><td>Valid Passwords</td><td>{$stats['valid_passwords']}</td><td>" . round(($stats['valid_passwords']/$total)*100, 1) . "%</td></tr>";
echo "</table>";

// 4. SQL untuk manual fix
echo "<h3>4. Manual SQL Fix (Alternative)</h3>";
echo "<div style='background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px;'>";
echo "<p>Jika Anda prefer manual fix via SQL:</p>";
echo "<pre style='background-color: #e9ecef; padding: 10px; border-radius: 3px;'>";
echo "-- Set default password untuk user dengan password NULL\n";
echo "UPDATE users \n";
echo "SET password = '" . password_hash('asdf', PASSWORD_DEFAULT) . "'\n";
echo "WHERE password IS NULL OR password = '';\n\n";
echo "-- Verifikasi hasil\n";
echo "SELECT COUNT(*) as fixed_users \n";
echo "FROM users \n";
echo "WHERE password IS NOT NULL AND password != '';";
echo "</pre>";
echo "</div>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

h2 {
    color: #333;
    border-bottom: 2px solid #0066cc;
    padding-bottom: 10px;
}

h3 {
    color: #0066cc;
    margin-top: 30px;
}

pre {
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}
</style>
