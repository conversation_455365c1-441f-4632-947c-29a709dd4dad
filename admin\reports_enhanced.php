<?php
session_start();
include '../config/config.php';

// Validate admin access
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get filters
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '2000-01-01'; // Default to a very old date
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-12-31');    // Default to end of current year
$dept_filter = isset($_GET['department']) ? $_GET['department'] : '';
$training_type = isset($_GET['training_type']) ? $_GET['training_type'] : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$sort_by = isset($_GET['sort_by']) ? $_GET['sort_by'] : 'start_date';
$sort_order = isset($_GET['sort_order']) ? $_GET['sort_order'] : 'DESC';
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'dashboard'; // New: view mode (dashboard, table, calendar)

// Base WHERE clause
$where_clause = "start_date BETWEEN ? AND ?";
$params = [$start_date, $end_date];
$types = "ss";

if ($dept_filter) {
    $where_clause .= " AND departemen = ?";
    $params[] = $dept_filter;
    $types .= "s";
}

if ($training_type) {
    $where_clause .= " AND training_type = ?";
    $params[] = $training_type;
    $types .= "s";
}

if ($status_filter) {
    $where_clause .= " AND status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

// Training status statistics
$query_stats = "SELECT
    COUNT(*) as total_trainings,
    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
    SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
    COALESCE(SUM(training_cost), 0) as total_cost,
    COUNT(DISTINCT departemen) as total_departments,
    COUNT(DISTINCT full_name) as total_participants
FROM training_submissions
WHERE $where_clause";

$stmt = $conn->prepare($query_stats);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$stats = $stmt->get_result()->fetch_assoc();

// Department-wise training distribution
$query_dept = "SELECT
    departemen,
    COUNT(*) as training_count,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
    COALESCE(SUM(training_cost), 0) as dept_cost,
    COUNT(DISTINCT full_name) as participant_count
FROM training_submissions
WHERE $where_clause
GROUP BY departemen
ORDER BY training_count DESC";

$stmt = $conn->prepare($query_dept);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$dept_stats = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Monthly training trends
$query_trends = "SELECT
    DATE_FORMAT(start_date, '%Y-%m') as month,
    COUNT(*) as total_count,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
    COALESCE(SUM(training_cost), 0) as monthly_cost,
    COUNT(DISTINCT full_name) as participant_count
FROM training_submissions
WHERE $where_clause
GROUP BY DATE_FORMAT(start_date, '%Y-%m')
ORDER BY month ASC";

$stmt = $conn->prepare($query_trends);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$monthly_trends = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Training type distribution
$query_types = "SELECT
    training_type,
    COUNT(*) as type_count,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
    COALESCE(SUM(training_cost), 0) as type_cost
FROM training_submissions
WHERE $where_clause AND training_type IS NOT NULL AND training_type != ''
GROUP BY training_type
ORDER BY type_count DESC";

$stmt = $conn->prepare($query_types);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$training_type_stats = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Get unique departments, training types, and statuses for filters
$query_filters = "SELECT DISTINCT departemen, training_type, status FROM training_submissions WHERE start_date BETWEEN ? AND ?";
$stmt = $conn->prepare($query_filters);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$filter_result = $stmt->get_result();
$departments = [];
$training_types = [];
$statuses = [];
while ($row = $filter_result->fetch_assoc()) {
    if ($row['departemen']) $departments[] = $row['departemen'];
    if ($row['training_type']) $training_types[] = $row['training_type'];
    if ($row['status']) $statuses[] = $row['status'];
}
$departments = array_unique($departments);
$training_types = array_unique($training_types);
$statuses = array_unique($statuses);

// Recent trainings with pagination
$items_per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10; // New: configurable items per page
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $items_per_page;

// Count total records for pagination
$count_query = "SELECT COUNT(*) as total FROM training_submissions WHERE $where_clause";
$stmt = $conn->prepare($count_query);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$total_records = $stmt->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total_records / $items_per_page);

// Get paginated records
$query_recent = "SELECT
    id,
    training_topic,
    full_name,
    departemen,
    start_date,
    status,
    training_cost,
    training_type
FROM training_submissions
WHERE $where_clause
ORDER BY $sort_by $sort_order
LIMIT ?, ?";

$stmt = $conn->prepare($query_recent);
$all_params = array_merge($params, [$offset, $items_per_page]);
$stmt->bind_param($types . "ii", ...$all_params);
$stmt->execute();
$recent_trainings = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Calculate ROI if we have cost data
$total_cost = $stats['total_cost'];
$completed_trainings = $stats['completed'];
$avg_cost_per_training = $completed_trainings > 0 ? $total_cost / $completed_trainings : 0;

// Get top participants
$query_participants = "SELECT
    full_name,
    COUNT(*) as training_count,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count
FROM training_submissions
WHERE $where_clause
GROUP BY full_name
ORDER BY training_count DESC
LIMIT 5";

$stmt = $conn->prepare($query_participants);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$top_participants = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// NEW: Get calendar data for calendar view
$query_calendar = "SELECT
    id,
    training_topic,
    full_name,
    departemen,
    start_date,
    end_date,
    is_confirmed,
    training_place as training_place_name,
    status,
    training_cost,
    training_type
FROM training_submissions
WHERE $where_clause
ORDER BY start_date ASC";

$stmt = $conn->prepare($query_calendar);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$calendar_data = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Get current year for reference
$current_year = date('Y', strtotime($end_date));

// Function to automatically update training status to "Completed"
// when director has approved and training date has passed
function updateCompletedTrainings($conn) {
    $today = date('Y-m-d');

    // Update trainings with status "Approved" where the training date has passed
    $query = "UPDATE training_submissions
              SET status = 'Completed'
              WHERE status = 'Approved' AND (
                  (start_date < ? AND is_confirmed = 0) OR
                  (start_date < ? AND is_confirmed = 1) OR
                  (end_date < ? AND end_date IS NOT NULL)
              )";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("sss", $today, $today, $today);
    $stmt->execute();

    // Return number of updated records
    return $stmt->affected_rows;
}

// Run the update function
$updated_trainings = updateCompletedTrainings($conn);

// NEW: Get training effectiveness data (completion rate by department)
$query_effectiveness = "SELECT
    departemen,
    COUNT(*) as total,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
    (SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) / COUNT(*)) * 100 as completion_rate
FROM training_submissions
WHERE $where_clause
GROUP BY departemen
ORDER BY completion_rate DESC";

$stmt = $conn->prepare($query_effectiveness);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$effectiveness_data = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- ApexCharts for better visualizations -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<!-- DataTables for better table functionality -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>
<!-- DateRangePicker for better date selection -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<!-- FullCalendar for calendar view -->
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css' rel='stylesheet' />
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js'></script>
<!-- CountUp.js for animated counters -->
<script src="https://cdn.jsdelivr.net/npm/countup.js@2.0.8/dist/countUp.min.js"></script>
<!-- Tippy.js for enhanced tooltips -->
<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="https://unpkg.com/tippy.js@6"></script>

<style>
:root {
    --primary-color: #BF0000;
    --primary-light: rgba(191, 0, 0, 0.1);
    --secondary-color: #333;
    --success-color: #28a745;
    --warning-color: #f57c00;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-gray: #f8f9fa;
    --dark-gray: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    background-color: #f5f5f5;
    color: var(--secondary-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dashboard-container {
    max-width: 1400px;
    margin: 20px auto;
    padding: 0 15px;
}

.dashboard-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
}

.auto-update-notification {
    width: 100%;
    margin-bottom: 15px;
}

.auto-update-notification .alert {
    margin-bottom: 0;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.dashboard-header h1 {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 28px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.dashboard-header h1 i {
    margin-right: 10px;
}

.view-toggle {
    display: flex;
    background: white;
    border-radius: 30px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    margin-bottom: 10px;
}

.view-toggle a {
    padding: 8px 15px;
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.view-toggle a i {
    margin-right: 5px;
}

.view-toggle a.active {
    background-color: var(--primary-color);
    color: white;
}

.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 25px;
    overflow: hidden;
    transition: var(--transition);
    background-color: white;
}

.card:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #eee;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
}

.card-header h5 i {
    margin-right: 8px;
}

.card-body {
    padding: 20px;
    background-color: white;
}

.filter-card {
    background-color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card .icon {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.stat-card h3 {
    font-size: 16px;
    color: var(--secondary-color);
    margin-bottom: 10px;
    font-weight: 500;
}

.stat-card .number {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-card .trend {
    font-size: 14px;
    color: var(--success-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 5px;
}

.stat-card .trend i {
    margin-right: 5px;
}

.stat-card .trend.negative {
    color: var(--danger-color);
}

.stat-card .cost {
    color: var(--success-color);
    margin-top: 5px;
    font-size: 14px;
    font-weight: 500;
}

.chart-container {
    background: white;
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 25px;
    box-shadow: var(--box-shadow);
    height: 400px;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
    gap: 25px;
    margin-bottom: 25px;
}

.table-card {
    background: white;
    border-radius: var(--border-radius);
    margin-bottom: 25px;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.table-responsive {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

th {
    background-color: #f8f9fa;
    color: var(--secondary-color);
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

tbody tr:hover {
    background-color: #f8f9fa;
}

.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.status-approved { background: #e3f2fd; color: #1976d2; }
.status-completed { background: #e8f5e9; color: #2e7d32; }
.status-rejected { background: #ffebee; color: #c62828; }
.status-pending { background: #fff3e0; color: #f57c00; }

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #a00000;
    border-color: #a00000;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.pagination .page-item .page-link {
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.export-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.export-btn {
    padding: 10px 15px;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
    font-weight: 500;
}

.export-btn.excel {
    background-color: #1F7244;
}

.export-btn.excel:hover {
    background-color: #165933;
}

.export-btn.pdf {
    background-color: #F40F02;
}

.export-btn.pdf:hover {
    background-color: #C50D02;
}

.export-btn.print {
    background-color: #333;
}

.export-btn.print:hover {
    background-color: #000;
}

.progress {
    height: 8px;
    margin-top: 5px;
    border-radius: 4px;
}

.progress-bar {
    background-color: var(--primary-color);
}

.top-performers {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.performer-card {
    background: white;
    padding: 15px;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: var(--transition);
}

.performer-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.performer-card .name {
    font-weight: 600;
    margin-bottom: 5px;
}

.performer-card .count {
    color: var(--primary-color);
    font-weight: 500;
}

.sort-icon {
    margin-left: 5px;
    font-size: 12px;
}

.calendar-container {
    height: 700px;
    background: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 25px;
}

.fc-event {
    cursor: pointer;
    border-radius: 4px;
    padding: 2px 5px;
}

.fc-event-title {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.fc-event-approved { background-color: #e3f2fd; border-color: #1976d2; color: #1976d2; }
.fc-event-completed { background-color: #e8f5e9; border-color: #2e7d32; color: #2e7d32; }
.fc-event-rejected { background-color: #ffebee; border-color: #c62828; color: #c62828; }
.fc-event-pending { background-color: #fff3e0; border-color: #f57c00; color: #f57c00; }



.effectiveness-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 25px;
    overflow: hidden;
}

.effectiveness-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.effectiveness-item:last-child {
    border-bottom: none;
}

.effectiveness-dept {
    font-weight: 500;
}

.effectiveness-rate {
    display: flex;
    align-items: center;
    gap: 10px;
}

.effectiveness-progress {
    width: 150px;
    height: 8px;
    background-color: #eee;
    border-radius: 4px;
    overflow: hidden;
}

.effectiveness-bar {
    height: 100%;
    background-color: var(--primary-color);
}

.effectiveness-value {
    font-weight: 600;
    color: var(--primary-color);
}

.tippy-box {
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    font-size: 14px;
}

.tippy-arrow {
    color: rgba(0, 0, 0, 0.8);
}

/* Calendar Styling */
.calendar-container {
    height: auto;
    min-height: 500px;
    background-color: #fff;
    border-radius: var(--border-radius);
    overflow: visible;
}

.fc-theme-standard .fc-scrollgrid {
    border: 1px solid #eee;
}

/* Remove scrollbars */
.fc-scroller {
    overflow: visible !important;
    height: auto !important;
}

/* Fix table layout */
.fc .fc-scrollgrid-liquid {
    height: auto !important;
}

/* Improve day cell appearance */
.fc-daygrid-day {
    min-height: 80px;
}

.fc .fc-daygrid-day.fc-day-today {
    background-color: var(--primary-light);
}

.fc .fc-button-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.fc .fc-button-primary:hover {
    background-color: #900000;
    border-color: #900000;
}

.fc .fc-col-header-cell-cushion {
    font-weight: 600;
    color: var(--light-gray);
    text-decoration: none;
}

.fc .fc-daygrid-day-number {
    color: var(--secondary-color);
    font-weight: 500;
    padding: 8px;
}

a.fc .fc-daygrid-day-number {
    text-decoration: none;
}

.fc-event {
    border-radius: 4px;
    padding: 2px 4px;
    margin-bottom: 2px;
    border: none;
    font-size: 12px;
    cursor: pointer;
    transition: transform 0.2s ease;
    text-decoration: none;
    color: black;
}
.fc-event-title {
    color: black;
}
.fc-event:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Time display in events */
.fc-event-time {
    font-weight: bold;
    font-size: 0.85em;
    opacity: 0.9;
    margin-right: 4px;
    color: #333;
    display: inline-block !important;
}

/* Improve time grid view */
.fc-timegrid-event {
    padding: 4px 6px !important;
}

.fc-timegrid-event .fc-event-time {
    font-weight: bold;
    margin-bottom: 2px;
    font-size: 0.9em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.fc-timegrid-event .fc-event-title {
    font-size: 0.85em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Ensure time is always visible in month view */
.fc-daygrid-event .fc-event-time {
    display: inline-block !important;
    font-weight: bold;
    margin-right: 4px;
}

/* Improve list view time display */
.fc-list-event-time {
    width: 110px !important;
    font-weight: 500 !important;
    color: #333 !important;
}

/* Styling for multi-day event markers */
.multi-day-start {
    border-radius: 4px !important;
    margin: 1px 0 !important;
    position: relative !important;
    border-left-width: 4px !important;
}

.multi-day-start::before {
    content: "Mulai" !important;
    position: absolute !important;
    top: -18px !important;
    left: 0 !important;
    font-size: 10px !important;
    background-color: #28a745 !important;
    color: black     !important;
    font-weight: bold!important;
    padding: 1px 5px !important;
    border-radius: 3px !important;
    z-index: 5 !important;
}

.multi-day-end {
    border-radius: 4px !important;
    margin: 1px 0 !important;
    position: relative !important;
    border-left-width: 4px !important;
}

.multi-day-end::before {
    content: "Selesai" !important;
    position: absolute !important;
    top: -18px !important;
    left: 0 !important;
    font-size: 10px !important;
    background-color: #dc3545 !important;
    color: black !important;
    font-weight: bold !important;
    padding: 1px 5px !important;
    border-radius: 3px !important;
    z-index: 5 !important;
}

/* Improve event title display */
.fc-event-title {
    font-weight: 500 !important;
    padding: 0 4px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* Ensure proper spacing */
.fc-daygrid-event-harness {
    margin-top: 1px !important;
    margin-bottom: 1px !important;
}

/* Fix for multi-day events in FullCalendar */
.fc-dayGridMonth-view .fc-daygrid-day-events {
    position: relative;
    min-height: 1.7em;
}

/* Ensure continuous display of multi-day events */
.fc-h-event {
    border: none !important;
}

/* Fix for event continuity */
.fc-daygrid-block-event .fc-event-main {
    padding: 2px 4px !important;
}

/* Ensure event title is visible */
.fc-daygrid-block-event .fc-event-title {
    font-weight: 500 !important;
    padding: 0 4px !important;
}

/* Additional styling for multi-day event markers */
.multi-day-start .fc-event-title {
    padding-left: 5px !important;
    font-weight: bold !important;
    position: relative !important;
}

.multi-day-start .fc-event-title::after {
    content: " (Mulai)" !important;
    font-size: 0.85em !important;
    color: #28a745 !important;
    font-weight: bold !important;
}

.multi-day-end .fc-event-title {
    padding-left: 5px !important;
    font-weight: bold !important;
    position: relative !important;
}

.multi-day-end .fc-event-title::after {
    content: " (Selesai)" !important;
    font-size: 0.85em !important;
    color: #dc3545 !important;
    font-weight: bold !important;
}

.fc-event-pending {
    background-color: #fff3e0 !important;
    color: #f57c00 !important;
    border-left: 3px solid #f57c00 !important;
}

.fc-event-approved {
    background-color: #e3f2fd !important;
    color: #1976d2 !important;
    border-left: 3px solid #1976d2 !important;
}

.fc-event-completed {
    background-color: #e8f5e9 !important;
    color: #2e7d32 !important;
    border-left: 3px solid #2e7d32 !important;
}

.fc-event-rejected {
    background-color: #ffebee !important;
    color: #c62828 !important;
    border-left: 3px solid #c62828 !important;
}

/* Multi-day event styling for start marker */
.multi-day-start.fc-event-pending {
    background-color: #fff3e0 !important;
    border-left: 4px solid #f57c00 !important;
}

.multi-day-start.fc-event-approved {
    background-color: #e3f2fd !important;
    border-left: 4px solid #1976d2 !important;
}

.multi-day-start.fc-event-completed {
    background-color: #e8f5e9 !important;
    border-left: 4px solid #2e7d32 !important;
}

.multi-day-start.fc-event-rejected {
    background-color: #ffebee !important;
    border-left: 4px solid #c62828 !important;
}

/* Multi-day event styling for end marker */
.multi-day-end.fc-event-pending {
    background-color: #fff3e0 !important;
    border-left: 4px solid #f57c00 !important;
}

.multi-day-end.fc-event-approved {
    background-color: #e3f2fd !important;
    border-left: 4px solid #1976d2 !important;
}

.multi-day-end.fc-event-completed {
    background-color: #e8f5e9 !important;
    border-left: 4px solid #2e7d32 !important;
}

.multi-day-end.fc-event-rejected {
    background-color: #ffebee !important;
    border-left: 4px solid #c62828 !important;
}

/* List view styling */
.fc-list-day-cushion {
    background-color: var(--primary-light) !important;
}

.fc-list-event:hover td {
    background-color: rgba(0, 0, 0, 0.05) !important;
}

.fc-list-event-title a {
    color: var(--secondary-color) !important;
    font-weight: 500;
    text-decoration: none !important;
}

.fc-list-event-dot {
    display: none !important;
}

.fc-list-event-time {
    width: 120px;
    font-weight: 500;
}

.fc-list-day-text {
    color: var(--light-gray) !important;
    font-weight: 600 !important;
    text-decoration: none !important;
}

.fc-list-day-side-text {
    color: var(--light-gray) !important;
    font-weight: 500 !important;
    text-decoration: none !important;
}

/* Custom list event markers for status */
.fc-list-event.fc-event-pending td:first-child {
    border-left: 4px solid #f57c00 !important;
}

.fc-list-event.fc-event-approved td:first-child {
    border-left: 4px solid #1976d2 !important;
}

.fc-list-event.fc-event-completed td:first-child {
    border-left: 4px solid #2e7d32 !important;
}

.fc-list-event.fc-event-rejected td:first-child {
    border-left: 4px solid #c62828 !important;
}

/* Badge styling for list view */
.fc-list-event-title .badge {
    font-size: 11px;
    padding: 3px 8px;
    margin-left: 8px;
    font-weight: 500;
}

/* Fix list view table */
.fc-list {
    border: none !important;
}

.fc-list-table {
    border-collapse: separate;
    border-spacing: 0 2px;
}

/* Training type indicators */
.fc-event::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.fc-event[data-type="inhouse"]::before {
    background-color: #4CAF50;
}

.fc-event[data-type="outhouse"]::before {
    background-color: #2196F3;
}

.fc-event[data-type="hybrid"]::before {
    background-color: #9C27B0;
}

.calendar-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.calendar-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
    padding: 15px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.legend-item {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.legend-color {
    width: 12px;
    height: 12px;
    margin-right: 5px;
    border-radius: 2px;
}

.calendar-sidebar {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 15px;
    box-shadow: var(--box-shadow);
    height: 100%;
}

.mini-calendar {
    margin-bottom: 20px;
}

.calendar-filters {
    margin-top: 20px;
}

.calendar-filters h6 {
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.filter-group {
    margin-bottom: 15px;
}

.filter-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.filter-checkbox input {
    margin-right: 8px;
}

.event-modal .modal-content {
    border-radius: 10px;
    overflow: hidden;
}

.event-modal .modal-header {
    background-color: var(--primary-color);
    color: white;
    border-bottom: none;
}

.event-modal .modal-title {
    font-weight: 600;
}

.event-modal .modal-body {
    padding: 20px;
}

.event-modal .event-detail {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.event-modal .event-detail i {
    margin-right: 10px;
    color: var(--primary-color);
    width: 20px;
    text-align: center;
}

.event-modal .event-detail-label {
    font-weight: 600;
    margin-right: 10px;
    min-width: 100px;
}

.event-modal .modal-footer {
    border-top: 1px solid #eee;
    padding: 15px 20px;
}

@media (max-width: 768px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .dashboard-header h1 {
        font-size: 24px;
    }
}

@media print {
    .no-print {
        display: none !important;
    }

    .dashboard-container {
        margin: 0;
        padding: 0;
    }

    .card {
        box-shadow: none;
        margin-bottom: 15px;
        break-inside: avoid;
    }

    .card-header {
        border-bottom: 1px solid #ddd;
    }

    .chart-container {
        height: 300px;
        break-inside: avoid;
    }
}
h5 {
    color: white !important;
}
</style>

<body>
<?php include '../config/navbar.php'; ?>
<div class="jarak"></div>

<div class="container-fluid dashboard-container">
    <div class="dashboard-header">
        <div>
            <h1><i class="fas fa-chart-line"></i> Dashboard Laporan Training</h1>
            <p class="text-muted">Periode: <?= date('d M Y', strtotime($start_date)) ?> - <?= date('d M Y', strtotime($end_date)) ?></p>
        </div>

        <?php if ($updated_trainings > 0): ?>
        <!-- Notification for auto-updated training status -->
        <div class="auto-update-notification">
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <strong><?= $updated_trainings ?> training</strong> telah otomatis diubah statusnya menjadi "Completed" karena tanggal training sudah berlalu.
                <button type="button" class="btn-close float-end" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
        <?php endif; ?>
        <div class="view-toggle no-print">
            <a href="?view=dashboard&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>" class="<?= $view_mode == 'dashboard' ? 'active' : '' ?>">
                <i class="fas fa-th-large"></i> Dashboard
            </a>
            <a href="?view=table&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>" class="<?= $view_mode == 'table' ? 'active' : '' ?>">
                <i class="fas fa-table"></i> Tabel
            </a>
            <a href="?view=calendar&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>" class="<?= $view_mode == 'calendar' ? 'active' : '' ?>">
                <i class="fas fa-calendar-alt"></i> Kalender
            </a>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="card filter-card no-print">
        <div class="card-header">
            <h5><i class="fas fa-filter"></i> Filter Laporan</h5>
            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div class="collapse show" id="filterCollapse">
            <div class="card-body">
                <form method="GET" action="" id="filterForm" class="row">
                    <input type="hidden" name="view" value="<?= $view_mode ?>">
                    <div class="col-md-3 mb-3">
                        <label for="date_range">Rentang Tanggal:</label>
                        <div class="input-group">
                            <input type="text" id="date_range" class="form-control" readonly>
                            <input type="hidden" id="start_date" name="start_date" value="<?= $start_date ?>">
                            <input type="hidden" id="end_date" name="end_date" value="<?= $end_date ?>">
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="department">Departemen:</label>
                        <select id="department" name="department" class="form-control">
                            <option value="">Semua Departemen</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?= htmlspecialchars($dept) ?>" <?= $dept_filter === $dept ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($dept) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="training_type">Jenis Training:</label>
                        <select id="training_type" name="training_type" class="form-control">
                            <option value="">Semua Jenis</option>
                            <?php foreach ($training_types as $type): ?>
                                <option value="<?= htmlspecialchars($type) ?>" <?= $training_type === $type ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($type) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="status">Status:</label>
                        <select id="status" name="status" class="form-control">
                            <option value="">Semua Status</option>
                            <?php foreach ($statuses as $status): ?>
                                <option value="<?= htmlspecialchars($status) ?>" <?= $status_filter === $status ? 'selected' : '' ?>>
                                    <?= ucfirst(htmlspecialchars($status)) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary mr-2">
                            <i class="fas fa-search"></i> Terapkan Filter
                        </button>
                        <a href="reports_enhanced.php" class="btn btn-outline-secondary">
                            <i class="fas fa-redo"></i> Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php if ($view_mode == 'dashboard' || $view_mode == ''): ?>

    <!-- Statistics Overview -->
    <div class="row">
        <div class="col-12">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-chalkboard-teacher"></i></div>
                    <h3>Total Training</h3>
                    <div class="number" id="total-trainings"><?= $stats['total_trainings'] ?></div>
                    <div class="cost">Total Biaya: Rp <span id="total-cost"><?= number_format($stats['total_cost'], 0, ',', '.') ?></span></div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-users"></i></div>
                    <h3>Total Peserta</h3>
                    <div class="number" id="total-participants"><?= $stats['total_participants'] ?></div>
                    <div class="cost">Dari <?= $stats['total_departments'] ?> Departemen</div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-check-circle"></i></div>
                    <h3>Completed</h3>
                    <div class="number" id="completed-trainings"><?= $stats['completed'] ?></div>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar"
                             style="width: <?= ($stats['total_trainings'] > 0) ? ($stats['completed'] / $stats['total_trainings'] * 100) : 0 ?>%"
                             aria-valuenow="<?= $stats['completed'] ?>" aria-valuemin="0" aria-valuemax="<?= $stats['total_trainings'] ?>">
                        </div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-clock"></i></div>
                    <h3>Pending</h3>
                    <div class="number" id="pending-trainings"><?= $stats['pending'] ?></div>
                    <div class="progress">
                        <div class="progress-bar bg-warning" role="progressbar"
                             style="width: <?= ($stats['total_trainings'] > 0) ? ($stats['pending'] / $stats['total_trainings'] * 100) : 0 ?>%"
                             aria-valuenow="<?= $stats['pending'] ?>" aria-valuemin="0" aria-valuemax="<?= $stats['total_trainings'] ?>">
                        </div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-thumbs-up"></i></div>
                    <h3>Approved</h3>
                    <div class="number" id="approved-trainings"><?= $stats['approved'] ?></div>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar"
                             style="width: <?= ($stats['total_trainings'] > 0) ? ($stats['approved'] / $stats['total_trainings'] * 100) : 0 ?>%"
                             aria-valuenow="<?= $stats['approved'] ?>" aria-valuemin="0" aria-valuemax="<?= $stats['total_trainings'] ?>">
                        </div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-times-circle"></i></div>
                    <h3>Rejected</h3>
                    <div class="number" id="rejected-trainings"><?= $stats['rejected'] ?></div>
                    <div class="progress">
                        <div class="progress-bar bg-danger" role="progressbar"
                             style="width: <?= ($stats['total_trainings'] > 0) ? ($stats['rejected'] / $stats['total_trainings'] * 100) : 0 ?>%"
                             aria-valuenow="<?= $stats['rejected'] ?>" aria-valuemin="0" aria-valuemax="<?= $stats['total_trainings'] ?>">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Grid -->
    <div class="row">
        <div class="col-12">
            <div class="charts-grid">
                <!-- Monthly Trends Chart -->
                <div class="chart-container">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line"></i> Tren Training Bulanan</h5>
                    </div>
                    <div class="card-body">
                        <div id="monthlyTrendsChart" style="height: 320px;"></div>
                    </div>
                </div>

                <!-- Department Distribution Chart -->
                <div class="chart-container">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> Distribusi Training per Departemen</h5>
                    </div>
                    <div class="card-body">
                        <div id="deptDistributionChart" style="height: 320px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Charts Row -->
    <div class="row">
        <div class="col-12">
            <div class="charts-grid">
                <!-- Training Type Distribution -->
                <div class="chart-container">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie"></i> Distribusi Jenis Training</h5>
                    </div>
                    <div class="card-body">
                        <div id="trainingTypeChart" style="height: 320px;"></div>
                    </div>
                </div>

                <!-- Training Status Distribution -->
                <div class="chart-container">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie"></i> Status Training</h5>
                    </div>
                    <div class="card-body">
                        <div id="statusDistributionChart" style="height: 320px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Training Effectiveness -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-award"></i> Efektivitas Training per Departemen</h5>
                </div>
                <div class="card-body">
                    <div class="effectiveness-card">
                        <?php foreach ($effectiveness_data as $index => $dept):
                            if ($index >= 5) break; // Show only top 5
                        ?>
                            <div class="effectiveness-item">
                                <div class="effectiveness-dept"><?= htmlspecialchars($dept['departemen']) ?></div>
                                <div class="effectiveness-rate">
                                    <div class="effectiveness-progress">
                                        <div class="effectiveness-bar" style="width: <?= round($dept['completion_rate']) ?>%"></div>
                                    </div>
                                    <div class="effectiveness-value"><?= round($dept['completion_rate']) ?>%</div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Participants -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-trophy"></i> Top Participants</h5>
                </div>
                <div class="card-body">
                    <div class="top-performers">
                        <?php foreach ($top_participants as $participant): ?>
                            <div class="performer-card">
                                <div class="name"><?= htmlspecialchars($participant['full_name']) ?></div>
                                <div class="count"><?= $participant['training_count'] ?> training</div>
                                <div class="text-muted"><?= $participant['completed_count'] ?> completed</div>
                                <div class="progress mt-2">
                                    <div class="progress-bar" role="progressbar"
                                         style="width: <?= ($participant['training_count'] > 0) ? ($participant['completed_count'] / $participant['training_count'] * 100) : 0 ?>%"
                                         aria-valuenow="<?= $participant['completed_count'] ?>" aria-valuemin="0" aria-valuemax="<?= $participant['training_count'] ?>">
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($view_mode == 'table'): ?>
    <!-- Department Statistics Table -->
    <div class="row">
        <div class="col-12">
            <div class="card table-card">
                <div class="card-header">
                    <h5><i class="fas fa-building"></i> Statistik per Departemen</h5>
                    <div class="d-flex">
                        <div class="dropdown mr-2">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <div class="dropdown-menu dropdown-menu-end" aria-labelledby="exportDropdown">
                                <a class="dropdown-item" href="#" id="exportDeptCSV">
                                    <i class="fas fa-file-csv"></i> Export CSV
                                </a>
                                <a class="dropdown-item" href="#" id="exportDeptExcel">
                                    <i class="fas fa-file-excel"></i> Export Excel
                                </a>
                                <a class="dropdown-item" href="#" id="printDeptTable">
                                    <i class="fas fa-print"></i> Print
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="deptTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Departemen</th>
                                    <th>Total Training</th>
                                    <th>Training Selesai</th>
                                    <th>Persentase Selesai</th>
                                    <th>Total Peserta</th>
                                    <th>Total Biaya</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($dept_stats as $dept): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($dept['departemen']) ?></td>
                                        <td><?= $dept['training_count'] ?></td>
                                        <td><?= $dept['completed_count'] ?></td>
                                        <td>
                                            <?= round(($dept['completed_count'] / $dept['training_count']) * 100, 1) ?>%
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar"
                                                     style="width: <?= ($dept['completed_count'] / $dept['training_count'] * 100) ?>%"
                                                     aria-valuenow="<?= $dept['completed_count'] ?>" aria-valuemin="0" aria-valuemax="<?= $dept['training_count'] ?>">
                                                </div>
                                            </div>
                                        </td>
                                        <td><?= $dept['participant_count'] ?></td>
                                        <td>Rp <?= number_format($dept['dept_cost'], 0, ',', '.') ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr class="table-active">
                                    <th>Total</th>
                                    <th><?= $stats['total_trainings'] ?></th>
                                    <th><?= $stats['completed'] ?></th>
                                    <th>
                                        <?= ($stats['total_trainings'] > 0) ? round(($stats['completed'] / $stats['total_trainings'] * 100), 1) : 0 ?>%
                                    </th>
                                    <th><?= $stats['total_participants'] ?></th>
                                    <th>Rp <?= number_format($stats['total_cost'], 0, ',', '.') ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Training List Table -->
    <div class="row">
        <div class="col-12">
            <div class="card table-card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> Daftar Training</h5>
                    <div class="d-flex">
                        <div class="dropdown mr-2">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-sort"></i> Sort
                            </button>
                            <div class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                                <a class="dropdown-item <?= $sort_by == 'start_date' && $sort_order == 'DESC' ? 'active' : '' ?>"
                                   href="?view=table&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>&sort_by=start_date&sort_order=DESC">
                                   Tanggal (Terbaru)
                                </a>
                                <a class="dropdown-item <?= $sort_by == 'start_date' && $sort_order == 'ASC' ? 'active' : '' ?>"
                                   href="?view=table&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>&sort_by=start_date&sort_order=ASC">
                                   Tanggal (Terlama)
                                </a>
                                <a class="dropdown-item <?= $sort_by == 'training_cost' && $sort_order == 'DESC' ? 'active' : '' ?>"
                                   href="?view=table&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>&sort_by=training_cost&sort_order=DESC">
                                   Biaya (Tertinggi)
                                </a>
                                <a class="dropdown-item <?= $sort_by == 'training_cost' && $sort_order == 'ASC' ? 'active' : '' ?>"
                                   href="?view=table&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>&sort_by=training_cost&sort_order=ASC">
                                   Biaya (Terendah)
                                </a>
                            </div>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="exportTrainingDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <div class="dropdown-menu dropdown-menu-end" aria-labelledby="exportTrainingDropdown">
                                <a class="dropdown-item" href="#" id="exportTrainingCSV">
                                    <i class="fas fa-file-csv"></i> Export CSV
                                </a>
                                <a class="dropdown-item" href="export_excel.php?start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>">
                                    <i class="fas fa-file-excel"></i> Export Excel
                                </a>
                                <a class="dropdown-item" href="export_pdf.php?start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>">
                                    <i class="fas fa-file-pdf"></i> Export PDF
                                </a>
                                <a class="dropdown-item" href="#" id="printTrainingTable">
                                    <i class="fas fa-print"></i> Print
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="trainingTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Topik</th>
                                    <th>Nama</th>
                                    <th>Departemen</th>
                                    <th>Jenis</th>
                                    <th>Tanggal</th>
                                    <th>Status</th>
                                    <th>Biaya</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_trainings as $training): ?>
                                    <tr>
                                        <td><?= $training['id'] ?></td>
                                        <td data-bs-toggle="tooltip" title="<?= htmlspecialchars($training['training_topic']) ?>">
                                            <?= htmlspecialchars(substr($training['training_topic'], 0, 30)) ?><?= strlen($training['training_topic']) > 30 ? '...' : '' ?>
                                        </td>
                                        <td><?= htmlspecialchars($training['full_name']) ?></td>
                                        <td><?= htmlspecialchars($training['departemen']) ?></td>
                                        <td><?= htmlspecialchars($training['training_type'] ?? 'N/A') ?></td>
                                        <td><?= date('d/m/Y', strtotime($training['start_date'])) ?></td>
                                        <td>
                                            <span class="status-badge status-<?= strtolower($training['status']) ?>">
                                                <?= ucfirst($training['status']) ?>
                                            </span>
                                        </td>
                                        <td>Rp <?= empty($training['training_cost']) ? '-' : number_format((float)$training['training_cost'], 0, ',', '.') ?></td>
                                        <td>
                                            <a href="../view/detail_training.php?id=<?= $training['id'] ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Lihat Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <li class="page-item <?= $page <= 1 ? 'disabled' : '' ?>">
                                <a class="page-link" href="?view=table&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>&sort_by=<?= $sort_by ?>&sort_order=<?= $sort_order ?>&page=<?= $page-1 ?>" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>

                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?view=table&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>&sort_by=<?= $sort_by ?>&sort_order=<?= $sort_order ?>&page=<?= $i ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <li class="page-item <?= $page >= $total_pages ? 'disabled' : '' ?>">
                                <a class="page-link" href="?view=table&start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>&sort_by=<?= $sort_by ?>&sort_order=<?= $sort_order ?>&page=<?= $page+1 ?>" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                    <?php endif; ?>

                    <!-- Items per page selector -->
                    <div class="d-flex justify-content-center mt-3">
                        <div class="form-inline">
                            <label for="per_page" class="mr-2">Tampilkan:</label>
                            <select id="per_page" class="form-control form-control-sm" onchange="changeItemsPerPage(this.value)">
                                <option value="10" <?= $items_per_page == 10 ? 'selected' : '' ?>>10</option>
                                <option value="25" <?= $items_per_page == 25 ? 'selected' : '' ?>>25</option>
                                <option value="50" <?= $items_per_page == 50 ? 'selected' : '' ?>>50</option>
                                <option value="100" <?= $items_per_page == 100 ? 'selected' : '' ?>>100</option>
                            </select>
                            <span class="ml-2">item per halaman</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($view_mode == 'calendar'): ?>
    <!-- Calendar View -->
    <div class="row">
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-alt"></i> Kalender Training</h5>
                    <div class="btn-group">
                        <button type="button" id="calendarPrev" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button type="button" id="calendarToday" class="btn btn-sm btn-outline-secondary">
                            Hari Ini
                        </button>
                        <button type="button" id="calendarNext" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <div class="dropdown ms-2">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="viewDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-eye"></i> Tampilan
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="viewDropdown">
                                <li><a class="dropdown-item" href="#" id="viewMonth">Bulan</a></li>
                                <li><a class="dropdown-item" href="#" id="viewWeek">Minggu</a></li>
                                <li><a class="dropdown-item" href="#" id="viewDay">Hari</a></li>
                                <li><a class="dropdown-item" href="#" id="viewList">Daftar</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="calendar-container" id="calendar"></div>
                </div>
            </div>

            <!-- Calendar Legend -->
            <div class="calendar-legend">
                <div class="d-flex flex-wrap gap-3 mb-2">
                    <strong>Status:</strong>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #fff3e0; border-left: 3px solid #f57c00;"></div>
                        <span>Pending</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #e3f2fd; border-left: 3px solid #1976d2;"></div>
                        <span>Approved</span>
                        <small class="text-muted">(Otomatis menjadi Completed setelah tanggal training berlalu)</small>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #e8f5e9; border-left: 3px solid #2e7d32;"></div>
                        <span>Completed</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #ffebee; border-left: 3px solid #c62828;"></div>
                        <span>Rejected</span>
                    </div>
                </div>

                <div class="d-flex flex-wrap gap-3">
                    <strong>Jenis:</strong>
                    <div class="legend-item">
                        <div class="legend-color" style="position: relative;">
                            <div style="position: absolute; top: 3px; left: 3px; width: 8px; height: 8px; border-radius: 50%; background-color: #4CAF50;"></div>
                        </div>
                        <span>Inhouse</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="position: relative;">
                            <div style="position: absolute; top: 3px; left: 3px; width: 8px; height: 8px; border-radius: 50%; background-color: #2196F3;"></div>
                        </div>
                        <span>Outhouse</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="position: relative;">
                            <div style="position: absolute; top: 3px; left: 3px; width: 8px; height: 8px; border-radius: 50%; background-color: #9C27B0;"></div>
                        </div>
                        <span>Hybrid</span>
                    </div>
                    <div class="ms-auto">
                        <span class="text-muted"><i class="fas fa-info-circle"></i> Klik pada event untuk melihat detail</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="calendar-sidebar">
                <h5 class="mb-3"><i class="fas fa-sliders-h"></i> Filter Kalender</h5>

                <div class="filter-group">
                    <h6>Status</h6>
                    <div class="filter-checkbox">
                        <input type="checkbox" id="filter-pending" checked>
                        <label for="filter-pending">Pending</label>
                    </div>
                    <div class="filter-checkbox">
                        <input type="checkbox" id="filter-approved" checked>
                        <label for="filter-approved">Approved</label>
                        <small class="d-block text-muted ms-4">(Otomatis menjadi Completed setelah tanggal training berlalu)</small>
                    </div>
                    <div class="filter-checkbox">
                        <input type="checkbox" id="filter-completed" checked>
                        <label for="filter-completed">Completed</label>
                    </div>
                    <div class="filter-checkbox">
                        <input type="checkbox" id="filter-rejected" checked>
                        <label for="filter-rejected">Rejected</label>
                    </div>
                </div>

                <div class="filter-group">
                    <h6>Jenis Training</h6>
                    <div class="filter-checkbox">
                        <input type="checkbox" id="filter-type-inhouse" class="training-type-filter" data-type="inhouse" checked>
                        <label for="filter-type-inhouse">Inhouse</label>
                    </div>
                    <div class="filter-checkbox">
                        <input type="checkbox" id="filter-type-outhouse" class="training-type-filter" data-type="outhouse" checked>
                        <label for="filter-type-outhouse">Outhouse</label>
                    </div>
                    <div class="filter-checkbox">
                        <input type="checkbox" id="filter-type-hybrid" class="training-type-filter" data-type="hybrid" checked>
                        <label for="filter-type-hybrid">Hybrid</label>
                    </div>
                    <?php if (count($training_types) > 3): ?>
                    <div class="mt-2">
                        <a href="#" id="showMoreTypes" class="text-primary"><small>Lihat jenis lainnya...</small></a>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="filter-group">
                    <h6>Statistik Periode</h6>
                    <div class="small mb-2">
                        <div><strong>Total Training:</strong> <span id="period-total"><?= $stats['total_trainings'] ?></span></div>
                        <div><strong>Completed:</strong> <span id="period-completed"><?= $stats['completed'] ?></span></div>
                        <div><strong>Pending:</strong> <span id="period-pending"><?= $stats['pending'] ?></span></div>
                        <div><strong>Total Biaya:</strong> <span id="period-cost">Rp <?= number_format($stats['total_cost'], 0, ',', '.') ?></span></div>
                    </div>
                </div>

                <div class="mt-4">
                    <button id="resetFilters" class="btn btn-sm btn-outline-secondary w-100">
                        <i class="fas fa-redo"></i> Reset Filter
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Trainings -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-clock"></i> Training Mendatang</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Topik</th>
                                    <th>Peserta</th>
                                    <th>Departemen</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="upcoming-trainings">
                                <?php
                                $today = date('Y-m-d');
                                $upcoming = array_filter($calendar_data, function($event) use ($today) {
                                    return $event['start_date'] >= $today;
                                });
                                $upcoming = array_slice($upcoming, 0, 5);

                                foreach ($upcoming as $event):
                                ?>
                                <tr>
                                    <td>
                                        <?php
                                        // Determine which date to display
                                        $displayDate = $event['start_date'];
                                        if (!empty($event['start_date'])) {
                                            $displayDate = $event['start_date_fixed'];
                                        } elseif (!empty($event['start_date_start']) && !empty($event['start_date_end'])) {
                                            $displayDate = $event['start_date_start'] . ' s/d ' . $event['start_date_end'];
                                        }

                                        // Check if this is a multi-day event
                                        $isMultiDay = (!empty($event['start_date']) && !empty($event['start_date_end']) &&
                                                      $event['start_date'] !== $event['date_end']);

                                        // Format the date
                                        if ($isMultiDay) {
                                            echo '<span class="badge bg-info mb-1">Multi-day</span><br>';
                                            echo date('d/m/Y', strtotime($event['start_date_start'])) . ' s/d<br>' .
                                                 date('d/m/Y', strtotime($event['start_date_end']));
                                        } else {
                                            echo date('d/m/Y', strtotime($displayDate));
                                        }
                                        ?>
                                        <div class="small text-muted">
                                            <i class="fas fa-clock"></i>
                                            <?php
                                            $timeStart = !empty($event['training_time_start']) ? substr($event['training_time_start'], 0, 5) : '08:00';
                                            $timeEnd = !empty($event['training_time_end']) ? substr($event['training_time_end'], 0, 5) : '16:00';
                                            echo $timeStart . ' - ' . $timeEnd;
                                            ?>
                                        </div>
                                    </td>
                                    <td><?= htmlspecialchars(substr($event['training_topic'], 0, 30)) ?><?= strlen($event['training_topic']) > 30 ? '...' : '' ?></td>
                                    <td><?= htmlspecialchars($event['full_name']) ?></td>
                                    <td><?= htmlspecialchars($event['departemen']) ?></td>
                                    <td><span class="status-badge status-<?= strtolower($event['status']) ?>"><?= ucfirst($event['status']) ?></span></td>
                                    <td>
                                        <a href="detail_training.php?id=<?= $event['id'] ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Lihat Detail">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php if (empty($upcoming)): ?>
                                <tr>
                                    <td colspan="6" class="text-center">Tidak ada training mendatang dalam periode ini</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Export Buttons
    <div class="row no-print">
        <div class="col-12">
            <div class="export-buttons">
                <a href="export_excel.php?start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>" class="export-btn excel">
                    <i class="fas fa-file-excel"></i> Export Excel
                </a>
                <a href="export_pdf.php?start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>" class="export-btn pdf">
                    <i class="fas fa-file-pdf"></i> Export PDF
                </a>
                <button onclick="window.print()" class="export-btn print">
                    <i class="fas fa-print"></i> Print
                </button>
                <a href="reports.php?start_date=<?= $start_date ?>&end_date=<?= $end_date ?>&department=<?= urlencode($dept_filter) ?>&training_type=<?= urlencode($training_type) ?>&status=<?= urlencode($status_filter) ?>" class="export-btn" style="background-color: #6c757d;">
                    <i class="fas fa-sync-alt"></i> Versi Lama
                </a>
            </div>
        </div>
    </div> -->
</div>

<?php include '../config/footer.php'; ?>

<script>
// Initialize DateRangePicker
$(function() {
    $('#date_range').daterangepicker({
        startDate: moment('<?= $start_date ?>'),
        endDate: moment('<?= $end_date ?>'),
        locale: {
            format: 'DD/MM/YYYY'
        },
        ranges: {
           'Hari Ini': [moment(), moment()],
           'Kemarin': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
           '7 Hari Terakhir': [moment().subtract(6, 'days'), moment()],
           '30 Hari Terakhir': [moment().subtract(29, 'days'), moment()],
           'Bulan Ini': [moment().startOf('month'), moment().endOf('month')],
           'Bulan Lalu': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
           'Tahun Ini': [moment().startOf('year'), moment().endOf('year')],
           'Sepanjang Masa': [moment('2000-01-01'), moment('2099-12-31')]
        }
    }, function(start, end, label) {
        $('#start_date').val(start.format('YYYY-MM-DD'));
        $('#end_date').val(end.format('YYYY-MM-DD'));

        // Auto-submit form when a range is selected
        if (label) {
            $('#filterForm').submit();
        }
    });
});

// Initialize DataTables
$(document).ready(function() {
    if ($('#deptTable').length) {
        $('#deptTable').DataTable({
            paging: false,
            searching: false,
            info: false,
            responsive: true,
            order: [[1, 'desc']], // Sort by total training count by default
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.10.25/i18n/Indonesian.json'
            }
        });
    }

    if ($('#trainingTable').length) {
        $('#trainingTable').DataTable({
            paging: false, // We're using our own pagination
            searching: true,
            info: true,
            responsive: true,
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.10.25/i18n/Indonesian.json'
            }
        });
    }

    // Enable tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Initialize CountUp.js for animated numbers
    const options = {
        duration: 2,
        useEasing: true,
        useGrouping: true,
        separator: '.',
        decimal: ',',
    };

    if (document.getElementById('total-trainings')) {
        const totalTrainings = new CountUp('total-trainings', 0, <?= $stats['total_trainings'] ?>, 0, 2, options);
        totalTrainings.start();
    }

    if (document.getElementById('total-participants')) {
        const totalParticipants = new CountUp('total-participants', 0, <?= $stats['total_participants'] ?>, 0, 2, options);
        totalParticipants.start();
    }

    if (document.getElementById('completed-trainings')) {
        const completedTrainings = new CountUp('completed-trainings', 0, <?= $stats['completed'] ?>, 0, 2, options);
        completedTrainings.start();
    }

    if (document.getElementById('pending-trainings')) {
        const pendingTrainings = new CountUp('pending-trainings', 0, <?= $stats['pending'] ?>, 0, 2, options);
        pendingTrainings.start();
    }

    if (document.getElementById('approved-trainings')) {
        const approvedTrainings = new CountUp('approved-trainings', 0, <?= $stats['approved'] ?>, 0, 2, options);
        approvedTrainings.start();
    }

    if (document.getElementById('rejected-trainings')) {
        const rejectedTrainings = new CountUp('rejected-trainings', 0, <?= $stats['rejected'] ?>, 0, 2, options);
        rejectedTrainings.start();
    }

    // Enhanced tooltips with Tippy.js
    tippy('[data-toggle="tooltip"]', {
        theme: 'light',
        arrow: true,
        animation: 'scale',
        duration: 200
    });
});

// Function to change items per page
function changeItemsPerPage(value) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('per_page', value);
    urlParams.set('page', 1); // Reset to first page
    window.location.href = window.location.pathname + '?' + urlParams.toString();
}

// Prepare data for charts
const monthlyData = <?= json_encode($monthly_trends) ?>;
const deptData = <?= json_encode($dept_stats) ?>;
const trainingTypeData = <?= json_encode($training_type_stats) ?>;

// Monthly Trends Chart with ApexCharts
if (document.getElementById('monthlyTrendsChart')) {
    const monthlyTrendsOptions = {
        series: [{
            name: 'Total Training',
            data: monthlyData.map(item => parseInt(item.total_count))
        }, {
            name: 'Training Selesai',
            data: monthlyData.map(item => parseInt(item.completed_count))
        }],
        chart: {
            type: 'area',
            height: 320,
            toolbar: {
                show: false,
                tools: {
                    download: false,
                    selection: false,
                    zoom: false,
                    zoomin: false,
                    zoomout: false,
                    pan: false,
                    reset: false
                }
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        colors: ['#BF0000', '#28a745'],
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.3,
                stops: [0, 90, 100]
            }
        },
        xaxis: {
            categories: monthlyData.map(item => {
                const [year, month] = item.month.split('-');
                return `${month}/${year}`;
            }),
            title: {
                text: 'Bulan'
            }
        },
        yaxis: {
            title: {
                text: 'Jumlah Training'
            },
            min: 0,
            forceNiceScale: true
        },
        tooltip: {
            shared: true,
            intersect: false,
            y: {
                formatter: function(value, { seriesIndex, dataPointIndex, w }) {
                    if (seriesIndex === 0) {
                        const cost = parseInt(monthlyData[dataPointIndex].monthly_cost);
                        return `${value} training<br>Biaya: Rp ${cost.toLocaleString('id-ID')}`;
                    }
                    return `${value} training`;
                }
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right'
        }
    };

    const monthlyTrendsChart = new ApexCharts(document.querySelector("#monthlyTrendsChart"), monthlyTrendsOptions);
    monthlyTrendsChart.render();
}

// Department Distribution Chart
if (document.getElementById('deptDistributionChart')) {
    const deptDistributionOptions = {
        series: [{
            name: 'Total Training',
            data: deptData.map(item => parseInt(item.training_count))
        }, {
            name: 'Training Selesai',
            data: deptData.map(item => parseInt(item.completed_count))
        }],
        chart: {
            type: 'bar',
            height: 320,
            toolbar: {
                show: false
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '70%',
                borderRadius: 4,
                dataLabels: {
                    position: 'top'
                }
            }
        },
        dataLabels: {
            enabled: false
        },
        colors: ['#BF0000', '#28a745'],
        xaxis: {
            categories: deptData.map(item => item.departemen),
            title: {
                text: 'Departemen'
            },
            labels: {
                rotate: -45,
                rotateAlways: false,
                style: {
                    fontSize: '12px'
                }
            }
        },
        yaxis: {
            title: {
                text: 'Jumlah Training'
            },
            min: 0
        },
        tooltip: {
            shared: true,
            intersect: false,
            y: {
                formatter: function(value, { seriesIndex, dataPointIndex, w }) {
                    if (seriesIndex === 0) {
                        const cost = parseInt(deptData[dataPointIndex].dept_cost);
                        return `${value} training<br>Biaya: Rp ${cost.toLocaleString('id-ID')}`;
                    }
                    return `${value} training`;
                }
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right'
        }
    };

    const deptDistributionChart = new ApexCharts(document.querySelector("#deptDistributionChart"), deptDistributionOptions);
    deptDistributionChart.render();
}

// Training Type Distribution Chart
if (document.getElementById('trainingTypeChart')) {
    const trainingTypeOptions = {
        series: trainingTypeData.map(item => parseInt(item.type_count)),
        chart: {
            type: 'donut',
            height: 320,
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800,
                animateGradually: {
                    enabled: true,
                    delay: 150
                },
                dynamicAnimation: {
                    enabled: true,
                    speed: 350
                }
            }
        },
        labels: trainingTypeData.map(item => item.training_type),
        colors: ['#BF0000', '#28a745', '#1976d2', '#f57c00', '#9c27b0', '#607d8b', '#795548'],
        plotOptions: {
            pie: {
                donut: {
                    size: '50%',
                    labels: {
                        show: true,
                        total: {
                            show: true,
                            label: 'Total',
                            formatter: function(w) {
                                return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                            }
                        }
                    }
                }
            }
        },
        legend: {
            position: 'bottom',
            horizontalAlign: 'center'
        },
        tooltip: {
            y: {
                formatter: function(value, { seriesIndex, dataPointIndex, w }) {
                    const cost = parseInt(trainingTypeData[seriesIndex].type_cost);
                    return `${value} training<br>Biaya: Rp ${cost.toLocaleString('id-ID')}`;
                }
            }
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    height: 300
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    };

    const trainingTypeChart = new ApexCharts(document.querySelector("#trainingTypeChart"), trainingTypeOptions);
    trainingTypeChart.render();
}

// Status Distribution Chart
if (document.getElementById('statusDistributionChart')) {
    const statusData = [
        { status: 'Pending', count: <?= $stats['pending'] ?> },
        { status: 'Approved', count: <?= $stats['approved'] ?> },
        { status: 'Completed', count: <?= $stats['completed'] ?> },
        { status: 'Rejected', count: <?= $stats['rejected'] ?> }
    ];

    const statusOptions = {
        series: statusData.map(item => item.count),
        chart: {
            type: 'pie',
            height: 320,
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        labels: statusData.map(item => item.status),
        colors: ['#f57c00', '#1976d2', '#28a745', '#dc3545'],
        legend: {
            position: 'bottom',
            horizontalAlign: 'center'
        },
        plotOptions: {
            pie: {
                dataLabels: {
                    offset: -5
                }
            }
        },
        dataLabels: {
            formatter(val, opts) {
                const name = opts.w.globals.labels[opts.seriesIndex];
                return [name, Math.round(val) + '%'];
            }
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    height: 300
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    };

    const statusDistributionChart = new ApexCharts(document.querySelector("#statusDistributionChart"), statusOptions);
    statusDistributionChart.render();
}

// Initialize Calendar
if (document.getElementById('calendar')) {
    const calendarEl = document.getElementById('calendar');

    // Prepare events data
    <?php
    // Convert PHP array to JSON for safer JavaScript integration
    $jsEvents = [];
    foreach ($calendar_data as $event) {
        // Determine which date to use based on available data
        $startDate = $event['start_date'];
        $endDate = $event['start_date'];
        $displayDate = $event['start_date'];
        $isMultiDay = false;

        if (!empty($event['start_date'])) {
            // Single fixed date
            $startDate = $event['start_date'];
            $endDate = $event['start_date'];
            $displayDate = $event['start_date'];
        } elseif (!empty($event['start_date_start']) && !empty($event['date_end'])) {
            // Multi-day training
            $startDate = $event['start_date'];
            $endDate = $event['date_end'];
            $displayDate = $event['start_date'] . ' s/d ' . $event['date_end'];

            // Check if this is truly a multi-day event
            $start = new DateTime($startDate);
            $end = new DateTime($endDate);
            $interval = $start->diff($end);
            $isMultiDay = ($interval->days > 0);
        }

        // Default times if not provided
        $timeStart = !empty($event['training_time_start']) ? $event['training_time_start'] : '08:00:00';
        $timeEnd = !empty($event['training_time_end']) ? $event['training_time_end'] : '16:00:00';

        // For multi-day events, create two separate events (start and end)
        if ($isMultiDay) {
            // Create event for start date
            $jsEvents[] = [
                'title' => addslashes($event['training_topic']),
                'start' => $startDate . 'T' . $timeStart,
                'end' => $startDate . 'T' . $timeEnd,
                'allDay' => false,
                'id' => $event['id'],
                'classNames' => ['fc-event-' . strtolower($event['status']), 'multi-day-start'],
                'extendedProps' => [
                    'department' => addslashes($event['departemen']),
                    'participant' => addslashes($event['full_name']),
                    'status' => $event['status'],
                    'cost' => empty($event['training_cost']) ? 0 : $event['training_cost'],
                    'type' => addslashes($event['training_type'] ?? 'N/A'),
                    'displayDate' => $displayDate,
                    'timeStart' => $timeStart,
                    'timeEnd' => $timeEnd,
                    'displayTime' => substr($timeStart, 0, 5) . ' - ' . substr($timeEnd, 0, 5),
                    'place' => addslashes($event['training_place'] ?? ''),
                    'isMultiDay' => true,
                    'isStartDate' => true,
                    'isEndDate' => false,
                    'startDate' => $startDate,
                    'endDate' => $endDate
                ]
            ];

            // Create event for end date
            $jsEvents[] = [
                'title' => addslashes($event['training_topic']),
                'start' => $endDate . 'T' . $timeStart,
                'end' => $endDate . 'T' . $timeEnd,
                'allDay' => false,
                'id' => $event['id'] . '_end',
                'classNames' => ['fc-event-' . strtolower($event['status']), 'multi-day-end'],
                'extendedProps' => [
                    'department' => addslashes($event['departemen']),
                    'participant' => addslashes($event['full_name']),
                    'status' => $event['status'],
                    'cost' => empty($event['training_cost']) ? 0 : $event['training_cost'],
                    'type' => addslashes($event['training_type'] ?? 'N/A'),
                    'displayDate' => $displayDate,
                    'timeStart' => $timeStart,
                    'timeEnd' => $timeEnd,
                    'displayTime' => substr($timeStart, 0, 5) . ' - ' . substr($timeEnd, 0, 5),
                    'place' => addslashes($event['training_place'] ?? ''),
                    'isMultiDay' => true,
                    'isStartDate' => false,
                    'isEndDate' => true,
                    'startDate' => $startDate,
                    'endDate' => $endDate,
                    'originalId' => $event['id']
                ]
            ];
        } else {
            // For single-day events, include the time
            $jsEvents[] = [
                'title' => addslashes($event['training_topic']),
                'start' => $startDate . 'T' . $timeStart,
                'end' => $endDate . 'T' . $timeEnd,
                'allDay' => false,
                'id' => $event['id'],
                'classNames' => ['fc-event-' . strtolower($event['status'])],
                'extendedProps' => [
                    'department' => addslashes($event['departemen']),
                    'participant' => addslashes($event['full_name']),
                    'status' => $event['status'],
                    'cost' => empty($event['training_cost']) ? 0 : $event['training_cost'],
                    'type' => addslashes($event['training_type'] ?? 'N/A'),
                    'displayDate' => $displayDate,
                    'timeStart' => $timeStart,
                    'timeEnd' => $timeEnd,
                    'displayTime' => substr($timeStart, 0, 5) . ' - ' . substr($timeEnd, 0, 5),
                    'place' => addslashes($event['training_place'] ?? ''),
                    'isMultiDay' => false,
                    'startDate' => $startDate,
                    'endDate' => $endDate
                ]
            ];
        }
    }
    ?>

    // Create events array
    const allEvents = <?= json_encode($jsEvents) ?>;

    // Initialize calendar
    const calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: '',
            center: 'title',
            right: ''
        },
        locale: 'id',
        events: allEvents,
        height: 'auto', // Make calendar height fit content
        contentHeight: 'auto', // Ensure content fits without scrollbars
        dayMaxEvents: true, // Show "more" link when too many events
        displayEventTime: true, // Show event time for non-all-day events
        displayEventEnd: true, // Show event end time for non-all-day events
        forceEventDuration: true, // Ensure all events have a duration
        nextDayThreshold: '00:00:00', // Allow events to span multiple days
        eventDisplay: 'block', // Display events as blocks that span the full width
        eventTimeFormat: {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false // Use 24-hour format
        },
        slotLabelFormat: {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false // Use 24-hour format
        },
        views: {
            dayGrid: {
                dayMaxEventRows: 3
            },
            timeGrid: {
                dayMaxEventRows: 6,
                slotDuration: '00:30:00', // 30-minute slots
                slotLabelInterval: '01:00', // Show hour labels
                slotMinTime: '07:00:00', // Start at 7 AM
                slotMaxTime: '19:00:00'  // End at 7 PM
            },
            timeGridWeek: {
                dayHeaderFormat: { weekday: 'short', month: 'numeric', day: 'numeric', omitCommas: true }
            },
            timeGridDay: {
                dayHeaderFormat: { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' }
            },
            listMonth: {
                listDayFormat: { month: 'long', day: 'numeric' },
                listDayAltFormat: { weekday: 'long' },
                displayEventTime: true, // Show event time in list view
                eventTimeFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                },
                eventDidMount: function(info) {
                    // Add status class to list events
                    const status = info.event.extendedProps.status.toLowerCase();
                    const eventType = info.event.extendedProps.type.toLowerCase();

                    // Add status class
                    info.el.classList.add(`fc-event-${status}`);

                    // Add type indicator
                    const titleEl = info.el.querySelector('.fc-list-event-title');
                    if (titleEl) {
                        const typeIndicator = document.createElement('span');
                        typeIndicator.className = 'badge rounded-pill ms-2';

                        if (eventType === 'inhouse') {
                            typeIndicator.className += ' bg-success';
                            typeIndicator.textContent = 'Inhouse';
                        } else if (eventType === 'outhouse') {
                            typeIndicator.className += ' bg-primary';
                            typeIndicator.textContent = 'Outhouse';
                        } else if (eventType === 'hybrid') {
                            typeIndicator.className += ' bg-purple';
                            typeIndicator.style.backgroundColor = '#9C27B0';
                            typeIndicator.style.color = 'white';
                            typeIndicator.textContent = 'Hybrid';
                        } else {
                            typeIndicator.className += ' bg-secondary';
                            typeIndicator.textContent = eventType;
                        }

                        titleEl.appendChild(typeIndicator);
                    }
                }
            }
        },
        eventDidMount: function(info) {
            const eventType = info.event.extendedProps.type.toLowerCase();
            const status = info.event.extendedProps.status.toLowerCase();
            const view = info.view.type;

            // Add data-type attribute for CSS styling
            info.el.setAttribute('data-type', eventType);

            // Don't add tooltips to list view events
            if (view !== 'listMonth') {
                // Add tooltips to events
                const props = info.event.extendedProps;

                // Format date information based on whether it's a multi-day event
                let dateInfo;
                if (props.isMultiDay) {
                    if (props.isStartDate) {
                        dateInfo = `<br><span style="color: #28a745; font-weight: bold;"><i class="fas fa-play-circle"></i> MULAI: ${moment(props.startDate).format('DD MMM YYYY')}</span>
                                   <br><span style="color: #666;"><i class="fas fa-calendar-week"></i> Sampai: ${moment(props.endDate).format('DD MMM YYYY')}</span>`;
                    } else if (props.isEndDate) {
                        dateInfo = `<br><span style="color: #dc3545; font-weight: bold;"><i class="fas fa-stop-circle"></i> SELESAI: ${moment(props.endDate).format('DD MMM YYYY')}</span>
                                   <br><span style="color: #666;"><i class="fas fa-calendar-week"></i> Dari: ${moment(props.startDate).format('DD MMM YYYY')}</span>`;
                    }
                } else {
                    dateInfo = `<br><span style="color: #666;"><i class="fas fa-calendar-day"></i> ${moment(props.startDate).format('DD MMM YYYY')}</span>`;
                }

                const timeInfo = `<br><span style="color: #666;"><i class="fas fa-clock"></i> ${props.displayTime}</span>`;
                const placeInfo = props.place ?
                    `<br><span style="color: #666;"><i class="fas fa-map-marker-alt"></i> ${props.place}</span>` : '';

                tippy(info.el, {
                    content: `<div style="font-weight: bold;">${info.event.title}</div>
                              <span style="color: #666;"><i class="fas fa-tag"></i> ${eventType}</span>
                              ${dateInfo}
                              ${timeInfo}
                              ${placeInfo}`,
                    placement: 'top',
                    arrow: true,
                    theme: 'light',
                    allowHTML: true
                });
            }
        },
        eventClick: function(info) {
            const event = info.event;
            const props = event.extendedProps;
            const eventDate = moment(event.start).format('DD MMMM YYYY');
            const statusClass = `status-${props.status.toLowerCase()}`;

            // For end date of multi-day events, use the original ID
            const eventId = props.isEndDate ? props.originalId : event.id;

            // Format cost with thousand separator
            const formattedCost = parseInt(props.cost).toLocaleString('id-ID');

            // Create a modal with event details
            const modalHtml = `
                <div class="modal fade event-modal" id="eventModal" tabindex="-1" role="dialog" aria-labelledby="eventModalLabel" aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="eventModalLabel">Detail Training</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="event-detail">
                                    <i class="fas fa-bookmark"></i>
                                    <div>
                                        <div class="event-detail-label">Topik</div>
                                        <div>${event.title}</div>
                                    </div>
                                </div>
                                <div class="event-detail">
                                    <i class="${props.isMultiDay ? 'fas fa-calendar-week' : 'fas fa-calendar-day'}"></i>
                                    <div>
                                        <div class="event-detail-label">Tanggal</div>
                                        <div>
                                            ${props.isMultiDay ?
                                                `<span class="badge bg-info me-2">Multi-day</span>
                                                <div class="mt-1">
                                                    <span class="badge bg-success me-1"><i class="fas fa-play-circle"></i> Mulai:</span> ${moment(props.startDate).format('DD MMM YYYY')}
                                                </div>
                                                <div class="mt-1">
                                                    <span class="badge bg-danger me-1"><i class="fas fa-stop-circle"></i> Selesai:</span> ${moment(props.endDate).format('DD MMM YYYY')}
                                                </div>` :
                                                moment(props.displayDate || eventDate).format('DD MMMM YYYY')
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="event-detail">
                                    <i class="fas fa-clock"></i>
                                    <div>
                                        <div class="event-detail-label">Waktu</div>
                                        <div>${props.displayTime}</div>
                                    </div>
                                </div>
                                ${props.place ? `
                                <div class="event-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <div>
                                        <div class="event-detail-label">Tempat</div>
                                        <div>${props.place}</div>
                                    </div>
                                </div>
                                ` : ''}
                                <div class="event-detail">
                                    <i class="fas fa-user"></i>
                                    <div>
                                        <div class="event-detail-label">Peserta</div>
                                        <div>${props.participant}</div>
                                    </div>
                                </div>
                                <div class="event-detail">
                                    <i class="fas fa-building"></i>
                                    <div>
                                        <div class="event-detail-label">Departemen</div>
                                        <div>${props.department}</div>
                                    </div>
                                </div>
                                <div class="event-detail">
                                    <i class="fas fa-tag"></i>
                                    <div>
                                        <div class="event-detail-label">Jenis</div>
                                        <div>${props.type}</div>
                                    </div>
                                </div>
                                <div class="event-detail">
                                    <i class="fas fa-info-circle"></i>
                                    <div>
                                        <div class="event-detail-label">Status</div>
                                        <div><span class="status-badge ${statusClass}">${props.status}</span></div>
                                    </div>
                                </div>
                                <div class="event-detail">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <div>
                                        <div class="event-detail-label">Biaya</div>
                                        <div>Rp ${formattedCost}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <a href="detail_training.php?id=${eventId}" class="btn btn-primary">
                                    <i class="fas fa-eye"></i> Lihat Detail
                                </a>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove any existing modal
            $('#eventModal').remove();

            // Add the new modal to the DOM
            $('body').append(modalHtml);

            // Show the modal
            var eventModal = new bootstrap.Modal(document.getElementById('eventModal'));
            eventModal.show();
        }
    });

    calendar.render();

    // Calendar navigation buttons
    document.getElementById('calendarPrev').addEventListener('click', function() {
        calendar.prev();
    });

    document.getElementById('calendarToday').addEventListener('click', function() {
        calendar.today();
    });

    document.getElementById('calendarNext').addEventListener('click', function() {
        calendar.next();
    });

    // Calendar view buttons
    document.getElementById('viewMonth').addEventListener('click', function(e) {
        e.preventDefault();
        calendar.changeView('dayGridMonth');
    });

    document.getElementById('viewWeek').addEventListener('click', function(e) {
        e.preventDefault();
        calendar.changeView('timeGridWeek');
    });

    document.getElementById('viewDay').addEventListener('click', function(e) {
        e.preventDefault();
        calendar.changeView('timeGridDay');
    });

    document.getElementById('viewList').addEventListener('click', function(e) {
        e.preventDefault();
        calendar.changeView('listMonth');
    });

    // Filter functionality
    function applyFilters() {
        const pendingChecked = document.getElementById('filter-pending').checked;
        const approvedChecked = document.getElementById('filter-approved').checked;
        const completedChecked = document.getElementById('filter-completed').checked;
        const rejectedChecked = document.getElementById('filter-rejected').checked;

        // Get training type filters
        const inhouseChecked = document.getElementById('filter-type-inhouse').checked;
        const outhouseChecked = document.getElementById('filter-type-outhouse').checked;
        const hybridChecked = document.getElementById('filter-type-hybrid').checked;

        // Get any additional type filters
        const additionalTypeFilters = document.querySelectorAll('.training-type-filter:not([id^="filter-type-inhouse"]):not([id^="filter-type-outhouse"]):not([id^="filter-type-hybrid"])');
        const additionalTypes = {};
        additionalTypeFilters.forEach(filter => {
            additionalTypes[filter.dataset.type] = filter.checked;
        });

        // Filter events based on status and training type
        const filteredEvents = allEvents.filter(event => {
            const status = event.extendedProps.status.toLowerCase();
            const type = event.extendedProps.type.toLowerCase();

            // Filter by status
            if (status === 'pending' && !pendingChecked) return false;
            if (status === 'approved' && !approvedChecked) return false;
            if (status === 'completed' && !completedChecked) return false;
            if (status === 'rejected' && !rejectedChecked) return false;

            // Filter by training type
            if (type === 'inhouse' && !inhouseChecked) return false;
            if (type === 'outhouse' && !outhouseChecked) return false;
            if (type === 'hybrid' && !hybridChecked) return false;

            // Check additional types
            if (!inhouseChecked && !outhouseChecked && !hybridChecked) {
                // If none of the main types are checked, check additional types
                if (additionalTypes[type] !== undefined && !additionalTypes[type]) {
                    return false;
                }
            }

            return true;
        });

        // Update calendar events
        calendar.removeAllEvents();
        calendar.addEventSource(filteredEvents);

        // Update statistics
        updateFilteredStats(filteredEvents);
    }

    // Function to update statistics based on filtered events
    function updateFilteredStats(filteredEvents) {
        const total = filteredEvents.length;
        const completed = filteredEvents.filter(event => event.extendedProps.status.toLowerCase() === 'completed').length;
        const pending = filteredEvents.filter(event => event.extendedProps.status.toLowerCase() === 'pending').length;
        const totalCost = filteredEvents.reduce((sum, event) => sum + parseInt(event.extendedProps.cost || 0), 0);

        // Update the statistics in the sidebar
        document.getElementById('period-total').textContent = total;
        document.getElementById('period-completed').textContent = completed;
        document.getElementById('period-pending').textContent = pending;
        document.getElementById('period-cost').textContent = 'Rp ' + totalCost.toLocaleString('id-ID');
    }

    // Add event listeners to filter checkboxes - status filters
    document.getElementById('filter-pending').addEventListener('change', applyFilters);
    document.getElementById('filter-approved').addEventListener('change', applyFilters);
    document.getElementById('filter-completed').addEventListener('change', applyFilters);
    document.getElementById('filter-rejected').addEventListener('change', applyFilters);

    // Add event listeners to training type filters
    document.getElementById('filter-type-inhouse').addEventListener('change', applyFilters);
    document.getElementById('filter-type-outhouse').addEventListener('change', applyFilters);
    document.getElementById('filter-type-hybrid').addEventListener('change', applyFilters);

    // Add event listeners to any additional training type filters
    document.querySelectorAll('.training-type-filter').forEach(filter => {
        filter.addEventListener('change', applyFilters);
    });

    // Reset filters button
    document.getElementById('resetFilters').addEventListener('click', function() {
        // Reset status checkboxes to checked
        document.getElementById('filter-pending').checked = true;
        document.getElementById('filter-approved').checked = true;
        document.getElementById('filter-completed').checked = true;
        document.getElementById('filter-rejected').checked = true;

        // Reset main training type filters
        document.getElementById('filter-type-inhouse').checked = true;
        document.getElementById('filter-type-outhouse').checked = true;
        document.getElementById('filter-type-hybrid').checked = true;

        // Reset all other training type filters
        const typeFilters = document.querySelectorAll('.training-type-filter');
        typeFilters.forEach(filter => {
            filter.checked = true;
        });

        // Apply filters
        applyFilters();

        // Show success message
        const toast = `
            <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 5">
                <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header bg-success text-white">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong class="me-auto">Filter Reset</strong>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">
                        Semua filter telah direset.
                    </div>
                </div>
            </div>
        `;

        // Remove any existing toast
        $('.toast').parent().remove();

        // Add the toast to the DOM
        $('body').append(toast);

        // Auto-hide the toast after 3 seconds
        setTimeout(() => {
            $('.toast').toast('hide');
        }, 3000);
    });

    // Show more training types
    if (document.getElementById('showMoreTypes')) {
        document.getElementById('showMoreTypes').addEventListener('click', function(e) {
            e.preventDefault();

            // Show modal with all training types
            const typesModalHtml = `
                <div class="modal fade" id="typesModal" tabindex="-1" aria-labelledby="typesModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="typesModalLabel">Semua Jenis Training</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <?php foreach ($training_types as $type): ?>
                                    <div class="col-md-6">
                                        <div class="filter-checkbox">
                                            <input type="checkbox" id="modal-filter-type-<?= md5($type) ?>"
                                                   class="training-type-filter" data-type="<?= htmlspecialchars($type) ?>" checked>
                                            <label for="modal-filter-type-<?= md5($type) ?>"><?= htmlspecialchars($type) ?></label>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                <button type="button" class="btn btn-primary" id="applyTypeFilters">Terapkan Filter</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove any existing modal
            $('#typesModal').remove();

            // Add the new modal to the DOM
            $('body').append(typesModalHtml);

            // Show the modal
            var typesModal = new bootstrap.Modal(document.getElementById('typesModal'));
            typesModal.show();

            // Apply type filters
            document.getElementById('applyTypeFilters').addEventListener('click', function() {
                // Apply filters and close modal
                applyFilters();
                typesModal.hide();
            });
        });
    }
}

// Export functionality
if (document.getElementById('exportDeptCSV')) {
    document.getElementById('exportDeptCSV').addEventListener('click', function(e) {
        e.preventDefault();
        exportTableToCSV('deptTable', 'statistik_departemen.csv');
    });
}

if (document.getElementById('exportDeptExcel')) {
    document.getElementById('exportDeptExcel').addEventListener('click', function(e) {
        e.preventDefault();
        exportTableToExcel('deptTable', 'statistik_departemen.xlsx');
    });
}

if (document.getElementById('printDeptTable')) {
    document.getElementById('printDeptTable').addEventListener('click', function(e) {
        e.preventDefault();
        printTable('deptTable');
    });
}

if (document.getElementById('exportTrainingCSV')) {
    document.getElementById('exportTrainingCSV').addEventListener('click', function(e) {
        e.preventDefault();
        exportTableToCSV('trainingTable', 'daftar_training.csv');
    });
}

if (document.getElementById('printTrainingTable')) {
    document.getElementById('printTrainingTable').addEventListener('click', function(e) {
        e.preventDefault();
        printTable('trainingTable');
    });
}

// Helper function to export table to CSV
function exportTableToCSV(tableId, filename) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tr');
    let csv = [];

    for (let i = 0; i < rows.length; i++) {
        const row = [], cols = rows[i].querySelectorAll('td, th');

        for (let j = 0; j < cols.length; j++) {
            // Get the text content and clean it
            let data = cols[j].innerText.replace(/(\r\n|\n|\r)/gm, '').replace(/(\s\s)/gm, ' ');
            // Escape double-quotes with double-quotes
            data = data.replace(/"/g, '""');
            // Push the data with surrounding quotes to handle commas within the data
            row.push('"' + data + '"');
        }
        csv.push(row.join(','));
    }

    // Download CSV file
    downloadCSV(csv.join('\n'), filename);
}

function downloadCSV(csv, filename) {
    const csvFile = new Blob([csv], {type: 'text/csv'});
    const downloadLink = document.createElement('a');

    // File name
    downloadLink.download = filename;

    // Create a link to the file
    downloadLink.href = window.URL.createObjectURL(csvFile);

    // Hide download link
    downloadLink.style.display = 'none';

    // Add the link to DOM
    document.body.appendChild(downloadLink);

    // Click download link
    downloadLink.click();

    // Clean up
    document.body.removeChild(downloadLink);
}

// Helper function to export table to Excel (simplified)
function exportTableToExcel(tableId, filename) {
    // This is a simplified version - in production, you might want to use a library like SheetJS
    const uri = 'data:application/vnd.ms-excel;base64,';
    const template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';

    const base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) };
    const format = function(s, c) { return s.replace(/{(\w+)}/g, function(m, p) { return c[p]; }) };

    const table = document.getElementById(tableId);
    const ctx = {worksheet: tableId || 'Worksheet', table: table.innerHTML};

    const link = document.createElement('a');
    link.download = filename;
    link.href = uri + base64(format(template, ctx));
    link.click();
}

// Helper function to print a specific table
function printTable(tableId) {
    const printContents = document.getElementById(tableId).outerHTML;
    const originalContents = document.body.innerHTML;

    document.body.innerHTML = `
        <html>
            <head>
                <title>Print</title>
                <style>
                    body { font-family: Arial, sans-serif; }
                    table { width: 100%; border-collapse: collapse; }
                    th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
                    th { background-color: #f2f2f2; }
                    .status-badge {
                        padding: 3px 8px;
                        border-radius: 12px;
                        font-size: 12px;
                        font-weight: 500;
                        display: inline-block;
                    }
                    .status-approved { background: #e3f2fd; color: #1976d2; }
                    .status-completed { background: #e8f5e9; color: #2e7d32; }
                    .status-rejected { background: #ffebee; color: #c62828; }
                    .status-pending { background: #fff3e0; color: #f57c00; }
                    .progress { display: none; }
                    @media print {
                        @page { margin: 0.5cm; }
                        h1 { font-size: 18pt; }
                        table { font-size: 10pt; }
                    }
                </style>
            </head>
            <body>
                <h1>Laporan Training</h1>
                <p>Periode: <?= date('d M Y', strtotime($start_date)) ?> - <?= date('d M Y', strtotime($end_date)) ?></p>
                ${printContents}
            </body>
        </html>
    `;

    window.print();
    document.body.innerHTML = originalContents;
}
</script>

</body>
</html>