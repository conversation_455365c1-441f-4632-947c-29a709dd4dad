<?php
require_once __DIR__ . '/mail.php';
require_once __DIR__ . '/config.php';

/**
 * Send email notification when Director approves external training
 *
 * @param int $training_id The ID of the training submission
 * @param object $conn Database connection
 * @param string $comments Comments from Director
 * @param string $assignment Assignment given by Director
 * @return array Result of the email sending operation
 */
function sendDirectorApprovalNotification($training_id, $conn, $comments = '', $assignment = '') {
    try {
        // Get training details with all related information
        $query = "SELECT ts.*,
                         u.name as requester_name,
                         u.email as requester_email,
                         k.nama as requester_nama,
                         k.dept as requester_dept,
                         k.bagian as requester_bagian,
                         k.jabatan as requester_jabatan
                  FROM training_submissions ts
                  LEFT JOIN users u ON ts.user_id = u.id
                  LEFT JOIN karyawan k ON u.nik = k.nik
                  WHERE ts.id = ?";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            return [
                'success' => false,
                'message' => 'Training tidak ditemukan'
            ];
        }

        $training = $result->fetch_assoc();
        $stmt->close();

        // Get participants data
        $participants_query = "SELECT p.nik_participants as nik,
                                     p.nama_participants as nama,
                                     p.jabatan_participants as jabatan,
                                     p.departemen_participants as departemen
                              FROM participants p
                              WHERE p.training_id = ?
                              ORDER BY p.nama_participants";

        $participants_stmt = $conn->prepare($participants_query);
        $participants_stmt->bind_param("i", $training_id);
        $participants_stmt->execute();
        $participants_result = $participants_stmt->get_result();
        $participants = $participants_result->fetch_all(MYSQLI_ASSOC);
        $participants_stmt->close();

        // Get email settings from database
        $settings_query = "SELECT * FROM settings WHERE id = 1";
        $settings_result = $conn->query($settings_query);
        $settings = $settings_result->fetch_assoc();

        if (!$settings) {
            return [
                'success' => false,
                'message' => 'Email settings tidak ditemukan'
            ];
        }

        // Determine recipients: Requester + Department Head
        $recipients = [];

        // Add requester
        if (!empty($training['requester_email'])) {
            $recipients[] = [
                'email' => $training['requester_email'],
                'name' => $training['requester_nama'] ?? $training['requester_name'],
                'role' => 'Pemohon'
            ];
        }

        // Get Department Head from same department as requester
        $dept_head_query = "SELECT u.name, u.email FROM users u
                           JOIN karyawan k ON u.nik = k.nik
                           WHERE k.dept = ? AND k.jabatan IN ('Supervisor', 'Chief')
                           AND k.level_karyawan >= 4
                           AND u.is_active = 1
                           ORDER BY k.level_karyawan DESC LIMIT 1";

        $dept_stmt = $conn->prepare($dept_head_query);
        $dept_stmt->bind_param("s", $training['requester_dept']);
        $dept_stmt->execute();
        $dept_result = $dept_stmt->get_result();

        if ($dept_head = $dept_result->fetch_assoc()) {
            $recipients[] = [
                'email' => $dept_head['email'],
                'name' => $dept_head['name'],
                'role' => 'Department Head'
            ];
        }
        $dept_stmt->close();

        // Base URL for links
        $base_url = 'http://' . $_SERVER['HTTP_HOST'] . '/Training/';

        // Format training date
        $training_date = 'Belum ditentukan';
        if (!empty($training['start_date'])) {
            if (!empty($training['end_date']) && $training['end_date'] !== $training['start_date']) {
                $training_date = date('d F Y', strtotime($training['start_date'])) . ' - ' . date('d F Y', strtotime($training['end_date']));
            } else {
                $training_date = date('d F Y', strtotime($training['start_date']));
            }
        }

        // Build approval timeline
        $timeline = [
            [
                'title' => 'Pengajuan Dikirim',
                'description' => 'Diajukan pada ' . date('d F Y H:i', strtotime($training['created_at'])),
                'icon' => '📤'
            ]
        ];

        // Add Department Head approval if exists
        if ($training['approved_dept_head'] === 'Approved') {
            $timeline[] = [
                'title' => 'Disetujui Department Head',
                'description' => 'Disetujui oleh Department Head',
                'icon' => '✅'
            ];
        }

        // Add L&D approval if exists
        if ($training['approved_hrd'] === 'Approved') {
            $timeline[] = [
                'title' => 'Disetujui L&D',
                'description' => 'Disetujui oleh Learning & Development',
                'icon' => '✅'
            ];
        }

        // Add HRGA approval if exists
        if ($training['approved_ga'] === 'Approved') {
            $timeline[] = [
                'title' => 'Disetujui HRGA',
                'description' => 'Disetujui oleh HR & General Affairs',
                'icon' => '✅'
            ];
        }

        // Add Factory Manager approval if exists
        if ($training['approved_fm'] === 'Approved') {
            $timeline[] = [
                'title' => 'Disetujui Factory Manager',
                'description' => 'Disetujui oleh Factory Manager',
                'icon' => '✅'
            ];
        }

        // Add Director approval (current)
        $timeline[] = [
            'title' => 'Disetujui Direktur',
            'description' => 'Disetujui oleh Direktur pada ' . date('d F Y H:i'),
            'icon' => '🎉'
        ];

        // Prepare email data
        $email_data = [
            'training_id' => $training['id'],
            'training_topic' => $training['training_topic'],
            'training_date' => $training_date,
            'requester_name' => $training['requester_nama'] ?? $training['requester_name'],
            'requester_nik' => $training['nik'],
            'departemen' => $training['requester_dept'],
            'bagian' => $training['requester_bagian'],
            'jabatan' => $training['requester_jabatan'],
            'training_type' => $training['training_type'] ?? '',
            'provider_name' => $training['provider_name'] ?? '',
            'training_place' => $training['training_place'] ?? '',
            'comments' => $comments,
            'assignment' => $assignment,
            'participants' => $participants,
            'timeline' => $timeline,
            'approved_at' => date('d F Y H:i'),
            'dashboard_url' => $base_url . 'pemohon/dashboard.php'
        ];

        // Send emails to all recipients
        $results = [];
        foreach ($recipients as $recipient) {
            $email_data['recipient_name'] = $recipient['name'];

            // Generate email body using template
            ob_start();
            $data = $email_data;
            include __DIR__ . '/email_templates/director_approval_notification.php';
            $email_body = ob_get_clean();

            // Send email
            $mail_result = send_mail(
                $recipient['email'],
                '🎉 Training Eksternal Disetujui Direktur - Training Center PAS',
                $email_body,
                $settings
            );

            $results[] = [
                'recipient' => $recipient['email'],
                'role' => $recipient['role'],
                'success' => $mail_result['success'],
                'message' => $mail_result['message']
            ];

            // Log email attempt
            error_log("Director Approval Email - Training ID: {$training_id}, Recipient: {$recipient['email']} ({$recipient['role']}), Result: " . ($mail_result['success'] ? 'SUCCESS' : 'FAILED'));
        }

        return [
            'success' => true,
            'message' => 'Email notifications sent successfully',
            'results' => $results
        ];

    } catch (Exception $e) {
        error_log("Error in sendDirectorApprovalNotification: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Error sending email: ' . $e->getMessage()
        ];
    }
}

/**
 * Send email notification when Director rejects external training
 *
 * @param int $training_id The ID of the training submission
 * @param object $conn Database connection
 * @param string $comments Comments from Director
 * @return array Result of the email sending operation
 */
function sendDirectorRejectionNotification($training_id, $conn, $comments = '') {
    try {
        // Get training details
        $query = "SELECT ts.*,
                         u.name as requester_name,
                         u.email as requester_email,
                         k.nama as requester_nama,
                         k.dept as requester_dept
                  FROM training_submissions ts
                  LEFT JOIN users u ON ts.user_id = u.id
                  LEFT JOIN karyawan k ON u.nik = k.nik
                  WHERE ts.id = ?";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $training_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            return [
                'success' => false,
                'message' => 'Training tidak ditemukan'
            ];
        }

        $training = $result->fetch_assoc();
        $stmt->close();

        // Get email settings
        $settings_query = "SELECT * FROM settings WHERE id = 1";
        $settings_result = $conn->query($settings_query);
        $settings = $settings_result->fetch_assoc();

        if (!$settings) {
            return [
                'success' => false,
                'message' => 'Email settings tidak ditemukan'
            ];
        }

        // Send email to requester only for rejection
        $recipients = [];
        if (!empty($training['requester_email'])) {
            $recipients[] = [
                'email' => $training['requester_email'],
                'name' => $training['requester_nama'] ?? $training['requester_name'],
                'role' => 'Pemohon'
            ];
        }

        // Base URL for links
        $base_url = 'http://' . $_SERVER['HTTP_HOST'] . '/Training/';

        // Format training date
        $training_date = 'Belum ditentukan';
        if (!empty($training['start_date'])) {
            if (!empty($training['end_date']) && $training['end_date'] !== $training['start_date']) {
                $training_date = date('d F Y', strtotime($training['start_date'])) . ' - ' . date('d F Y', strtotime($training['end_date']));
            } else {
                $training_date = date('d F Y', strtotime($training['start_date']));
            }
        }

        // Prepare email data for rejection
        $email_data = [
            'email_title' => '❌ Training Eksternal Ditolak Direktur',
            'email_message' => 'Mohon maaf, pengajuan training eksternal Anda telah ditolak oleh Direktur.',
            'training_id' => $training['id'],
            'training_topic' => $training['training_topic'],
            'training_date' => $training_date,
            'requester_name' => $training['requester_nama'] ?? $training['requester_name'],
            'departemen' => $training['requester_dept'],
            'comments' => $comments,
            'status' => 'Ditolak oleh Direktur',
            'status_class' => 'status-rejected',
            'dashboard_url' => $base_url . 'pemohon/dashboard.php'
        ];

        // Send emails
        $results = [];
        foreach ($recipients as $recipient) {
            $email_data['recipient_name'] = $recipient['name'];

            // Use director rejection template
            ob_start();
            $data = $email_data;
            include __DIR__ . '/email_templates/director_rejection_notification.php';
            $email_body = ob_get_clean();

            // Send email
            $mail_result = send_mail(
                $recipient['email'],
                '❌ Training Eksternal Ditolak Direktur - Training Center PAS',
                $email_body,
                $settings
            );

            $results[] = [
                'recipient' => $recipient['email'],
                'role' => $recipient['role'],
                'success' => $mail_result['success'],
                'message' => $mail_result['message']
            ];

            // Log email attempt
            error_log("Director Rejection Email - Training ID: {$training_id}, Recipient: {$recipient['email']} ({$recipient['role']}), Result: " . ($mail_result['success'] ? 'SUCCESS' : 'FAILED'));
        }

        return [
            'success' => true,
            'message' => 'Rejection email notifications sent successfully',
            'results' => $results
        ];

    } catch (Exception $e) {
        error_log("Error in sendDirectorRejectionNotification: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Error sending rejection email: ' . $e->getMessage()
        ];
    }
}
?>
