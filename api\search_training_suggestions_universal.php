<?php
/**
 * API untuk search training suggestions universal
 * Mendukung semua role dengan intelligent search
 */

header('Content-Type: application/json');
session_start();
include '../config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$query = $_GET['q'] ?? '';
$type = $_GET['type'] ?? 'all'; // all, online, offline
$limit = $_GET['limit'] ?? 10;
$user_role = $_SESSION['role_id'] ?? 1;

if (strlen($query) < 2) {
    echo json_encode(['success' => true, 'suggestions' => []]);
    exit;
}

try {
    $suggestions = [];
    $search_term = '%' . $query . '%';

    // Search offline training
    if ($type === 'all' || $type === 'offline') {
        $offline_query = "SELECT
                          ot.id,
                          ot.training_name as title,
                          ot.start_date,
                          ot.end_date,
                          ot.start_time,
                          ot.location,
                          ot.trainer,
                          ot.description,
                          ot.is_confirmed,
                          'offline' as type,
                          CASE
                            WHEN ot.training_name LIKE ? THEN 4
                            WHEN ot.trainer LIKE ? THEN 3
                            WHEN ot.location LIKE ? THEN 2
                            WHEN ot.description LIKE ? THEN 1
                            ELSE 0
                          END as relevance_score
                          FROM offline_training ot
                          WHERE (ot.training_name LIKE ?
                                 OR ot.trainer LIKE ?
                                 OR ot.location LIKE ?
                                 OR ot.description LIKE ?)";

        // Only show hidden events to admin roles
        if ($user_role != 4) { // Not admin
            $offline_query .= " AND ot.is_hidden = 0";
        }

        $offline_query .= " ORDER BY relevance_score DESC, ot.start_date DESC LIMIT ?";

        $stmt = $conn->prepare($offline_query);
        $stmt->bind_param("sssssssssi",
            $search_term, $search_term, $search_term, $search_term,
            $search_term, $search_term, $search_term, $search_term,
            $limit
        );
        $stmt->execute();
        $offline_result = $stmt->get_result();

        while ($row = $offline_result->fetch_assoc()) {
            // Format date
            if ($row['end_date'] && $row['end_date'] !== $row['start_date']) {
                $row['date'] = date('d M Y', strtotime($row['start_date'])) . ' - ' . date('d M Y', strtotime($row['end_date']));
            } else {
                $row['date'] = date('d M Y', strtotime($row['start_date']));
            }

            // Highlight matching text
            $row['highlighted_title'] = highlightMatch($row['title'], $query);
            $row['highlighted_trainer'] = highlightMatch($row['trainer'], $query);
            $row['highlighted_location'] = highlightMatch($row['location'], $query);

            $suggestions[] = $row;
        }
        $stmt->close();
    }

    // Search online training
    if ($type === 'all' || $type === 'online') {
        $online_query = "SELECT
                         ts.id,
                         ts.training_topic as title,
                         ts.start_date,
                         ts.end_date,
                         CONCAT(ts.training_time_start, ' - ', ts.training_time_end) as start_time,
                         ts.training_location as location,
                         ts.trainer_name as trainer,
                         ts.description,
                         ts.is_confirmed,
                         'online' as type,
                         CASE
                           WHEN ts.training_topic LIKE ? THEN 4
                           WHEN ts.trainer_name LIKE ? THEN 3
                           WHEN ts.training_location LIKE ? THEN 2
                           WHEN ts.description LIKE ? THEN 1
                           ELSE 0
                         END as relevance_score
                         FROM training_submissions ts
                         WHERE ts.status = 'Approved'
                         AND (ts.training_topic LIKE ?
                              OR ts.trainer_name LIKE ?
                              OR ts.training_location LIKE ?
                              OR ts.description LIKE ?)
                         ORDER BY relevance_score DESC, ts.start_date DESC LIMIT ?";

        $stmt = $conn->prepare($online_query);
        $stmt->bind_param("sssssssssi",
            $search_term, $search_term, $search_term, $search_term,
            $search_term, $search_term, $search_term, $search_term,
            $limit
        );
        $stmt->execute();
        $online_result = $stmt->get_result();

        while ($row = $online_result->fetch_assoc()) {
            // Format date
            if ($row['end_date'] && $row['end_date'] !== $row['start_date']) {
                $row['date'] = date('d M Y', strtotime($row['start_date'])) . ' - ' . date('d M Y', strtotime($row['end_date']));
            } else {
                $row['date'] = date('d M Y', strtotime($row['start_date']));
            }

            // Highlight matching text
            $row['highlighted_title'] = highlightMatch($row['title'], $query);
            $row['highlighted_trainer'] = highlightMatch($row['trainer'], $query);
            $row['highlighted_location'] = highlightMatch($row['location'], $query);

            $suggestions[] = $row;
        }
        $stmt->close();
    }

    // Sort by relevance score
    usort($suggestions, function($a, $b) {
        if ($a['relevance_score'] === $b['relevance_score']) {
            return strtotime($b['start_date']) - strtotime($a['start_date']);
        }
        return $b['relevance_score'] - $a['relevance_score'];
    });

    // Limit results
    $suggestions = array_slice($suggestions, 0, $limit);

    echo json_encode([
        'success' => true,
        'suggestions' => $suggestions,
        'total' => count($suggestions)
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

/**
 * Highlight matching text in search results
 */
function highlightMatch($text, $query) {
    if (empty($text) || empty($query)) {
        return $text;
    }

    return preg_replace('/(' . preg_quote($query, '/') . ')/i', '<mark>$1</mark>', $text);
}

$conn->close();
?>
