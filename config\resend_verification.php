<?php
session_start();
require_once 'email_helper.php';
require_once 'config.php';

header('Content-Type: application/json');

// Periksa apakah ada sesi verifikasi email yang aktif
if (!isset($_SESSION['email_verification'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Tidak ada proses verifikasi email yang aktif.'
    ]);
    exit();
}

// Generate kode verifikasi baru
$new_verification_code = generateVerificationCode();
$email = $_SESSION['email_verification']['email'];

// Ambil user_id dari session
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;

// Kirim kode verifikasi baru
if (sendVerificationEmail($email, $new_verification_code, true, $user_id)) {
    // Update informasi verifikasi di session
    $_SESSION['email_verification'] = [
        'email' => $email,
        'code' => $new_verification_code,
        'deadline' => time() + (15 * 60) // 15 menit
    ];

    // Jika user sudah login, update juga kode verifikasi di database
    if (isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];

        // Update kode verifikasi di database
        $query = "UPDATE users SET
                  verification_code = ?,
                  verification_expires = DATE_ADD(NOW(), INTERVAL 15 MINUTE),
                  pending_email = ?
                  WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("ssi", $new_verification_code, $email, $user_id);
        $stmt->execute();
    }

    echo json_encode([
        'success' => true,
        'message' => 'Kode verifikasi baru telah dikirim.'
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Gagal mengirim kode verifikasi baru.'
    ]);
}