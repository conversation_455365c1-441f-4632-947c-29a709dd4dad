<?php
session_start();
require_once '../config/config.php'; // Database connection and configuration

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

$pageTitle = "Riwayat Perubahan Karyawan";

// Debug settings - hanya admin yang dapat mengaktifkan debug
$debug = false;
if (isset($_SESSION['user_id']) && $_SESSION['role_id'] == 99) {
    if (isset($_GET['debug'])) {
        $debug = $_GET['debug'] == '1';
        // Simpan setting debug dalam session
        $_SESSION['employee_history_debug'] = $debug;
    } elseif (isset($_SESSION['employee_history_debug'])) {
        $debug = $_SESSION['employee_history_debug'];
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <?php include '../config/head.php'; ?>
    <style>
        body {
            background-color: #f5f5f5;
        }
        .container-fluid {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
            margin-top: 80px;
            position: relative;
            z-index: 1;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
            overflow: visible;
        }
        .card-header {
            background: rgb(157, 0, 0) !important;
            color: white;
            padding: 20px;
            border-bottom: none;
            border-radius: 10px 10px 0 0 !important;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        .card-header .btn {
            margin-left: 10px;
        }
        .card-body {
            padding: 30px;
        }
        .filter-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .table-responsive {
            margin-top: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        .table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            text-align: left;
            padding: 15px 10px;
            border-bottom: 2px solid #dee2e6;
        }
        .table td {
            padding: 15px 10px;
            border-bottom: 1px solid #e9ecef;
            color: #495057;
            vertical-align: middle;
        }
        .table tr:hover {
            background-color: #f8f9fa;
        }
        .pagination {
            margin-top: 20px;
            justify-content: center;
        }
        .pagination .page-item.active .page-link {
            background-color: #9d0000;
            border-color: #9d0000;
        }
        .pagination .page-link {
            color: #9d0000;
        }
        .btn-primary {
            background-color: #9d0000;
            border-color: #9d0000;
        }
        .btn-primary:hover {
            background-color: #7d0000;
            border-color: #7d0000;
        }
        .action-insert {
            color: #28a745;
            font-weight: bold;
        }
        .action-update {
            color: #ffc107;
            font-weight: bold;
        }
        .action-delete {
            color: #dc3545;
            font-weight: bold;
        }
        #loading-indicator {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
            color: #9d0000;
        }
        .search-box {
            position: relative;
        }
        .search-box .form-control {
            padding-right: 40px;
        }
        .search-box .search-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        .search-box .clear-search {
            position: absolute;
            right: 40px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            cursor: pointer;
            display: none;
        }
        .filter-badge {
            display: inline-block;
            padding: 5px 10px;
            margin: 5px;
            background-color: #f8f9fa;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        .filter-badge .badge-text {
            margin-right: 5px;
        }
        .filter-badge .badge-remove {
            cursor: pointer;
            color: #dc3545;
        }
        .active-filters {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <?php include '../config/navbara.php'; ?>

    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-history"></i> <?php echo $pageTitle; ?></h3>
            </div>
            <div class="card-body">
                <!-- Filter Form -->
                <div class="row g-3 filter-form">
                    <div class="col-md-3">
                        <label for="nik" class="form-label">NIK Karyawan</label>
                        <div class="search-box">
                            <input type="text" class="form-control" id="nik" placeholder="Masukkan NIK">
                            <span class="clear-search" id="clear-nik"><i class="fas fa-times"></i></span>
                            <span class="search-icon"><i class="fas fa-search"></i></span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="action" class="form-label">Jenis Aksi</label>
                        <select id="action" class="form-select">
                            <option value="">Semua Aksi</option>
                            <option value="INSERT">Penambahan</option>
                            <option value="UPDATE">Perubahan</option>
                            <option value="DELETE">Penghapusan</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">Tanggal Mulai</label>
                        <input type="date" class="form-control" id="start_date">
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">Tanggal Akhir</label>
                        <input type="date" class="form-control" id="end_date">
                    </div>
                    <div class="col-12">
                        <button type="button" id="reset-filters" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> Reset Filter
                        </button>
                    </div>
                    <div class="col-12">
                        <a href="employee_management.php" id="btn" class="action-button add-button">
                            <i class="fas fa-backward"></i> <span class="button-text">Kembali</span>
                        </a>
                        <a href="batch_employee_history.php" class="btn btn-info ml-2">
                            <i class="fas fa-layer-group"></i> Riwayat Batch
                        </a>
                        <button id="delete-all-history" class="btn btn-danger ml-2">
                            <i class="fas fa-trash-alt"></i> Hapus Semua Riwayat
                        </button>
                    </div>
                </div>

                <!-- Active Filters -->
                <div class="active-filters" id="active-filters"></div>

                <!-- Loading Indicator -->
                <div id="loading-indicator">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Memuat data...</p>
                </div>

                <!-- Employee History Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>NIK Karyawan</th>
                                <th>Aksi</th>
                                <th>Waktu</th>
                                <th>Diubah Oleh</th>
                                <th>Detail</th>
                                <th>Rollback</th>
                                <th>Hapus</th>
                            </tr>
                        </thead>
                        <tbody id="history-data">
                            <!-- Data will be loaded here -->
                        </tbody>
                    </table>
                </div>

                <!-- No Data Message -->
                <div id="no-data-message" style="display: none; text-align: center; padding: 20px;">
                    <p>Tidak ada data riwayat yang ditemukan</p>
                </div>

                <!-- Pagination -->
                <nav aria-label="Page navigation" id="pagination-container" style="display: none;">
                    <ul class="pagination" id="pagination">
                        <!-- Pagination will be generated here -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Detail Modal -->
    <div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header" style="background-color: #9d0000; color: white;">
            <h5 class="modal-title" id="detailModalLabel">Detail Perubahan</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="color: white;"></button>
          </div>
          <div class="modal-body" id="detailModalBody">
            <!-- Details will be loaded here -->
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
          </div>
        </div>
      </div>
    </div>

    <?php include '../config/footer.php'; ?>
    <script>
        // Global variables
        let currentPage = 1;
        let totalPages = 1;
        let currentFilters = {
            nik: '',
            action: '',
            start_date: '',
            end_date: '',
            debug: 0
        };

        // Function to load data
        function loadData(page = 1) {
            // Show loading indicator
            $('#loading-indicator').show();
            $('#history-data').empty();
            $('#pagination').empty();
            $('#no-data-message').hide();
            $('#pagination-container').hide();

            // Prepare URL with filters
            let url = 'get_employee_history_realtime.php?page=' + page;

            if (currentFilters.nik) {
                url += '&nik=' + encodeURIComponent(currentFilters.nik);
            }
            if (currentFilters.action) {
                url += '&action=' + encodeURIComponent(currentFilters.action);
            }
            if (currentFilters.start_date) {
                url += '&start_date=' + encodeURIComponent(currentFilters.start_date);
            }
            if (currentFilters.end_date) {
                url += '&end_date=' + encodeURIComponent(currentFilters.end_date);
            }

            // Make AJAX request
            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    // Hide loading indicator
                    $('#loading-indicator').hide();

                    if (response.success && response.data.length > 0) {
                        // Update pagination info
                        currentPage = response.pagination.current_page;
                        totalPages = response.pagination.total_pages;

                        // Render data
                        let html = '';
                        response.data.forEach(function(item) {
                            let actionClass = '';
                            switch(item.action_type) {
                                case 'INSERT': actionClass = 'action-insert'; break;
                                case 'UPDATE': actionClass = 'action-update'; break;
                                case 'DELETE': actionClass = 'action-delete'; break;
                            }

                            html += `
                                <tr>
                                    <td>${item.history_id}</td>
                                    <td>${item.nik}</td>
                                    <td class="${actionClass}">${item.action_label}</td>
                                    <td>${item.change_timestamp}</td>
                                    <td>${item.changed_by_name}</td>
                                    <td>
                                        <button class="btn btn-info btn-sm view-details" data-id="${item.history_id}">
                                            <i class="fas fa-eye"></i> Lihat
                                        </button>
                                    </td>
                                    <td>
                                        <button class="btn btn-warning btn-sm rollback-change" data-id="${item.history_id}" data-action="${item.action_type}">
                                            <i class="fas fa-undo"></i> Rollback
                                        </button>
                                    </td>
                                    <td>
                                        <button class="btn btn-danger btn-sm delete-history" data-id="${item.history_id}">
                                            <i class="fas fa-trash"></i> Hapus
                                        </button>
                                    </td>
                                </tr>
                            `;
                        });

                        $('#history-data').html(html);

                        // Generate pagination
                        generatePagination(response.pagination);
                        $('#pagination-container').show();
                    } else {
                        // Show no data message
                        $('#no-data-message').show();
                    }

                    // Update active filters display
                    updateActiveFilters();
                },
                error: function(xhr, status, error) {
                    // Hide loading indicator
                    $('#loading-indicator').hide();

                    // Show error message
                    $('#history-data').html(`<tr><td colspan="7" class="text-center text-danger">Error: ${error}</td></tr>`);
                    console.error('AJAX Error:', xhr.responseText);
                }
            });
        }

        // Function to generate pagination
        function generatePagination(pagination) {
            let html = '';

            // First and Previous buttons
            if (pagination.current_page > 1) {
                html += `
                    <li class="page-item">
                        <a class="page-link" href="#" data-page="1" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#" data-page="${pagination.current_page - 1}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                `;
            }

            // Page numbers
            let startPage = Math.max(1, pagination.current_page - 2);
            let endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `;
            }

            // Next and Last buttons
            if (pagination.current_page < pagination.total_pages) {
                html += `
                    <li class="page-item">
                        <a class="page-link" href="#" data-page="${pagination.current_page + 1}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#" data-page="${pagination.total_pages}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                `;
            }

            $('#pagination').html(html);
        }

        // Function to update active filters display
        function updateActiveFilters() {
            let html = '';
            let hasFilters = false;

            if (currentFilters.nik) {
                html += `
                    <div class="filter-badge">
                        <span class="badge-text">NIK: ${currentFilters.nik}</span>
                        <span class="badge-remove" data-filter="nik"><i class="fas fa-times"></i></span>
                    </div>
                `;
                hasFilters = true;
            }

            if (currentFilters.action) {
                let actionLabel = '';
                switch(currentFilters.action) {
                    case 'INSERT': actionLabel = 'Penambahan'; break;
                    case 'UPDATE': actionLabel = 'Perubahan'; break;
                    case 'DELETE': actionLabel = 'Penghapusan'; break;
                    default: actionLabel = currentFilters.action;
                }

                html += `
                    <div class="filter-badge">
                        <span class="badge-text">Aksi: ${actionLabel}</span>
                        <span class="badge-remove" data-filter="action"><i class="fas fa-times"></i></span>
                    </div>
                `;
                hasFilters = true;
            }

            if (currentFilters.start_date) {
                html += `
                    <div class="filter-badge">
                        <span class="badge-text">Tanggal Mulai: ${currentFilters.start_date}</span>
                        <span class="badge-remove" data-filter="start_date"><i class="fas fa-times"></i></span>
                    </div>
                `;
                hasFilters = true;
            }

            if (currentFilters.end_date) {
                html += `
                    <div class="filter-badge">
                        <span class="badge-text">Tanggal Akhir: ${currentFilters.end_date}</span>
                        <span class="badge-remove" data-filter="end_date"><i class="fas fa-times"></i></span>
                    </div>
                `;
                hasFilters = true;
            }

            if (hasFilters) {
                html = '<div><strong>Filter Aktif:</strong></div>' + html;
                $('#active-filters').html(html).show();
            } else {
                $('#active-filters').html('').hide();
            }
        }

        // Document ready
        $(document).ready(function() {
            // Initial data load
            loadData();

            // Reset filters button click
            $('#reset-filters').on('click', function() {
                // Clear form inputs
                $('#nik').val('');
                $('#action').val('');
                $('#start_date').val('');
                $('#end_date').val('');

                // Clear filters
                currentFilters.nik = '';
                currentFilters.action = '';
                currentFilters.start_date = '';
                currentFilters.end_date = '';

                // Reset to page 1 and reload data
                loadData(1);
            });

            // Pagination click event
            $(document).on('click', '#pagination .page-link', function(e) {
                e.preventDefault();
                let page = $(this).data('page');
                loadData(page);
            });

            // Remove filter badge click
            $(document).on('click', '.badge-remove', function() {
                let filter = $(this).data('filter');

                // Clear the filter
                currentFilters[filter] = '';

                // Update form input
                $(`#${filter}`).val('');

                // Reload data
                loadData(1);
            });

            // Clear search icon click
            $('#clear-nik').on('click', function() {
                $('#nik').val('');
                $(this).hide();

                // Clear the filter and reload data
                currentFilters.nik = '';
                loadData(1);
            });

            // Show/hide clear search icon
            $('#nik').on('input', function() {
                if ($(this).val().trim() !== '') {
                    $('#clear-nik').show();
                } else {
                    $('#clear-nik').hide();
                }
            });

            // Real-time search on NIK input (with debounce)
            let searchTimeout;
            $('#nik').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    currentFilters.nik = $('#nik').val().trim();
                    loadData(1);
                }, 500); // 500ms debounce
            });

            // Real-time filter on action select
            $('#action').on('change', function() {
                currentFilters.action = $(this).val();
                loadData(1);
            });

            // Real-time filter on date inputs
            $('#start_date, #end_date').on('change', function() {
                if ($(this).attr('id') === 'start_date') {
                    currentFilters.start_date = $(this).val();
                } else {
                    currentFilters.end_date = $(this).val();
                }
                loadData(1);
            });

            // View Details Button Click
            $(document).on('click', '.view-details', function() {
                var historyId = $(this).data('id');
                // AJAX call to get details
                $.ajax({
                    url: 'get_history_details_ajax.php',
                    type: 'POST',
                    data: { history_id: historyId },
                    success: function(response) {
                        $('#detailModalBody').html(response);
                        var detailModal = new bootstrap.Modal(document.getElementById('detailModal'));
                        detailModal.show();
                    },
                    error: function() {
                        alert('Error: Gagal mengambil detail perubahan.');
                    }
                });
            });

            // Rollback Button Click
            $(document).on('click', '.rollback-change', function() {
                var historyId = $(this).data('id');
                var action = $(this).data('action');

                var actionText = '';
                switch(action) {
                    case 'INSERT': actionText = 'penambahan'; break;
                    case 'UPDATE': actionText = 'perubahan'; break;
                    case 'DELETE': actionText = 'penghapusan'; break;
                    default: actionText = action.toLowerCase();
                }

                // Gunakan confirmAction sebagai pengganti confirm
                confirmAction(
                    'Apakah Anda yakin ingin melakukan rollback ' + actionText + ' ini (ID: ' + historyId + ')? Tindakan ini tidak dapat dibatalkan dengan mudah.',
                    function() {
                        // AJAX call to rollback
                        $.ajax({
                            url: 'rollback_employee_change.php',
                            type: 'POST',
                            data: { history_id: historyId, action: action },
                            dataType: 'json',
                            success: function(response) {
                                if (response.success) {
                                    alert('Rollback berhasil!');
                                    // Reload data to reflect changes
                                    loadData(currentPage);
                                } else {
                                    alert('Rollback gagal: ' + response.message);
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error("Rollback Error:", xhr.responseText);
                                alert('Terjadi kesalahan saat melakukan rollback. Periksa konsol untuk detail.');
                            }
                        });
                    }
                );
            });

            // Delete History Button Click
            $(document).on('click', '.delete-history', function() {
                var historyId = $(this).data('id');

                // Gunakan confirmAction sebagai pengganti confirm
                confirmAction(
                    'Apakah Anda yakin ingin menghapus riwayat ini (ID: ' + historyId + ')? Tindakan ini tidak dapat dibatalkan.',
                    function() {
                        // AJAX call to delete history
                        $.ajax({
                            url: 'delete_employee_history.php',
                            type: 'POST',
                            data: { history_id: historyId },
                            dataType: 'json',
                            success: function(response) {
                                if (response.success) {
                                    alert('Riwayat berhasil dihapus!');
                                    // Reload data to reflect changes
                                    loadData(currentPage);
                                } else {
                                    alert('Gagal menghapus riwayat: ' + response.message);
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error("Delete History Error:", xhr.responseText);
                                alert('Terjadi kesalahan saat menghapus riwayat. Periksa konsol untuk detail.');
                            }
                        });
                    }
                );
            });

            // Delete All History Button Click
            $(document).on('click', '#delete-all-history', function() {
                // Gunakan confirmAction sebagai pengganti confirm
                confirmAction(
                    'PERHATIAN: Apakah Anda yakin ingin menghapus SEMUA riwayat karyawan? Tindakan ini tidak dapat dibatalkan dan akan menghapus seluruh catatan riwayat.',
                    function() {
                        // Double confirmation for critical action
                        confirmAction(
                            'Konfirmasi sekali lagi: Anda akan menghapus SEMUA riwayat karyawan. Lanjutkan?',
                            function() {
                                // Show loading indicator
                                $('#loading-indicator').show();

                                // AJAX call to delete all history
                                $.ajax({
                                    url: 'delete_all_employee_history.php',
                                    type: 'POST',
                                    dataType: 'json',
                                    success: function(response) {
                                        $('#loading-indicator').hide();
                                        if (response.success) {
                                            alert('Semua riwayat berhasil dihapus! ' + response.count + ' catatan telah dihapus.');
                                            // Reload data to reflect changes
                                            loadData(1);
                                        } else {
                                            alert('Gagal menghapus semua riwayat: ' + response.message);
                                        }
                                    },
                                    error: function(xhr, status, error) {
                                        $('#loading-indicator').hide();
                                        console.error("Delete All History Error:", xhr.responseText);
                                        alert('Terjadi kesalahan saat menghapus semua riwayat. Periksa konsol untuk detail.');
                                    }
                                });
                            }
                        );
                    }
                );
            });
        });
    </script>
</body>
</html>
