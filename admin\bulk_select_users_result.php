<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Ambil hasil dari session
$results = $_SESSION['bulk_create_results'] ?? [];
$success_message = $_SESSION['bulk_create_success_message'] ?? '';
$role_id = $_SESSION['bulk_create_role_id'] ?? 0;

// Hapus data dari session setelah diambil
unset($_SESSION['bulk_create_results']);
unset($_SESSION['bulk_create_success_message']);
unset($_SESSION['bulk_create_role_id']);

// Query untuk mengambil daftar role
$query = "SELECT * FROM roles";
$result = $conn->query($query);
$roles = [];
while ($row = $result->fetch_assoc()) {
    $roles[$row['id']] = $row['role_name'];
}
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    html, body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 0;
        color: #333;
    }

    body {
        display: flex;
        flex-direction: column;
    }

    .content-wrapper {
        flex: 1;
        width: 100%;
        display: flex;
        flex-direction: column;
    }

    .container-form {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 80px 20px 40px 20px; /* Increased top padding to prevent navbar overlap */
        width: 100%;
    }

    .form-container {
        background: #fff;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        width: 100%;
        max-width: 800px;
        transition: all 0.3s ease;
    }

    .form-container:hover {
        box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
    }

    h2 {
        text-align: center;
        color: #BF0000;
        margin-bottom: 25px;
        font-weight: 600;
        position: relative;
        padding-bottom: 10px;
    }

    h2:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background-color: #BF0000;
        border-radius: 3px;
    }

    h3 {
        color: #BF0000;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .success-message {
        color: #2e7d32;
        font-weight: 500;
        text-align: center;
        margin-bottom: 20px;
        padding: 10px;
        background-color: #e8f5e9;
        border-radius: 8px;
        border-left: 4px solid #2e7d32;
    }

    .back-link {
        display: block;
        text-align: center;
        margin-top: 20px;
        color: #BF0000;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .back-link:hover {
        color: #a00000;
        text-decoration: underline;
    }

    .results-container {
        margin-top: 30px;
        border-top: 1px solid #ddd;
        padding-top: 20px;
        overflow-x: auto;
    }
    .results-container table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
    table-layout: auto; /* <== tambahkan ini */
}
.results-container th, .results-container td {
    border: 1px solid #eee;
    padding: 12px 15px;
    text-align: left;
    word-wrap: break-word;
    white-space: normal;       /* <== penting */
    max-width: 200px;          /* <== opsional, biar kolom tidak terlalu lebar */
    overflow-wrap: break-word; /* <== bantu pemisahan kata panjang */
}


    .results-container th {
        word-wrap: break-word;
        white-space: normal;
        background-color: #BF0000;
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85em;
        letter-spacing: 0.5px;
    }

    .results-container tr {
        transition: all 0.2s ease;
    }

    .results-container tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .results-container tr:hover {
        background-color: #f1f1f1;
    }

    .results-container tr.success {
        background-color: #e8f5e9;
    }

    .results-container tr.error {
        background-color: #ffebee;
    }

    .results-container tr.success:hover {
        background-color: #d5ecd7;
    }

    .results-container tr.error:hover {
        background-color: #f8d7da;
    }

    /* Status badges */
    .status-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.85em;
        font-weight: 600;
    }

    .status-success {
        background-color: #e8f5e9;
        color: #2e7d32;
        border: 1px solid #c8e6c9;
    }

    .status-error {
        background-color: #ffebee;
        color: #c62828;
        border: 1px solid #ffcdd2;
    }

    /* Jarak untuk navbar */
    .jarak {
        height: 100px;
    }

    /* Mobile responsive styles */
    @media screen and (max-width: 768px) {
        .container-form {
            padding: 120px 10px 20px 10px;
        }

        .form-container {
            padding: 20px 15px;
            border-radius: 8px;
        }

        .results-container th, .results-container td {
            padding: 8px 10px;
            font-size: 0.9em;
        }

        /* Stack table on mobile */
        .results-container table,
        .results-container thead,
        .results-container tbody,
        .results-container th,
        .results-container td,
        .results-container tr {
            display: block;
        }

        .results-container thead tr {
            position: absolute;
            top: -9999px;
            left: -9999px;
        }

        .results-container tr {
            border: 1px solid #ddd;
            margin-bottom: 15px;
            border-radius: 8px;
            overflow: hidden;
        }

        .results-container td {
            border: none;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 50%;
            text-align: right;
        }

        .results-container td:last-child {
            border-bottom: 0;
        }

        .results-container td:before {
            position: absolute;
            top: 8px;
            left: 10px;
            width: 45%;
            padding-right: 10px;
            white-space: nowrap;
            font-weight: 600;
            text-align: left;
        }

        /* Add labels for each td */
        .results-container td:nth-of-type(1):before { content: "Nama"; }
        .results-container td:nth-of-type(2):before { content: "NIK"; }
        .results-container td:nth-of-type(3):before { content: "Email"; }
        .results-container td:nth-of-type(4):before { content: "Role"; }
        .results-container td:nth-of-type(5):before { content: "Status"; }
        .results-container td:nth-of-type(6):before { content: "Pesan"; }
    }
</style>
<body>
<?php include '../config/navbarb.php'; ?>
<div class="jarak"></div>
<div class="content-wrapper">
    <div class="container-form">
        <div class="form-container">
            <h2>Hasil Pembuatan Akun</h2>

            <?php if (!empty($success_message)) { ?>
                <div class="success-message">
                    <p><?php echo $success_message; ?></p>
                </div>
            <?php } ?>

            <?php if (!empty($results)) { ?>
                <div class="results-container">
                    <h3>Detail Hasil</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Nama</th>
                                <th>NIK</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Pesan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $result) { ?>
                                <tr class="<?php echo $result['status']; ?>">
                                    <td><?php echo htmlspecialchars($result['data']['name']); ?></td>
                                    <td><?php echo htmlspecialchars($result['data']['nik']); ?></td>
                                    <td><?php echo htmlspecialchars($result['data']['email']); ?></td>
                                    <td><?php echo htmlspecialchars($roles[$role_id] ?? 'Unknown'); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $result['status']; ?>">
                                            <?php if ($result['status'] == 'success'): ?>
                                                <i class="fas fa-check-circle"></i> Berhasil
                                            <?php else: ?>
                                                <i class="fas fa-times-circle"></i> Gagal
                                            <?php endif; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php 
                                        if ($result['status'] == 'error' && $result['message'] == 'Nama sudah digunakan'): 
                                            echo 'Nama sudah digunakan. Sistem akan otomatis menambahkan 4 digit terakhir NIK ke nama pengguna.'; 
                                        else: 
                                            echo htmlspecialchars($result['message']); 
                                        endif; 
                                        ?>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            <?php } else { ?>
                <div style="text-align: center; padding: 20px;">
                    <p>Tidak ada data hasil yang tersedia.</p>
                </div>
            <?php } ?>

            <a href="bulk_select_users.php" class="back-link">Kembali ke Form Pembuatan Akun</a>
            <a href="index.php" class="back-link" style="margin-top: 10px;">Kembali ke Dashboard</a>
        </div>
    </div>
</div>
<?php include '../config/footer.php'; ?>
</body>
</html>