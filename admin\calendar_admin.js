/**
 * Admin Calendar Management JavaScript
 */

// Calendar variables
let currentDate = new Date();
let currentMonth = currentDate.getMonth();
let currentYear = currentDate.getFullYear();
let isUserViewMode = false; // Track if we're in user view mode

function initializeCalendar() {

    const monthNames = [
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'April', '<PERSON>', '<PERSON><PERSON>',
        '<PERSON><PERSON>', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];

    const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

    // Calendar elements
    const calendarTitle = document.getElementById('calendarTitle');
    const calendarGrid = document.getElementById('calendarGrid');
    const prevMonthBtn = document.getElementById('prevMonth');
    const nextMonthBtn = document.getElementById('nextMonth');
    const todayBtn = document.getElementById('todayBtn');

    // Event listeners
    prevMonthBtn.addEventListener('click', () => {
        currentMonth--;
        if (currentMonth < 0) {
            currentMonth = 11;
            currentYear--;
        }
        renderCalendar();
    });

    nextMonthBtn.addEventListener('click', () => {
        currentMonth++;
        if (currentMonth > 11) {
            currentMonth = 0;
            currentYear++;
        }
        renderCalendar();
    });

    todayBtn.addEventListener('click', () => {
        const today = new Date();
        currentMonth = today.getMonth();
        currentYear = today.getFullYear();
        renderCalendar();
    });

    // Functions
    function renderCalendar() {
        calendarTitle.textContent = `${monthNames[currentMonth]} ${currentYear}`;
        calendarGrid.innerHTML = '';

        // Add day headers
        dayNames.forEach(day => {
            const dayHeader = document.createElement('div');
            dayHeader.className = 'calendar-day-header';
            dayHeader.textContent = day;
            calendarGrid.appendChild(dayHeader);
        });

        // Get first day of month and number of days
        const firstDay = new Date(currentYear, currentMonth, 1).getDay();
        const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
        const daysInPrevMonth = new Date(currentYear, currentMonth, 0).getDate();

        // Add empty cells for previous month
        for (let i = firstDay - 1; i >= 0; i--) {
            const dayCell = createDayCell(daysInPrevMonth - i, true);
            calendarGrid.appendChild(dayCell);
        }

        // Add days of current month
        for (let day = 1; day <= daysInMonth; day++) {
            const dayCell = createDayCell(day, false);
            calendarGrid.appendChild(dayCell);
        }

        // Add empty cells for next month
        const totalCells = calendarGrid.children.length - 7;
        const remainingCells = 42 - totalCells;
        for (let day = 1; day <= remainingCells; day++) {
            const dayCell = createDayCell(day, true);
            calendarGrid.appendChild(dayCell);
        }

        loadTrainingEvents();
    }

    function createDayCell(day, isOtherMonth) {
        const dayCell = document.createElement('div');
        dayCell.className = 'calendar-day';

        if (isOtherMonth) {
            dayCell.classList.add('other-month');
        }

        // Check if it's today
        const today = new Date();
        if (!isOtherMonth &&
            day === today.getDate() &&
            currentMonth === today.getMonth() &&
            currentYear === today.getFullYear()) {
            dayCell.classList.add('today');
        }

        const dayNumber = document.createElement('div');
        dayNumber.className = 'fw-bold mb-2';
        dayNumber.textContent = day;
        dayCell.appendChild(dayNumber);

        // Add event button
        const addBtn = document.createElement('button');
        addBtn.className = 'add-event-btn';
        addBtn.innerHTML = '<i class="fas fa-plus"></i>';
        addBtn.title = 'Tambah Training';

        // Store date for event loading
        let cellDate;
        if (isOtherMonth) {
            if (day > 15) {
                cellDate = new Date(currentYear, currentMonth - 1, day);
            } else {
                cellDate = new Date(currentYear, currentMonth + 1, day);
            }
        } else {
            cellDate = new Date(currentYear, currentMonth, day);
        }

        // Use local date format to avoid timezone issues
        const year = cellDate.getFullYear();
        const month = String(cellDate.getMonth() + 1).padStart(2, '0');
        const dayOfMonth = String(cellDate.getDate()).padStart(2, '0');
        const dateStr = `${year}-${month}-${dayOfMonth}`;
        dayCell.dataset.date = dateStr;

        addBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            // Navigate to training_management.php with internal tab, selected date, and auto-open modal
            window.location.href = `training_management.php?training_type=internal&date=${dateStr}&auto_open=1`;
        });

        dayCell.appendChild(addBtn);
        return dayCell;
    }

    function loadTrainingEvents() {
        const startDate = new Date(currentYear, currentMonth, 1);
        const endDate = new Date(currentYear, currentMonth + 1, 0);

        // Use local date format to avoid timezone issues
        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${String(startDate.getDate()).padStart(2, '0')}`;
        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;

        fetch(`api/get_admin_training_events.php?start=${startDateStr}&end=${endDateStr}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayTrainingEvents(data.events);
                } else {
                    console.error('Error loading training events:', data.message);
                }
            })
            .catch(error => {
                console.error('Error fetching training events:', error);
            });
    }

    function displayTrainingEvents(events) {
        document.querySelectorAll('.calendar-event').forEach(event => event.remove());

        // Check if hidden events should be shown
        const showHiddenEvents = document.getElementById('showHiddenEvents').checked;

        // Track unique events for color legend
        const uniqueEvents = new Map();

        events.forEach(event => {
            // Skip hidden events if toggle is off
            if (event.is_hidden && !showHiddenEvents) {
                return;
            }

            // Track unique events for legend
            const eventKey = `${event.id}_${event.type}`;
            if (!uniqueEvents.has(eventKey)) {
                uniqueEvents.set(eventKey, {
                    id: event.id,
                    title: event.title,
                    type: event.type,
                    colors: generateEventColors(event.id, event.title, event.type)
                });
            }

            // Handle multi-day events
            if (event.end_date && event.end_date !== event.date) {
                displayMultiDayEvent(event);
            } else {
                displaySingleDayEvent(event);
            }
        });

        // Update color legend
        updateColorLegend(Array.from(uniqueEvents.values()));
    }

    function displaySingleDayEvent(event) {
        const dayCell = document.querySelector(`[data-date="${event.date}"]`);
        if (dayCell) {
            const eventElement = createEventElement(event, 'single');
            const addBtn = dayCell.querySelector('.add-event-btn');
            dayCell.insertBefore(eventElement, addBtn);
        }
    }

    function displayMultiDayEvent(event) {
        const startDate = new Date(event.date);
        const endDate = new Date(event.end_date);

        // Calculate all dates between start and end (inclusive)
        const currentDate = new Date(startDate);
        let dayIndex = 0;

        while (currentDate <= endDate) {
            const dateStr = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')}`;
            const dayCell = document.querySelector(`[data-date="${dateStr}"]`);

            if (dayCell) {
                const isFirstDay = dayIndex === 0;
                const isLastDay = currentDate.getTime() === endDate.getTime();
                const isMiddleDay = !isFirstDay && !isLastDay;

                let eventType = 'multi';
                if (isFirstDay) eventType = 'multi-start';
                else if (isLastDay) eventType = 'multi-end';
                else if (isMiddleDay) eventType = 'multi-middle';

                const eventElement = createEventElement(event, eventType, dayIndex);
                const addBtn = dayCell.querySelector('.add-event-btn');
                dayCell.insertBefore(eventElement, addBtn);
            }

            currentDate.setDate(currentDate.getDate() + 1);
            dayIndex++;
        }
    }

    function createEventElement(event, displayType, dayIndex = 0) {
        const eventElement = document.createElement('div');
        eventElement.className = `calendar-event ${event.type} ${displayType}`;

        // Add hidden class if event is hidden
        if (event.is_hidden) {
            eventElement.classList.add('event-hidden');
        }

        // Generate unique color for this event
        const eventColors = generateEventColors(event.id, event.title, event.type);
        applyEventColors(eventElement, eventColors, displayType);

        // Set event text based on display type
        let eventText = event.title;
        if (displayType === 'multi-start') {
            eventText = `📅 ${event.title}`;
        } else if (displayType === 'multi-middle') {
            eventText = `↔️ ${event.title}`;
        } else if (displayType === 'multi-end') {
            eventText = `🏁 ${event.title}`;
        }

        eventElement.textContent = eventText;
        eventElement.dataset.eventId = event.id;
        eventElement.dataset.eventType = event.type;
        eventElement.dataset.isHidden = event.is_hidden;
        eventElement.dataset.displayType = displayType;

        // Create tooltip text
        let tooltipText = `${event.title}\n${event.time || 'Waktu belum ditentukan'}\n${event.location || 'Lokasi belum ditentukan'}`;

        if (event.end_date && event.end_date !== event.date) {
            const startDate = new Date(event.date).toLocaleDateString('id-ID');
            const endDate = new Date(event.end_date).toLocaleDateString('id-ID');
            tooltipText += `\n📅 Multi-hari: ${startDate} - ${endDate}`;

            if (displayType === 'multi-start') {
                tooltipText += '\n🚀 Hari pertama';
            } else if (displayType === 'multi-middle') {
                tooltipText += `\n📍 Hari ke-${dayIndex + 1}`;
            } else if (displayType === 'multi-end') {
                tooltipText += '\n🏁 Hari terakhir';
            }
        }

        if (event.is_hidden) {
            tooltipText += '\n🔒 Training ini disembunyikan dari tampilan publik';
        }

        if (!event.is_confirmed) {
            tooltipText += '\n⚠️ Tanggal belum dikonfirmasi';
        }

        eventElement.title = tooltipText;

        eventElement.addEventListener('click', (e) => {
            e.stopPropagation();
            if (isUserViewMode) {
                showUserTrainingDetail(event.id, event.type);
            } else {
                showTrainingDetail(event.id, event.type);
            }
        });

        return eventElement;
    }

    // Color generation functions for unique event colors
    function generateEventColors(eventId, eventTitle, eventType) {
        // Create a unique seed from event ID and title
        const seed = hashString(eventId + eventTitle);
// Define more colorful and vibrant color palettes for different types
const colorPalettes = {
    online: [
        { primary: '#ff6f61', secondary: '#ffab91', accent: '#d84315' }, // Coral
        { primary: '#42a5f5', secondary: '#90caf9', accent: '#1565c0' }, // Sky blue
        { primary: '#ab47bc', secondary: '#ce93d8', accent: '#6a1b9a' }, // Orchid
        { primary: '#26c6da', secondary: '#80deea', accent: '#00838f' }, // Cyan
        { primary: '#ec407a', secondary: '#f48fb1', accent: '#ad1457' }, // Pink
        { primary: '#66bb6a', secondary: '#a5d6a7', accent: '#2e7d32' }, // Spring green
        { primary: '#ffee58', secondary: '#fff59d', accent: '#f9a825' }, // Yellow
        { primary: '#ffa726', secondary: '#ffcc80', accent: '#ef6c00' }  // Orange
    ],
    offline: [
        { primary: '#ef5350', secondary: '#ef9a9a', accent: '#c62828' }, // Red
        { primary: '#7e57c2', secondary: '#b39ddb', accent: '#512da8' }, // Purple
        { primary: '#26a69a', secondary: '#80cbc4', accent: '#004d40' }, // Teal
        { primary: '#ff7043', secondary: '#ffab91', accent: '#d84315' }, // Deep orange
        { primary: '#8d6e63', secondary: '#bcaaa4', accent: '#4e342e' }, // Brown
        { primary: '#78909c', secondary: '#b0bec5', accent: '#37474f' }, // Blue grey
        { primary: '#9ccc65', secondary: '#dce775', accent: '#558b2f' }, // Lime green
        { primary: '#f06292', secondary: '#f8bbd0', accent: '#c2185b' }  // Rose pink
    ]
};


        // Select palette based on event type
        const palette = colorPalettes[eventType] || colorPalettes.offline;
        const colorIndex = seed % palette.length;
        const selectedColors = palette[colorIndex];

        // Generate variations for multi-day events
        return {
            primary: selectedColors.primary,
            secondary: selectedColors.secondary,
            accent: selectedColors.accent,
            light: lightenColor(selectedColors.primary, 20),
            dark: darkenColor(selectedColors.primary, 20),
            gradient: {
                start: selectedColors.primary,
                middle: selectedColors.secondary,
                end: selectedColors.accent
            }
        };
    }

    function hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }

    function lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    function darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
            (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
            (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }

    function applyEventColors(element, colors, displayType) {
        // Apply colors based on display type
        if (displayType === 'single') {
            element.style.background = `linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 100%)`;
            element.style.borderLeft = `3px solid ${colors.accent}`;
        } else if (displayType === 'multi-start') {
            element.style.background = `linear-gradient(90deg, ${colors.gradient.start} 0%, ${colors.gradient.middle} 100%)`;
            element.style.borderLeft = `3px solid ${colors.accent}`;
        } else if (displayType === 'multi-middle') {
            element.style.background = `linear-gradient(90deg, ${colors.gradient.middle} 0%, ${colors.gradient.middle} 100%)`;
        } else if (displayType === 'multi-end') {
            element.style.background = `linear-gradient(90deg, ${colors.gradient.middle} 0%, ${colors.gradient.end} 100%)`;
            element.style.borderRight = `3px solid ${colors.accent}`;
        }

        // Set text color based on background brightness
        const textColor = getContrastColor(colors.primary);
        element.style.color = textColor;

        // Add subtle shadow for depth
        element.style.boxShadow = `0 2px 4px ${colors.primary}33`;

        // Store colors in dataset for later use
        element.dataset.primaryColor = colors.primary;
        element.dataset.secondaryColor = colors.secondary;
        element.dataset.accentColor = colors.accent;
    }

    function getContrastColor(hexColor) {
        // Convert hex to RGB
        const r = parseInt(hexColor.slice(1, 3), 16);
        const g = parseInt(hexColor.slice(3, 5), 16);
        const b = parseInt(hexColor.slice(5, 7), 16);

        // Calculate luminance
        const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

        // Return black or white based on luminance
        return luminance > 0.5 ? '#000000' : '#ffffff';
    }

    function updateColorLegend(uniqueEvents) {
        // Find or create color legend container
        let legendContainer = document.getElementById('dynamicColorLegend');
        if (!legendContainer) {
            // Create legend container if it doesn't exist
            const calendarContainer = document.querySelector('.calendar-container');
            if (calendarContainer) {
                legendContainer = document.createElement('div');
                legendContainer.id = 'dynamicColorLegend';
                legendContainer.className = 'dynamic-color-legend';
                calendarContainer.appendChild(legendContainer);
            }
        }

        if (!legendContainer || uniqueEvents.length === 0) {
            if (legendContainer) {
                legendContainer.style.display = 'none';
            }
            return;
        }

        // Sort events by type and title
        uniqueEvents.sort((a, b) => {
            if (a.type !== b.type) {
                return a.type === 'online' ? -1 : 1;
            }
            return a.title.localeCompare(b.title);
        });

        // Generate legend HTML
        let legendHTML = `
            <div class="legend-header">
                <h6><i class="fas fa-palette"></i> Event Colors</h6>
                <button class="btn btn-sm btn-outline-secondary" onclick="toggleColorLegend()">
                    <i class="fas fa-eye" id="legendToggleIcon"></i>
                </button>
            </div>
            <div class="legend-content" id="legendContent">
        `;

        // Group by type
        const onlineEvents = uniqueEvents.filter(e => e.type === 'online');
        const offlineEvents = uniqueEvents.filter(e => e.type === 'offline');

        if (onlineEvents.length > 0) {
            legendHTML += '<div class="legend-group"><h6 class="legend-group-title">💻 Online Training</h6>';
            onlineEvents.forEach(event => {
                const shortTitle = event.title.length > 25 ? event.title.substring(0, 25) + '...' : event.title;
                legendHTML += `
                    <div class="legend-item" title="${event.title}">
                        <div class="legend-color-sample" style="background: linear-gradient(135deg, ${event.colors.primary} 0%, ${event.colors.secondary} 100%); border-left: 3px solid ${event.colors.accent};"></div>
                        <span class="legend-text">${shortTitle}</span>
                    </div>
                `;
            });
            legendHTML += '</div>';
        }

        if (offlineEvents.length > 0) {
            legendHTML += '<div class="legend-group"><h6 class="legend-group-title">🏢 Offline Training</h6>';
            offlineEvents.forEach(event => {
                const shortTitle = event.title.length > 25 ? event.title.substring(0, 25) + '...' : event.title;
                legendHTML += `
                    <div class="legend-item" title="${event.title}">
                        <div class="legend-color-sample" style="background: linear-gradient(135deg, ${event.colors.primary} 0%, ${event.colors.secondary} 100%); border-left: 3px solid ${event.colors.accent};"></div>
                        <span class="legend-text">${shortTitle}</span>
                    </div>
                `;
            });
            legendHTML += '</div>';
        }

        legendHTML += '</div>';

        legendContainer.innerHTML = legendHTML;
        legendContainer.style.display = 'block';

        // Add CSS if not already added
        addColorLegendCSS();
    }

    function addColorLegendCSS() {
        if (document.getElementById('colorLegendCSS')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'colorLegendCSS';
        style.textContent = `
            .dynamic-color-legend {
                background: white;
                border-radius: 8px;
                padding: 15px;
                margin-top: 15px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                border: 1px solid #e9ecef;
            }

            .legend-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid #e9ecef;
            }

            .legend-header h6 {
                margin: 0;
                color: #495057;
                font-weight: 600;
            }

            .legend-content {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
            }

            .legend-group {
                background: #f8f9fa;
                padding: 12px;
                border-radius: 6px;
            }

            .legend-group-title {
                font-size: 14px;
                font-weight: 600;
                margin: 0 0 10px 0;
                color: #495057;
            }

            .legend-item {
                display: flex;
                align-items: center;
                margin: 6px 0;
                padding: 4px;
                border-radius: 4px;
                transition: background-color 0.2s ease;
            }

            .legend-item:hover {
                background-color: rgba(0,0,0,0.05);
            }

            .legend-color-sample {
                width: 24px;
                height: 16px;
                border-radius: 3px;
                margin-right: 10px;
                flex-shrink: 0;
                border: 1px solid rgba(0,0,0,0.1);
            }

            .legend-text {
                font-size: 13px;
                color: #495057;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            @media (max-width: 768px) {
                .legend-content {
                    grid-template-columns: 1fr;
                }

                .legend-text {
                    font-size: 12px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    window.toggleColorLegend = function() {
        const content = document.getElementById('legendContent');
        const icon = document.getElementById('legendToggleIcon');

        if (content && icon) {
            if (content.style.display === 'none') {
                content.style.display = 'grid';
                icon.className = 'fas fa-eye';
            } else {
                content.style.display = 'none';
                icon.className = 'fas fa-eye-slash';
            }
        }
    };

    window.showTrainingDetail = function(eventId, eventType) {
        const modal = new bootstrap.Modal(document.getElementById('trainingModal'));
        document.getElementById('modalTitle').textContent = 'Loading...';
        document.getElementById('modalBody').innerHTML = '<div class="text-center p-4"><i class="fas fa-spinner fa-spin fa-2x"></i><br>Loading...</div>';
        modal.show();

        // Use simplified API for better performance
        fetch(`api/get_training_detail_simple.php?id=${eventId}&type=${eventType}`)
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('API Response:', data);
                if (data.success) {
                    displayTrainingDetail(data.training);
                } else {
                    document.getElementById('modalBody').innerHTML = `<div class="alert alert-danger">Error: ${data.message}</div>`;
                }
            })
            .catch(error => {
                console.error('Error fetching training detail:', error);
                document.getElementById('modalBody').innerHTML = `<div class="alert alert-danger">Error loading training detail: ${error.message}</div>`;
            });
    }

    function displayTrainingDetail(training) {
        document.getElementById('modalTitle').textContent = training.title;

        // Format date display for multi-day events
        let dateDisplay = training.date;
        let durationInfo = '';

        // Use server-provided multi-day info if available
        if (training.is_multiday && training.total_days) {
            durationInfo = `<tr><td><strong>Durasi:</strong></td><td><span class="badge bg-info">${training.total_days} hari</span></td></tr>`;
        } else if (training.raw_date && training.raw_end_date && training.raw_end_date !== training.raw_date) {
            // Fallback to client-side calculation if server data not available
            try {
                const startDate = new Date(training.raw_date);
                const endDate = new Date(training.raw_end_date);

                if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
                    const diffTime = Math.abs(endDate - startDate);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                    durationInfo = `<tr><td><strong>Durasi:</strong></td><td><span class="badge bg-info">${diffDays} hari</span></td></tr>`;

                    // Update display with proper format
                    const startFormatted = startDate.toLocaleDateString('id-ID');
                    const endFormatted = endDate.toLocaleDateString('id-ID');
                    dateDisplay = `${startFormatted} - ${endFormatted}`;
                }
            } catch (error) {
                console.error('Error parsing dates:', error);
                // Keep original dateDisplay from server
            }
        }

        let html = `
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6><i class="fas fa-info-circle text-primary"></i> Informasi Training</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Tanggal:</strong></td><td>${dateDisplay}</td></tr>
                        ${durationInfo}
                        <tr><td><strong>Waktu:</strong></td><td>${training.time || 'Belum ditentukan'}</td></tr>
                        <tr><td><strong>Lokasi:</strong></td><td>${training.location || 'Belum ditentukan'}</td></tr>
                        <tr><td><strong>Trainer:</strong></td><td>${training.trainer || 'Belum ditentukan'}</td></tr>
                        <tr><td><strong>Max Peserta:</strong></td><td>${training.max_participants || 'Tidak terbatas'}</td></tr>
                        <tr><td><strong>Status:</strong></td><td><span class="badge bg-${getStatusColor(training.status)}">${training.status}</span></td></tr>
                        <tr><td><strong>Konfirmasi:</strong></td><td><span class="badge bg-${training.is_confirmed ? 'success' : 'warning'}">${training.is_confirmed ? 'Dikonfirmasi' : 'Belum Dikonfirmasi'}</span></td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-tools text-success"></i> Actions</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary btn-sm" onclick="editTraining('${training.id}', '${training.type}')">
                            <i class="fas fa-edit"></i> Edit Training
                        </button>
                        <button class="btn btn-info btn-sm" onclick="manageParticipants('${training.id}', '${training.type}')">
                            <i class="fas fa-users"></i> ${training.type === 'offline' ? 'Kelola Peserta' : 'Peserta Training'}
                        </button>
                        <button class="btn btn-${training.is_hidden ? 'success' : 'warning'} btn-sm" onclick="toggleTrainingVisibility('${training.id}', '${training.type}', ${training.is_hidden})">
                            <i class="fas fa-${training.is_hidden ? 'eye' : 'eye-slash'}"></i> ${training.is_hidden ? 'Tampilkan' : 'Sembunyikan'}
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteTraining('${training.id}', '${training.type}')">
                            <i class="fas fa-trash"></i> Hapus Training
                        </button>
                    </div>
                </div>
            </div>
        `;

        if (training.description) {
            html += `<div class="mb-3"><h6><i class="fas fa-file-alt text-info"></i> Deskripsi</h6><p class="text-muted">${training.description}</p></div>`;
        }

        // Multi-day training info
        if (training.is_multiday) {
            html += `
                <div class="alert alert-info mb-3">
                    <h6><i class="fas fa-calendar-alt"></i> Training Multi-Hari</h6>
                    <p class="mb-0">Training ini berlangsung selama <strong>${training.total_days} hari</strong> dari ${training.date}</p>
                </div>
            `;
        }

        if (training.participants && training.participants.length > 0) {
            html += `<div class="mb-3"><h6><i class="fas fa-users text-warning"></i> Peserta Training (${training.participants.length})</h6>`;

            if (training.type === 'offline' && training.is_multiday) {
                // For multi-day offline training, show attendance summary
                html += `<div class="table-responsive" style="max-height: 300px; overflow-y: auto;"><table class="table table-sm table-striped">`;
                html += `<thead class="table-dark"><tr><th>Nama</th><th>NIK</th><th>Departemen</th><th>Jabatan</th><th>Kehadiran</th><th>Status</th></tr></thead><tbody>`;

                training.participants.forEach(participant => {
                    html += `<tr>
                        <td>${participant.name}</td>
                        <td>${participant.nik}</td>
                        <td>${participant.dept}</td>
                        <td>${participant.jabatan}</td>
                        <td><span class="badge bg-info">${participant.attendance_summary}</span></td>
                        <td><span class="badge bg-${getParticipantStatusColor(participant.status)}">${participant.status}</span></td>
                    </tr>`;
                });
            } else {
                // For single day or online training
                html += `<div class="table-responsive" style="max-height: 300px; overflow-y: auto;"><table class="table table-sm table-striped">`;
                html += `<thead class="table-dark"><tr><th>Nama</th><th>NIK</th><th>Departemen</th><th>Jabatan</th><th>Status</th></tr></thead><tbody>`;

                training.participants.forEach(participant => {
                    html += `<tr>
                        <td>${participant.name}</td>
                        <td>${participant.nik}</td>
                        <td>${participant.dept}</td>
                        <td>${participant.jabatan}</td>
                        <td><span class="badge bg-${getParticipantStatusColor(participant.status)}">${participant.status}</span></td>
                    </tr>`;
                });
            }

            html += `</tbody></table></div></div>`;
        }

        document.getElementById('modalBody').innerHTML = html;
    }

    function getStatusColor(status) {
        switch(status?.toLowerCase()) {
            case 'active': return 'success';
            case 'completed': return 'primary';
            case 'cancelled': return 'danger';
            case 'approved': return 'success';
            default: return 'secondary';
        }
    }

    function getParticipantStatusColor(status) {
        switch(status?.toLowerCase()) {
            case 'active': return 'success';
            case 'hadir': return 'success';
            case 'tidak hadir': return 'danger';
            case 'completed': return 'primary';
            default: return 'secondary';
        }
    }

    // Initialize calendar
    renderCalendar();
}

// Global functions for modal actions
function addTraining(type) {
    showAddTrainingModal(null, type);
}

function showAddTrainingModal(date = null, type = 'offline') {
    document.getElementById('addModalTitle').textContent = `Tambah Training ${type === 'offline' ? 'Offline' : 'Online'}`;
    document.getElementById('trainingForm').reset();
    document.getElementById('trainingId').value = '';
    document.getElementById('trainingType').value = type;

    if (date) {
        document.getElementById('trainingDate').value = date;
    }

    if (type === 'online') {
        document.getElementById('trainingLocation').value = 'Online';
    }

    const addTrainingModal = new bootstrap.Modal(document.getElementById('addTrainingModal'));
    addTrainingModal.show();
}

function editTraining(id, type) {
    if (type === 'online') {
        // Redirect to edit_training.php for online training
        window.location.href = `edit_training.php?id=${id}`;
    } else {
        // Redirect to training_management.php for offline training
        window.location.href = `training_management.php?training_type=internal&id=${id}`;
    }
}

function saveTraining() {
    const form = document.getElementById('trainingForm');
    const formData = new FormData(form);

    fetch('api/save_training.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.text();
    })
    .then(text => {
        console.log('Raw response:', text);

        try {
            const data = JSON.parse(text);
            console.log('Parsed data:', data);

            if (data.success) {
                alert('Training berhasil disimpan!');
                const addTrainingModal = bootstrap.Modal.getInstance(document.getElementById('addTrainingModal'));
                if (addTrainingModal) {
                    addTrainingModal.hide();
                }
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        } catch (jsonError) {
            console.error('JSON parse error:', jsonError);
            console.error('Response text:', text);
            alert('Error: Invalid response format. Check console for details.');
        }
    })
    .catch(error => {
        console.error('Error saving training:', error);
        alert('Error saving training: ' + error.message);
    });
}

function deleteTraining(id, type) {
    if (confirm('Apakah Anda yakin ingin menghapus training ini?')) {
        fetch('api/delete_training.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: id,
                type: type
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Training berhasil dihapus!');
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error deleting training:', error);
            alert('Error deleting training');
        });
    }
}

function manageParticipants(id, type) {
    if (type === 'online') {
        // For online training, redirect to edit_training.php with tab parameter
        window.location.href = `edit_training.php?id=${id}&tab=participants`;
    } else {
        // For offline training, redirect to view_offline_attendance.php
        window.location.href = `view_offline_attendance.php?id=${id}`;
    }
}

function exportCalendar() {
    window.open('api/export_calendar.php', '_blank');
}

function viewReports() {
    window.open('training_reports.php', '_blank');
}

// Toggle between admin and user view mode
function toggleCalendarView() {
    isUserViewMode = !isUserViewMode;

    const viewToggleBtn = document.getElementById('viewToggleBtn');
    const viewToggleIcon = document.getElementById('viewToggleIcon');
    const viewToggleText = document.getElementById('viewToggleText');
    const viewModeInfo = document.getElementById('viewModeInfo');
    const showHiddenToggle = document.getElementById('showHiddenEvents');

    if (isUserViewMode) {
        // Switch to user view mode
        viewToggleBtn.className = 'btn btn-warning w-100';
        viewToggleIcon.className = 'fas fa-cog me-2';
        viewToggleText.textContent = 'Mode Admin';
        viewModeInfo.style.display = 'block';

        // Hide admin elements
        document.querySelectorAll('.add-event-btn').forEach(btn => {
            btn.style.display = 'none';
        });

        // Force hide hidden events in user mode
        if (showHiddenToggle) {
            showHiddenToggle.checked = false;
            showHiddenToggle.disabled = true;
            showHiddenToggle.parentElement.style.opacity = '0.5';
        }

        // Remove admin actions from event details
        document.querySelectorAll('.calendar-event').forEach(event => {
            event.style.cursor = 'default';
            // Remove click handlers for admin actions
            const newEvent = event.cloneNode(true);
            event.parentNode.replaceChild(newEvent, event);

            // Add simple click handler for basic info only
            newEvent.addEventListener('click', (e) => {
                e.stopPropagation();
                showUserTrainingDetail(newEvent.dataset.eventId, newEvent.dataset.eventType);
            });
        });

    } else {
        // Switch back to admin view mode
        viewToggleBtn.className = 'btn btn-info w-100';
        viewToggleIcon.className = 'fas fa-eye me-2';
        viewToggleText.textContent = 'Mode User';
        viewModeInfo.style.display = 'none';

        // Show admin elements
        document.querySelectorAll('.add-event-btn').forEach(btn => {
            btn.style.display = 'block';
        });

        // Re-enable hidden events toggle
        if (showHiddenToggle) {
            showHiddenToggle.checked = true;
            showHiddenToggle.disabled = false;
            showHiddenToggle.parentElement.style.opacity = '1';
        }
    }

    // Reload calendar to apply changes
    initializeCalendar();
}

// Simplified training detail view for user mode
function showUserTrainingDetail(trainingId, type) {
    // Show basic training info without admin actions
    fetch(`api/get_training_detail_simple.php?id=${trainingId}&type=${type}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                displayUserTrainingDetail(data.training);
            } else {
                document.getElementById('modalBody').innerHTML = `<div class="alert alert-danger">Error: ${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error fetching training detail:', error);
            document.getElementById('modalBody').innerHTML = `<div class="alert alert-danger">Error loading training detail: ${error.message}</div>`;
        });

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('trainingModal'));
    modal.show();
}

// Display training detail for user view (without admin actions)
function displayUserTrainingDetail(training) {
    const modalBody = document.getElementById('modalBody');

    modalBody.innerHTML = `
        <div class="training-detail-user">
            <div class="row">
                <div class="col-md-8">
                    <h4 class="text-primary mb-3">
                        <i class="fas fa-${training.type === 'offline' ? 'building' : 'laptop'} me-2"></i>
                        ${training.title}
                    </h4>

                    <div class="detail-grid">
                        <div class="detail-item">
                            <strong><i class="fas fa-calendar me-2"></i>Tanggal:</strong>
                            <span>${training.date}</span>
                            ${training.is_multiday ? `<br><small class="text-info"><i class="fas fa-info-circle"></i> Training Multi-Hari (${training.total_days} hari)</small>` : ''}
                        </div>

                        <div class="detail-item">
                            <strong><i class="fas fa-clock me-2"></i>Waktu:</strong>
                            <span>${training.time || 'Belum ditentukan'}</span>
                        </div>

                        <div class="detail-item">
                            <strong><i class="fas fa-map-marker-alt me-2"></i>Lokasi:</strong>
                            <span>${training.location || 'Belum ditentukan'}</span>
                        </div>

                        <div class="detail-item">
                            <strong><i class="fas fa-user-tie me-2"></i>Trainer:</strong>
                            <span>${training.trainer || 'Belum ditentukan'}</span>
                        </div>

                        <div class="detail-item">
                            <strong><i class="fas fa-info-circle me-2"></i>Status:</strong>
                            <span class="badge bg-${training.status === 'Active' ? 'success' : training.status === 'Completed' ? 'primary' : 'secondary'}">
                                ${training.status}
                            </span>
                        </div>
                    </div>

                    ${training.description ? `
                        <div class="mt-3">
                            <strong><i class="fas fa-file-text me-2"></i>Deskripsi:</strong>
                            <p class="mt-2">${training.description}</p>
                        </div>
                    ` : ''}
                </div>

                <div class="col-md-4">
                    <div class="training-type-badge">
                        <div class="badge bg-${training.type === 'offline' ? 'success' : 'info'} p-3 w-100">
                            <i class="fas fa-${training.type === 'offline' ? 'building' : 'laptop'} fa-2x d-block mb-2"></i>
                            <h6 class="mb-0">Training ${training.type === 'offline' ? 'Offline' : 'Online'}</h6>
                        </div>
                    </div>

                    ${training.participants && training.participants.length > 0 ? `
                        <div class="mt-3">
                            <h6><i class="fas fa-users me-2"></i>Peserta (${training.participants.length})</h6>
                            <div class="participants-list" style="max-height: 200px; overflow-y: auto;">
                                ${training.participants.map(p => `
                                    <div class="participant-item">
                                        <small><strong>${p.name}</strong><br>
                                        ${p.nik} - ${p.dept}</small>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}

// Function to show select training modal
function showSelectTrainingModal(type) {
    document.getElementById('selectModalTitle').textContent = `Pilih Training ${type === 'offline' ? 'Offline' : 'Online'}`;
    document.getElementById('searchTraining').value = '';

    const selectModal = new bootstrap.Modal(document.getElementById('selectTrainingModal'));
    selectModal.show();

    // Load trainings
    loadExistingTrainings(type);

    // Add search functionality
    document.getElementById('searchTraining').addEventListener('input', function() {
        loadExistingTrainings(type, this.value);
    });
}

function loadExistingTrainings(type, search = '') {
    const tbody = document.getElementById('trainingListBody');
    tbody.innerHTML = '<tr><td colspan="5" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>';

    let url = `api/get_existing_trainings.php?type=${type}`;
    if (search) {
        url += `&search=${encodeURIComponent(search)}`;
    }

    console.log('Loading trainings from:', url);

    fetch(url)
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return response.text();
        })
        .then(text => {
            console.log('Raw response:', text);

            try {
                const data = JSON.parse(text);
                console.log('Parsed data:', data);

                if (data.success) {
                    displayTrainingList(data.trainings, type);
                } else {
                    console.error('API error:', data.message);
                    tbody.innerHTML = `<tr><td colspan="5" class="text-center text-danger">Error: ${data.message}</td></tr>`;
                }
            } catch (jsonError) {
                console.error('JSON parse error:', jsonError);
                console.error('Response text:', text);
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">Invalid response format</td></tr>';
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            tbody.innerHTML = `<tr><td colspan="5" class="text-center text-danger">Network error: ${error.message}</td></tr>`;
        });
}

// Helper function to get status color
function getStatusColor(status) {
    switch (status?.toLowerCase()) {
        case 'active':
        case 'approved':
            return 'success';
        case 'completed':
            return 'primary';
        case 'pending':
            return 'warning';
        case 'rejected':
        case 'cancelled':
            return 'danger';
        default:
            return 'secondary';
    }
}

function displayTrainingList(trainings, type) {
    const tbody = document.getElementById('trainingListBody');

    if (trainings.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">Tidak ada training ditemukan</td></tr>';
        return;
    }

    let html = '';
    trainings.forEach(training => {
        const statusColor = getStatusColor(training.status);
        const formattedDate = training.date ? new Date(training.date).toLocaleDateString('id-ID') : 'Belum ditentukan';

        html += `
            <tr>
                <td>
                    <button class="btn btn-primary btn-sm" onclick="selectTrainingForCalendar('${training.id}', '${type}', '${training.title}')">
                        <i class="fas fa-plus"></i> Pilih
                    </button>
                </td>
                <td>
                    <strong>${training.title}</strong>
                    ${training.description ? '<br><small class="text-muted">' + training.description.substring(0, 100) + (training.description.length > 100 ? '...' : '') + '</small>' : ''}
                </td>
                <td>
                    ${formattedDate}
                    ${training.time ? '<br><small class="text-muted">' + training.time + '</small>' : ''}
                </td>
                <td>${training.location || 'Belum ditentukan'}</td>
                <td>
                    <span class="badge bg-${statusColor}">${training.status}</span>
                </td>
            </tr>
        `;
    });

    tbody.innerHTML = html;
}

function selectTrainingForCalendar(trainingId, type, title) {
    if (confirm(`Tampilkan "${title}" di kalender?`)) {
        // Close select modal
        const selectModal = bootstrap.Modal.getInstance(document.getElementById('selectTrainingModal'));
        selectModal.hide();

        // Show success message
        showNotification(`Training "${title}" akan ditampilkan di kalender!`, 'success');

        // Reload calendar to show the event
        loadTrainingEvents();
    }
}

// Helper function to show notifications
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Function to toggle training visibility (hide/unhide)
function toggleTrainingVisibility(trainingId, trainingType, isCurrentlyHidden) {
    const action = isCurrentlyHidden ? 'unhide' : 'hide';
    const actionText = isCurrentlyHidden ? 'menampilkan' : 'menyembunyikan';

    if (confirm(`Apakah Anda yakin ingin ${actionText} training ini dari kalender?`)) {
        fetch('api/toggle_training_visibility.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                training_id: trainingId,
                training_type: trainingType,
                action: action
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const successMessage = isCurrentlyHidden ?
                    `Training "${data.training_title}" sekarang ditampilkan di kalender` :
                    `Training "${data.training_title}" disembunyikan dari kalender`;

                showNotification(successMessage, 'success');

                // Update button immediately to show new state
                const toggleButton = document.querySelector(`button[onclick*="toggleTrainingVisibility('${trainingId}', '${trainingType}'"]`);
                if (toggleButton) {
                    const newIsHidden = data.is_hidden;
                    const newButtonClass = newIsHidden ? 'btn-success' : 'btn-warning';
                    const newIcon = newIsHidden ? 'eye' : 'eye-slash';
                    const newText = newIsHidden ? 'Tampilkan' : 'Sembunyikan';

                    // Update button appearance
                    toggleButton.className = `btn ${newButtonClass} btn-sm`;
                    toggleButton.innerHTML = `<i class="fas fa-${newIcon}"></i> ${newText}`;
                    toggleButton.onclick = () => toggleTrainingVisibility(trainingId, trainingType, newIsHidden);

                    // Show updated state message
                    const statusDiv = document.createElement('div');
                    statusDiv.className = 'alert alert-info mt-3';
                    statusDiv.innerHTML = `
                        <i class="fas fa-info-circle"></i>
                        <strong>Status Updated!</strong><br>
                        Training sekarang: <strong>${newIsHidden ? 'DISEMBUNYIKAN 🔒' : 'DITAMPILKAN 👁️'}</strong><br>
                        <small>Kalender akan diperbarui dalam 3 detik...</small>
                    `;

                    // Insert status message before actions
                    const actionsDiv = toggleButton.closest('.col-md-6');
                    if (actionsDiv) {
                        actionsDiv.insertBefore(statusDiv, actionsDiv.firstChild);
                    }
                }

                // Close modal and reload with longer delay to show the change
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('trainingModal'));
                    if (modal) {
                        modal.hide();
                    }

                    // Reload calendar to reflect changes
                    setTimeout(() => {
                        location.reload();
                    }, 500);
                }, 3000); // 3 seconds delay to show the updated state
            } else {
                showNotification('Error: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error toggling training visibility:', error);
            showNotification('Error toggling training visibility', 'danger');
        });
    }
}

// Global function to refresh calendar display
window.refreshCalendarDisplay = function() {
    if (typeof trainingEvents !== 'undefined' && trainingEvents.length > 0) {
        // Remove existing events
        document.querySelectorAll('.calendar-event').forEach(event => event.remove());

        // Check if hidden events should be shown
        const showHiddenEvents = document.getElementById('showHiddenEvents').checked;

        trainingEvents.forEach(event => {
            // Skip hidden events if toggle is off
            if (event.is_hidden && !showHiddenEvents) {
                return;
            }

            const dayCell = document.querySelector(`[data-date="${event.date}"]`);
            if (dayCell) {
                const eventElement = document.createElement('div');
                eventElement.className = `calendar-event ${event.type}`;

                // Add hidden class if event is hidden
                if (event.is_hidden) {
                    eventElement.classList.add('event-hidden');
                }

                eventElement.textContent = event.title;
                eventElement.dataset.eventId = event.id;
                eventElement.dataset.eventType = event.type;
                eventElement.dataset.isHidden = event.is_hidden;

                let tooltipText = `${event.title}\n${event.time || 'Waktu belum ditentukan'}\n${event.location || 'Lokasi belum ditentukan'}`;
                if (event.is_hidden) {
                    tooltipText += '\n🔒 Training ini disembunyikan dari tampilan publik';
                }
                eventElement.title = tooltipText;

                eventElement.addEventListener('click', (e) => {
                    e.stopPropagation();
                    showTrainingDetail(event.id, event.type);
                });

                const addBtn = dayCell.querySelector('.add-event-btn');
                dayCell.insertBefore(eventElement, addBtn);
            }
        });
    }
};

// Add event listener for show/hide hidden events toggle
document.addEventListener('DOMContentLoaded', function() {
    const showHiddenToggle = document.getElementById('showHiddenEvents');
    if (showHiddenToggle) {
        showHiddenToggle.addEventListener('change', function() {
            // Re-display events with new filter
            refreshCalendarDisplay();

            // Update toggle label
            const label = this.nextElementSibling;
            if (this.checked) {
                label.innerHTML = '<i class="fas fa-eye-slash"></i> Tampilkan training yang disembunyikan';
            } else {
                label.innerHTML = '<i class="fas fa-eye"></i> Sembunyikan training yang di-hide';
            }
        });
    }

    // Fix accessibility issues with modals
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('shown.bs.modal', function() {
            // Remove aria-hidden when modal is shown
            this.removeAttribute('aria-hidden');
        });

        modal.addEventListener('hidden.bs.modal', function() {
            // Add aria-hidden when modal is hidden
            this.setAttribute('aria-hidden', 'true');
        });
    });

    // Initialize calendar
    initializeCalendar();
});