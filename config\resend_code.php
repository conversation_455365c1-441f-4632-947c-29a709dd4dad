<?php
session_start();
include 'config.php';
include 'mail.php';

// Cek apakah user memiliki session yang valid
if (!isset($_SESSION['verification_email']) || !isset($_SESSION['temp_user_id'])) {
    header('Location: ../view/Aktivasi.php');
    exit();
}

$email = $_SESSION['verification_email'];
$user_id = $_SESSION['temp_user_id'];

// Generate kode verifikasi baru
$verification_code = sprintf("%06d", mt_rand(0, 999999));
$expires = date('Y-m-d H:i:s', strtotime('+15 minutes'));

// Update kode verifikasi di database
$update_query = "UPDATE users SET 
                verification_code = ?,
                verification_expires = ?
                WHERE id = ? AND email = ?";
                
$update_stmt = $conn->prepare($update_query);
$update_stmt->bind_param("ssis", $verification_code, $expires, $user_id, $email);

if ($update_stmt->execute()) {
    // Ambil konfigurasi email dari database
    $settingsQuery = "SELECT smtp_server, smtp_port, smtp_password, sender_email, sender_name, smtp_encryption FROM settings WHERE id = 1";
    $settingsResult = $conn->query($settingsQuery);
    $settings = $settingsResult->fetch_assoc();
    
    // Kirim email verifikasi
    $mail_result = send_mail(
        $email,
        'Kode Verifikasi Baru Akun Anda - Training Center PAS',
        "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 2px solid #a50000; border-radius: 5px; background-color: #f9f9f9;'>
            <h2 style='color: #a50000; text-align: center;'>Kode Verifikasi Baru</h2>
            <p style='color: #333; font-size: 16px;'>Berikut adalah kode verifikasi baru untuk aktivasi akun Anda:</p>
            <div style='background-color: #ffffff; border: 2px solid #a50000; border-radius: 5px; padding: 15px; margin: 20px 0; text-align: center;'>
                <h1 style='font-size: 36px; letter-spacing: 5px; color: #a50000; margin: 0; font-weight: bold;'>$verification_code</h1>
            </div>
            <p style='color: #333; font-size: 14px;'>Kode ini akan kadaluarsa dalam <strong>15 menit</strong>.</p>
            <p style='color: #333; font-size: 14px;'>Jika Anda tidak meminta kode verifikasi baru, abaikan email ini.</p>
            <div style='text-align: center; margin-top: 30px;'>
                <p style='color: #333; font-size: 12px;'>Email ini dikirim secara otomatis. Mohon jangan membalas email ini.</p>
            </div>
        </div>
        ",
        $settings
    );

    if ($mail_result['success']) {
        $_SESSION['message'] = '<div class="success-message">Kode verifikasi baru telah dikirim ke email Anda.</div>';
    } else {
        $_SESSION['message'] = '<div class="error-message">Gagal mengirim kode verifikasi: ' . $mail_result['message'] . '</div>';
    }
} else {
    $_SESSION['message'] = '<div class="error-message">Gagal mengupdate kode verifikasi!</div>';
}

// Redirect kembali ke halaman verifikasi
header('Location: verify_activation.php');
exit();
?>