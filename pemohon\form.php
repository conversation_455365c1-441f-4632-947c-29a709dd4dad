<?php
include 'security.php';
include '../config/config.php';
include '../config/access_control.php';

// Tambahkan ini di bagian atas file, setelah session_start()
error_log("Session data: " . print_r($_SESSION, true));
error_log("POST data: " . print_r($_POST, true));

// ===== ACCESS CONTROL: Check Level Karyawan =====
// Cek apakah user memiliki level 4 ke atas atau jabatan Supervisor/Chief
$user_id = $_SESSION['user_id'];
$user_data = checkUserLevel($user_id, 4, ['Supervisor', 'Chief'], '../index.php');
// ===== END ACCESS CONTROL =====

// Set session variables
$_SESSION['full_name'] = $user_data['name'];
$_SESSION['user_nik'] = $user_data['nik'];
$_SESSION['user_email'] = $user_data['email'];
$user_dept = $user_data['dept'];
$user_bagian = $user_data['bagian'];
$user_jabatan = $user_data['jabatan'];

// Query untuk mengambil Dept Head
$dept_head_query = "
    SELECT DISTINCT u.name
    FROM users u
    INNER JOIN user_departments ud ON u.id = ud.user_id
    WHERE ud.dept = ?
    AND u.role_id = 2";

$stmt = $conn->prepare($dept_head_query);
$stmt->bind_param("s", $user_dept);
$stmt->execute();
$result = $stmt->get_result();

// Ambil semua dept head yang bertanggung jawab atas departemen ini
$dept_heads = [];
while ($row = $result->fetch_assoc()) {
    $dept_heads[] = $row['name'];
}
$user_approver = !empty($dept_heads) ? implode(", ", $dept_heads) : null;

// Tambahkan debugging
if (!$user_approver) {
    error_log("Debug - User Dept: " . $user_dept);
    error_log("Debug - Query: " . $dept_head_query);

    // Query untuk cek dept head yang tersedia
    $check_query = "
        SELECT u.name, ud.dept
        FROM users u
        INNER JOIN user_departments ud ON u.id = ud.user_id
        WHERE u.role_id = 2";
    $check_result = $conn->query($check_query);
    while ($row = $check_result->fetch_assoc()) {
        error_log("Available Dept Head: " . $row['name'] . " - Dept: " . $row['dept']);
    }
}

// Generate token unik untuk form submission dan mencegah duplikasi
if (!isset($_SESSION['token'])) {
    $_SESSION['token'] = bin2hex(random_bytes(32));
}

// Generate token unik untuk mencegah duplikasi submit
$form_token = md5(uniqid(mt_rand(), true));
$_SESSION['form_token'] = $form_token;
$_SESSION['form_token_time'] = time();
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<link rel="stylesheet" href="../assets/css/form-style.css">

<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Memproses pengajuan training...</div>
    </div>
    <?php include '../config/navbara.php'; ?>
    <div class="jarak-form"></div>

    <div class="container-form">
        <!-- Notifications will be handled by JavaScript -->
        <?php if (isset($_SESSION['errors'])): ?>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    <?php foreach ($_SESSION['errors'] as $error): ?>
                        CustomModal.alert("<?php echo addslashes(htmlspecialchars($error)); ?>", "Error");
                    <?php endforeach; ?>
                });
            </script>
            <?php unset($_SESSION['errors']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['success'])): ?>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    CustomModal.success("<?php echo addslashes(htmlspecialchars($_SESSION['success'])); ?>", "Sukses");
                });
            </script>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>
        </div>

        <div class="form-container">
            <div class="form-header">
                <h1>Form Pengajuan Training</h1>
                <p>Silakan lengkapi informasi di bawah ini untuk mengajukan training</p>
            </div>

            <form action="submit.php" method="POST" id="trainingForm">
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['token']); ?>">
                <!-- Hidden field untuk token anti-duplikasi -->
                <input type="hidden" name="form_token" value="<?php echo htmlspecialchars($form_token); ?>">

                <!-- Informasi Pemohon -->
                <div class="form-section">
                    <h2>Informasi Pemohon</h2>
                    <div class="form-group">
                        <label class="required" for="full_name">Nama Pemohon</label>
                        <input type="text" id="full_name" name="full_name" required readonly
                               value="<?= htmlspecialchars($_SESSION['full_name'] ?? 'Pengguna') ?>">
                    </div>

                    <div class="form-group">
                        <label class="required" for="nik">NIK</label>
                        <input type="text" id="nik" name="nik" required readonly
                               value="<?= htmlspecialchars($_SESSION['user_nik'] ?? '0000000000000000') ?>">
                    </div>

                    <div class="form-group">
                        <label class="required" for="department">Departemen</label>
                        <input type="text" id="department" name="department" required readonly
                               value="<?= htmlspecialchars($user_dept ?? 'Tidak tersedia') ?>">
                    </div>

                    <div class="form-group">
                        <label class="required" for="Bagian">Bagian</label>
                        <input type="text" id="Bagian" name="Bagian" required readonly
                               value="<?= htmlspecialchars($user_bagian ?? 'Tidak tersedia') ?>">
                    </div>

                    <div class="form-group">
                        <label class="required" for="Jabatan">Jabatan</label>
                        <input type="text" id="Jabatan" name="Jabatan" required readonly
                               value="<?= htmlspecialchars($user_jabatan ?? 'Tidak tersedia') ?>">
                    </div>

                    <div class="form-group">
                        <label class="required" for="approver_name">Dept Head</label>
                        <?php if ($user_approver): ?>
                            <input type="text" id="approver_name" name="approver_name" required readonly
                                   value="<?= htmlspecialchars($user_approver) ?>">
                        <?php else: ?>
                            <input type="text" id="approver_name" name="approver_name" required readonly
                                   value="Tidak ada Dept Head tersedia">
                            <p class="error-text">Tidak ada Dept Head di departemen <?= htmlspecialchars($user_dept) ?>. Hubungi administrator.</p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Informasi Kontak -->
                <div class="form-section">
                    <h2>Informasi Kontak</h2>
                    <div class="form-group">
                        <label class="required" for="email">Email</label>
                        <input type="email" id="email" name="email" required
                               value="<?= htmlspecialchars($_SESSION['user_email'] ?? '') ?>"
                               placeholder="contoh <EMAIL>">
                        <small class="form-text text-muted">Email dapat diubah jika diperlukan</small>
                    </div>

                    <div class="form-group">
                        <label class="required" for="phone">Nomor Telepon</label>
                        <input type="tel" id="phone" name="phone" required
                               pattern="\d{10,15}"
                               title="Nomor telepon harus berisi 10-15 digit angka"
                               placeholder="Contoh: 081234567890">
                        <small class="form-text text-muted">Masukkan nomor telepon aktif yang dapat dihubungi (10-15 digit)</small>
                    </div>
                </div>

                <!-- Informasi Training -->
                <div class="form-section">
                    <h2>Informasi Training</h2>
                    <div class="form-group">
                        <label class="required" for="training_topic">Nama Training</label>
                        <input type="text" id="training_topic" name="training_topic" required
                               placeholder="Masukkan nama training yang diinginkan">
                        <small class="form-text text-muted">Masukkan nama/topik training yang diinginkan</small>
                    </div>

                    <div class="form-group">
                        <label class="required" for="start_date">Estimasi Tanggal Training</label>
                        <input type="date" id="start_date" name="start_date" required min="<?= date('Y-m-d', strtotime('+1 month')); ?>">
                        <small class="form-text text-muted">Tanggal training minimal harus 1 bulan ke depan dari hari ini</small>
                    </div>

                    <div class="form-group">
                        <label class="required" for="training_skill_type">Jenis Kompetensi Training</label>
                        <select name="training_skill_type" id="training_skill_type" required>
                            <option value="" disabled selected>Pilih kompetensi</option>
                            <option value="Soft Skill">Soft Skill</option>
                            <option value="Technical Skill">Technical Skill</option>
                            <option value="Compliance">Compliance</option>
                        </select>
                        <small class="form-text text-muted">Pilih jenis kompetensi training yang sesuai</small>
                    </div>

                    <div class="form-group">
                        <label class="required" for="additional_info">Urgensi Training</label>
                        <textarea id="additional_info" name="additional_info" rows="4" required placeholder="Jelaskan tujuan training, urgensi pelaksanaan, rekomendasi vendor (jika ada), dasar regulasi yang mewajibkan (jika ada), dan informasi penting lainnya terkait kebutuhan training ini"></textarea>
                    <small class="form-text text-muted">Jelaskan tujuan training, urgensi pelaksanaan, rekomendasi vendor (jika ada), dasar regulasi yang mewajibkan (jika ada), dan informasi penting lainnya terkait kebutuhan training ini</small>
                    </div>
                </div>

                <!-- Peserta Training -->
                <div class="form-section">
                    <h2>Peserta Training</h2>
                    <div class="search-section">
                        <input type="text" id="search_employee" placeholder="Cari karyawan...">
                        <div id="employee_results"></div>
                        <small class="form-text text-muted">Daftarkan peserta training yang akan mengikuti training</small>
                    </div>

                    <div class="form-section">
                        <h2 style="color: black;">Tambah Peserta</h2>
                        <div class="participant-controls">
                            <button type="button" class="btn-add-participant" onclick="addParticipantmanual()">
                                <i class="fas fa-user-plus"></i> Tambah Peserta Manual
                            </button>
                        </div>

                        <!-- Tabel untuk tampilan desktop -->
                        <div class="table-responsive">
                            <table id="participantTable">
                                <thead>
                                    <tr>
                                    <th>No</th>
                                        <th>Nama</th>
                                        <th>NIK</th>
                                        <th>Departemen</th>
                                        <th>Bagian</th>
                                        <th>Jabatan</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody id="participant_list">
                                </tbody>
                            </table>
                        </div>

                        <!-- Card untuk tampilan mobile -->
                        <div class="mobile-participant-card" id="mobile_participant_list">
                            <!-- Mobile cards will be added here by JavaScript -->
                        </div>
                    </div>
                </div>

                <?php
                // Generate form token untuk mencegah duplikasi submit
                $_SESSION['form_token'] = bin2hex(random_bytes(32));
                $_SESSION['form_token_time'] = time();
                ?>
                <input type="hidden" name="form_token" value="<?php echo $_SESSION['form_token']; ?>">

                <button type="submit" id="submitButton">Kirim Pengajuan Training</button>
            </form>
        </div>
    </div>
    </div>
</div>

<footer>
    <?php include '../config/footer.php'; ?>
</footer>

    <script>


function addParticipantmanual() {
        // Add to desktop table
        let tbody = document.getElementById("participant_list");
        let index = tbody.getElementsByTagName("tr").length + 1;
        let row = document.createElement("tr");
        row.innerHTML = `
            <td>${index}</td>
            <td><input type="text" name="nama[]" required placeholder="Nama Peserta" class="manual-input"></td>
            <td><input type="text" name="nik[]" required placeholder="NIK Peserta" class="manual-input"></td>
            <td><input type="text" name="departemen[]" required placeholder="Departemen" class="manual-input"></td>
            <td><input type="text" name="bagian[]" required placeholder="Bagian" class="manual-input"></td>
            <td><input type="text" name="jabatan[]" required placeholder="Jabatan" class="manual-input"></td>
            <td><button type="button" class="btn-remove" onclick="removeParticipantmanual(this, ${index})"><i class="fas fa-trash"></i> Hapus</button></td>
        `;
        tbody.appendChild(row);

        // Add to mobile view
        let mobileList = document.getElementById("mobile_participant_list");
        let mobileCard = document.createElement("div");
        mobileCard.className = "mobile-participant-item";
        mobileCard.setAttribute("data-index", index);
        mobileCard.innerHTML = `
            <div class="mobile-participant-header">
                <span>Peserta #${index}</span>
                <button type="button" class="btn-remove" onclick="removeParticipantmanual(this, ${index})"><i class="fas fa-trash"></i></button>
            </div>
            <div class="mobile-participant-body">
                <div class="mobile-participant-row">
                    <div class="mobile-participant-label">Nama:</div>
                    <div class="mobile-participant-value"><input type="text" name="nama[]" required placeholder="Nama Peserta" class="manual-input-mobile"></div>
                </div>
                <div class="mobile-participant-row">
                    <div class="mobile-participant-label">NIK:</div>
                    <div class="mobile-participant-value"><input type="text" name="nik[]" required placeholder="NIK Peserta" class="manual-input-mobile"></div>
                </div>
                <div class="mobile-participant-row">
                    <div class="mobile-participant-label">Departemen:</div>
                    <div class="mobile-participant-value"><input type="text" name="departemen[]" required placeholder="Departemen" class="manual-input-mobile"></div>
                </div>
                <div class="mobile-participant-row">
                    <div class="mobile-participant-label">Bagian:</div>
                    <div class="mobile-participant-value"><input type="text" name="bagian[]" required placeholder="Bagian" class="manual-input-mobile"></div>
                </div>
                <div class="mobile-participant-row">
                    <div class="mobile-participant-label">Jabatan:</div>
                    <div class="mobile-participant-value"><input type="text" name="jabatan[]" required placeholder="Jabatan" class="manual-input-mobile"></div>
                </div>
            </div>
        `;
        mobileList.appendChild(mobileCard);

        // Sync inputs between desktop and mobile views
        syncInputs(index - 1);
    }

    function syncInputs(index) {
        const desktopInputs = document.querySelectorAll(`#participant_list tr:nth-child(${index + 1}) input`);
        const mobileInputs = document.querySelectorAll(`.mobile-participant-item[data-index="${index + 1}"] input`);

        // Sync desktop to mobile
        desktopInputs.forEach((input, i) => {
            input.addEventListener('input', function() {
                if (mobileInputs[i]) mobileInputs[i].value = this.value;
            });
        });

        // Sync mobile to desktop
        mobileInputs.forEach((input, i) => {
            input.addEventListener('input', function() {
                if (desktopInputs[i]) desktopInputs[i].value = this.value;
            });
        });
    }

    function removeParticipantmanual(button, index) {
        // Remove from desktop table
        if (button.closest('tr')) {
            button.closest('tr').remove();
        }

        // Remove from mobile view
        const mobileCard = document.querySelector(`.mobile-participant-item[data-index="${index}"]`);
        if (mobileCard) {
            mobileCard.remove();
        }

        // Update desktop table indices
        let rows = document.getElementById("participant_list").getElementsByTagName("tr");
        for (let i = 0; i < rows.length; i++) {
            rows[i].getElementsByTagName("td")[0].innerText = i + 1;
        }

        // Update mobile card indices
        let mobileCards = document.querySelectorAll(".mobile-participant-item");
        mobileCards.forEach((card, i) => {
            card.setAttribute("data-index", i + 1);
            card.querySelector(".mobile-participant-header span").innerText = `Peserta #${i + 1}`;
        });
    }
        // Inisialisasi array participants jika belum ada
        if (!window.participants) {
            window.participants = [];
        }

        function addParticipant(participant) {
            // Tambah ke array participants
            window.participants.push(participant);

            // Update tabel
            const tbody = document.getElementById('participant_list');
            const rowNum = tbody.children.length + 1;

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${rowNum}</td>
                <td>
                    ${participant.nama}
                    <input type="hidden" name="nama[]" value="${participant.nama}">
                </td>
                <td>
                    ${participant.nik}
                    <input type="hidden" name="nik[]" value="${participant.nik}">
                </td>
                <td>
                    ${participant.departemen}
                    <input type="hidden" name="departemen[]" value="${participant.departemen}">
                </td>
                <td>
                    ${participant.bagian}
                    <input type="hidden" name="bagian[]" value="${participant.bagian}">
                </td>
                <td>
                    ${participant.jabatan}
                    <input type="hidden" name="jabatan[]" value="${participant.jabatan}">
                </td>
                <td>
                    <button type="button" class="btn-remove" onclick="removeParticipant(this)">Hapus</button>
                </td>
            `;

            tbody.appendChild(row);
        }

        function removeParticipant(button, index) {
            let nik;

            // Remove from desktop table
            if (button.closest('tr')) {
                const row = button.closest('tr');
                nik = row.querySelector('input[name="nik[]"]').value;
                row.remove();
            } else if (button.closest('.mobile-participant-item')) {
                const mobileCard = button.closest('.mobile-participant-item');
                nik = mobileCard.getAttribute('data-nik');
                mobileCard.remove();
            }

            // Hapus dari array participants
            if (nik) {
                window.participants = window.participants.filter(p => p.nik !== nik);
            }

            // Remove from mobile view if not already removed
            const mobileCard = document.querySelector(`.mobile-participant-item[data-nik="${nik}"]`);
            if (mobileCard) {
                mobileCard.remove();
            }

            // Remove from desktop view if not already removed
            const desktopRows = document.querySelectorAll('#participant_list tr');
            desktopRows.forEach(row => {
                const rowNik = row.querySelector('input[name="nik[]"]')?.value;
                if (rowNik === nik) {
                    row.remove();
                }
            });

            // Update desktop table indices
            const tbody = document.getElementById('participant_list');
            Array.from(tbody.children).forEach((row, i) => {
                row.cells[0].textContent = i + 1;

                // Update remove button onclick
                const removeBtn = row.querySelector('.btn-remove');
                if (removeBtn) {
                    removeBtn.setAttribute('onclick', `removeParticipant(this, ${i + 1})`);
                }
            });

            // Update mobile card indices
            const mobileCards = document.querySelectorAll('.mobile-participant-item');
            mobileCards.forEach((card, i) => {
                card.setAttribute('data-index', i + 1);
                card.querySelector('.mobile-participant-header span').innerText = `Peserta #${i + 1}`;

                // Update remove button onclick
                const removeBtn = card.querySelector('.btn-remove');
                if (removeBtn) {
                    removeBtn.setAttribute('onclick', `removeParticipant(this, ${i + 1})`);
                }
            });
        }

        // Click outside modal to close
        window.onclick = function(event) {
            const modal = document.getElementById('addParticipantModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Set minimum date untuk input tanggal training (1 bulan dari hari ini)
            const trainingDateInput = document.getElementById('start_date');
            if (trainingDateInput) {
                const today = new Date();
                const minDate = new Date(today.setMonth(today.getMonth() + 1));
                trainingDateInput.min = minDate.toISOString().split('T')[0];

                // Tambahkan validasi saat input berubah
                trainingDateInput.addEventListener('change', function() {
                    const selectedDate = new Date(this.value);
                    if (selectedDate < minDate) {
                        CustomModal.warning('Tanggal training harus minimal 1 bulan dari hari ini');
                        this.value = ''; // Reset nilai input
                    }
                });
            }

            // Validasi form sebelum submit
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const trainingDate = new Date(trainingDateInput.value);
                    const minDate = new Date();
                    minDate.setMonth(minDate.getMonth() + 1);

                    if (trainingDate < minDate) {
                        e.preventDefault();
                        CustomModal.warning('Tanggal training harus minimal 1 bulan dari hari ini');
                        trainingDateInput.focus();
                        return false;
                    }
                });
            }

            let participantCounter = 1;

            // Event listener untuk search employee
            const searchEmployee = document.getElementById('search_employee');
            if (searchEmployee) {
                searchEmployee.addEventListener('input', function(e) {
                    const query = this.value.trim();
                    if (query.length < 3) {
                        document.getElementById('employee_results').innerHTML = '';
                        return;
                    }

                    fetch(`search_employee.php?query=${encodeURIComponent(query)}`)
                        .then(response => response.json())
                        .then(data => {
                            const resultsDiv = document.getElementById('employee_results');
                            resultsDiv.innerHTML = '';
                            data.forEach(employee => {
                                const div = document.createElement('div');
                                div.className = 'employee-result';
                                div.innerHTML = `${employee.nama} (${employee.nik}) - ${employee.dept}`;
                                div.onclick = () => addParticipant({
                                    nama: employee.nama,
                                    nik: employee.nik,
                                    departemen: employee.dept,
                                    bagian: employee.bagian,
                                    jabatan: employee.jabatan
                                });
                                resultsDiv.appendChild(div);
                            });
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            document.getElementById('employee_results').innerHTML =
                                '<div class="error">Terjadi kesalahan saat mencari karyawan</div>';
                        });
                });
            }

            function addParticipant(participant) {
                // Validasi data
                if (!participant.nama || !participant.nik || !participant.departemen ||
                    !participant.bagian || !participant.jabatan) {
                    CustomModal.alert('Data peserta tidak lengkap!', 'Error');
                    return;
                }

                // Cek duplikasi NIK (case insensitive dan hapus karakter non-alphanumeric)
                const normalizedNIK = participant.nik.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();

                // Cek apakah NIK sudah ada dalam daftar (dengan normalisasi)
                const isDuplicate = window.participants.some(p => {
                    const existingNIK = p.nik.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
                    return existingNIK === normalizedNIK;
                });

                if (isDuplicate) {
                    CustomModal.warning('NIK sudah ada dalam daftar peserta!', 'Peringatan');
                    console.log('Duplikat NIK terdeteksi:', participant.nik);
                    return;
                }

                // Tambah ke array participants
                window.participants.push(participant);

                // Update desktop table
                const tbody = document.getElementById('participant_list');
                const rowNum = tbody.children.length + 1;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${rowNum}</td>
                    <td>
                        ${participant.nama}
                        <input type="hidden" name="nama[]" value="${participant.nama}">
                    </td>
                    <td>
                        ${participant.nik}
                        <input type="hidden" name="nik[]" value="${participant.nik}">
                    </td>
                    <td>
                        ${participant.departemen}
                        <input type="hidden" name="departemen[]" value="${participant.departemen}">
                    </td>
                    <td>
                        ${participant.bagian}
                        <input type="hidden" name="bagian[]" value="${participant.bagian}">
                    </td>
                    <td>
                        ${participant.jabatan}
                        <input type="hidden" name="jabatan[]" value="${participant.jabatan}">
                    </td>
                    <td>
                        <button type="button" class="btn-remove" onclick="removeParticipant(this, ${rowNum})"><i class="fas fa-trash"></i> Hapus</button>
                    </td>
                `;

                tbody.appendChild(row);

                // Update mobile view
                const mobileList = document.getElementById('mobile_participant_list');
                const mobileCard = document.createElement('div');
                mobileCard.className = 'mobile-participant-item';
                mobileCard.setAttribute('data-index', rowNum);
                mobileCard.setAttribute('data-nik', participant.nik);
                mobileCard.innerHTML = `
                    <div class="mobile-participant-header">
                        <span>Peserta #${rowNum}</span>
                        <button type="button" class="btn-remove" onclick="removeParticipant(this, ${rowNum})"><i class="fas fa-trash"></i></button>
                    </div>
                    <div class="mobile-participant-body">
                        <div class="mobile-participant-row">
                            <div class="mobile-participant-label">Nama:</div>
                            <div class="mobile-participant-value">${participant.nama}
                                <input type="hidden" name="nama[]" value="${participant.nama}">
                            </div>
                        </div>
                        <div class="mobile-participant-row">
                            <div class="mobile-participant-label">NIK:</div>
                            <div class="mobile-participant-value">${participant.nik}
                                <input type="hidden" name="nik[]" value="${participant.nik}">
                            </div>
                        </div>
                        <div class="mobile-participant-row">
                            <div class="mobile-participant-label">Departemen:</div>
                            <div class="mobile-participant-value">${participant.departemen}
                                <input type="hidden" name="departemen[]" value="${participant.departemen}">
                            </div>
                        </div>
                        <div class="mobile-participant-row">
                            <div class="mobile-participant-label">Bagian:</div>
                            <div class="mobile-participant-value">${participant.bagian}
                                <input type="hidden" name="bagian[]" value="${participant.bagian}">
                            </div>
                        </div>
                        <div class="mobile-participant-row">
                            <div class="mobile-participant-label">Jabatan:</div>
                            <div class="mobile-participant-value">${participant.jabatan}
                                <input type="hidden" name="jabatan[]" value="${participant.jabatan}">
                            </div>
                        </div>
                    </div>
                `;
                mobileList.appendChild(mobileCard);

                // Clear search results
                const searchResults = document.getElementById('employee_results');
                if (searchResults) {
                    searchResults.innerHTML = '';
                }

                // Reset search input
                const searchInput = document.getElementById('search_employee');
                if (searchInput) {
                    searchInput.value = '';
                }

                // Reset modal form jika penambahan melalui modal
                const modal = document.getElementById('addParticipantModal');
                if (modal) {
                    modal.style.display = 'none';
                }
            }

            // Variabel untuk mencegah multiple submit
            let isSubmitting = false;

            // Pastikan form utama mengirim data peserta
            document.getElementById('trainingForm').addEventListener('submit', function(e) {
                // Jika form sedang disubmit, cegah submit lagi
                if (isSubmitting) {
                    e.preventDefault();
                    console.log('Form sudah disubmit, mencegah multiple submit');
                    return false;
                }

                // Periksa apakah ada peserta yang ditambahkan melalui pencarian atau input manual
                const participantInputs = document.querySelectorAll('input[name="nama[]"]');

                if (participantInputs.length === 0) {
                    e.preventDefault();
                    CustomModal.alert('Mohon tambahkan minimal satu peserta training!', 'Error');
                    return false;
                }

                // Set flag bahwa form sedang disubmit
                isSubmitting = true;

                // Nonaktifkan tombol submit
                const submitButton = document.getElementById('submitButton');
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.innerHTML = 'Memproses...';
                    submitButton.style.backgroundColor = '#999';
                    submitButton.style.cursor = 'not-allowed';
                }

                // Tampilkan loading overlay
                const loadingOverlay = document.getElementById('loadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'flex';
                }

                // Lanjutkan submit form
                return true;
            });

            // Reset form jika halaman di-refresh atau di-load ulang
            window.addEventListener('pageshow', function(event) {
                // Jika halaman di-cache, reset form
                if (event.persisted) {
                    isSubmitting = false;
                    const submitButton = document.getElementById('submitButton');
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.innerHTML = 'Kirim Pengajuan Training';
                        submitButton.style.backgroundColor = '';
                        submitButton.style.cursor = '';
                    }

                    const loadingOverlay = document.getElementById('loadingOverlay');
                    if (loadingOverlay) {
                        loadingOverlay.style.display = 'none';
                    }
                }
            });

            // Notifikasi sekarang ditangani oleh custom-modal.js
        });

    </script>
</body>
</html>

<?php $conn->close(); ?>
