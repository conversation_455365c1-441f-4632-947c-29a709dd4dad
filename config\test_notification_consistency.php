<?php
/**
 * Script untuk test konsistensi notifikasi di berbagai halaman
 */

echo "🔔 TESTING NOTIFICATION CONSISTENCY\n";
echo "===================================\n\n";

// Daftar halaman yang perlu dicek
$pages_to_check = [
    'dept_head/index.php' => 'Dept Head Dashboard',
    'dept_head/classroom.php' => 'Dept Head Classroom',
    'pemohon/index.php' => 'Pemohon Dashboard',
    'pemohon/classroom.php' => 'Pemohon Classroom',
    'LnD/index.php' => 'LnD Dashboard',
    'Dir/index.php' => 'Director Dashboard'
];

echo "📄 Checking navbar includes in pages:\n";
echo "-------------------------------------\n";

foreach ($pages_to_check as $file => $description) {
    echo "📝 $description ($file):\n";
    
    if (!file_exists($file)) {
        echo "   ❌ File not found\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Check which navbar is included
    $navbar_patterns = [
        '../config/navbara.php' => 'Config Navbar A',
        '../config/navbar.php' => 'Config Navbar',
        '../config/navbarb.php' => 'Config Navbar B',
        '../includes/navbar.php' => 'Main Navbar',
        '../includes/fallback_navbar.php' => 'Fallback Navbar'
    ];
    
    $found_navbar = false;
    foreach ($navbar_patterns as $pattern => $name) {
        if (strpos($content, $pattern) !== false) {
            echo "   ✅ Uses: $name ($pattern)\n";
            $found_navbar = true;
            break;
        }
    }
    
    if (!$found_navbar) {
        echo "   ⚠️  No recognized navbar found\n";
    }
    
    // Check for CSS conflicts
    $css_files = [
        '../asset/style.css',
        '../asset/custom-modal.css',
        '../asset/responsive.css'
    ];
    
    $css_conflicts = [];
    foreach ($css_files as $css_file) {
        if (strpos($content, $css_file) !== false) {
            $css_conflicts[] = $css_file;
        }
    }
    
    if (!empty($css_conflicts)) {
        echo "   ⚠️  Potential CSS conflicts: " . implode(', ', $css_conflicts) . "\n";
    }
    
    echo "\n";
}

echo "🔍 Checking navbar files for notification integration:\n";
echo "-----------------------------------------------------\n";

$navbar_files = [
    'config/navbara.php' => 'Config Navbar A (used by dept_head)',
    'config/navbar.php' => 'Config Navbar',
    'config/navbarb.php' => 'Config Navbar B',
    'includes/navbar.php' => 'Main Navbar',
    'includes/fallback_navbar.php' => 'Fallback Navbar'
];

foreach ($navbar_files as $file => $description) {
    echo "📄 $description ($file):\n";
    
    if (!file_exists($file)) {
        echo "   ❌ File not found\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Check for notification integration
    $notification_indicators = [
        'notifications_dropdown.php' => 'Notification Dropdown Include',
        'getUnreadNotifications' => 'Notification Function Call',
        'fa-bell' => 'Bell Icon',
        'notification-bell' => 'Notification Bell Class',
        'notification-dropdown' => 'Notification Dropdown Class'
    ];
    
    $has_notifications = false;
    $found_indicators = [];
    
    foreach ($notification_indicators as $indicator => $description) {
        if (strpos($content, $indicator) !== false) {
            $has_notifications = true;
            $found_indicators[] = $description;
        }
    }
    
    if ($has_notifications) {
        echo "   ✅ Has notifications: " . implode(', ', $found_indicators) . "\n";
    } else {
        echo "   ❌ No notifications found\n";
    }
    
    echo "\n";
}

echo "🎨 Checking notification styling consistency:\n";
echo "--------------------------------------------\n";

$notification_file = 'includes/notifications_dropdown.php';
if (file_exists($notification_file)) {
    $content = file_get_contents($notification_file);
    
    // Check for important CSS overrides
    $important_count = substr_count($content, '!important');
    $z_index_count = substr_count($content, 'z-index');
    
    echo "📄 Notification Dropdown File:\n";
    echo "   ✅ File exists: $notification_file\n";
    echo "   🎨 CSS !important declarations: $important_count\n";
    echo "   📐 Z-index declarations: $z_index_count\n";
    
    // Check for specific styling fixes
    $styling_fixes = [
        'z-index: 99999' => 'High Z-Index for Dropdown',
        'position: relative !important' => 'Position Override',
        'display: flex !important' => 'Display Override',
        'nav .notification-container' => 'Navbar Conflict Fix'
    ];
    
    echo "   🔧 Styling fixes applied:\n";
    foreach ($styling_fixes as $fix => $description) {
        if (strpos($content, $fix) !== false) {
            echo "      ✅ $description\n";
        } else {
            echo "      ❌ Missing: $description\n";
        }
    }
} else {
    echo "❌ Notification dropdown file not found: $notification_file\n";
}

echo "\n📊 SUMMARY:\n";
echo "===========\n";

// Count files with notifications
$total_pages = count($pages_to_check);
$total_navbars = count($navbar_files);

echo "📄 Pages checked: $total_pages\n";
echo "🔔 Navbar files checked: $total_navbars\n";

echo "\n🎯 RECOMMENDATIONS:\n";
echo "===================\n";
echo "1. 🌐 Test both pages in browser:\n";
echo "   - http://localhost/training/dept_head/index.php\n";
echo "   - http://localhost/training/dept_head/classroom.php\n";
echo "\n";
echo "2. 🔔 Check notification bell appearance:\n";
echo "   - Bell icon should be visible and clickable\n";
echo "   - Badge should show notification count\n";
echo "   - Dropdown should appear when clicked\n";
echo "\n";
echo "3. 🎨 Verify styling consistency:\n";
echo "   - Bell icon should look the same on both pages\n";
echo "   - Dropdown should have same styling and positioning\n";
echo "   - No CSS conflicts should affect notification appearance\n";
echo "\n";
echo "4. ⚡ Test functionality:\n";
echo "   - Click bell icon to open dropdown\n";
echo "   - Click outside to close dropdown\n";
echo "   - Click notification to mark as read\n";
echo "\n";

echo "🚀 If notifications still look different:\n";
echo "=========================================\n";
echo "1. Clear browser cache and refresh\n";
echo "2. Check browser developer tools for CSS conflicts\n";
echo "3. Verify both pages use the same navbar file\n";
echo "4. Check for JavaScript errors in console\n";

echo "\n✅ NOTIFICATION CONSISTENCY CHECK COMPLETE!\n";
?>
