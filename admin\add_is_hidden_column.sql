-- <PERSON><PERSON> Script to add is_hidden column to FAQ table
-- Run this in phpMyAdmin or MySQL command line

-- Check if the column already exists
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'faq' 
  AND COLUMN_NAME = 'is_hidden';

-- Add is_hidden column if it doesn't exist
ALTER TABLE faq 
ADD COLUMN is_hidden TINYINT(1) DEFAULT 0 
AFTER category;

-- Verify the column was added
DESCRIBE faq;

-- Set all existing FAQs to visible (not hidden)
UPDATE faq SET is_hidden = 0 WHERE is_hidden IS NULL;

-- Show sample data
SELECT id, question, category, is_hidden FROM faq LIMIT 5;
