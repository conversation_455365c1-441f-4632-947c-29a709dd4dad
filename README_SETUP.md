# Database Setup Instructions

This folder contains files to set up the database for the Training System.

## Files

1. `setupdb.sql` - SQL script containing all database creation commands
2. `setupdb.php` - PHP script to execute the SQL commands through a web interface
3. `setupdb.txt` - Log file showing the current database structure (for reference only)
4. `setup_composer.php` - Composer script to install dependencies for the system

## Prerequisites

Before setting up the database, ensure you have the following:

1. A web server (Apache/Nginx)
2. MySQL/MariaDB database server
3. PHP installed on your web server
4. Composer (for dependency management)

## Installation

1. Clone or download this repository to your web server's document root
2. Run the `setup_composer.php` script to install PHP dependencies
3. Set up your MySQL/MariaDB database and update the database credentials in `setupdb.php`
4. Run the `setupdb.php` script in your web browser to create the database and tables
5. Access the system by navigating to: `URL_ADDRESS/training/`

## Setup Methods
There are multiple methods to set up the database for the Training System:
1. Using the PHP Script (Recommended)
2. Using MySQL Command Line
3. Using phpMyAdmin
### Prerequisites

Before setting up the database, ensure you have the following:

1. A web server (Apache/Nginx)
2. MySQL/MariaDB database server
3. PHP installed on your web server
4. Composer (for dependency management)

### Method 1: Using the PHP Script (Recommended)

1. Make sure your web server (Apache/Nginx) and MySQL/MariaDB are running
2. Open your web browser and navigate to: `http://localhost/training/setupdb.php`
3. Click the "Start Setup" button
4. The script will create the database and all necessary tables
5. After successful setup, you can click "Go to Login Page" to access the system

### Method 2: Using MySQL Command Line

1. Open your MySQL command line client
2. Run the following command:
   ```
   mysql -u root -p < setupdb.sql
   ```
3. Enter your MySQL root password when prompted
4. The script will create the database and all necessary tables

### Method 3: Using phpMyAdmin

1. Open phpMyAdmin in your web browser
2. Select the "SQL" tab
3. Click "Browse" and select the `setupdb.sql` file
4. Click "Go" to execute the SQL commands

## Default Admin Account

After setup, you can log in with the following admin account:

- Username: Administrator
- Email: <EMAIL>
- NIK: ADMIN001
- Password: password

## Troubleshooting

If you encounter any issues during setup:

1. Make sure MySQL/MariaDB is running
2. Check that the database credentials in `setupdb.php` match your MySQL setup
3. Ensure your web server has write permissions to create the database
4. If using Method 2 or 3, make sure you have the necessary MySQL privileges

For any other issues, please contact the system administrator.

## Database Structure

The setup creates the following tables:

- `users` - User accounts and authentication
- `roles` - User roles and permissions
- `karyawan` - Employee information
- `karyawan_history` - History of employee data changes
- `training_submissions` - Training requests and approvals
- `participants` - Training participants
- `data_training` - Training data and records
- `settings` - System settings
- `activity_logs` - User activity logs
- `faq` - Frequently asked questions
- `ignored_duplicates` - Ignored duplicate employee records
- `user_departments` - User department assignments
- `training_history` - Training history records