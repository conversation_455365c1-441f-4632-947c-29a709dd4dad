# 🔔 Panduan Notifikasi Internal System

## 📋 Cara Menggunakan dan Melihat Notifikasi Internal

### 🎯 **1. Lokasi Notifikasi dalam UI**

#### 🔔 **Dropdown Notifikasi (Header)**
- **Lokasi:** Icon bell (🔔) di navigation header
- **File:** `includes/notifications_dropdown.php`
- **Tampil di:** Semua halaman sistem
- **Fitur:**
  - Badge merah dengan angka notifikasi belum dibaca
  - Dropdown menampilkan 5 notifikasi terbaru
  - Icon berbeda untuk setiap jenis notifikasi
  - Timestamp notifikasi

#### 📄 **Halaman Semua Notifikasi**
- **URL:** `[role]/all_notifications.php`
- **Contoh:** 
  - `pemohon/all_notifications.php`
  - `dept_head/all_notifications.php`
  - `LnD/all_notifications.php`
- **Fitur:**
  - Daftar lengkap semua notifikasi
  - Filter read/unread
  - Mark as read individual
  - Mark all as read
  - Pagination

---

### 🚀 **2. Cara Melihat Notifikasi Bekerja**

#### **Step-by-Step:**

1. **🌐 Login ke Sistem**
   ```
   Buka browser → Login dengan akun user
   ```

2. **🔔 Cek Icon Bell di Header**
   ```
   Lihat navigation bar → Icon bell di kanan atas
   ```

3. **🔴 Badge Notifikasi**
   ```
   Jika ada badge merah dengan angka = ada notifikasi baru
   Jika tidak ada badge = tidak ada notifikasi baru
   ```

4. **👆 Klik Icon Bell**
   ```
   Dropdown akan muncul menampilkan:
   - 5 notifikasi terbaru
   - Icon sesuai jenis (info/success/warning/error)
   - Judul dan preview message
   - Timestamp
   ```

5. **📄 Lihat Semua Notifikasi**
   ```
   Klik "Lihat Semua Notifikasi" di dropdown
   → Redirect ke halaman all_notifications.php
   ```

6. **✅ Mark as Read**
   ```
   Klik notifikasi → Otomatis mark as read
   Badge count akan berkurang
   ```

---

### 💻 **3. Cara Mengintegrasikan dalam Kode**

#### **📝 Membuat Notifikasi:**

```php
// General notification (tidak terkait class tertentu)
createNotification(
    $user_id,           // ID user yang akan menerima
    null,               // NULL untuk general notification
    'Judul Notifikasi', // Judul
    'Pesan notifikasi', // Isi pesan
    'info'              // Jenis: info/success/warning/error
);

// Class-specific notification
createNotification(
    $user_id,
    $class_id,          // ID class training
    'Judul Notifikasi',
    'Pesan notifikasi',
    'success'
);
```

#### **📖 Membaca Notifikasi:**

```php
// Ambil notifikasi belum dibaca
$notifications = getUnreadNotifications($user_id, 5);

// Hitung jumlah untuk badge
$notification_count = count($notifications);

// Loop untuk menampilkan
foreach ($notifications as $notification) {
    echo $notification['title'];
    echo $notification['message'];
    echo $notification['created_at'];
}
```

#### **✅ Mark as Read:**

```php
// Mark single notification as read
markNotificationAsRead($notification_id, $user_id);

// Mark all notifications as read (custom implementation)
$query = "UPDATE training_notifications SET is_read = 1 WHERE user_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
```

---

### 🔄 **4. Implementasi dalam Workflow Training**

#### **🆕 Saat Training Disubmit:**
```php
// File: submit.php atau submit_training.php
if ($training_submitted) {
    // Notifikasi ke dept head
    createNotification(
        $dept_head_id,
        null,
        'Pengajuan Training Baru',
        "Ada pengajuan training '{$training_topic}' dari {$requester_name} yang memerlukan persetujuan Anda",
        'info'
    );
}
```

#### **✅ Saat Training Disetujui:**
```php
// File: approve_training.php
if ($training_approved) {
    // Notifikasi ke pemohon
    createNotification(
        $requester_id,
        null,
        'Training Disetujui',
        "Pengajuan training '{$training_topic}' Anda telah disetujui",
        'success'
    );
    
    // Notifikasi ke approver berikutnya jika ada
    if ($next_approver_id) {
        createNotification(
            $next_approver_id,
            null,
            'Persetujuan Training Diperlukan',
            "Training '{$training_topic}' memerlukan persetujuan Anda",
            'info'
        );
    }
}
```

#### **❌ Saat Training Ditolak:**
```php
// File: reject_training.php
if ($training_rejected) {
    createNotification(
        $requester_id,
        null,
        'Training Ditolak',
        "Pengajuan training '{$training_topic}' Anda ditolak. Alasan: {$rejection_reason}",
        'error'
    );
}
```

#### **📅 Reminder Training:**
```php
// File: training_reminder.php (cron job)
createNotification(
    $participant_id,
    null,
    'Reminder Training',
    "Training '{$training_topic}' akan dimulai besok pada {$training_date}",
    'warning'
);
```

---

### 🎨 **5. Jenis Notifikasi dan Icon**

| Jenis | Icon | Warna | Penggunaan |
|-------|------|-------|------------|
| `info` | ℹ️ `fa-info-circle` | Biru | Informasi umum, pengajuan baru |
| `success` | ✅ `fa-check-circle` | Hijau | Approval, berhasil |
| `warning` | ⚠️ `fa-exclamation-triangle` | Kuning | Reminder, deadline |
| `error` | ❌ `fa-times-circle` | Merah | Rejection, error |

---

### 🧪 **6. Testing Notifikasi**

#### **Manual Testing:**
```bash
# Jalankan demo notifikasi
php config/demo_internal_notifications.php

# Test sistem lengkap
php config/final_notification_test.php
```

#### **Browser Testing:**
1. Login sebagai user A
2. Buat notifikasi untuk user A (via admin atau script)
3. Refresh halaman user A
4. Cek badge notification di header
5. Klik bell icon → lihat dropdown
6. Klik "Lihat Semua Notifikasi"
7. Mark as read → cek badge berkurang

---

### 📊 **7. Monitoring Notifikasi**

#### **Database Query untuk Statistik:**
```sql
-- Total notifikasi per user
SELECT u.name, COUNT(*) as total_notifications
FROM users u 
JOIN training_notifications tn ON u.id = tn.user_id
GROUP BY u.id, u.name;

-- Notifikasi belum dibaca
SELECT COUNT(*) as unread_count 
FROM training_notifications 
WHERE is_read = 0;

-- Notifikasi per jenis
SELECT type, COUNT(*) as count 
FROM training_notifications 
GROUP BY type;
```

#### **Log Monitoring:**
```php
// Log saat membuat notifikasi
error_log("Notification created: {$title} for user {$user_id}");

// Log saat mark as read
error_log("Notification {$notification_id} marked as read by user {$user_id}");
```

---

### 🔧 **8. Troubleshooting**

#### **Notifikasi Tidak Muncul:**
1. Cek apakah `includes/notifications_dropdown.php` di-include di header
2. Cek session user_id tersedia
3. Cek database connection
4. Cek JavaScript untuk dropdown

#### **Badge Tidak Update:**
1. Refresh halaman
2. Cek cache browser
3. Cek query `getUnreadNotifications()`

#### **Error saat Membuat Notifikasi:**
1. Cek foreign key constraint (class_id)
2. Cek parameter function `createNotification()`
3. Cek database connection

---

### 🚀 **Status Sistem**

✅ **Notifikasi Internal:** AKTIF dan BERFUNGSI  
✅ **UI Integration:** Tersedia di semua role  
✅ **Database:** Configured dan tested  
✅ **Functions:** Working 100%  

**Sistem notifikasi internal siap digunakan untuk memberikan feedback real-time kepada user tentang status training dan aktivitas sistem!**
