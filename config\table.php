<style>
    .no-column {
        width: 30px !important;
        text-align: left !important;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }
    .keputusan table th, .keputusan table td, .keputusan table tr {
        padding: 8px;
        border: 1px solid #ddd;
        padding: 8px;
        vertical-align: top;
        word-break: break-word; /* untuk potong kata jika panjang */
        white-space: pre-wrap;  /* agar newline tetap terlihat jika ada */
    }

    .date-fixed, .date-pending {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        margin-bottom: 4px;
        flex-wrap: wrap;
    }
    .date-fixed {
        font-weight: bold;
        color: #2e7d32;
    }
    .date-fixed i {
        color: #2e7d32;
        font-size: 16px;
    }
    .date-pending {
        font-weight: bold;
        color: #757575;
    }
    .date-pending i {
        color: #f57c00;
        font-size: 16px;
    }
    .date-proposal {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: #757575;
        margin-top: 4px;
        padding-left: 4px;
    }
    .date-proposal i {
        color: #757575;
        font-size: 14px;
    }
    .proposal-text {
        font-style: italic;
    }
    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }
    .status-badge.confirmed {
        background-color: #e8f5e9;
        color: #2e7d32;
    }
    .status-badge.pending {
        background-color: #fff3e0;
        color: #f57c00;
    }
</style>

<table border="1">
    <!-- <tr><th colspan="5" style="text-align: center;">Informasi Training</th></tr>
    <tr><th>ID Training</th><td colspan="4"><?= htmlspecialchars($row['id'] ?? 'N/A') ?></td></tr> -->
    <table border="1">
        <tr><th colspan="5" style="text-align: center;">Informasi Pemohon Training</th></tr>
        <tr>
            <th>Nama</th>
            <th>NIK</th>
            <th>Jabatan</th>
            <th>Bagian</th>
            <th>Departemen</th>
        </tr>
        <tr>
            <td><?= htmlspecialchars($row['full_name'] ?? 'N/A') ?></td>
            <td><?= htmlspecialchars($row['nik'] ?? 'N/A') ?></td>
            <td><?= htmlspecialchars($row['jabatan'] ?? 'N/A') ?></td>
            <td><?= htmlspecialchars($row['bagian'] ?? 'N/A') ?></td>
            <td><?= htmlspecialchars($row['departemen'] ?? 'N/A') ?></td>
        </tr>
        <tr>
            <th colspan="3">Email</th>
            <th colspan="2">Telepon</th>
        </tr>
        <tr>
            <td colspan="3"><?= htmlspecialchars($row['email'] ?? 'N/A') ?></td>
            <td colspan="2"><?= htmlspecialchars($row['phone'] ?? 'N/A') ?></td>
        </tr>
    </table>
    <table border="1">
        <tr><th colspan="5" style="text-align: center;">Informasi Training</th></tr>
        <tr><th>Nama Training</th>
            <td colspan="4"><?= htmlspecialchars($row['training_topic'] ?? 'N/A') ?></td></tr>
   <tr><th>Jenis Skill Training</th><td colspan="4"><?= htmlspecialchars($row['training_skill_type'] ?? 'Tidak ada jenis skill training') ?></td></tr>
        <tr>
            <th>Tanggal Training</th>
            <td colspan="4">
                <?php if (!empty($row['start_date'])): ?>
                    <?php if ($row['is_confirmed'] == 1 && !empty($row['end_date']) && $row['end_date'] !== $row['start_date']): ?>
                        <!-- Multi-day training yang sudah dikonfirmasi -->
                        <span class="date-fixed">
                            <i class="fas fa-calendar-week"></i>
                            <span style="white-space: nowrap; display: inline-block;">
                                <?= htmlspecialchars($row['start_date']) ?> s/d <?= htmlspecialchars($row['end_date']) ?>
                            </span>
                            <span class="status-badge <?= $row['is_confirmed'] ? 'confirmed' : 'pending' ?>" style="margin-left: 5px;">
                                <?= $row['is_confirmed'] ? 'Sudah Ditetapkan' : 'Belum Ditetapkan' ?>
                            </span>
                        </span>
                    <?php else: ?>
                        <!-- Single day training -->
                        <span class="<?= $row['is_confirmed'] ? 'date-fixed' : 'date-pending' ?>">
                            <i class="fas fa-calendar-<?= $row['is_confirmed'] ? 'check' : 'alt' ?>"></i>
                            <?= htmlspecialchars($row['start_date']) ?>
                            <span class="status-badge <?= $row['is_confirmed'] ? 'confirmed' : 'pending' ?>">
                                <?= $row['is_confirmed'] ? 'Sudah Ditetapkan' : 'Belum Ditetapkan' ?>
                            </span>
                        </span>
                    <?php endif; ?>
                <?php else: ?>
                    <span class="date-pending">
                        <i class="fas fa-question-circle"></i>
                        <span class="status-badge pending">Tanggal Belum Ditentukan</span>
                    </span>
                <?php endif; ?>
            </td>
        </tr>
        <tr><th>Urgensi Training</th><td colspan="4"><?= htmlspecialchars($row['additional_info'] ?? 'Tidak ada informasi tambahan') ?></td></tr>
    </table>

    <?php if (isset($row['provider_type']) && $row['provider_type'] === 'Internal'):
        // Handle JSON encoded trainer fields
        // Debug: Log raw values
        error_log("Raw trainer_name_internal: " . ($row['trainer_name_internal'] ?? 'NULL'));
        error_log("Raw trainer_nik_internal: " . ($row['trainer_nik_internal'] ?? 'NULL'));

        // Decode JSON data with proper error handling
        $trainerNames = [];
        $trainerNiks = [];
        $trainerDepartments = [];
        $trainerSubDepartments = [];
        $trainerPositions = [];

        // Check if the values are already arrays (not JSON strings)
        if (is_array($row['trainer_name_internal'] ?? null)) {
            $trainerNames = $row['trainer_name_internal'];
        } else {
            $decoded = json_decode($row['trainer_name_internal'] ?? '[]', true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $trainerNames = $decoded;
            }
        }

        if (is_array($row['trainer_nik_internal'] ?? null)) {
            $trainerNiks = $row['trainer_nik_internal'];
        } else {
            $decoded = json_decode($row['trainer_nik_internal'] ?? '[]', true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $trainerNiks = $decoded;
            }
        }

        if (is_array($row['trainer_department_internal'] ?? null)) {
            $trainerDepartments = $row['trainer_department_internal'];
        } else {
            $decoded = json_decode($row['trainer_department_internal'] ?? '[]', true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $trainerDepartments = $decoded;
            }
        }

        if (is_array($row['trainer_sub_department_internal'] ?? null)) {
            $trainerSubDepartments = $row['trainer_sub_department_internal'];
        } else {
            $decoded = json_decode($row['trainer_sub_department_internal'] ?? '[]', true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $trainerSubDepartments = $decoded;
            }
        }

        if (is_array($row['trainer_position_internal'] ?? null)) {
            $trainerPositions = $row['trainer_position_internal'];
        } else {
            $decoded = json_decode($row['trainer_position_internal'] ?? '[]', true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $trainerPositions = $decoded;
            }
        }

        // Debug: Log decoded values
        error_log("Decoded trainerNames: " . print_r($trainerNames, true));
        error_log("Decoded trainerNiks: " . print_r($trainerNiks, true));

        if (!empty($trainerNames)) {
            echo '<table>';
            echo '<tr><th colspan="6" style="text-align: center;">Trainer</th></tr>';
            echo '<tr>
                    <th class="no-column">No</th>
                    <th>Nama</th>
                    <th>NIK</th>
                    <th>Jabatan</th>
                    <th>Bagian</th>
                    <th>Departemen</th>
                  </tr>';

            for ($i = 0; $i < count($trainerNames); $i++) {
                echo '<tr>';
                echo '<td class="no-column">' . ($i + 1) . '</td>';
                echo '<td>' . htmlspecialchars($trainerNames[$i] ?? '') . '</td>';
                echo '<td>' . htmlspecialchars($trainerNiks[$i] ?? '') . '</td>';
                echo '<td>' . htmlspecialchars($trainerPositions[$i] ?? '') . '</td>';
                echo '<td>' . htmlspecialchars($trainerSubDepartments[$i] ?? '') . '</td>';
                echo '<td>' . htmlspecialchars($trainerDepartments[$i] ?? '') . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        } else {
            echo '<table border="1">';
            echo '<tr><th colspan="5" style="text-align: center;">Trainer</th></tr>';
            echo '<tr><td colspan="5" style="text-align: center;">Tidak ada trainer.</td></tr>';
            echo '</table>';
        }
    endif; ?>

    <?php
    if (isset($row['participant_names']) && $row['participant_names']) {
        $participants = explode(', ', $row['participant_names']);
        $niks = explode(', ', $row['participant_niks']);
        $jabatans = explode(', ', $row['participant_jabatans']);
        $bagians = explode(', ', $row['participant_bagians']);
        $departemens = explode(', ', $row['participant_departemens']);

        echo '<table>';
        echo '<tr><th colspan="9" style="text-align: center;">Peserta</th></tr>';
        echo '<tr>
                <th class="no-column">No</th>
                <th colspan="2">Nama</th>
                <th colspan="2">NIK</th>
                <th colspan="2">Jabatan</th>
                <th>Bagian</th>
                <th>Departemen</th>
              </tr>';

        for ($i = 0; $i < count($participants); $i++) {
            echo '<tr>';
            echo '<td class="no-column">' . ($i + 1) . '</td>';
            echo '<td colspan="2">' . htmlspecialchars($participants[$i]) . '</td>';
            echo '<td colspan="2">' . htmlspecialchars($niks[$i] ?? 'N/A') . '</td>';
            echo '<td colspan="2">' . htmlspecialchars($jabatans[$i] ?? 'N/A') . '</td>';
            echo '<td>' . htmlspecialchars($bagians[$i] ?? 'N/A') . '</td>';
            echo '<td>' . htmlspecialchars($departemens[$i] ?? 'N/A') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<table>';
        echo '<tr><th colspan="6" style="text-align: center;">Peserta</th></tr>';
        echo '<tr><td colspan="6" style="text-align: center;">Tidak ada peserta.</td></tr>';
        echo '</table>';
    }
    ?>
        <div class="keputusan">
    <table border="1">
        <tr><th colspan="5" style="text-align: center;">Status Approval</th></tr>
        <tr><th>Sedang Ditinjau Oleh</th><td colspan="4"><?= htmlspecialchars($row['current_approver'] ?? 'Selesai') ?></td></tr>
        <tr><th>Komentar Dept Head</th><td colspan="4"><?= htmlspecialchars($row['comments_dept_head'] ?? 'Belum ada komentar.') ?></td></tr>
        <tr><th>Penugasan Dept Head</th><td colspan="4"><?= htmlspecialchars($row['assignment'] ?? 'Belum ada penugasan.') ?></td></tr>
        <tr><th>Internal Memo</th>
            <td colspan="4">
                <?php if (!empty($row['internal_memo_image'])): ?>
                    <button type="button" class="btn btn-info view-memo-btn" style="padding: 6px 12px; white-space: nowrap; width: auto; font-size: 14px; font-weight: 500; border-radius: 4px; cursor: pointer;" onclick="showMemoModal(); return false;">
                        <i class="fas fa-image" style="margin-right: 5px;"></i> Lihat Internal Memo
                    </button>
                <?php else: ?>
                    <span class="text-muted">Tidak ada internal memo</span>
                <?php endif; ?>
            </td>
        </tr>
        <tr>
            <th>Komentar Chief Spv LnD</th>
            <td colspan="4"><?= htmlspecialchars($row['comments_hrd'] ?? 'Belum ada komentar.') ?></td>
        </tr>
        <tr><th>Sharing Knowledge?</th><td colspan="4"><?= htmlspecialchars($row['sharing_knowledge'] ?? 'Belum ditentukan.') ?></td></tr>
        <tr><th>Komentar Manager HRGA</th><td colspan="4"><?= htmlspecialchars($row['comments_ga'] ?? 'Belum ada komentar.') ?></td></tr>
        <tr><th>Komentar Factory Manager</th><td colspan="4"><?= htmlspecialchars($row['comments_fm'] ?? 'Belum ada komentar.') ?></td></tr>
        <tr><th>Komentar Direktur</th><td colspan="4"><?= htmlspecialchars($row['comments_dir'] ?? 'Belum ada komentar.') ?></td></tr>
    </table>
        </div>

</table>

<!-- Modal for Internal Memo -->
<?php if (!empty($row['internal_memo_image'])): ?>
<div class="modal fade" id="memoModal" tabindex="-1" role="dialog" aria-labelledby="memoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="memoModalLabel">Internal Memo</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img src="../<?= htmlspecialchars($row['internal_memo_image']) ?>" alt="Internal Memo" style="max-width: 100%; max-height: 80vh;">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                <a href="../config/download_memo.php?file=<?= urlencode($row['internal_memo_image']) ?>&action=download" class="btn btn-primary">Download</a>
            </div>
        </div>
    </div>
</div>

<script>
    // Initialize modal if jQuery and Bootstrap are available
    $(document).ready(function() {
        // Check if Bootstrap modal function exists
        if (typeof $.fn.modal === 'function') {
            // Modal is already initialized by Bootstrap
        } else {
            // Fallback for modal functionality if Bootstrap is not available
            $('.view-memo-btn').on('click', function() {
                $('#memoModal').css('display', 'block');
            });

            $('.close, .btn-secondary').on('click', function() {
                $('#memoModal').css('display', 'none');
            });

            // Close modal when clicking outside of it
            $(window).on('click', function(event) {
                if ($(event.target).is('#memoModal')) {
                    $('#memoModal').css('display', 'none');
                }
            });
        }
    });
</script>

<style>
    /* Fallback modal styles if Bootstrap is not available */
    .modal {
        display: none;
        position: fixed;
        z-index: 1050;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.4);
    }

    .modal-dialog {
        position: relative;
        width: auto;
        margin: 30px auto;
        max-width: 800px;
    }

    .modal-content {
        position: relative;
        background-color: #fff;
        border: 1px solid rgba(0,0,0,.2);
        border-radius: .3rem;
        outline: 0;
        box-shadow: 0 5px 15px rgba(0,0,0,.5);
    }

    .modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
    }

    .modal-title {
        margin: 0;
        line-height: 1.5;
    }

    .close {
        float: right;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: .5;
        background: none;
        border: 0;
        padding: 0;
        cursor: pointer;
    }

    .modal-body {
        position: relative;
        padding: 15px;
    }

    .modal-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 15px;
        border-top: 1px solid #e9ecef;
    }

    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: .25rem;
        transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
        cursor: pointer;
    }

    .btn-secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-primary {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }

    .btn-info {
        color: #fff;
        background-color: #17a2b8;
        border-color: #17a2b8;
    }

    .btn-sm {
        padding: .25rem .5rem;
        font-size: .875rem;
        line-height: 1.5;
        border-radius: .2rem;
    }

    .view-memo-btn {
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .view-memo-btn i {
        font-size: 14px;
    }
</style>
<?php endif; ?>
