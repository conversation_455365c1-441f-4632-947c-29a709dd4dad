<?php
/**
 * Activity Logger Class
 * Mengelola logging aktivitas user untuk audit trail
 */

class ActivityLogger {
    private $conn;
    
    public function __construct($conn) {
        $this->conn = $conn;
    }
    
    /**
     * Log aktivitas user
     */
    public function log($user_id, $action, $category = 'general', $session_id = null) {
        if (!$session_id) {
            $session_id = session_id();
        }
        
        $query = "INSERT INTO activity_logs (user_id, action, category, timestamp, session_id) 
                  VALUES (?, ?, ?, NOW(), ?)";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("isss", $user_id, $action, $category, $session_id);
        
        return $stmt->execute();
    }
    
    /**
     * Log login activity
     */
    public function logLogin($user_id, $ip_address = null) {
        $ip = $ip_address ?: $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $action = "User login from IP: $ip";
        return $this->log($user_id, $action, 'authentication');
    }
    
    /**
     * Log logout activity
     */
    public function logLogout($user_id) {
        $action = "User logout";
        return $this->log($user_id, $action, 'authentication');
    }
    
    /**
     * Log training submission
     */
    public function logTrainingSubmission($user_id, $training_topic) {
        $action = "Submitted training: " . $training_topic;
        return $this->log($user_id, $action, 'training');
    }
    
    /**
     * Log training approval
     */
    public function logTrainingApproval($user_id, $training_id, $status, $role) {
        $action = "Training ID $training_id $status by $role";
        return $this->log($user_id, $action, 'training_approval');
    }
    
    /**
     * Log training cancellation
     */
    public function logTrainingCancellation($user_id, $training_id) {
        $action = "Cancelled training ID: $training_id";
        return $this->log($user_id, $action, 'training');
    }
    
    /**
     * Log FAQ submission
     */
    public function logFAQSubmission($user_id, $question) {
        $action = "Submitted FAQ question: " . substr($question, 0, 100) . "...";
        return $this->log($user_id, $action, 'faq');
    }
    
    /**
     * Log file access
     */
    public function logFileAccess($user_id, $file_path, $action = 'view') {
        $action_text = ucfirst($action) . " file: " . basename($file_path);
        return $this->log($user_id, $action_text, 'file_access');
    }
    
    /**
     * Log data modification
     */
    public function logDataModification($user_id, $table, $record_id, $action) {
        $action_text = ucfirst($action) . " record ID $record_id in table $table";
        return $this->log($user_id, $action_text, 'data_modification');
    }
    
    /**
     * Get user activity logs
     */
    public function getUserLogs($user_id, $limit = 50, $category = null) {
        $query = "SELECT al.*, u.name as user_name 
                  FROM activity_logs al 
                  LEFT JOIN users u ON al.user_id = u.id 
                  WHERE al.user_id = ?";
        
        $params = [$user_id];
        $types = "i";
        
        if ($category) {
            $query .= " AND al.category = ?";
            $params[] = $category;
            $types .= "s";
        }
        
        $query .= " ORDER BY al.timestamp DESC LIMIT ?";
        $params[] = $limit;
        $types .= "i";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        
        return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    }
    
    /**
     * Get all activity logs (admin only)
     */
    public function getAllLogs($limit = 100, $category = null, $date_from = null, $date_to = null) {
        $query = "SELECT al.*, u.name as user_name 
                  FROM activity_logs al 
                  LEFT JOIN users u ON al.user_id = u.id 
                  WHERE 1=1";
        
        $params = [];
        $types = "";
        
        if ($category) {
            $query .= " AND al.category = ?";
            $params[] = $category;
            $types .= "s";
        }
        
        if ($date_from) {
            $query .= " AND DATE(al.timestamp) >= ?";
            $params[] = $date_from;
            $types .= "s";
        }
        
        if ($date_to) {
            $query .= " AND DATE(al.timestamp) <= ?";
            $params[] = $date_to;
            $types .= "s";
        }
        
        $query .= " ORDER BY al.timestamp DESC LIMIT ?";
        $params[] = $limit;
        $types .= "i";
        
        $stmt = $this->conn->prepare($query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        
        return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    }
    
    /**
     * Get activity statistics
     */
    public function getActivityStats($days = 30) {
        $query = "SELECT 
                    category,
                    COUNT(*) as count,
                    DATE(timestamp) as date
                  FROM activity_logs 
                  WHERE timestamp >= DATE_SUB(NOW(), INTERVAL ? DAY)
                  GROUP BY category, DATE(timestamp)
                  ORDER BY date DESC, count DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $days);
        $stmt->execute();
        
        return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    }
    
    /**
     * Clean old logs (older than specified days)
     */
    public function cleanOldLogs($days = 365) {
        $query = "DELETE FROM activity_logs WHERE timestamp < DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $days);
        
        return $stmt->execute();
    }
}
