<?php
session_start();
include '../config/config.php';

// Check authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    die('Unauthorized access');
}

echo "<h2>Debug Training Data</h2>";

// 1. Check database connection
echo "<h3>1. Database Connection</h3>";
if ($conn) {
    echo "✅ Database connected successfully<br>";
} else {
    echo "❌ Database connection failed: " . mysqli_connect_error() . "<br>";
    exit();
}

// 2. Check if training_submissions table exists
echo "<h3>2. Table Structure</h3>";
$tables_query = "SHOW TABLES LIKE 'training_submissions'";
$tables_result = $conn->query($tables_query);
if ($tables_result->num_rows > 0) {
    echo "✅ training_submissions table exists<br>";
    
    // Show table structure
    echo "<h4>Table Columns:</h4>";
    $columns_query = "SHOW COLUMNS FROM training_submissions";
    $columns_result = $conn->query($columns_query);
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($col = $columns_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ training_submissions table does not exist<br>";
    exit();
}

// 3. Check total records
echo "<h3>3. Data Count</h3>";
$count_query = "SELECT COUNT(*) as total FROM training_submissions";
$count_result = $conn->query($count_query);
$total_records = $count_result->fetch_assoc()['total'];
echo "Total records in training_submissions: <strong>$total_records</strong><br>";

if ($total_records == 0) {
    echo "❌ No data found in training_submissions table<br>";
} else {
    echo "✅ Data found in training_submissions table<br>";
}

// 4. Check deleted records
$deleted_query = "SELECT COUNT(*) as deleted FROM training_submissions WHERE deleted_at IS NOT NULL AND deleted_at != ''";
$deleted_result = $conn->query($deleted_query);
$deleted_records = $deleted_result->fetch_assoc()['deleted'];
echo "Deleted records: <strong>$deleted_records</strong><br>";
echo "Active records: <strong>" . ($total_records - $deleted_records) . "</strong><br>";

// 5. Show sample data
if ($total_records > 0) {
    echo "<h3>4. Sample Data (First 5 Records)</h3>";
    $sample_query = "SELECT id, full_name, training_topic, start_date, end_date, is_confirmed, status, submission_date, deleted_at
                     FROM training_submissions
                     ORDER BY id DESC
                     LIMIT 5";
    $sample_result = $conn->query($sample_query);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr>";
    echo "<th>ID</th><th>Name</th><th>Topic</th><th>Start Date</th><th>End Date</th><th>Confirmed</th><th>Status</th><th>Submission</th><th>Deleted</th>";
    echo "</tr>";
    
    while ($row = $sample_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['full_name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['training_topic']) . "</td>";
        echo "<td>" . $row['start_date'] . "</td>";
        echo "<td>" . $row['end_date'] . "</td>";
        echo "<td>" . ($row['is_confirmed'] ? 'YES' : 'NO') . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . $row['submission_date'] . "</td>";
        echo "<td>" . ($row['deleted_at'] ? 'YES' : 'NO') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// 6. Check status distribution
echo "<h3>5. Status Distribution</h3>";
$status_query = "SELECT status, COUNT(*) as count FROM training_submissions WHERE (deleted_at IS NULL OR deleted_at = '') GROUP BY status";
$status_result = $conn->query($status_query);

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Status</th><th>Count</th></tr>";
while ($row = $status_result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['status'] . "</td>";
    echo "<td>" . $row['count'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// 7. Test the exact query from get_filtered_training.php
echo "<h3>6. Test API Query</h3>";
$test_query = "SELECT ts.*, u.name as requester_name
              FROM training_submissions ts
              LEFT JOIN users u ON ts.user_id = u.id
              WHERE ts.deleted_at IS NULL OR ts.deleted_at = ''
              ORDER BY ts.id DESC
              LIMIT 10";

$test_result = $conn->query($test_query);
if ($test_result) {
    echo "✅ API query executed successfully<br>";
    echo "Records returned: <strong>" . $test_result->num_rows . "</strong><br>";
    
    if ($test_result->num_rows > 0) {
        echo "<h4>Sample API Response Data:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Full Name</th><th>Training Topic</th><th>Status</th><th>Requester Name</th></tr>";
        
        $count = 0;
        while ($row = $test_result->fetch_assoc() && $count < 3) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['training_topic']) . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . htmlspecialchars($row['requester_name'] ?? 'N/A') . "</td>";
            echo "</tr>";
            $count++;
        }
        echo "</table>";
    }
} else {
    echo "❌ API query failed: " . $conn->error . "<br>";
}

// 8. Check users table
echo "<h3>7. Users Table Check</h3>";
$users_query = "SELECT COUNT(*) as total FROM users";
$users_result = $conn->query($users_query);
if ($users_result) {
    $users_count = $users_result->fetch_assoc()['total'];
    echo "Total users: <strong>$users_count</strong><br>";
} else {
    echo "❌ Users table query failed: " . $conn->error . "<br>";
}

$conn->close();
?>

<style>
table {
    margin: 10px 0;
    font-size: 12px;
}
th {
    background-color: #f0f0f0;
    padding: 5px;
}
td {
    padding: 5px;
    max-width: 200px;
    word-wrap: break-word;
}
h3 {
    color: #333;
    border-bottom: 2px solid #ccc;
    padding-bottom: 5px;
}
</style>
