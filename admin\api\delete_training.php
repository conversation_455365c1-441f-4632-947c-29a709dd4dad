<?php
/**
 * API untuk menghapus training
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include necessary files
require_once '../../config/config.php';
require_once '../security.php';

// Check if user is admin (same validation as security.php)
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role_id'], [2, 3, 4, 5, 99])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized - Admin access required']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid JSON input');
    }

    $training_id = $input['id'] ?? '';
    $training_type = $input['type'] ?? '';

    if (empty($training_id) || empty($training_type)) {
        throw new Exception('Missing required parameters');
    }

    // Start transaction
    $conn->begin_transaction();

    try {
        if ($training_type === 'offline') {
            // Delete offline training and related data

            // First, delete attendance records
            $delete_attendance = "DELETE FROM training_attendance WHERE training_id = ?";
            $stmt = $conn->prepare($delete_attendance);
            $stmt->bind_param("i", $training_id);
            $stmt->execute();
            $stmt->close();

            // Then delete the training
            $delete_training = "DELETE FROM offline_training WHERE id = ?";
            $stmt = $conn->prepare($delete_training);
            $stmt->bind_param("i", $training_id);

            if ($stmt->execute()) {
                if ($stmt->affected_rows > 0) {
                    $conn->commit();
                    echo json_encode([
                        'success' => true,
                        'message' => 'Training Internal berhasil dihapus'
                    ]);
                } else {
                    throw new Exception('Training Internal tidak ditemukan');
                }
            } else {
                throw new Exception('Gagal menghapus Training Internal');
            }
            $stmt->close();

        } elseif ($training_type === 'online') {
            // Delete online training (training submission) and related data

            // First, delete participants if any
            $delete_participants = "DELETE FROM training_participants WHERE training_id = ?";
            $stmt = $conn->prepare($delete_participants);
            $stmt->bind_param("i", $training_id);
            $stmt->execute();
            $stmt->close();

            // Then delete the training submission
            $delete_training = "DELETE FROM training_submissions WHERE id = ?";
            $stmt = $conn->prepare($delete_training);
            $stmt->bind_param("i", $training_id);

            if ($stmt->execute()) {
                if ($stmt->affected_rows > 0) {
                    $conn->commit();
                    echo json_encode([
                        'success' => true,
                        'message' => 'Training Eksternal berhasil dihapus'
                    ]);
                } else {
                    throw new Exception('Training Eksternal tidak ditemukan');
                }
            } else {
                throw new Exception('Gagal menghapus Training Eksternal');
            }
            $stmt->close();

        } else {
            throw new Exception('Tipe training tidak valid');
        }

    } catch (Exception $e) {
        $conn->rollback();
        throw $e;
    }

} catch (Exception $e) {
    error_log("Error in delete_training.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
