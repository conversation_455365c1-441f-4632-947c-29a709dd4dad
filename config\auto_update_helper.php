<?php
/**
 * Auto Update Training Status Helper
 *
 * Fungsi terpusat untuk mengupdate status training secara otomatis
 * berdasarkan tanggal training yang sudah lewat.
 */

// Prevent multiple inclusion
if (defined('AUTO_UPDATE_HELPER_LOADED')) {
    return;
}
define('AUTO_UPDATE_HELPER_LOADED', true);

/**
 * Update training status otomatis berdasarkan tanggal
 * 
 * @param mysqli $conn Database connection
 * @return array Result dengan informasi update
 */
function autoUpdateTrainingStatus($conn) {
    $today = date('Y-m-d');
    $current_datetime = date('Y-m-d H:i:s');
    $results = [
        'approved_to_completed' => 0,
        'pending_to_canceled' => 0,
        'total_updated' => 0,
        'errors' => [],
        'details' => []
    ];
    
    try {
        // Start transaction untuk konsistensi data
        $conn->begin_transaction();
        
        // 1. Update Approved → Completed (training sudah selesai)
        $query_completed = "UPDATE training_submissions
                           SET status = 'Completed'
                           WHERE status = 'Approved'
                           AND (
                               start_date < ? OR
                               (end_date IS NOT NULL AND end_date < ?)
                           )";
        
        $stmt_completed = $conn->prepare($query_completed);
        if (!$stmt_completed) {
            throw new Exception("Prepare statement error for completed update: " . $conn->error);
        }
        
        $stmt_completed->bind_param("ss", $today, $today);
        
        if ($stmt_completed->execute()) {
            $results['approved_to_completed'] = $stmt_completed->affected_rows;
            $results['details'][] = "Updated {$results['approved_to_completed']} training from Approved to Completed";
        } else {
            throw new Exception("Execute error for completed update: " . $stmt_completed->error);
        }
        $stmt_completed->close();
        
        // 2. Update Pending → Canceled (training expired tanpa approval)
        $query_canceled = "UPDATE training_submissions
                          SET status = 'Canceled',
                              canceled_at = ?,
                              canceled_by = 0,
                              comments = 'Auto-canceled: Training date has passed without approval'
                          WHERE status = 'Pending'
                          AND start_date < ?
                          AND start_date < DATE_SUB(?, INTERVAL 1 DAY)"; // Grace period 1 hari
        
        $stmt_canceled = $conn->prepare($query_canceled);
        if (!$stmt_canceled) {
            throw new Exception("Prepare statement error for canceled update: " . $conn->error);
        }
        
        $stmt_canceled->bind_param("sss", $current_datetime, $today, $today);
        
        if ($stmt_canceled->execute()) {
            $results['pending_to_canceled'] = $stmt_canceled->affected_rows;
            $results['details'][] = "Updated {$results['pending_to_canceled']} training from Pending to Canceled";
        } else {
            throw new Exception("Execute error for canceled update: " . $stmt_canceled->error);
        }
        $stmt_canceled->close();
        
        // 3. Update training internal (offline_training) jika ada
        $query_offline = "UPDATE offline_training
                         SET status = 'Completed'
                         WHERE status = 'Approved'
                         AND (
                             start_date < ? OR
                             (end_date IS NOT NULL AND end_date < ?)
                         )";

        $stmt_offline = $conn->prepare($query_offline);
        if ($stmt_offline) {
            $stmt_offline->bind_param("ss", $today, $today);
            if ($stmt_offline->execute()) {
                $offline_updated = $stmt_offline->affected_rows;
                $results['details'][] = "Updated {$offline_updated} offline training to Completed";
                $results['total_updated'] += $offline_updated;
            }
            $stmt_offline->close();
        }
        
        // Calculate total
        $results['total_updated'] = $results['approved_to_completed'] + $results['pending_to_canceled'];
        
        // Commit transaction
        $conn->commit();
        
        // Log hasil update
        if ($results['total_updated'] > 0) {
            $log_message = "Auto-update training status: " . 
                          "Completed: {$results['approved_to_completed']}, " .
                          "Canceled: {$results['pending_to_canceled']}, " .
                          "Total: {$results['total_updated']}";
            error_log($log_message);
            
            // Simpan ke database log jika tabel ada
            logAutoUpdate($conn, $results);
        }
        
        $results['success'] = true;
        $results['message'] = "Auto-update completed successfully";
        
    } catch (Exception $e) {
        // Rollback jika ada error
        $conn->rollback();
        
        $results['success'] = false;
        $results['message'] = "Auto-update failed: " . $e->getMessage();
        $results['errors'][] = $e->getMessage();
        
        error_log("Auto-update training status error: " . $e->getMessage());
    }
    
    return $results;
}

/**
 * Log auto-update activity ke database
 * 
 * @param mysqli $conn Database connection
 * @param array $results Update results
 */
function logAutoUpdate($conn, $results) {
    try {
        // Cek apakah tabel log ada
        $check_table = "SHOW TABLES LIKE 'auto_update_logs'";
        $table_result = $conn->query($check_table);
        
        if ($table_result->num_rows == 0) {
            // Buat tabel log jika belum ada
            createAutoUpdateLogTable($conn);
        }
        
        $query = "INSERT INTO auto_update_logs 
                  (update_date, approved_to_completed, pending_to_canceled, total_updated, details, created_at) 
                  VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($query);
        if ($stmt) {
            $update_date = date('Y-m-d');
            $details_json = json_encode($results['details']);
            $created_at = date('Y-m-d H:i:s');
            
            $stmt->bind_param("siiiss", 
                $update_date, 
                $results['approved_to_completed'], 
                $results['pending_to_canceled'], 
                $results['total_updated'], 
                $details_json, 
                $created_at
            );
            
            $stmt->execute();
            $stmt->close();
        }
    } catch (Exception $e) {
        error_log("Failed to log auto-update: " . $e->getMessage());
    }
}

/**
 * Buat tabel log untuk auto-update
 * 
 * @param mysqli $conn Database connection
 */
function createAutoUpdateLogTable($conn) {
    $sql = "CREATE TABLE IF NOT EXISTS auto_update_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        update_date DATE NOT NULL,
        approved_to_completed INT DEFAULT 0,
        pending_to_canceled INT DEFAULT 0,
        total_updated INT DEFAULT 0,
        details TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_update_date (update_date)
    )";
    
    if (!$conn->query($sql)) {
        error_log("Failed to create auto_update_logs table: " . $conn->error);
    }
}

/**
 * Cek apakah auto-update sudah dijalankan hari ini
 * 
 * @param mysqli $conn Database connection
 * @return bool True jika sudah dijalankan hari ini
 */
function isAutoUpdateRunToday($conn) {
    try {
        $today = date('Y-m-d');
        
        // Cek dari session terlebih dahulu (untuk web request)
        if (isset($_SESSION['last_auto_update']) && $_SESSION['last_auto_update'] === $today) {
            return true;
        }
        
        // Cek dari database log
        $query = "SELECT id FROM auto_update_logs WHERE update_date = ? LIMIT 1";
        $stmt = $conn->prepare($query);
        if ($stmt) {
            $stmt->bind_param("s", $today);
            $stmt->execute();
            $result = $stmt->get_result();
            $exists = $result->num_rows > 0;
            $stmt->close();
            
            if ($exists && isset($_SESSION)) {
                $_SESSION['last_auto_update'] = $today;
            }
            
            return $exists;
        }
    } catch (Exception $e) {
        error_log("Error checking auto-update status: " . $e->getMessage());
    }
    
    return false;
}

/**
 * Force run auto-update (untuk cron job atau manual trigger)
 * 
 * @param mysqli $conn Database connection
 * @return array Update results
 */
function forceAutoUpdateTrainingStatus($conn) {
    // Reset session flag jika ada
    if (isset($_SESSION['last_auto_update'])) {
        unset($_SESSION['last_auto_update']);
    }
    
    return autoUpdateTrainingStatus($conn);
}

/**
 * Get auto-update statistics
 * 
 * @param mysqli $conn Database connection
 * @param int $days Number of days to look back
 * @return array Statistics
 */
function getAutoUpdateStats($conn, $days = 30) {
    $stats = [
        'total_runs' => 0,
        'total_completed' => 0,
        'total_canceled' => 0,
        'last_run' => null,
        'recent_activity' => []
    ];
    
    try {
        $date_limit = date('Y-m-d', strtotime("-{$days} days"));
        
        $query = "SELECT * FROM auto_update_logs 
                  WHERE update_date >= ? 
                  ORDER BY update_date DESC";
        
        $stmt = $conn->prepare($query);
        if ($stmt) {
            $stmt->bind_param("s", $date_limit);
            $stmt->execute();
            $result = $stmt->get_result();
            
            while ($row = $result->fetch_assoc()) {
                $stats['total_runs']++;
                $stats['total_completed'] += $row['approved_to_completed'];
                $stats['total_canceled'] += $row['pending_to_canceled'];
                
                if (!$stats['last_run']) {
                    $stats['last_run'] = $row['update_date'];
                }
                
                $stats['recent_activity'][] = $row;
            }
            
            $stmt->close();
        }
    } catch (Exception $e) {
        error_log("Error getting auto-update stats: " . $e->getMessage());
    }
    
    return $stats;
}
?>
