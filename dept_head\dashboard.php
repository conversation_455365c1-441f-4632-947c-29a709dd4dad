<?php
include '../config/config.php';
include 'security.php';

$user_id = $_SESSION['user_id'];

// Ambil informasi user saat ini
$user_query = "SELECT jabatan FROM users WHERE id = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("i", $user_id);
$user_stmt->execute();
$user_result = $user_stmt->get_result();
$user_data = $user_result->fetch_assoc();
$current_jabatan = $user_data['jabatan'] ?? '';

// Cek apakah user adalah Manager HRGA atau Factory Manager
$is_hrga_manager = ($current_jabatan == 'Manager HRGA');
$is_factory_manager = ($current_jabatan == 'Factory Manager');

if ($is_factory_manager) {
    // Jika user adalah Factory Manager, tampilkan pengajuan yang dikirim khusus untuk Factory Manager
    // dan juga training yang sudah disetujui oleh Manager HRGA (role id 2)
    $query = "SELECT
        ts.id,
        ts.training_topic,
        ts.start_date,
        ts.end_date,
        ts.is_confirmed,
        ts.status,
        ts.rejected_by,
        ts.approved_dept_head,
        ts.approved_ga,
        ts.approved_fm,
        ts.approved_dir,
        u.name AS requester_name,
        COALESCE(ts.departemen, u.dept) AS departemen,
        COUNT(p.id) AS total_participants
    FROM training_submissions ts
    LEFT JOIN users u ON ts.user_id = u.id
    LEFT JOIN participants p ON ts.id = p.training_id
    WHERE (
        -- Training yang ditujukan langsung ke Factory Manager (next_approver_id = user_id)
        (ts.current_approver_role_id = 2 AND ts.next_approver_id = ?)
        OR
        -- Training yang sudah disetujui Manager HRGA dan menunggu Factory Manager (approved_ga = Approved, approved_fm = Pending/NULL)
        (ts.approved_ga = 'Approved' AND (ts.approved_fm IS NULL OR ts.approved_fm = 'Pending') AND ts.status != 'Rejected')
    )
    AND ts.status != 'Rejected'
    GROUP BY ts.id, u.name, COALESCE(ts.departemen, u.dept)
    ORDER BY ts.id DESC";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
} else if ($is_hrga_manager) {
    // Jika user adalah Manager HRGA, tampilkan pengajuan yang dikirim khusus untuk Manager HRGA
    // dan juga training yang sudah disetujui oleh LnD (role id 3)
    $query = "SELECT
        ts.id,
        ts.training_topic,
        ts.start_date,
        ts.end_date,
        ts.is_confirmed,
        ts.status,
        ts.rejected_by,
        ts.approved_dept_head,
        ts.approved_ga,
        ts.approved_fm,
        ts.approved_dir,
        u.name AS requester_name,
        COALESCE(ts.departemen, u.dept) AS departemen,
        COUNT(p.id) AS total_participants
    FROM training_submissions ts
    LEFT JOIN users u ON ts.user_id = u.id
    LEFT JOIN participants p ON ts.id = p.training_id
    WHERE (
        -- Training yang ditujukan langsung ke Manager HRGA (next_approver_id = user_id)
        (ts.current_approver_role_id = 2 AND ts.next_approver_id = ?)
        OR
        -- Training dari departemen HRGA yang masih pending approval dept head
        (ts.current_approver_role_id = 2 AND COALESCE(ts.departemen, u.dept) = 'HRGA' AND ts.approved_dept_head = 'Pending')
        OR
        -- Training yang sudah disetujui LnD dan menunggu Manager HRGA (approved_hrd = Approved, approved_ga = Pending/NULL)
        (ts.approved_hrd = 'Approved' AND (ts.approved_ga IS NULL OR ts.approved_ga = 'Pending') AND ts.status != 'Rejected')
    )
    AND ts.status != 'Rejected'
    GROUP BY ts.id, u.name, COALESCE(ts.departemen, u.dept)
    ORDER BY ts.id DESC";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
} else {
    // Ambil departemen dept head
    $dept_query = "SELECT dept FROM user_departments WHERE user_id = ?";
    $dept_stmt = $conn->prepare($dept_query);
    $dept_stmt->bind_param("i", $user_id);
    $dept_stmt->execute();
    $dept_result = $dept_stmt->get_result();

    // Buat array departemen yang dikelola oleh dept head ini
    $departments = [];
    while ($dept_row = $dept_result->fetch_assoc()) {
        $departments[] = $dept_row['dept'];
    }

    // Jika tidak ada departemen, tampilkan pesan
    if (empty($departments)) {
        echo "<p>Anda tidak memiliki departemen yang dikelola.</p>";
        exit;
    }

    // Buat string placeholders untuk query IN
    $placeholders = str_repeat('?,', count($departments) - 1) . '?';

    // Query untuk mengambil data training yang perlu di-approve
    $query = "SELECT
        ts.id,
        ts.training_topic,
        ts.start_date,
        ts.end_date,
        ts.is_confirmed,
        ts.status,
        ts.rejected_by,
        ts.approved_dept_head,
        ts.approved_ga,
        ts.approved_fm,
        ts.approved_dir,
        u.name AS requester_name,
        COALESCE(ts.departemen, u.dept) AS departemen,
        COUNT(p.id) AS total_participants
    FROM training_submissions ts
    LEFT JOIN users u ON ts.user_id = u.id
    LEFT JOIN participants p ON ts.id = p.training_id
    WHERE ts.current_approver_role_id = ?
    AND (
        -- Pengajuan yang ditujukan langsung ke dept head ini
        (ts.next_approver_id IS NULL OR ts.next_approver_id = ?)
        OR
        -- Pengajuan dari departemen yang dikelola oleh dept head ini
        (COALESCE(ts.departemen, u.dept) IN ($placeholders) AND ts.approved_dept_head = 'Pending')
    )
    AND ts.status != 'Rejected'
    GROUP BY ts.id, u.name, COALESCE(ts.departemen, u.dept)
    ORDER BY ts.id DESC";

    // Siapkan parameter untuk bind_param
    $types = "ii" . str_repeat('s', count($departments));
    $params = [$types, $_SESSION['role_id'], $user_id];
    foreach ($departments as $dept) {
        $params[] = $dept;
    }

    // Gunakan call_user_func_array untuk bind_param dengan jumlah parameter dinamis
    $stmt = $conn->prepare($query);
    $ref_params = [];
    for ($i = 0; $i < count($params); $i++) {
        $ref_params[$i] = &$params[$i];
    }
    call_user_func_array([$stmt, 'bind_param'], $ref_params);
}
$stmt->execute();
$result = $stmt->get_result();

// Ambil departemen dept head untuk statistik
$dept_query_stats = "SELECT dept FROM user_departments WHERE user_id = ?";
$dept_stmt_stats = $conn->prepare($dept_query_stats);
$dept_stmt_stats->bind_param("i", $user_id);
$dept_stmt_stats->execute();
$dept_result_stats = $dept_stmt_stats->get_result();

// Buat array departemen yang dikelola oleh dept head ini
$departments_stats = [];
while ($dept_row = $dept_result_stats->fetch_assoc()) {
    $departments_stats[] = $dept_row['dept'];
}
$dept_stmt_stats->close();

// Jika tidak ada departemen, tambahkan nilai dummy untuk menghindari error SQL
if (empty($departments_stats)) {
    $departments_stats[] = 'NO_DEPARTMENT';
}

// Buat string placeholders untuk query IN
$placeholders_stats = str_repeat('?,', count($departments_stats) - 1) . '?';

// Query untuk menghitung jumlah training yang disetujui - berbeda untuk Factory Manager
if ($is_factory_manager) {
    // Untuk Factory Manager, hitung berdasarkan approved_fm
    $query_approved = "SELECT COUNT(*) as total_approved
                      FROM training_submissions ts
                      LEFT JOIN users u ON ts.user_id = u.id
                      WHERE ts.approved_fm = 'Approved'
                      AND ts.status != 'Rejected'";
    $stmt_approved = $conn->prepare($query_approved);
    $stmt_approved->execute();
    $result_approved = $stmt_approved->get_result();
    $approved_count = $result_approved->fetch_assoc()['total_approved'];
    $stmt_approved->close();
} else {
    // Untuk Dept Head biasa, hitung berdasarkan approved_dept_head
    $query_approved = "SELECT COUNT(*) as total_approved
                      FROM training_submissions ts
                      LEFT JOIN users u ON ts.user_id = u.id
                      WHERE ts.approved_dept_head = 'Approved'
                      AND ts.status != 'Rejected'
                      AND COALESCE(ts.departemen, u.dept) IN (" . implode(',', array_fill(0, count($departments_stats), '?')) . ")";
    $stmt_approved = $conn->prepare($query_approved);
    $types_approved = str_repeat('s', count($departments_stats));
    $stmt_approved->bind_param($types_approved, ...$departments_stats);
    $stmt_approved->execute();
    $result_approved = $stmt_approved->get_result();
    $approved_count = $result_approved->fetch_assoc()['total_approved'];
    $stmt_approved->close();
}

// Query untuk menghitung jumlah training yang di-reject - berbeda untuk Factory Manager
if ($is_factory_manager) {
    // Untuk Factory Manager, hitung berdasarkan approved_fm = 'Rejected'
    $query_rejected = "SELECT COUNT(*) as total_rejected
                      FROM training_submissions ts
                      LEFT JOIN users u ON ts.user_id = u.id
                      WHERE ts.approved_fm = 'Rejected'
                      AND ts.status = 'Rejected'";
    $stmt_rejected = $conn->prepare($query_rejected);
    $stmt_rejected->execute();
    $result_rejected = $stmt_rejected->get_result();
    $rejected_count = $result_rejected->fetch_assoc()['total_rejected'];
    $stmt_rejected->close();
} else {
    // Untuk Dept Head biasa, hitung berdasarkan rejected_by
    $query_rejected = "SELECT COUNT(*) as total_rejected
                      FROM training_submissions ts
                      LEFT JOIN users u ON ts.user_id = u.id
                      WHERE ts.rejected_by = ?
                      AND ts.status = 'Rejected'
                      AND COALESCE(ts.departemen, u.dept) IN (" . implode(',', array_fill(0, count($departments_stats), '?')) . ")";
    $stmt_rejected = $conn->prepare($query_rejected);
    $types_rejected = "i" . str_repeat('s', count($departments_stats));
    $params_rejected = array_merge([$user_id], $departments_stats);
    $stmt_rejected->bind_param($types_rejected, ...$params_rejected);
    $stmt_rejected->execute();
    $result_rejected = $stmt_rejected->get_result();
    $rejected_count = $result_rejected->fetch_assoc()['total_rejected'];
    $stmt_rejected->close();
}

// Query untuk menghitung jumlah training yang menunggu persetujuan - berbeda untuk Factory Manager
if ($is_factory_manager) {
    // Untuk Factory Manager, hitung training yang menunggu approved_fm
    $query_pending = "SELECT COUNT(*) as total_pending
                     FROM training_submissions ts
                     LEFT JOIN users u ON ts.user_id = u.id
                     WHERE (
                         -- Training yang ditujukan langsung ke Factory Manager
                         (ts.current_approver_role_id = 2 AND ts.next_approver_id = ?)
                         OR
                         -- Training yang sudah disetujui Manager HRGA dan menunggu Factory Manager
                         (ts.approved_ga = 'Approved' AND (ts.approved_fm IS NULL OR ts.approved_fm = 'Pending'))
                     )
                     AND ts.status != 'Rejected'";
    $stmt_pending = $conn->prepare($query_pending);
    $stmt_pending->bind_param("i", $user_id);
    $stmt_pending->execute();
    $result_pending = $stmt_pending->get_result();
    $pending_count = $result_pending->fetch_assoc()['total_pending'];
    $stmt_pending->close();
} else {
    // Untuk Dept Head biasa, hitung berdasarkan current_approver_role_id dan departemen
    $query_pending = "SELECT COUNT(*) as total_pending
                     FROM training_submissions ts
                     LEFT JOIN users u ON ts.user_id = u.id
                     WHERE ts.current_approver_role_id = 2
                     AND (ts.next_approver_id = ? OR (COALESCE(ts.departemen, u.dept) IN (" . implode(',', array_fill(0, count($departments_stats), '?')) . ") AND ts.approved_dept_head = 'Pending'))
                     AND ts.status != 'Rejected'";

    $stmt_pending = $conn->prepare($query_pending);
    $params_pending = [$user_id];
    foreach ($departments_stats as $dept) {
        $params_pending[] = $dept;
    }
    $types_pending = "i" . str_repeat('s', count($departments_stats));
    $stmt_pending->bind_param($types_pending, ...$params_pending);
    $stmt_pending->execute();
    $result_pending = $stmt_pending->get_result();
    $pending_count = $result_pending->fetch_assoc()['total_pending'];
    $stmt_pending->close();
}

// Query untuk menghitung jumlah total training - berbeda untuk Factory Manager
if ($is_factory_manager) {
    // Untuk Factory Manager, hitung semua training yang pernah sampai ke level Factory Manager
    $query_total = "SELECT COUNT(*) as total_submissions
                   FROM training_submissions ts
                   LEFT JOIN users u ON ts.user_id = u.id
                   WHERE (
                       -- Training yang sudah disetujui Manager HRGA (sampai ke Factory Manager)
                       ts.approved_ga = 'Approved'
                       OR
                       -- Training yang sudah di-approve/reject oleh Factory Manager
                       ts.approved_fm IS NOT NULL
                   )
                   AND ts.status != 'Rejected'";
    $stmt_total = $conn->prepare($query_total);
    $stmt_total->execute();
    $result_total = $stmt_total->get_result();
    $total_count = $result_total->fetch_assoc()['total_submissions'];
    $stmt_total->close();
} else {
    // Untuk Dept Head biasa, hitung berdasarkan departemen
    $query_total = "SELECT COUNT(*) as total_submissions
                   FROM training_submissions ts
                   LEFT JOIN users u ON ts.user_id = u.id
                   WHERE COALESCE(ts.departemen, u.dept) IN (" . implode(',', array_fill(0, count($departments_stats), '?')) . ")
                   AND ts.status != 'Rejected'";

    $stmt_total = $conn->prepare($query_total);
    $types_total = str_repeat('s', count($departments_stats));
    $stmt_total->bind_param($types_total, ...$departments_stats);
    $stmt_total->execute();
    $result_total = $stmt_total->get_result();
    $total_count = $result_total->fetch_assoc()['total_submissions'];
    $stmt_total->close();
}

// Mengambil statistik
$stats = [
    'total' => $total_count,
    'pending' => $pending_count,
    'approved' => $approved_count,
    'rejected' => $rejected_count,
    'completed' => 0
];

// Karena kita sudah menghitung jumlah training yang disetujui oleh dept head ini di atas,
// kita bisa langsung menggunakan nilai tersebut
$stats['completed'] = $approved_count;

// Ambil array hasil untuk ditampilkan
$result_array = [];
while ($row = $result->fetch_assoc()) {
    $result_array[] = $row;
}
?>
<style>
    .jarak {
        height: 100px;
    }
</style>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<body>
    <?php include '../config/navbara.php'; ?>
    <div class="jarak"></div>
<div class="container-form">
    <?php include '../config/setujudepthead.php'; ?>
</div>

<?php include '../config/footer.php'; ?>
</body>
</html>

<?php
$stmt->close();
$conn->close();
?>
</html>
