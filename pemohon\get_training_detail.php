<?php
/**
 * API untuk mengambil detail training dan daftar peserta
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Include necessary files
require_once '../config/config.php';
require_once 'security.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    // Get parameters
    $training_id = $_GET['id'] ?? '';
    $training_type = $_GET['type'] ?? '';

    if (empty($training_id) || empty($training_type)) {
        throw new Exception('Missing required parameters');
    }

    // Validate training_id is numeric
    if (!is_numeric($training_id)) {
        throw new Exception('Invalid training ID');
    }

    // Validate training_type
    if (!in_array($training_type, ['online', 'offline'])) {
        throw new Exception('Invalid training type');
    }

    // Check database connection
    if (!$conn) {
        throw new Exception('Database connection failed');
    }

    $training_detail = null;
    $participants = [];

    switch ($training_type) {
        case 'offline':
            // Get offline training detail - ultra simplified
            $query = "SELECT id, training_topic as title, training_description as description,
                            start_date as date, end_date, is_confirmed,
                            training_time_start, training_time_end, location,
                            trainer_name as trainer, max_participants, status
                      FROM offline_training WHERE id = ? LIMIT 1";

            $stmt = $conn->prepare($query);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }

            $stmt->bind_param("i", $training_id);
            if (!$stmt->execute()) {
                throw new Exception("Execute failed: " . $stmt->error);
            }

            $result = $stmt->get_result();
            if (!$result) {
                throw new Exception("Get result failed: " . $conn->error);
            }

            if ($result->num_rows > 0) {
                $training_detail = $result->fetch_assoc();
                $training_detail['raw_date'] = $training_detail['date'];

                // Format time safely
                $time_parts = [];
                if (!empty($training_detail['training_time_start'])) {
                    $time_parts[] = $training_detail['training_time_start'];
                }
                if (!empty($training_detail['training_time_end'])) {
                    $time_parts[] = $training_detail['training_time_end'];
                }
                $training_detail['time'] = empty($time_parts) ? '' : implode(' - ', $time_parts);

                // Set defaults
                $training_detail['status'] = $training_detail['status'] ?: 'Active';
                $training_detail['location'] = $training_detail['location'] ?: 'N/A';
                $training_detail['trainer'] = $training_detail['trainer'] ?: 'N/A';

                // Add a simple participant summary
                $participants[] = [
                    'name' => 'Training Internal',
                    'nik' => 'N/A',
                    'dept' => 'N/A',
                    'jabatan' => 'N/A',
                    'status' => 'summary'
                ];
            } else {
                throw new Exception("Training not found with ID: " . $training_id);
            }
            $stmt->close();
            break;

        case 'online':
            // Get online training (training submission) detail - ultra simplified
            $query = "SELECT id, training_topic as title, additional_info as description,
                            start_date as date, end_date, is_confirmed,
                            training_time_start, training_time_end,
                            training_place as location, contact_person as trainer,
                            status, user_id
                      FROM training_submissions WHERE id = ? LIMIT 1";

            $stmt = $conn->prepare($query);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }

            $stmt->bind_param("i", $training_id);
            if (!$stmt->execute()) {
                throw new Exception("Execute failed: " . $stmt->error);
            }

            $result = $stmt->get_result();
            if (!$result) {
                throw new Exception("Get result failed: " . $conn->error);
            }

            if ($result->num_rows > 0) {
                $training_detail = $result->fetch_assoc();
                $training_detail['raw_date'] = $training_detail['date'];

                // Format time safely
                $time_parts = [];
                if (!empty($training_detail['training_time_start'])) {
                    $time_parts[] = $training_detail['training_time_start'];
                }
                if (!empty($training_detail['training_time_end'])) {
                    $time_parts[] = $training_detail['training_time_end'];
                }
                $training_detail['time'] = empty($time_parts) ? '' : implode(' - ', $time_parts);

                // Set defaults
                $training_detail['location'] = $training_detail['location'] ?: 'Online';
                $training_detail['trainer'] = $training_detail['trainer'] ?: 'N/A';

                // Add a simple participant entry to avoid empty array
                $participants[] = [
                    'name' => 'Pemohon Training',
                    'nik' => 'N/A',
                    'dept' => 'N/A',
                    'jabatan' => 'N/A',
                    'role' => 'Pemohon',
                    'status' => 'active'
                ];
            } else {
                throw new Exception("Training not found with ID: " . $training_id);
            }
            $stmt->close();
            break;

        default:
            throw new Exception('Invalid training type');
    }

    if (!$training_detail) {
        throw new Exception('Training not found');
    }

    // Format date for display
    if ($training_detail['date']) {
        $date = new DateTime($training_detail['date']);
        $training_detail['date'] = $date->format('d M Y');

        // Handle multi-day events
        if (!empty($training_detail['end_date']) && $training_detail['end_date'] !== $training_detail['raw_date']) {
            $endDate = new DateTime($training_detail['end_date']);
            $training_detail['date'] = $date->format('d M Y') . ' - ' . $endDate->format('d M Y');
        }
    }

    // Add participants to training detail
    $training_detail['participants'] = $participants;

    echo json_encode([
        'success' => true,
        'training' => $training_detail
    ]);

} catch (Exception $e) {
    error_log("Error in get_training_detail.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error loading training detail: ' . $e->getMessage()
    ]);
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}
?>
