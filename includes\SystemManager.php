<?php
/**
 * System Manager Class
 * Mengelola integrasi semua sistem (Activity Logs, CSRF, FAQ, Notifications, Role Permissions)
 */

require_once 'ActivityLogger.php';
require_once 'CSRFProtection.php';
require_once 'FAQManager.php';
require_once 'TrainingRoleManager.php';
require_once 'TrainingNotificationManager.php';

class SystemManager {
    private $conn;
    private $activityLogger;
    private $csrfProtection;
    private $faqManager;
    private $roleManager;
    private $notificationManager;

    public function __construct($conn) {
        $this->conn = $conn;
        $this->initializeManagers();
    }

    /**
     * Initialize all managers
     */
    private function initializeManagers() {
        try {
            $this->activityLogger = new ActivityLogger($this->conn);
            $this->csrfProtection = new CSRFProtection($this->conn);
            $this->faqManager = new FAQManager($this->conn, $this->activityLogger);
            $this->roleManager = new TrainingRoleManager($this->conn, $this->activityLogger);
            $this->notificationManager = new TrainingNotificationManager($this->conn, $this->activityLogger);
        } catch (Exception $e) {
            error_log("SystemManager initialization error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get Activity Logger instance
     */
    public function getActivityLogger() {
        return $this->activityLogger;
    }

    /**
     * Get CSRF Protection instance
     */
    public function getCSRFProtection() {
        return $this->csrfProtection;
    }

    /**
     * Get FAQ Manager instance
     */
    public function getFAQManager() {
        return $this->faqManager;
    }

    /**
     * Get Role Manager instance
     */
    public function getRoleManager() {
        return $this->roleManager;
    }

    /**
     * Get Notification Manager instance
     */
    public function getNotificationManager() {
        return $this->notificationManager;
    }

    /**
     * Initialize session with CSRF protection
     */
    public function initializeSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Generate CSRF token if not exists
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = $this->csrfProtection->generateToken();
        }
    }

    /**
     * Log user login with comprehensive tracking
     */
    public function handleUserLogin($user_id, $username) {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

        // Log login activity
        $this->activityLogger->logLogin($user_id, $ip_address);

        // Additional login tracking
        $action = "Login successful - IP: $ip_address, User Agent: " . substr($user_agent, 0, 100);
        $this->activityLogger->log($user_id, $action, 'authentication');

        // Clean old CSRF tokens
        $this->csrfProtection->cleanExpiredTokens();

        return true;
    }

    /**
     * Log user logout
     */
    public function handleUserLogout($user_id) {
        $this->activityLogger->logLogout($user_id);
        $this->csrfProtection->cleanSessionTokens();

        return true;
    }

    /**
     * Handle training submission with full logging and notifications
     */
    public function handleTrainingSubmission($user_id, $training_data) {
        // Log training submission
        $this->activityLogger->logTrainingSubmission($user_id, $training_data['training_topic']);

        // Send notification to relevant parties (dept head, etc.)
        if (isset($training_data['training_id'])) {
            $title = "New Training Submission";
            $message = "Training submission: " . $training_data['training_topic'] . " requires approval.";

            // You can customize this based on your approval workflow
            // $this->notificationManager->sendToMultipleUsers($approver_ids, 0, $title, $message, 'info');
        }

        return true;
    }

    /**
     * Handle training approval with logging and notifications
     */
    public function handleTrainingApproval($approver_id, $training_id, $status, $role) {
        // Log approval activity
        $this->activityLogger->logTrainingApproval($approver_id, $training_id, $status, $role);

        // Send notification to training submitter
        if ($status === 'approved') {
            $title = "Training Approved";
            $message = "Your training submission has been approved by $role.";

            // Get training submitter ID and send notification
            // $this->notificationManager->createNotification($submitter_id, 0, $title, $message, 'success');
        }

        return true;
    }

    /**
     * Handle FAQ submission
     */
    public function handleFAQSubmission($user_id, $question, $category) {
        $submission_id = $this->faqManager->submitQuestion($user_id, $question, $category);

        if ($submission_id) {
            // Send notification to FAQ administrators
            $title = "New FAQ Submission";
            $message = "A new FAQ question has been submitted and requires review.";

            // You can customize this to send to FAQ admins
            // $this->notificationManager->sendToMultipleUsers($admin_ids, 0, $title, $message, 'info');
        }

        return $submission_id;
    }

    /**
     * Validate CSRF token for forms
     */
    public function validateCSRF() {
        return $this->csrfProtection->validateRequest();
    }

    /**
     * Get CSRF token for forms
     */
    public function getCSRFToken() {
        return $this->csrfProtection->getToken();
    }

    /**
     * Get CSRF hidden input for forms
     */
    public function getCSRFInput() {
        return $this->csrfProtection->getHiddenInput();
    }

    /**
     * Check user permission for class action
     */
    public function checkClassPermission($user_id, $class_id, $permission) {
        return $this->roleManager->userHasClassPermission($user_id, $class_id, $permission);
    }

    /**
     * Require permission (middleware)
     */
    public function requirePermission($user_id, $class_id, $permission) {
        $this->roleManager->requirePermission($user_id, $class_id, $permission);
    }

    /**
     * Get user notifications
     */
    public function getUserNotifications($user_id, $limit = 10) {
        return $this->notificationManager->getUserNotifications($user_id, $limit);
    }

    /**
     * Get unread notification count
     */
    public function getUnreadNotificationCount($user_id) {
        return $this->notificationManager->getUnreadCount($user_id);
    }

    /**
     * Mark notification as read
     */
    public function markNotificationAsRead($notification_id, $user_id) {
        return $this->notificationManager->markAsRead($notification_id, $user_id);
    }

    /**
     * Get system statistics
     */
    public function getSystemStats() {
        return [
            'activity_stats' => $this->activityLogger->getActivityStats(),
            'csrf_stats' => $this->csrfProtection->getTokenStats(),
            'faq_stats' => $this->faqManager->getSubmissionStats(),
            'role_stats' => $this->roleManager->getPermissionStats(),
            'notification_stats' => $this->notificationManager->getNotificationStats()
        ];
    }

    /**
     * Perform system maintenance
     */
    public function performMaintenance() {
        $results = [];

        // Clean old activity logs (older than 1 year)
        $results['activity_cleanup'] = $this->activityLogger->cleanOldLogs(365);

        // Clean expired CSRF tokens
        $results['csrf_cleanup'] = $this->csrfProtection->cleanExpiredTokens();

        // Clean old notifications (older than 30 days)
        $results['notification_cleanup'] = $this->notificationManager->deleteOldNotifications(30);

        // Log maintenance activity
        $this->activityLogger->log($_SESSION['user_id'] ?? 0, 'System maintenance performed', 'system');

        return $results;
    }

    /**
     * Log file access
     */
    public function logFileAccess($user_id, $file_path, $action = 'view') {
        return $this->activityLogger->logFileAccess($user_id, $file_path, $action);
    }

    /**
     * Log data modification
     */
    public function logDataModification($user_id, $table, $record_id, $action) {
        return $this->activityLogger->logDataModification($user_id, $table, $record_id, $action);
    }

    /**
     * Send notification to class participants
     */
    public function sendClassNotification($class_id, $title, $message, $type = 'info') {
        return $this->notificationManager->sendToClassParticipants($class_id, $title, $message, $type);
    }
}
