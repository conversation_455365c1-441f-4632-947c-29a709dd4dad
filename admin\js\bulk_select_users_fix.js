// Fix untuk masalah departemenOptions yang dideklarasikan dua kali
// Perik<PERSON> apakah variabel sudah dideklarasikan sebelumnya
if (typeof window.departemenOptions === 'undefined') {
    window.departemenOptions = [];
}
if (typeof window.Bagian === 'undefined') {
    window.Bagian = {};
}
if (typeof window.Jabatan === 'undefined') {
    window.Jabatan = {};
}

// Pastikan window.selectedEmployees selalu diinisialisasi sebagai array
if (typeof window.selectedEmployees === 'undefined' || !Array.isArray(window.selectedEmployees)) {
    window.selectedEmployees = [];
}

// Fix untuk masalah play() request yang terinterupsi
// Fungsi untuk submit form dengan AJAX dan menampilkan progress dengan penanganan promise yang benar
function submitFormWithProgress() {
    const form = document.getElementById('bulkCreateForm');
    const formData = new FormData(form);
    
    // Pastikan selectedEmployees adalah array sebelum memanggil slice()
    const allEmployees = Array.isArray(window.selectedEmployees) ? window.selectedEmployees.slice() : [];
    const totalEmployees = allEmployees.length;
    
    if (totalEmployees === 0) {
        CustomModal.warning('Tidak ada karyawan yang dipilih.', 'Peringatan');
        return;
    }
    
    // Tampilkan overlay loading
    document.getElementById('loadingOverlay').style.display = 'flex';
    
    // Ukuran batch untuk memproses karyawan
    const BATCH_SIZE = 10; // Proses 10 karyawan per batch
    let currentBatch = 0;
    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;
    let retryCount = 0;
    const MAX_RETRIES = 3;
    
    // Fungsi untuk memproses batch karyawan
    function processBatch() {
        // Hitung indeks awal dan akhir untuk batch saat ini
        const startIdx = currentBatch * BATCH_SIZE;
        const endIdx = Math.min(startIdx + BATCH_SIZE, totalEmployees);
        
        // Jika semua karyawan sudah diproses, selesai
        if (startIdx >= totalEmployees) {
            // Proses selesai, redirect ke halaman hasil
            const redirectWithDelay = function() {
                return new Promise((resolve) => {
                    setTimeout(function() {
                        resolve();
                    }, 1000);
                });
            };
            
            redirectWithDelay().then(() => {
                window.location.href = 'bulk_select_users_result.php';
            });
            return;
        }
        
        // Ambil karyawan untuk batch ini
        const batchEmployees = allEmployees.slice(startIdx, endIdx);
        
        // Buat FormData baru untuk batch ini
        const batchFormData = new FormData();
        batchFormData.append('role_id', formData.get('role_id'));
        batchFormData.append('selected_employees', JSON.stringify(batchEmployees));
        
        // Buat objek XMLHttpRequest
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'bulk_select_users_process.php', true);
        
        // Variabel untuk menyimpan respons yang belum diproses
        let buffer = '';
        
        // Tambahkan timeout untuk mengatasi masalah koneksi
        xhr.timeout = 120000; // 2 menit timeout per batch
        
        // Tangani respons streaming dari server
        xhr.onprogress = function() {
            // Ambil respons baru yang ditambahkan
            const newData = xhr.responseText.substring(buffer.length);
            buffer = xhr.responseText;
            
            // Proses setiap baris respons JSON
            const lines = newData.split('\n\n');
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;
                
                try {
                    const response = JSON.parse(line);
                    
                    if (response.status === 'progress') {
                        // Update progress global
                        const current = processedCount + response.current;
                        const percent = Math.round((current / totalEmployees) * 100);
                        
                        // Update progress bar
                        document.getElementById('progressBar').style.width = percent + '%';
                        document.getElementById('progressText').textContent = percent + '%';
                        document.getElementById('progressDetails').textContent = 
                            `Memproses ${current} dari ${totalEmployees} karyawan`;
                        
                        // Update detail progress
                        document.getElementById('progressMessage').textContent = response.message || '';
                    } else if (response.status === 'batch_complete') {
                        // Batch selesai, update counter
                        processedCount += response.processed;
                        successCount += response.success;
                        errorCount += response.error;
                        
                        // Lanjut ke batch berikutnya
                        currentBatch++;
                        processBatch();
                        return;
                    }
                } catch (e) {
                    console.error('Error parsing JSON response:', e, 'Line:', line);
                }
            }
        };
        
        // Tangani error
        xhr.onerror = function() {
            console.error('Network error during batch processing');
            
            // Coba lagi jika belum mencapai batas retry
            if (retryCount < MAX_RETRIES) {
                retryCount++;
                console.log(`Retrying batch ${currentBatch} (attempt ${retryCount})...`);
                setTimeout(processBatch, 2000); // Tunggu 2 detik sebelum retry
            } else {
                // Tampilkan pesan error
                document.getElementById('loadingOverlay').style.display = 'none';
                CustomModal.error(
                    `Terjadi kesalahan jaringan saat memproses batch ${currentBatch + 1}. ` +
                    `Silakan coba lagi nanti.`,
                    'Error Jaringan'
                );
            }
        };
        
        // Tangani timeout
        xhr.ontimeout = function() {
            console.error('Timeout during batch processing');
            
            // Coba lagi jika belum mencapai batas retry
            if (retryCount < MAX_RETRIES) {
                retryCount++;
                console.log(`Retrying batch ${currentBatch} after timeout (attempt ${retryCount})...`);
                setTimeout(processBatch, 2000); // Tunggu 2 detik sebelum retry
            } else {
                // Tampilkan pesan error
                document.getElementById('loadingOverlay').style.display = 'none';
                CustomModal.error(
                    `Waktu pemrosesan habis untuk batch ${currentBatch + 1}. ` +
                    `Server mungkin sibuk. Silakan coba lagi nanti.`,
                    'Timeout'
                );
            }
        };
        
        // Kirim request
        xhr.send(batchFormData);
    }
    
    // Mulai pemrosesan batch pertama
    processBatch();
    
    // Mencegah form submit normal
    return false;
}

// Pastikan fungsi ini tersedia secara global
window.submitFormWithProgress = submitFormWithProgress;