<?php
/**
 * Training Role Manager Class
 * Mengelola role permissions untuk training system
 */

class TrainingRoleManager {
    private $conn;
    private $activityLogger;
    
    // Available roles
    const ROLE_INSTRUCTOR = 'instructor';
    const ROLE_ASSISTANT = 'assistant';
    const ROLE_STUDENT = 'student';
    
    // Available permissions
    const PERMISSIONS = [
        'view_class' => 'View class details',
        'edit_class' => 'Edit class information',
        'manage_participants' => 'Manage class participants',
        'grade_assignments' => 'Grade assignments',
        'view_assignments' => 'View assignments',
        'submit_assignments' => 'Submit assignments',
        'manage_materials' => 'Manage class materials',
        'view_materials' => 'View class materials',
        'manage_attendance' => 'Manage attendance',
        'view_attendance' => 'View attendance',
        'create_announcements' => 'Create announcements',
        'view_announcements' => 'View announcements',
        'manage_schedule' => 'Manage class schedule',
        'view_schedule' => 'View class schedule',
        'export_data' => 'Export class data',
        'delete_class' => 'Delete class'
    ];
    
    public function __construct($conn, $activityLogger = null) {
        $this->conn = $conn;
        $this->activityLogger = $activityLogger;
        $this->initializeDefaultPermissions();
    }
    
    /**
     * Initialize default permissions for roles
     */
    private function initializeDefaultPermissions() {
        $defaultPermissions = [
            self::ROLE_INSTRUCTOR => [
                'view_class', 'edit_class', 'manage_participants', 'grade_assignments',
                'view_assignments', 'manage_materials', 'view_materials', 'manage_attendance',
                'view_attendance', 'create_announcements', 'view_announcements',
                'manage_schedule', 'view_schedule', 'export_data'
            ],
            self::ROLE_ASSISTANT => [
                'view_class', 'manage_participants', 'grade_assignments', 'view_assignments',
                'manage_materials', 'view_materials', 'manage_attendance', 'view_attendance',
                'create_announcements', 'view_announcements', 'view_schedule'
            ],
            self::ROLE_STUDENT => [
                'view_class', 'view_assignments', 'submit_assignments', 'view_materials',
                'view_attendance', 'view_announcements', 'view_schedule'
            ]
        ];
        
        foreach ($defaultPermissions as $role => $permissions) {
            foreach ($permissions as $permission) {
                $this->addPermission($role, $permission, false); // false = don't log
            }
        }
    }
    
    /**
     * Add permission to role
     */
    public function addPermission($role, $permission, $log = true) {
        $query = "INSERT IGNORE INTO training_role_permissions (role, permission) VALUES (?, ?)";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("ss", $role, $permission);
        
        if ($stmt->execute() && $log && $this->activityLogger) {
            $this->activityLogger->log($_SESSION['user_id'] ?? 0, 
                "Added permission '$permission' to role '$role'", 'role_management');
        }
        
        return $stmt->execute();
    }
    
    /**
     * Remove permission from role
     */
    public function removePermission($role, $permission) {
        $query = "DELETE FROM training_role_permissions WHERE role = ? AND permission = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("ss", $role, $permission);
        
        if ($stmt->execute() && $this->activityLogger) {
            $this->activityLogger->log($_SESSION['user_id'] ?? 0, 
                "Removed permission '$permission' from role '$role'", 'role_management');
        }
        
        return $stmt->execute();
    }
    
    /**
     * Check if role has permission
     */
    public function hasPermission($role, $permission) {
        $query = "SELECT id FROM training_role_permissions WHERE role = ? AND permission = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("ss", $role, $permission);
        $stmt->execute();
        
        return $stmt->get_result()->num_rows > 0;
    }
    
    /**
     * Get all permissions for a role
     */
    public function getRolePermissions($role) {
        $query = "SELECT permission FROM training_role_permissions WHERE role = ? ORDER BY permission";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("s", $role);
        $stmt->execute();
        
        $result = $stmt->get_result();
        $permissions = [];
        while ($row = $result->fetch_assoc()) {
            $permissions[] = $row['permission'];
        }
        
        return $permissions;
    }
    
    /**
     * Get user's role in a specific class
     */
    public function getUserClassRole($user_id, $class_id) {
        // Check if user is instructor
        $query = "SELECT 'instructor' as role FROM classes WHERE instructor_id = ? AND id = ?
                  UNION
                  SELECT 'assistant' as role FROM class_assistants WHERE user_id = ? AND class_id = ?
                  UNION
                  SELECT 'student' as role FROM class_participants WHERE user_id = ? AND class_id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("iiiiii", $user_id, $class_id, $user_id, $class_id, $user_id, $class_id);
        $stmt->execute();
        
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            return $row['role'];
        }
        
        return null;
    }
    
    /**
     * Check if user has permission for specific class
     */
    public function userHasClassPermission($user_id, $class_id, $permission) {
        $role = $this->getUserClassRole($user_id, $class_id);
        
        if (!$role) {
            return false;
        }
        
        return $this->hasPermission($role, $permission);
    }
    
    /**
     * Get all roles and their permissions
     */
    public function getAllRolesPermissions() {
        $query = "SELECT role, permission FROM training_role_permissions ORDER BY role, permission";
        $result = $this->conn->query($query);
        
        $roles = [];
        while ($row = $result->fetch_assoc()) {
            $roles[$row['role']][] = $row['permission'];
        }
        
        return $roles;
    }
    
    /**
     * Update role permissions (replace all)
     */
    public function updateRolePermissions($role, $permissions) {
        // Start transaction
        $this->conn->begin_transaction();
        
        try {
            // Remove all existing permissions for role
            $query = "DELETE FROM training_role_permissions WHERE role = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->bind_param("s", $role);
            $stmt->execute();
            
            // Add new permissions
            foreach ($permissions as $permission) {
                $this->addPermission($role, $permission, false);
            }
            
            // Log activity
            if ($this->activityLogger) {
                $this->activityLogger->log($_SESSION['user_id'] ?? 0, 
                    "Updated permissions for role '$role'", 'role_management');
            }
            
            $this->conn->commit();
            return true;
            
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }
    
    /**
     * Get available permissions list
     */
    public function getAvailablePermissions() {
        return self::PERMISSIONS;
    }
    
    /**
     * Get available roles
     */
    public function getAvailableRoles() {
        return [
            self::ROLE_INSTRUCTOR => 'Instructor',
            self::ROLE_ASSISTANT => 'Assistant',
            self::ROLE_STUDENT => 'Student'
        ];
    }
    
    /**
     * Middleware function to check permission
     */
    public function requirePermission($user_id, $class_id, $permission) {
        if (!$this->userHasClassPermission($user_id, $class_id, $permission)) {
            http_response_code(403);
            die('Access denied. You do not have permission to perform this action.');
        }
    }
    
    /**
     * Get permission statistics
     */
    public function getPermissionStats() {
        $query = "SELECT 
                    role,
                    COUNT(*) as permission_count
                  FROM training_role_permissions 
                  GROUP BY role
                  ORDER BY role";
        
        $result = $this->conn->query($query);
        return $result->fetch_all(MYSQLI_ASSOC);
    }
}
