<?php
// Include notification helper if not already included
if (!function_exists('getUnreadNotifications')) {
    // Try different paths to find the notification_helper.php file
    $paths = [
        '../includes/notification_helper.php',
        __DIR__ . '/notification_helper.php',
        dirname(__DIR__) . '/includes/notification_helper.php'
    ];

    foreach ($paths as $path) {
        if (file_exists($path)) {
            include_once $path;
            break;
        }
    }

    // If function still doesn't exist, define a dummy function to prevent errors
    if (!function_exists('getUnreadNotifications')) {
        function getUnreadNotifications($user_id, $limit = 10) {
            return [];
        }
    }
}

// Get unread notifications for current user
$unread_notifications = getUnreadNotifications($_SESSION['user_id'], 5);
$notification_count = count($unread_notifications);
?>

<!-- Notification Bell Icon -->
<div class="notification-container">
    <a href="#" class="notification-bell" id="notificationBell">
        <i class="fas fa-bell"></i>
        <?php if ($notification_count > 0): ?>
            <span class="notification-badge"><?= $notification_count ?></span>
        <?php endif; ?>
    </a>
    
    <!-- Notification Dropdown -->
    <div class="notification-dropdown" id="notificationDropdown">
        <div class="notification-header">
            <h6>Notifikasi</h6>
        </div>
        
        <div class="notification-body">
            <?php if (empty($unread_notifications)): ?>
                <div class="notification-empty">
                    <i class="fas fa-bell-slash"></i>
                    <p>Tidak ada notifikasi baru</p>
                </div>
            <?php else: ?>
                <?php foreach ($unread_notifications as $notification): ?>
                    <a href="mark_notification.php?id=<?= $notification['id'] ?>&redirect=<?= urlencode($_SERVER['REQUEST_URI']) ?>" class="notification-item">
                        <div class="notification-icon">
                            <?php
                            $icon_class = 'fa-info-circle';
                            $icon_color = '#2196F3';
                            switch ($notification['type']) {
                                case 'success':
                                    $icon_class = 'fa-check-circle';
                                    $icon_color = '#4CAF50';
                                    break;
                                case 'warning':
                                    $icon_class = 'fa-exclamation-triangle';
                                    $icon_color = '#FF9800';
                                    break;
                                case 'error':
                                    $icon_class = 'fa-times-circle';
                                    $icon_color = '#F44336';
                                    break;
                            }
                            ?>
                            <i class="fas <?= $icon_class ?>" style="color: <?= $icon_color ?>"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title"><?= htmlspecialchars($notification['title']) ?></div>
                            <div class="notification-message"><?= htmlspecialchars(substr($notification['message'], 0, 80)) ?><?= strlen($notification['message']) > 80 ? '...' : '' ?></div>
                            <div class="notification-time"><?= date('d M H:i', strtotime($notification['created_at'])) ?></div>
                        </div>
                    </a>
                <?php endforeach; ?>
                
                <div class="notification-footer">
                    <a href="all_notifications.php">Lihat Semua Notifikasi</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* Notification Container */
.notification-container {
    position: relative !important;
    display: inline-block !important;
    margin-right: 15px !important;
    z-index: 9999 !important;
}

/* Notification Bell */
.notification-bell {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 40px !important;
    height: 40px !important;
    color: #333 !important;
    text-decoration: none !important;
    border-radius: 50% !important;
    transition: all 0.3s ease !important;
    background-color: transparent !important;
    z-index: 9999 !important;
}

.notification-bell:hover {
    background-color: rgba(191, 0, 0, 0.1);
    color: #BF0000;
    transform: scale(1.1);
}

.notification-bell i {
    font-size: 18px;
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background-color: #FF3333;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Notification Dropdown */
.notification-dropdown {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    width: 320px !important;
    background: white !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    z-index: 99999 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(-10px) !important;
    transition: all 0.3s ease !important;
    max-height: 400px !important;
    overflow: hidden !important;
}

.notification-dropdown.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
}

/* Notification Header */
.notification-header {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f8f9fa;
}

.notification-header h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* Notification Body */
.notification-body {
    max-height: 300px;
    overflow-y: auto;
}

/* Empty State */
.notification-empty {
    padding: 30px 20px;
    text-align: center;
    color: #999;
}

.notification-empty i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}

.notification-empty p {
    margin: 0;
    font-size: 14px;
}

/* Notification Item */
.notification-item {
    display: flex;
    padding: 15px 20px;
    border-bottom: 1px solid #f5f5f5;
    text-decoration: none;
    color: inherit;
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: inherit;
}

.notification-item:last-child {
    border-bottom: none;
}

/* Notification Icon */
.notification-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #f0f0f0;
    margin-right: 12px;
}

.notification-icon i {
    font-size: 16px;
}

/* Notification Content */
.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
    line-height: 1.3;
}

.notification-message {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 4px;
}

.notification-time {
    font-size: 11px;
    color: #999;
}

/* Notification Footer */
.notification-footer {
    padding: 12px 20px;
    border-top: 1px solid #f0f0f0;
    background-color: #f8f9fa;
    text-align: center;
}

.notification-footer a {
    color: #BF0000;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.notification-footer a:hover {
    text-decoration: underline;
}

/* Responsive */
@media (max-width: 768px) {
    .notification-dropdown {
        width: 280px !important;
        right: -20px !important;
    }
}

/* Override any conflicting styles */
.notification-container * {
    box-sizing: border-box !important;
}

.notification-bell:hover {
    background-color: rgba(191, 0, 0, 0.1) !important;
    color: #BF0000 !important;
    transform: scale(1.1) !important;
}

.notification-bell i {
    font-size: 18px !important;
}

/* Ensure dropdown appears above all other elements */
.notification-dropdown {
    position: absolute !important;
    z-index: 999999 !important;
}

/* Fix for navbar conflicts */
nav .notification-container,
.navbar .notification-container {
    position: relative !important;
    display: inline-block !important;
}

nav .notification-bell,
.navbar .notification-bell {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const notificationBell = document.getElementById('notificationBell');
    const notificationDropdown = document.getElementById('notificationDropdown');
    
    if (notificationBell && notificationDropdown) {
        // Toggle dropdown on bell click
        notificationBell.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            notificationDropdown.classList.toggle('show');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!notificationBell.contains(e.target) && !notificationDropdown.contains(e.target)) {
                notificationDropdown.classList.remove('show');
            }
        });
        
        // Prevent dropdown from closing when clicking inside
        notificationDropdown.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
});
</script>
