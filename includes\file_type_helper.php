<?php
/**
 * File Type Helper Functions
 * Provides utilities for handling different file types and generating appropriate buttons
 */

/**
 * Get file type information based on file extension
 * @param string $file_path The file path or filename
 * @return array File type information including icon, color, label, and action
 */
function getFileTypeInfo($file_path) {
    $file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
    
    $file_types = [
        // Documents
        'pdf' => [
            'icon' => 'fas fa-file-pdf',
            'color' => 'btn-danger',
            'label' => 'PDF',
            'action' => 'view',
            'category' => 'document'
        ],
        'doc' => [
            'icon' => 'fas fa-file-word',
            'color' => 'btn-primary',
            'label' => 'Word',
            'action' => 'download',
            'category' => 'document'
        ],
        'docx' => [
            'icon' => 'fas fa-file-word',
            'color' => 'btn-primary',
            'label' => 'Word',
            'action' => 'download',
            'category' => 'document'
        ],
        'ppt' => [
            'icon' => 'fas fa-file-powerpoint',
            'color' => 'btn-warning',
            'label' => 'PowerPoint',
            'action' => 'download',
            'category' => 'document'
        ],
        'pptx' => [
            'icon' => 'fas fa-file-powerpoint',
            'color' => 'btn-warning',
            'label' => 'PowerPoint',
            'action' => 'download',
            'category' => 'document'
        ],
        'xls' => [
            'icon' => 'fas fa-file-excel',
            'color' => 'btn-success',
            'label' => 'Excel',
            'action' => 'download',
            'category' => 'document'
        ],
        'xlsx' => [
            'icon' => 'fas fa-file-excel',
            'color' => 'btn-success',
            'label' => 'Excel',
            'action' => 'download',
            'category' => 'document'
        ],
        'txt' => [
            'icon' => 'fas fa-file-alt',
            'color' => 'btn-secondary',
            'label' => 'Text',
            'action' => 'view',
            'category' => 'document'
        ],
        'rtf' => [
            'icon' => 'fas fa-file-alt',
            'color' => 'btn-secondary',
            'label' => 'RTF',
            'action' => 'download',
            'category' => 'document'
        ],
        
        // Images
        'jpg' => [
            'icon' => 'fas fa-file-image',
            'color' => 'btn-info',
            'label' => 'Gambar',
            'action' => 'view',
            'category' => 'image'
        ],
        'jpeg' => [
            'icon' => 'fas fa-file-image',
            'color' => 'btn-info',
            'label' => 'Gambar',
            'action' => 'view',
            'category' => 'image'
        ],
        'png' => [
            'icon' => 'fas fa-file-image',
            'color' => 'btn-info',
            'label' => 'Gambar',
            'action' => 'view',
            'category' => 'image'
        ],
        'gif' => [
            'icon' => 'fas fa-file-image',
            'color' => 'btn-info',
            'label' => 'Gambar',
            'action' => 'view',
            'category' => 'image'
        ],
        'bmp' => [
            'icon' => 'fas fa-file-image',
            'color' => 'btn-info',
            'label' => 'Gambar',
            'action' => 'view',
            'category' => 'image'
        ],
        'webp' => [
            'icon' => 'fas fa-file-image',
            'color' => 'btn-info',
            'label' => 'Gambar',
            'action' => 'view',
            'category' => 'image'
        ],
        'svg' => [
            'icon' => 'fas fa-file-image',
            'color' => 'btn-info',
            'label' => 'SVG',
            'action' => 'view',
            'category' => 'image'
        ],
        
        // Videos
        'mp4' => [
            'icon' => 'fas fa-file-video',
            'color' => 'btn-dark',
            'label' => 'Video',
            'action' => 'view',
            'category' => 'video'
        ],
        'avi' => [
            'icon' => 'fas fa-file-video',
            'color' => 'btn-dark',
            'label' => 'Video',
            'action' => 'download',
            'category' => 'video'
        ],
        'mov' => [
            'icon' => 'fas fa-file-video',
            'color' => 'btn-dark',
            'label' => 'Video',
            'action' => 'download',
            'category' => 'video'
        ],
        'wmv' => [
            'icon' => 'fas fa-file-video',
            'color' => 'btn-dark',
            'label' => 'Video',
            'action' => 'download',
            'category' => 'video'
        ],
        'mkv' => [
            'icon' => 'fas fa-file-video',
            'color' => 'btn-dark',
            'label' => 'Video',
            'action' => 'download',
            'category' => 'video'
        ],
        'flv' => [
            'icon' => 'fas fa-file-video',
            'color' => 'btn-dark',
            'label' => 'Video',
            'action' => 'download',
            'category' => 'video'
        ],
        'webm' => [
            'icon' => 'fas fa-file-video',
            'color' => 'btn-dark',
            'label' => 'Video',
            'action' => 'view',
            'category' => 'video'
        ],
        
        // Audio
        'mp3' => [
            'icon' => 'fas fa-file-audio',
            'color' => 'btn-purple',
            'label' => 'Audio',
            'action' => 'view',
            'category' => 'audio'
        ],
        'wav' => [
            'icon' => 'fas fa-file-audio',
            'color' => 'btn-purple',
            'label' => 'Audio',
            'action' => 'view',
            'category' => 'audio'
        ],
        'ogg' => [
            'icon' => 'fas fa-file-audio',
            'color' => 'btn-purple',
            'label' => 'Audio',
            'action' => 'view',
            'category' => 'audio'
        ],
        
        // Archives
        'zip' => [
            'icon' => 'fas fa-file-archive',
            'color' => 'btn-secondary',
            'label' => 'ZIP',
            'action' => 'download',
            'category' => 'archive'
        ],
        'rar' => [
            'icon' => 'fas fa-file-archive',
            'color' => 'btn-secondary',
            'label' => 'RAR',
            'action' => 'download',
            'category' => 'archive'
        ],
        '7z' => [
            'icon' => 'fas fa-file-archive',
            'color' => 'btn-secondary',
            'label' => '7Z',
            'action' => 'download',
            'category' => 'archive'
        ],
        'tar' => [
            'icon' => 'fas fa-file-archive',
            'color' => 'btn-secondary',
            'label' => 'TAR',
            'action' => 'download',
            'category' => 'archive'
        ],
        'gz' => [
            'icon' => 'fas fa-file-archive',
            'color' => 'btn-secondary',
            'label' => 'GZ',
            'action' => 'download',
            'category' => 'archive'
        ]
    ];
    
    // Return file type info or default
    return $file_types[$file_extension] ?? [
        'icon' => 'fas fa-file',
        'color' => 'btn-outline-secondary',
        'label' => strtoupper($file_extension),
        'action' => 'download',
        'category' => 'other'
    ];
}

/**
 * Generate file action buttons HTML
 * @param int $material_id The material ID
 * @param string $file_path The file path
 * @param string $base_url The base URL for download (default: '../download_material.php')
 * @return string HTML for the buttons
 */
function generateFileButtons($material_id, $file_path, $base_url = '../download_material.php') {
    $file_info = getFileTypeInfo($file_path);
    $html = '';
    
    // Main action button (View or Download)
    if ($file_info['action'] === 'view') {
        $html .= sprintf(
            '<a href="%s?id=%d" class="btn %s" target="_blank" title="Lihat %s">
                <i class="%s"></i> Lihat %s
            </a>',
            htmlspecialchars($base_url),
            $material_id,
            htmlspecialchars($file_info['color']),
            htmlspecialchars($file_info['label']),
            htmlspecialchars($file_info['icon']),
            htmlspecialchars($file_info['label'])
        );
    } else {
        $html .= sprintf(
            '<a href="%s?id=%d" class="btn %s" target="_blank" title="Download %s">
                <i class="%s"></i> Download %s
            </a>',
            htmlspecialchars($base_url),
            $material_id,
            htmlspecialchars($file_info['color']),
            htmlspecialchars($file_info['label']),
            htmlspecialchars($file_info['icon']),
            htmlspecialchars($file_info['label'])
        );
    }
    
    // Additional download button for viewable files
    $viewable_extensions = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'mp4', 'webm', 'mp3', 'wav', 'ogg'];
    $file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
    
    if ($file_info['action'] === 'view' && in_array($file_extension, $viewable_extensions)) {
        $html .= sprintf(
            ' <a href="%s?id=%d&download=1" class="btn btn-outline-secondary btn-sm" title="Download File">
                <i class="fas fa-download"></i>
            </a>',
            htmlspecialchars($base_url),
            $material_id
        );
    }
    
    return $html;
}

/**
 * Check if file type is viewable in browser
 * @param string $file_path The file path
 * @return bool True if viewable, false otherwise
 */
function isFileViewable($file_path) {
    $file_info = getFileTypeInfo($file_path);
    return $file_info['action'] === 'view';
}

/**
 * Get file category
 * @param string $file_path The file path
 * @return string The file category (document, image, video, audio, archive, other)
 */
function getFileCategory($file_path) {
    $file_info = getFileTypeInfo($file_path);
    return $file_info['category'];
}
?>
