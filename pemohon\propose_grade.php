<?php
include '../config/config.php';
include 'security.php';
include '../includes/class_role_helper.php';
include '../includes/grade_proposal_helper.php';
include '../includes/notification_helper.php';
include '../includes/permission_helper.php';

// Get assignment ID and student ID from URL
$assignment_id = isset($_GET['assignment_id']) ? intval($_GET['assignment_id']) : 0;
$student_id = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;

// Check if assignment exists
$assignment_query = "SELECT a.*, c.id as class_id, c.title as class_title 
                    FROM training_assignments a
                    JOIN training_classes c ON a.class_id = c.id
                    WHERE a.id = ?";
$stmt = $conn->prepare($assignment_query);
$stmt->bind_param("i", $assignment_id);
$stmt->execute();
$result = $stmt->get_result();
$assignment = $result->fetch_assoc();

if (!$assignment) {
    $_SESSION['error'] = "Tugas tidak ditemukan.";
    header("Location: classroom.php");
    exit();
}

$class_id = $assignment['class_id'];

// Check if user is an assistant in this class
if (!isAssistant($_SESSION['user_id'], $class_id)) {
    $_SESSION['error'] = "Anda tidak memiliki akses ke halaman ini.";
    header("Location: classroom_detail.php?id=$class_id");
    exit();
}

// Check if student exists and is in the class
$student_query = "SELECT u.id, u.name 
                 FROM users u
                 JOIN training_participants p ON u.id = p.user_id
                 WHERE u.id = ? AND p.class_id = ? AND p.role = 'student'";
$stmt = $conn->prepare($student_query);
$stmt->bind_param("ii", $student_id, $class_id);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();

if (!$student) {
    $_SESSION['error'] = "Peserta tidak ditemukan dalam kelas ini.";
    header("Location: classroom_detail.php?id=$class_id");
    exit();
}

// Get submission if exists
$submission_query = "SELECT * FROM training_assignment_submissions 
                    WHERE assignment_id = ? AND user_id = ?";
$stmt = $conn->prepare($submission_query);
$stmt->bind_param("ii", $assignment_id, $student_id);
$stmt->execute();
$result = $stmt->get_result();
$submission = $result->fetch_assoc();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['propose_grade'])) {
    $proposed_grade = floatval($_POST['grade']);
    $feedback = $_POST['feedback'];
    
    // Validate grade
    if ($proposed_grade < 0 || $proposed_grade > 100) {
        $_SESSION['error'] = "Nilai harus antara 0 dan 100.";
    } else {
        $result = createGradeProposal(
            $class_id, 
            $assignment_id, 
            $student_id, 
            $_SESSION['user_id'], 
            $proposed_grade, 
            $feedback
        );
        
        if ($result) {
            $_SESSION['success'] = "Pengajuan nilai berhasil dibuat. Menunggu persetujuan instruktur.";
            header("Location: classroom_detail.php?id=$class_id");
            exit();
        } else {
            $_SESSION['error'] = "Gagal membuat pengajuan nilai.";
        }
    }
}

// Page title
$page_title = "Ajukan Nilai - " . $assignment['title'];
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <link rel="stylesheet" href="../asset/css/bootstrap.min.css">
    <link rel="stylesheet" href="../asset/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .submission-content {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .file-attachment {
            display: inline-block;
            padding: 8px 12px;
            background-color: #e9ecef;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .file-attachment i {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="classroom.php">Kelas</a></li>
                        <li class="breadcrumb-item"><a href="classroom_detail.php?id=<?= $class_id ?>"><?= $assignment['class_title'] ?></a></li>
                        <li class="breadcrumb-item active">Ajukan Nilai</li>
                    </ol>
                </nav>
                
                <h2 class="mb-4">Ajukan Nilai</h2>
                
                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger">
                        <?= $_SESSION['error'] ?>
                        <?php unset($_SESSION['error']); ?>
                    </div>
                <?php endif; ?>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Detail Tugas</h5>
                    </div>
                    <div class="card-body">
                        <h4><?= htmlspecialchars($assignment['title']) ?></h4>
                        <p class="text-muted">Peserta: <?= htmlspecialchars($student['name']) ?></p>
                        <p><?= nl2br(htmlspecialchars($assignment['description'])) ?></p>
                        
                        <?php if ($submission): ?>
                            <h5 class="mt-4">Jawaban Peserta</h5>
                            <div class="submission-content">
                                <?php if (!empty($submission['content'])): ?>
                                    <?= nl2br(htmlspecialchars($submission['content'])) ?>
                                <?php else: ?>
                                    <p class="text-muted">Tidak ada konten teks.</p>
                                <?php endif; ?>
                                
                                <?php if (!empty($submission['file_path'])): ?>
                                    <div class="mt-3">
                                        <h6>Lampiran:</h6>
                                        <div class="file-attachment">
                                            <i class="fas fa-file"></i>
                                            <a href="../<?= $submission['file_path'] ?>" target="_blank">
                                                <?= basename($submission['file_path']) ?>
                                            </a>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="text-muted mt-2">
                                    Dikirim pada: <?= date('d M Y H:i', strtotime($submission['submitted_at'])) ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                Peserta belum mengirimkan jawaban untuk tugas ini.
                            </div>
                        <?php endif; ?>
                        
                        <form method="post" action="" class="mt-4">
                            <div class="mb-3">
                                <label for="grade" class="form-label">Nilai (0-100):</label>
                                <input type="number" class="form-control" id="grade" name="grade" min="0" max="100" step="0.01" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="feedback" class="form-label">Umpan Balik untuk Peserta:</label>
                                <textarea class="form-control" id="feedback" name="feedback" rows="5" required></textarea>
                                <div class="form-text">Berikan umpan balik yang konstruktif untuk membantu peserta memahami nilai mereka.</div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="classroom_detail.php?id=<?= $class_id ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Kembali
                                </a>
                                <button type="submit" name="propose_grade" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> Ajukan Nilai
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../asset/js/bootstrap.bundle.min.js"></script>
</body>
</html>
