<?php
// Test script to debug trainer submission
session_start();

echo "<h2>POST Data Debug</h2>";
echo "<h3>All POST Data:</h3>";
echo "<pre>";
print_r($_POST);
echo "</pre>";

echo "<h3>Trainer Data Specifically:</h3>";
echo "trainer_name_internal: ";
var_dump($_POST['trainer_name_internal'] ?? 'NOT SET');
echo "<br>";

echo "trainer_nik_internal: ";
var_dump($_POST['trainer_nik_internal'] ?? 'NOT SET');
echo "<br>";

echo "trainer_department_internal: ";
var_dump($_POST['trainer_department_internal'] ?? 'NOT SET');
echo "<br>";

echo "trainer_sub_department_internal: ";
var_dump($_POST['trainer_sub_department_internal'] ?? 'NOT SET');
echo "<br>";

echo "trainer_position_internal: ";
var_dump($_POST['trainer_position_internal'] ?? 'NOT SET');
echo "<br>";

echo "provider_type: ";
var_dump($_POST['provider_type'] ?? 'NOT SET');
echo "<br>";

if ($_POST) {
    echo "<h3>Validation Test:</h3>";
    $training_provider = $_POST['provider_type'] ?? '';
    $trainer_name_internal = $_POST['trainer_name_internal'] ?? [];
    
    echo "Training Provider: " . htmlspecialchars($training_provider) . "<br>";
    echo "Trainer Name Internal: ";
    var_dump($trainer_name_internal);
    echo "<br>";
    echo "Is Array: " . (is_array($trainer_name_internal) ? 'YES' : 'NO') . "<br>";
    echo "Array Count: " . (is_array($trainer_name_internal) ? count($trainer_name_internal) : 'NOT ARRAY') . "<br>";
    
    if ($training_provider === 'Internal') {
        $has_trainer = false;
        if (is_array($trainer_name_internal) && count($trainer_name_internal) > 0) {
            foreach ($trainer_name_internal as $name) {
                if (!empty(trim($name))) {
                    $has_trainer = true;
                    break;
                }
            }
        }
        
        echo "Has Trainer: " . ($has_trainer ? 'YES' : 'NO') . "<br>";
        
        if (!$has_trainer) {
            echo "<div style='color: red; font-weight: bold;'>ERROR: Setidaknya satu trainer harus ditambahkan untuk provider internal.</div>";
        } else {
            echo "<div style='color: green; font-weight: bold;'>SUCCESS: Trainer validation passed!</div>";
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Trainer Submission Test</title>
</head>
<body>
    <h2>Test Form</h2>
    <form method="POST" action="">
        <h3>Provider Type</h3>
        <select name="provider_type" id="provider_type_select" onchange="updateTrainingProvider()">
            <option value="">Select Provider</option>
            <option value="Internal">Internal</option>
            <option value="External">External</option>
        </select>
        
        <div id="internalFields" style="display: none;">
            <h3>Internal Trainers</h3>
            <div>
                <label>Trainer Name:</label>
                <input type="text" name="trainer_name_internal[]" value="Test Trainer 1">
            </div>
            <div>
                <label>Trainer NIK:</label>
                <input type="text" name="trainer_nik_internal[]" value="12345">
            </div>
            <div>
                <label>Department:</label>
                <input type="text" name="trainer_department_internal[]" value="IT">
            </div>
            <div>
                <label>Sub Department:</label>
                <input type="text" name="trainer_sub_department_internal[]" value="Development">
            </div>
            <div>
                <label>Position:</label>
                <input type="text" name="trainer_position_internal[]" value="Senior Developer">
            </div>
        </div>
        
        <div id="externalFields" style="display: none;">
            <h3>External Provider</h3>
            <div>
                <label>Provider Name:</label>
                <input type="text" name="provider_name" value="External Provider">
            </div>
        </div>
        
        <br><br>
        <button type="submit">Test Submit</button>
    </form>
    
    <script>
        function updateTrainingProvider() {
            const providerSelect = document.getElementById("provider_type_select");
            const internalFields = document.getElementById("internalFields");
            const externalFields = document.getElementById("externalFields");
            
            if (providerSelect.value === "Internal") {
                internalFields.style.display = "block";
                externalFields.style.display = "none";
            } else if (providerSelect.value === "External") {
                internalFields.style.display = "none";
                externalFields.style.display = "block";
            } else {
                internalFields.style.display = "none";
                externalFields.style.display = "none";
            }
        }
    </script>
</body>
</html>
