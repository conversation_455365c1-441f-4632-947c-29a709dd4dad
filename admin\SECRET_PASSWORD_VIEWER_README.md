# 🔐 Secret Password Viewer - Developer Documentation

## Overview
Secret Password Viewer adalah fitur khusus developer untuk melihat informasi password user dalam sistem training. Fitur ini dibuat dengan multiple layer security untuk memastikan hanya developer yang berwenang yang dapat mengakses.

## 🚨 PERINGATAN KEAMANAN
- **HANYA UNTUK DEVELOPER**: Fitur ini hanya boleh digunakan oleh developer yang berwenang
- **SEMUA AKSES DICATAT**: Setiap akses ke password viewer akan dicatat dengan detail lengkap
- **ENVIRONMENT RESTRICTED**: Hanya aktif di development/staging environment
- **PASSWORD TETAP HASH**: Password ditampilkan dalam bentuk hash, tidak bisa di-decrypt

## 📁 File yang Terlibat

### 1. `config/developer_config.php`
- Konfigurasi utama untuk developer access
- Berisi secret token, IP whitelist, dan environment settings
- Fungsi validasi dan logging

### 2. `admin/secret_password_viewer.php`
- Interface utama untuk melihat password
- Menampilkan informasi user dan password hash
- Dilengkapi dengan search dan filter

### 3. `admin/manage_user.php` (Modified)
- Ditambahkan tombol "Developer Access" 
- Modal untuk input secret token
- Hanya muncul untuk admin (role_id = 99)

## 🔑 Cara Menggunakan

### Step 1: Akses dari Manage Users
1. Login sebagai admin (role_id = 99)
2. Buka halaman `admin/manage_user.php`
3. Klik tombol "🔐 Developer Access" (warna merah)

### Step 2: Input Secret Token
1. Modal akan muncul meminta secret token
2. Masukkan token: `dev_secret_2024_training_system`
3. Klik "Access Password Viewer"

### Step 3: View Password Information
1. Halaman secret password viewer akan terbuka di tab baru
2. Gunakan search untuk mencari user tertentu
3. Lihat informasi password hash untuk setiap user

## 🛡️ Security Features

### Multi-Layer Authentication
1. **Admin Role Check**: Harus login sebagai admin (role_id = 99)
2. **Secret Token**: Harus memasukkan token yang benar
3. **Environment Check**: Hanya aktif di development environment
4. **IP Restriction**: Opsional pembatasan IP address

### Activity Logging
- Semua akses dicatat ke `activity_logs` table (jika ada)
- Backup log ke file `logs/developer_access.log`
- Informasi yang dicatat:
  - User ID dan nama
  - IP address dan user agent
  - Timestamp akses
  - User yang diakses
  - URL request

### Token Security
- Secret token disimpan di config file
- Bisa diganti sesuai kebutuhan
- Tidak ada token yang tersimpan di database

## ⚙️ Konfigurasi

### Mengubah Secret Token
Edit file `config/developer_config.php`:
```php
define('DEVELOPER_SECRET_TOKEN', 'your_new_secret_token_here');
```

### Mengatur IP Whitelist
Edit array `$ALLOWED_DEVELOPER_IPS`:
```php
$ALLOWED_DEVELOPER_IPS = [
    '127.0.0.1',        // localhost
    '*************',    // IP developer
    '10.0.0.0/8',       // Network range
];
```

### Mengatur Environment
Edit konstanta `CURRENT_ENVIRONMENT`:
```php
define('CURRENT_ENVIRONMENT', 'development'); // atau 'staging'
```

## 🔍 URL Access Patterns

### Direct URL Access
```
admin/secret_password_viewer.php?token=dev_secret_2024_training_system
```

### Search Specific User
```
admin/secret_password_viewer.php?token=dev_secret_2024_training_system&search=john
```

### View Specific User ID
```
admin/secret_password_viewer.php?token=dev_secret_2024_training_system&user_id=123
```

## 📊 Log Monitoring

### Database Logs
Cek table `activity_logs` untuk melihat akses:
```sql
SELECT * FROM activity_logs 
WHERE action = 'SECRET_PASSWORD_VIEWER_ACCESS' 
ORDER BY created_at DESC;
```

### File Logs
Cek file `logs/developer_access.log`:
```bash
tail -f logs/developer_access.log
```

## 🚫 Disable Feature

### Temporary Disable
Ubah environment di `config/developer_config.php`:
```php
define('CURRENT_ENVIRONMENT', 'production');
```

### Permanent Disable
1. Hapus atau rename file `config/developer_config.php`
2. Atau ubah secret token menjadi string kosong

## 🔧 Troubleshooting

### "Access Denied" Error
- Pastikan login sebagai admin (role_id = 99)
- Cek secret token sudah benar
- Pastikan environment = development/staging
- Cek IP address dalam whitelist (jika diaktifkan)

### Tombol Developer Access Tidak Muncul
- Pastikan file `config/developer_config.php` ada
- Pastikan login sebagai admin
- Cek console browser untuk error JavaScript

### Password Tidak Muncul
- Password memang ditampilkan dalam bentuk hash
- Ini adalah fitur keamanan, password tidak bisa di-decrypt
- Gunakan untuk debugging atau reset password manual

## 📝 Best Practices

1. **Ganti Secret Token**: Gunakan token yang unik dan kompleks
2. **Monitor Logs**: Selalu monitor akses ke fitur ini
3. **Restrict IP**: Aktifkan IP restriction untuk keamanan tambahan
4. **Environment Control**: Pastikan hanya aktif di development
5. **Regular Audit**: Audit log secara berkala

## 🆘 Emergency Procedures

### Jika Token Bocor
1. Segera ganti secret token di `config/developer_config.php`
2. Cek log untuk melihat akses yang mencurigakan
3. Restart aplikasi jika perlu

### Jika Akses Tidak Sah
1. Disable fitur dengan mengubah environment ke 'production'
2. Audit semua log akses
3. Ganti semua password yang mungkin terkompromi

---

**⚠️ REMINDER**: Fitur ini sangat sensitif dan hanya boleh digunakan untuk keperluan development yang sah. Selalu ikuti prosedur keamanan yang berlaku.
