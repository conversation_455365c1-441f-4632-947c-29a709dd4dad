<?php
session_start();
include '../config/config.php';
require_once '../includes/SystemManager.php';

// Validasi akses admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit();
}

// Validasi parameter
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid submission ID']);
    exit();
}

$submission_id = intval($_GET['id']);

try {
    // Initialize System Manager
    $systemManager = new SystemManager($conn);
    $faqManager = $systemManager->getFAQManager();
    
    // Get submission data
    $submission = $faqManager->getSubmission($submission_id);
    
    if ($submission) {
        echo json_encode([
            'success' => true,
            'submission' => $submission
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Submission not found'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Get submission error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error: ' . $e->getMessage()
    ]);
}
?>
