<?php
// File: admin/sync_users_karyawan.php
// Script untuk sinkronisasi data antara tabel users dan karyawan

include '../config/config.php';
include 'security.php';

$page_title = "Sinkronisasi Data Users - Karyawan";
include 'header.php';

// Process sync if requested
$sync_results = [];
if (isset($_POST['sync_action'])) {
    $action = $_POST['sync_action'];
    
    if ($action === 'sync_by_nik') {
        // Sync users with karyawan by NIK
        $query = "SELECT u.id as user_id, u.nik as user_nik, u.name as user_name,
                         k.id as karyawan_id, k.nik as karyawan_nik, k.nama as karyawan_nama
                  FROM users u
                  LEFT JOIN karyawan k ON u.nik = k.nik
                  WHERE u.nik IS NOT NULL AND u.nik != ''";
        
        $result = $conn->query($query);
        $sync_results['by_nik'] = [];
        
        while ($row = $result->fetch_assoc()) {
            $sync_results['by_nik'][] = $row;
        }
        
    } elseif ($action === 'sync_by_name') {
        // Find potential matches by name
        $query = "SELECT u.id as user_id, u.nik as user_nik, u.name as user_name,
                         k.id as karyawan_id, k.nik as karyawan_nik, k.nama as karyawan_nama
                  FROM users u
                  LEFT JOIN karyawan k ON LOWER(u.name) = LOWER(k.nama)
                  WHERE u.nik IS NOT NULL";
        
        $result = $conn->query($query);
        $sync_results['by_name'] = [];
        
        while ($row = $result->fetch_assoc()) {
            $sync_results['by_name'][] = $row;
        }
        
    } elseif ($action === 'update_user_nik') {
        // Update user NIK based on selected mapping
        $user_id = $_POST['user_id'];
        $new_nik = $_POST['new_nik'];
        
        $update_query = "UPDATE users SET nik = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("si", $new_nik, $user_id);
        
        if ($update_stmt->execute()) {
            $sync_results['update_success'] = "NIK user berhasil diupdate";
        } else {
            $sync_results['update_error'] = "Gagal update NIK: " . $conn->error;
        }
    }
}

// Get unmatched users
$unmatched_query = "SELECT u.id, u.name, u.nik, u.email, u.dept, u.jabatan
                   FROM users u
                   LEFT JOIN karyawan k ON u.nik = k.nik
                   WHERE k.id IS NULL AND u.is_active = 1
                   ORDER BY u.name";
$unmatched_result = $conn->query($unmatched_query);

// Get all karyawan for matching
$karyawan_query = "SELECT id, nik, nama, dept, jabatan FROM karyawan ORDER BY nama";
$karyawan_result = $conn->query($karyawan_query);
$all_karyawan = [];
while ($row = $karyawan_result->fetch_assoc()) {
    $all_karyawan[] = $row;
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-sync-alt"></i> Sinkronisasi Data Users - Karyawan
                    </h4>
                    <p class="text-muted">Tool untuk menyelaraskan data antara tabel users dan karyawan</p>
                </div>
                <div class="card-body">
                    
                    <!-- Sync Actions -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="sync_action" value="sync_by_nik">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-id-card"></i> Cek Sinkronisasi by NIK
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="sync_action" value="sync_by_name">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-user"></i> Cek Sinkronisasi by Nama
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Sync Results -->
                    <?php if (!empty($sync_results)): ?>
                        <div class="alert alert-info">
                            <h6>Hasil Sinkronisasi:</h6>
                            <?php if (isset($sync_results['update_success'])): ?>
                                <div class="alert alert-success"><?= $sync_results['update_success'] ?></div>
                            <?php endif; ?>
                            <?php if (isset($sync_results['update_error'])): ?>
                                <div class="alert alert-danger"><?= $sync_results['update_error'] ?></div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Unmatched Users -->
                    <div class="card">
                        <div class="card-header">
                            <h5>Users Tanpa Data Karyawan yang Cocok</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Nama User</th>
                                            <th>NIK User</th>
                                            <th>Email</th>
                                            <th>Dept</th>
                                            <th>Jabatan</th>
                                            <th>Cocokkan dengan Karyawan</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($user = $unmatched_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><strong><?= htmlspecialchars($user['name']) ?></strong></td>
                                            <td><?= htmlspecialchars($user['nik']) ?></td>
                                            <td><?= htmlspecialchars($user['email']) ?></td>
                                            <td><?= htmlspecialchars($user['dept']) ?></td>
                                            <td><?= htmlspecialchars($user['jabatan']) ?></td>
                                            <td>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="sync_action" value="update_user_nik">
                                                    <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                                    <select name="new_nik" class="form-select form-select-sm" required>
                                                        <option value="">Pilih Karyawan...</option>
                                                        <?php foreach ($all_karyawan as $karyawan): ?>
                                                            <?php 
                                                            $similarity = similar_text(strtolower($user['name']), strtolower($karyawan['nama']));
                                                            $highlight = $similarity > 5 ? 'style="background-color: #fff3cd;"' : '';
                                                            ?>
                                                            <option value="<?= $karyawan['nik'] ?>" <?= $highlight ?>>
                                                                <?= htmlspecialchars($karyawan['nama']) ?> (<?= htmlspecialchars($karyawan['nik']) ?>) - <?= htmlspecialchars($karyawan['dept']) ?>
                                                                <?php if ($similarity > 5): ?> ⭐<?php endif; ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                            </td>
                                            <td>
                                                    <button type="submit" class="btn btn-success btn-sm">
                                                        <i class="fas fa-link"></i> Link
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="alert alert-warning mt-4">
                        <h6><i class="fas fa-info-circle"></i> Petunjuk:</h6>
                        <ul class="mb-0">
                            <li><strong>Cek Sinkronisasi by NIK:</strong> Melihat users yang sudah/belum tersinkronisasi berdasarkan NIK</li>
                            <li><strong>Cek Sinkronisasi by Nama:</strong> Mencari kemungkinan match berdasarkan nama</li>
                            <li><strong>Link User-Karyawan:</strong> Menghubungkan user dengan data karyawan yang sesuai</li>
                            <li><strong>⭐ Highlighted:</strong> Karyawan dengan nama yang mirip (recommended)</li>
                        </ul>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-highlight similar names
document.addEventListener('DOMContentLoaded', function() {
    const selects = document.querySelectorAll('select[name="new_nik"]');
    
    selects.forEach(select => {
        const userName = select.closest('tr').querySelector('td:first-child strong').textContent.toLowerCase();
        
        Array.from(select.options).forEach(option => {
            if (option.value) {
                const karyawanName = option.textContent.toLowerCase();
                const similarity = calculateSimilarity(userName, karyawanName);
                
                if (similarity > 0.6) {
                    option.style.backgroundColor = '#d4edda';
                    option.textContent += ' 🎯';
                }
            }
        });
    });
});

function calculateSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
}

function levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }
    
    return matrix[str2.length][str1.length];
}
</script>

<?php include 'footer.php'; ?>
