<?php
session_start();

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

include '../config/config.php';

// Ambil statistik pengajuan
$query_stats = "SELECT 
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) AS total_submissions,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) AS pending,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) AS approved,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) AS completed
                FROM training_submissions";

$result_stats = mysqli_query($conn, $query_stats);
$stats = $result_stats ? mysqli_fetch_assoc($result_stats) : [
    'total_submissions' => 0,
    'pending' => 0,
    'approved' => 0,
    'completed' => 0
];

$current_date = date("Y-m-d");

$query_latest = "SELECT full_name, training_topic, training_date, status FROM training_submissions 
                 WHERE training_date >= ? 
                 ORDER BY training_date DESC ";

$stmt = $conn->prepare($query_latest);
$stmt->bind_param("s", $current_date);
$stmt->execute();
$result_latest = $stmt->get_result();
$latest_submissions = $result_latest->fetch_all(MYSQLI_ASSOC);

$stmt->close();

// Ambil data pengguna dari database
$user_id = $_SESSION['user_id'];
$query_user = "SELECT name FROM users WHERE id = ?";
$stmt = $conn->prepare($query_user);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result_user = $stmt->get_result();
$user = $result_user->fetch_assoc();
$stmt->close();
$username = $user['name'] ?? 'Pengguna';

mysqli_close($conn); // Close the connection after all queries are executed

?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style> 
/* Welcoming Section */
.welcome-container {
    margin-top: 30px;
    text-align: center;
    padding: 40px 20px;
    background:rgb(157, 0, 0);
    color: white;
    border-radius: 10px;
    margin-bottom: 30px;
}

.welcome-container h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.welcome-container p {
    font-size: 1.2rem;
}


/* Stats Section */
.stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.stat-box {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
    flex: 1;
    margin: 0 10px;
    transition: transform 0.3s;
}

.stat-box h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.stat-box p {
    font-size: 1.5rem;
    color:rgb(255, 0, 0);
}

.stat-box:hover {
    transform: translateY(-5px);
}

/* Tabel Pengajuan Terbaru */
.latest-submissions table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 30px;
    background: #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.latest-submissions table th, .latest-submissions table td {
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid #ddd;
}

.latest-submissions table th {
    background: #ddd;
    border-color: white solid;
    border: 1px solid #ddd;
    color: black;
    font-size: 1.1rem;
}

.latest-submissions table td {
    font-size: 1rem;
}

.latest-submissions table tr:hover {
    background-color: #f1f1f1;
}

/* Aksi Cepat */
.quick-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

</style>
<body>
    <?php include '../config/navbar.php';?>
    <div class="container-form">
    <div class="welcome-container">
        <h1>👋 Selamat Datang, <span><?= htmlspecialchars($username ?? 'Pengguna') ?></span>!</h1>
        <p>Semoga harimu menyenangkan dan penuh semangat 🚀</p>
    </div>

    <div class="dashboard-container">
        <!-- Ringkasan Statistik -->
        <div class="stats">
            <div class="stat-box">
                <h3>Total Pengajuan</h3>
                <p><?= htmlspecialchars($stats['total_submissions'] ?? 0) ?></p>
            </div>
            <div class="stat-box">
                <h3>Pengajuan Pending</h3>
                <p><?= htmlspecialchars($stats['pending'] ?? 0) ?></p>
            </div>
            <div class="stat-box">
                <h3>Pelatihan Disetujui</h3>
                <p><?= htmlspecialchars($stats['approved'] ?? 0) ?></p>
            </div>
            <div class="stat-box">
                <h3>Pelatihan Selesai</h3>
                <p><?= htmlspecialchars($stats['completed'] ?? 0) ?></p>
            </div>
        </div>

        <!-- Daftar Pengajuan Terbaru -->
        <div class="latest-submissions">
            <h2>Pengajuan Training Terbaru</h2>
            <table>
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Nama</th>
                        <th>Jenis Training</th>
                        <th>Tanggal Pengajuan</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($latest_submissions)): ?>
                        <?php $no = 1; foreach ($latest_submissions as $submission): ?>
                            <tr>
                                <td><?= $no++ ?></td>
                                <td><?= htmlspecialchars($submission['full_name'] ?? 'Tidak Diketahui') ?></td>
<td><?= htmlspecialchars($submission['training_topic'] ?? 'Tidak Diketahui') ?></td>
<td><?= htmlspecialchars($submission['training_date'] ?? 'Tidak Diketahui') ?></td>
<td><?= htmlspecialchars($submission['status'] ?? 'Tidak Diketahui') ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="5">Belum ada pengajuan training terbaru.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Aksi Cepat -->
        <div class="quick-actions">
            <a href="form.php" class="btn">Tambah Pengajuan</a>
            <a href="approved.php" class="btn">Lihat Pelatihan Disetujui</a>
        </div>
    </div>
    </div>
    <?php include '../config/footer.php';?>
</body>
</html>
