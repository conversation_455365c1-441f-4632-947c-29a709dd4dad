<?php
/**
 * Script untuk test notifikasi dengan dept head yang benar
 */

// Disable session untuk testing
define('NO_SESSION', true);

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/notification_helper.php';

echo "🧪 Testing Correct Notification Logic\n";
echo "======================================\n\n";

// Test dengan training ID 146 (HRGA department) TANPA next_approver_id spesifik
echo "📧 Testing notification for HRGA training without specific approver...\n";

try {
    $result = send_status_update_notification(
        146,                       // training_id (HRGA department)
        'Pending',                 // status
        'Test notification - should go to correct dept head',  // comments
        '',                        // assignment
        2,                         // next_approver_role_id (dept head)
        $conn,                     // database connection
        null                       // NO specific approver - let system find correct one
    );
    
    if ($result['success']) {
        echo "   ✅ Notification sent successfully!\n";
        echo "   📧 Message: {$result['message']}\n";
        
        if (isset($result['errors']) && !empty($result['errors'])) {
            echo "   ⚠️  Errors encountered:\n";
            foreach ($result['errors'] as $error) {
                echo "      - $error\n";
            }
        }
    } else {
        echo "   ❌ Notification failed\n";
        echo "   💥 Error: {$result['message']}\n";
        
        if (isset($result['errors']) && !empty($result['errors'])) {
            echo "   📝 Error details:\n";
            foreach ($result['errors'] as $error) {
                echo "      - $error\n";
            }
        }
    }
} catch (Exception $e) {
    echo "   ❌ Exception occurred: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n";

// Test dengan departemen lain
echo "📧 Testing notification for different departments...\n\n";

$test_departments = [
    'ENG' => 'Engineering training',
    'WRH02' => 'Warehouse training',
    'FAC' => 'Finance training'
];

foreach ($test_departments as $dept => $description) {
    echo "🏢 Testing $description (Dept: $dept):\n";
    
    // Cari training dari departemen ini atau buat simulasi
    $dept_training_query = "SELECT id FROM training_submissions WHERE departemen = ? LIMIT 1";
    $dept_training_stmt = $conn->prepare($dept_training_query);
    $dept_training_stmt->bind_param("s", $dept);
    $dept_training_stmt->execute();
    $dept_training_result = $dept_training_stmt->get_result();
    
    if ($dept_training_result->num_rows > 0) {
        $dept_training = $dept_training_result->fetch_assoc();
        $training_id = $dept_training['id'];
        echo "   📚 Using existing training ID: $training_id\n";
    } else {
        echo "   📚 No existing training for $dept, using ID 146 with dept override\n";
        $training_id = 146;
        
        // Temporarily update training 146 to test different department
        $update_query = "UPDATE training_submissions SET departemen = ? WHERE id = 146";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("s", $dept);
        $update_stmt->execute();
    }
    
    // Cari expected dept head
    $expected_dept_head_query = "SELECT u.name FROM users u 
                                INNER JOIN user_departments ud ON u.id = ud.user_id 
                                WHERE ud.dept = ? AND u.role_id = 2 LIMIT 1";
    $expected_stmt = $conn->prepare($expected_dept_head_query);
    $expected_stmt->bind_param("s", $dept);
    $expected_stmt->execute();
    $expected_result = $expected_stmt->get_result();
    
    if ($expected_result->num_rows > 0) {
        $expected_dept_head = $expected_result->fetch_assoc();
        echo "   🎯 Expected dept head: {$expected_dept_head['name']}\n";
        
        // Test notification (dry run - just check logic, don't send email)
        echo "   🔍 Testing notification logic...\n";
        
        try {
            // Simulate the notification logic without actually sending
            $training_query = "SELECT departemen FROM training_submissions WHERE id = ?";
            $training_stmt = $conn->prepare($training_query);
            $training_stmt->bind_param("i", $training_id);
            $training_stmt->execute();
            $training_result = $training_stmt->get_result();
            $training_data = $training_result->fetch_assoc();
            
            $approver_query = "SELECT u.id, u.name, u.email 
                              FROM users u 
                              INNER JOIN user_departments ud ON u.id = ud.user_id 
                              WHERE ud.dept = ? AND u.role_id = 2 
                              ORDER BY u.id LIMIT 1";
            $approver_stmt = $conn->prepare($approver_query);
            $approver_stmt->bind_param("s", $training_data['departemen']);
            $approver_stmt->execute();
            $approver_result = $approver_stmt->get_result();
            
            if ($approver_result->num_rows > 0) {
                $approver = $approver_result->fetch_assoc();
                echo "   ✅ Found correct dept head: {$approver['name']}\n";
                
                if ($approver['name'] == $expected_dept_head['name']) {
                    echo "   ✅ PASS: Correct dept head selected\n";
                } else {
                    echo "   ❌ FAIL: Wrong dept head selected\n";
                }
            } else {
                echo "   ❌ No dept head found for department: {$training_data['departemen']}\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ Error in logic test: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ⚠️  No dept head found for department: $dept\n";
    }
    
    echo "\n";
}

// Restore training 146 to HRGA
$restore_query = "UPDATE training_submissions SET departemen = 'HRGA' WHERE id = 146";
$conn->query($restore_query);

echo "🏁 Correct notification logic test completed!\n";
echo "📋 Check the error logs for detailed email sending information\n";

$conn->close();
?>
