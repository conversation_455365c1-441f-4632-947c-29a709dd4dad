<?php
/**
 * <PERSON><PERSON> untuk mengelola peserta training
 */

include '../config/config.php';
include 'security.php';

// Get parameters
$training_id = $_GET['id'] ?? '';
$training_type = $_GET['type'] ?? '';

if (empty($training_id) || empty($training_type)) {
    header('Location: calendar_management.php');
    exit;
}

// Get training details
$training_detail = null;
if ($training_type === 'offline') {
    $query = "SELECT training_topic as title, start_date as date, location FROM offline_training WHERE id = ?";
} else {
    $query = "SELECT training_topic as title, start_date as date,
              COALESCE(training_place, 'Online') as location FROM training_submissions WHERE id = ?";
}

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $training_id);
$stmt->execute();
$result = $stmt->get_result();
if ($result->num_rows > 0) {
    $training_detail = $result->fetch_assoc();
}
$stmt->close();

if (!$training_detail) {
    header('Location: calendar_management.php');
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_participant'])) {
        // Add new participant
        $user_id = $_POST['user_id'] ?? '';
        $nik = $_POST['nik'] ?? '';
        $nama = $_POST['nama'] ?? '';

        if ($training_type === 'offline') {
            // Add to training_attendance
            $insert_query = "INSERT INTO training_attendance (training_id, karyawan_id, nik, nama, status)
                           VALUES (?, 0, ?, ?, 'tidak hadir')";
            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("iss", $training_id, $nik, $nama);
            $stmt->execute();
            $stmt->close();
        } else {
            // TODO: Add participant to online training
            // Disabled for now due to table structure uncertainty
            echo "<script>alert('Fitur tambah peserta untuk Training Eksternal sedang dalam pengembangan.');</script>";
        }
    } elseif (isset($_POST['remove_participant'])) {
        // Remove participant
        $participant_id = $_POST['participant_id'] ?? '';

        if ($training_type === 'offline') {
            $delete_query = "DELETE FROM training_attendance WHERE id = ? AND training_id = ?";
            $stmt = $conn->prepare($delete_query);
            $stmt->bind_param("ii", $participant_id, $training_id);
            $stmt->execute();
            $stmt->close();
        } else {
            // TODO: Remove participant from online training
            // Disabled for now due to table structure uncertainty
            echo "<script>alert('Fitur hapus peserta untuk Training Eksternal sedang dalam pengembangan.');</script>";
        }
    }
}

// Get current participants
$participants = [];
if ($training_type === 'offline') {
    $query = "SELECT ta.id, ta.nama as name, ta.nik, COALESCE(u.dept, 'N/A') as dept,
              COALESCE(u.jabatan, 'N/A') as jabatan, ta.status
              FROM training_attendance ta
              LEFT JOIN users u ON ta.nik = u.nik
              WHERE ta.training_id = ?
              ORDER BY ta.nama";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $training_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $participants[] = $row;
    }
    $stmt->close();
} else {
    // For online training, get submitter from training_submissions
    $query = "SELECT
                ts.id,
                COALESCE(u.name, 'Unknown User') as name,
                COALESCE(u.nik, 'N/A') as nik,
                COALESCE(u.dept, 'N/A') as dept,
                COALESCE(u.jabatan, 'N/A') as jabatan,
                'Pemohon' as status
              FROM training_submissions ts
              LEFT JOIN users u ON ts.user_id = u.id
              WHERE ts.id = ?";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $training_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $participants[] = $row;
    }
    $stmt->close();

    // TODO: Add additional participants from training_participants table once structure is confirmed
}

// Get available users for adding
$available_users = [];
$users_query = "SELECT id, name, nik, dept, jabatan FROM users WHERE is_active = 1 ORDER BY name";
$result = $conn->query($users_query);
while ($row = $result->fetch_assoc()) {
    $available_users[] = $row;
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Peserta Training</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #9d0000;
            --primary-dark: #7f0000;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
        }

        .table th {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-users me-2"></i>
                Kelola Peserta Training
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="calendar_management.php">
                    <i class="fas fa-arrow-left me-1"></i>Kembali ke Kalender
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Training Info -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Informasi Training
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Judul:</strong> <?= htmlspecialchars($training_detail['title']) ?>
                            </div>
                            <div class="col-md-4">
                                <strong>Tanggal:</strong> <?= date('d M Y', strtotime($training_detail['date'])) ?>
                            </div>
                            <div class="col-md-4">
                                <strong>Lokasi:</strong> <?= htmlspecialchars($training_detail['location']) ?>
                            </div>
                        </div>
                        <div class="mt-2">
                            <span class="badge bg-<?= $training_type === 'offline' ? 'success' : 'info' ?>">
                                <?= $training_type === 'offline' ? 'Training Internal' : 'Training Eksternal' ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Participant -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            Tambah Peserta
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <?php if ($training_type === 'offline'): ?>
                                    <div class="col-md-6">
                                        <label for="nik" class="form-label">NIK</label>
                                        <input type="text" class="form-control" id="nik" name="nik" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="nama" class="form-label">Nama</label>
                                        <input type="text" class="form-control" id="nama" name="nama" required>
                                    </div>
                                <?php else: ?>
                                    <div class="col-md-12">
                                        <label for="user_id" class="form-label">Pilih User</label>
                                        <select class="form-control" id="user_id" name="user_id" required>
                                            <option value="">-- Pilih User --</option>
                                            <?php foreach ($available_users as $user): ?>
                                                <option value="<?= $user['id'] ?>">
                                                    <?= htmlspecialchars($user['name']) ?> (<?= htmlspecialchars($user['nik']) ?>) - <?= htmlspecialchars($user['dept']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="mt-3">
                                <button type="submit" name="add_participant" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>Tambah Peserta
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Participants List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            Daftar Peserta (<?= count($participants) ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($participants)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <p>Belum ada peserta terdaftar</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Nama</th>
                                            <th>NIK</th>
                                            <th>Departemen</th>
                                            <th>Jabatan</th>
                                            <th>Status</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($participants as $participant): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($participant['name']) ?></td>
                                                <td><?= htmlspecialchars($participant['nik']) ?></td>
                                                <td><?= htmlspecialchars($participant['dept']) ?></td>
                                                <td><?= htmlspecialchars($participant['jabatan']) ?></td>
                                                <td>
                                                    <span class="badge bg-<?= $participant['status'] === 'hadir' ? 'success' : ($participant['status'] === 'active' ? 'primary' : 'secondary') ?>">
                                                        <?= htmlspecialchars($participant['status']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="participant_id" value="<?= $participant['id'] ?>">
                                                        <button type="submit" name="remove_participant" class="btn btn-danger btn-sm"
                                                                onclick="return confirm('Hapus peserta ini?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
$conn->close();
?>
