<?php
/**
 * Remove Participant Page for Admin
 * This page handles the removal of a participant from a training class
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if participant ID and class ID are provided
$participant_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;

if ($participant_id <= 0 || $class_id <= 0) {
    $_SESSION['error_message'] = "ID peserta atau ID kelas tidak valid.";
    header('Location: manage_classes.php');
    exit();
}

// Verify that the participant belongs to the specified class
$verify_query = "SELECT p.*, u.name as participant_name 
                FROM training_participants p
                JOIN users u ON p.user_id = u.id
                WHERE p.id = ? AND p.class_id = ?";
$stmt = $conn->prepare($verify_query);
$stmt->bind_param("ii", $participant_id, $class_id);
$stmt->execute();
$result = $stmt->get_result();
$participant = $result->fetch_assoc();

if (!$participant) {
    // Participant doesn't exist or doesn't belong to the specified class
    $_SESSION['error_message'] = "Peserta tidak ditemukan atau tidak terkait dengan kelas yang ditentukan.";
    header('Location: manage_class.php?id=' . $class_id);
    exit();
}
$stmt->close();

try {
    // Start transaction
    $conn->begin_transaction();

    // Delete the participant
    $delete_query = "DELETE FROM training_participants WHERE id = ?";
    $stmt = $conn->prepare($delete_query);
    $stmt->bind_param("i", $participant_id);
    $stmt->execute();
    $stmt->close();

    // Commit transaction
    $conn->commit();

    // Set success message
    $_SESSION['success_message'] = "Peserta " . htmlspecialchars($participant['participant_name']) . " berhasil dihapus dari kelas.";
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();

    // Set error message
    $_SESSION['error_message'] = "Gagal menghapus peserta: " . $e->getMessage();
}

// Redirect back to manage class page
header('Location: manage_class.php?id=' . $class_id);
exit();
?>
