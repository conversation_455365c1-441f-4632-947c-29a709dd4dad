<?php
/**
 * Edit Quiz Page for Admin/Trainer
 * This page allows admins/trainers to edit quizzes and add questions
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role or is instructor/assistant for this class
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];
$is_admin = $_SESSION['role_id'] == 99;

// Check if quiz ID is provided
$quiz_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($quiz_id <= 0) {
    header('Location: manage_classes.php');
    exit();
}

// Get quiz information
$quiz_query = "SELECT q.*, c.title as class_title, c.id as class_id, t.training_topic
              FROM training_quizzes q
              JOIN training_classes c ON q.class_id = c.id
              JOIN training_submissions t ON c.training_id = t.id
              WHERE q.id = ?";
$stmt = $conn->prepare($quiz_query);
$stmt->bind_param("i", $quiz_id);
$stmt->execute();
$result = $stmt->get_result();
$quiz = $result->fetch_assoc();
$stmt->close();

if (!$quiz) {
    header('Location: manage_classes.php');
    exit();
}

// If not admin, check if user is instructor or assistant for this class
if (!$is_admin) {
    $access_check = "SELECT role FROM training_participants
                    WHERE class_id = ? AND user_id = ? AND role IN ('instructor', 'assistant') AND status = 'active'";
    $stmt = $conn->prepare($access_check);
    $stmt->bind_param("ii", $quiz['class_id'], $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $access_row = $result->fetch_assoc();
    $stmt->close();

    if (!$access_row) {
        header('Location: ../view/login.php');
        exit();
    }
}

// Handle quiz update
$success_message = '';
$error_message = '';

// Check for session messages
if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

if (isset($_SESSION['error_message'])) {
    $error_message = $_SESSION['error_message'];
    unset($_SESSION['error_message']);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_quiz'])) {
    // Get form data
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $instructions = trim($_POST['instructions']);
    $time_limit = !empty($_POST['time_limit']) ? intval($_POST['time_limit']) : null;
    $passing_score = !empty($_POST['passing_score']) ? intval($_POST['passing_score']) : null;
    $is_published = isset($_POST['is_published']) ? 1 : 0;
    $allow_multiple_attempts = isset($_POST['allow_multiple_attempts']) ? 1 : 0;
    $max_attempts = !empty($_POST['max_attempts']) ? intval($_POST['max_attempts']) : null;
    $randomize_questions = isset($_POST['randomize_questions']) ? 1 : 0;

    // Process answer display options
    $answer_display_option = $_POST['answer_display_option'] ?? 'no_display';
    $show_correct_answers = ($answer_display_option === 'show_correct') ? 1 : 0;
    $hide_all_answers = ($answer_display_option === 'hide_all') ? 1 : 0;

    // Validate input
    if (empty($title)) {
        $error_message = "Judul kuis harus diisi.";
    } else {
        // Update quiz
        $update_query = "UPDATE training_quizzes SET
                        title = ?, description = ?, instructions = ?, time_limit = ?,
                        passing_score = ?, is_published = ?, allow_multiple_attempts = ?,
                        max_attempts = ?, randomize_questions = ?, show_correct_answers = ?,
                        hide_all_answers = ?
                        WHERE id = ?";

        $stmt = $conn->prepare($update_query);
        $stmt->bind_param(
            "sssiiiiiiiii",
            $title, $description, $instructions, $time_limit,
            $passing_score, $is_published, $allow_multiple_attempts,
            $max_attempts, $randomize_questions, $show_correct_answers,
            $hide_all_answers, $quiz_id
        );

        if ($stmt->execute()) {
            $success_message = "Kuis berhasil diperbarui.";

            // Refresh quiz data
            $stmt->close();
            $stmt = $conn->prepare($quiz_query);
            $stmt->bind_param("i", $quiz_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $quiz = $result->fetch_assoc();
        } else {
            $error_message = "Gagal memperbarui kuis: " . $conn->error;
        }
        $stmt->close();
    }
}

// Handle add question
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_question'])) {
    $question_text = trim($_POST['question_text']);
    $question_type = $_POST['question_type'];
    $points = intval($_POST['points']);

    // Validate input
    if (empty($question_text)) {
        $error_message = "Teks pertanyaan harus diisi.";
    } else {
        // Get the highest order number
        $order_query = "SELECT MAX(order_number) as max_order FROM training_questions WHERE quiz_id = ?";
        $stmt = $conn->prepare($order_query);
        $stmt->bind_param("i", $quiz_id);
        $stmt->execute();
        $order_result = $stmt->get_result();
        $order_row = $order_result->fetch_assoc();
        $order_number = ($order_row['max_order'] !== null) ? $order_row['max_order'] + 1 : 0;
        $stmt->close();

        // Insert question
        $insert_query = "INSERT INTO training_questions (
                        quiz_id, question_text, question_type, points, order_number
                    ) VALUES (?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param("issii", $quiz_id, $question_text, $question_type, $points, $order_number);

        if ($stmt->execute()) {
            $question_id = $stmt->insert_id;
            $success_message = "Pertanyaan berhasil ditambahkan.";

            // If multiple choice or true/false, redirect to add options
            if ($question_type == 'multiple_choice' || $question_type == 'true_false') {
                header("Location: edit_question.php?id=" . $question_id);
                exit();
            }
        } else {
            $error_message = "Gagal menambahkan pertanyaan: " . $conn->error;
        }
        $stmt->close();
    }
}

// Get questions for this quiz
$questions = [];
$questions_query = "SELECT * FROM training_questions WHERE quiz_id = ? ORDER BY order_number ASC";
$stmt = $conn->prepare($questions_query);
$stmt->bind_param("i", $quiz_id);
$stmt->execute();
$questions_result = $stmt->get_result();

while ($row = $questions_result->fetch_assoc()) {
    // Get options for multiple choice questions
    if ($row['question_type'] == 'multiple_choice' || $row['question_type'] == 'true_false') {
        $options_query = "SELECT * FROM training_question_options WHERE question_id = ? ORDER BY order_number ASC";
        $options_stmt = $conn->prepare($options_query);
        $options_stmt->bind_param("i", $row['id']);
        $options_stmt->execute();
        $options_result = $options_stmt->get_result();

        $options = [];
        while ($option = $options_result->fetch_assoc()) {
            $options[] = $option;
        }
        $options_stmt->close();

        $row['options'] = $options;
    }

    $questions[] = $row;
}
$stmt->close();
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .form-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .form-section h3 {
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .quiz-options {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }

    .max-attempts-container {
        padding: 10px;
        margin-top: 10px;
        background-color: #f8f9fa;
        border-radius: 5px;
        border-left: 3px solid #6c757d;
    }

    .show-answers-options {
        padding: 10px;
        margin-top: 10px;
        background-color: #f8f9fa;
        border-radius: 5px;
        border-left: 3px solid #6c757d;
    }

    .question-card {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        position: relative;
    }

    .question-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .question-type {
        font-size: 0.85rem;
        color: #6c757d;
        background-color: #e9ecef;
        padding: 2px 8px;
        border-radius: 4px;
    }

    .question-points {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .question-text {
        font-weight: 600;
        margin-bottom: 10px;
    }

    .question-options {
        margin-left: 20px;
    }

    .question-option {
        margin-bottom: 5px;
    }

    .correct-option {
        color: #155724;
        font-weight: 600;
    }

    .question-actions {
        margin-top: 10px;
        display: flex;
        gap: 10px;
    }

    .add-question-form {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="welcome-section">
                <h1><i class="fas fa-edit"></i> Edit Kuis</h1>
                <p class="text-white">Edit kuis dan tambahkan pertanyaan</p>
                <a href="manage_class.php?id=<?= $quiz['class_id'] ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Kelas
                </a>
            </div>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="form-section">
                <h3>Informasi Kuis</h3>

                <form method="post" action="">
                    <div class="mb-3">
                        <label class="form-label">Kelas Training</label>
                        <div class="form-control bg-light"><?= htmlspecialchars($quiz['class_title']) ?> - <?= htmlspecialchars($quiz['training_topic']) ?></div>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">Judul Kuis</label>
                        <input type="text" class="form-control" id="title" name="title" value="<?= htmlspecialchars($quiz['title']) ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Deskripsi Kuis</label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?= htmlspecialchars($quiz['description']) ?></textarea>
                        <div class="form-text">Deskripsi singkat tentang kuis ini</div>
                    </div>

                    <div class="mb-3">
                        <label for="instructions" class="form-label">Instruksi Pengerjaan</label>
                        <textarea class="form-control" id="instructions" name="instructions" rows="3"><?= htmlspecialchars($quiz['instructions']) ?></textarea>
                        <div class="form-text">Instruksi untuk peserta tentang cara mengerjakan kuis</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="time_limit" class="form-label">Batas Waktu (menit)</label>
                                <input type="number" class="form-control" id="time_limit" name="time_limit" min="0" value="<?= $quiz['time_limit'] ?>">
                                <div class="form-text">Biarkan kosong jika tidak ada batas waktu</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="passing_score" class="form-label">Nilai Kelulusan (%)</label>
                                <input type="number" class="form-control" id="passing_score" name="passing_score" min="0" max="100" value="<?= $quiz['passing_score'] ?>">
                                <div class="form-text">Nilai minimum untuk lulus kuis (dalam persen)</div>
                            </div>
                        </div>
                    </div>

                    <div class="quiz-options">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_published" name="is_published" <?= $quiz['is_published'] ? 'checked' : '' ?>>
                            <label class="form-check-label" for="is_published">
                                Publikasikan Kuis
                            </label>
                            <div class="form-text">Jika dicentang, kuis akan langsung terlihat oleh peserta</div>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="allow_multiple_attempts" name="allow_multiple_attempts" <?= $quiz['allow_multiple_attempts'] ? 'checked' : '' ?>>
                            <label class="form-check-label" for="allow_multiple_attempts">
                                Izinkan Percobaan Berulang
                            </label>
                            <div class="form-text">Peserta dapat mengerjakan kuis lebih dari sekali</div>
                        </div>

                        <div class="form-group max-attempts-container" style="<?= $quiz['allow_multiple_attempts'] ? '' : 'display: none;' ?>">
                            <label for="max_attempts" class="form-label">Jumlah Percobaan Maksimal</label>
                            <input type="number" class="form-control" id="max_attempts" name="max_attempts" min="0" value="<?= $quiz['max_attempts'] ?>">
                            <div class="form-text">Biarkan kosong jika tidak ada batasan jumlah percobaan</div>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="randomize_questions" name="randomize_questions" <?= $quiz['randomize_questions'] ? 'checked' : '' ?>>
                            <label class="form-check-label" for="randomize_questions">
                                Acak Urutan Pertanyaan
                            </label>
                            <div class="form-text">Urutan pertanyaan akan diacak untuk setiap peserta</div>
                        </div>

                    </div>
                    <div class="mb-3">
                        <label class="form-label">Pengaturan Tampilan Jawaban</label>
                        <div class="form-text mb-2">Pilih salah satu opsi di bawah ini untuk mengatur tampilan jawaban benar/salah</div>

                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="answer_display_option" id="option_hide_all" value="hide_all" <?= $quiz['hide_all_answers'] ? 'checked' : '' ?>>
                            <label class="form-check-label" for="option_hide_all">
                                <strong>Sembunyikan Semua Jawaban Benar/Salah</strong>
                            </label>
                            <div class="form-text">Peserta tidak dapat melihat jawaban benar/salah sama sekali (untuk mencegah mengingat jawaban)</div>
                        </div>

                        <div class="form-check mt-2">
                            <input class="form-check-input" type="radio" name="answer_display_option" id="option_show_correct" value="show_correct" <?= (!$quiz['hide_all_answers'] && $quiz['show_correct_answers']) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="option_show_correct">
                                <strong>Tampilkan Jawaban Benar</strong>
                            </label>
                            <div class="form-text">Peserta dapat melihat jawaban yang benar setelah selesai</div>
                        </div>

                        <div class="form-check mt-2">
                            <input class="form-check-input" type="radio" name="answer_display_option" id="option_no_display" value="no_display" <?= (!$quiz['hide_all_answers'] && !$quiz['show_correct_answers']) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="option_no_display">
                                <strong>Tidak Menampilkan Jawaban Benar</strong>
                            </label>
                            <div class="form-text">Peserta hanya melihat jawaban mereka sendiri tanpa indikasi benar/salah</div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" name="update_quiz" class="btn btn-primary">
                                <i class="fas fa-save"></i> Perbarui Kuis
                            </button>

                            <a href="quiz_results.php?id=<?= $quiz_id ?>" class="btn btn-info">
                                <i class="fas fa-chart-bar"></i> Lihat Hasil Kuis
                            </a>

                            <a href="import_quiz_questions.php?quiz_id=<?= $quiz_id ?>" class="btn btn-success">
                                <i class="fas fa-file-import"></i> Import Pertanyaan dari Excel
                            </a>
                        </div>

                        <a href="manage_class.php?id=<?= $quiz['class_id'] ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali ke Kelas
                        </a>
                    </div>
                </form>
            </div>

            <div class="form-section">
                <h3>Daftar Pertanyaan</h3>

                <?php if (empty($questions)): ?>
                    <div class="alert alert-info">
                        Belum ada pertanyaan untuk kuis ini. Tambahkan pertanyaan menggunakan form di bawah.
                    </div>
                <?php else: ?>
                    <?php foreach ($questions as $index => $question): ?>
                        <div class="question-card">
                            <div class="question-header">
                                <div>
                                    <span class="question-type">
                                        <?php
                                        $question_type_labels = [
                                            'multiple_choice' => 'Pilihan Ganda',
                                            'true_false' => 'Benar/Salah',
                                            'short_answer' => 'Jawaban Singkat',
                                            'essay' => 'Esai'
                                        ];
                                        echo $question_type_labels[$question['question_type']] ?? $question['question_type'];
                                        ?>
                                    </span>
                                    <span class="question-points ms-2"><?= $question['points'] ?> poin</span>
                                </div>
                                <div>
                                    <span class="badge bg-secondary">Pertanyaan #<?= $index + 1 ?></span>
                                </div>
                            </div>

                            <div class="question-text"><?= htmlspecialchars($question['question_text']) ?></div>

                            <?php if (isset($question['options']) && !empty($question['options'])): ?>
                                <div class="question-options">
                                    <?php foreach ($question['options'] as $option): ?>
                                        <div class="question-option <?= $option['is_correct'] ? 'correct-option' : '' ?>">
                                            <?php if ($option['is_correct']): ?>
                                                <i class="fas fa-check-circle text-success"></i>
                                            <?php else: ?>
                                                <i class="fas fa-circle"></i>
                                            <?php endif; ?>
                                            <?= htmlspecialchars($option['option_text']) ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                            <div class="question-actions">
                                <a href="edit_question.php?id=<?= $question['id'] ?>" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a href="javascript:void(0);" class="btn btn-sm btn-danger delete-question-btn" data-question-id="<?= $question['id'] ?>" data-quiz-id="<?= $quiz_id ?>">
                                    <i class="fas fa-trash"></i> Hapus
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>

                <div class="add-question-form">
                    <h4>Tambah Pertanyaan Baru</h4>

                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="question_text" class="form-label">Teks Pertanyaan</label>
                            <textarea class="form-control" id="question_text" name="question_text" rows="3" required></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="question_type" class="form-label">Jenis Pertanyaan</label>
                                    <select class="form-select" id="question_type" name="question_type" required>
                                        <option value="multiple_choice">Pilihan Ganda</option>
                                        <option value="true_false">Benar/Salah</option>
                                        <option value="short_answer">Jawaban Singkat</option>
                                        <option value="essay">Esai</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="points" class="form-label">Poin</label>
                                    <input type="number" class="form-control" id="points" name="points" min="1" value="1" required>
                                </div>
                            </div>
                        </div>

                        <button type="submit" name="add_question" class="btn btn-success">
                            <i class="fas fa-plus"></i> Tambah Pertanyaan
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    // Add any JavaScript functionality here
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);

        // Handle allow_multiple_attempts checkbox
        const allowMultipleAttemptsCheckbox = document.getElementById('allow_multiple_attempts');
        const maxAttemptsContainer = document.querySelector('.max-attempts-container');

        allowMultipleAttemptsCheckbox.addEventListener('change', function() {
            maxAttemptsContainer.style.display = this.checked ? 'block' : 'none';

            // If unchecked, clear the max_attempts value
            if (!this.checked) {
                document.getElementById('max_attempts').value = '';
            }
        });

        // No additional JavaScript needed for radio buttons as they are mutually exclusive by default

        // Handle delete question buttons
        const deleteButtons = document.querySelectorAll('.delete-question-btn');
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const questionId = this.getAttribute('data-question-id');
                const quizId = this.getAttribute('data-quiz-id');

                // Use confirmAction for confirmation
                confirmAction(
                    'Apakah Anda yakin ingin menghapus pertanyaan ini?',
                    function() {
                        // Redirect to delete_question.php when confirmed
                        window.location.href = 'delete_question.php?id=' + questionId + '&quiz_id=' + quizId;
                    }
                );
            });
        });
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
