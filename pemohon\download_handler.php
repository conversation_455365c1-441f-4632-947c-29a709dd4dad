<?php
/**
 * Download Handler for Training Materials
 * This script handles secure file downloads with proper validation and error handling
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    die('Akses ditolak. Silakan login terlebih dahulu.');
}

// Check if user has the correct role (role_id = 1 for pemohon)
if (!isset($_SESSION['role_id']) || $_SESSION['role_id'] != 1) {
    die('Akses ditolak. Anda tidak memiliki izin yang diperlukan.');
}

// Get user information
$user_id = $_SESSION['user_id'];

// Validate required parameters
if (!isset($_GET['file']) || empty($_GET['file']) || !isset($_GET['class_id']) || empty($_GET['class_id'])) {
    die('Parameter tidak valid.');
}

$file_name = basename($_GET['file']);
$class_id = intval($_GET['class_id']);

// Validate class participation
$participant_query = "SELECT * FROM training_participants WHERE class_id = ? AND user_id = ?";
$stmt = $conn->prepare($participant_query);
$stmt->bind_param("ii", $class_id, $user_id);
$stmt->execute();
$participant_result = $stmt->get_result();
$is_participant = $participant_result->num_rows > 0;
$stmt->close();

if (!$is_participant) {
    die('Akses ditolak. Anda bukan peserta kelas ini.');
}

// Validate material exists in database
$material_query = "SELECT * FROM training_materials WHERE class_id = ? AND file_path LIKE ?";
$stmt = $conn->prepare($material_query);
$file_pattern = '%' . $file_name;
$stmt->bind_param("is", $class_id, $file_pattern);
$stmt->execute();
$material_result = $stmt->get_result();
$material = $material_result->fetch_assoc();
$stmt->close();

if (!$material || !$material['is_published']) {
    die('Materi tidak ditemukan atau belum dipublikasikan.');
}

// Construct the full file path
$file_path = '../uploads/materials/' . $file_name;

// Validate file exists
if (!file_exists($file_path)) {
    die('File tidak ditemukan.');
}

// Get file information
$file_info = pathinfo($file_path);
$file_size = filesize($file_path);

// Set appropriate headers for download
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . $file_info['basename'] . '"');
header('Content-Length: ' . $file_size);
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Clear output buffer
ob_clean();
flush();

// Read and output file in chunks
$chunk_size = 1024 * 1024; // 1MB chunks
$handle = fopen($file_path, 'rb');

while (!feof($handle)) {
    echo fread($handle, $chunk_size);
    ob_flush();
    flush();
}

fclose($handle);
exit();