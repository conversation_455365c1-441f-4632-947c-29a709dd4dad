<?php
/**
 * Delete Question Page for Admin
 * This page handles the deletion of a quiz question
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if question ID and quiz ID are provided
$question_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$quiz_id = isset($_GET['quiz_id']) ? intval($_GET['quiz_id']) : 0;

if ($question_id <= 0 || $quiz_id <= 0) {
    header('Location: manage_classes.php');
    exit();
}

// Verify that the question belongs to the specified quiz
$verify_query = "SELECT id FROM training_questions WHERE id = ? AND quiz_id = ?";
$stmt = $conn->prepare($verify_query);
$stmt->bind_param("ii", $question_id, $quiz_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    // Question doesn't exist or doesn't belong to the specified quiz
    $_SESSION['error_message'] = "Pertanyaan tidak ditemukan atau tidak terkait dengan kuis yang ditentukan.";
    header('Location: edit_quiz.php?id=' . $quiz_id);
    exit();
}
$stmt->close();

try {
    // Start transaction
    $conn->begin_transaction();

    // Delete options first (this should be handled by ON DELETE CASCADE, but we'll do it explicitly for safety)
    $delete_options_query = "DELETE FROM training_question_options WHERE question_id = ?";
    $stmt = $conn->prepare($delete_options_query);
    $stmt->bind_param("i", $question_id);
    $stmt->execute();
    $stmt->close();

    // Delete answers related to this question (this should be handled by ON DELETE CASCADE, but we'll do it explicitly for safety)
    $delete_answers_query = "DELETE FROM training_quiz_answers WHERE question_id = ?";
    $stmt = $conn->prepare($delete_answers_query);
    $stmt->bind_param("i", $question_id);
    $stmt->execute();
    $stmt->close();

    // Delete the question
    $delete_question_query = "DELETE FROM training_questions WHERE id = ?";
    $stmt = $conn->prepare($delete_question_query);
    $stmt->bind_param("i", $question_id);
    $stmt->execute();
    $stmt->close();

    // Commit transaction
    $conn->commit();

    // Set success message
    $_SESSION['success_message'] = "Pertanyaan berhasil dihapus.";
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    
    // Set error message
    $_SESSION['error_message'] = "Gagal menghapus pertanyaan: " . $e->getMessage();
}

// Redirect back to edit quiz page
header('Location: edit_quiz.php?id=' . $quiz_id);
exit();
?>
