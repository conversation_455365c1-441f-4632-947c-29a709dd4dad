<?php
/**
 * Unified Training Data Handler
 * Handles both external and internal training data for AJAX requests
 */

session_start();
include '../config/config.php';

// Check if user is logged in and has admin access
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Get parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$training_type = isset($_GET['training_type']) ? $_GET['training_type'] : 'external';

try {
    // Build query based on training type
    if ($training_type === 'internal') {
        $query = "SELECT ot.*, u.name as requester_name
                  FROM offline_training ot
                  LEFT JOIN users u ON ot.created_by = u.id
                  WHERE 1=1";
    } else {
        $query = "SELECT ts.*, u.name as requester_name
                  FROM training_submissions ts
                  LEFT JOIN users u ON ts.user_id = u.id
                  WHERE 1=1";
    }

    // Add search conditions based on training type
    if ($search) {
        if ($training_type === 'internal') {
            $search_escaped = mysqli_real_escape_string($conn, $search);
            $query .= " AND (ot.training_topic LIKE '%$search_escaped%'
                        OR ot.training_description LIKE '%$search_escaped%'
                        OR ot.trainer_name LIKE '%$search_escaped%'
                        OR ot.location LIKE '%$search_escaped%')";
        } else {
            $search_escaped = mysqli_real_escape_string($conn, $search);
            $query .= " AND (ts.full_name LIKE '%$search_escaped%'
                        OR ts.training_topic LIKE '%$search_escaped%'
                        OR ts.email LIKE '%$search_escaped%')";
        }
    }

    // Add status filter based on training type
    if ($status_filter) {
        $status_escaped = mysqli_real_escape_string($conn, $status_filter);
        if ($training_type === 'internal') {
            $query .= " AND ot.status = '$status_escaped'";
        } else {
            $query .= " AND ts.status = '$status_escaped'";
        }
    }

    // Add date filter based on training type
    if ($date_from && $date_to) {
        $date_from_escaped = mysqli_real_escape_string($conn, $date_from);
        $date_to_escaped = mysqli_real_escape_string($conn, $date_to);
        
        if ($training_type === 'internal') {
            $query .= " AND (
                (ot.start_date BETWEEN '$date_from_escaped' AND '$date_to_escaped') OR
                (ot.end_date BETWEEN '$date_from_escaped' AND '$date_to_escaped')
            )";
        } else {
            $query .= " AND (
                (ts.start_date BETWEEN '$date_from_escaped' AND '$date_to_escaped') OR
                (ts.end_date BETWEEN '$date_from_escaped' AND '$date_to_escaped')
            )";
        }
    }

    // Add ORDER BY based on training type
    if ($training_type === 'internal') {
        $query .= " ORDER BY ot.start_date DESC, ot.id DESC";
    } else {
        $query .= " ORDER BY ts.id DESC";
    }

    $result = mysqli_query($conn, $query);

    if (!$result) {
        throw new Exception('Database query failed: ' . mysqli_error($conn));
    }

    $data = [];
    while ($row = mysqli_fetch_assoc($result)) {
        if ($training_type === 'internal') {
            // Format internal training data
            $formatted_row = [
                'id' => $row['id'],
                'training_topic' => $row['training_topic'],
                'training_description' => $row['training_description'],
                'start_date' => $row['start_date'],
                'end_date' => $row['end_date'],
                'training_time_start' => $row['training_time_start'],
                'training_time_end' => $row['training_time_end'],
                'location' => $row['location'],
                'trainer_name' => $row['trainer_name'],
                'max_participants' => $row['max_participants'],
                'status' => $row['status'],
                'requester_name' => $row['requester_name'],
                'type' => 'internal'
            ];
        } else {
            // Format external training data
            $formatted_row = [
                'id' => $row['id'],
                'full_name' => $row['full_name'],
                'training_topic' => $row['training_topic'],
                'training_date' => $row['training_date'] ?? null,
                'training_date_fixed' => $row['training_date_fixed'] ?? null,
                'training_date_start' => $row['training_date_start'] ?? null,
                'training_date_end' => $row['training_date_end'] ?? null,
                'start_date' => $row['start_date'] ?? null,
                'end_date' => $row['end_date'] ?? null,
                'status' => $row['status'],
                'requester_name' => $row['requester_name'],
                'email' => $row['email'] ?? '',
                'type' => 'external'
            ];
        }
        $data[] = $formatted_row;
    }

    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($data);

} catch (Exception $e) {
    error_log("Error in get_filtered_training_unified.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
