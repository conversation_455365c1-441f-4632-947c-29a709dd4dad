<?php
/**
 * Enhanced Batch History System
 * This file provides enhanced history tracking for rollback operations
 */

require_once 'record_batch_employee_history.php';
require_once 'karyawan_schema_helper.php';

/**
 * Record enhanced batch history with before/after data
 */
if (!function_exists('recordEnhancedBatchHistory')) {
function recordEnhancedBatchHistory($pdo, $action_type, $niks, $mode, $before_data, $after_data, $success_count, $error_count, $skipped_count) {
    try {
        $user_id = $_SESSION['user_id'] ?? 1;
        
        // Enhanced batch data with before/after states
        $enhanced_batch_data = [
            'action_type' => $action_type,
            'mode' => $mode,
            'niks' => $niks,
            'before_data' => $before_data, // Employee data before changes
            'after_data' => $after_data,   // Employee data after changes
            'total_success' => $success_count,
            'total_error' => $error_count,
            'total_skipped' => $skipped_count,
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => $user_id,
            'rollback_capable' => true,
            'enhanced_history' => true
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO karyawan_batch_history 
            (action_type, batch_data, changed_by, change_timestamp, is_rollback_capable, rollback_status) 
            VALUES (?, ?, ?, NOW(), TRUE, 'AVAILABLE')
        ");
        
        $result = $stmt->execute([
            $action_type,
            json_encode($enhanced_batch_data),
            $user_id
        ]);
        
        if ($result) {
            return $pdo->lastInsertId();
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("Error recording enhanced batch history: " . $e->getMessage());
        return false;
    }
}
}

/**
 * Get employee data for NIKs (for before/after tracking)
 */
if (!function_exists('getEmployeeDataForNIKs')) {
function getEmployeeDataForNIKs($pdo, $niks) {
    if (empty($niks)) {
        return [];
    }
    
    try {
        // Use safe query builder
        $placeholders = str_repeat('?,', count($niks) - 1) . '?';
        $query = "SELECT * FROM karyawan WHERE nik IN ($placeholders)";
        
        $stmt = $pdo->prepare($query);
        $stmt->execute($niks);
        
        $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Index by NIK for easy lookup
        $indexed_data = [];
        foreach ($employees as $emp) {
            $indexed_data[$emp['nik']] = $emp;
        }
        
        return $indexed_data;
        
    } catch (Exception $e) {
        error_log("Error getting employee data: " . $e->getMessage());
        return [];
    }
}
}

/**
 * Perform BATCH_UPDATE rollback
 */
if (!function_exists('rollbackBatchUpdate')) {
function rollbackBatchUpdate($pdo, $batch_id) {
    try {
        // Get batch details
        $batch_details = getBatchHistoryDetails($batch_id);
        if (!$batch_details) {
            throw new Exception("Batch history not found");
        }
        
        $batch_data = $batch_details['batch_data'];
        $before_data = $batch_data['before_data'] ?? [];
        $niks = $batch_data['niks'] ?? [];
        
        if (empty($before_data)) {
            throw new Exception("No before data available for rollback");
        }
        
        $success_count = 0;
        $error_count = 0;
        $errors = [];
        
        $pdo->beginTransaction();
        
        // Get current data for after_data in rollback record
        $current_data = getEmployeeDataForNIKs($pdo, $niks);
        
        foreach ($niks as $nik) {
            if (!isset($before_data[$nik])) {
                $error_count++;
                $errors[] = "No before data for NIK: $nik";
                continue;
            }
            
            try {
                // Build UPDATE query to restore previous state
                $before_employee = $before_data[$nik];
                $update_fields = [];
                $update_values = [];
                
                foreach ($before_employee as $field => $value) {
                    if ($field !== 'id' && $field !== 'nik') { // Don't update ID or NIK
                        $update_fields[] = "$field = ?";
                        $update_values[] = $value;
                    }
                }
                
                if (!empty($update_fields)) {
                    $update_values[] = $nik; // For WHERE clause
                    
                    $update_query = "UPDATE karyawan SET " . implode(', ', $update_fields) . " WHERE nik = ?";
                    $update_stmt = $pdo->prepare($update_query);
                    
                    if ($update_stmt->execute($update_values)) {
                        $success_count++;
                    } else {
                        $error_count++;
                        $errors[] = "Failed to rollback NIK: $nik";
                    }
                }
                
            } catch (Exception $e) {
                $error_count++;
                $errors[] = "Error rolling back NIK $nik: " . $e->getMessage();
            }
        }
        
        // Record the rollback operation with enhanced history
        $rollback_batch_id = recordEnhancedBatchHistory(
            $pdo,
            'BATCH_ROLLBACK',
            $niks,
            'rollback',
            $current_data, // Before rollback (current state)
            $before_data,  // After rollback (restored state)
            $success_count,
            $error_count,
            0
        );
        
        // Mark original batch as rolled back
        if ($rollback_batch_id) {
            $update_original = $pdo->prepare("
                UPDATE karyawan_batch_history 
                SET action_type = CONCAT(action_type, '_ROLLED_BACK'), rollback_status = 'ROLLED_BACK' 
                WHERE batch_id = ?
            ");
            $update_original->execute([$batch_id]);
        }
        
        $pdo->commit();
        
        return [
            'success' => true,
            'batch_id' => $rollback_batch_id,
            'results' => [
                'success_count' => $success_count,
                'error_count' => $error_count,
                'skipped_count' => 0,
                'errors' => $errors
            ]
        ];
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}
}

/**
 * Perform BATCH_ROLLBACK rollback (undo rollback)
 */
if (!function_exists('rollbackBatchRollback')) {
function rollbackBatchRollback($pdo, $batch_id) {
    try {
        // Get rollback batch details
        $batch_details = getBatchHistoryDetails($batch_id);
        if (!$batch_details) {
            throw new Exception("Rollback batch history not found");
        }
        
        $batch_data = $batch_details['batch_data'];
        $before_data = $batch_data['before_data'] ?? []; // State before rollback
        $after_data = $batch_data['after_data'] ?? [];   // State after rollback
        $niks = $batch_data['niks'] ?? [];
        
        if (empty($before_data)) {
            throw new Exception("No before data available for undo rollback");
        }
        
        $success_count = 0;
        $error_count = 0;
        $errors = [];
        
        $pdo->beginTransaction();
        
        // Get current data
        $current_data = getEmployeeDataForNIKs($pdo, $niks);
        
        // Restore to state before rollback (undo the rollback)
        foreach ($niks as $nik) {
            if (!isset($before_data[$nik])) {
                $error_count++;
                $errors[] = "No before data for NIK: $nik";
                continue;
            }
            
            try {
                // Restore to state before rollback
                $restore_employee = $before_data[$nik];
                $update_fields = [];
                $update_values = [];
                
                foreach ($restore_employee as $field => $value) {
                    if ($field !== 'id' && $field !== 'nik') {
                        $update_fields[] = "$field = ?";
                        $update_values[] = $value;
                    }
                }
                
                if (!empty($update_fields)) {
                    $update_values[] = $nik;
                    
                    $update_query = "UPDATE karyawan SET " . implode(', ', $update_fields) . " WHERE nik = ?";
                    $update_stmt = $pdo->prepare($update_query);
                    
                    if ($update_stmt->execute($update_values)) {
                        $success_count++;
                    } else {
                        $error_count++;
                        $errors[] = "Failed to undo rollback for NIK: $nik";
                    }
                }
                
            } catch (Exception $e) {
                $error_count++;
                $errors[] = "Error undoing rollback for NIK $nik: " . $e->getMessage();
            }
        }
        
        // Record the undo rollback operation
        $undo_batch_id = recordEnhancedBatchHistory(
            $pdo,
            'BATCH_UNDO_ROLLBACK',
            $niks,
            'undo_rollback',
            $current_data,  // Before undo (current state)
            $before_data,   // After undo (restored state)
            $success_count,
            $error_count,
            0
        );
        
        // Mark rollback batch as rolled back
        if ($undo_batch_id) {
            $update_rollback = $pdo->prepare("
                UPDATE karyawan_batch_history 
                SET action_type = CONCAT(action_type, '_ROLLED_BACK'), rollback_status = 'ROLLED_BACK' 
                WHERE batch_id = ?
            ");
            $update_rollback->execute([$batch_id]);
            
            // Find and restore original batch status
            $find_original = $pdo->prepare("
                SELECT batch_id FROM karyawan_batch_history 
                WHERE JSON_EXTRACT(batch_data, '$.niks') = ? 
                AND action_type LIKE '%_ROLLED_BACK' 
                AND batch_id != ? 
                ORDER BY change_timestamp DESC 
                LIMIT 1
            ");
            $find_original->execute([json_encode($niks), $batch_id]);
            $original = $find_original->fetch(PDO::FETCH_ASSOC);
            
            if ($original) {
                $restore_original = $pdo->prepare("
                    UPDATE karyawan_batch_history 
                    SET action_type = REPLACE(action_type, '_ROLLED_BACK', ''), rollback_status = 'AVAILABLE' 
                    WHERE batch_id = ?
                ");
                $restore_original->execute([$original['batch_id']]);
            }
        }
        
        $pdo->commit();
        
        return [
            'success' => true,
            'batch_id' => $undo_batch_id,
            'results' => [
                'success_count' => $success_count,
                'error_count' => $error_count,
                'skipped_count' => 0,
                'errors' => $errors
            ]
        ];
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}
}

/**
 * Check if batch supports enhanced rollback
 */
if (!function_exists('supportsEnhancedRollback')) {
function supportsEnhancedRollback($batch_data, $action_type) {
    // Extract base action type (remove _ROLLED_BACK suffix if present)
    $base_action_type = str_replace('_ROLLED_BACK', '', $action_type);

    // Check if batch has enhanced history data
    if (isset($batch_data['enhanced_history']) && $batch_data['enhanced_history'] === true) {
        return true;
    }

    // Check if batch has before_data for rollback
    if (isset($batch_data['before_data']) && !empty($batch_data['before_data'])) {
        return true;
    }

    // BATCH_ROLLBACK can be rolled back if it has proper data
    if ($action_type === 'BATCH_ROLLBACK' && isset($batch_data['before_data'])) {
        return true;
    }

    // BATCH_UPDATE_ROLLED_BACK can be rolled back again if it has enhanced data
    if ($action_type === 'BATCH_UPDATE_ROLLED_BACK' && isset($batch_data['before_data'])) {
        return true;
    }

    return false;
}
}

/**
 * Perform rollback on already rolled back batch (rollback again)
 */
if (!function_exists('rollbackRolledBackBatch')) {
function rollbackRolledBackBatch($pdo, $batch_id) {
    try {
        // Get batch details
        $batch_details = getBatchHistoryDetails($batch_id);
        if (!$batch_details) {
            throw new Exception("Batch history not found");
        }

        $batch_data = $batch_details['batch_data'];
        $action_type = $batch_details['action_type'];

        // For BATCH_UPDATE_ROLLED_BACK, we want to rollback to the state before the original update
        // This means using the 'before_data' from the original batch
        $before_data = $batch_data['before_data'] ?? [];
        $niks = $batch_data['niks'] ?? [];

        if (empty($before_data)) {
            throw new Exception("No before data available for rollback again");
        }

        $success_count = 0;
        $error_count = 0;
        $errors = [];

        $pdo->beginTransaction();

        // Get current data for after_data in rollback record
        $current_data = getEmployeeDataForNIKs($pdo, $niks);

        foreach ($niks as $nik) {
            if (!isset($before_data[$nik])) {
                $error_count++;
                $errors[] = "No before data for NIK: $nik";
                continue;
            }

            try {
                // Build UPDATE query to restore to original state (before first update)
                $before_employee = $before_data[$nik];
                $update_fields = [];
                $update_values = [];

                foreach ($before_employee as $field => $value) {
                    if ($field !== 'id' && $field !== 'nik') { // Don't update ID or NIK
                        $update_fields[] = "$field = ?";
                        $update_values[] = $value;
                    }
                }

                if (!empty($update_fields)) {
                    $update_values[] = $nik; // For WHERE clause

                    $update_query = "UPDATE karyawan SET " . implode(', ', $update_fields) . " WHERE nik = ?";
                    $update_stmt = $pdo->prepare($update_query);

                    if ($update_stmt->execute($update_values)) {
                        $success_count++;
                    } else {
                        $error_count++;
                        $errors[] = "Failed to rollback NIK: $nik";
                    }
                }

            } catch (Exception $e) {
                $error_count++;
                $errors[] = "Error rolling back NIK $nik: " . $e->getMessage();
            }
        }

        // Record the rollback operation with enhanced history
        $rollback_batch_id = recordEnhancedBatchHistory(
            $pdo,
            'BATCH_ROLLBACK_AGAIN',
            $niks,
            'rollback_again',
            $current_data, // Before rollback (current state)
            $before_data,  // After rollback (restored to original state)
            $success_count,
            $error_count,
            0
        );

        // Mark the rolled back batch as rolled back again
        if ($rollback_batch_id) {
            $update_original = $pdo->prepare("
                UPDATE karyawan_batch_history
                SET action_type = CONCAT(action_type, '_AGAIN'), rollback_status = 'ROLLED_BACK'
                WHERE batch_id = ?
            ");
            $update_original->execute([$batch_id]);
        }

        $pdo->commit();

        return [
            'success' => true,
            'batch_id' => $rollback_batch_id,
            'results' => [
                'success_count' => $success_count,
                'error_count' => $error_count,
                'skipped_count' => 0,
                'errors' => $errors
            ]
        ];

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}
}
