/**
 * Universal Calendar CSS untuk semua role
 * Modern, responsive, dan user-friendly design
 */

/* Calendar Container */
.calendar-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 20px 0;
}

/* Calendar Header */
.calendar-header {
    background: linear-gradient(135deg, #da0000 0%, #a50000 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.calendar-nav {
    display: flex;
    align-items: center;
    gap: 15px;
}

.calendar-nav button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 16px;
}

.calendar-nav button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.calendar-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

#todayBtn {
    background: rgba(255, 255, 255, 0.2)!important;
    border: none;
    color: white!important  ;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

#todayBtn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Calendar Grid */
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e9ecef;
    padding: 0;
}

/* Day Headers */
.calendar-day-header {
    background: #f8f9fa;
    padding: 15px 5px;
    text-align: center;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Calendar Days */
.calendar-day {
    background: white;
    min-height: 120px;
    padding: 8px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
}

.calendar-day:hover {
    background: #f8f9fa;
}

.calendar-day.today {
    background: linear-gradient(135deg, #e1d4d7 0%, #e1d4d7 100%);
    color: black;
}

.calendar-day.today .calendar-day-number {
    color: black;
    font-weight: bold;
}

.calendar-day.other-month {
    background: #f8f9fa;
    color: #adb5bd;
}

.calendar-day-number {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 5px;
    color: #495057;
}

/* Calendar Events - Dynamic Colors */
.calendar-event {
    padding: 4px 8px;
    margin: 2px 0;
    border-radius: 4px;
    font-size: 11px;
    line-height: 1.2;
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
    font-weight: 500;
    /* Colors will be set dynamically by JavaScript */
}

.calendar-event:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2) !important;
    z-index: 10;
}

/* Hidden events */
.calendar-event.event-hidden {
    opacity: 0.6;
    border-style: dashed !important;
}

.calendar-event.event-hidden::after {
    content: "🔒";
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 8px;
}

/* Unconfirmed events */
.calendar-event[data-confirmed="false"] {
    border-style: dotted !important;
    opacity: 0.8;
}

.calendar-event[data-confirmed="false"]::before {
    content: "⚠️";
    position: absolute;
    left: 2px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 8px;
}

/* Multi-day Events - Enhanced Admin Style */
.calendar-event.multi-start {
    border-radius: 4px 0 0 4px;
    border-right: none;
    position: relative;
    padding-left: 16px;
}

.calendar-event.multi-middle {
    border-radius: 0;
    border-left: none;
    border-right: none;
    position: relative;
}

.calendar-event.multi-end {
    border-radius: 0 4px 4px 0;
    border-left: none;
    position: relative;
    padding-right: 16px;
}

/* Multi-day event connectors */
.calendar-event.multi-start::after {
    content: "";
    position: absolute;
    right: -1px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: inherit;
    z-index: 1;
}

.calendar-event.multi-end::before {
    content: "";
    position: absolute;
    left: -1px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: inherit;
    z-index: 1;
}

/* Hover effects for multi-day events */
.calendar-event.multi-start:hover,
.calendar-event.multi-middle:hover,
.calendar-event.multi-end:hover {
    z-index: 20;
    box-shadow: 0 3px 8px rgba(0,0,0,0.3) !important;
}

/* Training Highlighted */
.calendar-day.training-highlighted {
    animation: highlightPulse 2s ease-in-out;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
}

@keyframes highlightPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Legend */
.legend {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #495057;
}

.legend-color {
    width: 20px;
    height: 12px;
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.legend-color.offline {
    background: #28a745;
}

.legend-color.online {
    background: #007bff;
}

.legend-color.hidden {
    background: #6c757d;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: slideInDown 0.3s ease;
}

.modal-header {
    background: linear-gradient(135deg, #da0000 0%, #a50000 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Training Detail Styles */
.training-detail {
    margin-bottom: 20px;
}

.training-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.training-info-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.training-info-item i {
    color: #667eea;
    width: 20px;
    text-align: center;
}

/* Participants Section */
.participants-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.participants-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.participant-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.participant-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.participant-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.participant-info {
    flex: 1;
}

.participant-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.participant-dept {
    font-size: 12px;
    color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .calendar-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .calendar-nav {
        order: 2;
    }

    .calendar-title {
        order: 1;
        font-size: 20px;
    }

    #todayBtn {
        order: 3;
    }

    .calendar-day {
        min-height: 80px;
        padding: 5px;
    }

    .calendar-event {
        font-size: 10px;
        padding: 2px 4px;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .training-info-grid {
        grid-template-columns: 1fr;
    }

    .participants-list {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Badge Styles */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bg-success {
    background-color: #28a745;
    color: white;
}

.bg-warning {
    background-color: #ffc107;
    color: #212529;
}

.bg-info {
    background-color: #17a2b8;
    color: white;
}

/* Training Color Legend Styles */
.training-color-legend {
    background: white;
    border-radius: 15px;
    margin-top: 20px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
}

.legend-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--primary-light);
}

.legend-header h6 {
    margin: 0;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.legend-section {
    margin-bottom: 20px;
}

.legend-section:last-child {
    margin-bottom: 0;
}

.legend-type-header {
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 10px;
    padding-left: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    background: #fafafa;
}

.legend-item:hover {
    background: #f0f0f0;
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.legend-color {
    width: 24px;
    height: 16px;
    border-radius: 4px;
    border: 1px solid rgba(0,0,0,0.1);
    flex-shrink: 0;
}

.legend-text {
    font-size: 14px;
    color: #495057;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    font-weight: 500;
}

.legend-item:hover .legend-text {
    color: #667eea;
}

/* Status indicators in legend */
.legend-text[title*="🔒"] {
    font-style: italic;
    opacity: 0.8;
}

.legend-text[title*="⚠️"] {
    border-left: 3px solid #ffc107;
    padding-left: 8px;
}

/* Responsive legend */
@media (max-width: 768px) {
    .training-color-legend {
        margin-top: 15px;
        padding: 15px;
    }

    .legend-items {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .legend-item {
        padding: 8px 10px;
    }

    .legend-text {
        font-size: 13px;
    }

    .legend-header h6 {
        font-size: 1rem;
    }
}

/* Animation for legend appearance */
@keyframes legendAppear {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.training-color-legend {
    animation: legendAppear 0.5s ease-out;
}

/* Hover effect for legend sections */
.legend-section:hover .legend-type-header {
    background: #e7f3ff;
    color: #667eea;
}
