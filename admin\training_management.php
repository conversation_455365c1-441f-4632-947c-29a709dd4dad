    <?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Inisialisasi variabel pencarian dan filter
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$training_type = isset($_GET['training_type']) ? $_GET['training_type'] : 'external'; // Default to external

// Handle date parameter from calendar
$auto_open_modal = false;
$auto_open_edit_modal = false;
$edit_training_id = null;

if (isset($_GET['date']) && !empty($_GET['date'])) {
    $calendar_date = $_GET['date'];
    // Set both date_from and date_to to the selected date for single day filter
    if (empty($date_from)) $date_from = $calendar_date;
    if (empty($date_to)) $date_to = $calendar_date;

    // If training_type is internal and date is provided, auto-open modal
    if ($training_type === 'internal' && isset($_GET['auto_open'])) {
        $auto_open_modal = true;
    }
}

// Handle edit parameter for auto-opening edit modal
$edit_training_data = null;
if (isset($_GET['id']) && !empty($_GET['id']) && $training_type === 'internal') {
    $edit_training_id = (int)$_GET['id'];

    // Fetch training data for edit modal
    $edit_query = "SELECT * FROM offline_training WHERE id = ?";
    $edit_stmt = $conn->prepare($edit_query);
    $edit_stmt->bind_param("i", $edit_training_id);
    $edit_stmt->execute();
    $edit_result = $edit_stmt->get_result();

    if ($edit_result->num_rows > 0) {
        $edit_training_data = $edit_result->fetch_assoc();
        $auto_open_edit_modal = true;
    }
    $edit_stmt->close();
}

// Handle internal training operations
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['internal_action'])) {
    $error = '';
    $success = '';

    if ($_POST['internal_action'] === 'add_internal') {
        $training_topic = $_POST['training_topic'] ?? '';
        $training_description = $_POST['training_description'] ?? '';
        $start_date = $_POST['start_date'] ?? null;
        $end_date = $_POST['end_date'] ?? null;
        $training_time_start = !empty($_POST['training_time_start']) ? $_POST['training_time_start'] : null;
        $training_time_end = !empty($_POST['training_time_end']) ? $_POST['training_time_end'] : null;
        $location = trim($_POST['location'] ?? '');
        $trainer_name = trim($_POST['trainer_name'] ?? '');
        $max_participants = !empty($_POST['max_participants']) ? (int)$_POST['max_participants'] : null;
        $status = $_POST['status'] ?? 'Active';

        if (empty($end_date) || $end_date === $start_date) {
            $end_date = null;
        }

        // Handle poster upload
        $poster_image = null;
        if (isset($_FILES['poster_image']) && $_FILES['poster_image']['error'] === UPLOAD_ERR_OK) {
            // Validate file type
            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png'];
            $file_type = $_FILES['poster_image']['type'];

            if (!in_array($file_type, $allowed_types)) {
                $_SESSION['error'] = "Format file tidak valid. Hanya file JPG, JPEG, dan PNG yang diperbolehkan.";
                header('Location: training_management.php?training_type=internal');
                exit();
            }

            // Validate file size (max 2MB)
            if ($_FILES['poster_image']['size'] > 2 * 1024 * 1024) {
                $_SESSION['error'] = "Ukuran file terlalu besar. Maksimal 2MB.";
                header('Location: training_management.php?training_type=internal');
                exit();
            }

            // Create upload directory if it doesn't exist
            $upload_dir = '../uploads/training_posters/offline/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            // Generate unique filename
            $file_extension = pathinfo($_FILES['poster_image']['name'], PATHINFO_EXTENSION);
            $unique_filename = 'poster_offline_' . time() . '_' . uniqid() . '.' . $file_extension;
            $upload_path = $upload_dir . $unique_filename;

            // Move uploaded file
            if (move_uploaded_file($_FILES['poster_image']['tmp_name'], $upload_path)) {
                $poster_image = 'uploads/training_posters/offline/' . $unique_filename;
            } else {
                $_SESSION['error'] = "Gagal mengupload poster.";
                header('Location: training_management.php?training_type=internal');
                exit();
            }
        }

        $query = "INSERT INTO offline_training
                  (training_topic, training_description, poster_image, start_date, end_date, training_time_start, training_time_end,
                   location, trainer_name, max_participants, status, created_by, is_confirmed)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("sssssssssisi", $training_topic, $training_description, $poster_image, $start_date, $end_date,
                          $training_time_start, $training_time_end, $location, $trainer_name,
                          $max_participants, $status, $_SESSION['user_id']);

        if ($stmt->execute()) {
            $_SESSION['success'] = "Training Internal berhasil ditambahkan";
        } else {
            $_SESSION['error'] = "Error: " . $stmt->error;
        }
        $stmt->close();
        header('Location: training_management.php?training_type=internal');
        exit();
    }

    if ($_POST['internal_action'] === 'edit_internal') {
        $training_id = $_POST['training_id'];
        $training_topic = $_POST['training_topic'] ?? '';
        $training_description = $_POST['training_description'] ?? '';
        $start_date = $_POST['start_date'] ?? null;
        $end_date = $_POST['end_date'] ?? null;
        $training_time_start = !empty($_POST['training_time_start']) ? $_POST['training_time_start'] : null;
        $training_time_end = !empty($_POST['training_time_end']) ? $_POST['training_time_end'] : null;
        $location = trim($_POST['location'] ?? '');
        $trainer_name = trim($_POST['trainer_name'] ?? '');
        $max_participants = !empty($_POST['max_participants']) ? (int)$_POST['max_participants'] : null;
        $status = $_POST['status'] ?? 'Active';

        if (empty($end_date) || $end_date === $start_date) {
            $end_date = null;
        }

        // Handle poster upload for edit
        $poster_update = "";
        $poster_params = "";
        if (isset($_FILES['poster_image']) && $_FILES['poster_image']['error'] === UPLOAD_ERR_OK) {
            // Validate file type
            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png'];
            $file_type = $_FILES['poster_image']['type'];

            if (!in_array($file_type, $allowed_types)) {
                $_SESSION['error'] = "Format file tidak valid. Hanya file JPG, JPEG, dan PNG yang diperbolehkan.";
                header('Location: training_management.php?training_type=internal');
                exit();
            }

            // Validate file size (max 2MB)
            if ($_FILES['poster_image']['size'] > 2 * 1024 * 1024) {
                $_SESSION['error'] = "Ukuran file terlalu besar. Maksimal 2MB.";
                header('Location: training_management.php?training_type=internal');
                exit();
            }

            // Get old poster to delete
            $old_poster_query = "SELECT poster_image FROM offline_training WHERE id = ?";
            $old_poster_stmt = $conn->prepare($old_poster_query);
            $old_poster_stmt->bind_param("i", $training_id);
            $old_poster_stmt->execute();
            $old_poster_result = $old_poster_stmt->get_result();
            $old_poster_row = $old_poster_result->fetch_assoc();
            $old_poster_stmt->close();

            // Create upload directory if it doesn't exist
            $upload_dir = '../uploads/training_posters/offline/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            // Generate unique filename
            $file_extension = pathinfo($_FILES['poster_image']['name'], PATHINFO_EXTENSION);
            $unique_filename = 'poster_offline_' . time() . '_' . uniqid() . '.' . $file_extension;
            $upload_path = $upload_dir . $unique_filename;

            // Move uploaded file
            if (move_uploaded_file($_FILES['poster_image']['tmp_name'], $upload_path)) {
                $new_poster_image = 'uploads/training_posters/offline/' . $unique_filename;
                $poster_update = ", poster_image = ?";
                $poster_params = "s";

                // Delete old poster file if exists
                if ($old_poster_row['poster_image'] && file_exists('../' . $old_poster_row['poster_image'])) {
                    unlink('../' . $old_poster_row['poster_image']);
                }
            } else {
                $_SESSION['error'] = "Gagal mengupload poster.";
                header('Location: training_management.php?training_type=internal');
                exit();
            }
        }

        $query = "UPDATE offline_training
                  SET training_topic = ?, training_description = ?, start_date = ?, end_date = ?,
                      training_time_start = ?, training_time_end = ?, location = ?,
                      trainer_name = ?, max_participants = ?, status = ?" . $poster_update . "
                  WHERE id = ?";
        $stmt = $conn->prepare($query);

        if ($poster_update) {
            $stmt->bind_param("ssssssssiss" . $poster_params . "i", $training_topic, $training_description, $start_date, $end_date,
                              $training_time_start, $training_time_end, $location, $trainer_name,
                              $max_participants, $status, $new_poster_image, $training_id);
        } else {
            $stmt->bind_param("ssssssssisi", $training_topic, $training_description, $start_date, $end_date,
                              $training_time_start, $training_time_end, $location, $trainer_name,
                              $max_participants, $status, $training_id);
        }

        if ($stmt->execute()) {
            $_SESSION['success'] = "Training Internal berhasil diperbarui";
        } else {
            $_SESSION['error'] = "Error: " . $stmt->error;
        }
        $stmt->close();
        header('Location: training_management.php?training_type=internal');
        exit();
    }

    if ($_POST['internal_action'] === 'delete_internal') {
        $training_id = $_POST['training_id'];

        // Check if there's attendance data
        $check_query = "SELECT COUNT(*) as count FROM offline_training_attendance WHERE offline_training_id = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("i", $training_id);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        $row = $result->fetch_assoc();
        $check_stmt->close();

        if ($row['count'] > 0) {
            $_SESSION['error'] = "Tidak dapat menghapus training karena sudah ada data absensi";
        } else {
            $query = "DELETE FROM offline_training WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $training_id);

            if ($stmt->execute()) {
                $_SESSION['success'] = "Training Internal berhasil dihapus";
            } else {
                $_SESSION['error'] = "Error: " . $stmt->error;
            }
            $stmt->close();
        }
        header('Location: training_management.php?training_type=internal');
        exit();
    }
}

// Build query based on training type
if ($training_type === 'internal') {
    $query = "SELECT ot.*, u.name as requester_name
              FROM offline_training ot
              LEFT JOIN users u ON ot.created_by = u.id
              WHERE 1=1";
} else {
    $query = "SELECT ts.*, u.name as requester_name
              FROM training_submissions ts
              LEFT JOIN users u ON ts.user_id = u.id
              WHERE 1=1";
}

// Add search conditions based on training type
if ($search) {
    if ($training_type === 'internal') {
        $query .= " AND (ot.training_topic LIKE '%$search%'
                    OR ot.training_description LIKE '%$search%'
                    OR ot.trainer_name LIKE '%$search%'
                    OR ot.location LIKE '%$search%')";
    } else {
        $query .= " AND (ts.full_name LIKE '%$search%'
                    OR ts.training_topic LIKE '%$search%'
                    OR ts.email LIKE '%$search%')";
    }
}

// Add status filter based on training type
if ($status_filter) {
    if ($training_type === 'internal') {
        $query .= " AND ot.status = '$status_filter'";
    } else {
        $query .= " AND ts.status = '$status_filter'";
    }
}

// Add date filter based on training type
if ($date_from && $date_to) {
    if ($training_type === 'internal') {
        $query .= " AND (
            (ot.start_date BETWEEN '$date_from' AND '$date_to') OR
            (ot.end_date BETWEEN '$date_from' AND '$date_to')
        )";
    } else {
        $query .= " AND (
            (ts.start_date BETWEEN '$date_from' AND '$date_to') OR
            (ts.end_date BETWEEN '$date_from' AND '$date_to')
        )";
    }
}

// Add ORDER BY based on training type
if ($training_type === 'internal') {
    $query .= " ORDER BY ot.start_date DESC, ot.id DESC";
} else {
    $query .= " ORDER BY ts.id DESC";
}

$result = mysqli_query($conn, $query);
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    
button .btn, .btn-sm {
    width: 30px !important; 
    padding: 5px !important;
    font-size:10px!important;
    min-width:20px !important;
}
.container-form {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    margin-top: 80px; /* Add top margin to prevent navbar overlap */
    position: relative;
    z-index: 1; /* Ensure it's above the navbar */
}

@media screen and (max-width: 768px) {
    .container-form {
        padding: 15px;
        margin-top: 70px; /* Adjust for smaller screens */
    }
}

@media screen and (max-width: 480px) {
    .container-form {
        padding: 10px;
        margin-top: 65px; /* Adjust for mobile screens */
    }
}

.training-management-container {
    padding: 25px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 6px 18px rgba(0,0,0,0.1);
    margin: 0;
}

.welcome-section {
    text-align: center;
    margin-bottom: 30px;
    padding: 30px;
    background: linear-gradient(135deg, #BF0000, #800000);
    color: white;
    border-radius: 12px;
    box-shadow: 0 6px 18px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSg0NSkiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjA1KSIvPjwvcGF0dGVybj48L2RlZnM+PHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwYXR0ZXJuKSIvPjwvc3ZnPg==');
    opacity: 0.2;
}

.welcome-section h1 {
    margin: 0;
    font-size: 2.8em;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    position: relative;
}

.welcome-section p {
    margin: 15px 0 0;
    font-size: 1.3em;
    opacity: 0.9;
    position: relative;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Summary Cards Styling */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 25px;
    margin-bottom: 35px;
}

.summary-card {
    padding: 25px;
    border-radius: 15px;
    background: white;
    box-shadow: 0 8px 20px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background: #BF0000;
    opacity: 0.8;
}

.summary-card.rejected::before {
    background: #dc3545;
}

.summary-card.approved::before {
    background: #28a745;
}

.summary-card.pending::before {
    background:rgb(255, 187, 0);
}

.summary-card.completed::before {
    background: #a50000;
}

.summary-card:hover {
    transform: translateY(-7px);
    box-shadow: 0 12px 25px rgba(0,0,0,0.1);
}

.summary-card h3 {
    margin: 0;
    color: #333;
    font-size: 1.1em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-card h3 i {
    font-size: 1.2em;
    opacity: 0.8;
}

.summary-card p {
    margin: 15px 0 0;
    font-size: 2.5em;
    font-weight: 700;
    color: #333;
    align-self: flex-end;
    width: 100%;
    text-align: right;
}

.summary-card.total p {
    color: #BF0000;
}
.summary-card.pending p {
    color: rgb(255, 187, 0);
}

.summary-card.rejected p {
    color: #dc3545;
}

.summary-card.approved p {
    color: #28a745;
}

.summary-card.completed p {
    color: #a50000;
}

/* Filters Section Styling */
.filters-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.filters-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #BF0000, #800000);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: #333;
    font-size: 0.9em;
    display: flex;
    align-items: center;
    gap: 6px;
}

.filter-group label i {
    color: #BF0000;
    font-size: 1.1em;
}

.filter-group input,
.filter-group select {
    padding: 14px;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.02);
}

.filter-group input:focus,
.filter-group select:focus {
    border-color: #BF0000;
    box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
    outline: none;
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-top: auto;
    justify-content: flex-end;
}

.filter-actions button {
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-actions .btn-reset {
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #ced4da;
}

.filter-actions .btn-reset:hover {
    background-color: #e9ecef;
}

.filter-actions .btn-apply {
    background-color: #BF0000;
    color: white;
    border: none;
}

.filter-actions .btn-apply:hover {
    background-color: #a50000;
}

/* Table Styling */
.table-responsive {
    background: white;
    padding: 0;
    border-radius: 15px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.08);
    overflow-x: auto;
    overflow-y: hidden;
    margin-top: 30px;
    width: 100%;
}

.table-header {
    background: linear-gradient(90deg, #BF0000, #800000);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h3 {
    color: white;
    margin: 0;
    font-size: 1.3em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.table-header h3 i {
    font-size: 1.2em;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.training-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 0;
    table-layout: fixed;
}

.training-table th {
    background: #f8f9fa;
    padding: 16px 20px;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    position: relative;
}

.training-table th:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #BF0000, transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.training-table th:hover:after {
    opacity: 1;
}

.training-table td {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Set specific column widths */
.training-table th:nth-child(1),
.training-table td:nth-child(1) {
    width: 5%;
}

.training-table th:nth-child(2),
.training-table td:nth-child(2) {
    width: 20%;
}

.training-table th:nth-child(3),
.training-table td:nth-child(3) {
    width: 20%;
}

.training-table th:nth-child(4),
.training-table td:nth-child(4) {
    width: 15%;
}

.training-table th:nth-child(5),
.training-table td:nth-child(5) {
    width: 12%;
}

.training-table th:nth-child(6),
.training-table td:nth-child(6) {
    width: 30%;
    min-width: 160px;
    text-align: center;
    position: sticky;
    right: 0;
    background-color: white;
    z-index: 2;
    box-shadow: -5px 0 10px rgba(0,0,0,0.05);
}

.training-table th:nth-child(6) {
    background-color: #f8f9fa;
    z-index: 3;
}

.training-table tbody tr {
    background-color: white;
    transition: all 0.2s ease;
}

.training-table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.03);
}

.training-table tbody tr:hover td:nth-child(6) {
    background-color: #f8f9fa;
}

.training-table tbody tr:last-child td {
    border-bottom: none;
}

.empty-state {
    padding: 60px 20px;
    text-align: center;
    color: #6c757d;
}

.empty-state i {
    font-size: 3em;
    margin-bottom: 15px;
    color: #dee2e6;
}

.empty-state h4 {
    margin: 0 0 10px;
    font-size: 1.2em;
    font-weight: 600;
}

.empty-state p {
    margin: 0;
    max-width: 400px;
    margin: 0 auto;
}

/* Training Tabs Styling */
.training-tabs {
    margin-bottom: 20px;
}

.tab-navigation {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
}

.tab-button {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: #f8f9fa;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
    position: relative;
}

.tab-button:hover {
    background: #e9ecef;
    color: #495057;
}

.tab-button.active {
    background: #a50000;
    color: white;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background:rgb(79, 0, 0);
}

.tab-button i {
    font-size: 16px;
}

.tab-count {
    background: rgba(255,255,255,0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    min-width: 20px;
    text-align: center;
}

.tab-button.active .tab-count {
    background: rgba(255,255,255,0.3);
}

/* Tab Content Animation */
.tab-content {
    opacity: 1;
    transition: opacity 0.3s ease;
}

.tab-content.loading {
    opacity: 0.6;
}

/* Status Badge Styling */
.status-badge {
    padding: 8px 15px;
    border-radius: 30px;
    font-size: 0.85em;
    font-weight: 600;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 110px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
    gap: 6px;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.2), transparent);
    z-index: 1;
}

.status-badge i {
    font-size: 0.9em;
    z-index: 2;
}

.status-badge span {
    z-index: 2;
}



.status-approved {
    background: #d4edda;
    color: #155724;
    border: 1px solid rgba(21, 87, 36, 0.2);
}

.status-completed {
    background: #cce5ff;
    color: #004085;
    border: 1px solid rgba(0, 64, 133, 0.2);
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid rgba(114, 28, 36, 0.2);
}

/* Button Styling */
.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
    min-width: 150px;
    white-space: nowrap;
    width: 100%;
}

.btn {
    padding: 6px 8px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
    font-size: 13px;
}

.btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), transparent);
    z-index: 1;
}

.btn i {
    margin-right: 6px;
    font-size: 0.9em;
    z-index: 2;
}

.btn span {
    z-index: 2;
}

.btn-view {
    background-color: white;
    color: #a50000;
    border: 1px solid #a50000;
}

.btn-view:hover {
    background-color: #a50000;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 123, 255, 0.2);
}

.btn-edit {
    background-color: white;
    color: #28a745;
    border: 1px solid #28a745;
}

.btn-edit:hover {
    background-color: #28a745;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(40, 167, 69, 0.2);
}

.btn-delete {
    background-color: white;
    color: #dc3545;
    border: 1px solid #dc3545;
}

.btn-delete:hover {
    background-color: #dc3545;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(220, 53, 69, 0.2);
}

.btn-primary {
    background: #BF0000;
    color: white;
    padding: 12px 24px;
    border: none;
}

.btn-primary:hover {
    background: #a50000;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(191, 0, 0, 0.2);
}

.btn-secondary {
    background-color: white;
    color: #6c757d;
    border: 1px solid #6c757d;
}

.btn-secondary:hover {
    background-color: #6c757d;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(108, 117, 125, 0.2);
}

.btn-create {
    background: #BF0000;
    color: white;
    padding: 12px 20px;
    border: none;
    font-weight: 600;
}

.btn-create:hover {
    background: #a50000;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(191, 0, 0, 0.2);
}

/* Loading Indicator */
#loadingIndicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 3px solid rgba(191, 0, 0, 0.1);
    border-top: 3px solid #BF0000;
    border-radius: 50%;
    animation: spin 1s cubic-bezier(0.5, 0.1, 0.5, 0.9) infinite;
    margin-bottom: 15px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.05);
}

.loading-text {
    font-size: 1em;
    color: #6c757d;
    font-weight: 500;
    margin-top: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pulse Animation for Loading */
@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.loading-pulse {
    animation: pulse 1.5s infinite;
}

/* Responsive Design */
@media screen and (max-width: 992px) {
    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }

    .filters-section {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 768px) {
    .training-management-container {
        padding: 20px;
    }

    .welcome-section h1 {
        font-size: 2.2em;
    }

    .welcome-section p {
        font-size: 1.1em;
    }

    .filters-section {
        padding: 20px;
    }

    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .table-actions {
        width: 100%;
        justify-content: space-between;
    }

    .action-buttons {
        flex-wrap: nowrap;
        min-width: 130px;
        justify-content: center;
    }

    .training-table th:nth-child(6),
    .training-table td:nth-child(6) {
        width: 35%;
        min-width: 160px;
        padding-left: 5px;
        padding-right: 5px;
    }

    .btn {
        padding: 6px 8px;
        font-size: 12px;
        min-width: 30px;
    }

    .status-badge {
        min-width: 90px;
        padding: 6px 10px;
        font-size: 0.8em;
    }

    .training-table th,
    .training-table td {
        padding: 12px 15px;
    }
}

@media screen and (max-width: 576px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }

    .filters-section {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        min-width: 120px;
        gap: 3px;
    }

    .btn {
        padding: 5px 7px;
        font-size: 11px;
        min-width: 28px;
    }

    .training-table th:nth-child(6),
    .training-table td:nth-child(6) {
        width: 40%;
        min-width: 120px;
    }

    .training-table th,
    .training-table td {
        padding: 10px 12px;
    }
}

/* Tooltip styling */
[title] {
    position: relative;
}

[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 99;
    pointer-events: none;
    margin-bottom: 5px;
}

/* Custom Scrollbar */
.table-responsive::-webkit-scrollbar {
    height: 8px;
    width: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, #BF0000, #800000);
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, #a50000, #700000);
}

.alert {
    padding: 18px 20px;
    margin-bottom: 25px;
    border: 1px solid transparent;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
    animation: slideDown 0.4s ease-out;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
}

.alert i {
    font-size: 1.5em;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 600;
    margin: 0 0 5px;
    font-size: 1.1em;
}

.alert-message {
    margin: 0;
    opacity: 0.9;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-success::before {
    background-color: #28a745;
}

.alert-success i {
    color: #28a745;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-danger::before {
    background-color: #dc3545;
}

.alert-danger i {
    color: #dc3545;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-warning::before {
    background-color: #ffc107;
}

.alert-warning i {
    color: #ffc107;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-info::before {
    background-color: #17a2b8;
}

.alert-info i {
    color: #17a2b8;
}

.alert-dismissible {
    padding-right: 50px;
}

.alert-dismissible .close {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    color: inherit;
    opacity: 0.7;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2em;
    padding: 5px;
}

.alert-dismissible .close:hover {
    opacity: 1;
}

@keyframes slideDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
</style>

<body>
    <?php include '../config/navbar.php'; ?>
    <div class="container-form">
        <div class="welcome-section">
            <h1>Manajemen Training</h1>
            <p>Kelola semua pengajuan training dengan mudah dan efisien</p>
        </div>

        <!-- Alerts -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <div class="alert-content">
                    <h4 class="alert-title">Berhasil!</h4>
                    <p class="alert-message"><?= $_SESSION['success'] ?></p>
                </div>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <div class="alert-content">
                    <h4 class="alert-title">Error!</h4>
                    <p class="alert-message"><?= $_SESSION['error'] ?></p>
                </div>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <div class="training-management-container">
            <!-- Summary Cards -->
            <div class="summary-cards">
                <?php
                if ($training_type === 'internal') {
                    $total_query = "SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'Cancelled' THEN 1 ELSE 0 END) as rejected,
                        SUM(CASE WHEN status = 'Active' THEN 1 ELSE 0 END) as approved,
                        SUM(CASE WHEN status = 'Completed' THEN 1 ELSE 0 END) as completed
                        FROM offline_training";
                    $total_result = mysqli_query($conn, $total_query);
                    $totals = mysqli_fetch_assoc($total_result);
                    $totals['pending'] = 0; // Internal training doesn't have pending status
                } else {
                    $total_query = "SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
                        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                        FROM training_submissions";
                    $total_result = mysqli_query($conn, $total_query);
                    $totals = mysqli_fetch_assoc($total_result);
                    $pending_query = "SELECT COUNT(*) as pending FROM training_submissions WHERE status = 'pending'";
                    $pending_result = mysqli_query($conn, $pending_query);
                    $pending = mysqli_fetch_assoc($pending_result);
                    $totals['pending'] = $pending['pending'];
                }

                $totals['rejected'] = $totals['rejected'] ?? 0;
                $totals['approved'] = $totals['approved'] ?? 0;
                $totals['completed'] = $totals['completed'] ?? 0;
                ?>
                <div class="summary-card total">
                    <h3><i class="fas fa-clipboard-list"></i> <span class="card-title">Total Pengajuan</span></h3>
                    <p class="count"><?= $totals['total'] ?></p>
                </div>

                <div class="summary-card pending">
                    <h3><i class="fas fa-clock"></i> <span class="card-title">Menunggu Persetujuan</span></h3>
                    <p class="count"><?= $totals['pending'] ?></p>
                </div>

                <div class="summary-card approved">
                    <h3><i class="fas fa-check-circle"></i> <span class="card-title">Pengajuan Disetujui</span></h3>
                    <p class="count"><?= $totals['approved'] ?></p>
                </div>
                <div class="summary-card completed">
                    <h3><i class="fas fa-award"></i> <span class="card-title">Training Selesai</span></h3>
                    <p class="count"><?= $totals['completed'] ?></p>
                </div>
                <div class="summary-card rejected">
                    <h3><i class="fas fa-times-circle"></i> <span class="card-title">Pengajuan Dibatalkan</span></h3>
                    <p class="count"><?= $totals['rejected'] ?></p>
                </div>
            </div>

            <!-- Training Type Tabs -->
            <div class="training-tabs">
                <div class="tab-navigation">
                    <button class="tab-button active" data-tab="external" id="externalTab">
                        <i class="fas fa-external-link-alt"></i>
                        <span>Training Eksternal</span>
                        <span class="tab-count" id="externalCount">0</span>
                    </button>
                    <button class="tab-button" data-tab="internal" id="internalTab">
                        <i class="fas fa-building"></i>
                        <span>Training Internal</span>
                        <span class="tab-count" id="internalCount">0</span>
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <form class="filters-section" method="GET" id="filterForm">
                <input type="hidden" name="training_type" id="trainingTypeInput" value="external">
                <div class="filter-group">
                    <label><i class="fas fa-search"></i> Cari:</label>
                    <input type="text"
                           id="searchInput"
                           name="search"
                           placeholder="Nama/Topic/Email"
                           value="<?= htmlspecialchars($search) ?>">
                </div>
                <div class="filter-group">
                    <label><i class="fas fa-filter"></i> Status:</label>
                    <select name="status" id="statusFilter">
                        <option value="">Semua Status</option>
                        <!-- Options will be populated dynamically based on active tab -->
                    </select>
                </div>
                <div class="filter-group">
                    <label><i class="fas fa-calendar-alt"></i> Dari Tanggal:</label>
                    <input type="date"
                           id="dateFrom"
                           name="date_from"
                           value="<?= htmlspecialchars($date_from) ?>">
                </div>
                <div class="filter-group">
                    <label><i class="fas fa-calendar-alt"></i> Sampai Tanggal:</label>
                    <input type="date"
                           id="dateTo"
                           name="date_to"
                           value="<?= htmlspecialchars($date_to) ?>">
                </div>
                <div class="filter-group">
                    <div class="filter-actions">
                        <button type="button" class="btn btn-reset" id="resetFilter">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                        <button type="button" class="btn btn-create" id="addInternalTrainingBtn" data-bs-toggle="modal" data-bs-target="#addInternalTrainingModal" style="display: none;">
                            <i class="fas fa-plus"></i> Tambah Training Internal
                        </button>
                    </div>
                </div>
            </form>

            <!-- Loading Indicator -->
            <div id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <p class="loading-text loading-pulse">Memuat data...</p>
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="table-responsive">
                <!-- Table content will be updated via JavaScript -->
            </div>
        </div>
    </div>

    <!-- Modal Tambah Training Internal -->
    <div class="modal fade" id="addInternalTrainingModal" tabindex="-1" aria-labelledby="addInternalTrainingModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addInternalTrainingModalLabel">Tambah Training Internal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="internal_action" value="add_internal">

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="training_topic" class="form-label">Topik Training <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="training_topic" name="training_topic" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="training_description" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="training_description" name="training_description" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="poster_image" class="form-label">Poster Training</label>
                            <input type="file" class="form-control" id="poster_image" name="poster_image" accept="image/*">
                            <small class="form-text text-muted">Format yang didukung: JPG, JPEG, PNG. Maksimal 2MB.</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="start_date" class="form-label">Tanggal Mulai <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?= isset($_GET['date']) ? htmlspecialchars($_GET['date']) : date('Y-m-d') ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="end_date" class="form-label">Tanggal Selesai</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?= isset($_GET['date']) ? htmlspecialchars($_GET['date']) : '' ?>">
                                <small class="form-text text-muted">Kosongkan jika training hanya 1 hari</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="training_time_start" class="form-label">Waktu Mulai</label>
                                <input type="time" class="form-control" id="training_time_start" name="training_time_start">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="training_time_end" class="form-label">Waktu Selesai</label>
                                <input type="time" class="form-control" id="training_time_end" name="training_time_end">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="location" class="form-label">Lokasi</label>
                                <input type="text" class="form-control" id="location" name="location">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="trainer_name" class="form-label">Nama Trainer</label>
                                <input type="text" class="form-control" id="trainer_name" name="trainer_name">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="max_participants" class="form-label">Maksimal Peserta</label>
                                <input type="number" class="form-control" id="max_participants" name="max_participants" min="1">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-control" id="status" name="status" required>
                                    <option value="Active">Active</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Cancelled">Cancelled</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Edit Training Internal -->
    <div class="modal fade" id="editInternalTrainingModal" tabindex="-1" aria-labelledby="editInternalTrainingModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editInternalTrainingModalLabel">Edit Training Internal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="internal_action" value="edit_internal">
                        <input type="hidden" name="training_id" id="edit_training_id">

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="edit_training_topic" class="form-label">Topik Training <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_training_topic" name="training_topic" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_training_description" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="edit_training_description" name="training_description" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="edit_poster_image" class="form-label">Poster Training</label>
                            <input type="file" class="form-control" id="edit_poster_image" name="poster_image" accept="image/*">
                            <small class="form-text text-muted">Format yang didukung: JPG, JPEG, PNG. Maksimal 2MB. Kosongkan jika tidak ingin mengubah poster.</small>
                            <div id="current_poster_display" style="margin-top: 10px; display: none;">
                                <small class="text-info">Poster saat ini:</small><br>
                                <img id="current_poster_img" src="" alt="Current Poster" style="max-width: 200px; max-height: 150px; border-radius: 4px; margin-top: 5px;">
                            </div>
                        </div>

                        <div class="row">
                             <div class="col-md-6 mb-3">
                                <label for="edit_start_date" class="form-label">Tanggal Mulai <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="edit_start_date" name="start_date" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_end_date" class="form-label">Tanggal Selesai</label>
                                <input type="date" class="form-control" id="edit_end_date" name="end_date">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="edit_training_time_start" class="form-label">Waktu Mulai</label>
                                <input type="time" class="form-control" id="edit_training_time_start" name="training_time_start">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="edit_training_time_end" class="form-label">Waktu Selesai</label>
                                <input type="time" class="form-control" id="edit_training_time_end" name="training_time_end">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="edit_location" class="form-label">Lokasi</label>
                                <input type="text" class="form-control" id="edit_location" name="location">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_trainer_name" class="form-label">Nama Trainer</label>
                                <input type="text" class="form-control" id="edit_trainer_name" name="trainer_name">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_max_participants" class="form-label">Maksimal Peserta</label>
                                <input type="number" class="form-control" id="edit_max_participants" name="max_participants" min="1">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-control" id="edit_status" name="status" required>
                                    <option value="Active">Active</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Cancelled">Cancelled</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Hapus Training Internal -->
    <div class="modal fade" id="deleteInternalTrainingModal" tabindex="-1" aria-labelledby="deleteInternalTrainingModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteInternalTrainingModalLabel">Konfirmasi Hapus</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin menghapus training <strong id="delete_training_topic"></strong>?</p>
                    <p class="text-danger">Perhatian: Tindakan ini tidak dapat dibatalkan!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <form method="post" action="" style="display: inline;">
                        <input type="hidden" name="internal_action" value="delete_internal">
                        <input type="hidden" name="training_id" id="delete_training_id">
                        <button type="submit" class="btn btn-danger">Hapus</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php include '../config/footer.php'; ?>
</body>
</html>

<script>
// Fungsi untuk menampilkan toast notification
function showToast(message, type = 'success', duration = 3000) {
    // Hapus toast yang sudah ada jika ada
    const existingToast = document.querySelector('.toast-notification');
    if (existingToast) {
        document.body.removeChild(existingToast);
    }

    // Buat toast baru
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;

    // Set icon berdasarkan type
    let icon = 'check';
    if (type === 'error') icon = 'exclamation-circle';
    if (type === 'warning') icon = 'exclamation-triangle';
    if (type === 'info') icon = 'info-circle';

    toast.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;
    document.body.appendChild(toast);

    // Animasi toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // Hapus toast setelah durasi tertentu
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// Fungsi untuk menampilkan dialog konfirmasi kustom
function showConfirmDialog(message, onConfirm, onCancel) {
    // Hapus dialog yang sudah ada jika ada
    const existingDialog = document.querySelector('.confirm-dialog-container');
    if (existingDialog) {
        document.body.removeChild(existingDialog);
    }

    // Buat dialog konfirmasi
    const dialogContainer = document.createElement('div');
    dialogContainer.className = 'confirm-dialog-container';

    dialogContainer.innerHTML = `
        <div class="confirm-dialog">
            <div class="confirm-dialog-header">
                <i class="fas fa-question-circle"></i>
                <h4>Konfirmasi</h4>
            </div>
            <div class="confirm-dialog-body">
                <p>${message}</p>
            </div>
            <div class="confirm-dialog-footer">
                <button class="btn btn-secondary btn-cancel">Batal</button>
                <button class="btn btn-danger btn-confirm">Ya, Lanjutkan</button>
            </div>
        </div>
    `;

    document.body.appendChild(dialogContainer);

    // Tambahkan event listener untuk tombol
    const cancelBtn = dialogContainer.querySelector('.btn-cancel');
    const confirmBtn = dialogContainer.querySelector('.btn-confirm');

    cancelBtn.addEventListener('click', () => {
        document.body.removeChild(dialogContainer);
        if (onCancel) onCancel();
    });

    confirmBtn.addEventListener('click', () => {
        document.body.removeChild(dialogContainer);
        if (onConfirm) onConfirm();
    });

    // Animasi dialog
    setTimeout(() => {
        dialogContainer.classList.add('show');
    }, 10);
}

// Tambahkan CSS untuk toast notification dan dialog konfirmasi
document.head.insertAdjacentHTML('beforeend', `
<style>
.toast-notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.toast-notification.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.toast-success i {
    color: #4CAF50;
}

.toast-error i {
    color: #F44336;
}

.toast-warning i {
    color: #FF9800;
}

.toast-info i {
    color: #2196F3;
}

.confirm-dialog-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.confirm-dialog-container.show {
    opacity: 1;
}

.confirm-dialog {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.confirm-dialog-container.show .confirm-dialog {
    transform: translateY(0);
}

.confirm-dialog-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px;
}

.confirm-dialog-header i {
    color: #BF0000;
    font-size: 1.2rem;
}

.confirm-dialog-header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: #212529;
}

.confirm-dialog-body {
    padding: 20px;
}

.confirm-dialog-body p {
    margin: 0;
    color: #495057;
}

.confirm-dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

@media screen and (max-width: 576px) {
    .confirm-dialog {
        width: 95%;
    }

    .confirm-dialog-header,
    .confirm-dialog-body,
    .confirm-dialog-footer {
        padding: 12px 15px;
    }
}
</style>
`);

let debounceTimer;

// Fungsi debounce untuk menunda eksekusi
function debounce(func, wait) {
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(debounceTimer);
            func(...args);
        };
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(later, wait);
    };
}

// Fungsi untuk memformat tanggal
function formatDate(date) {
    if (!date) return '';
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${day}-${month}-${year}`;
}

// Fungsi untuk mengambil data
async function fetchFilteredData() {
    const searchInput = document.getElementById('searchInput').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;
    const trainingType = currentTrainingType;

    const loadingIndicator = document.getElementById('loadingIndicator');
    loadingIndicator.style.display = 'block';

    try {
        const params = new URLSearchParams({
            search: searchInput,
            status: statusFilter,
            date_from: dateFrom,
            date_to: dateTo,
            training_type: trainingType
        });

        console.log('Fetching data with params:', params.toString());

        const response = await fetch(`get_filtered_training_unified.php?${params}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        console.log('Data received:', data);

        // Check if data contains error
        if (data.error) {
            throw new Error(data.error);
        }

        updateTable(data, trainingType);
    } catch (error) {
        console.error('Error fetching data:', error);

        // Show error message to user
        const errorHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
                <h4>Error Loading Data</h4>
                <p>${error.message}</p>
                <button onclick="fetchFilteredData()" class="btn btn-primary">
                    <i class="fas fa-refresh"></i> Retry
                </button>
            </div>
        `;
        document.getElementById('searchResults').innerHTML = errorHTML;
    } finally {
        loadingIndicator.style.display = 'none';
    }
}

// Fungsi untuk mengupdate tabel
function updateTable(data, trainingType) {
    if (data.length === 0) {
        // Show empty state if no data
        const emptyStateHTML = `
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <h4>Tidak ada data yang ditemukan</h4>
                <p>Coba ubah filter pencarian atau tambahkan training baru</p>
            </div>
        `;
        document.getElementById('searchResults').innerHTML = emptyStateHTML;
        return;
    }

    // Get status icon based on status and training type
    const getStatusIcon = (status, type) => {
        if (type === 'internal') {
            switch(status) {
                case 'Active': return '<i class="fas fa-play-circle"></i>';
                case 'Completed': return '<i class="fas fa-check-circle"></i>';
                case 'Cancelled': return '<i class="fas fa-times-circle"></i>';
                default: return '';
            }
        } else {
            switch(status.toLowerCase()) {
                case 'pending': return '<i class="fas fa-clock"></i>';
                case 'approved': return '<i class="fas fa-check-circle"></i>';
                case 'completed': return '<i class="fas fa-award"></i>';
                case 'rejected': return '<i class="fas fa-times-circle"></i>';
                default: return '';
            }
        }
    };

    // Generate table HTML based on training type
    let tableHTML = '';

    if (trainingType === 'internal') {
        tableHTML = `
            <table class="training-table">
                <thead>
                    <tr>
                        <th data-column="id" class="sortable-column">ID</th>
                        <th data-column="training_topic" class="sortable-column">Topik Training</th>
                        <th data-column="start_date" class="sortable-column">Tanggal</th>
                        <th data-column="trainer_name" class="sortable-column">Trainer</th>
                        <th data-column="location" class="sortable-column">Status</th>
                        <th data-column="status" class="sortable-column">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map(row => `
                        <tr>
                            <td>${row.id}</td>
                            <td style="white-space: wrap;">${escapeHtml(row.training_topic)}</td>
                            <td style="white-space: wrap;">
                                ${row.start_date ?
                                    (row.end_date && row.end_date !== row.start_date ?
                                        `${formatDate(row.start_date)} - ${formatDate(row.end_date)}` :
                                        formatDate(row.start_date)
                                    ) :
                                    '<span style="color: #999;">Belum ditentukan</span>'
                                }
                                ${row.training_time_start && row.training_time_end ?
                                    `<br><small style="color: #666;">${row.training_time_start} - ${row.training_time_end}</small>` :
                                    ''
                                }
                            </td>
                            <td style="white-space: wrap;">${escapeHtml(row.trainer_name || '-')}</td>
                            <td style="white-space: wrap;">
                                <span class="status-badge status-${row.status.toLowerCase()}">
                                    ${getStatusIcon(row.status, 'internal')}
                                    <span>${row.status}</span>
                                </span>
                            </td>
                            <td style="text-align: center;">
                                <div class="action-buttons">
                                    <a href="view_offline_attendance.php?id=${row.id}" class="btn btn-sm btn-view" title="Lihat Absensi">
                                        <i class="fas fa-list"></i>
                                    </a>
                                    <a href="rfid_attendance_offline.php?training_id=${row.id}" class="btn btn-sm btn-warning" title="Absensi RFID">
                                        <i class="fas fa-id-card"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-edit edit-internal-training"
                                        data-id="${row.id}"
                                        data-topic="${escapeHtml(row.training_topic)}"
                                        data-description="${escapeHtml(row.training_description || '')}"
                                        data-start-date="${row.start_date || ''}"
                                        data-end-date="${row.end_date || ''}"
                                        data-time-start="${row.training_time_start || ''}"
                                        data-time-end="${row.training_time_end || ''}"
                                        data-location="${escapeHtml(row.location || '')}"
                                        data-trainer="${escapeHtml(row.trainer_name || '')}"
                                        data-max="${row.max_participants || ''}"
                                        data-status="${row.status}"
                                        title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-delete delete-internal-training"
                                        data-id="${row.id}"
                                        data-topic="${escapeHtml(row.training_topic)}"
                                        title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    } else {
        tableHTML = `
            <table class="training-table">
                <thead>
                    <tr>
                        <th data-column="id" class="sortable-column">ID</th>
                        <th data-column="full_name" class="sortable-column">Nama</th>
                        <th data-column="training_topic" class="sortable-column">Topik Training</th>
                        <th data-column="training_date" class="sortable-column">Tanggal</th>
                        <th data-column="status" class="sortable-column">Status</th>
                        <th style="text-align: center;">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map(row => `
                        <tr>
                            <td>${row.id}</td>
                            <td style="white-space: wrap;">${escapeHtml(row.full_name)}</td>
                            <td style="white-space: wrap;">${escapeHtml(row.training_topic)}</td>
                            <td style="white-space: wrap;">
                                ${row.training_date_fixed ?
                                    `<span title="Tanggal Pasti ${escapeHtml(row.training_date_fixed)}" style="color: #2e7d32;"><i class="fas fa-calendar-check" style="color: #2e7d32;"></i> ${escapeHtml(row.training_date_fixed)}</span>` :
                                    row.training_date_start && row.training_date_end ?
                                        `<span title="Tanggal Pasti ${escapeHtml(row.training_date_start)} s/d ${escapeHtml(row.training_date_end)}" style="color: #2e7d32;"><i class="fas fa-calendar-week" style="color: #2e7d32;"></i> ${escapeHtml(row.training_date_start)} s/d ${escapeHtml(row.training_date_end)}</span>` :
                                        row.training_date ?
                                            `<span title="Tanggal Usulan ${escapeHtml(row.training_date)}" style="color: #f57c00;"><i class="fas fa-calendar-alt" style="color: #f57c00;"></i> ${escapeHtml(row.training_date)}</span>` :
                                            `<span style="color: #999;"><i class="fas fa-calendar-times" style="color: #999;"></i> Belum ditentukan</span>`
                                }
                            </td>
                            <td style="white-space: wrap;">
                                <span class="status-badge status-${(row.status || 'pending').toLowerCase()}">
                                    ${getStatusIcon(row.status || 'pending', 'external')}
                                    <span>${(row.status || 'pending').charAt(0).toUpperCase() + (row.status || 'pending').slice(1)}</span>
                                </span>
                            </td>
                            <td style="text-align: center;">
                                <div class="action-buttons">
                                    <a href="detail_training.php?id=${row.id}" class="btn btn-view" title="Lihat Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit_training.php?id=${row.id}" class="btn btn-edit" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="javascript:void(0)"
                                       class="btn btn-delete"
                                       onclick="confirmDelete(${row.id})"
                                       title="Hapus">
                                       <i class="fas fa-trash-alt"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    document.getElementById('searchResults').innerHTML = tableHTML;
}

// Fungsi untuk escape HTML
function escapeHtml(unsafe) {
    // Handle null, undefined, or non-string values
    if (unsafe === null || unsafe === undefined) {
        return '';
    }

    // Convert to string if not already
    const str = String(unsafe);

    return str
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// Global variable to track current training type
let currentTrainingType = 'external';

// Event listeners
document.getElementById('searchInput').addEventListener('input', debounce(() => fetchFilteredData(), 300));
document.getElementById('statusFilter').addEventListener('change', fetchFilteredData);
document.getElementById('dateFrom').addEventListener('change', fetchFilteredData);
document.getElementById('dateTo').addEventListener('change', fetchFilteredData);

// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check URL parameters for initial tab and date
    const urlParams = new URLSearchParams(window.location.search);
    const initialTab = urlParams.get('training_type') || 'external';
    const dateParam = urlParams.get('date');

    // Initialize tabs
    initializeTabs();

    // Set date filters if date parameter exists
    if (dateParam) {
        document.getElementById('dateFrom').value = dateParam;
        document.getElementById('dateTo').value = dateParam;
    }

    // Set initial tab based on URL parameter
    if (initialTab === 'internal') {
        switchTab('internal');
    } else {
        switchTab('external');
    }

    // Auto-open add modal if date parameter exists and training_type is internal
    <?php if ($auto_open_modal): ?>
    setTimeout(function() {
        const addModal = new bootstrap.Modal(document.getElementById('addInternalTrainingModal'));
        addModal.show();
    }, 500); // Small delay to ensure tab switching is complete
    <?php endif; ?>

    // Auto-open edit modal if id parameter exists and training_type is internal
    <?php if ($auto_open_edit_modal && $edit_training_data): ?>
    setTimeout(function() {
        // Pre-fill edit modal with training data
        const editData = <?= json_encode($edit_training_data) ?>;

        document.getElementById('edit_training_id').value = editData.id;
        document.getElementById('edit_training_topic').value = editData.training_topic || '';
        document.getElementById('edit_training_description').value = editData.training_description || '';
        document.getElementById('edit_start_date').value = editData.start_date || '';
        document.getElementById('edit_end_date').value = editData.end_date || '';
        document.getElementById('edit_training_time_start').value = editData.training_time_start || '';
        document.getElementById('edit_training_time_end').value = editData.training_time_end || '';
        document.getElementById('edit_location').value = editData.location || '';
        document.getElementById('edit_trainer_name').value = editData.trainer_name || '';
        document.getElementById('edit_max_participants').value = editData.max_participants || '';
        document.getElementById('edit_status').value = editData.status || 'Active';

        // Open edit modal
        const editModal = new bootstrap.Modal(document.getElementById('editInternalTrainingModal'));
        editModal.show();
    }, 500); // Small delay to ensure tab switching is complete
    <?php endif; ?>

    // Handle browser back/forward buttons
    window.addEventListener('popstate', function(event) {
        const urlParams = new URLSearchParams(window.location.search);
        const tabType = urlParams.get('training_type') || 'external';

        // Switch to the tab from URL without updating history again
        switchTabSilent(tabType);
    });
});

function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabType = this.dataset.tab;
            switchTab(tabType);
        });
    });
}

function switchTab(tabType) {
    // Update URL without reload
    const url = new URL(window.location);
    url.searchParams.set('training_type', tabType);
    window.history.pushState({}, '', url);

    // Perform the actual tab switch
    switchTabSilent(tabType);
}

function switchTabSilent(tabType) {
    // Update global variable
    currentTrainingType = tabType;

    // Update hidden input
    document.getElementById('trainingTypeInput').value = tabType;

    // Update tab appearance
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabType}"]`).classList.add('active');

    // Update search placeholder
    const searchInput = document.getElementById('searchInput');
    if (tabType === 'internal') {
        searchInput.placeholder = 'Topik/Trainer/Lokasi';
    } else {
        searchInput.placeholder = 'Nama/Topic/Email';
    }

    // Update status filter options
    updateStatusFilterOptions(tabType);

    // Update action buttons visibility
    updateActionButtons(tabType);

    // Clear search and filters (but preserve date if it came from URL parameter)
    searchInput.value = '';
    document.getElementById('statusFilter').value = '';

    // Only clear date filters if there's no date parameter in URL
    const urlParams = new URLSearchParams(window.location.search);
    const dateParam = urlParams.get('date');
    if (!dateParam) {
        document.getElementById('dateFrom').value = '';
        document.getElementById('dateTo').value = '';
    }

    // Fetch new data
    fetchFilteredData();
    updateSummaryCards();
}

function updateStatusFilterOptions(tabType) {
    const statusFilter = document.getElementById('statusFilter');
    statusFilter.innerHTML = '<option value="">Semua Status</option>';

    if (tabType === 'internal') {
        statusFilter.innerHTML += `
            <option value="Active">Active</option>
            <option value="Completed">Completed</option>
            <option value="Cancelled">Cancelled</option>
        `;
    } else {
        statusFilter.innerHTML += `
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="completed">Completed</option>
            <option value="rejected">Rejected</option>
        `;
    }
}

function updateActionButtons(tabType) {
    const addInternalBtn = document.getElementById('addInternalTrainingBtn');

    if (tabType === 'internal') {
        addInternalBtn.style.display = 'inline-block';
    } else {
        addInternalBtn.style.display = 'none';
    }
}

// Reset filter
document.getElementById('resetFilter').addEventListener('click', () => {
    document.getElementById('filterForm').reset();
    fetchFilteredData();
});

// Initial load
fetchFilteredData();

// Fungsi untuk konfirmasi penghapusan
function confirmDelete(id) {
    showConfirmDialog(`
        <div style="text-align: center; margin-bottom: 15px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 3em; color: #dc3545; margin-bottom: 15px;"></i>
            <h4 style="margin: 0 0 10px; font-size: 1.2em;">Konfirmasi Penghapusan</h4>
            <p style="margin: 0; color: #6c757d;">Apakah Anda yakin ingin menghapus training ini?</p>
            <p style="margin: 10px 0 0; font-size: 0.9em; color: #dc3545;">Tindakan ini tidak dapat dibatalkan.</p>
        </div>
    `, () => {
        // Show loading before redirect
        showToast('Menghapus training...', 'info');

        // Add small delay for better UX
        setTimeout(() => {
            window.location.href = `delete_training.php?id=${id}`;
        }, 500);
    });
}

// Function to update summary cards dynamically
async function updateSummaryCards() {
    try {
        const response = await fetch(`get_training_summary.php?training_type=${currentTrainingType}`);
        const data = await response.json();

        if (data.error) {
            console.error('Error fetching summary:', data.error);
            return;
        }

        // Update summary card labels and counts based on training type
        if (currentTrainingType === 'internal') {
            // Internal training labels
            document.querySelector('.summary-card.total .card-title').textContent = 'Total Training';
            document.querySelector('.summary-card.pending .card-title').textContent = 'Scheduled';
            document.querySelector('.summary-card.approved .card-title').textContent = 'Active';
            document.querySelector('.summary-card.completed .card-title').textContent = 'Completed';
            document.querySelector('.summary-card.rejected .card-title').textContent = 'Cancelled';
        } else {
            // External training labels
            document.querySelector('.summary-card.total .card-title').textContent = 'Total Pengajuan';
            document.querySelector('.summary-card.pending .card-title').textContent = 'Menunggu Persetujuan';
            document.querySelector('.summary-card.approved .card-title').textContent = 'Pengajuan Disetujui';
            document.querySelector('.summary-card.completed .card-title').textContent = 'Training Selesai';
            document.querySelector('.summary-card.rejected .card-title').textContent = 'Pengajuan Dibatalkan';
        }

        // Update summary cards counts
        document.querySelector('.summary-card.total .count').textContent = data.total || 0;
        document.querySelector('.summary-card.pending .count').textContent = data.pending || 0;
        document.querySelector('.summary-card.approved .count').textContent = data.approved || 0;
        document.querySelector('.summary-card.completed .count').textContent = data.completed || 0;
        document.querySelector('.summary-card.rejected .count').textContent = data.rejected || 0;

        // Update tab counts
        document.getElementById('externalCount').textContent = data.external_total || 0;
        document.getElementById('internalCount').textContent = data.internal_total || 0;

    } catch (error) {
        console.error('Error updating summary cards:', error);
    }
}

// Helper function to create links to specific tabs
function createTabLink(tabType, baseUrl = 'training_management.php') {
    return `${baseUrl}?training_type=${tabType}`;
}

// Helper function to create edit link for internal training
function createEditLink(trainingId, baseUrl = 'training_management.php') {
    return `${baseUrl}?training_type=internal&id=${trainingId}`;
}

// Helper function to create add training link with date
function createAddTrainingLink(date, baseUrl = 'training_management.php') {
    return `${baseUrl}?training_type=internal&date=${date}&auto_open=1`;
}

// Helper function to switch to tab programmatically
function goToTab(tabType) {
    switchTab(tabType);
}

// Global functions for external use
window.goToInternalTraining = function() {
    switchTab('internal');
};

window.goToExternalTraining = function() {
    switchTab('external');
};

window.goToEditTraining = function(trainingId) {
    window.location.href = createEditLink(trainingId);
};

window.goToAddTraining = function(date) {
    window.location.href = createAddTrainingLink(date);
};

// Internal Training Management Functions
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit internal training
    document.addEventListener('click', function(e) {
        if (e.target.closest('.edit-internal-training')) {
            const btn = e.target.closest('.edit-internal-training');
            const modal = document.getElementById('editInternalTrainingModal');

            // Populate modal fields
            document.getElementById('edit_training_id').value = btn.dataset.id;
            document.getElementById('edit_training_topic').value = btn.dataset.topic;
            document.getElementById('edit_training_description').value = btn.dataset.description;
            document.getElementById('edit_start_date').value = btn.dataset.startDate;
            document.getElementById('edit_end_date').value = btn.dataset.endDate;
            document.getElementById('edit_training_time_start').value = btn.dataset.timeStart;
            document.getElementById('edit_training_time_end').value = btn.dataset.timeEnd;
            document.getElementById('edit_location').value = btn.dataset.location;
            document.getElementById('edit_trainer_name').value = btn.dataset.trainer;
            document.getElementById('edit_max_participants').value = btn.dataset.max;
            document.getElementById('edit_status').value = btn.dataset.status;

            // Show modal
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }

        // Handle delete internal training
        if (e.target.closest('.delete-internal-training')) {
            const btn = e.target.closest('.delete-internal-training');
            const modal = document.getElementById('deleteInternalTrainingModal');

            // Populate modal fields
            document.getElementById('delete_training_id').value = btn.dataset.id;
            document.getElementById('delete_training_topic').textContent = btn.dataset.topic;

            // Show modal
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }
    });

    // Date validation for internal training forms
    const startDateInputs = document.querySelectorAll('#start_date, #edit_start_date');
    const endDateInputs = document.querySelectorAll('#end_date, #edit_end_date');

    startDateInputs.forEach((startInput, index) => {
        const endInput = endDateInputs[index];
        if (endInput) {
            startInput.addEventListener('change', function() {
                if (endInput.value && endInput.value < this.value) {
                    endInput.value = this.value;
                }
            });

            endInput.addEventListener('change', function() {
                if (this.value === startInput.value) {
                    this.value = '';
                }
            });
        }
    });
});
</script>





