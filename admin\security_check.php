<?php
/**
 * File untuk melakukan pemeriksaan keamanan website
 * Jalankan file ini secara berkala untuk memeriksa keamanan website
 */

// Mulai session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include konfigurasi database
require_once '../config/config.php';

// Pastikan hanya admin yang bisa akses
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Fungsi untuk memeriksa keamanan
function check_security() {
    global $conn;
    
    $results = [];
    $total_issues = 0;
    
    // 1. Periksa apakah file .htaccess ada
    $htaccess_file = '../.htaccess';
    if (file_exists($htaccess_file)) {
        $results[] = [
            'check' => 'File .htaccess',
            'status' => 'OK',
            'message' => 'File .htaccess ditemukan.',
            'severity' => 'low'
        ];
    } else {
        $results[] = [
            'check' => 'File .htaccess',
            'status' => 'WARNING',
            'message' => 'File .htaccess tidak ditemukan. Ini dapat mengurangi keamanan website.',
            'severity' => 'medium'
        ];
        $total_issues++;
    }
    
    // 2. Periksa apakah direktori logs ada dan dapat ditulis
    $logs_dir = '../logs';
    if (file_exists($logs_dir) && is_dir($logs_dir)) {
        if (is_writable($logs_dir)) {
            $results[] = [
                'check' => 'Direktori logs',
                'status' => 'OK',
                'message' => 'Direktori logs ditemukan dan dapat ditulis.',
                'severity' => 'low'
            ];
        } else {
            $results[] = [
                'check' => 'Direktori logs',
                'status' => 'WARNING',
                'message' => 'Direktori logs ditemukan tetapi tidak dapat ditulis. Ini dapat menyebabkan log tidak dapat disimpan.',
                'severity' => 'medium'
            ];
            $total_issues++;
        }
    } else {
        $results[] = [
            'check' => 'Direktori logs',
            'status' => 'ERROR',
            'message' => 'Direktori logs tidak ditemukan. Ini dapat menyebabkan log tidak dapat disimpan.',
            'severity' => 'high'
        ];
        $total_issues++;
    }
    
    // 3. Periksa apakah tabel keamanan ada
    $security_tables = ['login_attempts', 'rate_limits', 'security_logs', 'csrf_tokens'];
    foreach ($security_tables as $table) {
        $query = "SHOW TABLES LIKE '$table'";
        $result = $conn->query($query);
        
        if ($result->num_rows > 0) {
            $results[] = [
                'check' => "Tabel $table",
                'status' => 'OK',
                'message' => "Tabel $table ditemukan.",
                'severity' => 'low'
            ];
        } else {
            $results[] = [
                'check' => "Tabel $table",
                'status' => 'ERROR',
                'message' => "Tabel $table tidak ditemukan. Ini dapat mengurangi keamanan website.",
                'severity' => 'high'
            ];
            $total_issues++;
        }
    }
    
    // 4. Periksa apakah kolom keamanan ada di tabel users
    $security_columns = [
        'failed_attempts', 'last_failed_attempt', 'account_locked', 'lock_expires',
        'password_changed_at', 'reset_token', 'reset_token_expires', 'last_login',
        'last_login_ip', 'require_password_change', 'two_factor_enabled', 'two_factor_secret'
    ];
    
    foreach ($security_columns as $column) {
        $query = "SHOW COLUMNS FROM users LIKE '$column'";
        $result = $conn->query($query);
        
        if ($result->num_rows > 0) {
            $results[] = [
                'check' => "Kolom $column di tabel users",
                'status' => 'OK',
                'message' => "Kolom $column ditemukan di tabel users.",
                'severity' => 'low'
            ];
        } else {
            $results[] = [
                'check' => "Kolom $column di tabel users",
                'status' => 'ERROR',
                'message' => "Kolom $column tidak ditemukan di tabel users. Ini dapat mengurangi keamanan website.",
                'severity' => 'high'
            ];
            $total_issues++;
        }
    }
    
    // 5. Periksa apakah file keamanan ada
    $security_files = [
        '../config/security.php' => 'File keamanan utama',
        '../config/security_monitor.php' => 'File monitor keamanan',
        '../config/activity_logger.php' => 'File pencatat aktivitas'
    ];
    
    foreach ($security_files as $file => $description) {
        if (file_exists($file)) {
            $results[] = [
                'check' => $description,
                'status' => 'OK',
                'message' => "$description ditemukan.",
                'severity' => 'low'
            ];
        } else {
            $results[] = [
                'check' => $description,
                'status' => 'WARNING',
                'message' => "$description tidak ditemukan. Ini dapat mengurangi keamanan website.",
                'severity' => 'medium'
            ];
            $total_issues++;
        }
    }
    
    // 6. Periksa apakah ada akun dengan password default atau lemah
    $query = "SELECT COUNT(*) as count FROM users WHERE password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'";
    $result = $conn->query($query);
    $default_password_count = $result->fetch_assoc()['count'];
    
    if ($default_password_count > 0) {
        $results[] = [
            'check' => 'Password default',
            'status' => 'ERROR',
            'message' => "Ditemukan $default_password_count akun dengan password default. Ini dapat membahayakan keamanan website.",
            'severity' => 'high'
        ];
        $total_issues++;
    } else {
        $results[] = [
            'check' => 'Password default',
            'status' => 'OK',
            'message' => "Tidak ditemukan akun dengan password default.",
            'severity' => 'low'
        ];
    }
    
    // 7. Periksa apakah ada akun admin yang terkunci
    $query = "SELECT COUNT(*) as count FROM users WHERE role_id = 99 AND account_locked = 1";
    $result = $conn->query($query);
    $locked_admin_count = $result->fetch_assoc()['count'];
    
    if ($locked_admin_count > 0) {
        $results[] = [
            'check' => 'Akun admin terkunci',
            'status' => 'WARNING',
            'message' => "Ditemukan $locked_admin_count akun admin yang terkunci. Ini dapat menyebabkan admin tidak dapat mengakses website.",
            'severity' => 'medium'
        ];
        $total_issues++;
    } else {
        $results[] = [
            'check' => 'Akun admin terkunci',
            'status' => 'OK',
            'message' => "Tidak ditemukan akun admin yang terkunci.",
            'severity' => 'low'
        ];
    }
    
    // 8. Periksa apakah ada percobaan login yang mencurigakan
    $query = "SELECT COUNT(*) as count FROM login_attempts WHERE success = 0 AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 DAY)";
    $result = $conn->query($query);
    $suspicious_login_count = $result->fetch_assoc()['count'];
    
    if ($suspicious_login_count > 10) {
        $results[] = [
            'check' => 'Percobaan login mencurigakan',
            'status' => 'WARNING',
            'message' => "Ditemukan $suspicious_login_count percobaan login yang gagal dalam 24 jam terakhir. Ini dapat mengindikasikan serangan brute force.",
            'severity' => 'medium'
        ];
        $total_issues++;
    } else {
        $results[] = [
            'check' => 'Percobaan login mencurigakan',
            'status' => 'OK',
            'message' => "Tidak ditemukan percobaan login yang mencurigakan dalam 24 jam terakhir.",
            'severity' => 'low'
        ];
    }
    
    // 9. Periksa apakah ada serangan yang terdeteksi
    $query = "SELECT COUNT(*) as count FROM security_logs WHERE event_type LIKE 'ATTACK%' AND created_at > DATE_SUB(NOW(), INTERVAL 1 DAY)";
    $result = $conn->query($query);
    $attack_count = $result->fetch_assoc()['count'];
    
    if ($attack_count > 0) {
        $results[] = [
            'check' => 'Serangan terdeteksi',
            'status' => 'ERROR',
            'message' => "Ditemukan $attack_count serangan yang terdeteksi dalam 24 jam terakhir. Ini dapat mengindikasikan upaya peretasan.",
            'severity' => 'high'
        ];
        $total_issues++;
    } else {
        $results[] = [
            'check' => 'Serangan terdeteksi',
            'status' => 'OK',
            'message' => "Tidak ditemukan serangan yang terdeteksi dalam 24 jam terakhir.",
            'severity' => 'low'
        ];
    }
    
    // 10. Periksa apakah ada rate limiting yang aktif
    $query = "SELECT COUNT(*) as count FROM rate_limits WHERE attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
    $result = $conn->query($query);
    $rate_limit_count = $result->fetch_assoc()['count'];
    
    if ($rate_limit_count > 50) {
        $results[] = [
            'check' => 'Rate limiting',
            'status' => 'WARNING',
            'message' => "Ditemukan $rate_limit_count rate limiting dalam 1 jam terakhir. Ini dapat mengindikasikan aktivitas yang mencurigakan.",
            'severity' => 'medium'
        ];
        $total_issues++;
    } else {
        $results[] = [
            'check' => 'Rate limiting',
            'status' => 'OK',
            'message' => "Tidak ditemukan rate limiting yang berlebihan dalam 1 jam terakhir.",
            'severity' => 'low'
        ];
    }
    
    return [
        'results' => $results,
        'total_issues' => $total_issues,
        'high_severity' => count(array_filter($results, function($item) { return $item['severity'] == 'high' && $item['status'] != 'OK'; })),
        'medium_severity' => count(array_filter($results, function($item) { return $item['severity'] == 'medium' && $item['status'] != 'OK'; })),
        'low_severity' => count(array_filter($results, function($item) { return $item['severity'] == 'low' && $item['status'] != 'OK'; })),
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

// Jalankan pemeriksaan keamanan
$security_check = check_security();

// Tampilkan halaman
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .dashboard-container {
        padding: 20px;
    }
    
    .security-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .security-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .security-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }
    
    .security-summary {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .summary-item {
        flex: 1;
        padding: 15px;
        border-radius: 5px;
        text-align: center;
    }
    
    .summary-item.high {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    
    .summary-item.medium {
        background-color: #fff3cd;
        border: 1px solid #ffeeba;
        color: #856404;
    }
    
    .summary-item.low {
        background-color: #d1e7dd;
        border: 1px solid #badbcc;
        color: #0f5132;
    }
    
    .summary-item h3 {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .summary-item p {
        font-size: 14px;
        margin-bottom: 0;
    }
    
    .status-ok {
        color: #28a745;
    }
    
    .status-warning {
        color: #ffc107;
    }
    
    .status-error {
        color: #dc3545;
    }
    
    .severity-high {
        background-color: #f8d7da;
    }
    
    .severity-medium {
        background-color: #fff3cd;
    }
    
    .severity-low {
        background-color: #d1e7dd;
    }
    
    @media (max-width: 768px) {
        .security-summary {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>
<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container-fluid dashboard-container">
    <h1 class="mb-4"><i class="fas fa-shield-alt"></i> Pemeriksaan Keamanan</h1>
    
    <div class="security-card">
        <div class="security-header">
            <div class="security-title">Ringkasan Pemeriksaan Keamanan</div>
            <div>
                <span class="badge bg-secondary">Waktu: <?= $security_check['timestamp'] ?></span>
                <a href="?refresh=1" class="btn btn-sm btn-primary ms-2">
                    <i class="fas fa-sync-alt"></i> Refresh
                </a>
                <a href="security_monitoring.php" class="btn btn-sm btn-primary ms-2">
                    <i class="fas fa-eye"></i> Lihat Monitoring Keamanan
                </a>
            </div>
        </div>
        
        <div class="security-summary">
            <div class="summary-item high">
                <h3><?= $security_check['high_severity'] ?></h3>
                <p>Masalah Kritis</p>
            </div>
            <div class="summary-item medium">
                <h3><?= $security_check['medium_severity'] ?></h3>
                <p>Masalah Sedang</p>
            </div>
            <div class="summary-item low">
                <h3><?= $security_check['low_severity'] ?></h3>
                <p>Masalah Ringan</p>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Pemeriksaan</th>
                        <th>Status</th>
                        <th>Pesan</th>
                        <th>Tingkat Keparahan</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($security_check['results'] as $result): ?>
                    <tr class="<?= $result['status'] != 'OK' ? 'severity-' . $result['severity'] : '' ?>">
                        <td><?= $result['check'] ?></td>
                        <td class="status-<?= strtolower($result['status']) == 'ok' ? 'ok' : (strtolower($result['status']) == 'warning' ? 'warning' : 'error') ?>">
                            <?= $result['status'] ?>
                        </td>
                        <td><?= $result['message'] ?></td>
                        <td>
                            <?php if ($result['severity'] == 'high'): ?>
                                <span class="badge bg-danger">Tinggi</span>
                            <?php elseif ($result['severity'] == 'medium'): ?>
                                <span class="badge bg-warning text-dark">Sedang</span>
                            <?php else: ?>
                                <span class="badge bg-success">Rendah</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="security-card">
        <div class="security-header">
            <div class="security-title">Rekomendasi Keamanan</div>
        </div>
        
        <div class="accordion" id="securityRecommendations">
            <?php if ($security_check['high_severity'] > 0): ?>
            <div class="accordion-item">
                <h2 class="accordion-header" id="headingHigh">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseHigh" aria-expanded="true" aria-controls="collapseHigh">
                        <span class="badge bg-danger me-2"><?= $security_check['high_severity'] ?></span> Masalah Kritis
                    </button>
                </h2>
                <div id="collapseHigh" class="accordion-collapse collapse show" aria-labelledby="headingHigh" data-bs-parent="#securityRecommendations">
                    <div class="accordion-body">
                        <ul class="list-group">
                            <?php foreach ($security_check['results'] as $result): ?>
                                <?php if ($result['severity'] == 'high' && $result['status'] != 'OK'): ?>
                                <li class="list-group-item list-group-item-danger">
                                    <strong><?= $result['check'] ?>:</strong> <?= $result['message'] ?>
                                </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if ($security_check['medium_severity'] > 0): ?>
            <div class="accordion-item">
                <h2 class="accordion-header" id="headingMedium">
                    <button class="accordion-button <?= $security_check['high_severity'] > 0 ? 'collapsed' : '' ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMedium" aria-expanded="<?= $security_check['high_severity'] > 0 ? 'false' : 'true' ?>" aria-controls="collapseMedium">
                        <span class="badge bg-warning text-dark me-2"><?= $security_check['medium_severity'] ?></span> Masalah Sedang
                    </button>
                </h2>
                <div id="collapseMedium" class="accordion-collapse collapse <?= $security_check['high_severity'] > 0 ? '' : 'show' ?>" aria-labelledby="headingMedium" data-bs-parent="#securityRecommendations">
                    <div class="accordion-body">
                        <ul class="list-group">
                            <?php foreach ($security_check['results'] as $result): ?>
                                <?php if ($result['severity'] == 'medium' && $result['status'] != 'OK'): ?>
                                <li class="list-group-item list-group-item-warning">
                                    <strong><?= $result['check'] ?>:</strong> <?= $result['message'] ?>
                                </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if ($security_check['low_severity'] > 0): ?>
            <div class="accordion-item">
                <h2 class="accordion-header" id="headingLow">
                    <button class="accordion-button <?= $security_check['high_severity'] > 0 || $security_check['medium_severity'] > 0 ? 'collapsed' : '' ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapseLow" aria-expanded="<?= $security_check['high_severity'] > 0 || $security_check['medium_severity'] > 0 ? 'false' : 'true' ?>" aria-controls="collapseLow">
                        <span class="badge bg-success me-2"><?= $security_check['low_severity'] ?></span> Masalah Ringan
                    </button>
                </h2>
                <div id="collapseLow" class="accordion-collapse collapse <?= $security_check['high_severity'] > 0 || $security_check['medium_severity'] > 0 ? '' : 'show' ?>" aria-labelledby="headingLow" data-bs-parent="#securityRecommendations">
                    <div class="accordion-body">
                        <ul class="list-group">
                            <?php foreach ($security_check['results'] as $result): ?>
                                <?php if ($result['severity'] == 'low' && $result['status'] != 'OK'): ?>
                                <li class="list-group-item list-group-item-success">
                                    <strong><?= $result['check'] ?>:</strong> <?= $result['message'] ?>
                                </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if ($security_check['total_issues'] == 0): ?>
            <div class="alert alert-success mt-3">
                <i class="fas fa-check-circle"></i> Selamat! Tidak ditemukan masalah keamanan pada website Anda.
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>
</body>
</html>
