<?php
/**
 * Auto Backup Script
 *
 * This script handles automatic database backups based on settings.
 * It can be called via cron job or manually.
 */

// No direct access
if (count(get_included_files()) == 1) {
    // Called directly from browser or command line
    define('DIRECT_ACCESS', true);
} else {
    define('DIRECT_ACCESS', false);
}

// Include configuration
$configPath = dirname(__DIR__) . '/config/config.php';
if (file_exists($configPath)) {
    include_once $configPath;
} else {
    die("Configuration file not found");
}

/**
 * Function to create database backup
 *
 * @param array $settings Backup settings
 * @return array Result of backup operation
 */
function createDatabaseBackup($settings) {
    global $db_host, $db_user, $db_pass, $db_name, $conn;

    // Get backup settings
    $backupLocation = $settings['backup_location'] ?? '../backups';
    $compressBackup = ($settings['compress_backup'] ?? '1') == '1';
    $backupRetention = intval($settings['backup_retention'] ?? 7);

    // Check if location is relative or absolute path
    if (strpos($backupLocation, '/') !== 0 && strpos($backupLocation, ':') !== 1) {
        // Relative path, add base path
        $fullPath = dirname(__DIR__) . '/' . ltrim($backupLocation, '/');
    } else {
        // Absolute path
        $fullPath = $backupLocation;
    }

    // Ensure backup directory exists
    if (!file_exists($fullPath)) {
        if (!mkdir($fullPath, 0755, true)) {
            return [
                'success' => false,
                'message' => 'Backup directory not found and could not be created'
            ];
        }
    }

    // Create backup filename
    $timestamp = date('Y-m-d_H-i-s');
    $backupFileName = "db_{$db_name}_backup_{$timestamp}.sql";
    $backupFilePath = "{$fullPath}/{$backupFileName}";

    // Try to use mysqldump first (faster and more reliable)
    $mysqldump_success = false;
    $mysqldump_output = [];
    $mysqldump_return = 0;

    // Command for database backup
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        // Windows - Try to find mysqldump in common locations
        $mysqldump_paths = [
            'mysqldump',                                // In PATH
            'C:\\xampp\\mysql\\bin\\mysqldump.exe',     // XAMPP
            'C:\\laragon\\bin\\mysql\\bin\\mysqldump.exe', // Laragon
            'C:\\wamp64\\bin\\mysql\\mysql5.7.36\\bin\\mysqldump.exe', // WAMP
            'C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqldump.exe' // Standard MySQL install
        ];

        $mysqldump_cmd = 'mysqldump'; // Default
        foreach ($mysqldump_paths as $path) {
            if (file_exists($path) || (strpos($path, 'mysqldump') === 0)) {
                $mysqldump_cmd = $path;
                break;
            }
        }

        $command = "\"{$mysqldump_cmd}\" -h {$db_host} -u {$db_user}";
        if (!empty($db_pass)) {
            $command .= " -p\"{$db_pass}\"";
        }
        $command .= " {$db_name} > \"{$backupFilePath}\" 2>&1";
    } else {
        // Linux/Unix/Mac
        $command = "mysqldump -h {$db_host} -u {$db_user}";
        if (!empty($db_pass)) {
            $command .= " -p'{$db_pass}'";
        }
        $command .= " {$db_name} > '{$backupFilePath}' 2>&1";
    }

    // Log the command for debugging (without password)
    $log_command = preg_replace('/-p"[^"]*"/', '-p"***"', $command);
    error_log("Executing backup command: " . $log_command);

    // Execute backup command
    exec($command, $mysqldump_output, $mysqldump_return);

    // Check if mysqldump was successful
    if ($mysqldump_return === 0 && file_exists($backupFilePath) && filesize($backupFilePath) > 0) {
        $mysqldump_success = true;
    } else {
        error_log("Mysqldump failed, trying PHP backup method");
    }

    // If mysqldump failed, try PHP backup method
    if (!$mysqldump_success) {
        try {
            // Create backup file
            $backup_file = fopen($backupFilePath, 'w');
            if (!$backup_file) {
                throw new Exception("Could not create backup file: $backupFilePath");
            }

            // Add header
            fwrite($backup_file, "-- Database Backup for {$db_name}\n");
            fwrite($backup_file, "-- Generated on " . date('Y-m-d H:i:s') . "\n");
            fwrite($backup_file, "-- Using PHP Backup Method\n\n");

            // Set database
            fwrite($backup_file, "USE `{$db_name}`;\n\n");

            // Get all tables
            $tables_result = $conn->query("SHOW TABLES");
            if (!$tables_result) {
                throw new Exception("Failed to get tables: " . $conn->error);
            }

            $tables = [];
            while ($row = $tables_result->fetch_array()) {
                $tables[] = $row[0];
            }

            // Process each table
            foreach ($tables as $table) {
                // Get create table statement
                $create_table_result = $conn->query("SHOW CREATE TABLE `{$table}`");
                if (!$create_table_result) {
                    throw new Exception("Failed to get CREATE TABLE for {$table}: " . $conn->error);
                }

                $create_table_row = $create_table_result->fetch_array();
                $create_table_sql = $create_table_row[1];

                // Write create table statement
                fwrite($backup_file, "-- Table structure for table `{$table}`\n");
                fwrite($backup_file, "DROP TABLE IF EXISTS `{$table}`;\n");
                fwrite($backup_file, $create_table_sql . ";\n\n");

                // Get table data
                $data_result = $conn->query("SELECT * FROM `{$table}`");
                if (!$data_result) {
                    throw new Exception("Failed to get data for {$table}: " . $conn->error);
                }

                if ($data_result->num_rows > 0) {
                    fwrite($backup_file, "-- Data for table `{$table}`\n");
                    fwrite($backup_file, "INSERT INTO `{$table}` VALUES\n");

                    $row_count = 0;
                    while ($row = $data_result->fetch_assoc()) {
                        $row_count++;

                        // Start row
                        fwrite($backup_file, "(");

                        // Process each column
                        $columns = [];
                        foreach ($row as $column) {
                            if ($column === null) {
                                $columns[] = "NULL";
                            } else {
                                $columns[] = "'" . $conn->real_escape_string($column) . "'";
                            }
                        }

                        // Join columns and end row
                        fwrite($backup_file, implode(",", $columns));

                        // If this is the last row, end with semicolon, otherwise comma
                        if ($row_count < $data_result->num_rows) {
                            fwrite($backup_file, "),\n");
                        } else {
                            fwrite($backup_file, ");\n\n");
                        }
                    }
                }
            }

            // Close file
            fclose($backup_file);

            // Check if backup was successful
            if (file_exists($backupFilePath) && filesize($backupFilePath) > 0) {
                error_log("PHP backup method successful");
            } else {
                throw new Exception("Backup file is empty or does not exist");
            }

        } catch (Exception $e) {
            error_log("PHP backup method failed: " . $e->getMessage());

            // Check if mysqldump is available
            $mysqldump_check = '';
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                exec('where mysqldump 2>&1', $check_output, $check_return);
                $mysqldump_check = implode("\n", $check_output);
            } else {
                exec('which mysqldump 2>&1', $check_output, $check_return);
                $mysqldump_check = implode("\n", $check_output);
            }

            // Check directory permissions
            $dir_writable = is_writable($fullPath) ? 'Yes' : 'No';

            return [
                'success' => false,
                'message' => 'Failed to create database backup: ' . $e->getMessage(),
                'output' => $mysqldump_output,
                'debug_info' => [
                    'mysqldump_check' => $mysqldump_check,
                    'directory_writable' => $dir_writable,
                    'backup_path' => $fullPath,
                    'command' => $log_command,
                    'php_error' => $e->getMessage()
                ]
            ];
        }
    }

    // Compress backup file if enabled
    if ($compressBackup && extension_loaded('zip')) {
        $zipFileName = "{$backupFilePath}.zip";
        $zip = new ZipArchive();

        if ($zip->open($zipFileName, ZipArchive::CREATE) === TRUE) {
            $zip->addFile($backupFilePath, $backupFileName);
            $zip->close();

            // Delete original SQL file if compression was successful
            if (file_exists($zipFileName)) {
                unlink($backupFilePath);
                $backupFilePath = $zipFileName;
                $backupFileName .= '.zip';
            }
        }
    }

    // Clean up old backups
    cleanupOldBackups($fullPath, $backupRetention);

    // Log backup activity
    logBackupActivity('Automatic backup created', $backupFilePath);

    return [
        'success' => true,
        'message' => 'Database backup created successfully',
        'file_path' => $backupFilePath,
        'file_name' => $backupFileName
    ];
}

/**
 * Function to clean up old backups
 *
 * @param string $backupDir Backup directory
 * @param int $retention Number of backups to keep
 */
function cleanupOldBackups($backupDir, $retention) {
    // Get all backup files
    $files = glob($backupDir . '/*.{sql,zip}', GLOB_BRACE);

    // Sort files by modification time (oldest first)
    usort($files, function($a, $b) {
        return filemtime($a) - filemtime($b);
    });

    // Keep only the specified number of recent backups
    $filesToDelete = count($files) - $retention;

    if ($filesToDelete > 0) {
        for ($i = 0; $i < $filesToDelete; $i++) {
            if (file_exists($files[$i])) {
                unlink($files[$i]);
                logBackupActivity('Old backup deleted', $files[$i]);
            }
        }
    }
}

/**
 * Function to log backup activity
 *
 * @param string $action Action description
 * @param string $filePath Path to backup file
 */
function logBackupActivity($action, $filePath) {
    global $conn;

    if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
        return false;
    }

    try {
        $query = "INSERT INTO activity_logs (user_id, action, category, timestamp)
                  VALUES (0, ?, 'backup', NOW())";
        $stmt = $conn->prepare($query);
        $actionWithPath = $action . ': ' . $filePath;
        $stmt->bind_param("s", $actionWithPath);
        return $stmt->execute();
    } catch (Exception $e) {
        error_log("Error logging backup activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Function to check if backup should run today
 *
 * @param string $schedule Backup schedule (daily, weekly, monthly)
 * @return bool True if backup should run today
 */
function shouldRunBackupToday($schedule) {
    switch ($schedule) {
        case 'daily':
            return true;

        case 'weekly':
            // Run on Sundays (0)
            return date('w') == 0;

        case 'monthly':
            // Run on the 1st day of the month
            return date('j') == 1;

        case 'disabled':
        default:
            return false;
    }
}

/**
 * Main function to run backup process
 */
function runBackup() {
    global $conn, $db_host, $db_user, $db_pass, $db_name;

    // Get backup settings
    $query = "SELECT backup_schedule, backup_time, backup_location,
              backup_retention, compress_backup FROM settings WHERE id = 1";
    $result = $conn->query($query);

    if (!$result) {
        return [
            'success' => false,
            'message' => 'Failed to retrieve backup settings'
        ];
    }

    $settings = $result->fetch_assoc();

    // Check if backup is disabled
    if ($settings['backup_schedule'] == 'disabled') {
        return [
            'success' => false,
            'message' => 'Automatic backup is disabled in settings'
        ];
    }

    // Check if backup should run today based on schedule
    if (!shouldRunBackupToday($settings['backup_schedule'])) {
        return [
            'success' => false,
            'message' => 'No backup scheduled for today'
        ];
    }

    // Try to use the simple backup method first
    try {
        // Include the simple backup script
        $simplePath = __DIR__ . '/simple_backup.php';
        if (file_exists($simplePath)) {
            // Get backup location
            $backupLocation = $settings['backup_location'] ?? '../backups';

            // Check if location is relative or absolute path
            if (strpos($backupLocation, '/') !== 0 && strpos($backupLocation, ':') !== 1) {
                // Relative path, add base path
                $fullPath = dirname(__DIR__) . '/' . ltrim($backupLocation, '/');
            } else {
                // Absolute path
                $fullPath = $backupLocation;
            }

            // Create a function to run the simple backup without output
            function runSimpleBackup($db_host, $db_user, $db_pass, $db_name, $conn, $backupDir) {
                // Create backup filename
                $timestamp = date('Y-m-d_H-i-s');
                $backupFileName = "db_{$db_name}_backup_{$timestamp}.sql";
                $backupFilePath = "{$backupDir}/{$backupFileName}";

                // Create backup file
                $backup_file = fopen($backupFilePath, 'w');
                if (!$backup_file) {
                    throw new Exception("Could not create backup file: $backupFilePath");
                }

                // Add header
                fwrite($backup_file, "-- Database Backup for {$db_name}\n");
                fwrite($backup_file, "-- Generated on " . date('Y-m-d H:i:s') . "\n");
                fwrite($backup_file, "-- Simple PHP Backup\n\n");

                // Set database
                fwrite($backup_file, "USE `{$db_name}`;\n\n");

                // Get all tables
                $tables_result = $conn->query("SHOW TABLES");
                if (!$tables_result) {
                    throw new Exception("Failed to get tables: " . $conn->error);
                }

                $tables = [];
                while ($row = $tables_result->fetch_array()) {
                    $tables[] = $row[0];
                }

                // Process each table
                foreach ($tables as $table) {
                    // Get create table statement
                    $create_table_result = $conn->query("SHOW CREATE TABLE `{$table}`");
                    if (!$create_table_result) {
                        throw new Exception("Failed to get CREATE TABLE for {$table}: " . $conn->error);
                    }

                    $create_table_row = $create_table_result->fetch_array();
                    $create_table_sql = $create_table_row[1];

                    // Write create table statement
                    fwrite($backup_file, "-- Table structure for table `{$table}`\n");
                    fwrite($backup_file, "DROP TABLE IF EXISTS `{$table}`;\n");
                    fwrite($backup_file, $create_table_sql . ";\n\n");

                    // Get table data
                    $data_result = $conn->query("SELECT * FROM `{$table}`");
                    if (!$data_result) {
                        throw new Exception("Failed to get data for {$table}: " . $conn->error);
                    }

                    if ($data_result->num_rows > 0) {
                        fwrite($backup_file, "-- Data for table `{$table}`\n");
                        fwrite($backup_file, "INSERT INTO `{$table}` VALUES\n");

                        $row_count = 0;
                        $total_rows = $data_result->num_rows;

                        while ($row = $data_result->fetch_assoc()) {
                            $row_count++;

                            // Start row
                            fwrite($backup_file, "(");

                            // Process each column
                            $columns = [];
                            foreach ($row as $column) {
                                if ($column === null) {
                                    $columns[] = "NULL";
                                } else {
                                    $columns[] = "'" . $conn->real_escape_string($column) . "'";
                                }
                            }

                            // Join columns and end row
                            fwrite($backup_file, implode(",", $columns));

                            // If this is the last row, end with semicolon, otherwise comma
                            if ($row_count == $total_rows) {
                                fwrite($backup_file, ");\n\n");
                            } else {
                                fwrite($backup_file, "),\n");
                            }
                        }
                    }
                }

                // Close file
                fclose($backup_file);

                // Check if backup was successful
                if (file_exists($backupFilePath) && filesize($backupFilePath) > 0) {
                    return [
                        'success' => true,
                        'message' => 'Database backup created successfully',
                        'file_path' => $backupFilePath,
                        'file_name' => $backupFileName
                    ];
                } else {
                    throw new Exception("Backup file is empty or does not exist");
                }
            }

            // Run the simple backup
            $simpleResult = runSimpleBackup($db_host, $db_user, $db_pass, $db_name, $conn, $fullPath);

            // Process the result
            if ($simpleResult['success']) {
                // Compress backup file if enabled
                $backupFilePath = $simpleResult['file_path'];
                $backupFileName = $simpleResult['file_name'];

                if ($settings['compress_backup'] == '1' && extension_loaded('zip')) {
                    $zipFileName = "{$backupFilePath}.zip";
                    $zip = new ZipArchive();

                    if ($zip->open($zipFileName, ZipArchive::CREATE) === TRUE) {
                        $zip->addFile($backupFilePath, $backupFileName);
                        $zip->close();

                        // Delete original SQL file if compression was successful
                        if (file_exists($zipFileName)) {
                            unlink($backupFilePath);
                            $backupFilePath = $zipFileName;
                            $backupFileName .= '.zip';
                        }
                    }
                }

                // Clean up old backups
                $backupRetention = intval($settings['backup_retention'] ?? 7);
                cleanupOldBackups($fullPath, $backupRetention);

                // Log backup activity
                logBackupActivity('Automatic backup created', $backupFilePath);

                return [
                    'success' => true,
                    'message' => 'Database backup created successfully',
                    'file_path' => $backupFilePath,
                    'file_name' => $backupFileName
                ];
            }
        }
    } catch (Exception $e) {
        error_log("Simple backup method failed: " . $e->getMessage());
    }

    // If simple backup failed or file doesn't exist, fall back to the original method
    return createDatabaseBackup($settings);
}

// Run backup if accessed directly
if (DIRECT_ACCESS) {
    // Log execution for debugging
    $log_file = dirname(__DIR__) . '/backups/backup_debug.log';
    $log_message = date('Y-m-d H:i:s') . " - Auto backup script executed from " . (php_sapi_name() == 'cli' ? 'CLI' : 'Browser') . "\n";
    file_put_contents($log_file, $log_message, FILE_APPEND);

    // Make sure we have a database connection
    if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
        // Try to reconnect
        $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
        if ($conn->connect_error) {
            $error_message = "Database connection failed: " . $conn->connect_error;
            file_put_contents($log_file, date('Y-m-d H:i:s') . " - ERROR: " . $error_message . "\n", FILE_APPEND);

            if (php_sapi_name() == 'cli') {
                echo "ERROR: " . $error_message . "\n";
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => $error_message]);
            }
            exit;
        }
    }

    // Run the backup
    $result = runBackup();

    // Log the result
    $log_result = date('Y-m-d H:i:s') . " - " . ($result['success'] ? "SUCCESS: " : "ERROR: ") . $result['message'];
    if (isset($result['file_path']) && $result['success']) {
        $log_result .= " - File: " . $result['file_path'];
    }
    file_put_contents($log_file, $log_result . "\n", FILE_APPEND);

    // Output result based on request type
    if (php_sapi_name() == 'cli') {
        // Command line
        echo $result['success'] ? "SUCCESS: " : "ERROR: ";
        echo $result['message'] . "\n";

        if (isset($result['file_path']) && $result['success']) {
            echo "Backup saved to: " . $result['file_path'] . "\n";
        }
    } else {
        // Browser
        header('Content-Type: application/json');
        echo json_encode($result);
    }
}
