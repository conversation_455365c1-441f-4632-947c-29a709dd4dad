<?php
include '../config/config.php';
include '../config/security_helper.php';

// Inisialisasi variabel
$success = false;
$message = '';
$temp_password = '';
$reset_password = false;

// Cek apakah ada parameter debug_unlock dan identifier
if (isset($_GET['debug_unlock']) && $_GET['debug_unlock'] === 'true' && isset($_GET['identifier'])) {
    $identifier = $_GET['identifier'];

    // Cek apakah juga ingin reset password
    if (isset($_GET['reset_password']) && $_GET['reset_password'] === 'true') {
        $reset_password = true;
    }

    // Cari user berdasarkan identifier (bisa NIK, email, atau nama)
    $query = "SELECT id, name, nik, email, failed_attempts FROM users
              WHERE nik = ? OR email = ? OR name = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("sss", $identifier, $identifier, $identifier);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();

        // Jika reset password diminta
        if ($reset_password) {
            // Generate password sementara (kombinasi NIK + 4 digit random)
            $random_digits = sprintf("%04d", mt_rand(0, 9999));
            $temp_password = $user['nik'] . $random_digits;

            // Hash password
            $hashed_password = password_hash($temp_password, PASSWORD_DEFAULT);

            // Update password dan reset failed_attempts
            $update_query = "UPDATE users SET
                            password = ?,
                            failed_attempts = 0,
                            last_failed_attempt = NULL,
                            password_changed_at = NOW()
                            WHERE id = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("si", $hashed_password, $user['id']);
        } else {
            // Hanya reset failed_attempts dan last_failed_attempt
            $update_query = "UPDATE users SET
                            failed_attempts = 0,
                            last_failed_attempt = NULL
                            WHERE id = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("i", $user['id']);
        }

        if ($update_stmt->execute()) {
            $success = true;

            if ($reset_password) {
                $message = "Akun untuk {$user['name']} (NIK: {$user['nik']}) berhasil dibuka kembali dan password direset. Password sementara: $temp_password";
            } else {
                $message = "Akun untuk {$user['name']} (NIK: {$user['nik']}) berhasil dibuka kembali.";
            }

            // Log aktivitas
            if (file_exists('../config/activity_logger.php')) {
                include_once '../config/activity_logger.php';
                if (function_exists('log_activity')) {
                    $action = $reset_password ? "Reset penguncian akun dan password melalui parameter URL" : "Reset penguncian akun melalui parameter URL";
                    log_activity($user['id'], $action, "security", [
                        'method' => 'debug_unlock',
                        'reset_password' => $reset_password ? 'yes' : 'no',
                        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                    ]);
                }
            }
        } else {
            $message = "Gagal membuka kunci akun. Error: " . $conn->error;
        }
    } else {
        $message = "Identifier tidak ditemukan. Pastikan NIK, email, atau nama pengguna benar.";
    }
} else {
    $message = "Parameter tidak valid.";
}

// Redirect ke halaman login dengan pesan
if ($success) {
    header('Location: ../view/login.php?unlock_success=true&message=' . urlencode($message));
} else {
    header('Location: ../view/login.php?unlock_error=true&message=' . urlencode($message));
}
exit();
