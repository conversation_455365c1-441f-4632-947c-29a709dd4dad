<?php
// Optimasi untuk mencegah HTTP 500 error
error_reporting(E_ALL & ~E_WARNING & ~E_NOTICE);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Set memory limit dan max execution time
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 30);

// Konfigurasi session yang lebih aman dan optimal
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', 'Lax');
    ini_set('session.gc_maxlifetime', 3600);
    ini_set('session.gc_probability', 1);
    ini_set('session.gc_divisor', 100);

    session_start();
}

// Rate limiting untuk mencegah spam request
$client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$rate_limit_key = "login_attempts_{$client_ip}";

if (!isset($_SESSION[$rate_limit_key])) {
    $_SESSION[$rate_limit_key] = ['count' => 0, 'last_attempt' => time()];
}

// Reset counter jika sudah lewat 1 menit
if ((time() - $_SESSION[$rate_limit_key]['last_attempt']) > 60) {
    $_SESSION[$rate_limit_key] = ['count' => 0, 'last_attempt' => time()];
}

// Cek rate limit (maksimal 10 request per menit)
if ($_SESSION[$rate_limit_key]['count'] > 10) {
    http_response_code(429);
    die('Terlalu banyak request. Silakan tunggu sebentar.');
}

// Regenerate session ID untuk mencegah session fixation (lebih jarang untuk optimasi)
if (!isset($_SESSION['last_regeneration']) || (time() - $_SESSION['last_regeneration']) > 1800) {
    session_regenerate_id(true);
    $_SESSION['last_regeneration'] = time();
}

// Include necessary files
require_once __DIR__ . '/../config/constants.php';
if (!function_exists('get_app_name')) {
    require_once __DIR__ . '/../config/app_settings.php';
}
$app_name = get_app_name();
$company_logo = get_company_logo();
$current_page = basename($_SERVER['PHP_SELF']);

include '../config/config.php';
require '../vendor/autoload.php';
include '../config/mail.php';

// Initialize System Manager for activity logging and CSRF protection
require_once '../includes/SystemManager.php';
$systemManager = new SystemManager($conn);

// Security headers
header('X-Frame-Options: SAMEORIGIN');
header('X-Content-Type-Options: nosniff');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// Fungsi untuk menghasilkan token CSRF
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time']) || (time() - $_SESSION['csrf_token_time']) > 3600) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    }
    return $_SESSION['csrf_token'];
}

// Fungsi untuk sanitasi input
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Fungsi untuk mencatat percobaan login (from login_simple.php)
function record_login_attempt($user_id, $success) {
    global $conn;

    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

    // Catat percobaan login ke tabel login_attempts
    $query = "INSERT INTO login_attempts (user_id, ip_address, user_agent, attempt_time, success)
              VALUES (?, ?, ?, NOW(), ?)";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("issi", $user_id, $ip_address, $user_agent, $success);
    $stmt->execute();
    $stmt->close();

    // Jika gagal, catat juga ke tabel security_logs
    if (!$success) {
        $event_type = "LOGIN_FAILED";
        $description = "Percobaan login gagal dengan identifier: " . ($_POST['login_identifier'] ?? 'unknown');

        $query = "INSERT INTO security_logs (user_id, event_type, description, ip_address, user_agent, created_at)
                  VALUES (?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("issss", $user_id, $event_type, $description, $ip_address, $user_agent);
        $stmt->execute();
        $stmt->close();

        // Update kolom failed_attempts dan last_failed_attempt di tabel users jika user_id valid
        if ($user_id > 0) {
            $query = "UPDATE users SET
                      failed_attempts = failed_attempts + 1,
                      last_failed_attempt = NOW()
                      WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $stmt->close();

            // Cek apakah perlu mengunci akun
            $query = "SELECT failed_attempts FROM users WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $user_data = $result->fetch_assoc();
            $stmt->close();

            // Jika percobaan gagal melebihi 5 kali, kunci akun
            if ($user_data['failed_attempts'] >= 5) {
                $lock_expires = date('Y-m-d H:i:s', strtotime('+30 minutes'));
                $query = "UPDATE users SET
                          account_locked = 1,
                          lock_expires = ?
                          WHERE id = ?";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("si", $lock_expires, $user_id);
                $stmt->execute();
                $stmt->close();

                // Catat ke security_logs
                $event_type = "ACCOUNT_LOCKED";
                $description = "Akun dikunci karena terlalu banyak percobaan login gagal.";

                $query = "INSERT INTO security_logs (user_id, event_type, description, ip_address, user_agent, created_at)
                          VALUES (?, ?, ?, ?, ?, NOW())";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("issss", $user_id, $event_type, $description, $ip_address, $user_agent);
                $stmt->execute();
                $stmt->close();
            }
        }
    } else {
        // Jika berhasil, reset failed_attempts
        if ($user_id > 0) {
            $query = "UPDATE users SET
                      failed_attempts = 0,
                      last_login = NOW(),
                      last_login_ip = ?
                      WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("si", $ip_address, $user_id);
            $stmt->execute();
            $stmt->close();

            // Catat ke security_logs
            $event_type = "LOGIN_SUCCESS";
            $description = "Login berhasil.";

            $query = "INSERT INTO security_logs (user_id, event_type, description, ip_address, user_agent, created_at)
                      VALUES (?, ?, ?, ?, ?, NOW())";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("issss", $user_id, $event_type, $description, $ip_address, $user_agent);
            $stmt->execute();
            $stmt->close();
        }
    }
}

// Fungsi untuk mencatat percobaan login gagal untuk user yang tidak ditemukan (from login_simple.php)
function record_unknown_login_attempt($login_identifier) {
    global $conn;

    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

    // Catat percobaan login ke tabel login_attempts dengan user_id = 0 (unknown)
    $query = "INSERT INTO login_attempts (user_id, ip_address, user_agent, attempt_time, success)
              VALUES (0, ?, ?, NOW(), 0)";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $ip_address, $user_agent);
    $stmt->execute();
    $stmt->close();

    // Catat juga ke tabel security_logs
    $event_type = "LOGIN_UNKNOWN_USER";
    $description = "Percobaan login dengan identifier yang tidak dikenal: $login_identifier";

    $query = "INSERT INTO security_logs (user_id, event_type, description, ip_address, user_agent, created_at)
              VALUES (0, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ssss", $event_type, $description, $ip_address, $user_agent);
    $stmt->execute();
    $stmt->close();

    // Cek apakah perlu melakukan rate limiting berdasarkan IP
    $query = "SELECT COUNT(*) as count FROM login_attempts
              WHERE ip_address = ? AND success = 0 AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $ip_address);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['count'];
    $stmt->close();

    // Jika percobaan gagal dari IP ini melebihi 10 kali dalam 1 jam, catat ke rate_limits
    if ($count >= 10) {
        $query = "INSERT INTO rate_limits (user_id, action_type, ip_address, attempt_time)
                  VALUES (0, 'login', ?, NOW())";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $ip_address);
        $stmt->execute();
        $stmt->close();

        // Catat ke security_logs
        $event_type = "RATE_LIMIT_APPLIED";
        $description = "Rate limiting diterapkan untuk IP $ip_address karena terlalu banyak percobaan login gagal.";

        $query = "INSERT INTO security_logs (user_id, event_type, description, ip_address, user_agent, created_at)
                  VALUES (0, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("ssss", $event_type, $description, $ip_address, $user_agent);
        $stmt->execute();
        $stmt->close();
    }
}

// Fungsi untuk cek apakah password kedaluwarsa (dari login.php)
function is_password_expired($user_id) {
    global $conn;
    $query = "SELECT password_changed_at FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $stmt->close();

    // Misalkan password kedaluwarsa setelah 90 hari
    if ($user['password_changed_at']) {
        $last_change = strtotime($user['password_changed_at']);
        $now = time();
        $days_diff = ($now - $last_change) / (60 * 60 * 24);
        return $days_diff > 90;
    }
    return true; // Jika belum pernah ganti password, anggap kedaluwarsa
}

// Cek apakah pengguna sudah login
if (isset($_SESSION['user_id'])) {
    switch ($_SESSION['role_id']) {
        case 99:
            header('Location: ../admin/index.php');
            break;
        case 6:
            header('Location: ../kosong/index.php');
            break;
        case 4:
            header('Location: ../fm/index.php');
            break;
        case 3:
            header('Location: ../lnd/index.php');
            break;
        case 5:
            header('Location: ../dir/index.php');
            break;
        case 2:
            header('Location: ../dept_head/index.php');
            break;
        case 1:
            header('Location: ../pemohon/index.php');
            break;
        default:
            header('Location: index.php');
            break;
    }
    exit();
}

// Handle forgot password request
if (isset($_POST['forgot_password']) && isset($_POST['csrf_token'])) {
    if (!isset($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        $error_message = "Sesi keamanan tidak valid. Silakan coba lagi.";
        error_log("CSRF attack attempt on forgot password: " . json_encode([
            'IP' => $_SERVER['REMOTE_ADDR'],
            'User-Agent' => $_SERVER['HTTP_USER_AGENT'],
            'Referer' => $_SERVER['HTTP_REFERER'] ?? 'None'
        ]));
    } else {
        $email = sanitize_input($_POST['email'] ?? '');
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error_message = "Format email tidak valid.";
        } else {
            $query = "SELECT id, name FROM users WHERE email = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $user = $result->fetch_assoc();
                $token = bin2hex(random_bytes(32));
                $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));

                $updateQuery = "UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE email = ?";
                $updateStmt = $conn->prepare($updateQuery);
                $updateStmt->bind_param("sss", $token, $expires, $email);

                if ($updateStmt->execute()) {
                    $settingsQuery = "SELECT smtp_server, smtp_port, smtp_password, sender_email, sender_name, smtp_encryption FROM settings WHERE id = 1";
                    $settingsResult = $conn->query($settingsQuery);
                    $settings = $settingsResult->fetch_assoc();

                    try {
                        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                        $resetLink = $protocol . "://" . $_SERVER['HTTP_HOST'] . "/training/view/reset_password.php?token=" . $token;

                        $emailBody = "
                        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 2px solid #a50000; border-radius: 5px; background-color: #f9f9f9;'>
                            <h2 style='color: #a50000; text-align: center;'>Reset Password Request</h2>
                            <p style='color: #333; font-size: 16px;'>Anda telah meminta untuk mereset password Anda.</p>
                            <p style='color: #333; font-size: 16px;'>Klik link berikut untuk mereset password Anda:</p>
                            <div style='background-color: #ffffff; border: 2px solid #a50000; border-radius: 5px; padding: 15px; margin: 20px 0; text-align: center;'>
                                <a href='$resetLink' style='font-size: 18px; color: #a50000; text-decoration: none; font-weight: bold;'>Reset Password</a>
                            </div>
                            <p style='color: #333; font-size: 14px;'>Link ini akan kadaluarsa dalam <strong>1 jam</strong>.</p>
                            <p style='color: #333; font-size: 14px;'>Jika Anda tidak meminta reset password, abaikan email ini.</p>
                            <div style='text-align: center; margin-top: 30px;'>
                                <p style='color: #333; font-size: 12px;'>Email ini dikirim secara otomatis. Mohon jangan membalas email ini.</p>
                            </div>
                        </div>
                        ";

                        $mail_result = send_mail(
                            $email,
                            'Reset Password Link - Training Center PAS',
                            $emailBody,
                            $settings
                        );

                        if ($mail_result['success']) {
                            $success_message = "Link reset password telah dikirim ke email Anda.";
                            error_log("Reset password email sent successfully to: $email");
                            error_log("Password reset requested for user ID: " . $user['id'] . " from IP: " . $_SERVER['REMOTE_ADDR']);
                        } else {
                            $error_message = "Gagal mengirim email reset password. Error: " . $mail_result['message'];
                            error_log("Failed to send reset password email: " . $mail_result['message']);
                        }
                    } catch (Exception $e) {
                        $error_message = "Gagal memproses permintaan reset password: " . $e->getMessage();
                        error_log("Exception in reset password process: " . $e->getMessage());
                    }
                } else {
                    $error_message = "Gagal memproses permintaan reset password.";
                }
                $updateStmt->close();
            } else {
                $error_message = "Email tidak ditemukan.";
            }
            $stmt->close();
        }
    }
}

// Proses login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login_submit'])) {
    // Increment rate limit counter
    $_SESSION[$rate_limit_key]['count']++;
    $_SESSION[$rate_limit_key]['last_attempt'] = time();

    if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        $error_message = "Sesi keamanan tidak valid. Silakan coba lagi.";
        error_log("CSRF attack attempt on login: " . json_encode([
            'IP' => $_SERVER['REMOTE_ADDR'],
            'User-Agent' => $_SERVER['HTTP_USER_AGENT'],
            'Referer' => $_SERVER['HTTP_REFERER'] ?? 'None'
        ]));
    } else {
        $login_identifier = sanitize_input($_POST['login_identifier'] ?? '');
        $password = $_POST['password'] ?? '';

        if (empty($login_identifier) || empty($password)) {
            $error_message = "Silakan masukkan NIK/Nama Akun/Email dan password.";
        } else {
            // Modified query: case-insensitive search for names, handle multiple users with same name
            $query = "SELECT id, name, email, nik, password, role_id, dept, bagian, jabatan, is_active,
                             account_locked, lock_expires, failed_attempts, password_changed_at
                      FROM users WHERE LOWER(name) = LOWER(?) OR email = ? OR nik = ?";
            $stmt = $conn->prepare($query);

            if (!$stmt) {
                $error_message = "Terjadi kesalahan sistem. Silakan coba lagi.";
                error_log("Database prepare error: " . $conn->error);
            } else {
                $stmt->bind_param("sss", $login_identifier, $login_identifier, $login_identifier);

                if (!$stmt->execute()) {
                    $error_message = "Terjadi kesalahan sistem. Silakan coba lagi.";
                    error_log("Database execute error: " . $stmt->error);
                    $stmt->close();
                } else {
                    $result = $stmt->get_result();

            $authenticated_user = null;
            $found_users = [];

            // Collect all matching users
            while ($user = $result->fetch_assoc()) {
                $found_users[] = $user;
            }

            if (!empty($found_users)) {
                // Priority 1: Exact match for email or NIK (unique fields)
                foreach ($found_users as $user) {
                    if ($user['email'] === $login_identifier || $user['nik'] === $login_identifier) {
                        if (password_verify($password, $user['password'])) {
                            $authenticated_user = $user;
                            break;
                        }
                    }
                }

                // Priority 2: Case-insensitive name match with password verification
                if (!$authenticated_user) {
                    foreach ($found_users as $user) {
                        // Case-insensitive name comparison
                        if (strtolower($user['name']) === strtolower($login_identifier)) {
                            if (password_verify($password, $user['password'])) {
                                $authenticated_user = $user;
                                break;
                            }
                        }
                    }
                }

                // Priority 3: Partial name match with password verification (fallback)
                if (!$authenticated_user) {
                    foreach ($found_users as $user) {
                        // Check if login identifier is contained in user name (case-insensitive)
                        if (stripos($user['name'], $login_identifier) !== false || stripos($login_identifier, $user['name']) !== false) {
                            if (password_verify($password, $user['password'])) {
                                $authenticated_user = $user;
                                break;
                            }
                        }
                    }
                }
            }

            if ($authenticated_user) {
                $user = $authenticated_user;

                // Cek apakah akun terkunci
                $account_locked = false;
                if ($user['account_locked'] == 1) {
                    if ($user['lock_expires'] && strtotime($user['lock_expires']) > time()) {
                        $account_locked = true;
                        $lock_expires_time = date('d M Y H:i:s', strtotime($user['lock_expires']));
                        $error_message = "Akun Anda terkunci karena terlalu banyak percobaan login gagal. Silakan coba lagi setelah $lock_expires_time.";
                        record_login_attempt($user['id'], 0);
                    } else {
                        // Jika waktu kunci sudah berakhir, buka kunci akun
                        $query = "UPDATE users SET account_locked = 0, lock_expires = NULL WHERE id = ?";
                        $unlock_stmt = $conn->prepare($query);
                        $unlock_stmt->bind_param("i", $user['id']);
                        $unlock_stmt->execute();
                        $unlock_stmt->close();
                    }
                }

                // Lanjutkan proses login jika akun tidak terkunci
                if (!$account_locked) {
                    if (password_verify($password, $user['password'])) {
                        if ($user['is_active'] == 1) {
                            // Catat login berhasil
                            record_login_attempt($user['id'], 1);

                            // Log login activity using SystemManager
                            $systemManager->handleUserLogin($user['id'], $user['name']);

                            // Set session variables
                            $_SESSION['user_id'] = $user['id'];
                            $_SESSION['user_name'] = $user['name'];
                            $_SESSION['role_id'] = $user['role_id'];
                            $_SESSION['dept'] = $user['dept'];
                            $_SESSION['bagian'] = $user['bagian'];
                            $_SESSION['jabatan'] = $user['jabatan'];
                            $_SESSION['user_nik'] = $user['nik'];

                            // Cek apakah menggunakan password default "asdf"
                            if ($password === 'asdf') {
                                $_SESSION['default_password_used'] = true;
                                $_SESSION['require_password_change'] = true;
                                header('Location: change_password.php?reason=default');
                                exit();
                            }

                            // Cek apakah password kedaluwarsa
                            if (is_password_expired($user['id'])) {
                                $_SESSION['password_expired'] = true;
                                $_SESSION['require_password_change'] = true;
                                header('Location: change_password.php?reason=expired');
                                exit();
                            }

                            // Arahkan berdasarkan role
                            switch ($_SESSION['role_id']) {
                                case 99:
                                    header('Location: ../admin/index.php');
                                    break;
                                case 6:
                                    header('Location: ../kosong/index.php');
                                    break;
                                case 4:
                                    header('Location: ../fm/index.php');
                                    break;
                                case 3:
                                    header('Location: ../lnd/index.php');
                                    break;
                                case 5:
                                    header('Location: ../dir/index.php');
                                    break;
                                case 2:
                                    header('Location: ../dept_head/index.php');
                                    break;
                                case 1:
                                    header('Location: ../pemohon/index.php');
                                    break;
                                default:
                                    header('Location: forbidden.php');
                                    break;
                            }
                            exit();
                        } else {
                            $error_message = "Akun belum diaktivasi. Silakan aktivasi terlebih dahulu.";
                            record_login_attempt($user['id'], 0);
                        }
                    } else {
                        $error_message = "Password salah. Silakan coba lagi.";
                        record_login_attempt($user['id'], 0);
                    }
                }
                } else {
                    $error_message = "Akun tidak ditemukan.";
                    record_unknown_login_attempt($login_identifier);
                }
                $stmt->close();
                }
            }
        }
    }
}
echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">';
echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css">';

?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<body>

<style>
    body {
    display:center;
    justify-content: center !important;  
    align-items: center !important;
    height: 100vh;
    }
    @media (max-width: 768px) {
    .login-container {
        padding: 15px;
        padding-top: 100px; /* Hindari navbar overlap */
    }

    .login-box {
        padding: 25px 20px;
    }

    .login-logo {
        width: 100px;
    }

    .login-header h2 {
        font-size: 24px;
    }

    .form-control {
        padding: 12px;
        font-size: 14px;
    }

    .btn {
        padding: 12px;
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .login-box {
        padding: 20px 15px;
    }

    .login-logo {
        width: 90px;
    }

    .login-header h2 {
        font-size: 22px;
    }

    .form-group label {
        font-size: 14px;
    }

    .form-control {
        padding: 10px;
        font-size: 14px;
    }

    .btn {
        padding: 12px;
        font-size: 14px;
    }

    .alert {
        padding: 12px;
        font-size: 14px;
    }
}
:root {
    --primary-color: #BF0000;
    --primary-color-dark: #900000;
    --primary-color-light: rgba(191, 0, 0, 0.1);
    --primary-color-lighter: rgba(191, 0, 0, 0.05);
    --text-dark: #333333;
    --text-light: #ffffff;
    --text-muted: rgba(255, 255, 255, 0.8);
    --border-light: rgba(255, 255, 255, 0.2);
    --accent-color: #FF3333;
    --accent-light: rgba(255, 51, 51, 0.3);
    --accent-lighter: rgba(255, 51, 51, 0.1);
    --dark-accent: #333333;
    --dark-accent-light: rgba(51, 51, 51, 0.3);
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --transition-fast: 0.2s ease;
    --box-shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.1);
    --box-shadow-md: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Header Styles */
.header {
    background-color: #ffffff;
    box-shadow: var(--box-shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all var(--transition-fast);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    position: relative;
}

/* Logo Styles */
.logo-container {
    display: flex;
    align-items: center;
    height: 40px;
    margin-right: var(--spacing-md);
    overflow: hidden;
}

.logo-image {
    max-height: 40px;
    max-width: 150px;
    width: auto;
    height: auto;
    object-fit: contain;
    transition: all var(--transition-fast);
}

.logo {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
    transition: all var(--transition-fast);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 600px;
}

.logo:hover {
    color: var(--primary-color-dark);
}

/* Navigation Menu */
.nav-menu {
    display: flex;
    align-items: center;
    margin-left: auto;
    gap: var(--spacing-md);
}

.nav-menu a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
    overflow: hidden;
}

.nav-menu a i {
    font-size: var(--font-size-md);
    transition: all var(--transition-fast);
}

.nav-menu a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width var(--transition-fast);
}

.nav-menu a:hover {
    color: var(--primary-color);
    background-color: var(--primary-color-lighter);
}

.nav-menu a:hover i {
    transform: translateY(-2px);
}

.nav-menu a:hover::before {
    width: 80%;
}

.nav-menu a.active {
    color: var(--primary-color);
    background-color: var(--primary-color-light);
    font-weight: 600;
}

.nav-menu a.active::before {
    width: 80%;
}

/* User Profile and Logout */
.user-profile {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    margin-left: var(--spacing-md);
}

.username {
    font-weight: 600;
    color: var(--text-dark);
    transition: all var(--transition-fast);
}

.logout {
    background-color: var(--primary-color);
    color: var(--text-dark) !important;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    font-weight: 600;
    box-shadow: var(--box-shadow-sm);
}

.logout:hover {
    color: var(--text-dark) !important;
    background-color: var(--primary-color-dark);
    box-shadow: var(--box-shadow-md);
    transform: translateY(-2px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background-color: var(--primary-color);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    z-index: 1001;
    box-shadow: var(--box-shadow-sm);
}

.mobile-menu-toggle:hover {
    background-color: var(--primary-color-dark);
}

/* Responsive Styles */
@media screen and (max-width: 992px) {
    .header-content {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .logo {
        max-width: 180px;
        font-size: var(--font-size-md);
    }

    .nav-menu a {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

@media screen and (max-width: 768px) {
    .header {
        box-shadow: none;
    }

    .header-content {
        padding: var(--spacing-sm);
        justify-content: space-between;
    }

    .logo-container {
        height: 36px;
    }

    .logo-image {
        max-height: 36px;
        max-width: 120px;
    }

    .logo {
        max-width: 150px;
        font-size: var(--font-size-sm);
    }

    /* Show mobile menu toggle */
    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        margin-left: auto;
    }

    /* Style mobile navigation menu */
    .nav-menu {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--primary-color);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: var(--spacing-lg);
        gap: var(--spacing-md);
        z-index: 1000;
        overflow-y: auto;
    }

    .nav-menu::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.1;
        pointer-events: none;
    }

    .nav-menu.active {
        display: flex;
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    .nav-menu a {
        color: var(--text-light);
        width: 100%;
        text-align: center;
        padding: var(--spacing-md);
        font-size: var(--font-size-md);
        border-radius: var(--border-radius-md);
        justify-content: center;
    }

    .nav-menu a::before {
        display: none;
    }

    .nav-menu a:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--text-light);
    }

    .nav-menu a.active {
        background-color: rgba(255, 255, 255, 0.2);
        color: var(--text-light);
    }

    .user-section {
        display: flex;
        flex-direction: column;
        width: 100%;
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
    }

    .user-profile {
        margin-left: 0;
        margin-top: var(--spacing-md);
        width: 100%;
        justify-content: center;
    }

    .username {
        color: var(--text-light);
    }

    .logout {
        width: 100%;
        color: var(--text-dark);
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.3);
        margin-top: var(--spacing-md);
    }

    .logout:hover {
        color: var(--text-dark);
        background-color: rgba(255, 255, 255, 0.4);
    }
}

@media screen and (max-width: 480px) {
    .header-content {
        padding: var(--spacing-xs);
    }

    .logo-container {
        height: 32px;
    }

    .logo-image {
        max-height: 32px;
        max-width: 100px;
    }

    .logo {
        max-width: 120px;
        font-size: var(--font-size-xs);
    }

    .nav-menu a {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
}

/* Add spacing to prevent content from being hidden under fixed header */
.header-spacer {
    height: 80px; /* Further increased height to prevent content from being hidden */
    width: 100%;
    display: block;
    clear: both;
}

@media screen and (max-width: 768px) {
    .header-spacer {
        height: 70px; /* Increased height for medium screens */
    }
}

@media screen and (max-width: 480px) {
    .header-spacer {
        height: 65px; /* Increased height for small screens */
    }
}
</style>

<header class="header">
    <div class="header-content">
        <div class="logo-container">
            <img class="logo-image" src="../asset/picture/logopasbagus.jpg" alt="Company Logo">
        </div>
        <a href="login.php" class="logo">Knowledge Management System</a>

        <!-- Mobile Menu Toggle Button -->
        <button class="mobile-menu-toggle" id="mobileMenuToggle" type="button" aria-label="Toggle menu">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Navigation Menu -->
        <nav class="nav-menu" id="navMenu">
            <a href="index.php" class="<?= ($current_page == 'index.php') ? 'active' : '' ?>">
                <i class="fas fa-home"></i> Dashboard
            </a>
            <?php if(isset($_SESSION['role_id']) && $_SESSION['role_id'] == 99): ?>
            <a href="<?= getAdminMenuUrl('employee_management.php') ?>" class="<?= ($current_page == 'employee_management.php') ? 'active' : '' ?>">
                <i class="fas fa-users"></i> Karyawan
            </a>
            <a href="<?= getAdminMenuUrl('training_management.php') ?>" class="<?= ($current_page == 'training_management.php') ? 'active' : '' ?>">
                <i class="fas fa-chalkboard-teacher"></i> Training
            </a>
            <a href="<?= getAdminMenuUrl('settings.php') ?>" class="<?= ($current_page == 'settings.php') ? 'active' : '' ?>">
                <i class="fas fa-cog"></i> Pengaturan
            </a>
            <?php endif; ?>

            <?php if(isset($_SESSION['user_id'])): ?>
            <a href="<?= getCorrectUrl('config/userinfo.php') ?>" class="user-profile <?= ($current_page == 'userinfo.php') ? 'active' : '' ?>">
                <div class="avatar">
                <span class="username"><?= strtoupper(substr($_SESSION['user_name'] ?? 'Pengguna', 0, 1)) ?></span>
                </div>
            </a>
            <a href="<?= getCorrectUrl('view/logout.php') ?>" class="logout">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
            <?php endif; ?>
        </nav>
    </div>
</header>

<!-- Add spacing to prevent content from being hidden under fixed header -->
<div class="header-spacer"></div>

<script>
    // Function to toggle mobile menu
    document.getElementById('mobileMenuToggle').addEventListener('click', function() {
        try {
            const navMenu = document.getElementById('navMenu');
            navMenu.classList.toggle('active');

            // Change icon based on menu state
            if (navMenu.classList.contains('active')) {
                this.innerHTML = '<i class="fas fa-times"></i>';
            } else {
                this.innerHTML = '<i class="fas fa-bars"></i>';
            }
        } catch (error) {
            console.error('Error toggling menu:', error);
        }
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        try {
            const navMenu = document.getElementById('navMenu');
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');

            if (navMenu && mobileMenuToggle) {
                if (!navMenu.contains(event.target) && !mobileMenuToggle.contains(event.target)) {
                    navMenu.classList.remove('active');
                    mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';
                }
            }
        } catch (error) {
            console.error('Error handling click event:', error);
        }
    });

    // Add shadow to header on scroll
    window.addEventListener('scroll', function() {
        try {
            const header = document.querySelector('.header');
            if (window.scrollY > 10) {
                header.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.1)';
            }
        } catch (error) {
            console.error('Error handling scroll event:', error);
        }
    });
</script><div class="container-fluid">
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <img src="../asset/picture/logo-pas-with-text-removebg-preview.png" alt="Logo" class="login-logo">
                <h2>Login</h2>
            </div>

            <?php if (isset($_GET['unlock_success']) && $_GET['unlock_success'] === 'true'): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($_GET['message'] ?? 'Akun berhasil dibuka kembali.'); ?></div>
            <?php endif; ?>
            <?php if (isset($_GET['unlock_error']) && $_GET['unlock_error'] === 'true'): ?>
                <div class="alert alert-danger"><?php echo htmlspecialchars($_GET['message'] ?? 'Gagal membuka kunci akun.'); ?></div>
            <?php endif; ?>
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <!-- Login Form -->
            <form method="POST" class="login-form" id="loginForm">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <div class="form-group">
                    <label for="login_identifier"><i class="fas fa-user"></i> NIK, Nama Akun, atau Email</label>
                    <input type="text" name="login_identifier" id="login_identifier" required placeholder="Masukkan NIK, Nama Akun, atau Email" class="form-control" autocomplete="username">
                </div>
                <div class="form-group">
                    <label for="password"><i class="fas fa-lock"></i> Password</label>
                    <div class="password-input">
                        <input type="password" name="password" id="password" required placeholder="Masukkan Password" class="form-control" autocomplete="current-password">
                        <span class="toggle-password" onclick="togglePassword('password')"><i class="far fa-eye"></i></span>
                    </div>
                </div>
                <button type="submit" name="login_submit" class="btn btn-primary btn-block"><i class="fas fa-sign-in-alt"></i> Login</button>
            </form>

            <!-- Forgot Password Form -->
            <form method="POST" class="forgot-password-form" id="forgotPasswordForm" style="display: none;">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <div class="form-group">
                    <label for="email"><i class="fas fa-envelope"></i> Email</label>
                    <input type="email" name="email" id="email" required placeholder="Masukkan email Anda" class="form-control" autocomplete="email">
                </div>
                <button type="submit" name="forgot_password" class="btn btn-primary btn-block"><i class="fas fa-paper-plane"></i> Kirim Link Reset Password</button>
                <button type="button" class="btn btn-secondary btn-block" onclick="showLoginForm()"><i class="fas fa-arrow-left"></i> Kembali ke Login</button>
            </form>

            <div class="login-footer">
                <p>Belum punya akun? <a href="aktivasi.php">Aktivasi di sini</a></p>
                <p><a href="#" onclick="showForgotPassword()">Lupa Password?</a></p>
                <p class="small text-muted"><i class="fas fa-info-circle"></i> Jika akun Anda terkunci, hubungi admin pada footer di bawah:</p>
            </div>
        </div>
    </div>
</div>
    <style>
    /* Base styles */
    body {
        font-family: 'Roboto', Arial, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 0;
    }

    /* Login container */
    .login-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 120px); /* Adjust for navbar and footer */
        margin:auto;
        padding: 20px;
        background: linear-gradient(135deg, #c40000 0%, #8b0000 100%);
        position: relative;
        
    }

    .login-box {
        width: 100%;
        max-width: 400px;
        background: rgba(255, 255, 255, 0.98);
        border-radius: 15px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        padding: 30px;
        transition: all 0.3s ease;
    }

    /* Header styles */
    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .login-logo {
        width: 120px;
        height: auto;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }

    .login-header h2 {
        color: #BF0000;
        font-size: 28px;
        margin: 0;
        font-weight: 600;
    }

    /* Form styles */
    .form-group {
        margin-bottom: 22px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #444;
        font-weight: 500;
        font-size: 15px;
    }

    .form-control {
        width: 100%;
        padding: 14px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }

    .form-control:focus {
        border-color: #c40000;
        outline: none;
        box-shadow: 0 0 0 3px rgba(196, 0, 0, 0.1);
    }

    .password-input {
        position: relative;
    }

    .toggle-password {
        position: absolute;
        right: 14px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #666;
        font-size: 16px;
        padding: 5px;
        z-index: 10;
    }

    .toggle-password:hover {
        color: #c40000;
    }

    /* Button styles */
    .btn {
        width: 100%;
        padding: 14px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }

    .btn-primary {
        background: #c40000;
        color: white;
    }

    .btn-primary:hover {
        background: #a00000;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn-primary:active {
        transform: translateY(0);
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        margin-top: 12px;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Footer styles */
    .login-footer {
        text-align: center;
        margin-top: 25px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .login-footer p {
        margin: 8px 0;
        color: #555;
    }

    .login-footer a {
        color: #c40000;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .login-footer a:hover {
        color: #a00000;
        text-decoration: underline;
    }

    .small {
        font-size: 12px;
    }

    .text-muted {
        color: #6c757d;
    }

    code {
        background-color: #f8f9fa;
        padding: 2px 4px;
        border-radius: 4px;
        color: #e83e8c;
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        font-size: 11px;
        word-break: break-all;
    }

    .debug-info {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 8px;
        margin-top: 8px;
        margin-bottom: 8px;
    }

    .mb-1 {
        margin-bottom: 0.25rem !important;
    }

    .mt-2 {
        margin-top: 0.5rem !important;
    }

    /* Alert styles */
    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 25px;
        text-align: center;
        font-size: 15px;
        font-weight: 500;
    }

    .alert-danger {
        background: #fff2f2;
        color: #c40000;
        border: 1px solid #ffcfcf;
    }

    .alert-success {
        background: #f0fff4;
        color: #0d6832;
        border: 1px solid #c3e6cb;
    }

    /* Responsive styles */
    @media (max-width: 768px) {
        .login-container {
            padding: 15px;
            padding-top: 70px; /* Add top padding to prevent navbar overlap */
        }

        .login-box {
            padding: 25px 20px;
        }

        .login-logo {
            width: 100px;
        }

        .login-header h2 {
            font-size: 24px;
        }

        .form-control {
            padding: 12px;
            font-size: 14px;
        }

        .btn {
            padding: 12px;
            font-size: 15px;
        }
    }

    @media (max-width: 480px) {
        .login-box {
            padding: 20px 15px;
        }

        .login-logo {
            width: 90px;
        }

        .login-header h2 {
            font-size: 22px;
        }

        .form-group label {
            font-size: 14px;
        }

        .form-control {
            padding: 10px;
            font-size: 14px;
        }

        .btn {
            padding: 12px;
            font-size: 14px;
        }

        .alert {
            padding: 12px;
            font-size: 14px;
        }
    }
    </style>
<script>
    // Same JavaScript as in original login.php
    function togglePassword(fieldId) {
        try {
            const field = document.getElementById(fieldId);
            if (!field) return;
            const toggleElement = field.nextElementSibling;
            if (!toggleElement) return;
            const toggle = toggleElement.querySelector('i');
            if (!toggle) return;
            if (field.type === "password") {
                field.type = "text";
                toggle.classList.remove('fa-eye');
                toggle.classList.add('fa-eye-slash');
            } else {
                field.type = "password";
                toggle.classList.remove('fa-eye-slash');
                toggle.classList.add('fa-eye');
            }
        } catch (error) {
            console.error('Error toggling password visibility:', error);
        }
    }

    function showForgotPassword() {
        try {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('forgotPasswordForm').style.display = 'block';
            document.querySelector('.login-footer').style.display = 'none';
        } catch (error) {
            console.error('Error showing forgot password form:', error);
        }
    }

    function showLoginForm() {
        try {
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('forgotPasswordForm').style.display = 'none';
            document.querySelector('.login-footer').style.display = 'block';
        } catch (error) {
            console.error('Error showing login form:', error);
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('forgot') && urlParams.get('forgot') === '1') {
            showForgotPassword();
        } else {
            showLoginForm();
        }
    });
</script>
</body>
</html>