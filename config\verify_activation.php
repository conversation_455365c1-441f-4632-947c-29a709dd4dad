<?php
include 'config.php';
// session_start() sudah dipanggil di config.php

if (!isset($_SESSION['temp_user_id'])) {
    header('Location: ../view/Aktivasi.php');
    exit();
}

$message = '';
$user_id = $_SESSION['temp_user_id'];

// Check if this is a direct activation (without email verification)
$direct_activation = isset($_GET['direct']) && $_GET['direct'] == 1;

// If direct activation, activate the account immediately
if ($direct_activation) {
    // Activate the account
    $update_query = "UPDATE users SET
                    is_active = 1,
                    verification_code = NULL,
                    verification_expires = NULL
                    WHERE id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("i", $user_id);

    if ($update_stmt->execute()) {
        // Redirect to update email and password page
        header('Location: ../view/update_email_password.php');
        exit();
    } else {
        $message = '<div class="error-message">Gagal mengaktivasi akun!</div>';
        // Redirect back to activation page
        header('Refresh: 2; URL=../view/Aktivasi.php');
        exit();
    }
}

// For normal email verification flow
$email = isset($_SESSION['verification_email']) ? $_SESSION['verification_email'] : '';
if (empty($email)) {
    header('Location: ../view/Aktivasi.php');
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $entered_code = $_POST['verification_code'];

    // Verify the code
    $query = "SELECT * FROM users WHERE id = ? AND verification_code = ? AND verification_expires > NOW()";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("is", $user_id, $entered_code);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Activate the account
        $update_query = "UPDATE users SET
                        is_active = 1,
                        verification_code = NULL,
                        verification_expires = NULL
                        WHERE id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("i", $user_id);

        if ($update_stmt->execute()) {
            $message = '<div class="success-message">Akun berhasil diaktivasi! Anda akan dialihkan ke halaman login.</div>';
            session_destroy();
            header('Refresh: 2; URL=../view/login.php');
            exit();
        } else {
            $message = '<div class="error-message">Gagal mengaktivasi akun!</div>';
        }
    } else {
        $message = '<div class="error-message">Kode verifikasi tidak valid atau sudah kadaluarsa!</div>';
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<body>
    <?php include '../config/navbarb.php'; ?>
    <div class="container-form">
        <div class="verification-container">
            <h2>Verifikasi Email</h2>
            <p>Masukkan kode verifikasi yang telah dikirim ke email: <strong><?php echo htmlspecialchars($email); ?></strong></p>

            <?php if ($message): ?>
                <?php echo $message; ?>
            <?php endif; ?>

            <form method="POST">
                <div class="form-group">
                    <label for="verification_code">Kode Verifikasi:</label>
                    <input type="text"
                           name="verification_code"
                           id="verification_code"
                           required
                           maxlength="6"
                           pattern="\d{6}"
                           placeholder="Masukkan 6 digit kode"
                           class="form-control">
                </div>

                <button type="submit" class="btn btn-primary">Verifikasi</button>
            </form>

            <div class="resend-link">
                <p>Tidak menerima kode? <a href="resend_code.php">Kirim ulang</a></p>
            </div>
        </div>
    </div>
    <?php include '../config/footer.php'; ?>

    <style>
    .verification-container {
        max-width: 400px;
        margin: 50px auto;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    .form-control {
        text-align: center;
        letter-spacing: 5px;
        font-size: 24px;
    }

    .success-message {
        color: green;
        padding: 10px;
        background: #e8f5e9;
        border-radius: 4px;
        margin: 10px 0;
    }

    .error-message {
        color: red;
        padding: 10px;
        background: #ffebee;
        border-radius: 4px;
        margin: 10px 0;
    }
    </style>
</body>
</html>