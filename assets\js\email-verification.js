function resendVerificationCode() {
    // <PERSON><PERSON> permintaan AJAX untuk mengirim ulang kode verifikasi
    fetch('resend_verification.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        const modal = new CustomModal();
        if (data.success) {
            modal.alert({
                title: 'Berhasil',
                message: 'Kode verifikasi baru telah dikirim ke email Anda.',
                type: 'success'
            });
        } else {
            modal.alert({
                title: 'Error',
                message: data.message || 'Gagal mengirim kode verifikasi. Silakan coba lagi.',
                type: 'alert'
            });
        }
    })
    .catch(error => {
        const modal = new CustomModal();
        modal.alert({
            title: 'Error',
            message: '<PERSON><PERSON><PERSON><PERSON> kesalahan. Silakan coba lagi.',
            type: 'alert'
        });
    });
}

// Fungsi untuk memformat input kode verifikasi
document.addEventListener('DOMContentLoaded', function() {
    const verificationInput = document.getElementById('verification_code');
    if (verificationInput) {
        verificationInput.addEventListener('input', function(e) {
            // Hapus karakter non-angka
            this.value = this.value.replace(/\D/g, '');
            
            // Batasi panjang input maksimal 6 karakter
            if (this.value.length > 6) {
                this.value = this.value.slice(0, 6);
            }
        });
    }
});