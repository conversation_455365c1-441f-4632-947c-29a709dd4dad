<?php
/**
 * Test file untuk memverifikasi fitur password default
 * File ini untuk testing saja, hapus setelah testing selesai
 */

include 'config/config.php';

echo "<h2>Test Password Default Feature</h2>";

// 1. Cek apakah ada user dengan password "asdf"
echo "<h3>1. Checking for users with default password 'asdf'</h3>";

$query = "SELECT id, name, nik, email FROM users LIMIT 10";
$result = $conn->query($query);

if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>NIK</th><th>Email</th><th>Password Check</th></tr>";
    
    while ($user = $result->fetch_assoc()) {
        // Cek apakah password user adalah "asdf"
        $password_query = "SELECT password FROM users WHERE id = ?";
        $stmt = $conn->prepare($password_query);
        $stmt->bind_param("i", $user['id']);
        $stmt->execute();
        $password_result = $stmt->get_result();
        $password_data = $password_result->fetch_assoc();
        
        $is_default = password_verify('asdf', $password_data['password']);
        $password_status = $is_default ? 
            "<span style='color: red; font-weight: bold;'>DEFAULT PASSWORD (asdf)</span>" : 
            "<span style='color: green;'>Custom Password</span>";
        
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['nik']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>{$password_status}</td>";
        echo "</tr>";
        
        $stmt->close();
    }
    echo "</table>";
} else {
    echo "<p>No users found.</p>";
}

// 2. Buat user test dengan password default jika belum ada
echo "<h3>2. Create test user with default password (if not exists)</h3>";

$test_nik = "TEST001";
$check_query = "SELECT id FROM users WHERE nik = ?";
$stmt = $conn->prepare($check_query);
$stmt->bind_param("s", $test_nik);
$stmt->execute();
$check_result = $stmt->get_result();

if ($check_result->num_rows == 0) {
    // Buat user test
    $hashed_password = password_hash('asdf', PASSWORD_DEFAULT);
    $insert_query = "INSERT INTO users (name, nik, email, password, role_id, dept, bagian, jabatan, is_active) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $insert_stmt = $conn->prepare($insert_query);
    
    $name = "Test User Default Password";
    $email = "<EMAIL>";
    $role_id = 1;
    $dept = "IT";
    $bagian = "Testing";
    $jabatan = "Tester";
    $is_active = 1;
    
    $insert_stmt->bind_param("ssssisssi", $name, $test_nik, $email, $hashed_password, 
                            $role_id, $dept, $bagian, $jabatan, $is_active);
    
    if ($insert_stmt->execute()) {
        echo "<p style='color: green;'>✅ Test user created successfully!</p>";
        echo "<p><strong>Login credentials:</strong></p>";
        echo "<ul>";
        echo "<li>NIK: {$test_nik}</li>";
        echo "<li>Password: asdf</li>";
        echo "</ul>";
        echo "<p>Try logging in with these credentials to test the default password redirect.</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create test user: " . $conn->error . "</p>";
    }
    $insert_stmt->close();
} else {
    echo "<p style='color: blue;'>ℹ️ Test user already exists with NIK: {$test_nik}</p>";
}
$stmt->close();

// 3. Instruksi testing
echo "<h3>3. Testing Instructions</h3>";
echo "<div style='background-color: #f0f8ff; padding: 15px; border-left: 4px solid #0066cc; margin: 10px 0;'>";
echo "<h4>How to test the default password feature:</h4>";
echo "<ol>";
echo "<li>Go to <a href='view/login.php' target='_blank'>login page</a></li>";
echo "<li>Login with NIK: <strong>TEST001</strong> and Password: <strong>asdf</strong></li>";
echo "<li>You should be automatically redirected to <strong>change_password.php</strong></li>";
echo "<li>You should see a warning message about using default password</li>";
echo "<li>Change the password to something secure</li>";
echo "<li>After changing password, you should be redirected to the appropriate dashboard</li>";
echo "</ol>";
echo "</div>";

// 4. Cleanup option
echo "<h3>4. Cleanup</h3>";
echo "<div style='background-color: #fff8dc; padding: 15px; border-left: 4px solid #ffa500; margin: 10px 0;'>";
echo "<p><strong>After testing, you can:</strong></p>";
echo "<ul>";
echo "<li>Delete the test user manually from the database</li>";
echo "<li>Or run: <code>DELETE FROM users WHERE nik = 'TEST001'</code></li>";
echo "<li>Delete this test file: <code>test_default_password.php</code></li>";
echo "</ul>";
echo "</div>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

code {
    background-color: #f4f4f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

h2 {
    color: #333;
    border-bottom: 2px solid #0066cc;
    padding-bottom: 10px;
}

h3 {
    color: #0066cc;
    margin-top: 30px;
}
</style>
