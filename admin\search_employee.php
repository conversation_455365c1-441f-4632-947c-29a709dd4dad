<?php
// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config.php jika belum di-include
if (!isset($conn) || $conn === null) {
    include '../config/config.php';
}

// Get parameters
$query = isset($_GET['query']) ? $_GET['query'] : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'nik';
$order = isset($_GET['order']) ? $_GET['order'] : 'asc';
$limit = 10;
$offset = ($page - 1) * $limit;

try {
    // Validate sort and order parameters
    $allowed_sorts = ['nik', 'nama', 'dept', 'bagian', 'jabatan', 'group', 'status', 'pt'];
    $allowed_orders = ['asc', 'desc'];
    
    if (!in_array(strtolower($sort), $allowed_sorts)) $sort = 'nik';
    if (!in_array(strtolower($order), $allowed_orders)) $order = 'asc';

    // Prepare search condition
    $searchCondition = '';
    $params = [];
    $types = '';
    
    if (!empty($query)) {
        $searchCondition = "WHERE nik LIKE ? OR nama LIKE ? OR dept LIKE ? OR bagian LIKE ? OR jabatan LIKE ? OR `group` LIKE ? OR status LIKE ? OR pt LIKE ?";
        $searchParam = "%$query%";
        $params = array_fill(0, 8, $searchParam);
        $types = str_repeat('s', 8);
    }

    // Get total records for pagination
    $countQuery = "SELECT COUNT(*) as count FROM karyawan $searchCondition";
    if (!empty($params)) {
        $stmt = $conn->prepare($countQuery);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $total_result = $stmt->get_result();
    } else {
        $total_result = $conn->query($countQuery);
    }
    
    $total_records = $total_result->fetch_assoc()['count'];
    $total_pages = ceil($total_records / $limit);

    // Get employees with pagination and sorting
    $query = "SELECT * FROM karyawan 
              $searchCondition 
              ORDER BY $sort $order 
              LIMIT ? OFFSET ?";

    // Add limit and offset to params
    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';

    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $employees = [];
    while ($row = $result->fetch_assoc()) {
        $employees[] = $row;
    }

    // Return response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'employees' => $employees,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_records' => $total_records,
            'limit' => $limit
        ],
        'sort' => [
            'column' => $sort,
            'order' => $order
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
