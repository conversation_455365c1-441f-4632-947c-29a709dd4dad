<?php
session_start();
include '../config/config.php';

// Cek autentikasi
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Get search parameter
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Base query - hanya tampilkan karyawan yang belum memiliki akun
$query = "SELECT k.nik, k.nama, k.dept, k.bagian, k.jabatan
          FROM karyawan k
          LEFT JOIN users u ON k.nik = u.nik
          WHERE (k.nama LIKE ? OR k.nik LIKE ?) AND u.id IS NULL
          LIMIT 10";

try {
    $stmt = $conn->prepare($query);
    $searchParam = "%$search%";
    $stmt->bind_param("ss", $searchParam, $searchParam);
    $stmt->execute();
    $result = $stmt->get_result();

    $employees = [];
    while ($row = $result->fetch_assoc()) {
        $employees[] = $row;
    }

    // Hitung total karyawan yang cocok dengan pencarian (termasuk yang sudah memiliki akun)
    $totalQuery = "SELECT COUNT(*) as total FROM karyawan k WHERE (k.nama LIKE ? OR k.nik LIKE ?)";
    $totalStmt = $conn->prepare($totalQuery);
    $totalStmt->bind_param("ss", $searchParam, $searchParam);
    $totalStmt->execute();
    $totalResult = $totalStmt->get_result();
    $totalRow = $totalResult->fetch_assoc();
    $totalMatches = $totalRow['total'];

    // Hitung karyawan yang cocok dengan pencarian dan sudah memiliki akun
    $existingQuery = "SELECT COUNT(*) as existing FROM karyawan k
                     JOIN users u ON k.nik = u.nik
                     WHERE (k.nama LIKE ? OR k.nik LIKE ?)";
    $existingStmt = $conn->prepare($existingQuery);
    $existingStmt->bind_param("ss", $searchParam, $searchParam);
    $existingStmt->execute();
    $existingResult = $existingStmt->get_result();
    $existingRow = $existingResult->fetch_assoc();
    $existingAccounts = $existingRow['existing'];

    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'data' => $employees,
        'count' => count($employees),
        'total_matches' => $totalMatches,
        'existing_accounts' => $existingAccounts,
        'message' => 'Menampilkan karyawan yang belum memiliki akun.'
    ]);

} catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

