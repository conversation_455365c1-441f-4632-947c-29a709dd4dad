-- MySQL dump 10.13  Distrib 8.0.30, for Win64 (x86_64)
--
-- Host: localhost    Database: db_training
-- ------------------------------------------------------
-- Server version	8.0.30

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `karyawan_history`
--

DROP TABLE IF EXISTS `karyawan_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `karyawan_history` (
  `history_id` int NOT NULL AUTO_INCREMENT,
  `action_type` enum('INSERT','UPDATE','DELETE') NOT NULL,
  `karyawan_id` int DEFAULT NULL,
  `nik` varchar(255) DEFAULT NULL,
  `card_number` varchar(50) DEFAULT NULL,
  `normalized_nik` varchar(255) DEFAULT NULL,
  `nama` varchar(255) DEFAULT NULL,
  `tgl_masuk` varchar(255) DEFAULT NULL,
  `jk` char(1) DEFAULT NULL,
  `level_karyawan` varchar(255) DEFAULT NULL,
  `tgl_lahir` varchar(255) DEFAULT NULL,
  `agama` varchar(255) DEFAULT NULL,
  `pendidikan_akhir` varchar(255) DEFAULT NULL,
  `no_telp` varchar(255) NOT NULL,
  `dept` varchar(255) DEFAULT NULL,
  `bagian` varchar(255) DEFAULT NULL,
  `jabatan` varchar(255) DEFAULT NULL,
  `group` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `pt` varchar(255) DEFAULT NULL,
  `changed_by` int NOT NULL,
  `change_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `old_data` text COMMENT 'JSON data of the old record state',
  `new_data` text COMMENT 'JSON data of the new record state',
  PRIMARY KEY (`history_id`),
  KEY `idx_karyawan_history_action_type` (`action_type`),
  KEY `idx_karyawan_history_karyawan_id` (`karyawan_id`),
  KEY `idx_karyawan_history_nik` (`nik`),
  KEY `idx_karyawan_history_changed_at` (`change_timestamp`),
  KEY `idx_karyawan_history_changed_by` (`changed_by`),
  CONSTRAINT `karyawan_history_ibfk_1` FOREIGN KEY (`changed_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=891 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `karyawan_history`
--

LOCK TABLES `karyawan_history` WRITE;
/*!40000 ALTER TABLE `karyawan_history` DISABLE KEYS */;
/*!40000 ALTER TABLE `karyawan_history` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-23 22:13:52
