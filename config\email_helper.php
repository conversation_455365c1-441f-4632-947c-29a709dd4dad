<?php
require __DIR__ . '/../vendor/autoload.php';
require __DIR__ . '/email_config.php';

use P<PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

function sendVerificationEmail($to_email, $verification_code, $is_change_email = false, $user_id = null) {
    $mail = new PHPMailer(true);

    try {
        // Log email sending attempt
        error_log("Attempting to send verification email to: $to_email");
        error_log("SMTP Configuration - Host: " . SMTP_HOST . ", Port: " . SMTP_PORT . ", Username: " . SMTP_USERNAME);

        // Set SMTP configuration
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = SMTP_PORT;

        // Enable debug output
        $mail->SMTPDebug = 2; // 2 = client and server messages
        $mail->Debugoutput = function($str) {
            error_log("PHPMailer Debug: $str");
        };

        // Set sender information
        $mail->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);

        // Penerima
        $mail->addAddress($to_email);

        // Konten
        $mail->isHTML(true);
        $mail->Subject = $is_change_email ? 'Verifikasi Perubahan Email' : 'Kode Verifikasi Perubahan Email';

        // Buat link verifikasi jika user_id tersedia
        $verification_link = '';
        if ($user_id) {
            // Gunakan BASE_URL dari constants.php jika tersedia, atau buat URL relatif
            $base_url = defined('BASE_URL') ? BASE_URL : 'http://' . $_SERVER['HTTP_HOST'] . '/';
            $verification_link = $base_url . 'view/verify_email.php?token=' . urlencode($verification_code) . '&email=' . urlencode($to_email) . '&uid=' . $user_id;
        }

        // Template email
        $email_body = <<<EOT
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #BF0000; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .verification-code { font-size: 24px; font-weight: bold; text-align: center;
                                    padding: 10px; margin: 20px 0; background-color: #eee; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>Verifikasi Email</h2>
                </div>
                <div class="content">
                    <p>Halo, $to_email</p>
EOT;

        if ($is_change_email) {
            $email_body .= <<<EOT
                    <p><strong>Anda telah meminta untuk mengubah alamat email Anda ke $to_email.</strong></p>
                    <p>Untuk keamanan akun Anda, kami perlu memverifikasi bahwa email ini milik Anda.</p>
                    <p>Gunakan kode verifikasi berikut untuk menyelesaikan proses perubahan email:</p>
EOT;
        } else {
            $email_body .= <<<EOT
                    <p>Anda telah meminta untuk mengubah alamat email Anda ke $to_email. Gunakan kode verifikasi berikut untuk menyelesaikan proses perubahan email:</p>
EOT;
        }

        // Tambahkan link verifikasi jika tersedia
        if (!empty($verification_link)) {
            $email_body .= <<<EOT
                    <div class="verification-code">$verification_code</div>
                    <p>Kode ini akan kadaluarsa dalam 15 menit.</p>
                    <p>Atau klik tombol di bawah ini untuk memverifikasi email Anda secara langsung:</p>
                    <p style="text-align: center; margin: 25px 0;">
                        <a href="$verification_link" style="background-color: #BF0000; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">Verifikasi Email Saya</a>
                    </p>
                    <p>Jika tombol di atas tidak berfungsi, Anda dapat mengklik atau menyalin link berikut ke browser Anda:</p>
                    <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 5px; font-size: 12px;">
                        <a href="$verification_link">$verification_link</a>
                    </p>
                    <p>Jika Anda tidak meminta perubahan email, abaikan email ini dan hubungi administrator sistem.</p>
                </div>
                <div class="footer">
                    <p>Email ini dikirim secara otomatis, mohon tidak membalas email ini.</p>
                </div>
            </div>
        </body>
        </html>
        EOT;
        } else {
            $email_body .= <<<EOT
                    <div class="verification-code">$verification_code</div>
                    <p>Kode ini akan kadaluarsa dalam 15 menit.</p>
                    <p>Jika Anda tidak meminta perubahan email, abaikan email ini dan hubungi administrator sistem.</p>
                </div>
                <div class="footer">
                    <p>Email ini dikirim secara otomatis, mohon tidak membalas email ini.</p>
                </div>
            </div>
        </body>
        </html>
        EOT;
        }

        $mail->Body = $email_body;
        $mail->AltBody = "Kode verifikasi Anda: $verification_code\n\nKode ini akan kadaluarsa dalam 15 menit.";

        $mail->send();
        error_log("Email sent successfully to: $to_email");
        return true;
    } catch (Exception $exception) {
        error_log("Error sending verification email to $to_email: " . $exception->getMessage());
        error_log("PHPMailer Error: " . $mail->ErrorInfo);
        return false;
    }
}

function generateVerificationCode($length = 6) {
    return str_pad(random_int(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
}

// Function to update user's email after verification
function updateVerifiedEmail($user_id, $conn) {
    // Get the pending email
    $query = "SELECT pending_email FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $pending_email = $user['pending_email'];

        if (!empty($pending_email)) {
            // Update the email and clear pending_email
            $update_query = "UPDATE users SET email = ?, pending_email = NULL WHERE id = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("si", $pending_email, $user_id);

            if ($update_stmt->execute()) {
                return true;
            }
        }
    }

    return false;
}