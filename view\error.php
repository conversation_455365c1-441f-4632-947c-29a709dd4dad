<?php
/**
 * <PERSON><PERSON> error untuk keamanan
 */

// Konfigurasi session yang lebih aman
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.cookie_samesite', 'Lax');
ini_set('session.gc_maxlifetime', 3600);

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include constants.php first to define BASE_URL
require_once __DIR__ . '/../config/constants.php';

// Then include app_settings.php
if (!function_exists('get_app_name')) {
    require_once __DIR__ . '/../config/app_settings.php';
}

// Dapatkan pengaturan aplikasi
$app_name = get_app_name();
$company_logo = get_company_logo();
$current_page = basename($_SERVER['PHP_SELF']);

// Tambahkan security headers
header("Content-Security-Policy: default-src 'self'; script-src 'self' https://code.jquery.com https://cdn.jsdelivr.net 'unsafe-inline'; style-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-inline'; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self';");
header('X-Frame-Options: SAMEORIGIN');
header('X-Content-Type-Options: nosniff');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
header("Permissions-Policy: geolocation=(), microphone=(), camera=()");
header('Strict-Transport-Security: max-age=31536000; includeSubDomains');

// Tentukan jenis error
$error_type = isset($_GET['error']) ? $_GET['error'] : 'general';
$error_message = '';
$error_title = '';
$error_icon = '';
$error_description = '';

switch ($error_type) {
    case 'security':
        $error_title = 'Peringatan Keamanan';
        $error_icon = 'fas fa-shield-alt';
        $error_message = 'Kami mendeteksi aktivitas mencurigakan pada permintaan Anda.';
        $error_description = 'Untuk melindungi sistem kami, permintaan Anda telah diblokir. Jika Anda yakin ini adalah kesalahan, silakan hubungi administrator.';
        break;
    case '403':
        $error_title = 'Akses Ditolak';
        $error_icon = 'fas fa-ban';
        $error_message = 'Anda tidak memiliki izin untuk mengakses halaman ini.';
        $error_description = 'Silakan kembali ke halaman sebelumnya atau hubungi administrator jika Anda yakin seharusnya memiliki akses.';
        break;
    case '404':
        $error_title = 'Halaman Tidak Ditemukan';
        $error_icon = 'fas fa-search';
        $error_message = 'Halaman yang Anda cari tidak ditemukan.';
        $error_description = 'Halaman mungkin telah dipindahkan, dihapus, atau URL yang Anda masukkan salah.';
        break;
    case '500':
        $error_title = 'Kesalahan Server';
        $error_icon = 'fas fa-exclamation-triangle';
        $error_message = 'Terjadi kesalahan pada server kami.';
        $error_description = 'Tim teknis kami telah diberitahu dan sedang bekerja untuk memperbaikinya. Silakan coba lagi nanti.';
        break;
    case 'maintenance':
        $error_title = 'Sedang Dalam Pemeliharaan';
        $error_icon = 'fas fa-tools';
        $error_message = 'Sistem sedang dalam pemeliharaan.';
        $error_description = 'Kami sedang melakukan pemeliharaan terjadwal. Silakan coba lagi nanti.';
        break;
    default:
        $error_title = 'Terjadi Kesalahan';
        $error_icon = 'fas fa-exclamation-circle';
        $error_message = 'Terjadi kesalahan yang tidak terduga.';
        $error_description = 'Silakan coba lagi atau hubungi administrator jika masalah berlanjut.';
        break;
}

// Log error
$log_dir = __DIR__ . '/../logs';
if (!file_exists($log_dir)) {
    mkdir($log_dir, 0755, true);
}

$log_message = date('Y-m-d H:i:s') . " | Error: $error_type | " .
              "IP: " . $_SERVER['REMOTE_ADDR'] . " | " .
              "User-Agent: " . $_SERVER['HTTP_USER_AGENT'] . " | " .
              "URI: " . $_SERVER['REQUEST_URI'] . " | " .
              "User ID: " . ($_SESSION['user_id'] ?? 'Not logged in') . "\n";

error_log($log_message, 3, $log_dir . '/error.log');
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    :root {
        --primary-color: #BF0000;
        --primary-color-dark: #900000;
        --primary-color-light: rgba(191, 0, 0, 0.1);
        --primary-color-lighter: rgba(191, 0, 0, 0.05);
        --text-dark: #333333;
        --text-light: #ffffff;
        --text-muted: rgba(255, 255, 255, 0.8);
        --border-light: rgba(255, 255, 255, 0.2);
        --accent-color: #FF3333;
        --accent-light: rgba(255, 51, 51, 0.3);
        --accent-lighter: rgba(255, 51, 51, 0.1);
        --dark-accent: #333333;
        --dark-accent-light: rgba(51, 51, 51, 0.3);
        --spacing-xs: 5px;
        --spacing-sm: 10px;
        --spacing-md: 15px;
        --spacing-lg: 20px;
        --font-size-xs: 12px;
        --font-size-sm: 14px;
        --font-size-md: 16px;
        --font-size-lg: 18px;
        --border-radius-sm: 4px;
        --border-radius-md: 8px;
        --transition-fast: 0.2s ease;
        --box-shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.1);
        --box-shadow-md: 0 4px 10px rgba(0, 0, 0, 0.15);
    }

    body {
        font-family: 'Roboto', Arial, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 0;
    }

    .error-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 120px);
        padding: 20px;
        background: linear-gradient(135deg, #c40000 0%, #8b0000 100%);
        position: relative;
        z-index: 1;
    }

    .error-box {
        width: 100%;
        max-width: 600px;
        background: rgba(255, 255, 255, 0.98);
        border-radius: 15px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        padding: 40px;
        text-align: center;
        transition: all 0.3s ease;
    }

    .error-icon {
        font-size: 80px;
        color: var(--primary-color);
        margin-bottom: 20px;
    }

    .error-title {
        color: var(--primary-color);
        font-size: 28px;
        margin: 0 0 20px;
        font-weight: 600;
    }

    .error-message {
        color: var(--text-dark);
        font-size: 18px;
        margin-bottom: 15px;
        font-weight: 500;
    }

    .error-description {
        color: #666;
        font-size: 16px;
        margin-bottom: 30px;
        line-height: 1.5;
    }

    .btn {
        display: inline-block;
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        margin: 10px;
    }

    .btn-primary {
        background: #c40000;
        color: white;
    }

    .btn-primary:hover {
        background: #a00000;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    @media (max-width: 768px) {
        .error-box {
            padding: 30px 20px;
        }

        .error-icon {
            font-size: 60px;
        }

        .error-title {
            font-size: 24px;
        }

        .error-message {
            font-size: 16px;
        }

        .error-description {
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            font-size: 14px;
        }
    }

    @media (max-width: 480px) {
        .error-box {
            padding: 25px 15px;
        }

        .error-icon {
            font-size: 50px;
        }

        .error-title {
            font-size: 22px;
        }

        .error-message {
            font-size: 15px;
        }

        .error-description {
            font-size: 13px;
        }

        .btn {
            padding: 8px 16px;
            font-size: 13px;
            display: block;
            margin: 10px auto;
            max-width: 200px;
        }
    }
</style>
<body>
    <?php include '../config/navbara.php'; ?>

    <div class="error-container">
        <div class="error-box">
            <div class="error-icon">
                <i class="<?php echo $error_icon; ?>"></i>
            </div>
            <h1 class="error-title"><?php echo $error_title; ?></h1>
            <p class="error-message"><?php echo $error_message; ?></p>
            <p class="error-description"><?php echo $error_description; ?></p>
            <div class="error-actions">
                <a href="<?php echo BASE_URL; ?>" class="btn btn-primary">
                    <i class="fas fa-home"></i> Kembali ke Beranda
                </a>
                <?php if (isset($_SERVER['HTTP_REFERER'])): ?>
                <a href="<?php echo $_SERVER['HTTP_REFERER']; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Halaman Sebelumnya
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php include '../config/footer.php'; ?>
</body>
</html>
