<?php
/**
 * Quiz Results Page for Admin
 * This page displays the results of a quiz for all participants
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if quiz ID is provided
$quiz_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($quiz_id <= 0) {
    header('Location: manage_classes.php');
    exit();
}

// Get quiz information
$quiz_query = "SELECT q.*, c.title as class_title, c.id as class_id, t.training_topic
              FROM training_quizzes q
              JOIN training_classes c ON q.class_id = c.id
              JOIN training_submissions t ON c.training_id = t.id
              WHERE q.id = ?";
$stmt = $conn->prepare($quiz_query);
$stmt->bind_param("i", $quiz_id);
$stmt->execute();
$result = $stmt->get_result();
$quiz = $result->fetch_assoc();
$stmt->close();

if (!$quiz) {
    header('Location: manage_classes.php');
    exit();
}

// Get all attempts for this quiz
$attempts_query = "SELECT a.*, u.name, u.email, u.nik,
                  (SELECT COUNT(*) FROM training_quiz_answers ans WHERE ans.attempt_id = a.id AND ans.is_correct = 1) as correct_answers,
                  (SELECT COUNT(*) FROM training_quiz_answers ans WHERE ans.attempt_id = a.id) as total_answers
                  FROM training_quiz_attempts a
                  JOIN users u ON a.user_id = u.id
                  WHERE a.quiz_id = ? AND (a.status = 'completed' OR a.status = 'graded')
                  ORDER BY a.score DESC, a.end_time ASC";
$stmt = $conn->prepare($attempts_query);
$stmt->bind_param("i", $quiz_id);
$stmt->execute();
$attempts_result = $stmt->get_result();
$attempts = [];
while ($row = $attempts_result->fetch_assoc()) {
    $attempts[] = $row;
}
$stmt->close();

// Get questions for this quiz
$questions_query = "SELECT * FROM training_questions
                   WHERE quiz_id = ?
                   ORDER BY order_number ASC";
$stmt = $conn->prepare($questions_query);
$stmt->bind_param("i", $quiz_id);
$stmt->execute();
$questions_result = $stmt->get_result();
$questions = [];
while ($row = $questions_result->fetch_assoc()) {
    $questions[] = $row;
}
$stmt->close();

// Calculate statistics
$total_attempts = count($attempts);
$total_score = 0;
$passed_count = 0;
$highest_score = 0;
$lowest_score = 100;

foreach ($attempts as $attempt) {
    $total_score += $attempt['score'];
    if ($quiz['passing_score'] && $attempt['score'] >= $quiz['passing_score']) {
        $passed_count++;
    }
    if ($attempt['score'] > $highest_score) {
        $highest_score = $attempt['score'];
    }
    if ($attempt['score'] < $lowest_score) {
        $lowest_score = $attempt['score'];
    }
}

$average_score = $total_attempts > 0 ? round($total_score / $total_attempts) : 0;
$pass_rate = $total_attempts > 0 ? round(($passed_count / $total_attempts) * 100) : 0;

// Get question statistics
$question_stats = [];
foreach ($questions as $question) {
    $correct_answers_query = "SELECT COUNT(*) as correct_count
                             FROM training_quiz_answers a
                             JOIN training_quiz_attempts att ON a.attempt_id = att.id
                             WHERE a.question_id = ? AND a.is_correct = 1 AND (att.status = 'completed' OR att.status = 'graded')";
    $stmt = $conn->prepare($correct_answers_query);
    $stmt->bind_param("i", $question['id']);
    $stmt->execute();
    $correct_result = $stmt->get_result();
    $correct_row = $correct_result->fetch_assoc();
    $correct_count = $correct_row['correct_count'];
    $stmt->close();

    $total_answers_query = "SELECT COUNT(*) as total_count
                           FROM training_quiz_answers a
                           JOIN training_quiz_attempts att ON a.attempt_id = att.id
                           WHERE a.question_id = ? AND (att.status = 'completed' OR att.status = 'graded')";
    $stmt = $conn->prepare($total_answers_query);
    $stmt->bind_param("i", $question['id']);
    $stmt->execute();
    $total_result = $stmt->get_result();
    $total_row = $total_result->fetch_assoc();
    $total_count = $total_row['total_count'];
    $stmt->close();

    $correct_percentage = $total_count > 0 ? round(($correct_count / $total_count) * 100) : 0;

    $question_stats[$question['id']] = [
        'question_text' => $question['question_text'],
        'question_type' => $question['question_type'],
        'correct_count' => $correct_count,
        'total_count' => $total_count,
        'correct_percentage' => $correct_percentage
    ];
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<meta name="viewport" content="width=device-width, initial-scale=1">
<style>
    @media (max-width: 768px) {
    .quiz-title {
        font-size: 1.5rem;
    }

    .quiz-header-bar {
        flex-direction: column;
        align-items: flex-start;
        padding: 10px;
    }

    .timer {
        margin-top: 10px;
        font-size: 1rem;
    }

    .quiz-container {
        padding: 10px;
    }

    .question-text {
        font-size: 1.1rem;
    }

    .option-label {
        padding: 8px;
    }

    .option-input {
        margin-top: 2px;
    }

    .form-control.text-answer {
        width: 100%;
        padding: 10px;
        font-size: 1rem;
    }

    .quiz-actions {
        flex-direction: column;
        gap: 10px;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
    }

    .attempts-list {
        padding-left: 0;
    }

    .attempt-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .card-footer {
        text-align: left !important;
    }
}
    .results-header {
        background: linear-gradient(135deg, #4e73df, #224abe);
        color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .results-title {
        font-size: 1.8rem;
        margin-bottom: 10px;
    }

    .results-meta {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 15px;
    }

    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
    }

    .stat-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .attempts-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 30px;
    }

    .attempts-table th, .attempts-table td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #e9ecef;
    }

    .attempts-table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .attempts-table tr:hover {
        background-color: #f8f9fa;
    }

    .score-badge {
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: 600;
    }

    .score-high {
        background-color: #d4edda;
        color: #155724;
    }

    .score-medium {
        background-color: #fff3cd;
        color: #856404;
    }

    .score-low {
        background-color: #f8d7da;
        color: #721c24;
    }

    .questions-stats {
        margin-bottom: 30px;
    }

    .question-stat-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 15px;
    }

    .question-text {
        font-weight: 600;
        margin-bottom: 10px;
    }

    .question-meta {
        display: flex;
        justify-content: space-between;
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .progress {
        height: 10px;
        margin-bottom: 5px;
    }

    .progress-label {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
        color: #6c757d;
    }

    .nav-tabs {
        margin-bottom: 20px;
    }

    .tab-content {
        padding: 20px;
        background-color: #fff;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Chart container styles */
    .chart-container {
        position: relative;
        height: 100%;
        width: 100%;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="welcome-section">
                <div>
                    <h1><i class="fas fa-chart-bar"></i> Hasil Kuis</h1>
                    <p class="text-white">Lihat hasil kuis dari semua peserta</p>
                </div>
                <div>
                    <a href="export_quiz_results.php?id=<?= $quiz_id ?>" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> Ekspor Excel
                    </a>
                    <a href="edit_quiz.php?id=<?= $quiz_id ?>" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Kuis
                    </a>
                    <a href="manage_class.php?id=<?= $quiz['class_id'] ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali ke Kelas
                    </a>
                </div>
            </div>

            <div class="results-header">
                <h2 class="results-title"><?= htmlspecialchars($quiz['title']) ?></h2>
                <div class="results-meta">
                    <div><strong>Kelas:</strong> <?= htmlspecialchars($quiz['class_title']) ?></div>
                    <div><strong>Topik:</strong> <?= htmlspecialchars($quiz['training_topic']) ?></div>
                    <?php if ($quiz['passing_score']): ?>
                        <div><strong>Nilai Kelulusan:</strong> <?= $quiz['passing_score'] ?>%</div>
                    <?php endif; ?>
                    <?php if ($quiz['time_limit']): ?>
                        <div><strong>Batas Waktu:</strong> <?= $quiz['time_limit'] ?> menit</div>
                    <?php endif; ?>
                </div>
            </div>

            <ul class="nav nav-tabs" id="resultsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">
                        <i class="fas fa-chart-pie"></i> Ringkasan
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="attempts-tab" data-bs-toggle="tab" data-bs-target="#attempts" type="button" role="tab" aria-controls="attempts" aria-selected="false">
                        <i class="fas fa-users"></i> Peserta
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="questions-tab" data-bs-toggle="tab" data-bs-target="#questions" type="button" role="tab" aria-controls="questions" aria-selected="false">
                        <i class="fas fa-question-circle"></i> Pertanyaan
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="resultsTabsContent">
                <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                    <?php if (empty($attempts)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Belum ada peserta yang mengerjakan kuis ini.
                        </div>
                    <?php else: ?>
                        <div class="stats-container">
                            <div class="stat-card">
                                <div class="stat-value"><?= $total_attempts ?></div>
                                <div class="stat-label">Total Peserta</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value"><?= $average_score ?>%</div>
                                <div class="stat-label">Rata-rata Nilai</div>
                            </div>
                            <?php if ($quiz['passing_score']): ?>
                                <div class="stat-card">
                                    <div class="stat-value"><?= $pass_rate ?>%</div>
                                    <div class="stat-label">Tingkat Kelulusan</div>
                                </div>
                            <?php endif; ?>
                            <div class="stat-card">
                                <div class="stat-value"><?= $highest_score ?>%</div>
                                <div class="stat-label">Nilai Tertinggi</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value"><?= $lowest_score ?>%</div>
                                <div class="stat-label">Nilai Terendah</div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Distribusi Nilai</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="height: 300px;">
                                    <canvas id="scoreDistributionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="tab-pane fade" id="attempts" role="tabpanel" aria-labelledby="attempts-tab">
                    <?php if (empty($attempts)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Belum ada peserta yang mengerjakan kuis ini.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="attempts-table">
                                <thead>
                                    <tr>
                                        <th>Peserta</th>
                                        <th>NIK</th>
                                        <th>Nilai</th>
                                        <th>Jawaban Benar</th>
                                        <th>Waktu Pengerjaan</th>
                                        <th>Durasi</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($attempts as $attempt): ?>
                                        <tr>
                                            <td>
                                                <?= htmlspecialchars($attempt['name']) ?>
                                            </td>
                                            <td><?= htmlspecialchars($attempt['nik'] ?? '-') ?></td>
                                            <td>
                                                <span class="score-badge <?= $attempt['score'] >= 80 ? 'score-high' : ($attempt['score'] >= 60 ? 'score-medium' : 'score-low') ?>">
                                                    <?= $attempt['score'] ?>%
                                                </span>
                                            </td>
                                            <td><?= $attempt['correct_answers'] ?>/<?= $attempt['total_answers'] ?></td>
                                            <td><?= date('d M Y H:i', strtotime($attempt['end_time'])) ?></td>
                                            <td>
                                                <?php
                                                    $start = new DateTime($attempt['start_time']);
                                                    $end = new DateTime($attempt['end_time']);
                                                    $interval = $start->diff($end);

                                                    if ($interval->h > 0) {
                                                        echo $interval->format('%h jam %i menit');
                                                    } else {
                                                        echo $interval->format('%i menit %s detik');
                                                    }
                                                ?>
                                            </td>
                                            <td>
                                                <a href="view_attempt.php?id=<?= $attempt['id'] ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> Detail
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="tab-pane fade" id="questions" role="tabpanel" aria-labelledby="questions-tab">
                    <?php if (empty($question_stats)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Belum ada data statistik pertanyaan.
                        </div>
                    <?php else: ?>
                        <div class="questions-stats">
                            <?php foreach ($question_stats as $q_id => $stat): ?>
                                <div class="question-stat-card">
                                    <div class="question-text"><?= htmlspecialchars($stat['question_text']) ?></div>
                                    <div class="question-meta">
                                        <div>Tipe: <?= ucfirst(str_replace('_', ' ', $stat['question_type'])) ?></div>
                                        <div>Jawaban Benar: <?= $stat['correct_count'] ?>/<?= $stat['total_count'] ?></div>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar <?= $stat['correct_percentage'] >= 70 ? 'bg-success' : ($stat['correct_percentage'] >= 40 ? 'bg-warning' : 'bg-danger') ?>"
                                             role="progressbar"
                                             style="width: <?= $stat['correct_percentage'] ?>%"
                                             aria-valuenow="<?= $stat['correct_percentage'] ?>"
                                             aria-valuemin="0"
                                             aria-valuemax="100"></div>
                                    </div>
                                    <div class="progress-label">
                                        <span>Tingkat Keberhasilan</span>
                                        <span><?= $stat['correct_percentage'] ?>%</span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if (!empty($attempts)): ?>
    // Prepare data for score distribution chart
    const scoreRanges = ['0-20', '21-40', '41-60', '61-80', '81-100'];
    const scoreDistribution = [0, 0, 0, 0, 0];

    <?php foreach ($attempts as $attempt): ?>
    const score = <?= $attempt['score'] ?>;
    if (score <= 20) {
        scoreDistribution[0]++;
    } else if (score <= 40) {
        scoreDistribution[1]++;
    } else if (score <= 60) {
        scoreDistribution[2]++;
    } else if (score <= 80) {
        scoreDistribution[3]++;
    } else {
        scoreDistribution[4]++;
    }
    <?php endforeach; ?>

    // Debug: Log the score distribution
    console.log('Score Distribution:', scoreDistribution);

    // Check if all scores are in the same range (e.g., all 100%)
    const allSameRange = scoreDistribution.filter(count => count > 0).length === 1;
    console.log('All scores in same range:', allSameRange);

    // Create score distribution chart
    const ctx = document.getElementById('scoreDistributionChart');
    if (!ctx) {
        console.error('Canvas element not found!');
        return;
    }

    const ctxContext = ctx.getContext('2d');
    new Chart(ctxContext, {
        type: 'bar',
        data: {
            labels: scoreRanges,
            datasets: [{
                label: 'Jumlah Peserta',
                data: scoreDistribution,
                backgroundColor: [
                    'rgba(220, 53, 69, 0.7)',
                    'rgba(255, 193, 7, 0.7)',
                    'rgba(255, 193, 7, 0.7)',
                    'rgba(40, 167, 69, 0.7)',
                    'rgba(40, 167, 69, 0.7)'
                ],
                borderColor: [
                    'rgba(220, 53, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(40, 167, 69, 1)',
                    'rgba(40, 167, 69, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    },
                    // If all scores are in the same range, set max to make the chart visible
                    suggestedMax: allSameRange ? 2 : undefined
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.raw} peserta`;
                        }
                    }
                }
            }
        }
    });
    <?php endif; ?>
});
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
