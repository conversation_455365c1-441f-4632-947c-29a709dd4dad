<?php
/**
 * Test Delete Integration
 * This file helps test the delete integration with batch history
 */

session_start();
require_once '../config/config.php';
require_once 'karyawan_schema_helper.php';

// Check if the user is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role_id'] != 99) {
    echo 'Unauthorized access';
    exit();
}

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Delete Integration Test</h2>";
    
    // Check if there are test employees to delete
    echo "<h3>1. Available Test Employees:</h3>";

    // Use helper function to get safe query
    $query = getKaryawanSelectQuery($pdo, [], "nik LIKE 'TEST%'", 'nik');
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $test_employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get display headers
    $display_info = getKaryawanDisplayHeaders($pdo);
    $display_columns = $display_info['headers'];
    $select_columns = $display_info['columns'];

    if (empty($test_employees)) {
        echo "<p style='color: orange;'>⚠️ No test employees found. You can:</p>";
        echo "<ol>";
        echo "<li><a href='create_test_batch.php'>Create test employees first</a></li>";
        echo "<li>Or test with existing employees (be careful!)</li>";
        echo "</ol>";
    } else {
        echo "<p>Found " . count($test_employees) . " test employees:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr>";
        foreach ($display_columns as $col) {
            echo "<th>" . $col . "</th>";
        }
        echo "<th>Action</th></tr>";

        foreach ($test_employees as $emp) {
            echo "<tr>";
            foreach ($select_columns as $col) {
                echo "<td>" . htmlspecialchars($emp[$col] ?? '') . "</td>";
            }
            echo "<td>";
            echo "<button onclick='deleteTestEmployee(\"" . htmlspecialchars($emp['nik']) . "\")' style='background: #dc3545; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer;'>Delete</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Show recent delete history
    echo "<h3>2. Recent Delete History:</h3>";
    $stmt = $pdo->prepare("
        SELECT batch_id, action_type, batch_data, change_timestamp, is_rollback_capable, rollback_status
        FROM karyawan_batch_history 
        WHERE action_type IN ('INDIVIDUAL_DELETE', 'BATCH_DELETE') 
        ORDER BY change_timestamp DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $delete_history = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($delete_history)) {
        echo "<p>No delete history found yet.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Batch ID</th><th>Action Type</th><th>NIKs</th><th>Timestamp</th><th>Rollback Status</th></tr>";
        
        foreach ($delete_history as $history) {
            $batch_data = json_decode($history['batch_data'], true);
            $niks = isset($batch_data['niks']) ? $batch_data['niks'] : [];
            $nik_display = count($niks) <= 3 ? implode(', ', $niks) : count($niks) . ' NIKs';
            
            echo "<tr>";
            echo "<td>" . $history['batch_id'] . "</td>";
            echo "<td>" . $history['action_type'] . "</td>";
            echo "<td>" . $nik_display . "</td>";
            echo "<td>" . $history['change_timestamp'] . "</td>";
            echo "<td>" . $history['rollback_status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Integration status
    echo "<h3>3. Integration Status:</h3>";
    
    $integrations = [
        'delete_employee.php' => 'Individual employee deletion',
        'delete_karyawan.php' => 'Batch employee deletion',
        'batch_employee_history.php' => 'History display with INDIVIDUAL_DELETE support'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>File</th><th>Purpose</th><th>Status</th></tr>";
    
    foreach ($integrations as $file => $purpose) {
        $file_path = $file;
        $status = file_exists($file_path) ? '✅ Integrated' : '❌ Missing';
        
        // Check for specific integration markers
        if (file_exists($file_path)) {
            $content = file_get_contents($file_path);
            if (strpos($content, 'recordBatchEmployeeHistory') !== false) {
                $status = '✅ Fully Integrated';
            } elseif (strpos($content, 'INDIVIDUAL_DELETE') !== false) {
                $status = '✅ Partially Integrated';
            }
        }
        
        echo "<tr>";
        echo "<td>" . $file . "</td>";
        echo "<td>" . $purpose . "</td>";
        echo "<td>" . $status . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Instructions
    echo "<h3>4. How to Test:</h3>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>";
    echo "<h4>Method 1: Test with Test Employees (Safe)</h4>";
    echo "<ol>";
    echo "<li>If no test employees exist, <a href='create_test_batch.php'>create them first</a></li>";
    echo "<li>Click 'Delete' button above for any test employee</li>";
    echo "<li>Check <a href='batch_employee_history.php'>batch history</a> for INDIVIDUAL_DELETE record</li>";
    echo "</ol>";
    
    echo "<h4>Method 2: Test via Employee Management</h4>";
    echo "<ol>";
    echo "<li>Go to <a href='employee_management.php'>Employee Management</a></li>";
    echo "<li>Find a test employee (NIK starting with TEST)</li>";
    echo "<li>Click delete button in the actions column</li>";
    echo "<li>Confirm deletion</li>";
    echo "<li>Check <a href='batch_employee_history.php'>batch history</a> for new record</li>";
    echo "</ol>";
    
    echo "<h4>Method 3: Test Batch Deletion</h4>";
    echo "<ol>";
    echo "<li>Go to <a href='delete_karyawan.php'>Batch Delete</a></li>";
    echo "<li>Upload Excel file with test NIKs</li>";
    echo "<li>Process deletion</li>";
    echo "<li>Check <a href='batch_employee_history.php'>batch history</a> for BATCH_DELETE record</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h3>5. Expected Results:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
    echo "<ul>";
    echo "<li><strong>Individual Delete:</strong> Creates INDIVIDUAL_DELETE record with single NIK</li>";
    echo "<li><strong>Batch Delete:</strong> Creates BATCH_DELETE record with multiple NIKs</li>";
    echo "<li><strong>History Display:</strong> Shows both types in batch history with proper labels</li>";
    echo "<li><strong>Rollback Status:</strong> Currently set to 'NONE' (requires enhanced history for rollback)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<script>
function deleteTestEmployee(nik) {
    if (confirm('Delete test employee with NIK: ' + nik + '?\n\nThis will test the delete integration and create a history record.')) {
        fetch('delete_employee.php?nik=' + encodeURIComponent(nik))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Employee deleted successfully!\n\nCheck batch history to see the INDIVIDUAL_DELETE record.');
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error);
        });
    }
}
</script>

<style>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
