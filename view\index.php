<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Selamat Datang</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color:rgb(224, 222, 222);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        
        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(191, 0, 0, 0.2);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        h1 {
            color: #BF0000;
            margin-bottom: 20px;
        }
        
        p {
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .btn {
            background-color: #BF0000;
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background-color: #900000;
            transform: scale(1.05);
        }
        
        .features {
            display: flex;
            justify-content: space-around;
            margin: 40px 0;
            flex-wrap: wrap;
        }
        
        .feature {
            margin: 10px;
            flex: 1;
            min-width: 120px;
        }
        
        .feature i {
            font-size: 40px;
            color: #BF0000;
            margin-bottom: 10px;
        }
        
        footer {
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
        
        @media (max-width: 600px) {
            .container {
                padding: 20px;
                margin: 0 15px;
            }
        }
    </style>
    <!-- Font Awesome untuk ikon -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>Selamat Datang di Aplikasi LnD</h1>
        <p>Temukan kemudahan dan pengalaman terbaik dengan fitur-fitur unggulan yang kami tawarkan. Silakan login/aktivasi akun untuk melanjutkan.</p>
        
        <div class="features">
            <div class="feature">
                <i class="fas fa-lock"></i>
                <p>Keamanan Terjamin</p>
            </div>
            <div class="feature">
                <i class="fas fa-bolt"></i>
                <p>Akses Cepat</p>
            </div>
            <div class="feature">
                <i class="fas fa-user-shield"></i>
                <p>Privasi Terlindungi</p>
            </div>
        </div>
        
        <a href="login.php" class="btn" id="loginBtn">Login Sekarang</a>
        
        <footer>
            <p>&copy; <?php echo date("Y"); ?> Hak Cipta Dilindungi</p>
        </footer>
    </div>

    <script>
        // Menambahkan efek hover pada tombol
        document.getElementById('loginBtn').addEventListener('mouseover', function() {
            this.style.boxShadow = '0 5px 15px rgba(191, 0, 0, 0.4)';
        });
        
        document.getElementById('loginBtn').addEventListener('mouseout', function() {
            this.style.boxShadow = 'none';
        });
        
        // Animasi sederhana saat halaman dimuat
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            container.style.transition = 'all 0.5s ease';
            
            setTimeout(function() {
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>