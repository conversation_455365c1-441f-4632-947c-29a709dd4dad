<?php
/**
 * Edit Participant Page for Admin
 * This page allows admins to edit a participant's role in a training class
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];

// Check if participant ID is provided
$participant_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($participant_id <= 0) {
    $_SESSION['error_message'] = "ID peserta tidak valid.";
    header('Location: manage_classes.php');
    exit();
}

// Get participant information
$participant_query = "SELECT p.*, c.id as class_id, c.title as class_title, 
                     t.training_topic, u.id as user_id 
                     FROM training_participants p
                     JOIN training_classes c ON p.class_id = c.id
                     JOIN training_submissions t ON c.training_id = t.id
                     JOIN users u ON p.user_id = u.id
                     WHERE p.id = ?";

// First, let's check the structure of the users table to find the name column
$user_table_query = "DESCRIBE users";
$user_table_result = $conn->query($user_table_query);
$name_column = 'name'; // Default to name

if ($user_table_result) {
    while ($column = $user_table_result->fetch_assoc()) {
        // Look for common name column patterns
        if (in_array(strtolower($column['Field']), ['full_name', 'name', 'user_name', 'username', 'nama'])) {
            $name_column = $column['Field'];
            // Prefer full_name or name if available
            if (in_array(strtolower($column['Field']), ['full_name', 'name', 'nama'])) {
                break;
            }
        }
    }
}

// Update the query to use the correct name column
$participant_query = "SELECT p.*, c.id as class_id, c.title as class_title, 
                     t.training_topic, u.id as user_id, u.$name_column as user_name,
                     u.email as user_email
                     FROM training_participants p
                     JOIN training_classes c ON p.class_id = c.id
                     JOIN training_submissions t ON c.training_id = t.id
                     JOIN users u ON p.user_id = u.id
                     WHERE p.id = ?";

$stmt = $conn->prepare($participant_query);
$stmt->bind_param("i", $participant_id);
$stmt->execute();
$result = $stmt->get_result();
$participant = $result->fetch_assoc();
$stmt->close();

if (!$participant) {
    $_SESSION['error_message'] = "Peserta tidak ditemukan.";
    header('Location: manage_classes.php');
    exit();
}

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_participant'])) {
    $role = $_POST['role'];
    $status = $_POST['status'];
    
    // Update participant
    $update_query = "UPDATE training_participants 
                    SET role = ?, status = ? 
                    WHERE id = ?";
    $stmt = $conn->prepare($update_query);
    $stmt->bind_param("ssi", $role, $status, $participant_id);
    
    if ($stmt->execute()) {
        $success_message = "Informasi peserta berhasil diperbarui.";
        
        // Update participant data
        $participant['role'] = $role;
        $participant['status'] = $status;
    } else {
        $error_message = "Gagal memperbarui informasi peserta: " . $conn->error;
    }
    
    $stmt->close();
}

// Check for session messages
if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

if (isset($_SESSION['error_message'])) {
    $error_message = $_SESSION['error_message'];
    unset($_SESSION['error_message']);
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .user-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .user-info {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 24px;
        color: #6c757d;
    }
    
    .user-details h5 {
        margin-bottom: 5px;
    }
    
    .user-details p {
        margin-bottom: 5px;
        color: #6c757d;
    }
    
    .form-section {
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="welcome-section">
                <h1><i class="fas fa-user-edit"></i> Edit Peserta</h1>
                <a href="manage_class.php?id=<?= $participant['class_id'] ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Kelas
                </a>
            </div>
            
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <div class="form-container">
                <h3>Informasi Kelas</h3>
                <div class="mb-3">
                    <label class="form-label">Judul Kelas</label>
                    <div class="form-control bg-light"><?= htmlspecialchars($participant['class_title']) ?></div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Topik Training</label>
                    <div class="form-control bg-light"><?= htmlspecialchars($participant['training_topic']) ?></div>
                </div>
        
            
            <div class="row">
                <div class="col-md-4">
                    <div class="user-card">
                        <div class="user-info">
                            <div class="user-avatar">
                                <?= strtoupper(substr($participant['user_name'], 0, 1)) ?>
                            </div>
                            <div class="user-details">
                                <h5><?= htmlspecialchars($participant['user_name']) ?></h5>
                                <p><i class="fas fa-envelope"></i> <?= htmlspecialchars($participant['user_email']) ?></p>
                            </div>
                        </div>
                        
                        <div class="current-status">
                            <p><strong>Peran Saat Ini:</strong> 
                                <?php
                                    $role_labels = [
                                        'student' => 'Peserta',
                                        'instructor' => 'Instruktur',
                                        'assistant' => 'Asisten'
                                    ];
                                    echo $role_labels[$participant['role']] ?? $participant['role'];
                                ?>
                            </p>
                            <p><strong>Status Saat Ini:</strong> 
                                <?php
                                    $status_labels = [
                                        'active' => 'Aktif',
                                        'inactive' => 'Tidak Aktif',
                                        'completed' => 'Selesai',
                                        'dropped' => 'Keluar'
                                    ];
                                    echo $status_labels[$participant['status']] ?? $participant['status'];
                                ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="form-section">
                        <h4 class="mb-4">Edit Informasi Peserta</h4>
                        
                        <form method="post" action="">
                            <div class="mb-3">
                                <label for="role" class="form-label">Peran dalam Kelas</label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="student" <?= $participant['role'] == 'student' ? 'selected' : '' ?>>Peserta</option>
                                    <option value="instructor" <?= $participant['role'] == 'instructor' ? 'selected' : '' ?>>Instruktur</option>
                                    <option value="assistant" <?= $participant['role'] == 'assistant' ? 'selected' : '' ?>>Asisten</option>
                                </select>
                                <div class="form-text">Pilih peran peserta dalam kelas ini</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="status" class="form-label">Status Peserta</label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="active" <?= $participant['status'] == 'active' ? 'selected' : '' ?>>Aktif</option>
                                    <option value="inactive" <?= $participant['status'] == 'inactive' ? 'selected' : '' ?>>Tidak Aktif</option>
                                    <option value="completed" <?= $participant['status'] == 'completed' ? 'selected' : '' ?>>Selesai</option>
                                    <option value="dropped" <?= $participant['status'] == 'dropped' ? 'selected' : '' ?>>Keluar</option>
                                </select>
                                <div class="form-text">Pilih status peserta dalam kelas ini</div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="remove_participant.php?id=<?= $participant_id ?>&class_id=<?= $participant['class_id'] ?>" class="btn btn-danger" onclick="return confirmAction('Apakah Anda yakin ingin menghapus peserta ini dari kelas?')">
                                    <i class="fas fa-trash"></i> Hapus Peserta
                                </a>
                                <button type="submit" name="update_participant" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Simpan Perubahan
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);
    });
    
    // Confirmation function
    function confirmAction(message, callback) {
        // Create modal element
        const modalHtml = `
            <div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="confirmModalLabel">Konfirmasi</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            ${message}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="button" class="btn btn-primary" id="confirmButton">Ya, Lanjutkan</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Append modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Get modal element
        const modalElement = document.getElementById('confirmModal');
        const modal = new bootstrap.Modal(modalElement);
        
        // Show modal
        modal.show();
        
        // Handle confirm button click
        document.getElementById('confirmButton').addEventListener('click', function() {
            modal.hide();
            if (typeof callback === 'function') {
                callback();
            } else {
                return true;
            }
        });
        
        // Handle modal hidden event
        modalElement.addEventListener('hidden.bs.modal', function() {
            modalElement.remove();
        });
        
        return false;
    }
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
