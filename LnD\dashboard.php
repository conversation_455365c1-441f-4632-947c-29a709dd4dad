<?php
include '../config/config.php';
include 'security.php';
$user_id = $_SESSION['user_id'];

$departments_stats = [];

// Query untuk mengambil semua training yang sudah melalui atau sedang dalam proses approval LnD
$query = "SELECT
    ts.id,
    ts.training_topic,
    ts.start_date,
    ts.end_date,
    ts.is_confirmed,
    ts.approved_hrd,
    ts.status,
    ts.rejected_by,
    ts.status AS training_status,
    ts.current_approver_role_id,
    u.name AS requester_name,
    u.dept AS departemen,
    COUNT(p.id) AS total_participants
FROM training_submissions ts
LEFT JOIN users u ON ts.user_id = u.id
LEFT JOIN participants p ON ts.id = p.training_id
WHERE (ts.current_approver_role_id = ? OR ts.approved_hrd IN ('Approved', 'Pending', 'Rejected'))
GROUP BY ts.id, ts.current_approver_role_id, u.name, u.dept
ORDER BY ts.id DESC";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $_SESSION['role_id']);
$stmt->execute();
$result = $stmt->get_result();

// Query untuk menghitung total training yang relevan untuk LnD
$query_total = "SELECT COUNT(*) as total_submissions
               FROM training_submissions
               WHERE approved_hrd IN ('Approved', 'Pending', 'Rejected')";

$stmt_total = $conn->prepare($query_total);
$stmt_total->execute();
$result_total = $stmt_total->get_result();
$total_submissions = $result_total->fetch_assoc()['total_submissions'];

// Calculate statistics using consistent logic
$stats = [
    'total' => 0,
    'pending' => 0,
    'approved' => 0,
    'rejected' => 0,
];

// Process results and calculate stats
$result_array = [];
$pending_for_lnd = []; // Training yang sedang menunggu approval LnD

while ($row = $result->fetch_assoc()) {
    $result_array[] = $row;
    $stats['total']++;

    // Logika statistik berdasarkan status training
    if ($row['status'] == 'Rejected') {
        $stats['rejected']++;
    } elseif ($row['approved_hrd'] == 'Approved') {
        $stats['approved']++;
    } elseif ($row['approved_hrd'] == 'Pending') {
        $stats['pending']++;
        // Jika sedang menunggu approval LnD, masukkan ke array terpisah
        if (isset($row['current_approver_role_id']) && $row['current_approver_role_id'] == $_SESSION['role_id']) {
            $pending_for_lnd[] = $row;
        }
    } elseif ($row['approved_hrd'] == 'Rejected') {
        $stats['rejected']++;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<body>

<div class="jarak"></div>
<?php include '../config/navbara.php'; ?>
<div class="container-form">
    <div class="notifications-container">
        <?php if (isset($_SESSION['errors'])): ?>
            <div class="error-message">
                <div class="notification-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="notification-content">
                    <?php foreach ($_SESSION['errors'] as $error): ?>
                        <p><?php echo htmlspecialchars($error); ?></p>
                    <?php endforeach; ?>
                </div>
                <button class="notification-close" onclick="this.parentElement.style.animation='slideOut 0.5s forwards';setTimeout(() => this.parentElement.remove(), 500)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <?php unset($_SESSION['errors']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['success'])): ?>
            <div class="success-message">
                <div class="notification-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="notification-content">
                    <p><?php echo htmlspecialchars($_SESSION['success']); ?></p>
                </div>
                <button class="notification-close" onclick="this.parentElement.style.animation='slideOut 0.5s forwards';setTimeout(() => this.parentElement.remove(), 500)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>
    </div>

    <?php include '../config/setujulnd.php'; ?>
</div>

<script>
    // Auto-hide notifications after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const notifications = document.querySelectorAll('.success-message, .error-message');

        notifications.forEach(function(notification) {
            // Set timeout to hide notification
            setTimeout(function() {
                notification.style.animation = 'slideOut 0.5s ease-in forwards';

                // Remove from DOM after animation completes
                setTimeout(function() {
                    notification.remove();
                }, 500);
            }, 5000); // 5 seconds

            // Add click event to close buttons
            const closeButtons = document.querySelectorAll('.notification-close');
            closeButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    const notification = this.closest('.success-message, .error-message');
                    if (notification) {
                        notification.style.animation = 'slideOut 0.5s ease-in forwards';
                        setTimeout(function() {
                            notification.remove();
                        }, 500);
                    }
                });
            });
        });
    });
    
</script>

<?php include '../config/footer.php'; ?>
</body>
</html>
