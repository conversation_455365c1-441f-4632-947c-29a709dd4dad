<?php
/**
 * <PERSON><PERSON>wan Schema Helper
 * This file provides helper functions to work with karyawan table schema
 */

/**
 * Get available columns in karyawan table
 */
function getKaryawanColumns($pdo) {
    static $columns = null;
    
    if ($columns === null) {
        $stmt = $pdo->prepare("DESCRIBE karyawan");
        $stmt->execute();
        $schema = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $columns = array_column($schema, 'Field');
    }
    
    return $columns;
}

/**
 * Get safe SELECT query for karyawan table
 */
function getKaryawanSelectQuery($pdo, $additional_columns = [], $where_clause = '', $order_by = 'nik') {
    $available_columns = getKaryawanColumns($pdo);
    
    // Always include NIK as primary identifier
    $select_columns = ['nik'];
    
    // Common columns to include if available
    $preferred_columns = ['nama', 'name', 'jabatan', 'position', 'departemen', 'department', 'bagian', 'section', 'is_active'];
    
    foreach ($preferred_columns as $col) {
        if (in_array($col, $available_columns) && !in_array($col, $select_columns)) {
            $select_columns[] = $col;
        }
    }
    
    // Add any additional requested columns
    foreach ($additional_columns as $col) {
        if (in_array($col, $available_columns) && !in_array($col, $select_columns)) {
            $select_columns[] = $col;
        }
    }
    
    $query = "SELECT " . implode(', ', $select_columns) . " FROM karyawan";
    
    if (!empty($where_clause)) {
        $query .= " WHERE " . $where_clause;
    }
    
    if (!empty($order_by) && in_array($order_by, $available_columns)) {
        $query .= " ORDER BY " . $order_by;
    }
    
    return $query;
}

/**
 * Get safe INSERT query for karyawan table
 */
function getKaryawanInsertQuery($pdo, $data) {
    $available_columns = getKaryawanColumns($pdo);
    
    $insert_columns = [];
    $insert_values = [];
    $insert_data = [];
    
    // Map common field names
    $field_mapping = [
        'nik' => 'nik',
        'nama' => ['nama', 'name'],
        'jabatan' => ['jabatan', 'position'],
        'departemen' => ['departemen', 'department'],
        'bagian' => ['bagian', 'section'],
        'is_active' => 'is_active'
    ];
    
    foreach ($field_mapping as $key => $possible_columns) {
        if (is_array($possible_columns)) {
            foreach ($possible_columns as $col) {
                if (in_array($col, $available_columns)) {
                    if (isset($data[$key])) {
                        $insert_columns[] = $col;
                        $insert_values[] = '?';
                        $insert_data[] = $data[$key];
                    }
                    break;
                }
            }
        } else {
            if (in_array($possible_columns, $available_columns) && isset($data[$key])) {
                $insert_columns[] = $possible_columns;
                $insert_values[] = '?';
                $insert_data[] = $data[$key];
            }
        }
    }
    
    if (empty($insert_columns)) {
        throw new Exception("No valid columns found for insert");
    }
    
    $query = "INSERT INTO karyawan (" . implode(', ', $insert_columns) . ") VALUES (" . implode(', ', $insert_values) . ")";
    
    return ['query' => $query, 'data' => $insert_data, 'columns' => $insert_columns];
}

/**
 * Get display name for employee
 */
function getEmployeeDisplayName($employee_data) {
    $available_columns = getKaryawanColumns(null);
    
    // Try different name columns
    $name_columns = ['nama', 'name', 'full_name', 'employee_name'];
    
    foreach ($name_columns as $col) {
        if (isset($employee_data[$col]) && !empty($employee_data[$col])) {
            return $employee_data[$col];
        }
    }
    
    // Fallback to NIK if no name found
    return $employee_data['nik'] ?? 'Unknown';
}

/**
 * Get table headers for display
 */
function getKaryawanDisplayHeaders($pdo) {
    $available_columns = getKaryawanColumns($pdo);
    
    $headers = [];
    $columns = [];
    
    // NIK is always first
    if (in_array('nik', $available_columns)) {
        $headers[] = 'NIK';
        $columns[] = 'nik';
    }
    
    // Name column
    $name_columns = ['nama', 'name', 'full_name'];
    foreach ($name_columns as $col) {
        if (in_array($col, $available_columns)) {
            $headers[] = 'Nama';
            $columns[] = $col;
            break;
        }
    }
    
    // Position column
    $position_columns = ['jabatan', 'position'];
    foreach ($position_columns as $col) {
        if (in_array($col, $available_columns)) {
            $headers[] = 'Jabatan';
            $columns[] = $col;
            break;
        }
    }
    
    // Department column
    $dept_columns = ['departemen', 'department'];
    foreach ($dept_columns as $col) {
        if (in_array($col, $available_columns)) {
            $headers[] = 'Departemen';
            $columns[] = $col;
            break;
        }
    }
    
    // Section column
    $section_columns = ['bagian', 'section'];
    foreach ($section_columns as $col) {
        if (in_array($col, $available_columns)) {
            $headers[] = 'Bagian';
            $columns[] = $col;
            break;
        }
    }
    
    return ['headers' => $headers, 'columns' => $columns];
}

/**
 * Debug function to show table structure
 */
function debugKaryawanSchema($pdo) {
    echo "<h3>Karyawan Table Schema Debug:</h3>";
    
    $stmt = $pdo->prepare("DESCRIBE karyawan");
    $stmt->execute();
    $schema = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    foreach ($schema as $column) {
        echo "<tr>";
        echo "<td><strong>" . $column['Field'] . "</strong></td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show sample data
    echo "<h4>Sample Data (First 3 records):</h4>";
    $sample_query = getKaryawanSelectQuery($pdo, [], '', 'nik LIMIT 3');
    $stmt = $pdo->prepare($sample_query);
    $stmt->execute();
    $sample_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($sample_data)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        
        // Headers
        echo "<tr>";
        foreach (array_keys($sample_data[0]) as $column) {
            echo "<th>" . $column . "</th>";
        }
        echo "</tr>";
        
        // Data
        foreach ($sample_data as $row) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No sample data available.</p>";
    }
}
?>
