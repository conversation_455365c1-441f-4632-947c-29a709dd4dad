-- ============================================================================
-- Script untuk menambahkan kolom tracking completion (OPSIONAL)
-- ============================================================================

-- Jika Anda ingin menambahkan kolom untuk tracking kapan training completed
-- Jalankan script ini untuk menambahkan kolom completed_at

-- ============================================================================
-- 1. BACKUP DATA TERLEBIH DAHULU
-- ============================================================================

-- Backup tabel training_submissions
CREATE TABLE training_submissions_backup AS SELECT * FROM training_submissions;

-- Backup tabel offline_training  
CREATE TABLE offline_training_backup AS SELECT * FROM offline_training;

-- ============================================================================
-- 2. TAMBAHKAN KOLOM COMPLETED_AT (OPSIONAL)
-- ============================================================================

-- Tambahkan kolom completed_at ke training_submissions
ALTER TABLE training_submissions 
ADD COLUMN completed_at DATETIME NULL COMMENT 'Waktu training diselesaikan (auto-update)';

-- Tambahkan kolom completed_at ke offline_training
ALTER TABLE offline_training 
ADD COLUMN completed_at DATETIME NULL COMMENT 'Waktu training diselesaikan (auto-update)';

-- ============================================================================
-- 3. UPDATE EXISTING COMPLETED TRAINING
-- ============================================================================

-- Update existing completed training dengan timestamp saat ini
UPDATE training_submissions 
SET completed_at = NOW() 
WHERE status = 'Completed' AND completed_at IS NULL;

UPDATE offline_training 
SET completed_at = NOW() 
WHERE status = 'Completed' AND completed_at IS NULL;

-- ============================================================================
-- 4. VERIFIKASI HASIL
-- ============================================================================

-- Cek struktur tabel training_submissions
DESCRIBE training_submissions;

-- Cek struktur tabel offline_training  
DESCRIBE offline_training;

-- Cek data completed training
SELECT id, training_topic, status, completed_at 
FROM training_submissions 
WHERE status = 'Completed' 
LIMIT 5;

SELECT id, training_topic, status, completed_at 
FROM offline_training 
WHERE status = 'Completed' 
LIMIT 5;

-- ============================================================================
-- 5. ROLLBACK JIKA DIPERLUKAN
-- ============================================================================

-- Jika ada masalah, rollback dengan:
-- DROP TABLE training_submissions;
-- RENAME TABLE training_submissions_backup TO training_submissions;

-- DROP TABLE offline_training;
-- RENAME TABLE offline_training_backup TO offline_training;

-- ============================================================================
-- 6. CLEANUP BACKUP (SETELAH YAKIN TIDAK ADA MASALAH)
-- ============================================================================

-- Hapus backup setelah yakin tidak ada masalah
-- DROP TABLE training_submissions_backup;
-- DROP TABLE offline_training_backup;

-- ============================================================================
-- CATATAN PENTING
-- ============================================================================

/*
1. Script ini OPSIONAL - sistem auto-update sudah berfungsi tanpa kolom completed_at
2. Kolom completed_at hanya untuk tracking tambahan jika diperlukan
3. Jika tidak ingin menambahkan kolom, sistem tetap berfungsi normal
4. Auto-update helper sudah diperbaiki untuk tidak menggunakan completed_at
5. Status 'Completed' sudah cukup untuk menandai training selesai
*/
