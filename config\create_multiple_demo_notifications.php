<?php
/**
 * Script untuk membuat beberapa notifikasi demo dengan jenis berbeda
 */

// Disable session untuk testing
define('NO_SESSION', true);

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/../includes/notification_helper.php';

echo "🔔 MEMBUAT MULTIPLE DEMO NOTIFICATIONS\n";
echo "======================================\n\n";

// Cari user aktif
$user_query = "SELECT id, name FROM users WHERE is_active = 1 LIMIT 3";
$user_result = $conn->query($user_query);

$demo_users = [];
while ($user = $user_result->fetch_assoc()) {
    $demo_users[] = $user;
}

if (empty($demo_users)) {
    echo "❌ Tidak ada user aktif ditemukan\n";
    exit(1);
}

echo "👥 User yang akan menerima notifikasi demo:\n";
foreach ($demo_users as $user) {
    echo "   - {$user['name']} (ID: {$user['id']})\n";
}
echo "\n";

// Notifikasi demo dengan berbagai jenis
$demo_notifications = [
    [
        'title' => '🎓 Training Baru: Digital Marketing',
        'message' => 'Training "Digital Marketing Fundamentals" telah dibuka untuk pendaftaran. Kuota terbatas, segera daftar sebelum 15 Juni 2025!',
        'type' => 'info'
    ],
    [
        'title' => '✅ Pengajuan Training Disetujui',
        'message' => 'Selamat! Pengajuan training "Leadership Development" Anda telah disetujui oleh atasan. Silakan cek detail di dashboard.',
        'type' => 'success'
    ],
    [
        'title' => '⚠️ Deadline Assignment Mendekati',
        'message' => 'Anda memiliki assignment "Project Management Case Study" yang akan berakhir dalam 2 hari. Pastikan menyelesaikan tepat waktu.',
        'type' => 'warning'
    ],
    [
        'title' => '❌ Training Dibatalkan',
        'message' => 'Training "Advanced Excel" yang dijadwalkan hari ini dibatalkan karena trainer berhalangan. Akan ada penjadwalan ulang.',
        'type' => 'error'
    ],
    [
        'title' => '📅 Reminder: Training Besok',
        'message' => 'Jangan lupa! Training "Communication Skills" akan dimulai besok pukul 09:00 di ruang meeting lantai 2.',
        'type' => 'warning'
    ],
    [
        'title' => '🏆 Sertifikat Tersedia',
        'message' => 'Sertifikat untuk training "Data Analysis" yang telah Anda selesaikan sudah tersedia untuk diunduh.',
        'type' => 'success'
    ]
];

echo "📝 Membuat notifikasi demo...\n";

$created_count = 0;
foreach ($demo_users as $user_index => $user) {
    // Buat 2-3 notifikasi per user
    $notifications_per_user = 2 + ($user_index % 2); // 2 atau 3 notifikasi
    
    for ($i = 0; $i < $notifications_per_user; $i++) {
        $notification_index = ($user_index * 3 + $i) % count($demo_notifications);
        $notification = $demo_notifications[$notification_index];
        
        try {
            $created = createNotification(
                $user['id'],
                null, // General notification
                $notification['title'],
                $notification['message'],
                $notification['type']
            );
            
            if ($created) {
                echo "   ✅ '{$notification['title']}' → {$user['name']} ({$notification['type']})\n";
                $created_count++;
            } else {
                echo "   ❌ Gagal membuat notifikasi untuk {$user['name']}\n";
            }
        } catch (Exception $e) {
            echo "   ❌ Error untuk {$user['name']}: " . $e->getMessage() . "\n";
        }
    }
}

echo "\n📊 Hasil:\n";
echo "   ✅ Berhasil dibuat: $created_count notifikasi\n";
echo "   👥 User yang menerima: " . count($demo_users) . " user\n";

// Tampilkan statistik terbaru
$stats_query = "SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread,
                    COUNT(CASE WHEN type = 'info' THEN 1 END) as info_count,
                    COUNT(CASE WHEN type = 'success' THEN 1 END) as success_count,
                    COUNT(CASE WHEN type = 'warning' THEN 1 END) as warning_count,
                    COUNT(CASE WHEN type = 'error' THEN 1 END) as error_count,
                    COUNT(DISTINCT user_id) as users_with_notifications
                FROM training_notifications";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

echo "\n📈 Statistik sistem:\n";
echo "   📊 Total notifikasi: {$stats['total']}\n";
echo "   📬 Belum dibaca: {$stats['unread']}\n";
echo "   ℹ️  Info: {$stats['info_count']}\n";
echo "   ✅ Success: {$stats['success_count']}\n";
echo "   ⚠️  Warning: {$stats['warning_count']}\n";
echo "   ❌ Error: {$stats['error_count']}\n";
echo "   👥 User dengan notifikasi: {$stats['users_with_notifications']}\n";

echo "\n🎯 CARA MELIHAT NOTIFIKASI:\n";
echo "============================\n";
echo "1. 🌐 Buka browser dan login ke sistem training\n";
echo "2. 🔔 Lihat icon bell di header navigation (kanan atas)\n";
echo "3. 🔴 Jika ada badge merah dengan angka, berarti ada notifikasi baru\n";
echo "4. 👆 Klik icon bell untuk melihat dropdown notifikasi\n";
echo "5. 🎨 Perhatikan icon berbeda untuk setiap jenis:\n";
echo "   - ℹ️  Info (biru)\n";
echo "   - ✅ Success (hijau)\n";
echo "   - ⚠️  Warning (kuning)\n";
echo "   - ❌ Error (merah)\n";
echo "6. 📄 Klik 'Lihat Semua Notifikasi' untuk halaman lengkap\n";
echo "7. ✅ Klik notifikasi untuk mark as read\n";

echo "\n📱 URL untuk test:\n";
echo "   - Dept Head: http://localhost/training/dept_head/index.php\n";
echo "   - Pemohon: http://localhost/training/pemohon/index.php\n";
echo "   - LnD: http://localhost/training/LnD/index.php\n";
echo "   - Director: http://localhost/training/Dir/index.php\n";

echo "\n🧹 Untuk membersihkan notifikasi demo:\n";
echo "   php config/cleanup_demo_notifications.php\n";

echo "\n🚀 NOTIFIKASI DEMO DENGAN STYLING BARU SIAP DILIHAT!\n";

$conn->close();
?>
