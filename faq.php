<?php
session_start();
include 'config/config.php';
require_once 'includes/SystemManager.php';

// Initialize System Manager
$systemManager = new SystemManager($conn);
$faqManager = $systemManager->getFAQManager();

// Cek apakah user sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: view/login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// Handle FAQ submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_faq'])) {
    if (!$systemManager->validateCSRF()) {
        $faq_error = "Token keamanan tidak valid. Silakan coba lagi.";
    } else {
        $question = trim($_POST['faq_question'] ?? '');
        $category = trim($_POST['faq_category'] ?? '');

        if (empty($question) || empty($category)) {
            $faq_error = "Pertanyaan dan kategori harus diisi.";
        } else {
            $submission_id = $systemManager->handleFAQSubmission($user_id, $question, $category);

            if ($submission_id) {
                $faq_success = "Pertanyaan Anda berhasil dikirim. Tim kami akan meninjau dan memberikan jawaban segera.";
            } else {
                $faq_error = "Gagal mengirim pertanyaan. Silakan coba lagi.";
            }
        }
    }
}

// Cek dan buat tabel faq dan faq_votes jika belum ada
$check_faq = "SHOW TABLES LIKE 'faq'";
$check_votes = "SHOW TABLES LIKE 'faq_votes'";
$faq_exists = $conn->query($check_faq);
$votes_exists = $conn->query($check_votes);

// Buat tabel faq_votes jika belum ada
if ($votes_exists->num_rows == 0) {
    $create_votes_table = "CREATE TABLE faq_votes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        faq_id INT NOT NULL,
        user_id INT NOT NULL,
        vote_type ENUM('helpful', 'not_helpful') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (faq_id) REFERENCES faq(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_vote (faq_id, user_id)
    )";
    $conn->query($create_votes_table);
}

// Cek apakah tabel faq perlu dibuat

// Jika tabel faq belum ada, buat tabel dan isi dengan data awal
if ($faq_exists->num_rows == 0) {
    // Buat tabel FAQ
    $create_table = "CREATE TABLE IF NOT EXISTS faq (
        id INT AUTO_INCREMENT PRIMARY KEY,
        question TEXT NOT NULL,
        answer TEXT NOT NULL,
        category VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->query($create_table);

    // Isi dengan data awal
    $initial_data = [
        // Kategori: Umum
        [
            'question' => 'Apa itu Training Center?',
            'answer' => 'Training Center adalah platform digital yang digunakan untuk mengelola pengajuan, persetujuan, dan pelaksanaan pelatihan karyawan. Platform ini menggantikan form pengajuan training manual sebagai bentuk modernisasi sistem manajemen pelatihan.',
            'category' => 'Umum'
        ],
        [
            'question' => 'Siapa yang dapat mengakses Training Center?',
            'answer' => 'Training Center dapat diakses oleh semua karyawan yang memiliki akun. Setiap karyawan memiliki tingkat akses yang berbeda tergantung pada peran mereka dalam sistem.',
            'category' => 'Umum'
        ],
        [
            'question' => 'Bagaimana cara mendaftar di Training Center?',
            'answer' => 'Untuk mendaftar, Anda perlu menghubungi departemen HR atau admin sistem untuk membuat akun. Anda akan menerima email dengan instruksi untuk mengaktifkan akun dan membuat password.',
            'category' => 'Umum'
        ],

        // Kategori: Pengajuan Training
        [
            'question' => 'Bagaimana cara mengajukan permintaan training?',
            'answer' => 'Untuk mengajukan permintaan training, login ke akun Anda, klik menu "Pengajuan Training", isi formulir dengan informasi yang diperlukan seperti jenis training, tanggal, dan peserta, kemudian klik "Kirim Pengajuan".',
            'category' => 'Pengajuan Training'
        ],
        [
            'question' => 'Berapa lama waktu yang dibutuhkan untuk persetujuan training?',
            'answer' => 'Waktu persetujuan bervariasi tergantung pada ketersediaan approver. Biasanya, proses persetujuan membutuhkan waktu 3-5 hari kerja.',
            'category' => 'Pengajuan Training'
        ],
        [
            'question' => 'Siapa yang menyetujui permintaan training?',
            'answer' => 'Permintaan training akan disetujui oleh beberapa pihak secara berurutan: Kepala Departemen, Learning & Development (L&D), dan Manager HRGA.',
            'category' => 'Pengajuan Training'
        ],

        // Kategori: Akun & Keamanan
        [
            'question' => 'Bagaimana cara mengubah password?',
            'answer' => 'Untuk mengubah password, klik pada nama Anda di pojok kanan atas, pilih "Profil", kemudian klik tab "Keamanan" dan pilih opsi "Ubah Password".',
            'category' => 'Akun & Keamanan'
        ],
        [
            'question' => 'Apa yang harus dilakukan jika lupa password?',
            'answer' => 'Jika Anda lupa password, klik "Lupa Password" di halaman login. Sistem akan mengirimkan email dengan instruksi untuk mereset password Anda.',
            'category' => 'Akun & Keamanan'
        ],
        [
            'question' => 'Apa yang harus dilakukan jika akun terkunci?',
            'answer' => 'Jika akun Anda terkunci karena terlalu banyak percobaan login yang gagal, Anda dapat menunggu periode penguncian selesai (biasanya 15 menit) atau menghubungi admin. Anda juga dapat menggunakan parameter <code>debug_unlock=true&identifier=NIK_ANDA</code> pada URL untuk membuka kunci akun.',
            'category' => 'Akun & Keamanan'
        ],

        // Kategori: Teknis
        [
            'question' => 'Browser apa yang direkomendasikan untuk mengakses Training Center?',
            'answer' => 'Training Center berfungsi optimal di browser modern seperti Google Chrome, Mozilla Firefox, Microsoft Edge, dan Safari versi terbaru.',
            'category' => 'Teknis'
        ],
        [
            'question' => 'Apakah Training Center dapat diakses melalui perangkat mobile?',
            'answer' => 'Ya, Training Center dirancang dengan responsif dan dapat diakses melalui smartphone dan tablet. Namun, beberapa fitur mungkin lebih mudah digunakan pada desktop.',
            'category' => 'Teknis'
        ],
        [
            'question' => 'Bagaimana cara melaporkan masalah teknis?',
            'answer' => 'Untuk melaporkan masalah teknis, silakan kirim <NAME_EMAIL> dengan detail masalah yang Anda alami. Sertakan tangkapan layar jika memungkinkan.',
            'category' => 'Teknis'
        ]
    ];

    // Insert data awal
    $insert_query = "INSERT INTO faq (question, answer, category) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($insert_query);

    foreach ($initial_data as $data) {
        $stmt->bind_param("sss", $data['question'], $data['answer'], $data['category']);
        $stmt->execute();
    }
}

// Ambil data FAQ dengan DISTINCT untuk menghindari duplikasi, hanya yang tidak disembunyikan
try {
    // Cek apakah kolom is_hidden ada
    $check_column = "SHOW COLUMNS FROM faq LIKE 'is_hidden'";
    $column_result = $conn->query($check_column);

    if ($column_result && $column_result->num_rows > 0) {
        // Kolom is_hidden ada, gunakan filter
        $query = "SELECT DISTINCT id, question, answer, category, created_at, updated_at
                  FROM faq
                  WHERE COALESCE(is_hidden, 0) = 0
                  ORDER BY category, id";
    } else {
        // Kolom is_hidden tidak ada, tampilkan semua FAQ
        $query = "SELECT DISTINCT id, question, answer, category, created_at, updated_at
                  FROM faq
                  ORDER BY category, id";
    }

    $result = $conn->query($query);
} catch (Exception $e) {
    // Fallback query jika ada error
    error_log("FAQ query error: " . $e->getMessage());
    $query = "SELECT DISTINCT id, question, answer, category, created_at, updated_at
              FROM faq
              ORDER BY category, id";
    $result = $conn->query($query);
}

// Ambil data voting untuk setiap FAQ
$votes_query = "SELECT faq_id, COUNT(CASE WHEN vote_type = 'helpful' THEN 1 END) as helpful_votes,
                COUNT(CASE WHEN vote_type = 'not_helpful' THEN 1 END) as not_helpful_votes
                FROM faq_votes GROUP BY faq_id";
$votes_result = $conn->query($votes_query);
$votes = [];
while ($vote = $votes_result->fetch_assoc()) {
    $votes[$vote['faq_id']] = $vote;
}

// Kelompokkan FAQ berdasarkan kategori dan tambahkan data voting
$faqs_by_category = [];
$processed_ids = []; // Untuk melacak ID yang sudah diproses

while ($row = $result->fetch_assoc()) {
    // Periksa apakah ID ini sudah diproses sebelumnya
    if (in_array($row['id'], $processed_ids)) {
        continue; // Lewati jika sudah diproses
    }

    $processed_ids[] = $row['id']; // Tandai ID ini sebagai sudah diproses
    $row['votes'] = isset($votes[$row['id']]) ? $votes[$row['id']] : ['helpful_votes' => 0, 'not_helpful_votes' => 0];
    $faqs_by_category[$row['category']][] = $row;
}

// Urutkan FAQ berdasarkan jumlah helpful votes
foreach ($faqs_by_category as &$faqs) {
    usort($faqs, function($a, $b) {
        return ($b['votes']['helpful_votes'] - $b['votes']['not_helpful_votes']) -
               ($a['votes']['helpful_votes'] - $a['votes']['not_helpful_votes']);
    });
}
?>

<!DOCTYPE html>
<html lang="en">
<?php include 'config/head.php'; ?>
<style>
    .faq-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 40px 20px;
    }

    .faq-header {
        text-align: center;
        margin-bottom: 40px;
    }

    .faq-header h1 {
        color: #BF0000;
        font-size: 32px;
        margin-bottom: 10px;
    }

    .faq-header p {
        color: #666;
        font-size: 16px;
    }

    .faq-search {
        margin-bottom: 30px;
        position: relative;
    }

    .faq-search input {
        width: 100%;
        padding: 15px 20px 15px 50px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .faq-search i {
        position: absolute;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
    }

    .faq-categories {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 30px;
        justify-content: center;
    }

    .category-button {
        padding: 10px 20px;
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 30px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .category-button:hover {
        background-color: #e9e9e9;
    }

    .category-button.active {
        background-color: #BF0000;
        color: white;
        border-color: #BF0000;
    }

    .faq-category {
        margin-bottom: 40px;
    }

    .category-title {
        color: #BF0000;
        font-size: 24px;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f0f0f0;
    }

    .faq-item {
        margin-bottom: 20px;
        border: 1px solid #eee;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .faq-item:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .faq-question {
        padding: 20px;
        background-color: #f9f9f9;
        cursor: pointer;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .faq-question i {
        transition: transform 0.3s ease;
    }

    .faq-question.active i {
        transform: rotate(180deg);
    }

    .faq-answer {
        padding: 0 20px;
        max-height: 0;
        overflow: hidden;
        transition: all 0.3s ease;
        background-color: white;
    }

    .faq-answer.show {
        padding: 20px;
        max-height: 1000px;
    }

    .faq-answer p {
        margin: 0 0 15px 0;
        line-height: 1.6;
    }

    .faq-votes {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }

    .vote-btn {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 5px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .vote-btn:hover {
        background: #f5f5f5;
    }

    .vote-btn.helpful {
        color: #28a745;
    }

    .vote-btn.not-helpful {
        color: #dc3545;
    }

    .vote-btn.active {
        background: #e9ecef;
    }

    .highlight {
        background-color: #fff3cd;
        padding: 2px;
        border-radius: 2px;
    }

    .faq-answer code {
        background-color: #f0f0f0;
        padding: 2px 5px;
        border-radius: 4px;
        font-family: monospace;
    }

    .no-results {
        text-align: center;
        padding: 40px;
        color: #666;
    }

    .contact-support {
        text-align: center;
        margin-top: 50px;
        padding: 30px;
        color: white;
        background-color: #BF0000;
        border-radius: 8px;
    }

    .contact-support h3 {
        margin-bottom: 15px;
    }

    .contact-support p {
        margin-bottom: 20px;
    }

    .contact-button {
        display: inline-block;
        padding: 12px 25px;
        background-color: #BF0000;
        color: white;
        border-radius: 5px;
        text-decoration: none;
        transition: background-color 0.3s ease;
    }

    .contact-button:hover {
        background-color: #a00000;
    }

    /* Modal Styles */
    .modal {
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(3px);
    }

    .modal-content {
        background-color: white;
        margin: 5% auto;
        border-radius: 15px;
        width: 90%;
        max-width: 800px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-header {
        background: linear-gradient(135deg, #BF0000 0%, #a00000 100%);
        color: white;
        padding: 20px 30px;
        border-radius: 15px 15px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 1.5rem;
    }

    .close {
        color: white;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        transition: opacity 0.2s;
    }

    .close:hover {
        opacity: 0.7;
    }

    .modal-body {
        padding: 30px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
    }

    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s;
        font-family: inherit;
    }

    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #BF0000;
        box-shadow: 0 0 0 3px rgba(191, 0, 0, 0.1);
    }

    .form-group textarea {
        min-height: 120px;
        resize: vertical;
    }

    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 30px;
    }

    .btn-cancel,
    .btn-submit {
        padding: 12px 25px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-cancel {
        background: #6c757d;
        color: white;
    }

    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-1px);
    }

    .btn-submit {
        background: linear-gradient(135deg, #BF0000 0%, #a00000 100%);
        color: white;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(191, 0, 0, 0.3);
    }

    /* Alert Styles */
    .alert-overlay {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1100;
        animation: alertSlideIn 0.3s ease-out;
    }

    @keyframes alertSlideIn {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .alert-box {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        gap: 15px;
        min-width: 300px;
        max-width: 500px;
        border-left: 5px solid;
    }

    .alert-success {
        border-left-color: #28a745;
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
    }

    .alert-error {
        border-left-color: #dc3545;
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }

    .alert-close {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        margin-left: auto;
        opacity: 0.7;
        transition: opacity 0.2s;
    }

    .alert-close:hover {
        opacity: 1;
    }

    .contact-button {
        background: none;
        border: none;
        font-size: inherit;
        cursor: pointer;
    }

    @media (max-width: 768px) {
        .faq-header h1 {
            font-size: 28px;
        }

        .category-title {
            font-size: 20px;
        }

        .faq-question {
            padding: 15px;
            font-size: 15px;
        }

        .faq-answer {
            font-size: 14px;
        }

        .modal-content {
            width: 95%;
            margin: 10% auto;
        }

        .modal-body {
            padding: 20px;
        }

        .form-actions {
            flex-direction: column;
        }

        .alert-overlay {
            right: 10px;
            left: 10px;
        }

        .alert-box {
            min-width: auto;
        }
    }
</style>
<body>
    <?php include 'config/navbarb.php'; ?>

    <div class="faq-container">
        <div class="faq-header">
            <h1>Bantuan & FAQ</h1>
            <p>Temukan jawaban untuk pertanyaan yang sering diajukan tentang Training Center</p>
        </div>

        <div class="faq-search">
            <i class="fas fa-search"></i>
            <input type="text" id="searchFaq" placeholder="Cari pertanyaan...">
        </div>

        <div class="faq-categories">
            <button class="category-button active" data-category="all">Semua</button>
            <?php foreach (array_keys($faqs_by_category) as $category): ?>
                <button class="category-button" data-category="<?php echo htmlspecialchars($category); ?>"><?php echo htmlspecialchars($category); ?></button>
            <?php endforeach; ?>
        </div>

        <div id="faqContent">
            <?php foreach ($faqs_by_category as $category => $faqs): ?>
                <div class="faq-category" data-category="<?php echo htmlspecialchars($category); ?>">
                    <h2 class="category-title"><?php echo htmlspecialchars($category); ?></h2>

                    <?php foreach ($faqs as $faq): ?>
                        <div class="faq-item">
                            <div class="faq-question">
                                <?php echo htmlspecialchars($faq['question']); ?>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p><?php echo $faq['answer']; ?></p>
                                <div class="faq-votes">
                                    <button class="vote-btn helpful" data-faq="<?php echo $faq['id']; ?>" data-type="helpful">
                                        <i class="fas fa-thumbs-up"></i>
                                        <span class="vote-count"><?php echo $faq['votes']['helpful_votes']; ?></span>
                                    </button>
                                    <button class="vote-btn not-helpful" data-faq="<?php echo $faq['id']; ?>" data-type="not_helpful">
                                        <i class="fas fa-thumbs-down"></i>
                                        <span class="vote-count"><?php echo $faq['votes']['not_helpful_votes']; ?></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endforeach; ?>

            <div class="no-results" style="display: none;">
                <i class="fas fa-search" style="font-size: 48px; color: #ddd; margin-bottom: 20px;"></i>
                <h3>Tidak ada hasil yang ditemukan</h3>
                <p>Coba gunakan kata kunci yang berbeda atau hubungi dukungan kami.</p>
            </div>
        </div>

        <div class="contact-support">
            <h3>Masih memiliki pertanyaan?</h3>
            <p>Jika Anda tidak menemukan jawaban yang Anda cari, Anda dapat mengajukan pertanyaan baru atau menghubungi tim dukungan kami.</p>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <button class="contact-button" onclick="openFaqModal()">
                    <i class="fas fa-question-circle"></i> Ajukan Pertanyaan
                </button>
                <a href="mailto:<EMAIL>" class="contact-button" style="color: white;">
                    <i class="fas fa-envelope"></i> Hubungi Dukungan
                </a>
            </div>
        </div>
    </div>

    <!-- FAQ Submission Modal -->
    <div id="faqModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-question-circle"></i> Ajukan Pertanyaan FAQ</h3>
                <span class="close" onclick="closeFaqModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form method="POST" id="faqSubmissionForm">
                    <?= $systemManager->getCSRFInput() ?>
                    <input type="hidden" name="submit_faq" value="1">

                    <div class="form-group">
                        <label for="faq_category">Kategori *</label>
                        <select name="faq_category" id="faq_category" required>
                            <option value="">Pilih kategori</option>
                            <option value="Umum">Umum</option>
                            <option value="Pengajuan Training">Pengajuan Training</option>
                            <option value="Akun & Keamanan">Akun & Keamanan</option>
                            <option value="Teknis">Teknis</option>
                            <?php
                            $existing_categories = array_keys($faqs_by_category);
                            foreach ($existing_categories as $cat):
                                if (!in_array($cat, ['Umum', 'Pengajuan Training', 'Akun & Keamanan', 'Teknis'])):
                            ?>
                                <option value="<?= htmlspecialchars($cat) ?>"><?= htmlspecialchars($cat) ?></option>
                            <?php
                                endif;
                            endforeach;
                            ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="faq_question">Pertanyaan Anda *</label>
                        <textarea name="faq_question" id="faq_question" required
                                  placeholder="Jelaskan pertanyaan Anda dengan detail..."></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="button" onclick="closeFaqModal()" class="btn-cancel">
                            <i class="fas fa-times"></i> Batal
                        </button>
                        <button type="submit" class="btn-submit">
                            <i class="fas fa-paper-plane"></i> Kirim Pertanyaan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php include 'config/footer.php'; ?>

    <!-- Alert Messages -->
    <?php if (isset($faq_success)): ?>
        <div id="successAlert" class="alert-overlay">
            <div class="alert-box alert-success">
                <i class="fas fa-check-circle"></i>
                <span><?= htmlspecialchars($faq_success) ?></span>
                <button onclick="closeAlert('successAlert')" class="alert-close">&times;</button>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($faq_error)): ?>
        <div id="errorAlert" class="alert-overlay">
            <div class="alert-box alert-error">
                <i class="fas fa-exclamation-circle"></i>
                <span><?= htmlspecialchars($faq_error) ?></span>
                <button onclick="closeAlert('errorAlert')" class="alert-close">&times;</button>
            </div>
        </div>
    <?php endif; ?>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Fungsi untuk menangani voting
            function handleVote(button) {
                const faqId = button.dataset.faq;
                const voteType = button.dataset.type;
                const formData = new FormData();
                formData.append('faq_id', faqId);
                formData.append('vote_type', voteType);

                fetch('api/vote_faq.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update tampilan vote
                        const faqItem = button.closest('.faq-item');
                        const helpfulBtn = faqItem.querySelector('.vote-btn.helpful');
                        const notHelpfulBtn = faqItem.querySelector('.vote-btn.not-helpful');

                        helpfulBtn.querySelector('.vote-count').textContent = data.votes.helpful_votes;
                        notHelpfulBtn.querySelector('.vote-count').textContent = data.votes.not_helpful_votes;

                        // Toggle status aktif
                        if (data.message === 'Vote removed') {
                            button.classList.remove('active');
                        } else {
                            button.classList.add('active');
                            if (voteType === 'helpful') {
                                notHelpfulBtn.classList.remove('active');
                            } else {
                                helpfulBtn.classList.remove('active');
                            }
                        }
                    }
                })
                .catch(error => console.error('Error:', error));
            }

            // Tambahkan event listener untuk tombol voting
            document.querySelectorAll('.vote-btn').forEach(button => {
                button.addEventListener('click', function() {
                    handleVote(this);
                });
            });

            // Tambahkan animasi untuk transisi kategori
            const faqCategories = document.querySelectorAll('.faq-category');
            faqCategories.forEach(category => {
                category.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            });

            // Toggle FAQ answers
            const questions = document.querySelectorAll('.faq-question');
            questions.forEach(question => {
                question.addEventListener('click', function() {
                    this.classList.toggle('active');
                    const answer = this.nextElementSibling;
                    answer.classList.toggle('show');
                });
            });

            // Category filter
            const categoryButtons = document.querySelectorAll('.category-button');
            const faqCategoryContainers = document.querySelectorAll('.faq-category');

            categoryButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    categoryButtons.forEach(btn => btn.classList.remove('active'));
                    // Add active class to clicked button
                    this.classList.add('active');

                    const category = this.getAttribute('data-category');

                    // Fade out all categories first
                    faqCategoryContainers.forEach(cat => {
                        cat.style.opacity = '0';
                        cat.style.transform = 'translateY(20px)';
                    });

                    // Wait for fade out animation to complete
                    setTimeout(() => {
                        if (category === 'all') {
                            // Show all categories
                            faqCategoryContainers.forEach(cat => {
                                cat.style.display = 'block';
                            });

                            // Use a single setTimeout for fade in
                            setTimeout(() => {
                                faqCategoryContainers.forEach(cat => {
                                    cat.style.opacity = '1';
                                    cat.style.transform = 'translateY(0)';
                                });
                            }, 50);
                        } else {
                            // Show only selected category
                            faqCategoryContainers.forEach(cat => {
                                if (cat.getAttribute('data-category') === category) {
                                    cat.style.display = 'block';
                                } else {
                                    cat.style.display = 'none';
                                }
                            });

                            // Fade in only visible categories
                            setTimeout(() => {
                                faqCategoryContainers.forEach(cat => {
                                    if (cat.style.display === 'block') {
                                        cat.style.opacity = '1';
                                        cat.style.transform = 'translateY(0)';
                                    }
                                });
                            }, 50);
                        }
                    }, 300);

                    // Reset search
                    document.getElementById('searchFaq').value = '';
                    document.querySelectorAll('.faq-item').forEach(item => {
                        item.style.display = 'block';
                    });
                    document.querySelector('.no-results').style.display = 'none';
                });
            });

            // Search functionality with highlighting
            const searchInput = document.getElementById('searchFaq');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const faqItems = document.querySelectorAll('.faq-item');
                let resultsFound = false;

                faqItems.forEach(item => {
                    const questionEl = item.querySelector('.faq-question');
                    const answerEl = item.querySelector('.faq-answer p');
                    const question = questionEl.textContent;
                    const answer = answerEl.textContent;

                    if (question.toLowerCase().includes(searchTerm) || answer.toLowerCase().includes(searchTerm)) {
                        item.style.display = 'block';
                        resultsFound = true;

                        // Highlight matching text
                        if (searchTerm) {
                            const regex = new RegExp(`(${searchTerm})`, 'gi');
                            questionEl.innerHTML = question.replace(regex, '<span class="highlight">$1</span>');
                            answerEl.innerHTML = answer.replace(regex, '<span class="highlight">$1</span>');
                        } else {
                            questionEl.innerHTML = question;
                            answerEl.innerHTML = answer;
                        }
                    } else {
                        item.style.display = 'none';
                    }
                });

                // Show/hide no results message
                document.querySelector('.no-results').style.display = resultsFound ? 'none' : 'block';

                // Reset category filter
                if (searchTerm) {
                    categoryButtons.forEach(btn => btn.classList.remove('active'));
                    document.querySelector('[data-category="all"]').classList.add('active');
                    faqCategories.forEach(cat => cat.style.display = 'block');
                }
            });

            // Auto-close alerts after 5 seconds
            setTimeout(function() {
                const successAlert = document.getElementById('successAlert');
                const errorAlert = document.getElementById('errorAlert');

                if (successAlert) {
                    closeAlert('successAlert');
                }
                if (errorAlert) {
                    closeAlert('errorAlert');
                }
            }, 5000);
        });

        // FAQ Modal Functions
        function openFaqModal() {
            document.getElementById('faqModal').style.display = 'block';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }

        function closeFaqModal() {
            document.getElementById('faqModal').style.display = 'none';
            document.body.style.overflow = 'auto'; // Restore scrolling

            // Reset form
            document.getElementById('faqSubmissionForm').reset();
        }

        // Alert Functions
        function closeAlert(alertId) {
            const alert = document.getElementById(alertId);
            if (alert) {
                alert.style.animation = 'alertSlideOut 0.3s ease-in forwards';
                setTimeout(() => {
                    alert.remove();
                }, 300);
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('faqModal');
            if (event.target === modal) {
                closeFaqModal();
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeFaqModal();
            }
        });

        // Add slide out animation for alerts
        const style = document.createElement('style');
        style.textContent = `
            @keyframes alertSlideOut {
                from {
                    opacity: 1;
                    transform: translateX(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(100%);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
