

<footer class="footer">
    <!-- Elegant Pattern Overlay -->
    <div class="pattern-overlay"></div>

    <!-- Decorative Elements -->
    <div class="decorative-element left-element"></div>
    <div class="decorative-element right-element"></div>

    <div class="footer-content">
        <div class="footer-section about">
            <div class="section-header">
                <div class="header-decoration"></div>
                <h3>Tentang <PERSON></h3>
                <div class="header-decoration"></div>
            </div>
            <p>Knowledge Management System digunakan untuk pengelolaan training sebagai bentuk modernisasi sistem manajemen pelatihan.</p>
            <div class="social-icons">
                <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
            </div>
        </div>
        <div class="footer-section contact">
            <div class="section-header">
                <div class="header-decoration"></div>
                <h3>Kontak</h3>
                <div class="header-decoration"></div>
            </div>
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <a href="https://www.google.co.id/maps/place/PT+Prakarsa+Alam+Segar/@-6.1970193,106.9824478,17z/data=!3m1!4b1!4m6!3m5!1s0x2e698beb6efc70ab:0x65d9caf323122179!8m2!3d-6.1970246!4d106.9850227!16s%2Fg%2F11dyp299v0?entry=ttu&g_ep=EgoyMDI1MDMxOS4yIKXMDSoASAFQAw%3D%3D" target="_blank">Jl. Komp. PT. Bakr, RT.001/RW.024, Pejuang, Kecamatan Medan Satria, Kota Bks, Jawa Barat 17131</a>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-item">
                    <i class="fab fa-whatsapp"></i>
                    <a href="https://wa.me/62895602416781">+62895602416781</a>
                </div>
            </div>
        </div>
        <div class="footer-section help">
            <div class="section-header">
                <div class="header-decoration"></div>
                <h3>Bantuan & FAQ</h3>
                <div class="header-decoration"></div>
            </div>
            <div class="help-links">
                <div class="help-item">
                    <i class="fas fa-question-circle"></i>
                    <a href="<?php echo BASE_URL; ?>faq.php">Pertanyaan Umum (FAQ)</a>
                </div>
                <div class="help-item">
                    <i class="fas fa-book"></i>
                    <a href="<?php echo BASE_URL; ?>bantuan.php">Panduan Pengguna</a>
                </div>
                <div class="help-item">
                    <i class="fas fa-headset"></i>
                    <a href="mailto:<EMAIL>">Dukungan Teknis</a>
                </div>
            </div>
        </div>

    </div>

    <!-- Elegant Divider -->
    <div class="elegant-divider">
        <div class="divider-line"></div>
        <div class="divider-emblem"></div>
        <div class="divider-line"></div>
    </div>

    <div class="footer-bottom">
        <p>&copy; <?php echo date("Y"); ?> Knowledge Management System. All rights reserved.</p>
    </div>
</footer>

<style>
:root {
    --primary-color: #BF0000;
    --primary-color-dark: #900000;
    --primary-color-light: rgba(191, 0, 0, 0.1);
    --primary-color-lighter: rgba(191, 0, 0, 0.05);
    --text-light: #ffffff;
    --text-muted: rgba(255, 255, 255, 0.8);
    --border-light: rgba(255, 255, 255, 0.2);
    --accent-color: #FF3333;
    --accent-light: rgba(255, 51, 51, 0.3);
    --accent-lighter: rgba(255, 51, 51, 0.1);
    --dark-accent: #333333;
    --dark-accent-light: rgba(51, 51, 51, 0.3);
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --transition-fast: 0.2s ease;
}

.footer {
    background-color: var(--primary-color);
    background-image: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: var(--text-light);
    padding: var(--spacing-lg) 0;
    width: 100%;
    position: relative;
    bottom: 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    clear: both;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-around;
    padding: 0 var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.footer-section {
    flex: 1;
    min-width: 250px;
    max-width: 33%;
}

/* Pattern Overlay */
.pattern-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.1;
    pointer-events: none;
}

/* Decorative Elements */
.decorative-element {
    position: absolute;
    width: 200px;
    height: 200px;
    opacity: 0.1;
    pointer-events: none;
}

.left-element {
    top: -50px;
    left: -50px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200' viewBox='0 0 200 200'%3E%3Cpath fill='%23FFFFFF' d='M100 0C44.8 0 0 44.8 0 100s44.8 100 100 100 100-44.8 100-100S155.2 0 100 0zm0 180c-44.1 0-80-35.9-80-80s35.9-80 80-80 80 35.9 80 80-35.9 80-80 80z'/%3E%3Cpath fill='%23FFFFFF' d='M100 40c-33.1 0-60 26.9-60 60s26.9 60 60 60 60-26.9 60-60-26.9-60-60-60zm0 100c-22.1 0-40-17.9-40-40s17.9-40 40-40 40 17.9 40 40-17.9 40-40 40z'/%3E%3C/svg%3E");
    transform: rotate(-15deg);
}

.right-element {
    bottom: -50px;
    right: -50px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200' viewBox='0 0 200 200'%3E%3Cpath fill='%23FFFFFF' d='M0 0h40v40H0zm80 0h40v40H80zm80 0h40v40h-40zM40 40h40v40H40zm80 0h40v40h-40zM0 80h40v40H0zm80 0h40v40H80zm80 0h40v40h-40zM40 120h40v40H40zm80 0h40v40h-40zM0 160h40v40H0zm80 0h40v40H80zm80 0h40v40h-40z'/%3E%3C/svg%3E");
    transform: rotate(15deg);
}

/* Section Headers */
.section-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.header-decoration {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    flex-grow: 1;
}

.footer-section h3 {
    position: relative;
    padding: 0 var(--spacing-md);
    font-size: var(--font-size-md);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0;
    color: var(--text-light);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Elegant Divider */
.elegant-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: var(--spacing-lg) 0;
    padding: 0 var(--spacing-lg);
}

.divider-line {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-light), transparent);
    flex-grow: 1;
}

.divider-emblem {
    width: 40px;
    height: 40px;
    margin: 0 var(--spacing-md);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Cpath fill='%23FF3333' d='M20 0L24.5 13.8L38.8 13.8L27.1 22.4L31.6 36.2L20 27.6L8.4 36.2L12.9 22.4L1.2 13.8L15.5 13.8Z'/%3E%3C/svg%3E");
    opacity: 0.6;
}

.footer-section p {
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.social-icons {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--text-light);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, var(--accent-lighter) 0%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.social-icon:hover {
    background-color: var(--text-light);
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.social-icon:hover::before {
    opacity: 1;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    position: relative;
    z-index: 1;
}

.contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-sm);
    opacity: 0;
    transition: opacity var(--transition-fast);
    z-index: -1;
}

.contact-item:hover::before {
    opacity: 1;
}

.contact-item i {
    margin-top: 4px;
    font-size: var(--font-size-sm);
    color: var(--text-light);
    opacity: 0.8;
    transition: all var(--transition-fast);
}

.contact-item:hover i {
    opacity: 1;
    transform: scale(1.1);
}

.contact-item a {
    color: var(--text-muted);
    text-decoration: none;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    position: relative;
    padding-bottom: 2px;
}

.contact-item a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--accent-color), transparent);
    transition: width var(--transition-fast);
}

.contact-item a:hover {
    color: var(--text-light);
}

.contact-item a:hover::after {
    width: 100%;
}

/* Bantuan & FAQ Section */
.footer-section.help .help-links {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.help-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    position: relative;
    z-index: 1;
}

.help-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-sm);
    opacity: 0;
    transition: opacity var(--transition-fast);
    z-index: -1;
}

.help-item:hover::before {
    opacity: 1;
}

.help-item i {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    opacity: 0.8;
    transition: all var(--transition-fast);
}

.help-item:hover i {
    opacity: 1;
    transform: scale(1.1);
}

.help-item a {
    color: var(--text-muted);
    text-decoration: none;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    position: relative;
    padding-bottom: 2px;
}

.help-item a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--accent-color), transparent);
    transition: width var(--transition-fast);
}

.help-item a:hover {
    color: var(--text-light);
}

.help-item a:hover::after {
    width: 100%;
}

.footer-bottom {
    text-align: center;
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md) 0;
    position: relative;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    background-color: rgba(0, 0, 0, 0.1);
}

.footer-bottom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    opacity: 0.5;
}

.footer-bottom p {
    margin: 0;
    position: relative;
    display: inline-block;
}

.footer-bottom p::before,
.footer-bottom p::after {
    content: '•';
    color: var(--accent-color);
    margin: 0 var(--spacing-sm);
    opacity: 0.7;
}

/* Responsive Design */
@media screen and (max-width: 992px) {
    .footer-content {
        gap: var(--spacing-md);
    }

    .footer-section {
        min-width: 180px;
    }

    .decorative-element {
        width: 150px;
        height: 150px;
        opacity: 0.08;
    }
}

@media screen and (max-width: 768px) {
    .footer {
        padding: var(--spacing-md) 0;
    }

    .footer-content {
        flex-direction: column;
        padding: 0 var(--spacing-md);
        gap: var(--spacing-lg);
    }

    .footer-section {
        margin-right: 0;
        width: 100%;
    }

    .section-header {
        justify-content: center;
    }

    .header-decoration {
        max-width: 80px;
    }

    .footer-section h3 {
        font-size: 16px;
        text-align: center;
    }

    .footer-section p {
        font-size: 14px;
        text-align: center;
    }

    .contact-item a {
        font-size: 14px;
    }

    .social-icons {
        justify-content: center;
    }

    .divider-emblem {
        width: 30px;
        height: 30px;
    }

    .decorative-element {
        width: 100px;
        height: 100px;
        opacity: 0.05;
    }

    .left-element {
        top: -20px;
        left: -20px;
    }

    .right-element {
        bottom: -20px;
        right: -20px;
    }
}

@media screen and (max-width: 480px) {
    .footer {
        padding: var(--spacing-sm) 0;
    }

    .footer-content {
        padding: 0 var(--spacing-sm);
        gap: var(--spacing-md);
    }

    .footer-section h3 {
        font-size: 15px;
    }

    .footer-section p {
        font-size: 13px;
    }

    .contact-item {
        align-items: center;
    }

    .contact-item a {
        font-size: 13px;
    }

    .social-icon {
        width: 32px;
        height: 32px;
    }

    .footer-section.links li {
        margin-right: var(--spacing-sm);
    }

    .footer-bottom {
        margin-top: var(--spacing-md);
        padding: var(--spacing-sm) 0;
        font-size: 11px;
    }

    .footer-bottom p::before,
    .footer-bottom p::after {
        margin: 0 var(--spacing-xs);
    }

    .decorative-element {
        opacity: 0.03;
    }

    .pattern-overlay {
        opacity: 0.05;
    }

    .header-decoration {
        max-width: 50px;
    }
}
</style>

<?php
// Tampilkan informasi debugging jika fungsi tersedia
if (function_exists('debugUrl')) {
    debugUrl();
}
?>
