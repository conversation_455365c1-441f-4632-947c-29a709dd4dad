# Deny access to files with specific extensions
<FilesMatch "\.(bat|cmd|sh|exe|php|pl|py|cgi|asp|aspx|jsp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Allow access to common document and image file types
<FilesMatch "\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|jpg|jpeg|png|gif)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# Prevent script execution
Options -ExecCGI
AddHandler cgi-script .bat .cmd .sh .exe .php .pl .py .cgi .asp .aspx .jsp

# Disable PHP execution in this directory
<IfModule mod_php5.c>
    php_flag engine off
</IfModule>
<IfModule mod_php7.c>
    php_flag engine off
</IfModule>
<IfModule mod_php.c>
    php_flag engine off
</IfModule>

# Set content disposition to force download for potentially dangerous file types
<FilesMatch "\.(bat|cmd|sh|exe)$">
    Header set Content-Disposition "attachment"
</FilesMatch>

# Prevent directory listing
Options -Indexes
