<?php
session_start();
include '../config/config.php';

// Cek apakah pengguna sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Cek apakah pengguna memiliki role_id = 99 (Admin)
if ($_SESSION['role_id'] != 99) {
    header('Location: ../view/login.php');
    exit();
}

if (isset($_GET['id'])) {
    $id = $_GET['id'];

    // Ambil data pengguna berdasarkan ID
    $query = "SELECT * FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
    } else {
        echo "User not found.";
        exit();
    }

    // Departemen akan diambil dari script.js

    // Get user's current departments
    $user_departments = [];
    $dept_query = "SELECT dept FROM user_departments WHERE user_id = ?";
    $dept_stmt = $conn->prepare($dept_query);
    $dept_stmt->bind_param("i", $id);
    $dept_stmt->execute();
    $dept_result = $dept_stmt->get_result();
    while ($row = $dept_result->fetch_assoc()) {
        $user_departments[] = $row['dept'];
    }

    // Proses form submit
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $name = $_POST['name'];
        $email = $_POST['email'];
        $dept = $_POST['dept'];
        $bagian = $_POST['bagian'];
        $jabatan = $_POST['jabatan'];
        $nik = $_POST['nik'];
        $role_id = $_POST['role_id'];
        $password = $_POST['password'];

        // Cek apakah password diubah
        if (empty($password)) {
            // Jika password kosong, gunakan password lama
            $query = "UPDATE users SET name = ?, email = ?, nik = ?, role_id = ?, dept = ?, bagian = ?, jabatan = ? WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("sssssssi", $name, $email, $nik, $role_id, $dept, $bagian, $jabatan, $id);
        } else {
            // Jika password diisi, update dengan password baru
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $query = "UPDATE users SET name = ?, email = ?, nik = ?, role_id = ?, dept = ?, bagian = ?, jabatan = ?, password = ? WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("ssssssssi", $name, $email, $nik, $role_id, $dept, $bagian, $jabatan, $hashed_password, $id);
        }

        if ($stmt->execute()) {
            // Setelah update data user utama
            if (isset($_POST['departments'])) {
                // Hapus data departments yang lama
                $delete_query = "DELETE FROM user_departments WHERE user_id = ?";
                $delete_stmt = $conn->prepare($delete_query);
                $delete_stmt->bind_param("i", $id);
                $delete_stmt->execute();

                // Insert data departments yang baru
                $insert_query = "INSERT INTO user_departments (user_id, dept) VALUES (?, ?)";
                $insert_stmt = $conn->prepare($insert_query);

                foreach ($_POST['departments'] as $department) {
                    $insert_stmt->bind_param("is", $id, $department);
                    $insert_stmt->execute();
                }
            }

            // Log aktivitas
            if (file_exists('../config/activity_logger.php')) {
                include_once '../config/activity_logger.php';
                if (function_exists('log_activity')) {
                    $role_names = [
                        1 => 'Pemohon',
                        2 => 'Dept Head',
                        3 => 'LnD',
                        4 => 'FM',
                        5 => 'Direktur',
                        6 => 'Kosong',
                        99 => 'Admin'
                    ];
                    $role_name = $role_names[$role_id] ?? 'Unknown';

                    $action = empty($password) ?
                        "Mengupdate pengguna: {$name} (NIK: {$nik}) dengan role {$role_name}" :
                        "Mengupdate pengguna dan password: {$name} (NIK: {$nik}) dengan role {$role_name}";

                    log_activity($_SESSION['user_id'], $action, "user", [
                        'user_id' => $id,
                        'name' => $name,
                        'nik' => $nik,
                        'role_id' => $role_id,
                        'dept' => $dept,
                        'bagian' => $bagian,
                        'jabatan' => $jabatan,
                        'password_changed' => !empty($password)
                    ]);
                }
            }

            // Redirect ke halaman utama setelah sukses
            header('Location: manage_user.php');
            exit();
        } else {
            // Jika gagal update
            $error = "Gagal mengupdate pengguna: " . $conn->error;
        }
    }
}

// Query untuk mengambil daftar role
$query = "SELECT * FROM roles";
$result = $conn->query($query);

// Nama departemen akan diambil dari script.js

// Tambahkan di bagian awal file setelah query user
$current_password = ''; // Inisialisasi variable
$show_password = isset($_GET['password']); // Check jika parameter password ada
$password_info = null;

if ($user) {
    // Ambil password dari database (yang sudah di-hash)
    $current_password = $user['password'];

    // Jika parameter password ada, analisis password
    if ($show_password) {
        $password_info = analyzePassword($current_password, $user);
    }
}

// Fungsi untuk menganalisis password
function analyzePassword($hashed_password, $user_data = null) {
    $info = [
        'hash' => $hashed_password,
        'is_default' => false,
        'readable_password' => null,
        'security_level' => 'unknown',
        'password_found' => false,
        'passwords_tried' => 0
    ];

    // Daftar password yang akan dicoba (dari yang paling umum)
    $passwords_to_try = [
        // Password default sistem
        'asdf',

        // Password sangat umum
        'password',
        '123456',
        '12345678',
        'qwerty',
        'abc123',
        'password123',
        'admin',
        'test',
        'user',
        'guest',

        // Angka umum
        '111111',
        '000000',
        '123123',
        '654321',
        '987654321',
        '1234567890',

        // Pattern umum
        '123',
        '1234',
        '12345',
        '123456789',
        'admin123',
        'test123',
        'user123',

        // Keyboard patterns
        'qwertyuiop',
        'asdfghjkl',
        'zxcvbnm',
        '1qaz2wsx',
        'qwerty123',

        // Password Indonesia umum
        'indonesia',
        'jakarta',
        'bandung',
        'surabaya',
        'yogyakarta',
        'medan',
        'semarang',
        'palembang',

        // Nama umum + angka
        'admin1',
        'admin12',
        'admin2023',
        'admin2024',
        'test1',
        'test12',
        'user1',
        'user12',

        // Password perusahaan umum
        'company',
        'office',
        'training',
        'sistem',
        'aplikasi',
        'database',

        // Tanggal umum
        '01012023',
        '01012024',
        '12345678',
        '87654321',

        // Password lemah lainnya
        'welcome',
        'login',
        'master',
        'super',
        'root',
        'administrator',

        // Kombinasi huruf-angka sederhana
        'a1b2c3',
        'abc1234',
        '1a2b3c',
        'test1234',
        'pass1234',

        // Password berdasarkan NIK atau nama (akan ditambah dinamis)
    ];

    // Tambahkan password berdasarkan data user jika tersedia
    if ($user_data) {
        $dynamic_passwords = [];

        // Password berdasarkan NIK
        if (!empty($user_data['nik'])) {
            $nik = $user_data['nik'];
            $dynamic_passwords[] = $nik;
            $dynamic_passwords[] = $nik . '123';
            $dynamic_passwords[] = $nik . '12';
            $dynamic_passwords[] = $nik . '1';
            $dynamic_passwords[] = '123' . $nik;

            // NIK terbalik
            $dynamic_passwords[] = strrev($nik);
        }

        // Password berdasarkan nama
        if (!empty($user_data['name'])) {
            $name_parts = explode(' ', strtolower($user_data['name']));
            foreach ($name_parts as $part) {
                if (strlen($part) >= 3) {
                    $dynamic_passwords[] = $part;
                    $dynamic_passwords[] = $part . '123';
                    $dynamic_passwords[] = $part . '12';
                    $dynamic_passwords[] = $part . '1';
                    $dynamic_passwords[] = ucfirst($part);
                    $dynamic_passwords[] = ucfirst($part) . '123';
                }
            }

            // Nama lengkap tanpa spasi
            $full_name = str_replace(' ', '', strtolower($user_data['name']));
            if (strlen($full_name) >= 3) {
                $dynamic_passwords[] = $full_name;
                $dynamic_passwords[] = $full_name . '123';
            }
        }

        // Password berdasarkan email
        if (!empty($user_data['email'])) {
            $email_part = explode('@', $user_data['email'])[0];
            $dynamic_passwords[] = $email_part;
            $dynamic_passwords[] = $email_part . '123';
        }

        // Gabungkan dengan password umum
        $passwords_to_try = array_merge($passwords_to_try, $dynamic_passwords);
    }

    // Coba setiap password
    $info['passwords_tried'] = count($passwords_to_try);
    foreach ($passwords_to_try as $pwd) {
        $info['passwords_tried']++; // Tambahkan jumlah password yang dicoba

        if (password_verify($pwd, $hashed_password)) {
            $info['readable_password'] = $pwd;
            $info['password_found'] = true;

            // Tentukan level keamanan
            if ($pwd === 'asdf') {
                $info['is_default'] = true;
                $info['security_level'] = 'very_low';
            } else {
                $info['security_level'] = 'low';
            }
            break;
        }
    }

    // Jika tidak ditemukan, tandai sebagai secure
    if (!$info['password_found']) {
        $info['security_level'] = 'secure';
    }

    return $info;
}

?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>

.welcome-container {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #BF0000, #800000);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.welcome-container h1 {
    margin: 0;
    font-size: 2.5em;
    font-weight: 700;
}

.welcome-container p {
    margin: 10px 0 0;
    font-size: 1.2em;
    opacity: 0.9;
}
.container-form {
    padding: 15px;
    max-width: 1200px;
    margin: 0 auto;
}

.form-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #333;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group select {
    width: 100%;
    padding: 12px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px; /* Lebih besar untuk layar sentuh */
}

.form-group input:focus,
.form-group select:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 5px rgba(76,175,80,0.2);
}

.checkbox-group {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.checkbox-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    padding: 8px 5px;
}

.checkbox-item input[type="checkbox"] {
    margin-right: 8px;
    width: 18px;
    height: 18px;
}

.checkbox-item label {
    font-weight: normal;
    cursor: pointer;
}

.button-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.button-group button,
.button-group .btn {
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s;
    font-size: 16px;
    min-width: 120px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
}

.btn-submit {
    background-color: #4CAF50;
    color: white;
}

.btn-back {
    background-color: #666;
    color: white;
}

.btn-submit:hover {
    background-color: #45a049;
}

.btn-back:hover {
    background-color: #555;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.password-group {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #666;
    font-size: 18px;
    padding: 8px;
}

.info-text {
    font-size: 0.9em;
    color: #666;
    margin-top: 5px;
}

.required::after {
    content: " *";
    color: red;
}

.selected-info {
    display: block;
    font-size: 0.9em;
    color: #666;
    margin-top: 5px;
    font-style: italic;
}

.password-group input {
    padding-right: 40px; /* Ruang untuk icon toggle password */
}

.form-group select {
    margin-bottom: 5px;
    height: 45px; /* Tinggi yang lebih baik untuk mobile */
}

/* Media Queries untuk Responsif */
@media screen and (max-width: 768px) {
    .container-form {
        padding: 10px;
    }

    .form-container {
        padding: 15px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .checkbox-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .button-group button,
    .button-group .btn {
        flex: 1 0 100%;
        margin-bottom: 10px;
    }
}

@media screen and (max-width: 480px) {
    .checkbox-container {
        grid-template-columns: 1fr;
    }

    .section-title {
        font-size: 1.3rem;
    }

    .form-group label {
        font-size: 0.95rem;
    }

    .button-group {
        flex-direction: column;
    }
}

/* Password Info Styles */
.password-info-section {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.password-info-section.default {
    background: #f8d7da;
    border-color: #f5c6cb;
}

.password-info-section.weak {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.password-info-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #856404;
}

.password-info-section.default .password-info-title {
    color: #721c24;
}

.password-readable {
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    border-left: 4px solid #dc3545;
    margin: 10px 0;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: bold;
}

.password-hash {
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    word-break: break-all;
    border: 1px solid #dee2e6;
    max-height: 60px;
    overflow-y: auto;
}

.copy-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    margin-left: 8px;
}

.copy-btn:hover {
    background: #218838;
}

.developer-note {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
    margin-top: 10px;
}
</style>


<body>
<?php include '../config/navbarb.php'; ?>
<div class="jarak"></div>
<div class="container-form">
    <div class="form-container">
        <h2 class="welcome-container">Edit User</h2>

        <?php if ($show_password && $password_info): ?>
        <div class="password-info-section <?php echo $password_info['is_default'] ? 'default' : ($password_info['password_found'] ? 'weak' : ''); ?>">
            <div class="password-info-title">
                🔐 Password Information
                <?php if ($password_info['is_default']): ?>
                    <span style="color: #dc3545;">⚠️ DEFAULT PASSWORD</span>
                <?php elseif ($password_info['password_found']): ?>
                    <span style="color: #fd7e14;">⚠️ WEAK PASSWORD</span>
                <?php else: ?>
                    <span style="color: #28a745;">✅ SECURE PASSWORD</span>
                <?php endif; ?>
            </div>

            <?php if ($password_info['password_found']): ?>
                <div style="margin-bottom: 15px;">
                    <strong>🔓 Login Password:</strong>
                    <div class="password-readable">
                        <?php echo htmlspecialchars($password_info['readable_password']); ?>
                        <button type="button" class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars($password_info['readable_password']); ?>')">
                            Copy Password
                        </button>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        💡 Gunakan password ini untuk login sebagai user: <strong><?php echo htmlspecialchars($user['name']); ?></strong>
                    </div>
                </div>

                <?php if ($password_info['is_default']): ?>
                    <div style="background: #fff; padding: 10px; border-radius: 4px; border-left: 4px solid #dc3545; margin-bottom: 10px;">
                        <strong style="color: #dc3545;">⚠️ PERINGATAN:</strong>
                        User ini menggunakan password default sistem. Sangat disarankan untuk mengubah password!
                    </div>
                <?php endif; ?>

            <?php else: ?>
                <div style="background: #d4edda; padding: 15px; border-radius: 4px; border-left: 4px solid #28a745; margin-bottom: 10px;">
                    <strong style="color: #28a745;">✅ Password Aman</strong><br>
                    <span style="color: #155724;">Password ini tidak terdeteksi sebagai password umum. Kemungkinan user sudah menggunakan password yang aman.</span>
                </div>
                <div style="font-size: 12px; color: #666; margin-bottom: 15px;">
                    💡 Password tidak dapat ditampilkan karena menggunakan enkripsi yang aman<br>
                    🔍 Telah mencoba <?php echo $password_info['passwords_tried']; ?> kemungkinan password (termasuk berdasarkan NIK, nama, dan email user)
                </div>

                <!-- Reset Password Options -->
                <div style="background: #fff3cd; padding: 15px; border-radius: 4px; border-left: 4px solid #ffc107; margin-bottom: 10px;">
                    <strong style="color: #856404;">🔧 Opsi untuk Login sebagai User Ini:</strong><br>
                    <div style="margin-top: 10px;">
                        <button type="button" onclick="resetPasswordToKnown('<?php echo $user['id']; ?>', 'asdf')"
                                style="background: #ffc107; color: #212529; border: none; padding: 8px 12px; border-radius: 4px; margin-right: 8px; cursor: pointer;">
                            Reset ke "asdf"
                        </button>
                        <button type="button" onclick="resetPasswordToKnown('<?php echo $user['id']; ?>', '<?php echo $user['nik']; ?>')"
                                style="background: #17a2b8; color: white; border: none; padding: 8px 12px; border-radius: 4px; margin-right: 8px; cursor: pointer;">
                            Reset ke NIK (<?php echo $user['nik']; ?>)
                        </button>
                        <button type="button" onclick="resetPasswordToCustom('<?php echo $user['id']; ?>')"
                                style="background: #6c757d; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer;">
                            Reset ke Password Custom
                        </button>
                    </div>
                    <div style="font-size: 11px; color: #856404; margin-top: 8px;">
                        ⚠️ Setelah reset, Anda bisa login menggunakan password baru tersebut
                    </div>
                </div>
            <?php endif; ?>

            <details style="margin-top: 15px;">
                <summary style="cursor: pointer; font-weight: bold; color: #6c757d;">🔍 Show Technical Details</summary>
                <div style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <div style="margin-bottom: 8px;">
                        <strong>Security Level:</strong>
                        <span style="color: <?php echo $password_info['security_level'] === 'secure' ? '#28a745' : '#dc3545'; ?>">
                            <?php echo strtoupper($password_info['security_level']); ?>
                        </span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <strong>Hash Algorithm:</strong> bcrypt (PHP PASSWORD_DEFAULT)
                    </div>
                    <div>
                        <strong>Password Hash:</strong>
                        <div class="password-hash" style="margin-top: 5px;">
                            <?php echo htmlspecialchars($password_info['hash']); ?>
                            <button type="button" class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars($password_info['hash']); ?>')">
                                Copy Hash
                            </button>
                        </div>
                    </div>
                </div>
            </details>

            <div class="developer-note">
                💡 Tip: Tambahkan &password di URL untuk melihat informasi password
            </div>
        </div>
        <?php endif; ?>

        <form method="POST" action="edit_user.php?id=<?php echo $user['id']; ?>">
            <div class="form-row">
                <div class="form-group">
                    <label for="nik" class="required">NIK</label>
                    <input type="text" name="nik" value="<?php echo htmlspecialchars($user['nik']); ?>" required>
                </div>
                <div class="form-group">
                    <label for="name" class="required">Nama Lengkap</label>
                    <input type="text" name="name" value="<?php echo htmlspecialchars($user['name']); ?>" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="email" class="required">Email</label>
                    <input type="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="password-group">
                        <input type="password"
                               name="password"
                               id="password"
                               value=""
                               placeholder="Masukkan password baru jika ingin mengubah">
                        <span class="toggle-password" onclick="togglePassword('password')">
                            <i class="far fa-eye"></i>
                        </span>
                    </div>
                    <span class="info-text">Biarkan kosong jika tidak ingin mengubah password</span>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="dept" class="required">Departemen Utama</label>
                    <select id="dept" name="dept" required onchange="updateBagianFromScript()">
                        <option value="">Pilih Departemen</option>
                        <option value="selectedDept" selected><?php echo $user['dept']; ?></option>
                        <!-- Opsi departemen akan diisi oleh JavaScript -->
                    </select>
                    <span class="selected-info">
                        Departemen saat ini: <span id="currentDeptName"><?php echo $user['dept']; ?></span>
                    </span>
                </div>
                <div class="form-group">
                    <label for="bagian" class="required">Sub Departemen</label>
                    <select id="bagian" name="bagian" required onchange="updateJabatanFromScript()">
                        <option value="">Pilih Sub Dept</option>
                        <option value="selectedBagian" selected><?php echo $user['bagian']; ?></option>
                        <!-- Opsi bagian akan diisi oleh JavaScript -->
                    </select>
                    <span class="selected-info">
                        Sub Dept saat ini: <span id="currentBagian"><?php echo $user['bagian']; ?></span>
                    </span>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="jabatan" class="required">Jabatan</label>
                    <select id="jabatan" name="jabatan" required>
                        <option value="">Pilih Jabatan</option>
                        <option value="selectedJabatan" selected><?php echo $user['jabatan']; ?></option>
                        <!-- Opsi jabatan akan diisi oleh JavaScript -->
                    </select>
                    <span class="selected-info">
                        Jabatan saat ini: <span id="currentJabatan"><?php echo $user['jabatan']; ?></span>
                    </span>
                </div>
                <div class="form-group">
                    <label for="role_id" class="required">Role</label>
                    <select name="role_id" required>
                        <?php while ($row = $result->fetch_assoc()) { ?>
                            <option value="<?php echo $row['id']; ?>"
                                    <?php echo ($row['id'] == $user['role_id']) ? 'selected' : ''; ?>>
                                <?php echo $row['role_name']; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label>Akses Multiple Departemen</label>
                <div class="checkbox-group">
                    <div class="checkbox-container" id="departmentsCheckboxContainer">
                        <!-- Checkbox departemen akan diisi oleh JavaScript -->
                    </div>
                </div>
                <span class="info-text">Pilih departemen tambahan jika user memiliki akses ke multiple departemen</span>
            </div>

            <div class="button-group">
                <button type="submit" style="color: white;" class="btn btn-submit">Update User</button>
                <a href="manage_user.php" style="color: white;" class="btn btn-back">Kembali</a>
            </div>
        </form>
    </div>
</div>

<script>
    function togglePassword(inputId) {
        const input = document.getElementById(inputId);
        const toggleBtn = input.nextElementSibling;

        if (input.type === "password") {
            input.type = "text";
            toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            input.type = "password";
            toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
        }
    }

    // Function untuk copy text ke clipboard
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(function() {
                showCopyFeedback('Copied to clipboard!');
            }, function(err) {
                fallbackCopyTextToClipboard(text);
            });
        } else {
            fallbackCopyTextToClipboard(text);
        }
    }

    function fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showCopyFeedback('Copied to clipboard!');
            } else {
                showCopyFeedback('Failed to copy');
            }
        } catch (err) {
            showCopyFeedback('Failed to copy');
        }

        document.body.removeChild(textArea);
    }

    function showCopyFeedback(message) {
        // Create temporary notification
        const notification = document.createElement('div');
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            z-index: 9999;
            font-size: 14px;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            document.body.removeChild(notification);
        }, 2000);
    }

    // Functions untuk reset password
    function resetPasswordToKnown(userId, newPassword) {
        // Gunakan confirmAction jika tersedia (dari custom-modal.js)
        if (typeof window.confirmAction === 'function') {
            window.confirmAction(
                `Reset password user ini ke "${newPassword}"?<br><br>Setelah reset, Anda bisa login menggunakan password tersebut.`,
                function() {
                    // Callback untuk "Ya"
                    executePasswordReset(userId, newPassword);
                },
                function() {
                    // Callback untuk "Tidak" - tidak perlu melakukan apa-apa
                    console.log('Reset password dibatalkan');
                },
                'Konfirmasi Reset Password'
            );
        } else if (typeof window.CustomModal !== 'undefined') {
            // Fallback ke CustomModal.confirm
            window.CustomModal.confirm(
                `Reset password user ini ke "${newPassword}"?<br><br>Setelah reset, Anda bisa login menggunakan password tersebut.`,
                'Konfirmasi Reset Password',
                {
                    confirmText: 'Ya',
                    cancelText: 'Tidak',
                    onConfirm: function() {
                        executePasswordReset(userId, newPassword);
                    },
                    onCancel: function() {
                        console.log('Reset password dibatalkan');
                    }
                }
            );
        } else {
            // Fallback ke confirm() standar
            if (confirm(`Reset password user ini ke "${newPassword}"?\n\nSetelah reset, Anda bisa login menggunakan password tersebut.`)) {
                executePasswordReset(userId, newPassword);
            }
        }
    }

    function executePasswordReset(userId, newPassword) {
        fetch('reset_user_password.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                new_password: newPassword
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showCopyFeedback(`✅ Password berhasil direset ke "${newPassword}"`);
                // Refresh halaman setelah 2 detik
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                showCopyFeedback(`❌ Gagal reset password: ${data.message}`);
            }
        })
        .catch(error => {
            showCopyFeedback('❌ Error: ' + error.message);
        });
    }

    function resetPasswordToCustom(userId) {
        const customPassword = prompt('Masukkan password baru:');
        if (!customPassword) {
            return;
        }

        if (customPassword.length < 4) {
            alert('Password minimal 4 karakter');
            return;
        }

        resetPasswordToKnown(userId, customPassword);
    }

    // Variabel untuk menyimpan data user saat ini
    const currentUserData = {
        dept: "<?php echo $user['dept']; ?>",
        bagian: "<?php echo $user['bagian']; ?>",
        jabatan: "<?php echo $user['jabatan']; ?>",
        departments: <?php echo json_encode($user_departments); ?>
    };

    // Fungsi untuk mengisi dropdown departemen dari database karyawan
    async function populateDepartmentOptions() {
        try {
            const response = await fetch('get_employee_data.php?action=departments');
            const data = await response.json();

            if (data.success) {
                const deptSelect = document.getElementById('dept');
                deptSelect.innerHTML = '<option value="">Pilih Departemen</option>';

                data.data.forEach(dept => {
                    const option = document.createElement('option');
                    option.value = dept;
                    option.text = dept;
                    if (dept === currentUserData.dept) {
                        option.selected = true;
                    }
                    deptSelect.appendChild(option);
                });
            } else {
                console.error('Error loading departments:', data.message);
            }
        } catch (error) {
            console.error('Error fetching departments:', error);
        }
    }

    // Fungsi untuk mengisi checkbox departemen dari database karyawan
    async function populateDepartmentCheckboxes() {
        try {
            const response = await fetch('get_employee_data.php?action=departments');
            const data = await response.json();

            if (data.success) {
                const container = document.getElementById('departmentsCheckboxContainer');
                container.innerHTML = '';

                data.data.forEach(dept => {
                    const div = document.createElement('div');
                    div.className = 'checkbox-item';

                    const input = document.createElement('input');
                    input.type = 'checkbox';
                    input.id = 'dept_' + dept;
                    input.name = 'departments[]';
                    input.value = dept;
                    if (currentUserData.departments.includes(dept)) {
                        input.checked = true;
                    }

                    const label = document.createElement('label');
                    label.htmlFor = 'dept_' + dept;
                    label.textContent = dept;

                    div.appendChild(input);
                    div.appendChild(label);
                    container.appendChild(div);
                });
            } else {
                console.error('Error loading departments for checkboxes:', data.message);
            }
        } catch (error) {
            console.error('Error fetching departments for checkboxes:', error);
        }
    }

    // Fungsi untuk update bagian berdasarkan departemen dari database karyawan
    async function updateBagianFromScript() {
        const deptSelect = document.getElementById('dept');
        const bagianSelect = document.getElementById('bagian');
        const selectedDept = deptSelect.value;

        // Clear bagian options
        bagianSelect.innerHTML = '<option value="">Pilih Sub Dept</option>';

        if (!selectedDept) {
            return;
        }

        try {
            const response = await fetch(`get_employee_data.php?action=bagian&dept=${encodeURIComponent(selectedDept)}`);
            const data = await response.json();

            if (data.success) {
                data.data.forEach(bagian => {
                    const option = document.createElement('option');
                    option.value = bagian;
                    option.text = bagian;
                    if (bagian === currentUserData.bagian) {
                        option.selected = true;
                    }
                    bagianSelect.appendChild(option);
                });
            } else {
                console.error('Error loading bagian:', data.message);
            }
        } catch (error) {
            console.error('Error fetching bagian:', error);
        }

        // Update jabatan setelah bagian diperbarui
        await updateJabatanFromScript();
    }

    // Fungsi untuk update jabatan berdasarkan departemen dan bagian dari database karyawan
    async function updateJabatanFromScript() {
        const deptSelect = document.getElementById('dept');
        const bagianSelect = document.getElementById('bagian');
        const jabatanSelect = document.getElementById('jabatan');
        const selectedDept = deptSelect.value;
        const selectedBagian = bagianSelect.value;

        // Clear jabatan options
        jabatanSelect.innerHTML = '<option value="">Pilih Jabatan</option>';

        if (!selectedDept) {
            return;
        }

        try {
            let url = `get_employee_data.php?action=jabatan&dept=${encodeURIComponent(selectedDept)}`;
            if (selectedBagian) {
                url += `&bagian=${encodeURIComponent(selectedBagian)}`;
            }

            const response = await fetch(url);
            const data = await response.json();

            if (data.success) {
                data.data.forEach(jabatan => {
                    const option = document.createElement('option');
                    option.value = jabatan;
                    option.text = jabatan;
                    if (jabatan === currentUserData.jabatan) {
                        option.selected = true;
                    }
                    jabatanSelect.appendChild(option);
                });
            } else {
                console.error('Error loading jabatan:', data.message);
            }
        } catch (error) {
            console.error('Error fetching jabatan:', error);
        }
    }

    // Initialize everything on page load
    document.addEventListener('DOMContentLoaded', async function() {
        // Populate departemen dropdown
        await populateDepartmentOptions();

        // Populate departemen checkboxes
        await populateDepartmentCheckboxes();

        // Update bagian dan jabatan
        await updateBagianFromScript();

        // Set password field type ke password secara default
        const passwordField = document.getElementById('password');
        if (passwordField.value) {
            passwordField.type = 'password';
        }
    });
</script>

<?php include '../config/footer.php'; ?>
</body>


</html>