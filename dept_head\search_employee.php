<?php
include '../config/config.php';

header('Content-Type: application/json');

try {
    if (!isset($_GET['query'])) {
        throw new Exception('Search query is required');
    }

    $query = "%" . $_GET['query'] . "%";
    $sql = "SELECT nik, nama, dept, bagian, jabatan FROM karyawan WHERE nik LIKE ? OR nama LIKE ? LIMIT 10";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('Failed to prepare statement');
    }

    $stmt->bind_param("ss", $query, $query);
    if (!$stmt->execute()) {
        throw new Exception('Failed to execute query');
    }

    $result = $stmt->get_result();
    $employees = [];
    
    while ($row = $result->fetch_assoc()) {
        $employees[] = [
            'nik' => $row['nik'],
            'nama' => $row['nama'],
            'dept' => $row['dept'],
            'bagian' => $row['bagian'],
            'jabatan' => $row['jabatan']
        ];
    }

    echo json_encode($employees);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
} finally {
    if (isset($stmt)) {
        $stmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}
?>
