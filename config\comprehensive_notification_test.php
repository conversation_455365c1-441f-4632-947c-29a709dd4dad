<?php
/**
 * Comprehensive test untuk memastikan sistem notifikasi berfungsi penuh
 */

// Disable session untuk testing
define('NO_SESSION', true);

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/notification_helper.php';

echo "🔍 COMPREHENSIVE NOTIFICATION SYSTEM TEST\n";
echo "==========================================\n\n";

$all_tests_passed = true;

// Test 1: Email Configuration
echo "1️⃣ Testing Email Configuration...\n";
$settings_query = "SELECT smtp_server, smtp_port, sender_email, sender_name, smtp_encryption FROM settings WHERE id = 1";
$settings_result = $conn->query($settings_query);

if ($settings_result && $settings_result->num_rows > 0) {
    $settings = $settings_result->fetch_assoc();
    
    $required_fields = ['smtp_server', 'smtp_port', 'sender_email', 'sender_name'];
    $config_complete = true;
    
    foreach ($required_fields as $field) {
        if (empty($settings[$field])) {
            echo "   ❌ Missing: $field\n";
            $config_complete = false;
        }
    }
    
    if ($config_complete) {
        echo "   ✅ Email configuration complete\n";
        echo "   📧 SMTP: {$settings['smtp_server']}:{$settings['smtp_port']}\n";
        echo "   👤 From: {$settings['sender_name']} <{$settings['sender_email']}>\n";
    } else {
        echo "   ❌ Email configuration incomplete\n";
        $all_tests_passed = false;
    }
} else {
    echo "   ❌ No email settings found\n";
    $all_tests_passed = false;
}

echo "\n";

// Test 2: Department Head Mappings
echo "2️⃣ Testing Department Head Mappings...\n";
$dept_mappings_query = "SELECT COUNT(DISTINCT ud.dept) as dept_count, COUNT(DISTINCT u.id) as head_count 
                        FROM user_departments ud 
                        JOIN users u ON ud.user_id = u.id 
                        WHERE u.role_id = 2 AND u.is_active = 1";
$dept_mappings_result = $conn->query($dept_mappings_query);

if ($dept_mappings_result) {
    $mappings = $dept_mappings_result->fetch_assoc();
    echo "   📊 Departments covered: {$mappings['dept_count']}\n";
    echo "   👥 Active dept heads: {$mappings['head_count']}\n";
    
    if ($mappings['dept_count'] > 0 && $mappings['head_count'] > 0) {
        echo "   ✅ Department mappings available\n";
    } else {
        echo "   ❌ No department mappings found\n";
        $all_tests_passed = false;
    }
} else {
    echo "   ❌ Failed to check department mappings\n";
    $all_tests_passed = false;
}

echo "\n";

// Test 3: Key Department Heads
echo "3️⃣ Testing Key Department Heads...\n";
$key_departments = ['HRGA', 'WRH02', 'ENG', 'FAC'];

foreach ($key_departments as $dept) {
    $dept_head_query = "SELECT u.name, u.email FROM users u 
                        INNER JOIN user_departments ud ON u.id = ud.user_id 
                        WHERE ud.dept = ? AND u.role_id = 2 AND u.is_active = 1 LIMIT 1";
    $dept_head_stmt = $conn->prepare($dept_head_query);
    $dept_head_stmt->bind_param("s", $dept);
    $dept_head_stmt->execute();
    $dept_head_result = $dept_head_stmt->get_result();
    
    if ($dept_head_result->num_rows > 0) {
        $dept_head = $dept_head_result->fetch_assoc();
        echo "   ✅ $dept: {$dept_head['name']} ({$dept_head['email']})\n";
    } else {
        echo "   ❌ $dept: No dept head found\n";
        $all_tests_passed = false;
    }
}

echo "\n";

// Test 4: Notification Function
echo "4️⃣ Testing Notification Function...\n";

// Cari training data untuk test
$training_query = "SELECT ts.id, ts.training_topic, ts.departemen, u.name as requester_name 
                   FROM training_submissions ts 
                   LEFT JOIN users u ON ts.user_id = u.id 
                   WHERE ts.departemen = 'HRGA' 
                   ORDER BY ts.id DESC LIMIT 1";
$training_result = $conn->query($training_query);

if ($training_result && $training_result->num_rows > 0) {
    $training = $training_result->fetch_assoc();
    echo "   📚 Test training: {$training['training_topic']} (ID: {$training['id']})\n";
    echo "   🏢 Department: {$training['departemen']}\n";
    echo "   👤 Requester: {$training['requester_name']}\n";
    
    try {
        // Test notification function (tanpa next_approver_id spesifik)
        $result = send_status_update_notification(
            $training['id'],
            'Pending',
            'Comprehensive test notification',
            '',
            2, // dept head role
            $conn,
            null // let system find correct dept head
        );
        
        if ($result['success']) {
            echo "   ✅ Notification function works\n";
            echo "   📧 {$result['message']}\n";
        } else {
            echo "   ❌ Notification function failed\n";
            echo "   💥 {$result['message']}\n";
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "   ❌ Exception in notification function: " . $e->getMessage() . "\n";
        $all_tests_passed = false;
    }
} else {
    echo "   ⚠️  No HRGA training found for testing\n";
}

echo "\n";

// Test 5: Email Template
echo "5️⃣ Testing Email Template...\n";
$template_file = __DIR__ . '/email_templates/status_update.php';

if (file_exists($template_file)) {
    echo "   ✅ Email template exists\n";
    
    // Test template rendering
    $test_data = [
        'email_title' => 'Test Email',
        'recipient_name' => 'Test User',
        'intro_message' => 'Test message',
        'training_id' => 123,
        'training_topic' => 'Test Training',
        'training_date' => '01 January 2025',
        'requester_name' => 'Test Requester',
        'requester_nik' => '12345',
        'departemen' => 'TEST',
        'status' => 'Pending',
        'status_color' => '#ffc107',
        'comments' => 'Test comments',
        'participants' => [],
        'action_required' => true,
        'action_message' => 'Test action',
        'action_url' => 'http://localhost/test'
    ];
    
    try {
        ob_start();
        $data = $test_data;
        include $template_file;
        $template_output = ob_get_clean();
        
        if (!empty($template_output) && strpos($template_output, 'Test Email') !== false) {
            echo "   ✅ Email template renders correctly\n";
        } else {
            echo "   ❌ Email template rendering failed\n";
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "   ❌ Template rendering error: " . $e->getMessage() . "\n";
        $all_tests_passed = false;
    }
} else {
    echo "   ❌ Email template not found\n";
    $all_tests_passed = false;
}

echo "\n";

// Test 6: PHPMailer Availability
echo "6️⃣ Testing PHPMailer Availability...\n";
$autoload_path = __DIR__ . '/../vendor/autoload.php';

if (file_exists($autoload_path)) {
    echo "   ✅ PHPMailer autoload found\n";
    
    try {
        require_once $autoload_path;
        if (class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
            echo "   ✅ PHPMailer class available\n";
        } else {
            echo "   ❌ PHPMailer class not found\n";
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "   ❌ PHPMailer loading error: " . $e->getMessage() . "\n";
        $all_tests_passed = false;
    }
} else {
    echo "   ❌ PHPMailer not installed\n";
    $all_tests_passed = false;
}

echo "\n";

// Final Result
echo "🏁 COMPREHENSIVE TEST RESULTS\n";
echo "==============================\n";

if ($all_tests_passed) {
    echo "🎉 ALL TESTS PASSED! \n";
    echo "✅ Sistem notifikasi BERFUNGSI PENUH\n\n";
    
    echo "📋 Summary:\n";
    echo "   ✅ Email configuration: Complete\n";
    echo "   ✅ Department mappings: Available\n";
    echo "   ✅ Key dept heads: Found\n";
    echo "   ✅ Notification function: Working\n";
    echo "   ✅ Email template: Functional\n";
    echo "   ✅ PHPMailer: Available\n";
    
    echo "\n🚀 SISTEM SIAP DIGUNAKAN!\n";
} else {
    echo "❌ SOME TESTS FAILED\n";
    echo "⚠️  Sistem notifikasi mungkin tidak berfungsi optimal\n";
    echo "🔧 Periksa komponen yang gagal di atas\n";
}

$conn->close();
?>
