<?php
// Database connection parameters
$host = "localhost"; 
$username = "root";  
$password = "";      
$database = "db_training"; 

// Connect to MySQL
$conn = new mysqli($host, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Function to get all tables with their dependencies
function getTableDependencies($conn, $database) {
    $tables = [];
    $dependencies = [];
    
    // Get all tables
    $result = $conn->query("SHOW TABLES FROM `$database`");
    while ($row = $result->fetch_row()) {
        $tables[] = $row[0];
    }
    
    // Get foreign key dependencies for each table
    foreach ($tables as $table) {
        $dependencies[$table] = [];
        
        $result = $conn->query("
            SELECT REFERENCED_TABLE_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = '$database'
            AND TABLE_NAME = '$table'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        
        while ($row = $result->fetch_assoc()) {
            $dependencies[$table][] = $row['REFERENCED_TABLE_NAME'];
        }
    }
    
    return [
        'tables' => $tables,
        'dependencies' => $dependencies
    ];
}

// Function to sort tables based on dependencies
function sortTables($tables, $dependencies) {
    $sorted = [];
    $visited = [];
    
    // Helper function for topological sort
    function visit($table, $dependencies, &$sorted, &$visited) {
        if (isset($visited[$table])) {
            return;
        }
        
        $visited[$table] = true;
        
        if (isset($dependencies[$table])) {
            foreach ($dependencies[$table] as $dependency) {
                visit($dependency, $dependencies, $sorted, $visited);
            }
        }
        
        $sorted[] = $table;
    }
    
    // Visit each table
    foreach ($tables as $table) {
        visit($table, $dependencies, $sorted, $visited);
    }
    
    return $sorted;
}

// Function to export a table
function exportTable($conn, $username, $database, $table, $outputDir) {
    $filename = "$outputDir/$table.sql";
    
    // Create the output directory if it doesn't exist
    if (!file_exists($outputDir)) {
        mkdir($outputDir, 0777, true);
    }
    
    // Export structure and data
    $command = "mysqldump -u $username --no-create-db --tables $database $table > \"$filename\"";
    
    exec($command, $output, $returnVar);
    
    return $returnVar === 0;
}

// Main execution
$action = $_GET['action'] ?? '';

if ($action === 'analyze') {
    $info = getTableDependencies($conn, $database);
    $sortedTables = sortTables($info['tables'], $info['dependencies']);
    
    echo "<h1>Database Table Analysis</h1>";
    echo "<h2>Tables in Correct Import Order:</h2>";
    echo "<ol>";
    foreach ($sortedTables as $table) {
        echo "<li>$table";
        if (!empty($info['dependencies'][$table])) {
            echo " (depends on: " . implode(", ", $info['dependencies'][$table]) . ")";
        }
        echo "</li>";
    }
    echo "</ol>";
    
    echo "<p><a href='extract_tables.php?action=export' class='button'>Export Tables in Correct Order</a></p>";
}
elseif ($action === 'export') {
    $info = getTableDependencies($conn, $database);
    $sortedTables = sortTables($info['tables'], $info['dependencies']);
    
    $outputDir = "db_export";
    
    echo "<h1>Exporting Tables</h1>";
    echo "<ul>";
    
    foreach ($sortedTables as $table) {
        $success = exportTable($conn, $username, $database, $table, $outputDir);
        echo "<li>$table: " . ($success ? "Success" : "Failed") . "</li>";
    }
    
    echo "</ul>";
    
    // Create a combined SQL file with proper order
    $combinedFile = "$outputDir/combined_import.sql";
    $fp = fopen($combinedFile, 'w');
    
    fwrite($fp, "-- Combined SQL import file with correct table order\n");
    fwrite($fp, "-- Generated by extract_tables.php\n\n");
    fwrite($fp, "SET FOREIGN_KEY_CHECKS=0;\n\n");
    
    foreach ($sortedTables as $table) {
        $tableFile = "$outputDir/$table.sql";
        if (file_exists($tableFile)) {
            fwrite($fp, "-- Importing $table\n");
            fwrite($fp, file_get_contents($tableFile));
            fwrite($fp, "\n\n");
        }
    }
    
    fwrite($fp, "SET FOREIGN_KEY_CHECKS=1;\n");
    fclose($fp);
    
    echo "<p>Combined import file created: <strong>$combinedFile</strong></p>";
    echo "<p><a href='extract_tables.php' class='button'>Back to Main</a></p>";
}
else {
    // Default view
    echo "<h1>Database Table Extraction Tool</h1>";
    echo "<p>This tool helps analyze and extract database tables in the correct order to avoid foreign key constraint issues.</p>";
    
    echo "<h2>Options:</h2>";
    echo "<p><a href='extract_tables.php?action=analyze' class='button'>Analyze Table Dependencies</a></p>";
    echo "<p><a href='import_fix.php' class='button'>Go to Import Fix Tool</a></p>";
}

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 20px;
}
h1, h2 {
    color: #333;
}
.button {
    display: inline-block;
    padding: 10px 15px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    margin: 5px 0;
}
ul, ol {
    margin-bottom: 20px;
}
</style>
