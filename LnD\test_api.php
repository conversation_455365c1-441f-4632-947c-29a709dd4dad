<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h2>API Test</h2>
    <div id="result"></div>

    <script>
        console.log('Testing API call...');

        // Test the API call
        fetch('../api/get_organization_data.php?type=all')
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('Response text:', text);
                document.getElementById('result').innerHTML = '<pre>' + text + '</pre>';

                // Try to parse as JSON
                try {
                    const data = JSON.parse(text);
                    console.log('Parsed JSON:', data);
                } catch (e) {
                    console.error('Failed to parse JSON:', e);
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                document.getElementById('result').innerHTML = '<div style="color: red;">Error: ' + error.message + '</div>';
            });
    </script>
</body>
</html>
