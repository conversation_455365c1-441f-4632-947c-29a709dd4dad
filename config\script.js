// Departemen options
const departemenOptions = ["ADMIN", "DIR", "ENG", "EXP", "FAC", "GMN", "HRGA", "IRER", "ITE", "MEX", "PPC", "PRN01", "PRN02", "PRS01", "PRS02", "PRS03", "PUR01", "PUR02", "QCO", "RQA", "WRH01", "WRH02"];

// Bagian (Sub Departemen) berdasarkan Departemen
const Bagian = {
    "ADMIN": ["ADMIN"],
    "DIR": ["DIR"],
    "ENG": ["EKL", "ENG", "EPR", "EU1", "EU2", "EWS", "HSE", "R&I"],
    "EXP": ["CPP", "EXS", "TIM"],
    "FAC": ["ACC", "FAC"],
    "GMN": ["FM"],
    "HRGA": ["HAP", "HGA", "HRGA", "HRR", "HRT"],
    "IRER": ["ER", "IR"],
    "ITE": ["ITE"],
    "MEX": ["OPE", "QEX"],
    "PPC": ["PPC"],
    "PRN01": ["PRN01"],
    "PRN02": ["PRC", "PRN02", "PSYM"],
    "PRS01": ["PRS01"],
    "PRS02": ["PRS02", "PRSS"],
    "PRS03": ["PRS03"],
    "PUR01": ["PURRM"],
    "PUR02": ["PURSP"],
    "QCO": ["FSMS", "QCN01", "QCN02", "QCO", "QCS01", "QCS02", "QCS03", "QRM"],
    "RQA": ["QA", "QLN", "QLS", "RDS", "RND"],
    "WRH01": ["FRM", "WFM", "WPM", "WRH01", "WRM", "WSM"],
    "WRH02": ["WBB", "WCP", "WFG", "WLQ", "WRH02", "WSP"],
};

// Jabatan berdasarkan Bagian
const Jabatan = {
"ACC": [
"ACC_Helper",
"ACC_Supervisor",
"Staff A/R",
"Staff Analyst KPI",
"Staff Finance",
"Staff Inventory Control",
"Supervisor Analyst KPI",
],
"ADMIN": [
"ADMINISTRATOR",
],
"CPP": [
"CPP_Driver_Jemputan",
],
"DIR": [
"Direktur",
],
"EKL": [
"EKL_Administrasi",
"EKL_Teknisi",
],
"ENG": [
"Manager Engineering & HSE",
],
"EPR": [
"Chief Spv Project Mechanical Electrical Automation",
"EPR_Helper",
"Foreman Automation",
"Foreman Electric",
"Foreman Project",
"Spv Project Mechanical Electrical Automation",
"Supervisor Project Automation",
],
"ER": [
"Manager IR & ER",
"Senior Supervisor IR & ER",
"Staff ER",
],
"EU1": [
"Chief Supervisor Utility",
"EU1_Helper",
"EU1_Leader",
"EU1_Teknisi",
"Foreman Corrective Boiler & WWTP",
"Foreman Corrective Maintenance",
"Foreman Improvement Maintenance",
"Foreman Preventive Maintenance",
"Supervisor Utility 1",
],
"EU2": [
"EU2_Helper",
"EU2_Leader",
"EU2_Operator_WWT",
"EU2_Teknisi",
"Foreman Chemical Management & Water Treatment",
"Foreman Kalibrasi",
"Foreman Operasional Utility",
"Supervisor Utility 2",
],
"EWS": [
"Chief Supervisor Project & Workshop",
"EWS_ADM_HRD",
"EWS_Administrasi",
"EWS_Helper",
"EWS_Leader",
"EWS_Teknisi",
"Foreman Fabrikasi",
"Foreman Machining",
"Foreman Maintenance Building",
"Foreman Maintenance Planner",
"Foreman Otomotif",
"Foreman Project Koordinator",
"Supervisor Project",
"Supervisor Workshop",
],
"EXS": [
"EXS_staff",
"Manajer Expedisi",
"Staff Expedisi",
],
"FAC": [
"Manajer Finance & Accounting",
"Staff A/P",
"Staff Finance",
"Staff Tax",
"Supervisor A/P",
"Supervisor Cashier",
"Supervisor Costing",
"Supervisor Finance",
],
"FM": [
"Factory Manager",
],
"FRM": [
"FRM_ADM_HRD",
"FRM_Administrasi",
"FRM_Driver_Forklift",
"FRM_Helper",
"FRM_Pengawas_Bongkar_Muat",
"FRM_Pengawas_Pintu Gudang",
"FRM_Stock_Control",
"FRM_Teknisi",
],
"FSMS": [
"Administrasi QCN",
"Foreman FSMS",
"Helper QCN",
"Leader QCN",
"Supervisor FSMS",
],
"HAP": [
"Chief Supervisor HR Operation",
"HAP_Adm Personalia",
"Personalia & Payroll Specialist",
"Staff HRD BAS",
"Staff Payroll & Personalia PAS",
],
"HGA": [
"Chief Supervisor GA",
"Cleaning & Maintenance Supervisor",
"HGA_ADM_HRD",
"HGA_Administrasi GA",
"HGA_Driver_Internal",
"HGA_Helper",
"HGA_Leader",
"HGA_Mesennger",
"HGA_Receiptionist",
"Security & Gardening Coordinator",
"Staff Canteen, Clinic, Hospitality Coord",
"Staff Cleaning & Maintenance",
],
"HRGA": [
"Manager HRGA",
],
"HRR": [
"Staff Recruitment",
"Supervisor Recruitment",
],
"HRT": [
"Chief Supervisor Learning & Development",
"HAP_Staff Recruitment",
"Staff Learning & Development Soft Skills",
"Supervisor Learning & Development Technical Skills",
],
"HSE": [
"Chief Supervisor HSE",
"Foreman HSE",
"HSE_Administrasi",
"HSE_Stock_Control",
"Supervisor HSE",
],
"IR": [
"Staff IR",
],
"ITE": [
"Foreman IT",
"ITE_Administrasi",
"ITE_Helper",
"ITE_Teknisi",
"Supervisor IT",
],
"OPE": [
"Chief Supervisor Manufacturing Excellence",
"Staff Manufacturing Excellence",
"Supervisor Manufacturing Excellence",
],
"PPC": [
"Chief Supervisor PPIC PAS",
"Manajer PPIC & MANEX",
"PPC_Senior Staff",
"PPC_Specialist",
"PPC_staff",
"Staff Material Planner Kardus",
"Staff Material Planner PM Lokal",
"Staff Material Planner Premix",
"Staff Scheduler LAS",
"Staff Scheduler Noodle 2",
"Staff Scheduler SAS",
"Staff Scheduler Seasoning 3",
"Supervisor Material Planner 1",
"Supervisor Material Planner 2",
"Supervisor PPIC BAS",
"Supervisor PPIC SAS",
"Supervisor Scheduler Noodle",
"Supervisor Scheduler Seasoning",
],
"PRC": [
"PRC_ADM_HRD",
"PRC_ENG_TEKNISI",
"PRC_Helper",
"PRC_Operator_Alkali",
"PRC_Operator_Cup_Maker",
"PRC_Operator_Friyer",
"PRC_Operator_Garpu",
"PRC_Operator_Husky",
"PRC_Operator_Packing",
"PRC_Operator_Proses",
"PRC_Operator_Roll_Pres",
"PRC_Sub_Leader_CUP_Maker",
"PRC_Sub_Leader_Packing",
"PRC_Sub_Leader_Supply",
"PRC_Subleader_Proses",
],
"PRN01": [
"Chief Supervisor Engineering Noodle 1",
"Chief Supervisor Produksi Noodle 1",
"Driver_forklift_Noodle",
"Foreman Automation Noodle 1",
"Foreman Corrective Packing",
"Foreman Corrective Proses",
"Foreman DC & FSMS",
"Foreman HSE & Building Maintenance",
"Foreman Improvement System",
"Foreman Packing Noodle 1",
"Foreman Planner Engineering Noodle 1",
"Foreman Preventive Packing Noodle 1",
"Foreman Preventive Proses Noodle 1",
"Foreman Proses Noodle 1",
"Foreman Supply",
"Foreman Workshop",
"Manager Produksi Noodle 1",
"Opr_Robot",
"PRN_ADM_HRD_A",
"PRN_ADM_HRD_B",
"PRN_ADM_HRD_C",
"PRN_ADM_HRD_N",
"PRN_Admin_Silo",
"PRN_Administrasi_Field",
"PRN_Administrasi_produksi",
"PRN_Barcode_Scaner",
"PRN_Driver_Forklift",
"PRN_ENG_TEKNISI",
"PRN_Helper",
"PRN_Leader_Mixer",
"PRN_Leader_Packing",
"PRN_Leader_Palletezing",
"PRN_Leader_Proses",
"PRN_Operator_Alkali",
"PRN_Operator_Fryer",
"PRN_Operator_Mixer",
"PRN_Operator_Packing",
"PRN_Operator_Premix",
"PRN_Operator_RollPress",
"PRN_Scaner",
"PRN_Sub_leader_Packing",
"PRN_Teknisi_CNP",
"Supervisor Corrective Maintenance",
"Supervisor Improvement System, MB & Automation",
"Supervisor Packing Noodle 1",
"Supervisor Preventive Maintenance",
"Supervisor Proses Noodle 1",
],
"PRN02": [
"Chief Supervisor Engineering Noodle 2",
"Chief Supervisor Produksi Noodle 2",
"Foreman Corrective Cup Support",
"Foreman Corrective Process Packing",
"Foreman Cup & Garpu",
"Foreman HSE Noodle 2",
"Foreman Material Balance & System Improvement",
"Foreman Packing Noodle 2",
"Foreman Plain Maintenance",
"Foreman Preventive Cup Support",
"Foreman Preventive Packing Noodle 2",
"Foreman Preventive Proses Noodle 2",
"Foreman Project Improvement",
"Foreman Proses Noodle 2",
"Manager Produksi Noodle 2",
"PRN_02_Administrasi_Produksi",
"PRN_02_Administrasi_Silo",
"PRN_02_Helper",
"PRN_02_Leader_Packing",
"PRN_02_Leader_Proses",
"PRN_02_Operator_Friyer",
"PRN_02_Operator_Mixer",
"PRN_02_Operator_Packing",
"PRN_02_Operator_Proses",
"PRN_02_Operator_Roll",
"PRN_02_STOCK",
"PRN_02_Sub_Leader_Packing",
"PRN02_ADM_HRD",
"PRN02_Sub_Leader_Supply",
"Supervisor Cup Support & Project Improvement",
"Supervisor Produksi Noodle 2",
"Supervisor Proses & Packing",
],
"PRS01": [
"Chief Supervisor Produksi Seasoning 1",
"Foreman Corrective Engineering",
"Foreman GA, DC, HSE, & FSMS",
"Foreman Material Balance",
"Foreman Packing Seasoning 1",
"Foreman Plan Maintenance",
"Foreman Project & Preventive Packing",
"Foreman Project & Preventive Proses",
"Foreman Proses Seasoning 1",
"Manager Produksi Seasoning 1",
"PRS_ADM_HRD_A",
"PRS_ADM_HRD_B",
"PRS_ADM_HRD_C",
"PRS_Administrasi HSE & Downtime",
"PRS_Administrasi Maintenance",
"PRS_Administrasi_Field",
"PRS_Barcode_Scaner",
"PRS_Driver_Forklift",
"PRS_ENG_teknisi",
"PRS_Helper",
"PRS_Leader_Oil_Packing",
"PRS_Leader_Powder_Packing",
"PRS_Leader_Proses",
"PRS_Operator_Packing_Oil",
"PRS_Operator_Packing_Powder",
"PRS_Operator_Proses",
"PRS_Sub_Leader_Oil_Packing",
"PRS_Sub_Leader_Powder_Packing",
"PRS_Sub_Leader_Proses",
"Supervisor Engineering Produksi Seasoning 1",
"Supervisor Produksi Seasoning 1",
],
"PRS02": [
"Chief Supervisor Engineering Seasoning 2",
"Chief Supervisor Produksi Seasoning 2",
"Foreman CM Seasoning 2",
"Foreman FGC",
"Foreman HSE & FSMS",
"Foreman MB Seasoning 2",
"Foreman Packing Seasoning 2",
"Foreman PM Packing",
"Foreman Pre FGC",
"Manajer Produksi Seasoning 2",
"PRS02_ADM_HRD_A",
"PRS02_ADM_HRD_B",
"PRS02_ADM_HRD_C",
"PRS02_ADM_HRD_N",
"PRS02_Administrasi_Produksi",
"PRS02_Barcode_Scaner",
"PRS02_Driver_Forklift",
"PRS02_ENG_TEKNISI",
"PRS02_Helper",
"PRS02_Leader_Packing_Garnish",
"PRS02_Leader_Proses",
"PRS02_Operator_Packing_Garnish",
"PRS02_Operator_Packing_Powder",
"PRS02_Operator_Proses",
"PRS02_Sub_Leader_Powder_Packing",
"PRS02_Sub_Leader_Proses",
"Supervisor Engineering Produksi Seasoning 2",
"Supervisor Packing Seasoning 2",
"Supervisor Proses Seasoning 2",
],
"PRS03": [
"Foreman CM Seasoning 3",
"Foreman Packing Seasoning 3",
"Foreman PM Proses & Packing",
"Foreman Proses Seasoning 3",
"Foreman SAP & Planner",
"PRS03_ADM_HRD_A",
"PRS03_ADM_HRD_C",
"PRS03_ADM_HRD_N",
"PRS03_Administrasi_Field",
"PRS03_Driver_Forklift",
"PRS03_ENG_TEKNISI",
"PRS03_Helper",
"PRS03_Leader_Packing_Powder",
"PRS03_Leader_Proses",
"PRS03_Operator_Packing_Powder",
"PRS03_Sub_Leader_Powder_Packing",
"PRS03_Sub_Leader_Proses",
"PRS03_Subleader_Mixer_Powder",
"Supervisor Engineering",
"Supervisor Produksi Seasoning 3",
],
"PRSS": [
"Foreman Saos",
"PRSS_Administrasi_Produksi",
"PRSS_Helper",
"PRSS_Leader",
"Supervisor Saos",
],
"PSYM": [
"PSYM_Helper",
"PSYM_Operator_Fryer",
"PSYM_Operator_Mixer",
"PSYM_Operator_Packing",
"PSYM_Operator_Proses",
"PSYM_Sub_Leader_Packing",
],
"PURRM": [
"Chief Supervisor Operasional & Corporate RM",
"Manager Purchasing RM",
"PUR_Helper",
"PUR_Senior Staff",
"PUR_staff",
"Staff Corporate RM Chemical Based",
"Staff Fresh RM,Dry Vegetable,Coloring,Saos,Seas",
"Staff PM Flexible Export & Other Plastic Pack",
"Staff PM Flexible Lokal",
"Staff PM Paper Based",
"Staff Purchasing Co-Product",
"Staff Purchasing Merchandise",
"Staff RM Bumbu Dapur",
"Staff RM Seasoning Premix",
"Supervisor Purchasing Corporate RM",
"Supervisor Purchasing Merchandise & Co Product",
"Supervisor Purchasing Operasional Non PAS",
"Supervisor Purchasing Operasional PM PAS",
"Supervisor Purchasing Operasional RM PAS",
],
"PURSP": [
"Chief Supervisor Purchasing Spare Part",
"Manajer Purchasing Spare Part",
"Staff Purchasing Spare Part",
"Supervisor Purchasing Spare Part",
],
"QA": [
"Manager RQA",
"Supervisor QA Lab",
],
"QCN01": [
"Administrasi QCN",
"Chief Supervisor QCN",
"Field QCC",
"Field QCN",
"Foreman Improvement QCN",
"Foreman QCN 1",
"Supervisor QCN 1",
],
"QCN02": [
"Administrasi QCC",
"Field QCC",
"Supervisor QCN 2",
],
"QCO": [
"Manager QCRM",
],
"QCS01": [
"Chief Supervisor QC Seasoning",
"Field QCS",
"Foreman Improvement QC Seasoning",
"Foreman Oil",
],
"QCS02": [
"Administrasi QCS",
"Field QCS",
"Foreman Packing Garnish",
"Supervisor Garnish & Saos",
],
"QCS03": [
"Field QCS",
"Foreman Powder",
"Supervisor Powder",
],
"QEX": [
"Quality Excellent Specialist",
],
"QLN": [
"Foreman QA Lab Noodle",
"QLN_ADM_HRD",
"QLN_Noodle_Administrasi",
"QLN_Noodle_Analist",
"QLN_Noodle_Helper",
],
"QLS": [
"Foreman QA Lab Seasoning",
"QLS_Administrasi",
"QLS_Seasoning_Analist",
"QLS_Seasoning_Helper",
],
"QRM": [
"Administrasi QRM",
"Field QRM",
"Foreman QCRM",
"Helper QRM",
"Supervisor QCRM",
],
"R&I": [
"R&I Dokumen Control",
],
"RDS": [
"Foreman RDS",
"Supervisor R&D Review & Improvement Seasoning",
"Supervisor RDS",
],
"RND": [
"Admin_RND",
"Chief R&D Review & Improvement",
"Foreman QA Regulasi",
"Foreman R&D Development",
"RND_ADM_HRD",
"RND_Helper",
"RND_Leader",
"Senior Foreman R&D Development",
"Supervisor R&D Development",
"Supervisor R&D Review & Improvement Noodle",
],
"TIM": [
"Supervisor Expedisi",
"TIM_ADM_HRD",
"TIM_Administrasi",
],
"WBB": [
"WBB_Driver_Wheel_Loader",
"WBB_Helper",
],
"WCP": [
"Foreman Stock Analyst & Reporting",
"Foreman WCP",
"Supervisor WLQ/WCP",
"WCP_ADM_HRD",
"WCP_Administrasi",
"WCP_Driver_Forklift",
"WCP_Driver_Losbak",
"WCP_Helper",
"WCP_Pengawas_bongkar_Muat",
"WCP_Stock_Control",
],
"WFG": [
"Foreman Stock Analyst & Reporting WFG",
"Foreman WFG",
"WFG_ADM_HRD",
"WFG_Administrasi",
"WFG_Barcode_Scaner",
"WFG_Driver_Forklift",
"WFG_Helper",
"WFG_Pengawas_Bongkar_Muat",
"WFG_Stock_Control",
],
"WFM": [
"Foreman WFM",
"Supervisor WFM",
],
"WLQ": [
"Foreman WLQ/WBB",
"WLQ_Driver_Losbak",
"WLQ_Helper",
"WLQ_Pengawas_Bongkar_Muat",
],
"WPM": [
"Foreman WPM",
"Supervisor WPM",
"WPM_ADM_HRD",
"WPM_Administrasi",
"WPM_Driver_Built_Up",
"WPM_Driver_forklift",
"WPM_Driver_Reach_Truck",
"WPM_Helper",
"WPM_Leader_Bongkar_Muat",
"WPM_Pengawas_Bongkar_Muat",
"WPM_Pengawas_Pintu",
"WPM_Stock_Control",
"WPM_Sub_Leader",
],
"WRH01": [
"Chief Supervisor WRH 1",
"Chief Supervisor WRH 2",
],
"WRH02": [
"Manager Warehouse 2",
],
"WRM": [
"Foreman Warehouse Excellent",
"Foreman WRM",
"Supervisor WRM",
"WRM_Administrasi",
"WRM_Driver_Built Up",
"WRM_Driver_forklift",
"WRM_Helper",
"WRM_Pengawas_bongkar_muat",
"WRM_Pengawas_Pintu Gudang",
"WRM_Stock_Control",
],
"WSM": [
"Foreman WSM",
"Supervisor WSM",
"WSM_ADM_HRD",
"WSM_Administrasi",
"WSM_Driver_forklift",
"WSM_Driver_Losbak",
"WSM_Driver_Reach_Truck",
"WSM_Helper",
"WSM_Pengawas_bongkar_muat",
"WSM_Pengawas_Pintu Gudang",
"WSM_Stock_Control",
],
"WSP": [
"Foreman WSP",
"Supervisor WSP",
"WSP_Administrasi",
"WSP_Stock_Control",
],
};

// Function to update Bagian dropdown based on selected Dept
function updateBagian(deptSelect, bagianSelect) {
    const selectedDept = deptSelect.value;
    bagianSelect.innerHTML = '<option value="">Pilih Sub Dept</option>';

    if (Bagian[selectedDept]) {
        Bagian[selectedDept].forEach(bagian => {
            const option = document.createElement('option');
            option.value = bagian;
            option.text = bagian;
            bagianSelect.appendChild(option);
        });
    }
}

// Function to update Jabatan dropdown based on selected Bagian
function updateJabatan(bagianSelect, jabatanSelect) {
    const selectedBagian = bagianSelect.value;
    jabatanSelect.innerHTML = '<option value="">Pilih Jabatan</option>';

    if (Jabatan[selectedBagian]) {
        Jabatan[selectedBagian].forEach(jabatan => {
            const option = document.createElement('option');
            option.value = jabatan;
            option.text = jabatan;
            jabatanSelect.appendChild(option);
        });
    }
}