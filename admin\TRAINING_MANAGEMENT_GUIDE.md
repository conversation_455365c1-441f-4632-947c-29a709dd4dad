# Panduan Manajemen Training - Admin

## Konsep Training System

### 📋 **Dua <PERSON> Training**
1. **Training Internal**: Data disimpan di tabel `offline_training`
2. **Training Eksternal**: Data disimpan di tabel `training_submissions` (status: Approved)

### 🎯 **Dua Cara Menambah Training**

#### **1. Buat Training Baru**
- **Buat Training Internal**: Membuat record baru di `offline_training`
- **Buat Training Eksternal**: Membuat record baru di `training_submissions`

#### **2. Pilih Training Existing**
- **Pilih Training Internal**: Memilih dari data existing di `offline_training`
- **Pilih Training Eksternal**: Memilih dari data existing di `training_submissions`

## Fitur Quick Actions

### 🟢 **Buat Training Internal**
- **Fungsi**: Membuat Training Internal baru
- **Target**: Tabel `offline_training`
- **Form**: Modal dengan field lengkap untuk Training Internal
- **Result**: Record baru di `offline_training`

### 🔵 **Buat Training Eksternal**
- **Fungsi**: Membuat Training Eksternal baru
- **Target**: Tabel `training_submissions`
- **Form**: Modal dengan field lengkap untuk Training Eksternal
- **Result**: Record baru di `training_submissions` dengan status "Approved"

### 🟡 **Pilih Training Internal**
- **Fungsi**: Memilih dari Training Internal yang sudah ada
- **Source**: Tabel `offline_training`
- **Interface**: Modal dengan tabel searchable
- **Result**: Training existing ditampilkan di kalender

### 🟣 **Pilih Training Eksternal**
- **Fungsi**: Memilih dari Training Eksternal yang sudah ada
- **Source**: Tabel `training_submissions`
- **Interface**: Modal dengan tabel searchable
- **Result**: Training existing ditampilkan di kalender

## Cara Penggunaan

### 1. Akses Dashboard Admin
```
http://localhost/Training/admin/calendar_management.php
```

### 2. Quick Actions
Di bagian atas kalender terdapat 6 tombol:

#### **Membuat Training Baru**
1. **Klik "Buat Training Internal"** → Form modal untuk training baru
2. **Klik "Buat Training Eksternal"** → Form modal untuk training baru
3. **Isi form lengkap** → Data disimpan ke tabel yang sesuai
4. **Klik "Simpan"** → Training muncul di kalender

#### **Memilih Training Existing**
1. **Klik "Pilih Training Internal"** → Modal daftar Training Internal
2. **Klik "Pilih Training Eksternal"** → Modal daftar Training Eksternal
3. **Gunakan search** untuk mencari training tertentu
4. **Klik "Pilih"** → Training ditampilkan di kalender

### 3. Manajemen Training di Kalender
- **Klik event** → Lihat detail training
- **Edit Training** → Ubah data training
- **Kelola Peserta** → Tambah/hapus peserta
- **Hapus Training** → Hapus training dari sistem

## Database Schema

### offline_training (Training Internal)
```sql
- id (Primary Key)
- training_topic (Judul)
- training_description (Deskripsi)
- training_date (Tanggal)
- training_time_start (Waktu mulai)
- training_time_end (Waktu selesai)
- location (Lokasi)
- trainer_name (Trainer)
- max_participants (Max peserta)
- status (Active/Completed/Cancelled)
- created_by (User pembuat)
- created_at, updated_at
```

### training_submissions (Training Eksternal)
```sql
- id (Primary Key)
- user_id (User pembuat)
- training_topic (Judul)
- additional_info (Deskripsi)
- training_date_fixed (Tanggal)
- training_time_start (Waktu mulai)
- training_time_end (Waktu selesai)
- training_place (Lokasi)
- contact_person (Trainer/PIC)
- status (Approved untuk training aktif)
- created_at, updated_at
```

## API Endpoints

### Existing Training APIs
- `GET /admin/api/get_existing_trainings.php?type=offline` - Daftar Training Internal
- `GET /admin/api/get_existing_trainings.php?type=online` - Daftar Training Eksternal
- `GET /admin/api/get_existing_trainings.php?type=offline&search=keyword` - Search training

### Training Management APIs
- `POST /admin/api/save_training.php` - Simpan/update training
- `POST /admin/api/delete_training.php` - Hapus training
- `GET /admin/api/get_admin_training_detail.php` - Detail training

## Workflow Training

### Scenario 1: Training Baru
1. **Admin klik "Buat Training Internal/Online"**
2. **Isi form dengan data baru**
3. **Simpan → Record baru dibuat**
4. **Training muncul di kalender**

### Scenario 2: Training Existing
1. **Admin klik "Pilih Training Internal/Online"**
2. **Browse/search training yang sudah ada**
3. **Pilih training → Ditampilkan di kalender**
4. **Training dapat diedit/dikelola seperti biasa**

## Keuntungan Sistem Ini

### ✅ **Fleksibilitas**
- Bisa buat training baru atau pilih existing
- Dua sumber data terpisah sesuai jenis training

### ✅ **Efisiensi**
- Tidak perlu input ulang untuk training berulang
- Search dan filter untuk menemukan training cepat

### ✅ **Konsistensi Data**
- Training Internal selalu dari `offline_training`
- Training Eksternal selalu dari `training_submissions`

### ✅ **User Experience**
- Interface yang jelas dan intuitif
- Modal yang responsive dan user-friendly

## Troubleshooting

### Training Tidak Muncul di Modal
1. **Cek tabel database** - Pastikan data ada
2. **Cek status training** - Harus status yang valid
3. **Cek API response** - Lihat browser console

### Error Saat Simpan Training
1. **Validasi form** - Pastikan field required terisi
2. **Cek database connection** - Pastikan koneksi OK
3. **Lihat error log** - Check server error log

### Modal Tidak Terbuka
1. **Cek JavaScript console** - Lihat error JS
2. **Pastikan Bootstrap loaded** - Cek library
3. **Clear browser cache** - Refresh halaman

## Tips Penggunaan

### 🎯 **Best Practices**
1. **Gunakan "Buat Baru"** untuk training yang belum pernah ada
2. **Gunakan "Pilih Existing"** untuk training berulang/reguler
3. **Manfaatkan search** untuk menemukan training cepat
4. **Update data existing** jika ada perubahan jadwal

### 🔍 **Search Tips**
- Search berdasarkan judul training
- Search berdasarkan lokasi
- Search berdasarkan nama trainer
- Gunakan keyword singkat untuk hasil lebih luas

### 📅 **Calendar Management**
- Klik tanggal kosong untuk tambah training baru
- Klik event existing untuk edit/kelola
- Gunakan navigasi bulan untuk lihat periode lain
- Export kalender untuk backup/sharing

Sistem ini memberikan fleksibilitas maksimal untuk mengelola training dari berbagai sumber data dengan interface yang user-friendly!
