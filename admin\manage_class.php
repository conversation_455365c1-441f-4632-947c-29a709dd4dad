<?php
/**
 * Manage Class Page for Admin
 * This page allows admins to manage a specific training class
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include configuration file
include_once '../config/config.php';
include '../includes/functions.php';

// Check if user is logged in and has admin role or is an instructor for this class
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id'])) {
    header('Location: ../view/login.php');
    exit();
}

// Allow admin (role_id = 99) or check if user is instructor/assistant for this specific class
$user_id = $_SESSION['user_id'];
$is_admin = $_SESSION['role_id'] == 99;
$is_instructor = false;
$is_assistant = false;
$user_class_role = null; // Store the actual role in the class

// If not admin, check if user is instructor or assistant for this class
if (!$is_admin) {
    $class_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
    if ($class_id > 0) {
        // Check if user is instructor or assistant for this class
        $access_check = "SELECT role FROM training_participants
                        WHERE class_id = ? AND user_id = ? AND role IN ('instructor', 'assistant') AND status = 'active'";
        $stmt = $conn->prepare($access_check);
        $stmt->bind_param("ii", $class_id, $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        if ($row) {
            $user_class_role = $row['role']; // Store the role
            if ($row['role'] == 'instructor') {
                $is_instructor = true;
            } elseif ($row['role'] == 'assistant') {
                $is_assistant = true;
            }
        }
        $stmt->close();
    }

    // If not admin and not instructor/assistant, deny access
    if (!$is_instructor && !$is_assistant) {
        header('Location: ../view/login.php');
        exit();
    }
}

/**
 * Generate a unique class code
 *
 * @param mysqli $conn Database connection
 * @return string Unique class code
 */
function generateUniqueClassCode($conn) {
    $characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Excluding similar looking characters like I, 1, O, 0
    $code_length = 6;
    $max_attempts = 10;
    $attempt = 0;

    do {
        // Generate a random code
        $code = '';
        for ($i = 0; $i < $code_length; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }

        // Check if the code already exists
        $check_query = "SELECT id FROM training_classes WHERE class_code = ?";
        $stmt = $conn->prepare($check_query);
        $stmt->bind_param("s", $code);
        $stmt->execute();
        $result = $stmt->get_result();
        $exists = $result->num_rows > 0;
        $stmt->close();

        $attempt++;
    } while ($exists && $attempt < $max_attempts);

    return $code;
}

// Check if class ID is provided
$class_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($class_id <= 0) {
    header('Location: manage_classes.php');
    exit();
}

// Get class information
$class_query = "SELECT c.*, t.training_topic, t.training_type
               FROM training_classes c
               JOIN training_submissions t ON c.training_id = t.id
               WHERE c.id = ?";
$stmt = $conn->prepare($class_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
$class = $result->fetch_assoc();
$stmt->close();

if (!$class) {
    header('Location: manage_classes.php');
    exit();
}

// Handle class update
$success_message = '';
$error_message = '';

// Check for session messages
if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

if (isset($_SESSION['error_message'])) {
    $error_message = $_SESSION['error_message'];
    unset($_SESSION['error_message']);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_class'])) {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $start_date = !empty($_POST['start_date']) ? $_POST['start_date'] : null;
    $end_date = !empty($_POST['end_date']) ? $_POST['end_date'] : null;
    $status = $_POST['status'];
    $class_code = isset($_POST['class_code']) ? trim($_POST['class_code']) : null;

    // Validate input
    if (empty($title)) {
        $error_message = "Judul kelas harus diisi.";
    } else {
        // Validate class code if provided
        if (!empty($class_code)) {
            // First, check if class_code column exists
            $check_column_query = "SHOW COLUMNS FROM training_classes LIKE 'class_code'";
            $column_result = $conn->query($check_column_query);

            if ($column_result && $column_result->num_rows > 0) {
                // Column exists, check if the class code is already used by another class
                $check_code_query = "SELECT id FROM training_classes WHERE class_code = ? AND id != ?";
                $check_stmt = $conn->prepare($check_code_query);
                $check_stmt->bind_param("si", $class_code, $class_id);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();

                if ($check_result->num_rows > 0) {
                    $error_message = "Kode kelas sudah digunakan oleh kelas lain. Silakan gunakan kode yang berbeda.";
                    $check_stmt->close();
                    return;
                }
                $check_stmt->close();
            } else {
                // Column doesn't exist, run the script to add it
                $error_message = "Kolom class_code belum ada di database. Silakan jalankan script add_class_code_column.php terlebih dahulu.";
                return;
            }
        }

        // Check if class_code column exists
        $check_column_query = "SHOW COLUMNS FROM training_classes LIKE 'class_code'";
        $column_result = $conn->query($check_column_query);

        if ($column_result && $column_result->num_rows > 0) {
            // Column exists, include it in the update
            $update_query = "UPDATE training_classes SET
                            title = ?, description = ?, start_date = ?, end_date = ?, status = ?, class_code = ?
                            WHERE id = ?";

            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("ssssssi", $title, $description, $start_date, $end_date, $status, $class_code, $class_id);
        } else {
            // Column doesn't exist, exclude it from the update
            $update_query = "UPDATE training_classes SET
                            title = ?, description = ?, start_date = ?, end_date = ?, status = ?
                            WHERE id = ?";

            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("sssssi", $title, $description, $start_date, $end_date, $status, $class_id);
        }

        if ($stmt->execute()) {
            $success_message = "Kelas berhasil diperbarui.";

            // Refresh class data
            $stmt->close();
            $stmt = $conn->prepare($class_query);
            $stmt->bind_param("i", $class_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $class = $result->fetch_assoc();
        } else {
            $error_message = "Gagal memperbarui kelas: " . $conn->error;
        }
        $stmt->close();
    }
}

// Get participants
$participants = [];
$participants_query = "SELECT p.*, u.user_name as participant_name
                      FROM training_participants p
                      JOIN users u ON p.user_id = u.id
                      WHERE p.class_id = ?
                      ORDER BY p.joined_at DESC";

// First, let's check the structure of the users table to find the name column
$user_table_query = "DESCRIBE users";
$user_table_result = $conn->query($user_table_query);
$name_column = 'user_name'; // Default to user_name if we can't find a better column

if ($user_table_result) {
    while ($column = $user_table_result->fetch_assoc()) {
        // Look for common name column patterns
        if (in_array(strtolower($column['Field']), ['full_name', 'name', 'user_name', 'username', 'nama'])) {
            $name_column = $column['Field'];
            // Prefer full_name or name if available
            if (in_array(strtolower($column['Field']), ['full_name', 'name', 'nama'])) {
                break;
            }
        }
    }
}

$participants_query = "SELECT p.*, u.$name_column as participant_name
                      FROM training_participants p
                      JOIN users u ON p.user_id = u.id
                      WHERE p.class_id = ?
                      ORDER BY p.joined_at DESC";

$stmt = $conn->prepare($participants_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $participants[] = $row;
}
$stmt->close();

// Get materials
$materials = [];
$materials_query = "SELECT * FROM training_materials
                   WHERE class_id = ?
                   ORDER BY order_number ASC";
$stmt = $conn->prepare($materials_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $materials[] = $row;
}
$stmt->close();

// Get quizzes
$quizzes = [];
$quizzes_query = "SELECT q.*,
                 (SELECT COUNT(*) FROM training_questions WHERE quiz_id = q.id) as question_count,
                 (SELECT COUNT(*) FROM training_quiz_attempts WHERE quiz_id = q.id) as attempt_count
                 FROM training_quizzes q
                 WHERE q.class_id = ?
                 ORDER BY q.created_at DESC";
$stmt = $conn->prepare($quizzes_query);
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $quizzes[] = $row;
}
$stmt->close();

// Get assignments
$assignments = [];
$assignments_table_exists = false;

// Check if the training_assignments table exists
$check_assignments_table = $conn->query("SHOW TABLES LIKE 'training_assignments'");
if ($check_assignments_table && $check_assignments_table->num_rows > 0) {
    $assignments_table_exists = true;

    $assignments_query = "SELECT a.*,
                         (SELECT COUNT(*) FROM training_assignment_submissions WHERE assignment_id = a.id) as submission_count
                         FROM training_assignments a
                         WHERE a.class_id = ?
                         ORDER BY a.due_date ASC";
    $stmt = $conn->prepare($assignments_query);
    $stmt->bind_param("i", $class_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $assignments[] = $row;
    }
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<style>
    .class-header {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .class-title {
        font-size: 1.8rem;
        margin-bottom: 10px;
        color: #333;
    }

    .class-meta {
        color: #6c757d;
        margin-bottom: 15px;
    }

    .class-description {
        margin-bottom: 15px;
    }

    .tab-content {
        background-color: #fff;
        border-radius: 0 0 8px 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .nav-tabs .nav-link {
        font-weight: 500;
    }

    .nav-tabs .nav-link.active {
        background-color: #fff;
        border-bottom-color: #fff;
    }

    .participant-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .participant-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .participant-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: #6c757d;
    }

    .participant-name {
        font-weight: 600;
    }

    .participant-role {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .material-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .material-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .material-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .material-type {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .material-description {
        color: #333;
        margin-bottom: 10px;
    }

    .quiz-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .quiz-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .quiz-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .quiz-stats {
        font-size: 0.85rem;
        color: #6c757d;
        display: flex;
        gap: 15px;
    }

    .quiz-description {
        color: #333;
        margin-bottom: 10px;
    }

    .form-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .form-section h3 {
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    /* Assignment styles */
    .assignment-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .assignment-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .assignment-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .assignment-meta {
        font-size: 0.85rem;
        color: #6c757d;
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .assignment-description {
        color: #333;
        margin-bottom: 10px;
    }

    .assignment-status {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .assignment-status.active {
        background-color: #e8f5e9;
        color: #4CAF50;
    }

    .assignment-status.upcoming {
        background-color: #e3f2fd;
        color: #2196F3;
    }

    .assignment-status.overdue {
        background-color: #ffebee;
        color: #f44336;
    }
</style>

<body>
<?php include '../config/navbara.php'; ?>
<div class="jarak"></div>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="welcome-section">
                <h1><i class="fas fa-chalkboard-teacher"></i> Kelola Kelas</h1>
                <a href="manage_classes.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Daftar Kelas
                </a>
            </div>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="form-container">
                <div class="class-title"><?= htmlspecialchars($class['title']) ?></div>
                <div class="class-meta">
                    <div><strong>Topik:</strong> <?= htmlspecialchars($class['training_topic']) ?></div>
                    <div><strong>Tipe:</strong> <?= htmlspecialchars($class['training_type']) ?></div>
                    <?php if (!empty($class['start_date'])): ?>
                        <div><strong>Tanggal Mulai:</strong> <?= date('d M Y', strtotime($class['start_date'])) ?></div>
                    <?php endif; ?>
                    <?php if (!empty($class['end_date'])): ?>
                        <div><strong>Tanggal Selesai:</strong> <?= date('d M Y', strtotime($class['end_date'])) ?></div>
                    <?php endif; ?>
                    <div>
                        <strong>Status:</strong>
                        <span class="badge <?= $class['status'] == 'active' ? 'bg-success' : ($class['status'] == 'completed' ? 'bg-info' : 'bg-danger') ?>">
                            <?= $class['status'] == 'active' ? 'Aktif' : ($class['status'] == 'completed' ? 'Selesai' : 'Tidak Aktif') ?>
                        </span>
                    </div>
                    <?php
                    // Check if class_code column exists and has a value
                    $has_class_code = isset($class['class_code']) && !empty($class['class_code']);
                    if ($has_class_code):
                    ?>
                    <div class="mt-2">
                        <strong>Kode Kelas:</strong>
                        <span class="badge bg-primary" style="font-size: 1rem; letter-spacing: 2px;">
                            <?= htmlspecialchars($class['class_code']) ?>
                        </span>
                        <span class="ms-2 text-muted">
                            <i class="fas fa-info-circle"></i> Peserta dapat bergabung dengan kelas menggunakan kode ini
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="class-description">
                    <?= !empty($class['description']) ? nl2br(htmlspecialchars($class['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                </div>
                <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#editClassForm" aria-expanded="false" aria-controls="editClassForm">
                    <i class="fas fa-edit"></i> Edit Kelas
                </button>
            </div>

            <div class="collapse mb-4" id="editClassForm">
                <div class="form-section">
                    <h3>Edit Informasi Kelas</h3>

                    <?php if ($user_class_role == 'assistant'): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> <strong>Mode Asisten:</strong> Anda dapat melihat informasi kelas, tetapi tidak dapat mengedit detail kelas. Hanya instruktur yang dapat mengubah informasi dasar kelas.
                        </div>
                    <?php endif; ?>

                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="title" class="form-label">Judul Kelas</label>
                            <input type="text" class="form-control" id="title" name="title" value="<?= htmlspecialchars($class['title']) ?>" <?= $user_class_role == 'assistant' ? 'readonly' : 'required' ?>>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi Kelas</label>
                            <textarea class="form-control" id="description" name="description" rows="3" <?= $user_class_role == 'assistant' ? 'readonly' : '' ?>><?= htmlspecialchars($class['description']) ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">Tanggal Mulai</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $class['start_date'] ?>" <?= $user_class_role == 'assistant' ? 'readonly' : '' ?>>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">Tanggal Selesai</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $class['end_date'] ?>" <?= $user_class_role == 'assistant' ? 'readonly' : '' ?>>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status" <?= $user_class_role == 'assistant' ? 'disabled' : 'required' ?>>
                                <option value="active" <?= $class['status'] == 'active' ? 'selected' : '' ?>>Aktif</option>
                                <option value="inactive" <?= $class['status'] == 'inactive' ? 'selected' : '' ?>>Tidak Aktif</option>
                                <option value="completed" <?= $class['status'] == 'completed' ? 'selected' : '' ?>>Selesai</option>
                            </select>
                        </div>

                        <?php
                        // Check if class_code column exists
                        $check_column_query = "SHOW COLUMNS FROM training_classes LIKE 'class_code'";
                        $column_result = $conn->query($check_column_query);
                        $class_code_column_exists = ($column_result && $column_result->num_rows > 0);

                        if ($class_code_column_exists):
                        ?>
                        <div class="mb-3">
                            <label for="class_code" class="form-label">Kode Kelas</label>
                            <div class="input-group"><input type="text" class="form-control" id="class_code" name="class_code" value="<?= htmlspecialchars($class['class_code'] ?? '') ?>" maxlength="6">
                              <button class="btn btn-outline-secondary" type="button" id="generate_code_btn">
                                    <i class="fas fa-sync-alt"></i> Generate Kode Baru
                                </button>
                            </div>
                            <div class="form-text">
                                Kode kelas digunakan untuk memungkinkan peserta bergabung dengan kelas secara langsung.
                                <?php if (!empty($class['class_code'])): ?>
                                    <br>
                                    <span class="text-success">
                                        <i class="fas fa-info-circle"></i> Peserta dapat bergabung dengan kelas ini menggunakan kode: <strong><?= htmlspecialchars($class['class_code']) ?></strong>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> Fitur kode kelas belum tersedia. Silakan jalankan script <code>add_class_code_column.php</code> terlebih dahulu untuk mengaktifkan fitur ini.
                        </div>
                        <?php endif; ?>

                        <?php if ($user_class_role != 'assistant'): ?>
                        <button type="submit" name="update_class" class="btn btn-primary">
                            <i class="fas fa-save"></i> Perbarui Kelas
                        </button>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

            <ul class="nav nav-tabs" id="classTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="participants-tab" data-bs-toggle="tab" data-bs-target="#participants" type="button" role="tab" aria-controls="participants" aria-selected="true">
                        <i class="fas fa-users"></i> Peserta (<?= count($participants) ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials" type="button" role="tab" aria-controls="materials" aria-selected="false">
                        <i class="fas fa-book"></i> Materi (<?= count($materials) ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="assignments-tab" data-bs-toggle="tab" data-bs-target="#assignments" type="button" role="tab" aria-controls="assignments" aria-selected="false">
                        <i class="fas fa-tasks"></i> Tugas (<?= count($assignments) ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="quizzes-tab" data-bs-toggle="tab" data-bs-target="#quizzes" type="button" role="tab" aria-controls="quizzes" aria-selected="false">
                        <i class="fas fa-question-circle"></i> Kuis (<?= count($quizzes) ?>)
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="classTabsContent">
                <div class="tab-pane fade show active" id="participants" role="tabpanel" aria-labelledby="participants-tab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3 class="mb-0">Daftar Peserta</h3>
                        <a href="add_participant.php?class_id=<?= $class_id ?>" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> Tambah Peserta
                        </a>
                    </div>

                    <?php if (empty($participants)): ?>
                        <div class="alert alert-info">
                            Belum ada peserta dalam kelas ini. Tambahkan peserta menggunakan tombol di atas.
                        </div>
                    <?php else: ?>
                        <?php foreach ($participants as $participant): ?>
                            <div class="participant-card">
                                <div class="participant-info">
                                    <div class="participant-avatar">
                                        <?= strtoupper(substr($participant['participant_name'], 0, 1)) ?>
                                    </div>
                                    <div>
                                        <div class="participant-name"><?= htmlspecialchars($participant['participant_name']) ?></div>
                                        <div class="participant-role">
                                            <?php
                                                $role_labels = [
                                                    'student' => 'Peserta',
                                                    'instructor' => 'Instruktur',
                                                    'assistant' => 'Asisten'
                                                ];
                                                echo $role_labels[$participant['role']] ?? $participant['role'];
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <?php if ($user_class_role != 'assistant'): ?>
                                    <a href="edit_participant.php?id=<?= $participant['id'] ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="javascript:void(0);" class="btn btn-sm btn-danger remove-participant-btn" data-participant-id="<?= $participant['id'] ?>" data-class-id="<?= $class_id ?>">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <?php else: ?>
                                    <span class="text-muted small">
                                        <i class="fas fa-eye"></i> View Only
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="tab-pane fade" id="materials" role="tabpanel" aria-labelledby="materials-tab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3 class="mb-0">Materi Pembelajaran</h3>
                        <a href="add_material.php?class_id=<?= $class_id ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Tambah Materi
                        </a>
                    </div>

                    <?php if (empty($materials)): ?>
                        <div class="alert alert-info">
                            Belum ada materi dalam kelas ini. Tambahkan materi menggunakan tombol di atas.
                        </div>
                    <?php else: ?>
                        <?php foreach ($materials as $material): ?>
                            <div class="material-card">
                                <div class="material-header">
                                    <div>
                                        <div class="material-title"><?= htmlspecialchars($material['title']) ?></div>
                                        <div class="material-type">
                                            <?php
                                                $type_labels = [
                                                    'document' => '<i class="fas fa-file-alt"></i> Dokumen',
                                                    'video' => '<i class="fas fa-video"></i> Video',
                                                    'link' => '<i class="fas fa-link"></i> Tautan',
                                                    'other' => '<i class="fas fa-file"></i> Lainnya'
                                                ];
                                                echo $type_labels[$material['type']] ?? $material['type'];
                                            ?>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="badge <?= $material['is_published'] ? 'bg-success' : 'bg-secondary' ?>">
                                            <?= $material['is_published'] ? 'Dipublikasikan' : 'Draft' ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="material-description">
                                    <?= !empty($material['description']) ? nl2br(htmlspecialchars($material['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                                </div>
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="edit_material.php?id=<?= $material['id'] ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <?php if ($user_class_role != 'assistant'): ?>
                                    <a href="javascript:void(0);" class="btn btn-sm btn-danger remove-material-btn" data-material-id="<?= $material['id'] ?>" data-class-id="<?= $class_id ?>">
                                        <i class="fas fa-trash"></i> Hapus
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="tab-pane fade" id="assignments" role="tabpanel" aria-labelledby="assignments-tab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3 class="mb-0">Tugas</h3>
                        <a href="create_assignment.php?class_id=<?= $class_id ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Tambah Tugas
                        </a>
                    </div>

                    <?php if (!$assignments_table_exists): ?>
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle"></i> Fitur Tugas Belum Tersedia</h5>
                            <p>Tabel database untuk fitur tugas belum dibuat. Silakan hubungi administrator sistem untuk mengaktifkan fitur ini.</p>
                        </div>
                    <?php elseif (empty($assignments)): ?>
                        <div class="alert alert-info">
                            <p>Belum ada tugas dalam kelas ini. Tambahkan tugas menggunakan tombol di atas.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($assignments as $assignment): ?>
                            <?php
                                // Determine assignment status
                                $now = new DateTime();
                                $due_date = new DateTime($assignment['due_date']);
                                $status_class = 'upcoming';
                                $status_text = 'Akan Datang';

                                if ($now > $due_date) {
                                    $status_class = 'overdue';
                                    $status_text = 'Tenggat Terlewat';
                                } elseif ($now->diff($due_date)->days <= 3) {
                                    $status_class = 'active';
                                    $status_text = 'Aktif';
                                }
                            ?>
                            <div class="assignment-card">
                                <div class="assignment-header">
                                    <div>
                                        <div class="assignment-title"><?= htmlspecialchars($assignment['title']) ?></div>
                                        <div class="assignment-meta">
                                            <span><i class="fas fa-calendar-alt"></i> Tenggat: <?= date('d M Y H:i', strtotime($assignment['due_date'])) ?></span>
                                            <?php if ($assignment['points']): ?>
                                                <span><i class="fas fa-star"></i> Poin: <?= $assignment['points'] ?></span>
                                            <?php endif; ?>
                                            <span><i class="fas fa-users"></i> <?= $assignment['submission_count'] ?> pengumpulan</span>
                                            <span class="assignment-status <?= $status_class ?>"><?= $status_text ?></span>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="badge <?= $assignment['is_published'] ? 'bg-success' : 'bg-secondary' ?>">
                                            <?= $assignment['is_published'] ? 'Dipublikasikan' : 'Draft' ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="assignment-description">
                                    <?= !empty($assignment['description']) ? nl2br(htmlspecialchars($assignment['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                                </div>
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="edit_assignment.php?id=<?= $assignment['id'] ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="assignment_submissions.php?id=<?= $assignment['id'] ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-clipboard-check"></i> Pengumpulan
                                    </a>
                                    <?php if ($user_class_role != 'assistant'): ?>
                                    <a href="javascript:void(0);" class="btn btn-sm btn-danger remove-assignment-btn" data-assignment-id="<?= $assignment['id'] ?>" data-class-id="<?= $class_id ?>">
                                        <i class="fas fa-trash"></i> Hapus
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="tab-pane fade" id="quizzes" role="tabpanel" aria-labelledby="quizzes-tab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3 class="mb-0">Kuis</h3>
                        <a href="create_quiz.php?class_id=<?= $class_id ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Tambah Kuis
                        </a>
                    </div>

                    <?php if (empty($quizzes)): ?>
                        <div class="alert alert-info">
                            Belum ada kuis dalam kelas ini. Tambahkan kuis menggunakan tombol di atas.
                        </div>
                    <?php else: ?>
                        <?php foreach ($quizzes as $quiz): ?>
                            <div class="quiz-card">
                                <div class="quiz-header">
                                    <div>
                                        <div class="quiz-title"><?= htmlspecialchars($quiz['title']) ?></div>
                                        <div class="quiz-stats">
                                            <span><i class="fas fa-question"></i> <?= $quiz['question_count'] ?> Pertanyaan</span>
                                            <span><i class="fas fa-users"></i> <?= $quiz['attempt_count'] ?> Percobaan</span>
                                            <?php if ($quiz['time_limit']): ?>
                                                <span><i class="fas fa-clock"></i> <?= $quiz['time_limit'] ?> menit</span>
                                            <?php endif; ?>
                                            <?php if ($quiz['passing_score']): ?>
                                                <span><i class="fas fa-check-circle"></i> Nilai kelulusan: <?= $quiz['passing_score'] ?>%</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="badge <?= $quiz['is_published'] ? 'bg-success' : 'bg-secondary' ?>">
                                            <?= $quiz['is_published'] ? 'Dipublikasikan' : 'Draft' ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="quiz-description">
                                    <?= !empty($quiz['description']) ? nl2br(htmlspecialchars($quiz['description'])) : '<em>Tidak ada deskripsi</em>' ?>
                                </div>
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="edit_quiz.php?id=<?= $quiz['id'] ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="quiz_results.php?id=<?= $quiz['id'] ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-chart-bar"></i> Hasil
                                    </a>
                                    <?php if ($user_class_role != 'assistant'): ?>
                                    <a href="javascript:void(0);" class="btn btn-sm btn-danger remove-quiz-btn" data-quiz-id="<?= $quiz['id'] ?>" data-class-id="<?= $class_id ?>">
                                        <i class="fas fa-trash"></i> Hapus
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../config/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 100000);

        // Generate class code - only if the elements exist
        const generateCodeBtn = document.getElementById('generate_code_btn');
        const classCodeInput = document.getElementById('class_code');

        if (generateCodeBtn && classCodeInput) {
            generateCodeBtn.addEventListener('click', function() {
                // Generate a random 6-character code
                const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Excluding similar looking characters
                let code = '';

                for (let i = 0; i < 6; i++) {
                    code += characters.charAt(Math.floor(Math.random() * characters.length));
                }

                classCodeInput.value = code;

                // Add visual feedback
                classCodeInput.classList.add('is-valid');
                setTimeout(() => {
                    classCodeInput.classList.remove('is-valid');
                }, 2000);
            });

            // Auto uppercase class code input
            classCodeInput.addEventListener('input', function() {
                this.value = this.value.toUpperCase();
            });
        }

        // Date validation
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');

        if (startDateInput && endDateInput) {
            endDateInput.addEventListener('change', function() {
                if (startDateInput.value && endDateInput.value) {
                    if (new Date(endDateInput.value) < new Date(startDateInput.value)) {
                        alert('Tanggal selesai tidak boleh lebih awal dari tanggal mulai');
                        endDateInput.value = '';
                    }
                }
            });

            startDateInput.addEventListener('change', function() {
                if (startDateInput.value && endDateInput.value) {
                    if (new Date(endDateInput.value) < new Date(startDateInput.value)) {
                        alert('Tanggal mulai tidak boleh lebih akhir dari tanggal selesai');
                        startDateInput.value = '';
                    }
                }
            });
        }

        // Handle remove participant buttons
        const removeParticipantButtons = document.querySelectorAll('.remove-participant-btn');
        removeParticipantButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const participantId = this.getAttribute('data-participant-id');
                const classId = this.getAttribute('data-class-id');

                // Use confirmAction for confirmation
                confirmAction(
                    'Apakah Anda yakin ingin menghapus peserta ini dari kelas?',
                    function() {
                        // Redirect to remove_participant.php when confirmed
                        window.location.href = 'remove_participant.php?id=' + participantId + '&class_id=' + classId;
                    }
                );
            });
        });

        // Handle remove material buttons
        const removeMaterialButtons = document.querySelectorAll('.remove-material-btn');
        removeMaterialButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const materialId = this.getAttribute('data-material-id');
                const classId = this.getAttribute('data-class-id');

                // Use confirmAction for confirmation
                confirmAction(
                    'Apakah Anda yakin ingin menghapus materi ini?',
                    function() {
                        // Redirect to remove_material.php when confirmed
                        window.location.href = 'remove_material.php?id=' + materialId + '&class_id=' + classId;
                    }
                );
            });
        });

        // Handle remove quiz buttons
        const removeQuizButtons = document.querySelectorAll('.remove-quiz-btn');
        removeQuizButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const quizId = this.getAttribute('data-quiz-id');
                const classId = this.getAttribute('data-class-id');

                // Use confirmAction for confirmation
                confirmAction(
                    'Apakah Anda yakin ingin menghapus kuis ini?',
                    function() {
                        // Redirect to delete_quiz.php when confirmed
                        window.location.href = 'delete_quiz.php?id=' + quizId + '&class_id=' + classId;
                    }
                );
            });
        });

        // Handle remove assignment buttons
        const removeAssignmentButtons = document.querySelectorAll('.remove-assignment-btn');
        removeAssignmentButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const assignmentId = this.getAttribute('data-assignment-id');
                const classId = this.getAttribute('data-class-id');

                // Use confirmAction for confirmation
                confirmAction(
                    'Apakah Anda yakin ingin menghapus tugas ini?',
                    function() {
                        // Redirect to delete_assignment.php when confirmed
                        window.location.href = 'delete_assignment.php?id=' + assignmentId + '&class_id=' + classId;
                    }
                );
            });
        });
    });
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
