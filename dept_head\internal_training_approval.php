<?php
include '../config/config.php';
include '../config/access_control.php';
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../view/login.php");
    exit();
}

// Check if user has dept head access (level 4+ or supervisor/chief)
$user_id = $_SESSION['user_id'];
$user_data = checkUserLevel($user_id, 4, ['Supervisor', 'Chief'], '../index.php');

// Get user department for filtering
$user_dept = $user_data['dept'];

// Handle approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $training_id = (int)$_POST['training_id'];
    $action = $_POST['action'];
    $comments = trim($_POST['comments'] ?? '');
    
    if ($action === 'approve') {
        // Approve by dept head, move to L&D
        $query = "UPDATE offline_training 
                  SET approved_dept_head = 'Approved',
                      comments_dept_head = ?,
                      approved_by_dept_head = ?,
                      approved_at_dept_head = NOW(),
                      current_approver = 'lnd',
                      status = 'Pending L&D Approval'
                  WHERE id = ? AND current_approver = 'dept_head'";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param("sii", $comments, $user_id, $training_id);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = "Training internal berhasil disetujui dan diteruskan ke L&D.";
        } else {
            $_SESSION['error'] = "Gagal menyetujui training internal.";
        }
        
    } elseif ($action === 'reject') {
        // Reject by dept head
        $query = "UPDATE offline_training 
                  SET approved_dept_head = 'Rejected',
                      comments_dept_head = ?,
                      approved_by_dept_head = ?,
                      approved_at_dept_head = NOW(),
                      current_approver = 'completed',
                      status = 'Rejected by Dept Head'
                  WHERE id = ? AND current_approver = 'dept_head'";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param("sii", $comments, $user_id, $training_id);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = "Training internal telah ditolak.";
        } else {
            $_SESSION['error'] = "Gagal menolak training internal.";
        }
    }
    
    header("Location: internal_training_approval.php");
    exit();
}

// Get pending training requests for this department
$query = "SELECT ot.*, 
                 u.name as requester_name,
                 k.nama as requester_nama,
                 k.dept as requester_dept,
                 k.bagian as requester_bagian,
                 k.jabatan as requester_jabatan
          FROM offline_training ot
          LEFT JOIN users u ON ot.created_by = u.id
          LEFT JOIN karyawan k ON u.nik = k.nik
          WHERE ot.current_approver = 'dept_head'
          AND k.dept = ?
          ORDER BY ot.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->bind_param("s", $user_dept);
$stmt->execute();
$result = $stmt->get_result();
$pending_trainings = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();
?>

<!DOCTYPE html>
<html lang="en">
<?php include '../config/head.php'; ?>
<style>
    .approval-container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
    }

    .approval-header {
        background: linear-gradient(135deg, #c40000 0%, #a50000 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 30px;
    }

    .training-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
        border-left: 5px solid #c40000;
    }

    .training-header {
        background: #f8f9fa;
        padding: 20px;
        border-bottom: 1px solid #eee;
    }

    .training-content {
        padding: 20px;
    }

    .training-actions {
        padding: 20px;
        background: #f8f9fa;
        border-top: 1px solid #eee;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        margin-right: 10px;
        transition: all 0.3s ease;
    }

    .btn-approve {
        background: #28a745;
        color: white;
    }

    .btn-reject {
        background: #dc3545;
        color: white;
    }

    .btn-approve:hover {
        background: #218838;
        transform: translateY(-2px);
    }

    .btn-reject:hover {
        background: #c82333;
        transform: translateY(-2px);
    }

    .detail-row {
        display: flex;
        margin-bottom: 10px;
    }

    .detail-label {
        font-weight: 600;
        min-width: 150px;
        color: #555;
    }

    .detail-value {
        flex: 1;
        color: #333;
    }

    .status-badge {
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .status-pending {
        background: #fff3cd;
        color: #856404;
    }

    .comments-box {
        width: 100%;
        padding: 10px;
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        margin-bottom: 15px;
        resize: vertical;
        min-height: 80px;
    }

    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .alert-success {
        background: #d4edda;
        color: #155724;
        border-left: 4px solid #28a745;
    }

    .alert-error {
        background: #f8d7da;
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    .no-data {
        text-align: center;
        padding: 50px;
        color: #6c757d;
    }

    @media (max-width: 768px) {
        .approval-container {
            margin: 10px;
            padding: 10px;
        }
        
        .detail-row {
            flex-direction: column;
        }
        
        .detail-label {
            min-width: auto;
            margin-bottom: 5px;
        }
    }
</style>

<body>
    <?php include '../config/navbara.php'; ?>
    <div class="jarak"></div>

    <div class="approval-container">
        <div class="approval-header">
            <h1><i class="fas fa-clipboard-check"></i> Approval Training Internal - Department Head</h1>
            <p>Review dan setujui pengajuan training internal dari departemen <?= htmlspecialchars($user_dept) ?></p>
        </div>

        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($_SESSION['success']) ?>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($_SESSION['error']) ?>
            </div>
            <?php unset($_SESSION['error']); ?>
        <?php endif; ?>

        <?php if (empty($pending_trainings)): ?>
            <div class="no-data">
                <i class="fas fa-inbox" style="font-size: 4rem; color: #ccc; margin-bottom: 20px;"></i>
                <h3>Tidak ada pengajuan training internal yang menunggu approval</h3>
                <p>Semua pengajuan training internal dari departemen <?= htmlspecialchars($user_dept) ?> sudah diproses.</p>
            </div>
        <?php else: ?>
            <?php foreach ($pending_trainings as $training): ?>
                <div class="training-card">
                    <div class="training-header">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <h3 style="margin: 0; color: #333;">
                                <i class="fas fa-building"></i> <?= htmlspecialchars($training['training_topic']) ?>
                            </h3>
                            <span class="status-badge status-pending">
                                Menunggu Approval Dept Head
                            </span>
                        </div>
                    </div>

                    <div class="training-content">
                        <div class="row" style="display: flex; gap: 30px;">
                            <div style="flex: 1;">
                                <h4>Informasi Pemohon</h4>
                                <div class="detail-row">
                                    <div class="detail-label">Nama:</div>
                                    <div class="detail-value"><?= htmlspecialchars($training['requester_nama'] ?? $training['requester_name']) ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Departemen:</div>
                                    <div class="detail-value"><?= htmlspecialchars($training['requester_dept']) ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Bagian:</div>
                                    <div class="detail-value"><?= htmlspecialchars($training['requester_bagian']) ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Jabatan:</div>
                                    <div class="detail-value"><?= htmlspecialchars($training['requester_jabatan']) ?></div>
                                </div>
                            </div>

                            <div style="flex: 1;">
                                <h4>Detail Training</h4>
                                <div class="detail-row">
                                    <div class="detail-label">Tanggal Mulai:</div>
                                    <div class="detail-value">
                                        <?= date('d F Y', strtotime($training['start_date'])) ?>
                                        <?php if (!empty($training['end_date']) && $training['end_date'] !== $training['start_date']): ?>
                                            - <?= date('d F Y', strtotime($training['end_date'])) ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php if (!empty($training['training_time_start'])): ?>
                                <div class="detail-row">
                                    <div class="detail-label">Waktu:</div>
                                    <div class="detail-value">
                                        <?= date('H:i', strtotime($training['training_time_start'])) ?>
                                        <?php if (!empty($training['training_time_end'])): ?>
                                            - <?= date('H:i', strtotime($training['training_time_end'])) ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($training['location'])): ?>
                                <div class="detail-row">
                                    <div class="detail-label">Lokasi:</div>
                                    <div class="detail-value"><?= htmlspecialchars($training['location']) ?></div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($training['max_participants'])): ?>
                                <div class="detail-row">
                                    <div class="detail-label">Est. Peserta:</div>
                                    <div class="detail-value"><?= htmlspecialchars($training['max_participants']) ?> orang</div>
                                </div>
                                <?php endif; ?>
                                <div class="detail-row">
                                    <div class="detail-label">Diajukan:</div>
                                    <div class="detail-value"><?= date('d F Y H:i', strtotime($training['created_at'])) ?></div>
                                </div>
                            </div>
                        </div>

                        <div style="margin-top: 20px;">
                            <h4>Deskripsi Training</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; white-space: pre-line;">
                                <?= htmlspecialchars($training['training_description']) ?>
                            </div>
                        </div>
                    </div>

                    <div class="training-actions">
                        <form method="POST" style="display: inline-block;">
                            <input type="hidden" name="training_id" value="<?= $training['id'] ?>">
                            <textarea name="comments" class="comments-box" placeholder="Komentar approval (opsional)"></textarea>
                            <button type="submit" name="action" value="approve" class="btn btn-approve">
                                <i class="fas fa-check"></i> Setujui
                            </button>
                            <button type="submit" name="action" value="reject" class="btn btn-reject" 
                                    onclick="return confirmAction('Apakah Anda yakin ingin menolak pengajuan training ini?')">
                                <i class="fas fa-times"></i> Tolak
                            </button>
                        </form>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <footer>
        <?php include '../config/footer.php'; ?>
    </footer>
</body>
</html>
