<?php
/**
 * Create Quiz Tables
 * This script creates the necessary tables for the quiz feature
 */

// Include configuration file
include_once 'config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role_id']) || $_SESSION['role_id'] != 99) {
    echo "Unauthorized access";
    exit();
}

// Function to create tables
function createQuizTables($conn) {
    $success = true;
    $messages = [];

    // Create training_quizzes table
    $query = "CREATE TABLE IF NOT EXISTS training_quizzes (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        class_id INT(11) NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NULL,
        instructions TEXT NULL,
        time_limit INT(11) NULL COMMENT 'Time limit in minutes',
        passing_score INT(11) NULL COMMENT 'Minimum score to pass (percentage)',
        is_published TINYINT(1) NOT NULL DEFAULT 0,
        allow_multiple_attempts TINYINT(1) NOT NULL DEFAULT 0,
        max_attempts INT(11) NULL COMMENT 'Maximum attempts allowed (null for unlimited)',
        randomize_questions TINYINT(1) NOT NULL DEFAULT 0,
        show_correct_answers TINYINT(1) NOT NULL DEFAULT 0,
        hide_all_answers TINYINT(1) NOT NULL DEFAULT 0,
        created_by INT(11) NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES training_classes(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (!$conn->query($query)) {
        $success = false;
        $messages[] = "Error creating training_quizzes table: " . $conn->error;
    } else {
        $messages[] = "Training quizzes table created successfully";
    }

    // Create training_questions table
    $query = "CREATE TABLE IF NOT EXISTS training_questions (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        quiz_id INT(11) NOT NULL,
        question_text TEXT NOT NULL,
        question_type ENUM('multiple_choice', 'true_false', 'short_answer', 'essay') NOT NULL DEFAULT 'multiple_choice',
        points INT(11) NOT NULL DEFAULT 1,
        order_number INT(11) NOT NULL DEFAULT 0,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (quiz_id) REFERENCES training_quizzes(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (!$conn->query($query)) {
        $success = false;
        $messages[] = "Error creating training_questions table: " . $conn->error;
    } else {
        $messages[] = "Training questions table created successfully";
    }

    // Create training_question_options table
    $query = "CREATE TABLE IF NOT EXISTS training_question_options (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        question_id INT(11) NOT NULL,
        option_text TEXT NOT NULL,
        is_correct TINYINT(1) NOT NULL DEFAULT 0,
        order_number INT(11) NOT NULL DEFAULT 0,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (question_id) REFERENCES training_questions(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (!$conn->query($query)) {
        $success = false;
        $messages[] = "Error creating training_question_options table: " . $conn->error;
    } else {
        $messages[] = "Training question options table created successfully";
    }

    // Create training_quiz_attempts table
    $query = "CREATE TABLE IF NOT EXISTS training_quiz_attempts (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        quiz_id INT(11) NOT NULL,
        user_id INT(11) NOT NULL,
        start_time DATETIME NOT NULL,
        end_time DATETIME NULL,
        score INT(11) NULL COMMENT 'Score in percentage',
        status ENUM('in_progress', 'completed', 'graded') NOT NULL DEFAULT 'in_progress',
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (quiz_id) REFERENCES training_quizzes(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (!$conn->query($query)) {
        $success = false;
        $messages[] = "Error creating training_quiz_attempts table: " . $conn->error;
    } else {
        $messages[] = "Training quiz attempts table created successfully";
    }

    // Create training_quiz_answers table
    $query = "CREATE TABLE IF NOT EXISTS training_quiz_answers (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        attempt_id INT(11) NOT NULL,
        question_id INT(11) NOT NULL,
        selected_option_id INT(11) NULL,
        text_answer TEXT NULL,
        is_correct TINYINT(1) NULL,
        points_earned INT(11) NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (attempt_id) REFERENCES training_quiz_attempts(id) ON DELETE CASCADE,
        FOREIGN KEY (question_id) REFERENCES training_questions(id) ON DELETE CASCADE,
        FOREIGN KEY (selected_option_id) REFERENCES training_question_options(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (!$conn->query($query)) {
        $success = false;
        $messages[] = "Error creating training_quiz_answers table: " . $conn->error;
    } else {
        $messages[] = "Training quiz answers table created successfully";
    }

    return ['success' => $success, 'messages' => $messages];
}

// Create tables
$result = createQuizTables($conn);

// Display results
echo "<h1>Create Quiz Tables</h1>";
echo "<ul>";
foreach ($result['messages'] as $message) {
    echo "<li>" . $message . "</li>";
}
echo "</ul>";

if ($result['success']) {
    echo "<p>All quiz tables created successfully. <a href='../admin/dashboard.php'>Go to Dashboard</a></p>";
} else {
    echo "<p>There were errors creating some tables. Please check the messages above.</p>";
}

// Close database connection
$conn->close();
?>
