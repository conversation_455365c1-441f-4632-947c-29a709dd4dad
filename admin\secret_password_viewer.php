<?php
/**
 * Secret Password Viewer
 * URL khusus untuk developer melihat password user
 * 
 * URL Format: secret_password_viewer.php?token=DEVELOPER_SECRET_TOKEN&user_id=123
 * atau: secret_password_viewer.php?token=DEVELOPER_SECRET_TOKEN (untuk melihat semua)
 */

session_start();
include '../config/config.php';
include '../config/developer_config.php';

// Validasi akses developer
$token = $_GET['token'] ?? '';
$validation = validateDeveloperAccess($token);

if (!$validation['valid']) {
    http_response_code(403);
    die('Access Denied: ' . $validation['message']);
}

// Log aktivitas akses
logDeveloperActivity('SECRET_PASSWORD_VIEWER_ACCESS', [
    'requested_user_id' => $_GET['user_id'] ?? 'all',
    'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
    'referer' => $_SERVER['HTTP_REFERER'] ?? ''
]);

// Get user ID jika spesifik
$user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : null;
$search = $_GET['search'] ?? '';

// Build query
if ($user_id) {
    $query = "SELECT u.id, u.name, u.nik, u.email, u.password, u.dept, u.bagian, u.jabatan, u.is_active, r.role_name 
              FROM users u 
              LEFT JOIN roles r ON u.role_id = r.id 
              WHERE u.id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
} else {
    // Query untuk semua user dengan search
    $where_clause = "1=1";
    $params = [];
    $param_types = "";
    
    if (!empty($search)) {
        $where_clause = "(u.name LIKE ? OR u.nik LIKE ? OR u.email LIKE ? OR u.dept LIKE ?)";
        $search_param = "%$search%";
        $params = [$search_param, $search_param, $search_param, $search_param];
        $param_types = "ssss";
    }
    
    $query = "SELECT u.id, u.name, u.nik, u.email, u.password, u.dept, u.bagian, u.jabatan, u.is_active, r.role_name 
              FROM users u 
              LEFT JOIN roles r ON u.role_id = r.id 
              WHERE $where_clause 
              ORDER BY u.is_active DESC, u.name ASC 
              LIMIT 50";
    
    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
}

$stmt->execute();
$result = $stmt->get_result();
$users = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Analyze password statistics
$password_stats = [
    'total' => count($users),
    'default_count' => 0,
    'weak_count' => 0,
    'secure_count' => 0
];

foreach ($users as &$user) {
    $pwd_info = getPasswordInfo($user['password']);
    $user['password_info'] = $pwd_info;

    if ($pwd_info['is_default']) {
        $password_stats['default_count']++;
    } elseif ($pwd_info['possible_password']) {
        $password_stats['weak_count']++;
    } else {
        $password_stats['secure_count']++;
    }
}

// Function to analyze password and provide useful information
function getPasswordInfo($hashed_password) {
    $info = [
        'hash' => $hashed_password,
        'algorithm' => 'bcrypt',
        'is_default' => false,
        'possible_password' => null,
        'security_level' => 'unknown',
        'note' => 'Password di-hash dengan bcrypt - tidak bisa di-decrypt'
    ];

    // Check if it's the default password "asdf"
    if (password_verify('asdf', $hashed_password)) {
        $info['is_default'] = true;
        $info['possible_password'] = 'asdf';
        $info['security_level'] = 'very_low';
        $info['note'] = '⚠️ PASSWORD DEFAULT TERDETEKSI: "asdf"';
    }

    // Check for other common passwords
    $common_passwords = ['password', '123456', 'admin', 'test', '12345678', 'qwerty'];
    foreach ($common_passwords as $common_pwd) {
        if (password_verify($common_pwd, $hashed_password)) {
            $info['possible_password'] = $common_pwd;
            $info['security_level'] = 'very_low';
            $info['note'] = "⚠️ PASSWORD LEMAH TERDETEKSI: \"$common_pwd\"";
            break;
        }
    }

    // Analyze hash pattern for additional info
    if (strpos($hashed_password, '$2y$') === 0) {
        $info['algorithm'] = 'bcrypt (PHP PASSWORD_DEFAULT)';
    } elseif (strpos($hashed_password, '$2a$') === 0) {
        $info['algorithm'] = 'bcrypt (legacy)';
    }

    return $info;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Secret Password Viewer - Developer Only</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .warning-banner {
            background: #ff4757;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            border-left: 5px solid #ff3742;
        }
        
        .search-section {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .search-form {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .search-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .content {
            padding: 20px;
        }
        
        .user-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .user-header {
            background: #343a40;
            color: white;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-info {
            padding: 15px;
        }
        
        .info-row {
            display: grid;
            grid-template-columns: 150px 1fr;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .password-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }

        .password-section.default {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }

        .password-section.weak {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }

        .password-section.secure {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        
        .password-hash {
            font-family: 'Courier New', monospace;
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            word-break: break-all;
            font-size: 12px;
            border: 1px solid #ddd;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .copy-btn:hover {
            background: #218838;
        }
        
        .footer {
            background: #343a40;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 12px;
        }
        
        .back-btn {
            background: #6c757d;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .back-btn:hover {
            background: #5a6268;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-key"></i> Secret Password Viewer</h1>
            <p>🚨 Developer Only - Confidential Access 🚨</p>
        </div>
        
        <div class="warning-banner">
            ⚠️ WARNING: Anda sedang mengakses informasi sensitif. Semua aktivitas dicatat dan dimonitor. ⚠️
        </div>
        
        <div class="search-section">
            <form method="GET" class="search-form">
                <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
                <input type="text" name="search" class="search-input" 
                       placeholder="Cari user (nama, NIK, email, dept)..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i> Search
                </button>
            </form>
        </div>
        
        <div class="content">
            <a href="manage_user.php" class="back-btn">
                <i class="fas fa-arrow-left"></i> Kembali ke Manage Users
            </a>
            
            <?php if (empty($users)): ?>
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <i class="fas fa-users" style="font-size: 48px; margin-bottom: 20px;"></i>
                    <h3>Tidak ada user ditemukan</h3>
                    <p>Coba ubah kriteria pencarian Anda.</p>
                </div>
            <?php else: ?>
                <!-- Password Statistics -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 25px;">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;">
                        <h4 style="margin: 0; color: #007bff;">📊 Total Users</h4>
                        <p style="margin: 5px 0 0; font-size: 24px; font-weight: bold;"><?php echo $password_stats['total']; ?></p>
                    </div>
                    <div style="background: #f8d7da; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;">
                        <h4 style="margin: 0; color: #dc3545;">⚠️ Default Password</h4>
                        <p style="margin: 5px 0 0; font-size: 24px; font-weight: bold;"><?php echo $password_stats['default_count']; ?></p>
                    </div>
                    <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #fd7e14;">
                        <h4 style="margin: 0; color: #fd7e14;">🔓 Weak Password</h4>
                        <p style="margin: 5px 0 0; font-size: 24px; font-weight: bold;"><?php echo $password_stats['weak_count']; ?></p>
                    </div>
                    <div style="background: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                        <h4 style="margin: 0; color: #28a745;">🔒 Secure Password</h4>
                        <p style="margin: 5px 0 0; font-size: 24px; font-weight: bold;"><?php echo $password_stats['secure_count']; ?></p>
                    </div>
                </div>

                <div style="margin-bottom: 20px; color: #6c757d;">
                    <i class="fas fa-info-circle"></i>
                    Menampilkan <?php echo count($users); ?> user(s)
                    <?php if ($password_stats['default_count'] > 0): ?>
                        <span style="color: #dc3545; font-weight: bold;">
                            | ⚠️ <?php echo $password_stats['default_count']; ?> user dengan password default!
                        </span>
                    <?php endif; ?>
                </div>
                
                <?php foreach ($users as $user): ?>
                    <div class="user-card">
                        <div class="user-header">
                            <div>
                                <h3><?php echo htmlspecialchars($user['name']); ?></h3>
                                <small>ID: <?php echo $user['id']; ?> | NIK: <?php echo htmlspecialchars($user['nik']); ?></small>
                            </div>
                            <div>
                                <span class="status-badge <?php echo $user['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo $user['is_active'] ? 'ACTIVE' : 'INACTIVE'; ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="user-info">
                            <div class="info-row">
                                <div class="info-label">Email:</div>
                                <div class="info-value"><?php echo htmlspecialchars($user['email']); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Role:</div>
                                <div class="info-value"><?php echo htmlspecialchars($user['role_name']); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Department:</div>
                                <div class="info-value"><?php echo htmlspecialchars($user['dept']); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Sub Dept:</div>
                                <div class="info-value"><?php echo htmlspecialchars($user['bagian']); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Jabatan:</div>
                                <div class="info-value"><?php echo htmlspecialchars($user['jabatan']); ?></div>
                            </div>
                            
                            <?php $pwd_info = $user['password_info']; ?>
                            <div class="password-section <?php echo $pwd_info['is_default'] ? 'default' : ($pwd_info['security_level'] === 'very_low' ? 'weak' : 'secure'); ?>">
                                <h4>
                                    <i class="fas fa-lock"></i> Password Information
                                    <?php if ($pwd_info['is_default']): ?>
                                        <span style="color: #dc3545; font-size: 14px;">⚠️ DEFAULT PASSWORD</span>
                                    <?php elseif ($pwd_info['possible_password']): ?>
                                        <span style="color: #fd7e14; font-size: 14px;">⚠️ WEAK PASSWORD</span>
                                    <?php endif; ?>
                                </h4>

                                <?php if ($pwd_info['possible_password']): ?>
                                    <div style="background: #fff; padding: 10px; border-radius: 4px; margin-bottom: 10px; border-left: 4px solid #dc3545;">
                                        <strong style="color: #dc3545;">🔓 Readable Password:</strong>
                                        <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-weight: bold;">
                                            <?php echo htmlspecialchars($pwd_info['possible_password']); ?>
                                        </code>
                                        <button class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars($pwd_info['possible_password']); ?>')">
                                            <i class="fas fa-copy"></i> Copy Password
                                        </button>
                                    </div>
                                <?php endif; ?>

                                <p><strong>Algorithm:</strong> <?php echo $pwd_info['algorithm']; ?></p>
                                <p><strong>Security Level:</strong>
                                    <span style="color: <?php echo $pwd_info['security_level'] === 'very_low' ? '#dc3545' : '#28a745'; ?>">
                                        <?php echo strtoupper(str_replace('_', ' ', $pwd_info['security_level'])); ?>
                                    </span>
                                </p>
                                <p><strong>Note:</strong> <?php echo $pwd_info['note']; ?></p>

                                <details style="margin-top: 10px;">
                                    <summary style="cursor: pointer; font-weight: bold;">🔍 Show Hash Details</summary>
                                    <div class="password-hash" style="margin-top: 10px;">
                                        <?php echo htmlspecialchars($pwd_info['hash']); ?>
                                        <button class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars($pwd_info['hash']); ?>')">
                                            <i class="fas fa-copy"></i> Copy Hash
                                        </button>
                                    </div>
                                </details>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <div class="footer">
            <p>
                <i class="fas fa-shield-alt"></i> 
                Akses pada: <?php echo date('Y-m-d H:i:s'); ?> | 
                IP: <?php echo getUserIP(); ?> | 
                User: <?php echo htmlspecialchars($_SESSION['user_name'] ?? 'Unknown'); ?>
            </p>
        </div>
    </div>
    
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('Password hash berhasil disalin ke clipboard!');
            }, function(err) {
                console.error('Gagal menyalin: ', err);
                // Fallback untuk browser lama
                const textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    alert('Password hash berhasil disalin ke clipboard!');
                } catch (err) {
                    alert('Gagal menyalin ke clipboard');
                }
                document.body.removeChild(textArea);
            });
        }
        
        // Auto-refresh warning setiap 5 menit
        setInterval(function() {
            console.warn('🚨 REMINDER: Anda sedang mengakses informasi sensitif!');
        }, 300000);
    </script>
</body>
</html>
