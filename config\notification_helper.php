<?php
/**
 * Helper functions for sending email notifications
 */

// Include mail.php if not already included
if (!function_exists('send_mail')) {
    include_once __DIR__ . '/mail.php';
}

/**
 * Send notification email to the next approver when training status changes
 *
 * @param int $training_id The ID of the training submission
 * @param string $status The new status of the training submission
 * @param string $comments Comments provided by the current approver
 * @param string $assignment Penugasan yang diberikan kepada peserta training
 * @param int $next_approver_role_id The role ID of the next approver
 * @param object $conn Database connection
 * @param int $next_approver_id The specific user ID of the next approver (if applicable)
 * @return array Result of the email sending operation
 */
function send_status_update_notification($training_id, $status, $comments, $assignment, $next_approver_role_id, $conn, $next_approver_id = null) {
    try {
    // Log function call
    error_log("Sending status update notification for training ID: $training_id, Status: $status, Next approver role: $next_approver_role_id, Next approver ID: " . ($next_approver_id ?? 'NULL'));

    // Get training submission details
    $query = "SELECT ts.*,
                    u.name AS requester_name,
                    r.role_name AS next_approver_role
              FROM training_submissions ts
              LEFT JOIN users u ON ts.user_id = u.id
              LEFT JOIN roles r ON ts.current_approver_role_id = r.id
              WHERE ts.id = ?";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $training_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        error_log("Training submission not found: $training_id");
        return [
            'success' => false,
            'message' => 'Training submission not found'
        ];
    }

    $training = $result->fetch_assoc();

    // Get participants
    $participants_query = "SELECT * FROM participants WHERE training_id = ?";
    $participants_stmt = $conn->prepare($participants_query);
    $participants_stmt->bind_param("i", $training_id);
    $participants_stmt->execute();
    $participants_result = $participants_stmt->get_result();

    $participants = [];
    while ($participant = $participants_result->fetch_assoc()) {
        $participants[] = [
            'nama' => $participant['nama_participants'],
            'nik' => $participant['nik_participants'],
            'jabatan' => $participant['jabatan_participants'],
            'departemen' => $participant['departemen_participants']
        ];
    }

    // Get email settings
    $settings_query = "SELECT * FROM settings WHERE id = 1";
    $settings_result = $conn->query($settings_query);

    if ($settings_result->num_rows === 0) {
        error_log("Email settings not found");
        return [
            'success' => false,
            'message' => 'Email settings not found'
        ];
    }

    $settings = $settings_result->fetch_assoc();

    // Validasi konfigurasi email
    if (empty($settings['smtp_server']) || empty($settings['sender_email']) || empty($settings['smtp_port'])) {
        error_log("Email configuration incomplete: SMTP server, sender email, or port missing");
        return [
            'success' => false,
            'message' => 'Email configuration incomplete. Please configure SMTP settings in admin panel.'
        ];
    }

    // Find the next approver(s)
    $next_approvers = [];

    if ($next_approver_id !== null) {
        // If a specific next approver is provided, validate it's the correct dept head for this department
        if ($next_approver_role_id == 2) {
            // For dept head role, verify the approver is responsible for this department
            $approver_query = "SELECT u.id, u.name, u.email, ud.dept as responsible_dept
                              FROM users u
                              LEFT JOIN user_departments ud ON u.id = ud.user_id
                              WHERE u.id = ? AND u.role_id = 2";
            $approver_stmt = $conn->prepare($approver_query);
            $approver_stmt->bind_param("i", $next_approver_id);
            $approver_stmt->execute();
            $approver_result = $approver_stmt->get_result();

            if ($approver_result->num_rows > 0) {
                $approver = $approver_result->fetch_assoc();

                // Check if this dept head is responsible for the training's department
                if ($approver['responsible_dept'] == $training['departemen'] ||
                    empty($approver['responsible_dept'])) { // Allow if no specific dept assigned
                    $next_approvers[] = $approver;
                    error_log("Using specific dept head: {$approver['name']} for department: {$training['departemen']}");
                } else {
                    error_log("Specified dept head {$approver['name']} (dept: {$approver['responsible_dept']}) doesn't match training dept: {$training['departemen']}. Finding correct dept head...");
                    // Find the correct dept head for this department
                    $correct_dept_head_query = "SELECT u.id, u.name, u.email
                                               FROM users u
                                               INNER JOIN user_departments ud ON u.id = ud.user_id
                                               WHERE ud.dept = ? AND u.role_id = 2
                                               ORDER BY u.id LIMIT 1";
                    $correct_stmt = $conn->prepare($correct_dept_head_query);
                    $correct_stmt->bind_param("s", $training['departemen']);
                    $correct_stmt->execute();
                    $correct_result = $correct_stmt->get_result();

                    if ($correct_result->num_rows > 0) {
                        $correct_approver = $correct_result->fetch_assoc();
                        $next_approvers[] = $correct_approver;
                        error_log("Found correct dept head: {$correct_approver['name']} for department: {$training['departemen']}");
                    } else {
                        // Fallback to the specified approver if no correct one found
                        $next_approvers[] = $approver;
                        error_log("No specific dept head found for {$training['departemen']}, using specified approver: {$approver['name']}");
                    }
                }
            }
        } else {
            // For non-dept head roles, use the specified approver directly
            $approver_query = "SELECT id, name, email FROM users WHERE id = ?";
            $approver_stmt = $conn->prepare($approver_query);
            $approver_stmt->bind_param("i", $next_approver_id);
            $approver_stmt->execute();
            $approver_result = $approver_stmt->get_result();

            if ($approver_result->num_rows > 0) {
                $next_approvers[] = $approver_result->fetch_assoc();
            }
        }
    } else {
        // Find approvers by role and department (for dept head role)
        if ($next_approver_role_id == 2) {
            // For dept head, find the one responsible for this department
            $approver_query = "SELECT u.id, u.name, u.email
                              FROM users u
                              INNER JOIN user_departments ud ON u.id = ud.user_id
                              WHERE ud.dept = ? AND u.role_id = 2
                              ORDER BY u.id LIMIT 1";
            $approver_stmt = $conn->prepare($approver_query);
            $approver_stmt->bind_param("s", $training['departemen']);
            $approver_stmt->execute();
            $approver_result = $approver_stmt->get_result();

            if ($approver_result->num_rows > 0) {
                $next_approvers[] = $approver_result->fetch_assoc();
                error_log("Found dept head by department: {$training['departemen']}");
            } else {
                error_log("No dept head found for department: {$training['departemen']}, falling back to any dept head");
                // Fallback to any dept head
                $fallback_query = "SELECT id, name, email FROM users WHERE role_id = 2 LIMIT 1";
                $fallback_result = $conn->query($fallback_query);
                if ($fallback_result && $fallback_result->num_rows > 0) {
                    $next_approvers[] = $fallback_result->fetch_assoc();
                }
            }
        } else {
            // For other roles, find by role_id
            $approver_query = "SELECT id, name, email FROM users WHERE role_id = ?";
            $approver_stmt = $conn->prepare($approver_query);
            $approver_stmt->bind_param("i", $next_approver_role_id);
            $approver_stmt->execute();
            $approver_result = $approver_stmt->get_result();

            while ($approver = $approver_result->fetch_assoc()) {
                $next_approvers[] = $approver;
            }
        }
    }

    // Also notify the requester if the status is final (Approved or Rejected)
    if ($status == 'Approved' || $status == 'Rejected') {
        $requester_query = "SELECT id, name, email FROM users WHERE id = ?";
        $requester_stmt = $conn->prepare($requester_query);
        $requester_stmt->bind_param("i", $training['user_id']);
        $requester_stmt->execute();
        $requester_result = $requester_stmt->get_result();

        if ($requester_result->num_rows > 0) {
            $requester = $requester_result->fetch_assoc();
            // Add requester to notification list
            $next_approvers[] = $requester;
        }
    }

    if (empty($next_approvers)) {
        error_log("No approvers found for role ID: $next_approver_role_id or specific approver ID: " . ($next_approver_id ?? 'NULL'));
        return [
            'success' => false,
            'message' => 'No approvers found'
        ];
    }

    // Format training date
    $training_date = '';
    if (!empty($training['start_date']) && !empty($training['end_date'])) {
        $start_date = date('d F Y', strtotime($training['start_date']));
        $end_date = date('d F Y', strtotime($training['end_date']));
        $training_date = "$start_date s/d $end_date";
    }

    // Set status color
    $status_color = '#000000'; // Default black
    if ($status == 'Approved') {
        $status_color = '#28a745'; // Green
    } elseif ($status == 'Rejected') {
        $status_color = '#dc3545'; // Red
    } elseif ($status == 'Pending') {
        $status_color = '#ffc107'; // Yellow
    }

    // Send email to each approver
    $success_count = 0;
    $error_messages = [];

    foreach ($next_approvers as $approver) {
        // Skip if no email
        if (empty($approver['email'])) {
            error_log("Approver has no email: " . $approver['name'] . " (ID: " . $approver['id'] . ")");
            continue;
        }

        // Determine if this is a notification to the requester
        $is_requester = ($approver['id'] == $training['user_id']);

        // Prepare email data
        $email_data = [
            'email_title' => $is_requester ? 'Update Status Pengajuan Training Anda' : 'Pengajuan Training Memerlukan Persetujuan Anda',
            'recipient_name' => $approver['name'],
            'intro_message' => $is_requester
                ? "Pengajuan training Anda telah diperbarui dengan status: <strong>$status</strong>."
                : "Ada pengajuan training yang memerlukan persetujuan Anda.",
            'training_id' => $training_id,
            'training_topic' => $training['training_topic'],
            'training_date' => $training_date,
            'requester_name' => $training['requester_name'] ?? $training['full_name'],
            'requester_nik' => $training['nik'],
            'departemen' => $training['departemen'],
            'status' => $status,
            'status_color' => $status_color,
            'comments' => $comments,
            'assignment' => $assignment,
            'participants' => $participants,
            'action_required' => !$is_requester,
            'action_message' => 'Silakan login ke sistem untuk melihat detail dan memberikan persetujuan.',
            'action_url' => 'http://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/training/' .
                            ($next_approver_role_id == 2 ?
                                // Untuk role_id 2, perlu cek apakah ini Factory Manager atau Dept Head biasa
                                (isset($next_approver_id) ?
                                    // Jika ada specific approver, cek jabatannya
                                    (function() use ($conn, $next_approver_id) {
                                        $query = "SELECT jabatan FROM users WHERE id = ?";
                                        $stmt = $conn->prepare($query);
                                        $stmt->bind_param("i", $next_approver_id);
                                        $stmt->execute();
                                        $result = $stmt->get_result();
                                        if ($result->num_rows > 0) {
                                            $user = $result->fetch_assoc();
                                            if ($user['jabatan'] == 'Factory Manager') {
                                                return 'dept_head'; // Factory Manager menggunakan interface dept_head
                                            } else {
                                                return 'dept_head'; // Dept Head biasa
                                            }
                                        }
                                        return 'dept_head'; // Default jika tidak ditemukan
                                    })()
                                    : 'dept_head') // Default jika tidak ada specific approver
                                : ($next_approver_role_id == 3 ? 'LnD' : 'admin')) .
                            '/detail_training.php?id=' . $training_id
        ];

        // Generate email subject
        $email_subject = $is_requester
            ? "[TRAINING] Status Pengajuan Training Anda: $status"
            : "[TRAINING] Pengajuan Training Baru Memerlukan Persetujuan";

        // Generate email body using template
        ob_start();
        // Variabel $data digunakan dalam template
        $data = $email_data;
        include __DIR__ . '/email_templates/status_update.php';
        $email_body = ob_get_clean();

        // Kirim email aktual
        error_log("ATTEMPTING to send email to {$approver['email']}: {$email_subject}");

        $mail_result = send_mail(
            $approver['email'],
            $email_subject,
            $email_body,
            $settings
        );

        if ($mail_result['success']) {
            $success_count++;
            error_log("✅ Email notification sent successfully to " . $approver['name'] . " (" . $approver['email'] . ")");
        } else {
            $error_messages[] = "Failed to send email to " . $approver['name'] . " (" . $approver['email'] . "): " . $mail_result['message'];
            error_log("❌ Failed to send email to " . $approver['name'] . " (" . $approver['email'] . "): " . $mail_result['message']);

            // Log additional debug info if available
            if (isset($mail_result['debug_output']) && !empty($mail_result['debug_output'])) {
                error_log("📧 SMTP Debug: " . $mail_result['debug_output']);
            }
        }
    }

    // Return result
    if ($success_count > 0) {
        return [
            'success' => true,
            'message' => "Email notifications sent successfully to $success_count recipient(s)",
            'errors' => $error_messages
        ];
    } else {
        return [
            'success' => false,
            'message' => "Failed to send email notifications",
            'errors' => $error_messages
        ];
    }
    } catch (Exception $e) {
        error_log("Exception in send_status_update_notification: " . $e->getMessage());
        return [
            'success' => false,
            'message' => "Exception occurred: " . $e->getMessage()
        ];
    }
}
