<?php
/**
 * Helper functions for managing class roles and permissions
 */

/**
 * Get user's role in a specific class
 *
 * @param int $user_id User ID
 * @param int $class_id Class ID
 * @return string|null Role (instructor, assistant, student) or null if not a participant
 */
function getUserRoleInClass($user_id, $class_id) {
    global $conn;

    $query = "SELECT role FROM training_participants
              WHERE user_id = ? AND class_id = ? AND status = 'active'";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $user_id, $class_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($row = $result->fetch_assoc()) {
        return $row['role'];
    }

    return null;
}

/**
 * Check if user has one of the specified roles in a class
 *
 * @param int $user_id User ID
 * @param int $class_id Class ID
 * @param array $required_roles Array of roles that have access
 * @return bool True if user has one of the required roles
 */
function checkClassRoleAccess($user_id, $class_id, $required_roles = ['instructor']) {
    $role = getUserRoleInClass($user_id, $class_id);
    return $role !== null && in_array($role, $required_roles);
}

/**
 * Check if user has access to a class with specific roles (alias for checkClassRoleAccess)
 *
 * @param mysqli $conn Database connection
 * @param int $user_id User ID
 * @param int $class_id Class ID
 * @param array $allowed_roles Array of allowed roles
 * @return bool True if user has access
 */
function hasClassAccess($conn, $user_id, $class_id, $allowed_roles = ['instructor']) {
    return checkClassRoleAccess($user_id, $class_id, $allowed_roles);
}

/**
 * Check if user is an instructor in a class
 *
 * @param int $user_id User ID
 * @param int $class_id Class ID
 * @return bool True if user is an instructor
 */
function isInstructor($user_id, $class_id) {
    return checkClassRoleAccess($user_id, $class_id, ['instructor']);
}

/**
 * Check if user is an assistant in a class
 *
 * @param int $user_id User ID
 * @param int $class_id Class ID
 * @return bool True if user is an assistant
 */
function isAssistant($user_id, $class_id) {
    return checkClassRoleAccess($user_id, $class_id, ['assistant']);
}

/**
 * Check if user is a student in a class
 *
 * @param int $user_id User ID
 * @param int $class_id Class ID
 * @return bool True if user is a student
 */
function isStudent($user_id, $class_id) {
    return checkClassRoleAccess($user_id, $class_id, ['student']);
}

/**
 * Check if user is an instructor or assistant in a class
 *
 * @param int $user_id User ID
 * @param int $class_id Class ID
 * @return bool True if user is an instructor or assistant
 */
function isTeachingStaff($user_id, $class_id) {
    return checkClassRoleAccess($user_id, $class_id, ['instructor', 'assistant']);
}

/**
 * Get all users with a specific role in a class
 *
 * @param int $class_id Class ID
 * @param string $role Role to filter by
 * @return array Array of user data
 */
function getUsersByRoleInClass($class_id, $role) {
    global $conn;

    $query = "SELECT u.id, u.name, u.email, p.role, p.joined_at
              FROM training_participants p
              JOIN users u ON p.user_id = u.id
              WHERE p.class_id = ? AND p.role = ? AND p.status = 'active'
              ORDER BY u.name";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("is", $class_id, $role);
    $stmt->execute();
    $result = $stmt->get_result();

    $users = [];
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }

    return $users;
}

/**
 * Redirect if user doesn't have required role in class
 *
 * @param int $user_id User ID
 * @param int $class_id Class ID
 * @param array $required_roles Array of roles that have access
 * @param string $redirect_url URL to redirect to if access denied
 */
function requireClassRoleAccess($user_id, $class_id, $required_roles, $redirect_url = '../pemohon/classroom.php') {
    if (!checkClassRoleAccess($user_id, $class_id, $required_roles)) {
        $_SESSION['error'] = "Anda tidak memiliki akses ke halaman ini.";
        header("Location: $redirect_url");
        exit();
    }
}
