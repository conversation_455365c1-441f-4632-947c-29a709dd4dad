<?php
session_start();
include '../config/config.php';

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    // Redirect based on role_id
    switch ($_SESSION['role_id']) {
        case 99:
            header('Location: ../admin/index.php');
            break;
        case 6:
            header('Location: ../kosong/index.php');
            break;
        case 4:
            header('Location: ../fm/index.php');
            break;
        case 3:
            header('Location: ../lnd/index.php');
            break;
        case 5:
            header('Location: ../dir/index.php');
            break;
        case 2:
            header('Location: ../dept_head/index.php');
            break;
        case 1:
            header('Location: ../pemohon/index.php');
            break;
        default:
            header('Location: forbidden.php');
            break;
    }
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $dept = $_POST['department'];
    $nik = $_POST['nik'];
    $verification_code = isset($_POST['verification_code']) ? $_POST['verification_code'] : '';
    $direct_activation = isset($_POST['direct_activation']) ? true : false;

    // Jika kode verifikasi diisi, langsung verifikasi
    if (!empty($verification_code)) {
        // Prepared statement untuk memeriksa dept, nik, dan kode verifikasi
        $query = "SELECT * FROM users WHERE dept = ? AND nik = ? AND verification_code = ? AND verification_expires > NOW() AND is_active = 0";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("sss", $dept, $nik, $verification_code);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();

            // Aktivasi akun
            $update_query = "UPDATE users SET
                            is_active = 1,
                            verification_code = NULL,
                            verification_expires = NULL
                            WHERE id = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("i", $user['id']);

            if ($update_stmt->execute()) {
                $success_message = "Akun berhasil diaktivasi! Anda akan dialihkan ke halaman login.";
                header('Refresh: 2; URL=login.php');
            } else {
                $error_message = "Gagal mengaktivasi akun!";
            }
        } else {
            $error_message = "Kode verifikasi tidak valid atau sudah kadaluarsa!";
        }
    }
    // Jika aktivasi langsung dipilih
    elseif ($direct_activation) {
        // Cek NIK dan departemen saja
        $query = "SELECT * FROM users WHERE dept = ? AND nik = ? AND is_active = 0";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("ss", $dept, $nik);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();

            // Simpan ID user untuk halaman update email
            $_SESSION['temp_user_id'] = $user['id'];
            $_SESSION['temp_user_name'] = $user['name'];
            $_SESSION['temp_user_nik'] = $user['nik'];

            // Redirect ke halaman verify_activation dengan parameter direct=1
            header('Location: ../config/verify_activation.php?direct=1');
            exit();
        } else {
            $error_message = "Departemen atau NIK tidak ditemukan atau akun sudah aktif.";
        }
    } else {
        // Jika kode verifikasi tidak diisi dan bukan aktivasi langsung, cek NIK dan departemen saja
        $query = "SELECT * FROM users WHERE dept = ? AND nik = ? AND is_active = 0";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("ss", $dept, $nik);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();
            $_SESSION['verification_email'] = $user['email'];
            $_SESSION['temp_user_id'] = $user['id'];
            header('Location: ../config/verify_activation.php');
            exit();
        } else {
            $error_message = "Departemen atau NIK tidak ditemukan atau akun sudah aktif.";
        }
    }
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="id">
<?php include '../config/head.php'; ?>
<body>

<style>
:root {
    --primary-color: #BF0000;
    --primary-color-dark: #900000;
    --primary-color-light: rgba(191, 0, 0, 0.1);
    --primary-color-lighter: rgba(191, 0, 0, 0.05);
    --text-dark: #333333;
    --text-light: #ffffff;
    --text-muted: rgba(255, 255, 255, 0.8);
    --border-light: rgba(255, 255, 255, 0.2);
    --accent-color: #FF3333;
    --accent-light: rgba(255, 51, 51, 0.3);
    --accent-lighter: rgba(255, 51, 51, 0.1);
    --dark-accent: #333333;
    --dark-accent-light: rgba(51, 51, 51, 0.3);
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --transition-fast: 0.2s ease;
    --box-shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.1);
    --box-shadow-md: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Header Styles */
.header {
    background-color: #ffffff;
    box-shadow: var(--box-shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all var(--transition-fast);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    position: relative;
}

/* Logo Styles */
.logo-container {
    display: flex;
    align-items: center;
    height: 40px;
    margin-right: var(--spacing-md);
    overflow: hidden;
}

.logo-image {
    max-height: 40px;
    max-width: 150px;
    width: auto;
    height: auto;
    object-fit: contain;
    transition: all var(--transition-fast);
}

.logo {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
    transition: all var(--transition-fast);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 600px;
}

.logo:hover {
    color: var(--primary-color-dark);
}

/* Navigation Menu */
.nav-menu {
    display: flex;
    align-items: center;
    margin-left: auto;
    gap: var(--spacing-md);
}

.nav-menu a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
    overflow: hidden;
}

.nav-menu a i {
    font-size: var(--font-size-md);
    transition: all var(--transition-fast);
}

.nav-menu a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width var(--transition-fast);
}

.nav-menu a:hover {
    color: var(--primary-color);
    background-color: var(--primary-color-lighter);
}

.nav-menu a:hover i {
    transform: translateY(-2px);
}

.nav-menu a:hover::before {
    width: 80%;
}

.nav-menu a.active {
    color: var(--primary-color);
    background-color: var(--primary-color-light);
    font-weight: 600;
}

.nav-menu a.active::before {
    width: 80%;
}

/* User Profile and Logout */
.user-profile {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    margin-left: var(--spacing-md);
}

.username {
    font-weight: 600;
    color: var(--text-dark);
    transition: all var(--transition-fast);
}

.logout {
    background-color: var(--primary-color);
    color: var(--text-dark) !important;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    font-weight: 600;
    box-shadow: var(--box-shadow-sm);
}

.logout:hover {
    color: var(--text-dark) !important;
    background-color: var(--primary-color-dark);
    box-shadow: var(--box-shadow-md);
    transform: translateY(-2px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background-color: var(--primary-color);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    z-index: 1001;
    box-shadow: var(--box-shadow-sm);
}

.mobile-menu-toggle:hover {
    background-color: var(--primary-color-dark);
}

/* Responsive Styles */
@media screen and (max-width: 992px) {
    .header-content {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .logo {
        max-width: 180px;
        font-size: var(--font-size-md);
    }

    .nav-menu a {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

@media screen and (max-width: 768px) {
    .header {
        box-shadow: none;
    }

    .header-content {
        padding: var(--spacing-sm);
        justify-content: space-between;
    }

    .logo-container {
        height: 36px;
    }

    .logo-image {
        max-height: 36px;
        max-width: 120px;
    }

    .logo {
        max-width: 150px;
        font-size: var(--font-size-sm);
    }

    /* Show mobile menu toggle */
    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        margin-left: auto;
    }

    /* Style mobile navigation menu */
    .nav-menu {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--primary-color);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: var(--spacing-lg);
        gap: var(--spacing-md);
        z-index: 1000;
        overflow-y: auto;
    }

    .nav-menu::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.1;
        pointer-events: none;
    }

    .nav-menu.active {
        display: flex;
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    .nav-menu a {
        color: var(--text-light);
        width: 100%;
        text-align: center;
        padding: var(--spacing-md);
        font-size: var(--font-size-md);
        border-radius: var(--border-radius-md);
        justify-content: center;
    }

    .nav-menu a::before {
        display: none;
    }

    .nav-menu a:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--text-light);
    }

    .nav-menu a.active {
        background-color: rgba(255, 255, 255, 0.2);
        color: var(--text-light);
    }

    .user-section {
        display: flex;
        flex-direction: column;
        width: 100%;
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
    }

    .user-profile {
        margin-left: 0;
        margin-top: var(--spacing-md);
        width: 100%;
        justify-content: center;
    }

    .username {
        color: var(--text-light);
    }

    .logout {
        width: 100%;
        color: var(--text-dark);
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.3);
        margin-top: var(--spacing-md);
    }

    .logout:hover {
        color: var(--text-dark);
        background-color: rgba(255, 255, 255, 0.4);
    }
}

@media screen and (max-width: 480px) {
    .header-content {
        padding: var(--spacing-xs);
    }

    .logo-container {
        height: 32px;
    }

    .logo-image {
        max-height: 32px;
        max-width: 100px;
    }

    .logo {
        max-width: 120px;
        font-size: var(--font-size-xs);
    }

    .nav-menu a {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
}

/* Add spacing to prevent content from being hidden under fixed header */
.header-spacer {
    height: 80px; /* Further increased height to prevent content from being hidden */
    width: 100%;
    display: block;
    clear: both;
}

@media screen and (max-width: 768px) {
    .header-spacer {
        height: 70px; /* Increased height for medium screens */
    }
}

@media screen and (max-width: 480px) {
    .header-spacer {
        height: 65px; /* Increased height for small screens */
    }
}
.jarak {
    height: 80px;
}

 /* Base styles */
    body {
        font-family: 'Roboto', Arial, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 0;
    }

    /* Login container */
    .login-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 120px); /* Adjust for navbar and footer */
        margin:auto;
        padding: 20px;
        background: linear-gradient(135deg, #c40000 0%, #8b0000 100%);
        position: relative;
        z-index: 1; /* Ensure it's above the navbar */
    }

    .login-box {
        width: 100%;
        max-width: 400px;
        background: rgba(255, 255, 255, 0.98);
        border-radius: 15px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        padding: 30px;
        transition: all 0.3s ease;
    }

    /* Header styles */
    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .login-logo {
        width: 120px;
        height: auto;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }

    .login-header h2 {
        color: #BF0000;
        font-size: 28px;
        margin: 0;
        font-weight: 600;
    }

    /* Form styles */
    .form-group {
        margin-bottom: 22px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #444;
        font-weight: 500;
        font-size: 15px;
    }

    .form-control {
        width: 100%;
        padding: 14px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }

    .form-control:focus {
        border-color: #c40000;
        outline: none;
        box-shadow: 0 0 0 3px rgba(196, 0, 0, 0.1);
    }

    select.form-control {
        appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 15px center;
        background-size: 1em;
        padding-right: 40px;
    }

    /* Button styles */
    .btn {
        width: 100%;
        padding: 14px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }

    .btn-primary {
        background: #c40000;
        color: white;
    }

    .btn-primary:hover {
        background: #a00000;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn-primary:active {
        transform: translateY(0);
    }

    /* Footer styles */
    .login-footer {
        text-align: center;
        margin-top: 25px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .login-footer p {
        margin: 8px 0;
        color: #555;
    }

    .login-footer a {
        color: #c40000;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .login-footer a:hover {
        color: #a00000;
        text-decoration: underline;
    }

    /* Alert styles */
    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 25px;
        text-align: center;
        font-size: 15px;
        font-weight: 500;
    }

    .alert-danger {
        background: #fff2f2;
        color: #c40000;
        border: 1px solid #ffcfcf;
    }

    .alert-success {
        background: #f0fff0;
        color: #2e7d32;
        border: 1px solid #c8e6c9;
    }

    /* Icon styles */
    .fas {
        margin-right: 8px;
        color: #666;
    }

    /* Responsive styles */
    @media (max-width: 768px) {
        .login-container {
            padding: 15px;
            padding-top: 70px; /* Add top padding to prevent navbar overlap */
        }

        .login-box {
            padding: 25px 20px;
        }

        .login-logo {
            width: 100px;
        }

        .login-header h2 {
            font-size: 24px;
        }

        .form-control {
            padding: 12px;
            font-size: 14px;
        }

        .btn {
            padding: 12px;
            font-size: 15px;
        }
    }

    @media (max-width: 480px) {
        .login-box {
            padding: 20px 15px;
        }

        .login-logo {
            width: 90px;
        }

        .login-header h2 {
            font-size: 22px;
        }

        .form-group label {
            font-size: 14px;
        }

        .form-control {
            padding: 10px;
            font-size: 14px;
        }

        .btn {
            padding: 12px;
            font-size: 14px;
        }

        .alert {
            padding: 12px;
            font-size: 14px;
        }
    }

</style>
<header class="header">
    <div class="header-content">
        <div class="logo-container">
            <img class="logo-image" src="../asset/picture/logopasbagus.jpg" alt="Company Logo">
        </div>
        <a href="login.php" class="logo">Knowledge Management System</a>

        <!-- Mobile Menu Toggle Button -->
        <button class="mobile-menu-toggle" id="mobileMenuToggle" type="button" aria-label="Toggle menu">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Navigation Menu -->
        <nav class="nav-menu" id="navMenu">
            <a href="index.php" class="<?= ($current_page == 'index.php') ? 'active' : '' ?>">
                <i class="fas fa-home"></i> Home
            </a>
            <?php if(isset($_SESSION['role_id']) && $_SESSION['role_id'] == 99): ?>
            <a href="<?= getAdminMenuUrl('employee_management.php') ?>" class="<?= ($current_page == 'employee_management.php') ? 'active' : '' ?>">
                <i class="fas fa-users"></i> Karyawan
            </a>
            <a href="<?= getAdminMenuUrl('training_management.php') ?>" class="<?= ($current_page == 'training_management.php') ? 'active' : '' ?>">
                <i class="fas fa-chalkboard-teacher"></i> Training
            </a>
            <a href="<?= getAdminMenuUrl('settings.php') ?>" class="<?= ($current_page == 'settings.php') ? 'active' : '' ?>">
                <i class="fas fa-cog"></i> Pengaturan
            </a>
            <?php endif; ?>

            <?php if(isset($_SESSION['user_id'])): ?>
            <a href="<?= getCorrectUrl('config/userinfo.php') ?>" class="user-profile <?= ($current_page == 'userinfo.php') ? 'active' : '' ?>">
                <div class="avatar">
                <span class="username"><?= strtoupper(substr($_SESSION['user_name'] ?? 'Pengguna', 0, 1)) ?></span>
                </div>
            </a>    
            <a href="<?= getCorrectUrl('view/logout.php') ?>" class="logout">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
            <?php endif; ?>
        </nav>
    </div>
</header>
<div class="jarak"></div>
        <div class="login-container">
            <div class="login-box">
                <div class="login-header">
                    <img src="../asset/picture/logo-pas-with-text-removebg-preview.png" alt="Logo" class="login-logo">
                    <h2 style="color: #BF0000;">Aktivasi Akun</h2>
                </div>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                <?php endif; ?>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>

                <form method="POST" class="login-form" id="activationForm">
                    <div class="form-group searchable-select">
                    <label for="department">
                        <i class="fas fa-building"></i> Departemen
                    </label>
                    <!-- Hidden select as reference -->
                    <select id="department" name="department" class="form-control" style="display:none;">
                        <option value="">Pilih Departemen</option>
                        <?php
                        // Query untuk mengambil departemen unik dari tabel karyawan
                        $dept_query = "SELECT DISTINCT dept FROM karyawan ORDER BY dept";
                        $dept_result = $conn->query($dept_query);

                        if ($dept_result->num_rows > 0) {
                            while($row = $dept_result->fetch_assoc()) {
                                echo "<option value='" . htmlspecialchars($row['dept']) . "'>" . htmlspecialchars($row['dept']) . "</option>";
                            }
                        }
                        ?>
                    </select>
                    <!-- Custom search input -->
                    <input type="text" id="searchDepartment" placeholder="Pilih Departemen" required />
                    <ul id="dropdownDepartmentList">
                        <?php
                        // Reset pointer hasil query
                        $dept_result->data_seek(0);
                        if ($dept_result->num_rows > 0) {
                            while($row = $dept_result->fetch_assoc()) {
                                echo "<li data-value='" . htmlspecialchars($row['dept']) . "'>" . htmlspecialchars($row['dept']) . "</li>";
                            }
                        }
                        ?>
                    </ul>
                </div>
                    <div class="form-group">
                        <label for="nik">
                            <i class="fas fa-id-card"></i> NIK
                        </label>
                        <input type="text"
                               name="nik"
                               id="nik"
                               required
                               placeholder="Masukkan NIK yang tertera pada ID Card"
                               class="form-control">
                    <small class="form-text text-muted">Masukkan Departemen dan NIK Karyawan yang tertera pada ID Card untuk mengaktivasi akun.</small>

                    </div>

                    <div class="form-group">
                        <div class="activation-options">
                            <label class="checkbox-container">
                                <input type="checkbox" name="direct_activation" id="direct_activation" checked>
                                <span class="checkmark"></span>
                                <strong>Aktivasi langsung tanpa verifikasi email</strong>
                            </label>
                            <small class="form-text text-muted">Pilih opsi ini jika Anda tidak memiliki akses ke email yang terdaftar. Anda akan diminta untuk mengatur email dan password setelah aktivasi.</small>
                        </div>
                    </div>

                    <div class="form-group verification-code-group" style="display: none;">
                        <label for="verification_code">
                            <i class="fas fa-key"></i> Kode Verifikasi
                        </label>
                        <input type="text"
                               name="verification_code"
                               id="verification_code"
                               placeholder="Masukkan kode verifikasi dari email"
                               class="form-control">
                        <small class="form-text text-muted">Jika Anda memiliki kode verifikasi dari email, masukkan di sini untuk aktivasi dengan verifikasi email.</small>
                    </div>


                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-user-plus" style="color: white;"></i> Aktivasi
                    </button>
                </form>

                <div class="login-footer">
                    <p>Sudah mengaktivasi akun?
                        <a href="login.php">Login di sini</a>
                    </p>
                </div>
            </div>
        </div>
    <?php include '../config/footer.php'; ?>
</body>
<script>
  // Searchable dropdown functionality
  const searchInput = document.getElementById('searchDepartment');
  const dropdownList = document.getElementById('dropdownDepartmentList');

  // Hanya jalankan kode jika elemen-elemen tersebut ada di halaman
  if (searchInput && dropdownList) {
    const originalSelect = document.getElementById('department');
    const items = Array.from(dropdownList.getElementsByTagName('li'));

    // Ketika input diklik, tampilkan daftar
    searchInput.addEventListener('click', () => {
      dropdownList.style.display = 'block';
    });

    // Filter saat mengetik
    searchInput.addEventListener('input', () => {
      const searchTerm = searchInput.value.toLowerCase();
      items.forEach(item => {
        const text = item.textContent.toLowerCase();
        item.style.display = text.includes(searchTerm) ? 'block' : 'none';
      });
      dropdownList.style.display = 'block';
    });

    // Set nilai ketika item dipilih
    items.forEach(item => {
      item.addEventListener('click', () => {
        searchInput.value = item.textContent;
        // Set nilai ke hidden select
        if (originalSelect) {
          originalSelect.value = item.getAttribute('data-value');
          // Trigger change event jika diperlukan
          const event = new Event('change');
          originalSelect.dispatchEvent(event);
        }
        dropdownList.style.display = 'none';

        // Reset border color when department is selected
        searchInput.style.borderColor = '';
      });
    });

    // Sembunyikan dropdown saat klik di luar
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.searchable-select')) {
        dropdownList.style.display = 'none';
      }
    });
  }

  // Toggle verification code field based on direct activation checkbox
  const directActivationCheckbox = document.getElementById('direct_activation');
  const verificationCodeGroup = document.querySelector('.verification-code-group');

  if (directActivationCheckbox && verificationCodeGroup) {
    // Initial state - hide verification code field if direct activation is checked
    verificationCodeGroup.style.display = directActivationCheckbox.checked ? 'none' : 'block';

    // Toggle on checkbox change
    directActivationCheckbox.addEventListener('change', function() {
      verificationCodeGroup.style.display = this.checked ? 'none' : 'block';
    });
  }

  // Form validation
  const activationForm = document.getElementById('activationForm');
  if (activationForm) {
    activationForm.addEventListener('submit', function(e) {
      const departmentSelect = document.getElementById('department');
      const searchInput = document.getElementById('searchDepartment');

      // Check if department is selected
      if (!departmentSelect.value || departmentSelect.value === '') {
        e.preventDefault();
        alert('Silakan pilih departemen terlebih dahulu!');
        if (searchInput) {
          searchInput.focus();
          searchInput.style.borderColor = '#dc3545';
        }
        return false;
      }

      // Reset border color if validation passes
      if (searchInput) {
        searchInput.style.borderColor = '';
      }
    });
  }
</script>
</html>
